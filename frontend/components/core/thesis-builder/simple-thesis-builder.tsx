"use client"

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Save, FileText, Target, Calculator, Star } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import { Form } from '@/lib/types/form';
import { FormAPI } from '@/lib/api/form-api';
import { ThesisStatus } from '@/lib/types/thesis';
import { ThesisAPI } from '@/lib/api/thesis-api';

interface ThesisFormData {
  name: string;
  description: string;
  form_id: string;
  status: ThesisStatus;
  is_active: boolean;
}

export function SimpleThesisBuilder() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [forms, setForms] = useState<Form[]>([]);
  const [selectedForm, setSelectedForm] = useState<Form | null>(null);

  const [thesisData, setThesisData] = useState<ThesisFormData>({
    name: '',
    description: '',
    form_id: '',
    status: ThesisStatus.DRAFT,
    is_active: true,
  });

  console.log('🎯 SimpleThesisBuilder state:', { thesisData, selectedForm, forms });

  // Load available forms
  useEffect(() => {
    loadForms();
  }, []);

  const loadForms = async () => {
    try {
      console.log('📋 Loading available forms...');
      setIsLoading(true);

      const formsList = await FormAPI.listForms();
      console.log('📋 Forms loaded:', formsList);

      setForms(formsList);
    } catch (error) {
      console.error('❌ Error loading forms:', error);
      toast({
        title: "Error loading forms",
        description: "Failed to load available forms. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormSelect = async (formId: string) => {
    console.log('📋 Form selected:', formId);

    const form = forms.find(f => (f._id || f.id) === formId);
    if (form) {
      setSelectedForm(form);
      setThesisData(prev => ({ ...prev, form_id: formId }));

      // Load form details if needed
      try {
        const formDetails = await FormAPI.getFormWithDetails(formId);
        console.log('📋 Form details loaded:', formDetails);
        setSelectedForm(formDetails);
      } catch (error) {
        console.error('❌ Error loading form details:', error);
      }
    }
  };

  const handleSave = async () => {
    try {
      console.log('💾 Saving thesis:', thesisData);

      // Validate required fields
      if (!thesisData.name || !thesisData.description || !thesisData.form_id) {
        toast({
          title: "Missing information",
          description: "Please provide a name, description, and select a form for your thesis.",
          variant: "destructive",
        });
        return;
      }

      setIsSaving(true);

      // Use static import instead of dynamic import to avoid chunk loading issues

      // Create the thesis using the real API
      const savedThesis = await ThesisAPI.createThesis({
        name: thesisData.name,
        description: thesisData.description,
        form_id: thesisData.form_id,
        status: ThesisStatus.DRAFT,
        is_active: true,
      });

      console.log('✅ Thesis created successfully:', savedThesis);

      toast({
        title: "Success",
        description: "Thesis created successfully!",
      });

      // Navigate back to thesis list
      router.push('/theses');

    } catch (error) {
      console.error('❌ Error saving thesis:', error);
      toast({
        title: "Error saving thesis",
        description: "Failed to save thesis. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const canSave = thesisData.name && thesisData.description && thesisData.form_id;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create New Thesis</h1>
          <p className="text-muted-foreground">
            Define your investment strategy with matching and scoring rules
          </p>
        </div>
        <Button
          onClick={handleSave}
          disabled={!canSave || isSaving}
          size="lg"
        >
          <Save className="mr-2 size-4" />
          {isSaving ? 'Saving...' : 'Create Thesis'}
        </Button>
      </div>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="size-5" />
            Basic Information
          </CardTitle>
          <CardDescription>
            Provide basic details about your investment thesis
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <label className="text-sm font-medium">Thesis Name *</label>
              <Input
                placeholder="e.g., Early Stage SaaS Thesis"
                value={thesisData.name}
                onChange={(e) => setThesisData(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <div className="flex items-center gap-2">
                <Badge variant={thesisData.status === ThesisStatus.ACTIVE ? 'default' : 'secondary'}>
                  {thesisData.status}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  {thesisData.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Description *</label>
            <Textarea
              placeholder="Describe your investment thesis, target criteria, and evaluation approach..."
              value={thesisData.description}
              onChange={(e) => setThesisData(prev => ({ ...prev, description: e.target.value }))}
              className="min-h-[100px]"
            />
          </div>
        </CardContent>
      </Card>

      {/* Form Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Form</CardTitle>
          <CardDescription>
            Choose the form this thesis will apply to. This determines which questions will be available for scoring and matching.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="py-4 text-center">
              <p className="text-muted-foreground">Loading forms...</p>
            </div>
          ) : (
            <div className="space-y-3">
              {forms.map((form) => {
                const formId = form._id || form.id;
                const isSelected = thesisData.form_id === formId;

                return (
                  <div
                    key={formId}
                    className={`cursor-pointer rounded-lg border p-4 transition-colors ${
                      isSelected ? 'border-primary bg-primary/5' : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => handleFormSelect(formId || '')}
                  >
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <h4 className="font-medium">{form.name}</h4>
                        {form.description && (
                          <p className="text-sm text-muted-foreground">
                            {form.description}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">
                          {form.sections?.length || 0} sections
                        </Badge>
                        {form.is_active && (
                          <Badge variant="default">Active</Badge>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}

              {forms.length === 0 && (
                <div className="py-8 text-center text-muted-foreground">
                  <p>No forms available. Please create a form first.</p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Configuration Preview */}
      {selectedForm && (
        <Card>
          <CardHeader>
            <CardTitle>Configuration Preview</CardTitle>
            <CardDescription>
              Preview of your thesis configuration
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="overview" className="space-y-4">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="matching">
                  <Target className="mr-2 size-4" />
                  Matching
                </TabsTrigger>
                <TabsTrigger value="scoring">
                  <Calculator className="mr-2 size-4" />
                  Scoring
                </TabsTrigger>
                <TabsTrigger value="bonus">
                  <Star className="mr-2 size-4" />
                  Bonus
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="rounded-lg border bg-muted/50 p-4">
                  <h4 className="mb-2 font-medium">Selected Form: {selectedForm.name}</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Sections:</span>
                      <span className="ml-2 font-medium">{selectedForm.sections?.length || 0}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Questions:</span>
                      <span className="ml-2 font-medium">
                        {selectedForm.sections?.reduce((total, section) =>
                          total + (section.questions?.length || 0), 0
                        ) || 0}
                      </span>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="matching">
                <div className="py-8 text-center text-muted-foreground">
                  <Target className="mx-auto mb-2 size-8 opacity-50" />
                  <p>Matching rules configuration will be available in the full version.</p>
                </div>
              </TabsContent>

              <TabsContent value="scoring">
                <div className="py-8 text-center text-muted-foreground">
                  <Calculator className="mx-auto mb-2 size-8 opacity-50" />
                  <p>Scoring rules configuration will be available in the full version.</p>
                </div>
              </TabsContent>

              <TabsContent value="bonus">
                <div className="py-8 text-center text-muted-foreground">
                  <Star className="mx-auto mb-2 size-8 opacity-50" />
                  <p>Bonus rules configuration will be available in the full version.</p>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* Save Button (Bottom) */}
      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          disabled={!canSave || isSaving}
          size="lg"
        >
          <Save className="mr-2 size-4" />
          {isSaving ? 'Saving...' : 'Create Thesis'}
        </Button>
      </div>
    </div>
  );
}
