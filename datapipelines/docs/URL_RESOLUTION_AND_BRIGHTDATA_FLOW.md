# URL Resolution and BrightData Processing Flow

This document explains the complete flow for URL resolution (LinkedIn/Crunchbase) and BrightData snapshot processing in the TractionX data pipeline service.

## Overview

The system provides two main flows:
1. **URL Resolution**: Find and validate company URLs on LinkedIn and Crunchbase
2. **BrightData Processing**: Download and process scraped data from BrightData snapshots

## Architecture

```
URL Resolution Flow:
Company Domain → Serper Search → LLM Selection → BrightData Trigger → Snapshot ID

BrightData Processing Flow:
Snapshot ID → Poll for Completion → Download Data → Process & Store → Database
```

## 1. URL Resolution Flow

### LinkedIn URL Resolution

**Trigger**: `resolve_linkedin_url_task`

**Steps**:
1. **Serper Search**: Search Google for company LinkedIn URLs
2. **LLM Selection**: Use AI to select the most relevant LinkedIn URL
3. **BrightData Trigger**: Trigger BrightData scraping for the selected URL
4. **Queue Processing**: Queue a processing task for when scraping completes

**Input**:
```json
{
  "company_domain": "example.com",
  "company_description": "Tech startup",
  "org_id": "your_org_id",
  "job_id": "optional_custom_job_id"
}
```

**Output**:
```json
{
  "success": true,
  "status": "triggered",
  "linkedin_url": "https://linkedin.com/company/example",
  "brightdata_snapshot_id": "snapshot_123",
  "processing_time": 15.2
}
```

### Crunchbase URL Resolution

**Trigger**: `resolve_crunchbase_url_task`

**Steps**:
1. **Serper Search**: Search Google for company Crunchbase URLs
2. **LLM Selection**: Use AI to select the most relevant Crunchbase URL
3. **BrightData Trigger**: Trigger BrightData scraping for the selected URL
4. **Queue Processing**: Queue a processing task for when scraping completes

**Input**: Same as LinkedIn resolution
**Output**: Similar to LinkedIn but with `crunchbase_url` instead of `linkedin_url`

## 2. BrightData Processing Flow

### LinkedIn Data Processing

**Trigger**: `process_brightdata_linkedin_data_task`

**Steps**:
1. **Poll for Completion**: Check if BrightData snapshot is ready (NEW!)
2. **Download Data**: Download the completed snapshot
3. **Store Raw Data**: Save raw data to S3 for audit
4. **Process Data**: Clean and normalize the data
5. **Store Results**: Save processed data to database

**Input**:
```json
{
  "snapshot_id": "snapshot_123",
  "company_domain": "example.com",
  "org_id": "your_org_id"
}
```

### Company Data Processing

**Trigger**: `process_brightdata_company_data_task`

**Steps**: Same as LinkedIn processing but for company data from Crunchbase

## 3. How to Trigger Individual Components

### Method 1: Using the Script

```bash
# Trigger LinkedIn URL resolution
python trigger_resolvers.py --linkedin --domain example.com --monitor

# Trigger Crunchbase URL resolution
python trigger_resolvers.py --crunchbase --domain example.com --monitor

# Process a BrightData LinkedIn snapshot
python trigger_resolvers.py --process-linkedin-snapshot --snapshot-id snapshot_123 --domain example.com --monitor

# Process a BrightData company snapshot
python trigger_resolvers.py --process-company-snapshot --snapshot-id snapshot_123 --domain example.com --monitor
```

### Method 2: Using the Queue Dispatcher

```python
from app.queueing.dispatcher import dispatch_job

# LinkedIn URL resolution
job_id = await dispatch_job(
    job_func="resolve_linkedin_url",
    job_args={
        "company_domain": "example.com",
        "company_description": "Tech startup",
        "org_id": "your_org_id"
    },
    priority="normal"
)

# Crunchbase URL resolution
job_id = await dispatch_job(
    job_func="resolve_crunchbase_url",
    job_args={
        "company_domain": "example.com",
        "company_description": "Tech startup",
        "org_id": "your_org_id"
    },
    priority="normal"
)

# BrightData LinkedIn processing
job_id = await dispatch_job(
    job_func="process_brightdata_linkedin_data",
    job_args={
        "snapshot_id": "snapshot_123",
        "company_domain": "example.com",
        "org_id": "your_org_id"
    },
    priority="normal"
)

# BrightData company processing
job_id = await dispatch_job(
    job_func="process_brightdata_company_data",
    job_args={
        "snapshot_id": "snapshot_123",
        "company_domain": "example.com",
        "org_id": "your_org_id"
    },
    priority="normal"
)
```

### Method 3: Using the API

```bash
# Trigger LinkedIn resolution via API
curl -X POST "http://localhost:8001/api/v1/pipelines/trigger" \
  -H "Content-Type: application/json" \
  -d '{
    "company_id": "example.com",
    "org_id": "your_org_id",
    "company_name": "Example Company",
    "domain": "example.com",
    "pipeline_types": ["linkedin_resolver"],
    "priority": "normal"
  }'

# Check job status
curl "http://localhost:8001/api/v1/pipelines/status/{job_id}"
```

## 4. Polling Implementation

### Why Polling is Important

Previously, the processing tasks would immediately try to download snapshots without checking if they were ready. This could lead to failures if the snapshot was still being processed by BrightData.

### New Polling Flow

Both `process_brightdata_linkedin_data_task` and `process_brightdata_company_data_task` now include:

1. **Poll for Completion**: Check BrightData API every 10 seconds for up to 10 minutes
2. **Status Handling**: Handle "ready", "error", "failed", and timeout states
3. **Download Only When Ready**: Only attempt download after confirming snapshot is ready

### Polling Configuration

```python
max_poll_time = 600  # 10 minutes
poll_interval = 10   # 10 seconds
```

### Status States

- `"ready"`: Snapshot is complete and ready for download
- `"error"` or `"failed"`: Snapshot processing failed
- `"timeout"`: Polling exceeded maximum time limit
- Other states: Still processing, continue polling

## 5. Error Handling

### Common Error Scenarios

1. **Snapshot Not Ready**: Handled by polling with timeout
2. **Download Failures**: Retry logic with exponential backoff
3. **Processing Errors**: Comprehensive error logging and S3 storage
4. **API Failures**: Graceful degradation and error reporting

### Error Recovery

- Failed jobs can be retried using the retry endpoint
- Raw data is always stored to S3 for debugging
- Comprehensive logging at each step
- Error states are propagated through the entire pipeline

## 6. Monitoring and Debugging

### Job Status Monitoring

```python
from app.queueing.dispatcher import get_job_status

job_result = await get_job_status(job_id)
print(f"Status: {job_result.status.value}")
print(f"Error: {job_result.error_message}")
print(f"Result: {job_result.result}")
```

### S3 Audit Trail

All raw data and processing results are stored in S3:
- `linkedin_resolver_data/{org_id}/{domain}/{timestamp}_{snapshot_id}.json`
- `crunchbase_resolver/{org_id}/{domain}/{timestamp}_{snapshot_id}.json`
- `linkedin_brightdata_raw/{org_id}/{domain}/{timestamp}_{snapshot_id}.json`
- `brightdata_company_data/{org_id}/{domain}/{timestamp}_{snapshot_id}.json`

### Logging

Comprehensive logging at each step:
- Input validation
- API calls and responses
- Processing steps
- Error conditions
- Performance metrics

## 7. Performance Considerations

### Timeouts

- **URL Resolution**: ~30-60 seconds (Serper + LLM + BrightData trigger)
- **BrightData Processing**: ~10-15 minutes (polling + download + processing)
- **Total Pipeline**: ~15-20 minutes end-to-end

### Resource Usage

- **Memory**: Moderate (JSON processing, data cleaning)
- **CPU**: Low to moderate (LLM calls, data processing)
- **Network**: High (API calls to external services)
- **Storage**: Moderate (S3 storage, database records)

### Scaling

- Jobs are queued and processed asynchronously
- Multiple workers can process jobs in parallel
- Priority queues for different job types
- Horizontal scaling supported

## 8. Best Practices

### When to Use Each Component

1. **URL Resolution**: When you have a company domain but need LinkedIn/Crunchbase URLs
2. **BrightData Processing**: When you have a snapshot ID and need to process the data
3. **Full Pipeline**: When you want to start from a domain and get complete enriched data

### Error Handling

1. Always check job status before proceeding
2. Implement retry logic for transient failures
3. Store raw data for debugging
4. Monitor job queues and worker health

### Monitoring

1. Set up alerts for job failures
2. Monitor queue lengths and processing times
3. Track API usage and costs
4. Monitor S3 storage usage

## 9. Troubleshooting

### Common Issues

1. **Snapshot Not Found**: Check if snapshot ID is correct and job was triggered
2. **Polling Timeout**: Increase `max_poll_time` or check BrightData service status
3. **Download Failures**: Check network connectivity and API credentials
4. **Processing Errors**: Check S3 storage and database connectivity

### Debug Steps

1. Check job status and logs
2. Verify S3 storage for raw data
3. Check API credentials and quotas
4. Monitor worker processes
5. Review error messages in logs

This comprehensive flow ensures reliable URL resolution and data processing with proper error handling, monitoring, and debugging capabilities. 