"""
PDF export tools for TractionX AI Agent system.
Handles PDF generation and export for deal memos, executive summaries, and reports.
"""

import os
import io
import asyncio
import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pathlib import Path
import tempfile
import base64

from pydantic import BaseModel, Field
import weasyprint
from jinja2 import Environment, FileSystemLoader, Template
from weasyprint import HTML, CSS

from ai.tools.base import BaseTool, ToolInput, ToolOutput
from ai.tools.memo_tools import DealMemoOutput, ExecutiveSummaryOutput, MemoSection
from ai.models import AgentContext
from ai.config import settings

logger = logging.getLogger(__name__)


class PDFExportInput(ToolInput):
    """Input for PDF export tool."""
    content_type: str = Field(..., description="Type of content: memo, summary, report")
    title: str = Field(..., description="Document title")
    content: Union[DealMemoOutput, ExecutiveSummaryOutput, Dict[str, Any]] = Field(..., description="Content to export")
    template_name: Optional[str] = Field(None, description="Custom template name")
    include_branding: bool = Field(default=True, description="Include TractionX branding")
    format_options: Dict[str, Any] = Field(default_factory=dict, description="PDF formatting options")


class PDFExportOutput(ToolOutput):
    """Output from PDF export tool."""
    pdf_data: Optional[bytes] = Field(None, description="Generated PDF as bytes")
    pdf_base64: Optional[str] = Field(None, description="PDF encoded as base64")
    file_size: int = Field(default=0, description="PDF file size in bytes")
    page_count: int = Field(default=0, description="Number of pages")
    generation_time: float = Field(default=0.0, description="Generation time in seconds")


class PDFTemplate(BaseModel):
    """PDF template configuration."""
    name: str = Field(..., description="Template name")
    html_template: str = Field(..., description="HTML template content")
    css_styles: str = Field(..., description="CSS styles")
    variables: Dict[str, Any] = Field(default_factory=dict, description="Template variables")


class PDFExportTool(BaseTool):
    """
    Tool for exporting content to professional PDF documents.
    
    Features:
    - Multiple template support (memo, summary, report)
    - TractionX branding and styling
    - Customizable formatting options
    - Professional layouts with headers/footers
    - Support for tables, charts, and images
    """
    
    def __init__(self):
        super().__init__(
            name="pdf_export",
            description="Export deal memos, summaries, and reports to professional PDF documents"
        )
        
        # Initialize Jinja2 environment
        self.jinja_env = Environment(
            loader=FileSystemLoader(self._get_template_dir()),
            autoescape=True
        )
        
        # Default templates
        self.templates = self._load_default_templates()
    
    async def execute(
        self,
        input_data: PDFExportInput,
        context: AgentContext
    ) -> PDFExportOutput:
        """Export content to PDF."""
        
        start_time = datetime.now()
        logger.info(f"Starting PDF export for: {input_data.title}")
        
        try:
            # Select template
            template = self._select_template(input_data.content_type, input_data.template_name)
            
            # Prepare template variables
            template_vars = self._prepare_template_variables(input_data, context)
            
            # Render HTML
            html_content = self._render_html(template, template_vars)
            
            # Generate PDF
            pdf_data = await self._generate_pdf(html_content, input_data.format_options)
            
            # Encode as base64
            pdf_base64 = base64.b64encode(pdf_data).decode('utf-8')
            
            generation_time = (datetime.now() - start_time).total_seconds()
            
            return PDFExportOutput(
                pdf_data=pdf_data,
                pdf_base64=pdf_base64,
                file_size=len(pdf_data),
                page_count=self._count_pages(pdf_data),
                generation_time=generation_time,
                success=True
            )
            
        except Exception as e:
            logger.error(f"PDF export failed: {e}")
            return PDFExportOutput(
                success=False,
                error=str(e),
                generation_time=(datetime.now() - start_time).total_seconds()
            )
    
    def _get_template_dir(self) -> str:
        """Get template directory path."""
        # Create templates directory if it doesn't exist
        template_dir = Path(__file__).parent.parent.parent / "templates" / "pdf"
        template_dir.mkdir(parents=True, exist_ok=True)
        return str(template_dir)
    
    def _load_default_templates(self) -> Dict[str, PDFTemplate]:
        """Load default PDF templates."""
        templates = {}
        
        # Deal Memo Template
        templates["memo"] = PDFTemplate(
            name="memo",
            html_template=self._get_memo_template(),
            css_styles=self._get_memo_styles(),
            variables={}
        )
        
        # Executive Summary Template
        templates["summary"] = PDFTemplate(
            name="summary",
            html_template=self._get_summary_template(),
            css_styles=self._get_summary_styles(),
            variables={}
        )
        
        # Generic Report Template
        templates["report"] = PDFTemplate(
            name="report",
            html_template=self._get_report_template(),
            css_styles=self._get_report_styles(),
            variables={}
        )
        
        return templates
    
    def _select_template(self, content_type: str, template_name: Optional[str]) -> PDFTemplate:
        """Select appropriate template."""
        if template_name and template_name in self.templates:
            return self.templates[template_name]
        
        # Map content types to templates
        type_mapping = {
            "memo": "memo",
            "deal_memo": "memo",
            "summary": "summary",
            "executive_summary": "summary",
            "report": "report"
        }
        
        template_key = type_mapping.get(content_type, "report")
        return self.templates[template_key]
    
    def _prepare_template_variables(
        self,
        input_data: PDFExportInput,
        context: AgentContext
    ) -> Dict[str, Any]:
        """Prepare variables for template rendering."""
        
        # Base variables
        variables = {
            "title": input_data.title,
            "generated_date": datetime.now().strftime("%B %d, %Y"),
            "generated_time": datetime.now().strftime("%I:%M %p"),
            "include_branding": input_data.include_branding,
            "org_id": str(context.org_id),
            "user_id": str(context.user_id) if context.user_id else None
        }
        
        # Content-specific variables
        if isinstance(input_data.content, DealMemoOutput):
            variables.update(self._prepare_memo_variables(input_data.content))
        elif isinstance(input_data.content, ExecutiveSummaryOutput):
            variables.update(self._prepare_summary_variables(input_data.content))
        elif isinstance(input_data.content, dict):
            variables.update(input_data.content)
        
        # Add format options
        variables.update(input_data.format_options)
        
        return variables
    
    def _prepare_memo_variables(self, memo: DealMemoOutput) -> Dict[str, Any]:
        """Prepare variables for deal memo template."""
        return {
            "memo_title": memo.memo_title,
            "executive_summary": memo.executive_summary,
            "sections": [
                {
                    "title": section.title,
                    "content": section.content,
                    "key_points": section.key_points,
                    "score": section.score,
                    "confidence": section.confidence
                }
                for section in memo.sections
            ],
            "overall_score": memo.overall_score,
            "recommendation": memo.recommendation,
            "key_risks": memo.key_risks,
            "key_opportunities": memo.key_opportunities,
            "generation_time": memo.generation_time
        }
    
    def _prepare_summary_variables(self, summary: ExecutiveSummaryOutput) -> Dict[str, Any]:
        """Prepare variables for executive summary template."""
        return {
            "summary": summary.summary,
            "key_highlights": summary.key_highlights,
            "investment_thesis": summary.investment_thesis,
            "recommendation": summary.recommendation,
            "generation_time": summary.generation_time
        }
    
    def _render_html(self, template: PDFTemplate, variables: Dict[str, Any]) -> str:
        """Render HTML from template."""
        try:
            # Create Jinja2 template
            jinja_template = Template(template.html_template)
            
            # Render HTML
            html_content = jinja_template.render(**variables)
            
            # Add CSS styles
            full_html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{variables.get('title', 'TractionX Document')}</title>
    <style>
        {template.css_styles}
    </style>
</head>
<body>
    {html_content}
</body>
</html>
"""
            
            return full_html
            
        except Exception as e:
            logger.error(f"HTML rendering failed: {e}")
            raise
    
    async def _generate_pdf(
        self,
        html_content: str,
        format_options: Dict[str, Any]
    ) -> bytes:
        """Generate PDF from HTML content."""
        try:
            # Default PDF options
            pdf_options = {
                "page_size": format_options.get("page_size", settings.PDF_PAGE_SIZE),
                "margin": format_options.get("margin", settings.PDF_MARGIN),
                "encoding": "utf-8"
            }
            
            # Create WeasyPrint HTML object
            html_doc = HTML(string=html_content, encoding="utf-8")
            
            # Generate PDF
            pdf_bytes = html_doc.write_pdf()
            
            return pdf_bytes
            
        except Exception as e:
            logger.error(f"PDF generation failed: {e}")
            raise
    
    def _count_pages(self, pdf_data: bytes) -> int:
        """Count pages in PDF (simple estimation)."""
        try:
            # Simple heuristic - count PDF page objects
            pdf_str = pdf_data.decode('latin-1', errors='ignore')
            page_count = pdf_str.count('/Type /Page')
            return max(page_count, 1)  # At least 1 page
        except Exception:
            return 1  # Default to 1 page if counting fails
