"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { motion } from "framer-motion"
import * as z from "zod"
import Link from "next/link"

import { cn } from "@/lib/utils"
import { visualRetreat } from "@/lib/utils/responsive"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { Icons } from "@/components/icons"
import { AuthCard } from "./auth-card"
import { AuthAPI } from "@/lib/api/auth-api"

const forgotPasswordSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
})

type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>

export function ForgotPasswordForm() {
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const form = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: "",
    },
  })

  async function onSubmit(data: ForgotPasswordFormValues) {
    setIsLoading(true)

    try {
      await AuthAPI.forgotPassword(data.email)
      setIsSubmitted(true)
      toast({
        title: "Reset link sent!",
        description: "Check your email for password reset instructions.",
      })
    } catch (error) {
      console.error("Forgot password error:", error)
      toast({
        title: "Something went wrong",
        description: "Please try again later or contact support if the problem persists.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (isSubmitted) {
    return (
      <AuthCard>
        <div className="space-y-6 text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", duration: 0.6 }}
            className="mx-auto flex size-16 items-center justify-center rounded-full bg-gradient-to-r from-green-500 to-emerald-500"
          >
            <Icons.mail className="size-8 text-white" />
          </motion.div>
          
          <div className="space-y-2">
            <h3 className="text-xl font-semibold">Check your email!</h3>
            <p className="text-muted-foreground">
              We've sent password reset instructions to{" "}
              <span className="font-medium text-foreground">
                {form.getValues("email")}
              </span>
            </p>
          </div>

          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Didn't receive the email? Check your spam folder or try again.
            </p>
            
            <Button
              variant="outline"
              onClick={() => {
                setIsSubmitted(false)
                form.reset()
              }}
              className="w-full"
            >
              Try again
            </Button>
          </div>

          <div className="text-center">
            <Link
              href="/login"
              className="text-sm text-blue-600 transition-colors hover:text-blue-500"
            >
              ← Back to sign in
            </Link>
          </div>
        </div>
      </AuthCard>
    )
  }

  return (
    <AuthCard>
      <form onSubmit={form.handleSubmit(onSubmit)} className={visualRetreat.form.container}>
        <div className="space-y-2 text-center">
          <h3 className="text-lg font-semibold">Forgot your password?</h3>
          <p className="text-sm text-muted-foreground">
            No worries! Enter your email and we'll send you reset instructions.
          </p>
        </div>

        <div className={visualRetreat.form.field}>
          <Label htmlFor="email" className="text-sm font-medium text-gray-700">
            Email address
          </Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            autoCapitalize="none"
            autoComplete="email"
            autoCorrect="off"
            disabled={isLoading}
            className={cn(
              visualRetreat.form.input,
              "border-white/20 bg-white/50 focus:border-blue-500/50 focus:ring-blue-500/20"
            )}
            {...form.register("email")}
          />
          {form.formState.errors.email && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-sm text-red-500"
            >
              {form.formState.errors.email.message}
            </motion.p>
          )}
        </div>

        <Button
          type="submit"
          disabled={isLoading}
          className={cn(
            visualRetreat.form.button,
            "bg-gradient-to-r from-blue-600 to-purple-600 font-medium text-white shadow-lg shadow-blue-500/25 hover:from-blue-700 hover:to-purple-700"
          )}
        >
          {isLoading ? (
            <>
              <Icons.spinner className="mr-2 size-4 animate-spin" />
              Sending reset link...
            </>
          ) : (
            "Send reset link"
          )}
        </Button>

        <div className="text-center">
          <Link
            href="/login"
            className="text-sm text-blue-600 transition-colors hover:text-blue-500"
          >
            ← Back to sign in
          </Link>
        </div>
      </form>
    </AuthCard>
  )
}
