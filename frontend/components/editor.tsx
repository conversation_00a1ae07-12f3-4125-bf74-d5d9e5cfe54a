import * as React from "react"

import { cn } from "@/lib/utils"
import { Textarea } from "@/components/ui/textarea"

interface EditorProps {
  placeholder?: string
  value?: string
  onChange?: (value: string) => void
  className?: string
}

export function Editor({
  placeholder = "Write something...",
  value = "",
  onChange,
  className,
}: EditorProps) {
  const handleChange = React.useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      onChange?.(e.target.value)
    },
    [onChange]
  )

  return (
    <div className={cn("relative w-full", className)}>
      <Textarea
        placeholder={placeholder}
        value={value}
        onChange={handleChange}
        className="min-h-[200px] resize-none"
      />
      <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
        This is a placeholder for a rich text editor.
      </div>
    </div>
  )
}
