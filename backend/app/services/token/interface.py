from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from app.models.token import TokenType


class ITokenService(ABC):
    @abstractmethod
    async def create_token(
        self,
        user_id: str,
        token_type: TokenType,
        expires_in: int = 3600,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        pass

    @abstractmethod
    async def verify_token(self, token: str, token_type: TokenType) -> Dict[str, Any]:
        pass

    @abstractmethod
    async def revoke_token(self, token: str) -> None:
        pass

    @abstractmethod
    async def revoke_all_user_tokens(self, user_id: str, token_type: Optional[TokenType] = None) -> None:
        pass

    @abstractmethod
    async def create_password_reset_token(self, user_id: str) -> str:
        pass

    @abstractmethod
    async def create_invitation_token(self, user_id: str, org_id: str, role: str) -> str:
        pass

    @abstractmethod
    async def verify_password_reset_token(self, token: str) -> Dict[str, Any]:
        pass

    @abstractmethod
    async def verify_invitation_token(self, token: str) -> Dict[str, Any]:
        pass
