from typing import Type, Any, Union, Optional, Set, get_origin, get_args
from pydantic import create_model, Field

from app.models.base import TractionXModel
from app.core.logging import get_logger
logger = get_logger(__name__)

class PopulateReference:
    def __init__(self, model_class: Union[Type[Any], str]):
        self.model_class = model_class
        self.is_forward_ref = isinstance(model_class, str)

    def __repr__(self):
        return f"PopulateReference({self.model_class!r})"

    def __hash__(self):
        return hash((self.model_class, self.is_forward_ref))

    def __eq__(self, other):
        return (
            isinstance(other, PopulateReference)
            and self.model_class == other.model_class
            and self.is_forward_ref == other.is_forward_ref
        )


def populate_reference(model_class: Union[Type[Any], str]) -> PopulateReference:
    """
    Decorator-like utility for marking a field as a reference to another model class.

    This can be used in ODM schema definitions to indicate that a field should be 
    auto-populated during document retrieval.

    Args:
        model_class (Union[Type[Any], str]): The model class that the field should reference.
            Can be either a class or a string name of the class.

    Returns:
        Dict[str, Any]: Metadata indicating population behavior, typically merged into 
                        field definitions.
    """
    return PopulateReference(model_class)


def partial_model(suffix: str = "Update", exclude_fields: Set[str] = None, make_optional: bool = None):
    """
    Decorator to generate a partial version of a Pydantic model based on the suffix:
    - For 'Update' suffix: All fields are made optional (default behavior)
    - For 'Create' suffix: Fields maintain their original required/optional status
    - For other suffixes: Follows make_optional parameter (defaults to True for backward compatibility)
    
    Both cases inherit from TractionXModel and exclude common metadata fields.
    
    Args:
        suffix: Suffix to append to the model name (default: "Update")
        exclude_fields: Additional fields to exclude from the partial model
        make_optional: Override whether fields should be optional. If None, determined by suffix
                      (True for backward compatibility)
    """
    if exclude_fields is None:
        exclude_fields = {"id", "created_at", "updated_at", "created_by", "updated_by", "version"}
    else:
        exclude_fields.update({"id", "created_at", "updated_at", "created_by", "updated_by", "version"})

    def decorator(model_cls: Type[TractionXModel]) -> Type[TractionXModel]:
        logger.debug(f"Creating {suffix} model for {model_cls.__name__}")
        
        # Determine if fields should be optional based on suffix and make_optional parameter
        should_make_optional = make_optional if make_optional is not None else (
            suffix != "Create"  # True for "Update" and any other suffix (backward compatible)
        )
        
        # Get all fields from the model
        fields = {}
        model_fields = model_cls.model_fields

        for field_name, field in model_fields.items():
            if field_name in exclude_fields:
                continue

            # Get the field type
            field_type = field.annotation
            origin = get_origin(field_type)
            
            # Handle nested models
            if isinstance(field_type, type) and issubclass(field_type, TractionXModel):
                nested_partial = partial_model(
                    suffix, 
                    exclude_fields,
                    make_optional=should_make_optional
                )(field_type)
                field_type = Optional[nested_partial] if should_make_optional else nested_partial
            elif origin is not None:
                # Handle container types (List, Dict, etc.)
                args = get_args(field_type)
                if args and isinstance(args[0], type) and issubclass(args[0], TractionXModel):
                    nested_partial = partial_model(
                        suffix, 
                        exclude_fields,
                        make_optional=should_make_optional
                    )(args[0])
                    field_type = origin[nested_partial, *args[1:]]
                if should_make_optional:
                    field_type = Optional[field_type]
            elif should_make_optional:
                field_type = Optional[field_type]

            # Create new field info with the same metadata
            field_info = field.json_schema_extra or {}
            
            # For optional models (Update or explicitly requested), everything is optional
            # For Create models or non-optional, maintain the original default value and required status
            if should_make_optional:
                field_kwargs = {
                    "default": None,
                    "description": field.description,
                    "title": getattr(field, 'title', None),
                    "examples": getattr(field, 'examples', None),
                    **field_info
                }
            else:
                field_kwargs = {
                    "description": field.description,
                    "title": getattr(field, 'title', None),
                    "examples": getattr(field, 'examples', None),
                    **field_info
                }
                # Preserve the original default value if it exists
                if field.default is not None:
                    field_kwargs["default"] = field.default
                elif field.default_factory is not None:
                    field_kwargs["default_factory"] = field.default_factory

            fields[field_name] = (field_type, Field(**field_kwargs))

        # Create the partial model class
        partial_cls = create_model(
            model_cls.__name__ + suffix,
            __base__=TractionXModel,
            **fields
        )

        # Copy over any class validators
        if hasattr(model_cls, "__validators__"):
            partial_cls.__validators__ = model_cls.__validators__

        # Preserve model config
        if hasattr(model_cls, "model_config"):
            partial_cls.model_config = model_cls.model_config

        return partial_cls

    return decorator