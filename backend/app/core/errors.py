from enum import Enum
from typing import Optional, Dict, Any
import logging
from fastapi import HTTPException, status

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    VALIDATION = "validation"
    DATABASE = "database"
    EXTERNAL_SERVICE = "external_service"
    INTERNAL = "internal"


class APIError(Exception):
    def __init__(
        self,
        message: str,
        status_code: int,
        severity: ErrorSeverity,
        category: ErrorCategory,
        details: Optional[Dict[str, Any]] = None,
        should_alert: bool = False,
        should_raise: bool = True
    ):
        self.message = message
        self.status_code = status_code
        self.severity = severity
        self.category = category
        self.details = details or {}
        self.should_alert = should_alert
        self.should_raise = should_raise
        super().__init__(message)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "message": self.message,
            "status_code": self.status_code,
            "severity": self.severity.value,
            "category": self.category.value,
            "details": self.details
        }

    def to_http_exception(self) -> HTTPException:
        return HTTPException(
            status_code=self.status_code,
            detail=self.to_dict()
        )


class AuthenticationError(APIError):
    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        should_alert: bool = False
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.AUTHENTICATION,
            details=details,
            should_alert=should_alert
        )


class AuthorizationError(APIError):
    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        should_alert: bool = True
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_403_FORBIDDEN,
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.AUTHORIZATION,
            details=details,
            should_alert=should_alert
        )


class ValidationError(APIError):
    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        should_alert: bool = False
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_400_BAD_REQUEST,
            severity=ErrorSeverity.WARNING,
            category=ErrorCategory.VALIDATION,
            details=details,
            should_alert=should_alert
        )


class DatabaseError(APIError):
    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        should_alert: bool = True
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.DATABASE,
            details=details,
            should_alert=should_alert
        )


class ExternalServiceError(APIError):
    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        should_alert: bool = True
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_502_BAD_GATEWAY,
            severity=ErrorSeverity.ERROR,
            category=ErrorCategory.EXTERNAL_SERVICE,
            details=details,
            should_alert=should_alert
        )


class InternalError(APIError):
    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        should_alert: bool = True
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            severity=ErrorSeverity.CRITICAL,
            category=ErrorCategory.INTERNAL,
            details=details,
            should_alert=should_alert
        )


async def handle_error(error: APIError) -> None:
    """Handle API errors with appropriate logging and alerting."""
    # Log the error
    log_message = f"{error.category.value.upper()} Error: {error.message}"
    if error.details:
        log_message += f" Details: {error.details}"

    if error.severity == ErrorSeverity.CRITICAL:
        logger.critical(log_message, exc_info=True)
    elif error.severity == ErrorSeverity.ERROR:
        logger.error(log_message, exc_info=True)
    elif error.severity == ErrorSeverity.WARNING:
        logger.warning(log_message)
    else:
        logger.info(log_message)

    # Send alert if required
    if error.should_alert:
        await send_alert(error)


async def send_alert(error: APIError) -> None:
    """Send alert for critical errors."""
    # TODO: Implement your alerting system here (e.g., Slack, Email, etc.)
    alert_message = f"""
🚨 ALERT: {error.category.value.upper()} Error
Message: {error.message}
Severity: {error.severity.value}
Details: {error.details}
    """
    logger.critical(alert_message)
