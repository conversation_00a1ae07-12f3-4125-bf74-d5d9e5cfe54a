"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON>, usePathname } from "next/navigation"
import { motion } from "framer-motion"
import { User, Building2, Users, Settings, <PERSON>, Target } from "lucide-react"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { useAuth } from "@/lib/auth-context"

interface SettingsLayoutProps {
  children?: React.ReactNode
  activeTab?: string
  className?: string
}

interface TabConfig {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  description: string
  adminOnly?: boolean
  disabled?: boolean
}

const tabs: TabConfig[] = [
  {
    id: "profile",
    label: "Profile",
    icon: User,
    description: "Manage your personal account settings and preferences"
  },
  {
    id: "organization", 
    label: "Organization",
    icon: Building2,
    description: "Configure your organization's branding and settings",
    adminOnly: true
  },
  {
    id: "members",
    label: "Members",
    icon: Users,
    description: "Manage team members and user permissions",
    adminOnly: true
  },
  {
    id: "org-thesis",
    label: "Org Thesis",
    icon: Target,
    description: "Configure your organization's investment thesis and preferences",
    adminOnly: true
  },
  {
    id: "orbit-ai",
    label: "Orbit AI Controls",
    icon: Lock,
    description: "Advanced AI configuration and controls",
    adminOnly: true,
    disabled: true
  }
]

export function SettingsLayout({
  children,
  activeTab: propActiveTab,
  className
}: SettingsLayoutProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { user } = useAuth()

  // Determine active tab from URL
  const getActiveTabFromPath = () => {
    if (pathname?.includes('/settings/profile')) return 'profile'
    if (pathname?.includes('/settings/organization')) return 'organization'
    if (pathname?.includes('/settings/members')) return 'members'
    if (pathname?.includes('/settings/org-thesis')) return 'org-thesis'
    if (pathname?.includes('/settings/orbit-ai')) return 'orbit-ai'
    return 'profile'
  }

  const [activeTab, setActiveTab] = useState(propActiveTab || getActiveTabFromPath())

  useEffect(() => {
    const tabFromPath = getActiveTabFromPath()
    setActiveTab(tabFromPath)
  }, [pathname])

  // Check if user is admin based on superuser status or role
  // For now, we'll consider users with role_id as admins, but this should be refined based on actual role system
  const isAdmin = user?.is_superuser || (user?.role_id !== null && user?.role_id !== undefined)

  const availableTabs = tabs.filter(tab =>
    !tab.adminOnly || isAdmin
  )

  const handleTabChange = (tabId: string) => {
    const tab = tabs.find(t => t.id === tabId)
    if (tab?.disabled) return // Prevent navigation to disabled tabs
    
    setActiveTab(tabId)
    router.push(`/settings/${tabId}`)
  }

  return (
    <div className={cn("min-h-screen bg-white", className)}>
      <div className="relative">
        {/* Header */}
        <div className="border-b border-gray-100 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
          <div className="container mx-auto max-w-screen-xl px-6 py-8 sm:px-8 sm:py-10 lg:px-10">
            <div className="flex items-center space-x-4">
              <div className="flex size-12 items-center justify-center rounded-xl bg-gradient-to-r from-[#6366F1] via-[#4F46E5] to-[#06B6D4] shadow-lg">
                <Settings className="size-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                  Settings
                </h1>
                <p className="mt-2 text-base text-gray-600 sm:text-lg">
                  Manage your account, organization, and team preferences
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="container mx-auto max-w-screen-xl px-6 py-8 sm:px-8 sm:py-10 lg:px-10">
          <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-8">
            {/* Tab Navigation - Left to Right */}
            <TabsList className="flex w-full justify-start rounded-xl bg-gray-50 p-1 shadow-sm border border-gray-100">
              {availableTabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    disabled={tab.disabled}
                    className={cn(
                      "flex items-center space-x-3 rounded-lg px-6 py-3 text-sm font-medium transition-all duration-200",
                      "data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#e0e7ef] data-[state=active]:via-[#f3f4f6] data-[state=active]:to-[#cbd5e1] data-[state=active]:text-[#22292f] data-[state=active]:shadow-md data-[state=active]:border data-[state=active]:border-gray-200",
                      "data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-gray-900 data-[state=inactive]:hover:bg-gray-100",
                      tab.disabled && "opacity-50 cursor-not-allowed data-[state=inactive]:hover:bg-transparent data-[state=inactive]:hover:text-gray-400"
                    )}
                  >
                    <Icon className="size-4" />
                    <span>{tab.label}</span>
                  </TabsTrigger>
                )
              })}
            </TabsList>

            {/* Tab Content */}
            <div className="space-y-8">
              {availableTabs.map((tab) => (
                <TabsContent key={tab.id} value={tab.id} className="space-y-0">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, ease: "easeOut" }}
                    className="space-y-8"
                  >
                    <div className="space-y-4">
                      <div className="flex items-center space-x-4">
                        <div className="flex size-10 items-center justify-center rounded-xl bg-gradient-to-r from-[#6366F1] via-[#4F46E5] to-[#06B6D4] shadow-md">
                          <tab.icon className="size-5 text-white" />
                        </div>
                        <div>
                          <h2 className="text-2xl font-bold text-gray-900 sm:text-3xl">
                            {tab.label}
                          </h2>
                          <p className="text-base text-gray-600">
                            {tab.description}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-8">
                      {children}
                    </div>
                  </motion.div>
                </TabsContent>
              ))}
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  )
}

// Export individual tab content components for use in pages
export function ProfileTabContent({ children }: { children: React.ReactNode }) {
  return <div className="space-y-8">{children}</div>
}

export function OrganizationTabContent({ children }: { children: React.ReactNode }) {
  return <div className="space-y-8">{children}</div>
}

export function MembersTabContent({ children }: { children: React.ReactNode }) {
  return <div className="space-y-8">{children}</div>
}

export function OrgThesisTabContent({ children }: { children: React.ReactNode }) {
  return <div className="space-y-8">{children}</div>
}

export function OrbitAiTabContent({ children }: { children: React.ReactNode }) {
  return <div className="space-y-8">{children}</div>
}
