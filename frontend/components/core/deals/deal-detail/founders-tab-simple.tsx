import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { 
  Linkedin, 
  Twitter, 
  Github, 
  ExternalLink,
  Star,
  Building,
  Award,
  Edit2,
  Check,
  X,
  Users,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  MapPin,
  Calendar,
  GraduationCap,
  Briefcase,
  Download,
  Copy,
  HelpCircle,
  Mail,
  Globe,
  User,
  BookOpen,
  Zap
} from "lucide-react";
import { cn } from "@/lib/utils";
import { DealDetailData } from "@/lib/types/deal-detail";
import { Founder } from "@/lib/types/deal";
import { DealAPI } from "@/lib/api/deal-api";
import { FounderAPI } from "@/lib/api/founder-api";
import { useToast } from "@/components/ui/use-toast";

interface FoundersTabProps {
  deal: DealDetailData;
  onDealUpdate?: (deal: DealDetailData) => void;
}

// Type guard for enriched founder
function isEnrichedFounder(f: any): f is { signals: { score?: number; tags?: string[] } } {
  return f && typeof f === 'object' && 'signals' in f && typeof f.signals === 'object';
}

export default function FoundersTabSimple(props: FoundersTabProps) {
  // Defensive: check all critical imports
  if (
    typeof Card === 'undefined' ||
    typeof CardContent === 'undefined' ||
    typeof Button === 'undefined' ||
    typeof Badge === 'undefined' ||
    typeof motion === 'undefined' ||
    typeof Users === 'undefined'
  ) {
    return <div style={{color: 'red', fontWeight: 'bold'}}>❌ One or more UI imports are undefined. Check your UI library exports and import paths.</div>;
  }

  const [founders, setFounders] = useState<Founder[]>(props.deal.founders || []);
  const { toast } = useToast ? useToast() : { toast: () => {} };
  const [editingFounderIndex, setEditingFounderIndex] = useState<number | null>(null);
  const [editingLinkedIn, setEditingLinkedIn] = useState<string>('');
  const [isUpdating, setIsUpdating] = useState(false);
  const [analysisState, setAnalysisState] = useState<{
    enrichedFounders: Founder[];
    isLoading: boolean;
    error: string | null;
    teamSummary: string | null;
    isGeneratingSummary: boolean;
  }>({
    enrichedFounders: [],
    isLoading: false,
    error: null,
    teamSummary: null,
    isGeneratingSummary: false
  });

  useEffect(() => {
    setFounders(props.deal.founders || []);
  }, [props.deal.founders]);

  // Update founder LinkedIn
  const updateFounderLinkedIn = async (founderIndex: number, newLinkedIn: string) => {
    try {
      setIsUpdating(true);
      
      // Create updated founders array
      const updatedFounders = [...founders];
      updatedFounders[founderIndex] = {
        ...updatedFounders[founderIndex],
        linkedin: newLinkedIn
      };

      // Update deal with new founders data
      const updatedDeal = await DealAPI.updateDeal(props.deal.id, {
        founders: updatedFounders
      });

      if (updatedDeal) {
        setFounders(updatedFounders);
        setEditingFounderIndex(null);
        setEditingLinkedIn('');
        
        // Call parent callback if provided
        if (props.onDealUpdate) {
          // Cast to DealDetailData for the callback
          props.onDealUpdate(updatedDeal as DealDetailData);
        }

        toast({
          title: "LinkedIn Updated",
          description: "Founder LinkedIn profile has been updated successfully.",
        });
      }
    } catch (error: any) {
      console.error('Error updating founder LinkedIn:', error);
      toast({
        title: "Update Failed",
        description: error.message || "Failed to update founder LinkedIn. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Generate team summary
  const generateTeamSummary = async () => {
    try {
      setAnalysisState(prev => ({ ...prev, isGeneratingSummary: true }));
      
      // Safely extract domain from company_website
      let domain = "unknown";
      if (props.deal.company_website) {
        try {
          // Add protocol if missing
          const websiteUrl = props.deal.company_website.startsWith('http') 
            ? props.deal.company_website 
            : `https://${props.deal.company_website}`;
          domain = new URL(websiteUrl).hostname;
        } catch (urlError) {
          console.warn('Invalid company website URL:', props.deal.company_website);
          // Try to extract domain manually if URL parsing fails
          const website = props.deal.company_website.replace(/^https?:\/\//, '');
          domain = website.split('/')[0] || "unknown";
        }
      }
      
      // Ensure domain is never undefined
      if (!domain || domain === "unknown") {
        domain = props.deal.company_name?.toLowerCase().replace(/\s+/g, '') || "unknown";
      }
      
      console.log('Deal data:', {
        company_website: props.deal.company_website,
        company_name: props.deal.company_name,
        org_id: props.deal.org_id
      });
      console.log('Extracted domain:', domain);
      console.log('Founders to enrich:', founders);
      
      // Trigger enrichment for ALL founders
      const enrichmentPromises = founders.map(async (founder, index) => {
        const payload = {
          company_id: domain,
          org_id: props.deal.org_id || localStorage.getItem('orgId') || "",
          company_name: props.deal.company_name || "Unknown Company",
          domain: domain,
          form_data: {
            founder_name: founder.name || "",
            founder_linkedin: founder.linkedin || ""
          },
          pipeline_types: ["founder"],
          priority: "high" as const
        };

        console.log(`Triggering enrichment for founder ${index + 1}:`, payload);
        return await FounderAPI.triggerEnrichment(payload);
      });
      
      // Wait for all enrichment requests to complete
      const responses = await Promise.all(enrichmentPromises);
      
      // Check if all requests were successful
      const allSuccessful = responses.every(response => response.success);
      
      if (allSuccessful) {
        toast({
          title: "Founder Enrichment Started",
          description: `AI analysis is now processing for ${founders.length} founder${founders.length !== 1 ? 's' : ''}. This may take a few minutes.`,
        });

        // Optionally refresh the page or refetch data after a delay
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      } else {
        const failedCount = responses.filter(response => !response.success).length;
        throw new Error(`${failedCount} out of ${founders.length} enrichment requests failed`);
      }
    } catch (error: any) {
      console.error('Error triggering founder enrichment:', error);
      toast({
        title: "Enrichment Failed",
        description: error.message || "Failed to start founder enrichment. Please try again.",
        variant: "destructive"
      });
    } finally {
      setAnalysisState(prev => ({ ...prev, isGeneratingSummary: false }));
    }
  };

  const teamMetrics = {
    totalFounders: analysisState.enrichedFounders.length || founders.length,
    avgScore: analysisState.enrichedFounders.length > 0 
      ? Math.round(
          analysisState.enrichedFounders.reduce(
            (sum, f) => sum + (isEnrichedFounder(f) && typeof f.signals.score === 'number' ? f.signals.score : 0),
            0
          ) / analysisState.enrichedFounders.length
        )
      : 0,
    topTags: analysisState.enrichedFounders.length > 0
      ? Array.from(
          new Set(
            analysisState.enrichedFounders.flatMap(f =>
              isEnrichedFounder(f) && Array.isArray(f.signals.tags) ? f.signals.tags : []
            )
          )
        ).slice(0, 3)
      : []
  };

  if (founders.length === 0 && analysisState.enrichedFounders.length === 0) {
    return (
      <div className="flex min-h-[400px] flex-col items-center justify-center rounded-xl border border-dashed p-12 text-center">
        <div className="mb-4 flex size-24 items-center justify-center rounded-full bg-muted">
          <Users className="size-12" />
        </div>
        <h2 className="mb-2 text-2xl font-bold">No founder data available for this deal</h2>
        <p className="text-muted-foreground">
          Founder profiles will appear here once they're added to the deal.
        </p>
      </div>
    );
  }

  const handleSocialClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const startEditingLinkedIn = (founderIndex: number, currentLinkedIn: string) => {
    setEditingFounderIndex(founderIndex);
    setEditingLinkedIn(currentLinkedIn || '');
  };

  const cancelEditing = () => {
    setEditingFounderIndex(null);
    setEditingLinkedIn('');
  };

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Team Overview Header - Sticky */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="sticky top-0 z-10 border-b bg-background/80 backdrop-blur-sm"
        >
          <Card className="border-0 bg-background/60 shadow-sm backdrop-blur-md">
            <CardContent className="p-6">
              <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <Users className="size-5 text-muted-foreground" />
                    <span className="text-2xl font-bold">{teamMetrics.totalFounders}</span>
                    <span className="text-sm text-muted-foreground">
                      Founder{teamMetrics.totalFounders !== 1 ? 's' : ''}
                    </span>
                  </div>
                  {teamMetrics.avgScore > 0 && (
                    <div className="flex items-center gap-2">
                      <TrendingUp className="size-5 text-muted-foreground" />
                      <span className="text-2xl font-bold">{teamMetrics.avgScore}</span>
                      <span className="text-sm text-muted-foreground">/100</span>
                      <Badge variant="outline" className="text-xs">Avg Score</Badge>
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-3">
                  {teamMetrics.topTags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {teamMetrics.topTags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                  <Button
                    onClick={generateTeamSummary}
                    disabled={analysisState.isGeneratingSummary}
                    className="gap-2"
                    size="sm"
                  >
                    <Zap className="size-4" />
                    {analysisState.isGeneratingSummary ? 'Generating...' : 'Generate Summary'}
                  </Button>
                </div>
              </div>
              {analysisState.teamSummary && (
                <div className="mt-4 rounded-lg border bg-muted/50 p-4">
                  <p className="text-sm text-muted-foreground">{analysisState.teamSummary}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Enhanced Founder Cards */}
        <div className="space-y-8">
          {analysisState.isLoading ? (
            <div className="py-8 text-center">
              <div className="mx-auto mb-4 size-8 animate-spin rounded-full border-b-2 border-primary"></div>
              <p className="text-sm text-muted-foreground">Loading enriched founder data...</p>
            </div>
          ) : analysisState.error ? (
            <div className="py-8 text-center">
              <p className="mb-4 text-sm text-destructive">{analysisState.error}</p>
              <p className="text-xs text-muted-foreground">Showing basic founder information instead</p>
            </div>
          ) : null}
          
          {/* Show enhanced founder cards */}
          {founders.map((founder, index) => (
            <motion.div
              key={founder._id || index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="overflow-hidden">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <Avatar className="size-20">
                      <AvatarImage 
                        src={founder.profile_picture || undefined} 
                        alt={founder.name || 'Founder'} 
                      />
                      <AvatarFallback className="text-lg">
                        {(founder.name || 'F').split(' ').map(n => n[0]).join('').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="min-w-0 flex-1">
                      {/* Name and Role */}
                      <div className="mb-4">
                        <h3 className="text-xl font-bold">
                          {founder.name || 'Unknown Founder'}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          {Array.isArray(founder.role) ? founder.role.join(', ') : founder.role || 'Role not specified'}
                        </p>
                      </div>

                      {/* Skills Section */}
                      {founder.skills && Array.isArray(founder.skills) && founder.skills.length > 0 && (
                        <div className="mb-4">
                          <div className="flex items-center gap-2 mb-2">
                            <Zap className="size-4 text-muted-foreground" />
                            <span className="text-sm font-medium">Skills</span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {founder.skills.map((skill, skillIndex) => (
                              <Badge key={skillIndex} variant="outline" className="text-xs">
                                {skill}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Experience Section */}
                      {founder.experience && (
                        <div className="mb-4">
                          <div className="flex items-center gap-2 mb-2">
                            <Briefcase className="size-4 text-muted-foreground" />
                            <span className="text-sm font-medium">Experience</span>
                          </div>
                          <div className="space-y-1">
                            {Array.isArray(founder.experience) ? (
                              <>
                                {(founder.experience as any[]).slice(0, 3).map((exp, expIndex) => (
                                  <div key={expIndex} className="text-xs text-muted-foreground">
                                    {typeof exp === 'string'
                                      ? exp
                                      : exp && typeof exp === 'object'
                                        ? [(exp as any).company, (exp as any).position, (exp as any).duration, (exp as any).description]
                                            .filter(Boolean)
                                            .join(' • ')
                                        : ''}
                                  </div>
                                ))}
                                {(founder.experience as any[]).length > 3 && (
                                  <div className="text-xs text-muted-foreground">
                                    +{(founder.experience as any[]).length - 3} more
                                  </div>
                                )}
                              </>
                            ) : founder.experience ? (
                              <div className="text-xs text-muted-foreground">
                                {typeof founder.experience === 'string'
                                  ? founder.experience
                                  : founder.experience && typeof founder.experience === 'object'
                                    ? [(founder.experience as any).company, (founder.experience as any).position, (founder.experience as any).duration, (founder.experience as any).description]
                                        .filter(Boolean)
                                        .join(' • ')
                                    : ''}
                              </div>
                            ) : null}
                          </div>
                        </div>
                      )}

                      {/* Education Section */}
                      {founder.education && (
                        <div className="mb-4">
                          <div className="flex items-center gap-2 mb-2">
                            <GraduationCap className="size-4 text-muted-foreground" />
                            <span className="text-sm font-medium">Education</span>
                          </div>
                          <div className="space-y-1">
                            {Array.isArray(founder.education) ? (
                              <>
                                {(founder.education as any[]).slice(0, 2).map((edu, eduIndex) => (
                                  <div key={eduIndex} className="text-xs text-muted-foreground">
                                    {typeof edu === 'string'
                                      ? edu
                                      : edu && typeof edu === 'object'
                                        ? [(edu as any).institution, (edu as any).degree, (edu as any).major, (edu as any).startDate, (edu as any).endDate]
                                            .filter(Boolean)
                                            .join(' • ')
                                        : ''}
                                  </div>
                                ))}
                                {(founder.education as any[]).length > 2 && (
                                  <div className="text-xs text-muted-foreground">
                                    +{(founder.education as any[]).length - 2} more
                                  </div>
                                )}
                              </>
                            ) : founder.education ? (
                              <div className="text-xs text-muted-foreground">
                                {typeof founder.education === 'string'
                                  ? founder.education
                                  : founder.education && typeof founder.education === 'object'
                                    ? [(founder.education as any).institution, (founder.education as any).degree, (founder.education as any).major, (founder.education as any).startDate, (founder.education as any).endDate]
                                        .filter(Boolean)
                                        .join(' • ')
                                    : ''}
                              </div>
                            ) : null}
                          </div>
                        </div>
                      )}

                      {/* Achievements Section */}
                      {founder.achievements && Array.isArray(founder.achievements) && founder.achievements.length > 0 && (
                        <div className="mb-4">
                          <div className="flex items-center gap-2 mb-2">
                            <Award className="size-4 text-muted-foreground" />
                            <span className="text-sm font-medium">Achievements</span>
                          </div>
                          <div className="space-y-1">
                            {founder.achievements.slice(0, 2).map((achievement, achIndex) => (
                              <div key={achIndex} className="text-xs text-muted-foreground">
                                {typeof achievement === 'string'
                                  ? achievement
                                  : achievement && typeof achievement === 'object'
                                    ? Object.values(achievement).filter(Boolean).join(' • ')
                                    : ''}
                              </div>
                            ))}
                            {founder.achievements.length > 2 && (
                              <div className="text-xs text-muted-foreground">
                                +{founder.achievements.length - 2} more
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* LinkedIn Section with Editing */}
                      <div className="mt-4 pt-4 border-t">
                        {editingFounderIndex === index ? (
                          <div className="flex items-center gap-2">
                            <Input
                              value={editingLinkedIn}
                              onChange={(e) => setEditingLinkedIn(e.target.value)}
                              placeholder="Enter LinkedIn URL"
                              className="flex-1"
                              disabled={isUpdating}
                            />
                            <Button
                              size="sm"
                              onClick={() => updateFounderLinkedIn(index, editingLinkedIn)}
                              disabled={isUpdating}
                            >
                              <Check className="size-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={cancelEditing}
                              disabled={isUpdating}
                            >
                              <X className="size-4" />
                            </Button>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            {founder.linkedin ? (
                              <>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleSocialClick(founder.linkedin!)}
                                  className="h-8 gap-2"
                                >
                                  <Linkedin className="size-4" />
                                  LinkedIn
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => startEditingLinkedIn(index, founder.linkedin!)}
                                  className="h-8"
                                >
                                  <Edit2 className="size-4" />
                                </Button>
                              </>
                            ) : (
                              <>
                                <span className="text-sm text-muted-foreground">No LinkedIn profile</span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => startEditingLinkedIn(index, '')}
                                  className="h-8 gap-2"
                                >
                                  <Edit2 className="size-4" />
                                  Add LinkedIn
                                </Button>
                              </>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </TooltipProvider>
  );
}
