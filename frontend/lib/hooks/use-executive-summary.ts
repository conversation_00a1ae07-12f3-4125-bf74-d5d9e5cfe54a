import { useState, useEffect, useCallback } from 'react'
import { DealDetailAPI } from '@/lib/api/deal-detail-api'
import { useToast } from '@/components/ui/use-toast'
import { useAuth } from '@/lib/auth-context'

interface ExecutiveSummaryData {
  executive_summary: { title: string; content: string }[]
  metadata?: {
    model_used: string
    generated_at: string
  }
}

interface UseExecutiveSummaryReturn {
  data: ExecutiveSummaryData | null
  loading: boolean
  error: string | null
  refreshing: boolean
  hasData: boolean
  refresh: () => Promise<void>
  reload: () => Promise<void>
}

export function useExecutiveSummary({ dealId }: { dealId: string }): UseExecutiveSummaryReturn {
  const [data, setData] = useState<ExecutiveSummaryData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)
  const { toast } = useToast()
  const { isAuthenticated } = useAuth()

  const fetchExecutiveSummary = useCallback(async () => {
    // Don't fetch if not authenticated
    if (!isAuthenticated) {
      setError('Authentication required. Please log in to view executive summary.')
      setLoading(false)
      return
    }

    try {
      setError(null)
      console.log(`🔍 Fetching executive summary for deal ${dealId}`)
      const executiveSummaryData = await DealDetailAPI.getExecutiveSummary(dealId)
      console.log('✅ Executive summary data received:', executiveSummaryData)
      setData(executiveSummaryData)
    } catch (err: any) {
      // Handle different types of errors gracefully
      let errorMessage = 'Failed to fetch executive summary'
      
      if (err?.response?.status === 401) {
        errorMessage = 'Authentication required. Please log in to view executive summary.'
      } else if (err?.response?.status === 403) {
        errorMessage = 'Access denied. You do not have permission to view this executive summary.'
      } else if (err?.response?.status === 404) {
        errorMessage = 'Executive summary not found for this deal.'
      } else if (err?.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.'
      } else if (err?.message) {
        errorMessage = err.message
      }
      
      console.error('❌ Error fetching executive summary:', {
        error: err,
        message: errorMessage,
        status: err?.response?.status,
        dealId,
        responseData: err?.response?.data,
        errorDetail: err?.response?.data?.detail
      })
      
      setError(errorMessage)
    }
  }, [dealId, isAuthenticated])

  const load = useCallback(async () => {
    setLoading(true)
    await fetchExecutiveSummary()
    setLoading(false)
  }, [fetchExecutiveSummary])

  const refresh = useCallback(async () => {
    setRefreshing(true)
    await fetchExecutiveSummary()
    setRefreshing(false)
    
    toast({
      title: "Executive Summary Refreshed",
      description: "The executive summary has been updated.",
    })
  }, [fetchExecutiveSummary, toast])

  const reload = useCallback(async () => {
    setError(null)
    await load()
  }, [load])

  useEffect(() => {
    load()
  }, [load])

  const hasData = Boolean(data?.executive_summary?.length)

  return {
    data,
    loading,
    error,
    refreshing,
    hasData,
    refresh,
    reload
  }
} 