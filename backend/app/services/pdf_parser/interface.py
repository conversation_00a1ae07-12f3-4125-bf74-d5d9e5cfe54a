"""
PDF Parser Service Interface

Defines the interface for PDF parsing services.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from io import BytesIO


class PDFParserInterface(ABC):
    """Interface for PDF parsing services."""

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the PDF parser service."""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup service resources."""
        pass

    @abstractmethod
    async def extract_text_from_pdf(
        self, 
        pdf_content: BytesIO,
        filename: str
    ) -> Dict[str, Any]:
        """
        Extract text from PDF content.
        
        Args:
            pdf_content: PDF file content as BytesIO
            filename: Original filename for context
            
        Returns:
            Dictionary containing:
            - text: Extracted text content
            - pages: List of page texts
            - page_count: Number of pages
            - metadata: PDF metadata
        """
        pass

    @abstractmethod
    async def extract_text_from_s3(
        self,
        s3_bucket: str,
        s3_key: str
    ) -> Dict[str, Any]:
        """
        Extract text from PDF stored in S3.
        
        Args:
            s3_bucket: S3 bucket name
            s3_key: S3 object key
            
        Returns:
            Dictionary containing extracted text and metadata
        """
        pass

    @abstractmethod
    def detect_pitch_type(self, page_count: int, text_content: str) -> str:
        """
        Detect if the document is a deck or one-pager.
        
        Args:
            page_count: Number of pages in the document
            text_content: Extracted text content
            
        Returns:
            "deck" or "one_pager"
        """
        pass
