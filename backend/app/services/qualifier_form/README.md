# Qualifier Form Module

A specialized form module that extends the base form system with sharing and trigger capabilities.

## Features

- Built on top of the existing form system
- Integrated with sharing framework for distribution
- Connected to trigger system for downstream actions
- Statistics tracking for views and submissions
- Configurable for various use cases

## Usage

### Creating a Qualifier Form

```python
from app.services.factory import get_qualifier_form_service

async def create_form_example():
    qualifier_form_service = await get_qualifier_form_service()
    
    # Create a qualifier form
    form = await qualifier_form_service.create_qualifier_form(
        name="Startup Qualifier",
        description="Initial qualification form for startups",
        org_id="org123",
        sections=["section1", "section2"],
        default_section_ids=["section1"],
        is_active=True
    )
    
    return form
```

### Configuring Sharing

```python
from app.services.sharing.interfaces import SharingType, EmbedType

async def configure_sharing_example(form_id):
    qualifier_form_service = await get_qualifier_form_service()
    
    # Configure sharing for a qualifier form
    form = await qualifier_form_service.configure_sharing(
        form_id=form_id,
        sharing_config={
            "enabled": True,
            "sharing_types": [SharingType.LINK, SharingType.EMBED, SharingType.QR_CODE],
            "allowed_domains": ["example.com"],
            "embed_type": EmbedType.INLINE,
            "tracking_enabled": True
        }
    )
    
    return form
```

### Configuring Triggers

```python
from app.services.trigger.interfaces import TriggerType

async def configure_triggers_example(form_id):
    qualifier_form_service = await get_qualifier_form_service()
    
    # Configure triggers for a qualifier form
    form = await qualifier_form_service.configure_triggers(
        form_id=form_id,
        trigger_configs=[
            {
                "type": TriggerType.EMAIL_NOTIFICATION,
                "name": "Submission Notification",
                "description": "Send email when form is submitted",
                "enabled": True,
                "config": {
                    "template_id": "submission_notification",
                    "recipient_field": "email"
                }
            },
            {
                "type": TriggerType.THESIS_FORM,
                "name": "Create Thesis Form",
                "description": "Create thesis form from submission",
                "enabled": True,
                "config": {
                    "thesis_id": "thesis123"
                }
            }
        ]
    )
    
    return form
```

### Submitting a Qualifier Form

```python
async def submit_form_example(form_id):
    qualifier_form_service = await get_qualifier_form_service()
    
    # Submit a qualifier form
    submission = await qualifier_form_service.submit_qualifier_form(
        form_id=form_id,
        answers={
            "question1": "answer1",
            "question2": "answer2",
            "email": "<EMAIL>"
        }
    )
    
    return submission
```

### Getting Form Statistics

```python
async def get_stats_example(form_id):
    qualifier_form_service = await get_qualifier_form_service()
    
    # Get statistics for a qualifier form
    stats = await qualifier_form_service.get_qualifier_form_stats(form_id)
    
    return stats
```

## Integration with Other Systems

The qualifier form module integrates with:

1. **Base Form System** - For form structure and submission handling
2. **Sharing Framework** - For distributing forms via links, embeds, and QR codes
3. **Trigger System** - For executing actions when forms are submitted
4. **Queue System** - For asynchronous processing of submissions and triggers

## Architecture

The qualifier form module consists of:

1. **Qualifier Form Configuration** - Extension of the base form with additional settings
2. **Sharing Configuration** - Settings for how the form can be shared
3. **Trigger Configuration** - Settings for what happens when the form is submitted
4. **Statistics** - Tracking of views, submissions, and conversion rates

The module is designed to be extensible and can be used for various qualification workflows in the application.
