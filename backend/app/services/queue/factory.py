"""
Queue service factory.

This module provides a factory for creating queue services.
"""

from app.services.queue.interfaces import QueueServiceInterface
from app.services.queue.worker_pool import WorkerPool
from app.services.queue.worker_interface import WorkerPoolInterface


class QueueFactory:
    """Factory for creating queue services and workers."""

    
    @staticmethod
    def create_worker_pool(
        queue_service: QueueServiceInterface,
        **kwargs
    ) -> WorkerPoolInterface:
        """
        Create a worker pool.
        
        Args:
            queue_service: Queue service to use
            **kwargs: Additional arguments to pass to the worker pool
            
        Returns:
            Worker pool
        """
        return WorkerPool(
            queue_service=queue_service,
            **kwargs
        )
  