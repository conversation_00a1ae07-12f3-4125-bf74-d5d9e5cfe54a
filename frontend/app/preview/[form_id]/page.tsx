"use client"

import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { ReadOnlyFormRenderer } from '@/components/core/form-preview/read-only-form-renderer';
import { useAuth } from '@/lib/auth-context';
import FormAPI from '@/lib/api/form-api';
import { Loader2, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function FormPreviewPage() {
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated, loading: authLoading } = useAuth();
  const [formData, setFormData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const formId = params?.form_id as string;

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
      return;
    }
  }, [isAuthenticated, authLoading, router]);

  useEffect(() => {
    const fetchForm = async () => {
      if (!formId || !isAuthenticated) {
        return;
      }

      try {
        console.log(`Fetching form preview for ID: ${formId}`);
        setLoading(true);

        // Use the new preview endpoint that requires authentication
        const previewData = await FormAPI.getFormPreview(formId);
        console.log('Form preview data fetched:', previewData);

        setFormData(previewData);
        setError(null);
      } catch (error: any) {
        console.error('Error fetching form preview:', error);

        if (error.response?.status === 403) {
          setError('You do not have access to preview this form.');
        } else if (error.response?.status === 404) {
          setError('This form is not available for preview.');
        } else {
          setError('Failed to load form preview. Please try again.');
        }
      } finally {
        setLoading(false);
      }
    };

    if (formId && isAuthenticated && !authLoading) {
      fetchForm();
    }
  }, [formId, isAuthenticated, authLoading]);

  // Loading state
  if (authLoading || loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="flex flex-col items-center gap-6">
          <div className="relative">
            <div className="size-20 animate-pulse rounded-2xl bg-gradient-to-r from-blue-500 to-blue-600"></div>
            <Loader2 className="absolute inset-0 m-auto size-8 animate-spin text-white" />
          </div>
          <div className="space-y-2 text-center">
            <h2 className="bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-2xl font-bold text-transparent">
              Loading Form Preview
            </h2>
            <p className="animate-pulse text-slate-600">Preparing read-only preview...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !formData) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
        <div className="w-full max-w-md">
          <Alert variant="destructive">
            <AlertCircle className="size-4" />
            <AlertDescription>
              {error || 'Form preview not available'}
            </AlertDescription>
          </Alert>
          <div className="mt-4 text-center">
            <button
              onClick={() => router.back()}
              className="font-medium text-primary hover:text-primary/80"
            >
              ← Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Render the read-only form preview
  return <ReadOnlyFormRenderer formData={formData} />;
}
