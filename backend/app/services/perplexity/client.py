"""
Perplexity API Client

Handles async chat completions with Perplexity Sonar API.
"""

from typing import Any, Dict, List, Optional

import httpx

from app.core.config import settings
from app.core.logging import get_logger
from app.models.chat import ChatSource
from app.utils.ai.prompt import (
    RESEARCH_PROMPT_TEMPLATE,
    RESEARCH_SYSTEM_LINES,
    _format_research_context,
)

logger = get_logger(__name__)


class PerplexityError(Exception):
    """Base exception for Perplexity API errors."""

    pass


class PerplexityRateLimitError(PerplexityError):
    """Raised when rate limit is exceeded."""

    pass


class PerplexityTimeoutError(PerplexityError):
    """Raised when request times out."""

    pass


class PerplexityClient:
    """
    Async client for Perplexity Sonar API.
    Handles chat completions with sources and citations.
    """

    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or getattr(settings, "PERPLEXITY_API_KEY", None)
        if not self.api_key:
            raise ValueError("Perplexity API key is required")

        self.base_url = "https://api.perplexity.ai"
        self.timeout = 30.0

        # Default headers
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

    async def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "sonar-reasoning",
        max_tokens: int = 1000,
        temperature: float = 0.2,
    ) -> Dict[str, Any]:
        """
        Create a synchronous chat completion request.

        Returns:
            completion_data: Full completion response with content and citations
        """
        # Log the message sequence for debugging
        logger.debug(
            f"Sending message sequence to Perplexity: {[msg['role'] for msg in messages]}"
        )
        for i, msg in enumerate(messages):
            logger.debug(f"Message {i}: {msg['role']} - {msg['content'][:100]}...")

        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": 0.9,
            "return_images": False,
            "return_related_questions": False,
            "stream": False,
            "presence_penalty": 0,
            "frequency_penalty": 0,
        }

        try:
            async with httpx.AsyncClient(
                timeout=60.0
            ) as client:  # Increased timeout for sync calls
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload,
                )

                if response.status_code == 429:
                    raise PerplexityRateLimitError(
                        "Rate limit exceeded. Please try again later."
                    )
                elif response.status_code == 400:
                    error_detail = response.text
                    logger.error(
                        f"Perplexity 400 error - Message sequence: {[msg['role'] for msg in messages]}"
                    )
                    logger.error(f"Perplexity 400 error - Full error: {error_detail}")
                    raise PerplexityError(f"Bad request: {error_detail}")
                elif response.status_code == 401:
                    raise PerplexityError("Invalid API key")
                elif response.status_code == 403:
                    raise PerplexityError(
                        "Access forbidden. Check your API permissions."
                    )
                elif response.status_code >= 500:
                    raise PerplexityError(
                        f"Perplexity server error ({response.status_code}). Please try again."
                    )
                elif response.status_code >= 400:
                    error_detail = response.text
                    raise PerplexityError(
                        f"API error {response.status_code}: {error_detail}"
                    )

                result = response.json()

                if not result.get("choices"):
                    raise PerplexityError(
                        "No response choices returned from Perplexity API"
                    )

                logger.info(f"Completed Perplexity chat completion with model: {model}")
                return result

        except httpx.TimeoutException:
            raise PerplexityTimeoutError("Request timed out. Please try again.")
        except httpx.RequestError as e:
            logger.error(f"Network error during completion: {e}")
            raise PerplexityError(
                "Network error. Please check your connection and try again."
            )
        except Exception as e:
            logger.error(f"Error creating chat completion: {e}")
            if isinstance(
                e, (PerplexityError, PerplexityRateLimitError, PerplexityTimeoutError)
            ):
                raise
            raise PerplexityError(f"Failed to create completion: {str(e)}")

    def extract_sources_from_completion(
        self, completion_data: Dict[str, Any]
    ) -> List[ChatSource]:
        """
        Extract sources/citations from Perplexity completion response.
        """
        sources = []

        try:
            # For synchronous completions, citations are in the response
            citations = completion_data.get("citations", [])

            # Also check in choices if citations are nested there
            if not citations and completion_data.get("choices"):
                choice = completion_data["choices"][0]
                citations = choice.get("citations", [])

            for citation in citations:
                source = ChatSource(
                    title=citation.get("title", "Unknown Source"),
                    url=citation.get("url", ""),
                    snippet=citation.get("snippet"),
                    domain=citation.get("domain"),
                )
                sources.append(source)

        except Exception as e:
            logger.error(f"Error extracting sources: {e}")

        return sources

    def extract_content_from_completion(self, completion_data: Dict[str, Any]) -> str:
        """
        Extract content from Perplexity completion response.
        """
        try:
            # For synchronous completions, content is in choices
            choices = completion_data.get("choices", [])
            if choices:
                message = choices[0].get("message", {})
                content = message.get("content", "")
                return content

            # Fallback to direct content field
            return completion_data.get("content", "No response generated")

        except Exception as e:
            logger.error(f"Error extracting content from completion: {e}")
            return "Error processing response"

    def build_investment_prompt(
        self,
        user_message: str,
        deal_context: Optional[Dict[str, Any]] = None,
        chat_history: Optional[List[Dict[str, str]]] = None,
        mode: str = "research",
    ) -> List[Dict[str, str]]:
        """
        Build a context-aware prompt for investment research analysis.
        """
        messages: List[Dict[str, str]] = []

        # Render system message only for research mode
        if mode == "research":
            # Prepare context
            ctx = _format_research_context(deal_context or {})
            system_content = RESEARCH_PROMPT_TEMPLATE.render(
                system_lines=RESEARCH_SYSTEM_LINES,
                **ctx,
                user_message="",  # placeholder; actual user message appended below
            )
            messages.append({"role": "system", "content": system_content})
        else:
            # Fallback for chat mode (unchanged)
            messages.append({"role": "system", "content": ""})

        # Add recent chat history (last 6 messages) with validation
        if chat_history:
            # Validate and clean chat history to ensure proper alternation
            cleaned_history = self._validate_and_clean_chat_history(chat_history)
            messages.extend(cleaned_history[-6:])

            # Log the cleaned history for debugging
            logger.debug(f"Cleaned chat history: {cleaned_history}")

        # Check if the last message in history is already the current user message
        # If so, don't append it again to avoid duplication
        if (
            messages
            and messages[-1].get("role") == "user"
            and messages[-1].get("content") == user_message
        ):
            logger.debug(
                "Current user message already present in history, skipping append"
            )
        else:
            # Check if the last message is from a user - if so, replace it with the current message
            # to avoid consecutive user messages
            if messages and messages[-1].get("role") == "user":
                logger.debug(
                    "Replacing last user message with current user message to avoid consecutive user messages"
                )
                messages[-1] = {"role": "user", "content": user_message}
            else:
                # Finally append user message
                messages.append({"role": "user", "content": user_message})

        # Final validation of the complete message sequence
        self._validate_message_sequence(messages)

        logger.debug(f"Final message sequence: {[msg['role'] for msg in messages]}")
        return messages

    def _validate_and_clean_chat_history(
        self, chat_history: List[Dict[str, str]]
    ) -> List[Dict[str, str]]:
        """
        Validate and clean chat history to ensure proper user/assistant alternation.
        Removes consecutive messages with the same role.
        """
        if not chat_history:
            return []

        cleaned_history = []
        last_role = None

        for msg in chat_history:
            current_role = msg.get("role")
            content = msg.get("content", "").strip()

            # Skip empty messages
            if not content:
                continue

            # Skip if same role as previous message (except for system messages)
            if current_role == last_role and current_role != "system":
                logger.warning(
                    f"Skipping consecutive {current_role} message: {content[:50]}..."
                )
                continue

            # Only allow user, assistant, and system roles
            if current_role not in ["user", "assistant", "system"]:
                logger.warning(
                    f"Skipping message with invalid role '{current_role}': {content[:50]}..."
                )
                continue

            cleaned_history.append(msg)
            last_role = current_role

        return cleaned_history

    def _validate_message_sequence(self, messages: List[Dict[str, str]]) -> None:
        """
        Validate that the message sequence follows Perplexity's requirements.
        Raises an exception if validation fails.
        """
        if not messages:
            raise PerplexityError("Message sequence cannot be empty")

        # Check for proper alternation after system messages
        system_count = 0
        last_non_system_role = None

        for msg in messages:
            role = msg.get("role")
            content = msg.get("content", "").strip()

            if not content:
                raise PerplexityError(f"Empty content for {role} message")

            if role == "system":
                system_count += 1
                # System messages should only be at the beginning
                if system_count > 1:
                    raise PerplexityError("Multiple system messages not allowed")
            else:
                # For non-system messages, check alternation
                if last_non_system_role == role:
                    raise PerplexityError(
                        f"Consecutive {role} messages not allowed. "
                        f"Messages must alternate between user and assistant."
                    )
                last_non_system_role = role

        # Ensure we end with a user message
        if messages and messages[-1].get("role") != "user":
            raise PerplexityError("Message sequence must end with a user message")
