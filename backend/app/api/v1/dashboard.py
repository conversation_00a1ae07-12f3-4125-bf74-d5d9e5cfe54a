"""
Dashboard API Endpoints

Unified dashboard summary endpoint providing real-time data for all dashboard components.
"""

from datetime import datetime, timezone
from typing import Any, Dict, Optional, Tuple

from bson import ObjectId
from fastapi import Depends, HTTPException, Query, status
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.api.base import BaseAPIRouter
from app.core.database import get_database
from app.core.logging import get_logger
from app.dependencies.auth import get_current_user
from app.dependencies.org import get_org_context
from app.models.deal import Deal, DealStatus
from app.models.form import Form
from app.models.thesis import InvestmentThesis
from app.models.user import User
from app.schemas.dashboard import DashboardSummaryResponse, DealInsightsResponse

logger = get_logger(__name__)
router = BaseAPIRouter(prefix="/dashboard", tags=["dashboard"])


@router.get("/summary", response_model=DashboardSummaryResponse)
async def get_dashboard_summary(
    current_user: User = Depends(get_current_user),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    db: AsyncIOMotorDatabase = Depends(get_database),
) -> DashboardSummaryResponse:
    """
    Get unified dashboard summary with real-time data.

    Returns:
    - Active deals count and percentage change
    - Forms and theses counts
    - AI activity status
    - Sector distribution
    - Deal stages distribution
    - Onboarding status
    """
    try:
        org_id, _ = org_context
        org_object_id = ObjectId(org_id)

        logger.info(f"Getting dashboard summary for org {org_id}")

        # Get all deals for the organization
        deals = await Deal.find_many(query={"org_id": org_object_id})
        active_deals = len(deals)

        # Calculate deals change percentage (this month vs last month)
        # For now, return 0 - can be enhanced with actual historical data
        active_deals_change_pct = 0

        # Get forms count
        forms = await Form.find_many(query={"org_id": org_object_id, "is_active": True})
        # Only include forms that are not create by the system, i.e form.name!='Demo Form - Understanding Form Builder'
        forms_count = len([
            form
            for form in forms
            if form.name != "Demo Form - Understanding Form Builder"
        ])

        # Get theses count
        theses = await InvestmentThesis.find_many(
            query={"org_id": org_object_id, "is_active": True, "is_deleted": False}
        )
        theses_count = len([
            thesis
            for thesis in theses
            if thesis.name != "Demo Investment Thesis - Understanding Form Builder"
        ])
        # theses_count = len(theses)

        # AI Activity - dummy data for now
        ai_activity = {"active": False, "last_sync": None}

        # Sector distribution
        sector_distribution = []
        sector_counts = {}
        for deal in deals:
            if deal.sector:
                # Handle both string and list sectors
                sectors = (
                    deal.sector if isinstance(deal.sector, list) else [deal.sector]
                )
                for sector in sectors:
                    if sector:
                        sector_counts[sector] = sector_counts.get(sector, 0) + 1

        for sector, count in sector_counts.items():
            sector_distribution.append({
                "sector": sector.replace("_", " ").capitalize(),
                "count": count,
            })

        # Deal stages distribution
        deal_stages = []
        stage_counts = {}
        for deal in deals:
            if deal.stage:
                stage_counts[deal.stage.replace("_", " ").capitalize()] = (
                    stage_counts.get(deal.stage, 0) + 1
                )

        # Ensure common stages are included even if count is 0
        common_stages = [
            "Pre-Seed",
            "Seed",
            "Series A",
            "Series B",
            "Series C",
            "Growth",
        ]
        for stage in common_stages:
            if stage not in stage_counts:
                stage_counts[stage] = 0

        for stage, count in stage_counts.items():
            deal_stages.append({"stage": stage, "count": count})

        # Onboarding status
        has_form = forms_count > 0
        has_thesis = theses_count > 0

        # onboarding = {"has_form": has_form, "has_thesis": has_thesis}
        onboarding = {"has_form": has_form, "has_thesis": has_thesis}

        summary = DashboardSummaryResponse(
            active_deals=active_deals,
            active_deals_change_pct=active_deals_change_pct,
            forms=forms_count,
            theses=theses_count,
            ai_activity=ai_activity,  # type: ignore
            sector_distribution=sector_distribution,
            deal_stages=deal_stages,
            onboarding=onboarding,  # type: ignore
        )

        logger.info(
            f"Dashboard summary generated: {active_deals} deals, {forms_count} forms, {theses_count} theses"
        )
        return summary

    except Exception as e:
        logger.error(f"Error getting dashboard summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/insights", response_model=DealInsightsResponse)
async def get_deal_insights(
    current_user: User = Depends(get_current_user),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    db: AsyncIOMotorDatabase = Depends(get_database),
    date_range_start: Optional[int] = Query(
        None, description="Start timestamp for date range filter"
    ),
    date_range_end: Optional[int] = Query(
        None, description="End timestamp for date range filter"
    ),
    assigned_user_ids: Optional[str] = Query(
        None, description="Comma-separated list of user IDs to filter by"
    ),
) -> DealInsightsResponse:
    """
    Get comprehensive deal insights for dashboard charts.

    Returns:
    - Deal funnel breakdown by status
    - Sector × Stage deal matrix
    - Deal activity timeline
    - Exclusion filters triggered
    - Deal velocity by user
    - Forms count
    - Deals assigned to current user
    """
    try:
        org_id, _ = org_context
        org_object_id = ObjectId(org_id)

        logger.info(f"Getting deal insights for org {org_id}")

        # Build query filters
        query_filters: Dict[str, Any] = {"org_id": org_object_id}

        # Add date range filter if provided
        if date_range_start or date_range_end:
            date_filter: Dict[str, int] = {}
            if date_range_start:
                date_filter["$gte"] = date_range_start
            if date_range_end:
                date_filter["$lte"] = date_range_end
            query_filters["created_at"] = date_filter

        # Add assigned user filter if provided
        if assigned_user_ids:
            user_ids = [ObjectId(uid.strip()) for uid in assigned_user_ids.split(",")]
            query_filters["assigned_user_ids"] = {"$in": user_ids}

        # Get all relevant deals
        deals = await Deal.find_many(query=query_filters)

        # Get forms count
        forms = await Form.find_many(
            query={"org_id": ObjectId(org_object_id), "is_active": True}
        )
        # forms_count = len([
        #     form
        #     for form in forms
        #     if form.name != "Demo Form - Understanding Form Builder"
        # ])
        forms_count = len(forms)

        # Get deals assigned to current user
        deals_assigned_to_me = await Deal.find_many(
            query={
                "org_id": ObjectId(org_object_id),
                "assigned_user_ids": {"$in": [current_user.id]},
            }
        )
        deals_assigned_to_me_count = len(deals_assigned_to_me)

        # Get all users for reference
        all_users = await User.find_many(query={"org_id": org_object_id})
        user_map = {str(user.id): user for user in all_users}

        # 1. Deal Funnel Breakdown
        deal_funnel_breakdown = []
        status_counts = {}

        # Initialize all statuses with 0 count
        for deal_status in DealStatus:
            status_counts[deal_status.value] = 0

        # Count deals by status
        for deal in deals:
            status_counts[deal.status.value] += 1

        # Convert to response format
        for deal_status, count in status_counts.items():
            deal_funnel_breakdown.append({"status": deal_status, "count": count})

        # 2. Sector × Stage Deal Matrix
        sector_stage_matrix = {}

        for deal in deals:
            if deal.sector and deal.stage:
                # Normalize sector to always be a list
                sectors = (
                    deal.sector if isinstance(deal.sector, list) else [deal.sector]
                )

                for sector in sectors:
                    if sector:
                        # Clean and format sector name
                        clean_sector = sector.replace("_", " ").capitalize()

                        if clean_sector not in sector_stage_matrix:
                            sector_stage_matrix[clean_sector] = {}

                        # Clean and format stage name
                        clean_stage = deal.stage.replace("_", " ").capitalize()
                        sector_stage_matrix[clean_sector][clean_stage] = (
                            sector_stage_matrix[clean_sector].get(clean_stage, 0) + 1
                        )

        # 3. Deal Activity Timeline
        deal_activity_timeline = []
        date_counts = {}

        for deal in deals:
            # Convert timestamp to date string
            deal_date = datetime.fromtimestamp(
                deal.created_at, tz=timezone.utc
            ).strftime("%Y-%m-%d")
            date_counts[deal_date] = date_counts.get(deal_date, 0) + 1

        # Sort by date and convert to response format
        for date in sorted(date_counts.keys()):
            deal_activity_timeline.append({"date": date, "count": date_counts[date]})

        # 4. Exclusion Filters Triggered
        exclusion_filters_triggered = {}

        for deal in deals:
            if deal.exclusion_filter_result and deal.exclusion_filter_result.get(
                "excluded", False
            ):
                reason = deal.exclusion_filter_result.get("reason")
                if reason:
                    clean_reason = reason.replace("_", " ").capitalize()
                else:
                    clean_reason = "Unspecified"

                exclusion_filters_triggered[clean_reason] = (
                    exclusion_filters_triggered.get(clean_reason, 0) + 1
                )

        # 5. Deal Velocity by User
        deal_velocity_by_user = {}

        for deal in deals:
            # Get all users involved with this deal (created_by + assigned_user_ids)
            involved_users = set()
            if deal.created_by:
                involved_users.add(str(deal.created_by))
            involved_users.update([str(uid) for uid in deal.assigned_user_ids])

            # Process timeline events for status transitions
            for event in deal.timeline:
                if "Status changed to" in event.get("event", ""):
                    # Extract the new status from the event
                    event_text = event["event"]
                    if "Status changed to " in event_text:
                        new_status = event_text.replace(
                            "Status changed to ", ""
                        ).lower()

                        # For each involved user, track this transition
                        for user_id in involved_users:
                            if user_id in user_map:
                                user_name = (
                                    user_map[user_id].name
                                    or user_map[user_id].email
                                    or "Unknown User"
                                )

                                if user_id not in deal_velocity_by_user:
                                    deal_velocity_by_user[user_id] = {
                                        "name": user_name,
                                        "status_transitions": {},
                                    }

                                # Create transition key (we'll need to track previous status)
                                # For now, we'll use a simplified approach
                                transition_key = f"→{new_status}"
                                deal_velocity_by_user[user_id]["status_transitions"][
                                    transition_key
                                ] = (
                                    deal_velocity_by_user[user_id][
                                        "status_transitions"
                                    ].get(transition_key, 0)
                                    + 1
                                )

        insights = DealInsightsResponse(
            deal_funnel_breakdown=deal_funnel_breakdown,
            sector_stage_matrix=sector_stage_matrix,
            deal_activity_timeline=deal_activity_timeline,
            exclusion_filters_triggered=exclusion_filters_triggered,
            deal_velocity_by_user=deal_velocity_by_user,
            forms_count=forms_count,
            deals_assigned_to_me=deals_assigned_to_me_count,
        )

        logger.info(
            f"Deal insights generated: {len(deals)} deals analyzed, "
            f"{len(deal_funnel_breakdown)} statuses, {len(sector_stage_matrix)} sectors, "
            f"{forms_count} forms, {deals_assigned_to_me_count} deals assigned to user"
        )
        return insights

    except Exception as e:
        logger.error(f"Error getting deal insights: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
