"""
Data models for TractionX Data Pipeline Service.
"""

from app.models.base import (
    BaseModel,
    TractionXModel,
    PipelineJobMetadata,
    PipelineStatus,
    EnrichmentSource,
    ProcessingResult
)

from app.models.company import (
    CompanyData,
    CompanyEnrichmentData,
    ApolloCompanyData,
    CompanyKeyword,
    CompanyTechnology,
    CompanyDepartmentCount
)

from app.models.founder import (
    FounderData,
    FounderEnrichmentData,
    PDLFounderData
)

from app.models.news import (
    NewsData,
    NewsArticle,
    BingNewsData
)

from app.models.embedding import (
    EmbeddingData,
    TextEmbedding,
    EmbeddingMetadata
)

from app.models.crunchbase import (
    CrunchbaseResolverInput,
    CrunchbaseResolverOutput,
    SerperSearchResult,
    SerperSearchResponse,
    BrightDataCompanyData,
    BrightDataSnapshotRecord,
    CrunchbaseEnrichmentData
)

__all__ = [
    # Base models
    "BaseModel",
    "TractionXModel",
    "PipelineJobMetadata",
    "PipelineStatus", 
    "EnrichmentSource",
    "ProcessingResult",
    
    # Company models
    "CompanyData",
    "CompanyEnrichmentData",
    "ApolloCompanyData",
    "CompanyKeyword",
    "CompanyTechnology",
    "CompanyDepartmentCount",
    
    # Founder models
    "FounderData",
    "FounderEnrichmentData", 
    "PDLFounderData",
    
    # News models
    "NewsData",
    "NewsArticle",
    "BingNewsData",
    
    # Embedding models
    "EmbeddingData",
    "TextEmbedding",
    "EmbeddingMetadata",
    
    # Crunchbase models
    "CrunchbaseResolverInput",
    "CrunchbaseResolverOutput",
    "SerperSearchResult",
    "SerperSearchResponse",
    "BrightDataCompanyData",
    "BrightDataSnapshotRecord",
    "CrunchbaseEnrichmentData"
]
