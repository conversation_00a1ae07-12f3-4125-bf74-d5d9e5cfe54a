"use client"

import * as React from "react"
import {
  Root as Accordion,
  Item as AccordionItem,
  <PERSON><PERSON> as A<PERSON>rdionHeader,
  <PERSON><PERSON> as AccordionTrigger,
  Content as AccordionContent,
} from "@radix-ui/react-accordion"
import { ChevronDown } from "lucide-react"

import { cn } from "@/lib/utils"

const AccordionItemComponent = React.forwardRef<
  React.ElementRef<typeof AccordionItem>,
  React.ComponentPropsWithoutRef<typeof AccordionItem>
>(({ className, ...props }, ref) => (
  <AccordionItem
    ref={ref}
    className={cn("border-b", className)}
    {...props}
  />
))
AccordionItemComponent.displayName = "AccordionItem"

const AccordionTriggerComponent = React.forwardRef<
  React.ElementRef<typeof AccordionTrigger>,
  React.ComponentPropsWithoutRef<typeof AccordionTrigger>
>(({ className, children, ...props }, ref) => (
  <AccordionHeader className="flex">
    <AccordionTrigger
      ref={ref}
      className={cn(
        "flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",
        className
      )}
      {...props}
    >
      {children}
      <ChevronDown className="size-4 transition-transform duration-200" />
    </AccordionTrigger>
  </AccordionHeader>
))
AccordionTriggerComponent.displayName = "AccordionTrigger"

const AccordionContentComponent = React.forwardRef<
  React.ElementRef<typeof AccordionContent>,
  React.ComponentPropsWithoutRef<typeof AccordionContent>
>(({ className, children, ...props }, ref) => (
  <AccordionContent
    ref={ref}
    className={cn(
      "overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",
      className
    )}
    {...props}
  >
    <div className="pb-4 pt-0">{children}</div>
  </AccordionContent>
))
AccordionContentComponent.displayName = "AccordionContent"

export { 
  Accordion, 
  AccordionItemComponent as AccordionItem, 
  AccordionTriggerComponent as AccordionTrigger, 
  AccordionContentComponent as AccordionContent 
}
