"use client"

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  AlertTriangle, 
  Globe, 
  CheckCircle, 
  Loader2,
  Edit3
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { DealAPI } from '@/lib/api/deal-api'
import { useToast } from '@/components/ui/use-toast'

interface CompanyWebsiteWarningProps {
  dealId: string
  companyName?: string
  companyWebsite?: string
  onUpdate?: (updatedDeal: any) => void
  className?: string
}

export function CompanyWebsiteWarning({ 
  dealId, 
  companyName, 
  companyWebsite, 
  onUpdate,
  className 
}: CompanyWebsiteWarningProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [website, setWebsite] = useState(companyWebsite || '')
  const [isUpdating, setIsUpdating] = useState(false)
  const { toast } = useToast()

  // Don't show if company website is already set
  if (companyWebsite) {
    return null
  }

  const handleUpdate = async () => {
    if (!website.trim()) {
      toast({
        title: "Website required",
        description: "Please enter a valid company website.",
        variant: "destructive"
      })
      return
    }

    // Basic URL validation
    const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/
    if (!urlPattern.test(website.trim())) {
      toast({
        title: "Invalid website",
        description: "Please enter a valid website URL (e.g., example.com or https://example.com).",
        variant: "destructive"
      })
      return
    }

    setIsUpdating(true)
    try {
      // Ensure website has protocol
      let formattedWebsite = website.trim()
      if (!formattedWebsite.startsWith('http://') && !formattedWebsite.startsWith('https://')) {
        formattedWebsite = `https://${formattedWebsite}`
      }

      const updatedDeal = await DealAPI.updateDeal(dealId, {
        company_website: formattedWebsite
      })

      toast({
        title: "Website updated successfully!",
        description: "Company website has been updated and enrichment will be triggered automatically.",
      })

      onUpdate?.(updatedDeal)
      setIsEditing(false)
      
    } catch (error: any) {
      console.error('Error updating company website:', error)
      toast({
        title: "Update failed",
        description: error?.message || "Failed to update company website. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsUpdating(false)
    }
  }

  const handleCancel = () => {
    setWebsite(companyWebsite || '')
    setIsEditing(false)
  }

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className={cn("w-full", className)}
      >
        <Card className="border-2 border-orange-200 bg-orange-50">
          <CardContent className="p-6">
            <div className="space-y-4">
              {/* Header */}
              <div className="flex items-center gap-3">
                <AlertTriangle className="size-6 text-orange-600" />
                <div>
                  <h3 className="text-sm font-semibold text-orange-800">
                    Company Website Missing
                  </h3>
                  <p className="text-xs text-orange-700">
                    Add a company website to enable automatic enrichment and better deal analysis.
                  </p>
                </div>
              </div>

              {/* Content */}
              <div className="space-y-3">
                <p className="text-sm text-orange-700">
                  <strong>{companyName || 'This company'}</strong> is missing a website URL. 
                  Adding a website will automatically trigger company enrichment, providing:
                </p>
                
                <ul className="text-sm text-orange-700 space-y-1 ml-4">
                  <li>• Company technology stack and keywords</li>
                  <li>• Department and team size information</li>
                  <li>• Market positioning and competitive analysis</li>
                  <li>• Enhanced deal scoring and insights</li>
                </ul>

                {/* Edit Form */}
                {isEditing ? (
                  <div className="space-y-3 pt-2">
                    <div className="space-y-2">
                      <Label htmlFor="company-website" className="text-sm font-medium text-orange-800">
                        Company Website
                      </Label>
                      <Input
                        id="company-website"
                        type="url"
                        placeholder="e.g., example.com or https://example.com"
                        value={website}
                        onChange={(e) => setWebsite(e.target.value)}
                        className="border-orange-300 focus:border-orange-500"
                        disabled={isUpdating}
                      />
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={handleUpdate}
                        disabled={isUpdating || !website.trim()}
                        className="bg-orange-600 hover:bg-orange-700 text-white"
                        size="sm"
                      >
                        {isUpdating ? (
                          <>
                            <Loader2 className="size-4 mr-2 animate-spin" />
                            Updating...
                          </>
                        ) : (
                          <>
                            <CheckCircle className="size-4 mr-2" />
                            Update Website
                          </>
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={handleCancel}
                        disabled={isUpdating}
                        size="sm"
                        className="border-orange-300 text-orange-700 hover:bg-orange-100"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <Button
                    onClick={() => setIsEditing(true)}
                    variant="outline"
                    size="sm"
                    className="border-orange-300 text-orange-700 hover:bg-orange-100"
                  >
                    <Edit3 className="size-4 mr-2" />
                    Add Company Website
                  </Button>
                )}
              </div>

              {/* Info Alert */}
              <Alert className="border-orange-200 bg-orange-50">
                <Globe className="size-4 text-orange-600" />
                <AlertDescription className="text-orange-700">
                  Once you add a website, our system will automatically enrich the deal with company data, 
                  technology insights, and market analysis within a few minutes.
                </AlertDescription>
              </Alert>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </AnimatePresence>
  )
} 