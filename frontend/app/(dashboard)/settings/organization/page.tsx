"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { toast } from "sonner"

import { SettingsLayout } from "@/components/core/settings/settings-layout"
import { OrganizationForm } from "@/components/core/settings/organization-form"
import { SettingsAPI, OrganizationInfo } from "@/lib/api/settings-api"
import { useAuth } from "@/lib/auth-context"

export default function OrganizationSettingsPage() {
  const router = useRouter()
  const { user } = useAuth()
  const [organization, setOrganization] = useState<OrganizationInfo | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadOrganization()
  }, [])

  const loadOrganization = async () => {
    try {
      setIsLoading(true)
      const orgData = await SettingsAPI.getOrganization()
      setOrganization(orgData)
    } catch (error: any) {
      console.error("Failed to load organization:", error)
      toast.error("Failed to load organization")
    } finally {
      setIsLoading(false)
    }
  }

  const handleOrganizationUpdate = async (data: {
    name?: string
    logoUrl?: string
    website?: string
    contactEmail?: string
    address?: string
    description?: string
  }) => {
    try {
      // Map the fields to match the API interface
      await SettingsAPI.updateOrganization({
        name: data.name,
        logo_url: data.logoUrl,
        website: data.website,
        contact_email: data.contactEmail,
        address: data.address,
        description: data.description,
      })
      toast.success("Organization updated successfully!")
      
      // Reload organization to get updated data
      await loadOrganization()
    } catch (error: any) {
      console.error("Failed to update organization:", error)
      toast.error(error.message || "Failed to update organization")
    }
  }

  if (isLoading) {
    return (
      <SettingsLayout activeTab="organization">
        <div className="flex items-center justify-center py-12">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="size-8 rounded-full border-2 border-primary border-t-transparent"
          />
        </div>
      </SettingsLayout>
    )
  }

  if (!organization) {
    return (
      <SettingsLayout activeTab="organization">
        <div className="py-12 text-center">
          <p className="text-muted-foreground">Failed to load organization</p>
        </div>
      </SettingsLayout>
    )
  }

  return (
    <SettingsLayout activeTab="organization">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <OrganizationForm
          organization={organization}
          onUpdate={handleOrganizationUpdate}
        />
      </motion.div>
    </SettingsLayout>
  )
}
