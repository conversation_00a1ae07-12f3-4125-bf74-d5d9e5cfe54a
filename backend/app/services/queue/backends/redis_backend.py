"""
Redis queue backend implementation.

This module provides a Redis-based implementation of the queue backend interface.
"""

import json
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import redis.asyncio as redis  # type: ignore
from redis.asyncio.client import Redis  # type: ignore

from app.core.config import settings
from app.core.logging import get_logger
from app.models.queue import Job, JobPriority, JobStatus, QueueStats, QueueType
from app.services.queue.interfaces import QueueBackendInterface

logger = get_logger(__name__)


class RedisQueueBackend(QueueBackendInterface):
    """Redis implementation of the queue backend interface."""

    def __init__(
        self,
        redis_url: str,
        namespace: str = settings.REDIS_KEY_PREFIX,
        job_expiration: int = 86400 * 7,  # 7 days in seconds
        processing_timeout: int = 1800,  # 30 minutes in seconds
    ):
        """
        Initialize the Redis queue backend.

        Args:
            redis_url: Redis connection URL
            namespace: Namespace for Redis keys
            job_expiration: Time in seconds before jobs expire from Redis
            processing_timeout: Time in seconds before a processing job is considered stalled
        """
        self.redis_url = redis_url
        self.namespace = namespace
        self.job_expiration = job_expiration
        self.processing_timeout = processing_timeout
        self.redis_client: Optional[Redis] = None
        self.lua_scripts: Dict[str, str] = {}
        self.lua_script_shas: Dict[str, str] = {}

    async def initialize(self) -> None:
        """Initialize the Redis client and load Lua scripts."""
        self.redis_client = await redis.from_url(self.redis_url, decode_responses=True)
        # Test connection
        try:
            if await self.redis_client.ping():
                logger.info("Connected to Redis queue service")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise

        # Load Lua scripts for atomic operations
        await self._load_lua_scripts()

    async def cleanup(self) -> None:
        """Close the Redis client."""
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None

    def _get_queue_key(self, queue_type: QueueType) -> str:
        """Get the Redis key for a queue."""
        return f"{self.namespace}:queue:{queue_type.value}"

    def _get_job_key(self, job_id: str) -> str:
        """Get the Redis key for a job."""
        return f"{self.namespace}:job:{job_id}"

    def _get_processing_key(self, queue_type: QueueType) -> str:
        """Get the Redis key for processing jobs."""
        return f"{self.namespace}:processing:{queue_type.value}"

    def _get_scheduled_key(self) -> str:
        """Get the Redis key for scheduled jobs."""
        return f"{self.namespace}:scheduled"

    def _get_stats_key(self, queue_type: QueueType) -> str:
        """Get the Redis key for queue statistics."""
        return f"{self.namespace}:stats:{queue_type.value}"

    async def _load_lua_scripts(self) -> None:
        """Load Lua scripts for atomic operations."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        # Atomic dequeue script - pops job from queue and moves to processing atomically
        self.lua_scripts["atomic_dequeue"] = """
            local queue_key = KEYS[1]
            local processing_key = KEYS[2]
            local current_time = ARGV[1]
            
            -- Get the job with the lowest score (highest priority)
            local job_data = redis.call('ZRANGE', queue_key, 0, 0, 'WITHSCORES')
            
            if #job_data == 0 then
                return nil  -- No jobs available
            end
            
            local job_id = job_data[1]
            local score = job_data[2]
            
            -- Atomically remove from queue and add to processing
            local removed = redis.call('ZREM', queue_key, job_id)
            
            if removed == 1 then
                -- Successfully removed from queue, add to processing
                redis.call('ZADD', processing_key, current_time, job_id)
                return job_id
            else
                -- Job was already taken by another worker
                return nil
            end
        """

        # Atomic dequeue with job type filter script
        self.lua_scripts["atomic_dequeue_filtered"] = """
            local queue_key = KEYS[1]
            local processing_key = KEYS[2]
            local job_key_prefix = KEYS[3]
            local current_time = ARGV[1]
            local job_types_json = ARGV[2]
            
            -- Parse job types from JSON
            local job_types = {}
            if job_types_json and job_types_json ~= '' then
                local decoded = cjson.decode(job_types_json)
                for _, job_type in ipairs(decoded) do
                    job_types[job_type] = true
                end
            end
            
            -- Get all jobs in queue (ordered by score)
            local all_jobs = redis.call('ZRANGE', queue_key, 0, -1)
            
            for _, job_id in ipairs(all_jobs) do
                -- Get job data to check type
                local job_data_key = job_key_prefix .. ':' .. job_id
                local job_data_str = redis.call('HGET', job_data_key, 'data')
                
                -- If job data doesn't exist as hash, try as string
                if not job_data_str then
                    job_data_str = redis.call('GET', job_data_key)
                end
                
                if job_data_str then
                    -- Parse job data to check type
                    local job_data = cjson.decode(job_data_str)
                    local job_type = job_data.type
                    
                    -- Check if job type matches filter
                    if not job_types_json or job_types_json == '' or job_types[job_type] then
                        -- Job matches filter, try to claim it
                        local removed = redis.call('ZREM', queue_key, job_id)
                        
                        if removed == 1 then
                            -- Successfully removed from queue, add to processing
                            redis.call('ZADD', processing_key, current_time, job_id)
                            return job_id
                        end
                    end
                else
                    -- Job data not found, remove from queue
                    redis.call('ZREM', queue_key, job_id)
                end
            end
            
            return nil  -- No matching jobs found
        """

        try:
            # Load scripts and store their SHA hashes
            for script_name, script_code in self.lua_scripts.items():
                sha = await self.redis_client.script_load(script_code)
                self.lua_script_shas[script_name] = sha
                logger.info(f"Loaded Lua script '{script_name}' with SHA: {sha[:8]}...")

        except Exception as e:
            logger.error(f"Failed to load Lua scripts: {e}")
            # Fallback: scripts will be executed directly if SHA loading fails
            logger.warning("Will fall back to direct script execution")

    async def enqueue(self, job: Job) -> Job:
        """Add a job to the queue."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        # Update job timestamps
        current_time = int(time.time() * 1000)
        job.updated_at = current_time

        # Handle delayed jobs
        if job.scheduled_for and job.scheduled_for > current_time:
            # Add to scheduled queue with score = scheduled_for timestamp
            await self.redis_client.zadd(
                self._get_scheduled_key(), {job.id: job.scheduled_for}
            )
            job.status = JobStatus.SCHEDULED
        else:
            # Add to the appropriate queue
            queue_key = self._get_queue_key(job.queue)

            # Use priority as score (lower score = higher priority)
            priority_score = {
                JobPriority.HIGH: 1,
                JobPriority.NORMAL: 100,
                JobPriority.LOW: 1000,
            }.get(job.priority, 100)

            # Add job ID to sorted set with priority as score
            await self.redis_client.zadd(queue_key, {job.id: priority_score})
            job.status = JobStatus.PENDING

        # Store the job data
        job_data = job.to_dict()
        await self.redis_client.hset(
            self._get_job_key(job.id), mapping={"data": json.dumps(job_data)}
        )

        # Set expiration
        await self.redis_client.expire(self._get_job_key(job.id), self.job_expiration)

        # Update stats
        await self._increment_stat(job.queue, "total_enqueued")
        if job.status == JobStatus.PENDING:
            await self._increment_stat(job.queue, "pending")
        elif job.status == JobStatus.SCHEDULED:
            await self._increment_stat(job.queue, "scheduled")

        return job

    async def dequeue(
        self,
        queue_type: QueueType = QueueType.DEFAULT,
        job_types: Optional[List[str]] = None,
        wait_timeout: int = 0,
    ) -> Optional[Job]:
        """Dequeue a job from the specified queue atomically.

        Args:
            queue_type: The type of queue to dequeue from
            job_types: Optional list of job types to filter by
            wait_timeout: How long to wait for a job (in seconds)

        Returns:
            Optional[Job]: The dequeued job, or None if no job is available
        """
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        queue_key = self._get_queue_key(queue_type)
        processing_key = self._get_processing_key(queue_type)

        # First process scheduled jobs
        await self.process_scheduled_jobs()

        current_time = int(time.time())

        try:
            if job_types:
                # Use atomic dequeue with job type filtering
                job_id = await self._atomic_dequeue_filtered(
                    queue_key, processing_key, job_types, current_time
                )
            else:
                # Use simple atomic dequeue
                job_id = await self._atomic_dequeue_simple(
                    queue_key, processing_key, current_time
                )

            if not job_id:
                return None

            # Get and validate job data
            job = await self._process_dequeued_job(job_id, queue_type)
            if not job:
                # Job was invalid, clean up processing queue
                await self.redis_client.zrem(processing_key, job_id)
                return None

            return job

        except Exception as e:
            logger.error(f"Error in atomic dequeue: {str(e)}")
            return None

    async def _atomic_dequeue_simple(
        self, queue_key: str, processing_key: str, current_time: int
    ) -> Optional[str]:
        """Atomically dequeue a job without filtering."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        try:
            # Try to use pre-loaded Lua script
            script_sha = self.lua_script_shas.get("atomic_dequeue")
            if script_sha:
                try:
                    result = await self.redis_client.evalsha(
                        script_sha, 2, queue_key, processing_key, str(current_time)
                    )
                    return result
                except Exception as e:
                    logger.warning(
                        f"Failed to execute Lua script SHA, falling back to direct execution: {e}"
                    )

            # Fallback: execute script directly
            script = self.lua_scripts.get("atomic_dequeue")
            if script:
                result = await self.redis_client.eval(
                    script, 2, queue_key, processing_key, str(current_time)
                )
                return result
            else:
                # Ultimate fallback: use legacy pipeline method (less atomic but functional)
                return await self._legacy_dequeue_simple(
                    queue_key, processing_key, current_time
                )

        except Exception as e:
            logger.error(f"Error in atomic dequeue simple: {e}")
            return None

    async def _atomic_dequeue_filtered(
        self,
        queue_key: str,
        processing_key: str,
        job_types: List[str],
        current_time: int,
    ) -> Optional[str]:
        """Atomically dequeue a job with job type filtering."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        try:
            # Try to use pre-loaded Lua script
            script_sha = self.lua_script_shas.get("atomic_dequeue_filtered")
            job_key_prefix = f"{self.namespace}:job"
            job_types_json = json.dumps(job_types)

            if script_sha:
                try:
                    result = await self.redis_client.evalsha(
                        script_sha,
                        3,
                        queue_key,
                        processing_key,
                        job_key_prefix,
                        str(current_time),
                        job_types_json,
                    )
                    return result
                except Exception as e:
                    logger.warning(
                        f"Failed to execute Lua script SHA, falling back to direct execution: {e}"
                    )

            # Fallback: execute script directly
            script = self.lua_scripts.get("atomic_dequeue_filtered")
            if script:
                result = await self.redis_client.eval(
                    script,
                    3,
                    queue_key,
                    processing_key,
                    job_key_prefix,
                    str(current_time),
                    job_types_json,
                )
                return result
            else:
                # Ultimate fallback: use legacy method
                return await self._legacy_dequeue_filtered(
                    queue_key, processing_key, job_types, current_time
                )

        except Exception as e:
            logger.error(f"Error in atomic dequeue filtered: {e}")
            return None

    async def _process_dequeued_job(
        self, job_id: str, queue_type: QueueType
    ) -> Optional[Job]:
        """Process a job that was atomically dequeued."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        try:
            # Get job data
            job_data_str = None
            key_type = await self.redis_client.type(self._get_job_key(job_id))
            if key_type == "hash":
                job_data_str = await self.redis_client.hget(
                    self._get_job_key(job_id), "data"
                )
            elif key_type == "string":
                job_data_str = await self.redis_client.get(self._get_job_key(job_id))

            if not job_data_str:
                logger.warning(f"Job data not found for dequeued job {job_id}")
                return None

            # Parse job data
            try:
                job_data = json.loads(job_data_str)
                job = Job.from_dict(job_data)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in job {job_id}: {e}")
                return None

            # Update job status
            job.status = JobStatus.PROCESSING
            job.started_at = int(time.time() * 1000)
            job.updated_at = job.started_at
            job.attempts += 1

            # Save updated job data
            try:
                await self.redis_client.hset(
                    self._get_job_key(job.id),
                    mapping={"data": json.dumps(job.to_dict())},
                )
            except Exception:
                # If HSET fails, try SET
                await self.redis_client.set(
                    self._get_job_key(job.id),
                    json.dumps(job.to_dict()),
                )

            # Update stats
            await self._decrement_stat(job.queue, "pending")
            await self._increment_stat(job.queue, "processing")

            return job

        except Exception as e:
            logger.error(f"Error processing dequeued job {job_id}: {e}")
            return None

    async def _legacy_dequeue_simple(
        self, queue_key: str, processing_key: str, current_time: int
    ) -> Optional[str]:
        """Legacy non-atomic dequeue implementation as fallback."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        logger.warning("Using legacy non-atomic dequeue - race conditions possible")

        try:
            # Get job with lowest score
            result = await self.redis_client.zrange(queue_key, 0, 0, withscores=True)
            if not result:
                return None

            job_id, score = result[0]

            # Try to atomically move to processing using pipeline
            async with self.redis_client.pipeline(transaction=True) as pipe:
                await pipe.zrem(queue_key, job_id)
                await pipe.zadd(processing_key, {job_id: current_time})
                results = await pipe.execute()

                # Check if we successfully removed the job
                if results[0] == 1:  # Successfully removed from queue
                    return job_id
                else:
                    # Job was already taken by another worker
                    return None

        except Exception as e:
            logger.error(f"Error in legacy dequeue: {e}")
            return None

    async def _legacy_dequeue_filtered(
        self,
        queue_key: str,
        processing_key: str,
        job_types: List[str],
        current_time: int,
    ) -> Optional[str]:
        """Legacy non-atomic filtered dequeue implementation as fallback."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        logger.warning(
            "Using legacy non-atomic filtered dequeue - race conditions possible"
        )

        try:
            # Get all jobs in queue
            job_ids = await self.redis_client.zrange(queue_key, 0, -1)

            for jid in job_ids:
                try:
                    # Get job data to check type
                    job_data_str = None
                    key_type = await self.redis_client.type(self._get_job_key(jid))
                    if key_type == "hash":
                        job_data_str = await self.redis_client.hget(
                            self._get_job_key(jid), "data"
                        )
                    elif key_type == "string":
                        job_data_str = await self.redis_client.get(
                            self._get_job_key(jid)
                        )

                    if not job_data_str:
                        # Job data not found, remove from queue
                        await self.redis_client.zrem(queue_key, jid)
                        continue

                    try:
                        job_data = json.loads(job_data_str)
                        if job_data.get("type") in job_types:
                            # Found matching job, try to move to processing
                            async with self.redis_client.pipeline(
                                transaction=True
                            ) as pipe:
                                await pipe.zrem(queue_key, jid)
                                await pipe.zadd(processing_key, {jid: current_time})
                                results = await pipe.execute()

                                # Check if we successfully removed the job
                                if results[0] == 1:  # Successfully removed from queue
                                    return jid
                                # Otherwise, continue to next job

                    except json.JSONDecodeError:
                        # Invalid JSON, remove from queue
                        await self.redis_client.zrem(queue_key, jid)
                        continue

                except Exception as e:
                    logger.error(
                        f"Error processing job {jid} in legacy filtered dequeue: {e}"
                    )
                    continue

            return None

        except Exception as e:
            logger.error(f"Error in legacy filtered dequeue: {e}")
            return None

    async def complete(
        self, job_id: str, result: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Mark a job as completed."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        # Get job data
        job_data_str = await self.redis_client.hget(self._get_job_key(job_id), "data")

        if not job_data_str:
            return False

        # Parse job data
        job_data = json.loads(job_data_str)
        job = Job.from_dict(job_data)

        # Update job status and timestamps
        job.status = JobStatus.COMPLETED
        job.completed_at = int(time.time() * 1000)
        job.updated_at = job.completed_at
        job.result = result

        # Save updated job data
        await self.redis_client.hset(
            self._get_job_key(job.id), mapping={"data": json.dumps(job.to_dict())}
        )

        # Remove from processing set
        processing_key = self._get_processing_key(job.queue)
        await self.redis_client.zrem(processing_key, job.id)

        # Update stats
        await self._decrement_stat(job.queue, "processing")
        await self._increment_stat(job.queue, "completed")
        await self._increment_stat(job.queue, "total_processed")

        return True

    async def fail(self, job_id: str, error: str, retry: bool = True) -> bool:
        """Mark a job as failed."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        # Get job data
        job_data_str = await self.redis_client.hget(self._get_job_key(job_id), "data")

        if not job_data_str:
            return False

        # Parse job data
        job_data = json.loads(job_data_str)
        job = Job.from_dict(job_data)

        # Update job status and timestamps
        current_time = int(time.time() * 1000)
        job.failed_at = current_time
        job.updated_at = current_time
        job.error = error

        # Check if we should retry
        if retry and job.attempts < job.max_attempts:
            # Calculate next retry time
            delay = job.calculate_next_retry_delay()
            job.next_retry_at = current_time + (delay * 1000)
            job.status = JobStatus.RETRYING

            logger.info(f"Job {job.id} will be retried in {delay} seconds")
            # Add to scheduled queue with score = next_retry_at
            await self.redis_client.zadd(
                self._get_scheduled_key(), {job.id: job.next_retry_at}
            )

            # Update stats
            await self._decrement_stat(job.queue, "processing")
            await self._increment_stat(job.queue, "retried")
            await self._increment_stat(job.queue, "scheduled")
        else:
            # Job is permanently failed, move to DLQ
            job.status = JobStatus.FAILED

            # Move to dead letter queue
            dlq_key = self._get_queue_key(QueueType.DEAD_LETTER)
            response = await self.redis_client.zadd(dlq_key, {job.id: current_time})
            logger.info(f"Job {job.id} moved to dead letter queue: {response}")
            # Update stats
            await self._decrement_stat(job.queue, "processing")
            await self._increment_stat(QueueType.DEAD_LETTER, "total_enqueued")
            await self._increment_stat(QueueType.DEAD_LETTER, "pending")

        # Save updated job data
        await self.redis_client.hset(
            self._get_job_key(job.id), mapping={"data": json.dumps(job.to_dict())}
        )

        # Remove from processing set
        processing_key = self._get_processing_key(job.queue)
        await self.redis_client.zrem(processing_key, job.id)

        return True

    async def get_job(self, job_id: str) -> Optional[Job]:
        """Get job details by ID."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        # Get job data
        job_data_str = await self.redis_client.hget(self._get_job_key(job_id), "data")

        if not job_data_str:
            return None

        # Parse job data
        job_data = json.loads(job_data_str)
        return Job.from_dict(job_data)

    async def cancel(self, job_id: str) -> bool:
        """Cancel a pending or scheduled job."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        # Get job data
        job_data_str = await self.redis_client.hget(self._get_job_key(job_id), "data")

        if not job_data_str:
            return False

        # Parse job data
        job_data = json.loads(job_data_str)
        job = Job.from_dict(job_data)

        # Check if job can be cancelled
        if job.status not in [
            JobStatus.PENDING,
            JobStatus.SCHEDULED,
            JobStatus.RETRYING,
        ]:
            return False

        # Update job status
        job.status = JobStatus.CANCELLED
        job.updated_at = int(time.time() * 1000)

        # Save updated job data
        await self.redis_client.hset(
            self._get_job_key(job.id), mapping={"data": json.dumps(job.to_dict())}
        )

        # Remove from queues
        queue_key = self._get_queue_key(job.queue)
        scheduled_key = self._get_scheduled_key()

        await self.redis_client.zrem(queue_key, job.id)
        await self.redis_client.zrem(scheduled_key, job.id)

        # Update stats
        if job.status == JobStatus.PENDING:
            await self._decrement_stat(job.queue, "pending")
        elif job.status in [JobStatus.SCHEDULED, JobStatus.RETRYING]:
            await self._decrement_stat(job.queue, "scheduled")

        return True

    async def retry(self, job_id: str, delay_seconds: int = 0) -> bool:
        """Retry a failed job."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        # Get job data
        job_data_str = await self.redis_client.hget(self._get_job_key(job_id), "data")

        if not job_data_str:
            return False

        # Parse job data
        job_data = json.loads(job_data_str)
        job = Job.from_dict(job_data)

        # Check if job can be retried
        # if job.status not in [JobStatus.FAILED, JobStatus.CANCELLED]:
        #     return False

        # Update job status and timestamps
        current_time = int(time.time() * 1000)
        job.updated_at = current_time

        if delay_seconds > 0:
            # Schedule for later
            job.status = JobStatus.SCHEDULED
            job.scheduled_for = current_time + (delay_seconds * 1000)

            # Add to scheduled queue
            await self.redis_client.zadd(
                self._get_scheduled_key(), {job.id: job.scheduled_for}
            )

            # Update stats
            await self._increment_stat(job.queue, "scheduled")
        else:
            # Add back to queue immediately
            job.status = JobStatus.PENDING
            queue_key = self._get_queue_key(job.queue)

            # Use priority as score
            priority_score = {
                JobPriority.HIGH: 1,
                JobPriority.NORMAL: 100,
                JobPriority.LOW: 1000,
            }.get(job.priority, 100)

            await self.redis_client.zadd(queue_key, {job.id: priority_score})

            # Update stats
            await self._increment_stat(job.queue, "pending")

        # Save updated job data
        await self.redis_client.hset(
            self._get_job_key(job.id), mapping={"data": json.dumps(job.to_dict())}
        )

        return True

    async def purge(
        self, queue_type: QueueType, older_than: Optional[Union[datetime, int]] = None
    ) -> int:
        """Remove all jobs from a queue."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        queue_key = self._get_queue_key(queue_type)
        processing_key = self._get_processing_key(queue_type)

        # Get all job IDs in the queue
        job_ids = await self.redis_client.zrange(queue_key, 0, -1)

        # Add processing jobs if needed
        processing_ids = await self.redis_client.zrange(processing_key, 0, -1)
        job_ids.extend(processing_ids)

        # If older_than is provided, filter jobs
        if older_than is not None:
            # Convert to timestamp if datetime
            if isinstance(older_than, datetime):
                older_than_ts = int(older_than.timestamp() * 1000)
            else:
                older_than_ts = older_than

            filtered_ids = []
            for job_id in job_ids:
                # Get job data
                job_data_str = await self.redis_client.hget(
                    self._get_job_key(job_id), "data"
                )

                if not job_data_str:
                    continue

                # Parse job data
                job_data = json.loads(job_data_str)

                # Check created_at timestamp
                if job_data.get("created_at", 0) < older_than_ts:
                    filtered_ids.append(job_id)

            job_ids = filtered_ids

        # Remove jobs from queues
        if job_ids:
            await self.redis_client.zrem(queue_key, *job_ids)
            await self.redis_client.zrem(processing_key, *job_ids)

            # Delete job data
            for job_id in job_ids:
                await self.redis_client.delete(self._get_job_key(job_id))

        # Reset stats
        await self.redis_client.delete(self._get_stats_key(queue_type))

        return len(job_ids)

    async def list_jobs(
        self,
        queue_type: QueueType = QueueType.DEFAULT,
        status: Optional[JobStatus] = None,
        job_type: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Job]:
        """List jobs in a queue."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        result = []

        # Get job IDs based on status
        job_ids = []
        if status == JobStatus.PENDING:
            # Get from queue
            queue_key = self._get_queue_key(queue_type)
            job_ids = await self.redis_client.zrange(queue_key, 0, -1)
        elif status == JobStatus.PROCESSING:
            # Get from processing set
            processing_key = self._get_processing_key(queue_type)
            job_ids = await self.redis_client.zrange(processing_key, 0, -1)
        elif status == JobStatus.SCHEDULED:
            # Get from scheduled set
            scheduled_key = self._get_scheduled_key()
            job_ids = await self.redis_client.zrange(scheduled_key, 0, -1)
        else:
            # Get all jobs
            queue_key = self._get_queue_key(queue_type)
            processing_key = self._get_processing_key(queue_type)
            scheduled_key = self._get_scheduled_key()

            pending_ids = await self.redis_client.zrange(queue_key, 0, -1)
            processing_ids = await self.redis_client.zrange(processing_key, 0, -1)
            scheduled_ids = await self.redis_client.zrange(scheduled_key, 0, -1)

            job_ids = list(set(pending_ids + processing_ids + scheduled_ids))

        # Apply pagination
        paginated_ids = job_ids[offset : offset + limit]

        # Get job data for each ID
        for job_id in paginated_ids:
            job = await self.get_job(job_id)

            if not job:
                continue

            # Filter by job type if needed
            if job_type and job.type != job_type:
                continue

            # Filter by status if needed
            if status and job.status != status:
                continue

            # Filter by queue type
            if job.queue != queue_type:
                continue

            result.append(job)

        return result

    async def get_stats(
        self, queue_type: Optional[QueueType] = None
    ) -> Union[QueueStats, Dict[str, QueueStats]]:
        """Get statistics about the queue(s)."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        if queue_type:
            # Get stats for a specific queue
            stats_key = self._get_stats_key(queue_type)
            stats_data = await self.redis_client.hgetall(stats_key)

            # Convert to integers
            for key, value in stats_data.items():
                stats_data[key] = int(value) if value.isdigit() else 0

            return QueueStats(queue=queue_type, **stats_data)
        else:
            # Get stats for all queues
            result = {}

            for qt in QueueType:
                stats_key = self._get_stats_key(qt)
                stats_data = await self.redis_client.hgetall(stats_key)

                # Convert to integers
                for key, value in stats_data.items():
                    stats_data[key] = int(value) if value.isdigit() else 0

                result[qt.value] = QueueStats(queue=qt, **stats_data)

            return result

    async def process_scheduled_jobs(self) -> int:
        """Process scheduled jobs that are due."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        scheduled_key = self._get_scheduled_key()
        current_time = int(time.time() * 1000)

        # Get all scheduled jobs that are due
        due_jobs = await self.redis_client.zrangebyscore(scheduled_key, 0, current_time)

        processed_count = 0

        for job_id in due_jobs:
            # Get job data
            job_data_str = await self.redis_client.hget(
                self._get_job_key(job_id), "data"
            )

            if not job_data_str:
                # Job data not found, remove from scheduled set
                await self.redis_client.zrem(scheduled_key, job_id)
                continue

            # Parse job data
            job_data = json.loads(job_data_str)
            job = Job.from_dict(job_data)

            # Handle retrying jobs differently
            if job.status == JobStatus.RETRYING:
                # Reset retry-specific fields
                job.next_retry_at = None
                job.attempts = (job.attempts or 0) + 1

                # If we've exceeded max attempts, move to failed
                if job.attempts >= job.max_attempts:
                    job.status = JobStatus.FAILED
                    job.error = f"Max retry attempts ({job.max_attempts}) exceeded"

                    # Move to dead letter queue
                    dlq_key = self._get_queue_key(QueueType.DEAD_LETTER)
                    await self.redis_client.zadd(dlq_key, {job.id: current_time})

                    # Update stats
                    await self._increment_stat(QueueType.DEAD_LETTER, "total_enqueued")
                    await self._increment_stat(QueueType.DEAD_LETTER, "pending")
                    await self._increment_stat(job.queue, "failed")
                else:
                    # Move back to pending for retry
                    job.status = JobStatus.PENDING

                    # Add to queue with priority
                    queue_key = self._get_queue_key(job.queue)
                    priority_score = {
                        JobPriority.HIGH: 1,
                        JobPriority.NORMAL: 100,
                        JobPriority.LOW: 1000,
                    }.get(job.priority, 100)

                    await self.redis_client.zadd(queue_key, {job.id: priority_score})

                    # Update stats
                    await self._increment_stat(job.queue, "pending")
                    await self._increment_stat(job.queue, "retried")
            else:
                # Handle regular scheduled jobs
                job.status = JobStatus.PENDING
                job.updated_at = current_time

                # Add to queue
                queue_key = self._get_queue_key(job.queue)

                # Use priority as score
                priority_score = {
                    JobPriority.HIGH: 1,
                    JobPriority.NORMAL: 100,
                    JobPriority.LOW: 1000,
                }.get(job.priority, 100)

                await self.redis_client.zadd(queue_key, {job.id: priority_score})

                # Update stats
                await self._increment_stat(job.queue, "pending")

            # Remove from scheduled set
            await self.redis_client.zrem(scheduled_key, job.id)

            # Save updated job data
            await self.redis_client.hset(
                self._get_job_key(job.id), mapping={"data": json.dumps(job.to_dict())}
            )

            # Update stats for scheduled jobs
            if job.status != JobStatus.RETRYING:
                await self._decrement_stat(job.queue, "scheduled")

            processed_count += 1

        return processed_count

    async def process_stalled_jobs(self) -> int:
        """Process stalled jobs (jobs that have been processing for too long)."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")

        processed_count = 0
        current_time = int(time.time())
        stall_threshold = current_time - self.processing_timeout

        # Check each queue type
        for queue_type in QueueType:
            processing_key = self._get_processing_key(queue_type)

            # Get all processing jobs that started before the threshold
            stalled_jobs = await self.redis_client.zrangebyscore(
                processing_key, 0, stall_threshold
            )

            for job_id in stalled_jobs:
                # # Get job data
                # job_data_str = await self.redis_client.hget(
                #     self._get_job_key(job_id),
                #     "data"
                # )

                # if not job_data_str:
                #     # Job data not found, remove from processing set
                #     await self.redis_client.zrem(processing_key, job_id)
                #     continue

                # Parse job data
                # job_data = json.loads(job_data_str)
                # job = Job.from_dict(job_data)

                # Mark as failed with retry
                await self.fail(job_id, "Job processing timed out", retry=True)

                processed_count += 1

        return processed_count

    async def _increment_stat(
        self, queue_type: QueueType, stat_name: str, amount: int = 1
    ) -> None:
        """Increment a queue statistic."""
        if not self.redis_client:
            return

        stats_key = self._get_stats_key(queue_type)
        await self.redis_client.hincrby(stats_key, stat_name, amount)

    async def _decrement_stat(
        self, queue_type: QueueType, stat_name: str, amount: int = 1
    ) -> None:
        """Decrement a queue statistic."""
        if not self.redis_client:
            return

        stats_key = self._get_stats_key(queue_type)
        await self.redis_client.hincrby(stats_key, stat_name, -amount)
