"""
Migration 003: Create deal_notes collection

This migration creates the deal_notes collection with proper indexes
for efficient querying of notes by deal, organization, and user mentions.
"""

import asyncio

from app.core.database import get_database
from motor.motor_asyncio import AsyncIOMotorDatabase


async def create_deal_notes_collection(db: AsyncIOMotorDatabase) -> None:
    """Create the deal_notes collection with proper indexes."""

    # Create the collection
    await db.create_collection("deal_notes")

    # Create indexes for efficient querying

    # Index for querying notes by deal_id and org_id (most common query)
    await db.deal_notes.create_index([
        ("deal_id", 1),
        ("org_id", 1),
        ("deleted_at", 1),  # Include deleted_at for filtering
    ])

    # Index for querying notes by deal_id, org_id, and pinned status
    await db.deal_notes.create_index([
        ("deal_id", 1),
        ("org_id", 1),
        ("pinned", -1),  # Pinned notes first
        ("created_at", -1),  # Then by creation date descending
        ("deleted_at", 1),
    ])

    # Index for querying notes where a user is tagged
    await db.deal_notes.create_index([
        ("tagged_user_ids", 1),
        ("org_id", 1),
        ("deleted_at", 1),
    ])

    # Index for querying notes by creator
    await db.deal_notes.create_index([
        ("created_by", 1),
        ("org_id", 1),
        ("deleted_at", 1),
    ])

    # Index for querying notes by organization
    await db.deal_notes.create_index([
        ("org_id", 1),
        ("deleted_at", 1),
    ])

    # Index for querying notes by creation date
    await db.deal_notes.create_index([
        ("created_at", -1),
    ])

    # Index for querying notes by update date
    await db.deal_notes.create_index([
        ("updated_at", -1),
    ])

    # Text index for searching note content
    await db.deal_notes.create_index([
        ("content", "text"),
    ])

    print("✅ Created deal_notes collection with indexes")


async def migrate() -> None:
    """Run the migration."""
    print("🔄 Starting migration: Create deal_notes collection")

    db = await get_database()

    try:
        await create_deal_notes_collection(db)
        print("✅ Migration completed successfully")
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(migrate())
