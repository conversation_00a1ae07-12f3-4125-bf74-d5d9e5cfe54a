import * as React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

export function BillingForm() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Subscription Plan</CardTitle>
        <CardDescription>
          You are currently on the <strong>Free</strong> plan.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">
          This is a placeholder for the billing form. In a real application, this would contain
          subscription management options.
        </p>
      </CardContent>
      <CardFooter>
        <Button className="w-full">
          Upgrade to Pro
        </Button>
      </CardFooter>
    </Card>
  )
}
