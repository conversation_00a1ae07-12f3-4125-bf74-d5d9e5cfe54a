"""
News Intelligence API endpoints for TractionX AI Agent system.
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field
import logging

from ai.api.auth import get_auth_context, AuthContext
from ai.tools.news_intelligence import NewsIntelligenceTool, NewsIntelligenceInput
from ai.registry import agent_registry
from ai.runner import agent_runner

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/news", tags=["news"])


# Request/Response models
class NewsSearchRequest(BaseModel):
    """Request model for news search."""
    query: str = Field(..., min_length=1, max_length=500, description="Search query for news intelligence")
    max_sources: int = Field(default=3, ge=1, le=5, description="Maximum number of sources to query")
    time_range: str = Field(default="7d", description="Time range for news search")
    include_analysis: bool = Field(default=True, description="Include AI analysis and summarization")
    priority_sources: Optional[List[str]] = Field(None, description="Priority sources to use first")


class NewsSearchResponse(BaseModel):
    """Response model for news search."""
    success: bool
    query: str
    total_articles: int
    sources_used: List[str]
    sections: List[Dict[str, Any]]
    executive_summary: Optional[str]
    search_time: float
    error: Optional[str] = None


class NewsAgentRequest(BaseModel):
    """Request model for news intelligence agent."""
    message: str = Field(..., min_length=1, description="Message for the news intelligence agent")
    session_id: Optional[str] = None
    context_data: Optional[Dict[str, Any]] = Field(default_factory=dict)


class NewsAgentResponse(BaseModel):
    """Response model for news intelligence agent."""
    success: bool
    response: str
    session_id: str
    execution_time: float
    tools_used: List[str]
    error: Optional[str] = None


@router.post("/search", response_model=NewsSearchResponse)
async def search_news(
    request: NewsSearchRequest,
    auth_context: AuthContext = Depends(get_auth_context)
) -> NewsSearchResponse:
    """
    Search for news using the News Intelligence Tool directly.
    
    This endpoint provides direct access to the news intelligence functionality
    without going through the agent system.
    """
    
    logger.info(f"News search request from user {auth_context.user_id}: {request.query}")
    
    try:
        # Create news intelligence tool
        news_tool = NewsIntelligenceTool()
        
        # Create tool input
        tool_input = NewsIntelligenceInput(
            query=request.query,
            max_sources=request.max_sources,
            time_range=request.time_range,
            include_analysis=request.include_analysis,
            priority_sources=request.priority_sources
        )
        
        # Create context
        from ai.models import AgentContext
        context = AgentContext(
            org_id=auth_context.org_id,
            user_id=auth_context.user_id,
            session_id=f"news_search_{auth_context.user_id}"
        )
        
        # Execute search
        result = await news_tool.execute(tool_input, context)
        
        # Convert sections to dict format for JSON response
        sections_dict = []
        for section in result.sections:
            section_dict = {
                "title": section.title,
                "summary": section.summary,
                "articles": [
                    {
                        "title": article.title,
                        "url": article.url,
                        "description": article.description,
                        "source": article.source,
                        "published_at": article.published_at.isoformat() if article.published_at else None,
                        "image_url": article.image_url,
                        "author": article.author,
                        "score": article.metadata.get('score') if hasattr(article, 'metadata') else None
                    }
                    for article in section.articles
                ]
            }
            sections_dict.append(section_dict)
        
        return NewsSearchResponse(
            success=result.success,
            query=result.query,
            total_articles=result.total_articles,
            sources_used=result.sources_used,
            sections=sections_dict,
            executive_summary=result.executive_summary,
            search_time=result.search_time,
            error=result.error
        )
        
    except Exception as e:
        logger.error(f"News search failed for user {auth_context.user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"News search failed: {str(e)}"
        )


@router.post("/agent", response_model=NewsAgentResponse)
async def run_news_agent(
    request: NewsAgentRequest,
    auth_context: AuthContext = Depends(get_auth_context)
) -> NewsAgentResponse:
    """
    Run the News Intelligence Agent for comprehensive news analysis.
    
    This endpoint uses the full agent system with conversation memory,
    advanced prompting, and tool orchestration.
    """
    
    logger.info(f"News agent request from user {auth_context.user_id}: {request.message[:100]}...")
    
    try:
        # Get the News Intelligence Agent
        agent_data = await agent_registry.get_agent_by_name(
            "News Intelligence Agent", 
            agent_type="proprietary"
        )
        
        if not agent_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="News Intelligence Agent not found"
            )
        
        # Convert to Agent model
        from ai.models import Agent, AgentType, AgentVisibility, AgentStatus
        from ai.db import PyObjectId
        
        agent = Agent(
            id=PyObjectId(agent_data["_id"]),
            org_id=PyObjectId(agent_data["org_id"]),
            user_id=PyObjectId(agent_data["user_id"]) if agent_data.get("user_id") else None,
            name=agent_data["name"],
            description=agent_data["description"],
            type=AgentType(agent_data["type"]),
            visibility=AgentVisibility(agent_data["visibility"]),
            status=AgentStatus(agent_data["status"]),
            config=agent_data["config"],
            tools=agent_data["tools"],
            model=agent_data["model"],
            temperature=agent_data["temperature"],
            max_tokens=agent_data["max_tokens"],
            max_iterations=agent_data.get("max_iterations", 10),
            system_prompt=agent_data["system_prompt"],
            instructions=agent_data["instructions"],
            tags=agent_data.get("tags", []),
            category=agent_data.get("category")
        )
        
        # Create context
        from ai.models import AgentContext
        context = AgentContext(
            org_id=auth_context.org_id,
            user_id=auth_context.user_id,
            session_id=request.session_id or f"news_agent_{auth_context.user_id}",
            metadata=request.context_data
        )
        
        # Run agent
        result = await agent_runner.run_agent(
            agent=agent,
            context=context,
            input_message=request.message
        )
        
        return NewsAgentResponse(
            success=result["success"],
            response=result.get("response", ""),
            session_id=result["session_id"],
            execution_time=result["execution_time"],
            tools_used=result.get("tools_used", []),
            error=result.get("error")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"News agent execution failed for user {auth_context.user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"News agent execution failed: {str(e)}"
        )


@router.get("/sources")
async def get_available_sources(
    auth_context: AuthContext = Depends(get_auth_context)
) -> Dict[str, Any]:
    """
    Get available news sources and their status.
    """
    
    logger.info(f"Getting available news sources for user {auth_context.user_id}")
    
    try:
        # Create news intelligence tool to check available sources
        news_tool = NewsIntelligenceTool()
        
        # Check which sources are available
        available_sources = news_tool._get_prioritized_sources()
        
        # Get source information
        source_info = {
            "newsapi": {
                "name": "NewsAPI",
                "description": "Breaking news headlines and historical articles",
                "requires_key": True,
                "available": news_tool.web_search_tool._has_api_key("newsapi")
            },
            "bing_news": {
                "name": "Bing News",
                "description": "Real-time news articles with global coverage",
                "requires_key": True,
                "available": news_tool.web_search_tool._has_api_key("bing_news")
            },
            "google_news_rss": {
                "name": "Google News RSS",
                "description": "Free access to Google News via RSS feeds",
                "requires_key": False,
                "available": news_tool.web_search_tool._has_api_key("google_news_rss")
            },
            "serpapi": {
                "name": "SerpAPI",
                "description": "Google search results (coming soon)",
                "requires_key": True,
                "available": news_tool.web_search_tool._has_api_key("serpapi")
            },
            "gnews": {
                "name": "GNews",
                "description": "Google News API (coming soon)",
                "requires_key": True,
                "available": news_tool.web_search_tool._has_api_key("gnews")
            }
        }
        
        return {
            "available_sources": available_sources,
            "source_details": source_info,
            "total_available": len(available_sources)
        }
        
    except Exception as e:
        logger.error(f"Failed to get available sources for user {auth_context.user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get available sources: {str(e)}"
        )
