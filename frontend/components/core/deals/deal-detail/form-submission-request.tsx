"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Loader2, Mail, CheckCircle, Send, FileText } from "lucide-react"
import { cn } from "@/lib/utils"
import { FormAPI } from "@/lib/api/form-api"
import { Deal } from "@/lib/types/deal"
import { Form } from "@/lib/types/form"
import apiClient from "@/lib/api-client"

interface FormSubmissionRequestProps {
  deal: Deal
  onSuccess?: () => void
}

interface ShareDealFormRequest {
  email: string
}

export function FormSubmissionRequest({ deal, onSuccess }: FormSubmissionRequestProps) {
  const [invitedEmail, setInvitedEmail] = useState(deal.invited_email || "")
  const [selectedFormId, setSelectedFormId] = useState("")
  const [availableForms, setAvailableForms] = useState<Form[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingForms, setIsLoadingForms] = useState(true)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)

  // Load available forms on mount
  useEffect(() => {
    loadAvailableForms()
  }, [])

  const loadAvailableForms = async () => {
    try {
      setIsLoadingForms(true)
      const forms = await FormAPI.listForms()
      setAvailableForms(forms)
    } catch (err) {
      console.error("Error loading forms:", err)
      setError("Failed to load available forms")
    } finally {
      setIsLoadingForms(false)
    }
  }

  const handleSendFormRequest = async () => {
    if (!invitedEmail || !selectedFormId) {
      setError("Please provide both email and select a form")
      return
    }

    if (!invitedEmail.includes("@")) {
      setError("Please enter a valid email address")
      return
    }

    try {
      setIsLoading(true)
      setError("")

      // Call the sharing endpoint
      const response = await apiClient.post(
        `/sharing/deals/${deal.id}/forms/${selectedFormId}/share`,
        {
          email: invitedEmail
        } as ShareDealFormRequest
      )

      console.log("Form request sent successfully:", response.data)
      setSuccess(true)
      
      // Call success callback if provided
      if (onSuccess) {
        onSuccess()
      }
    } catch (err: any) {
      console.error("Error sending form request:", err)
      setError(err.response?.data?.detail || "Failed to send form request")
    } finally {
      setIsLoading(false)
    }
  }

  const handleEmailChange = (email: string) => {
    setInvitedEmail(email)
    setError("") // Clear error when user types
  }

  // If invite was already sent, show success state
  if (deal.invite_status === "sent") {
    return (
      <Card className="border-0 bg-green-50/80 shadow-sm backdrop-blur-sm">
        <CardContent className="p-6">
          <div className="flex items-center gap-3">
            <div className="rounded-lg bg-green-100 p-2 text-green-600">
              <CheckCircle className="size-5" />
            </div>
            <div>
              <p className="text-sm font-medium text-green-600">Form Request Sent</p>
              <p className="text-sm text-green-700">
                ✅ Form request sent to {deal.invited_email} — waiting on startup submission.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // If form request was just sent successfully
  if (success) {
    return (
      <Card className="border-0 bg-green-50/80 shadow-sm backdrop-blur-sm">
        <CardContent className="p-6">
          <div className="flex items-center gap-3">
            <div className="rounded-lg bg-green-100 p-2 text-green-600">
              <CheckCircle className="size-5" />
            </div>
            <div>
              <p className="text-sm font-medium text-green-600">Form Request Sent</p>
              <p className="text-sm text-green-700">
                ✅ Form request sent to {invitedEmail}. Scoring will begin once the submission is received.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="border-0 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg text-gray-800">
          <FileText className="size-5 text-blue-600" />
          Request Founder Submission
        </CardTitle>
        <p className="text-sm text-gray-600">
          Send a form request to the founder to begin scoring this deal.
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Email Input */}
        <div className="space-y-2">
          <Label htmlFor="invited-email" className="text-sm font-medium text-gray-700">
            Founder Email
          </Label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              id="invited-email"
              type="email"
              placeholder="<EMAIL>"
              value={invitedEmail}
              onChange={(e) => handleEmailChange(e.target.value)}
              className="pl-10"
              disabled={isLoading}
            />
          </div>
          {deal.invited_email && (
            <p className="text-xs text-gray-500">
              Previously invited: {deal.invited_email}
            </p>
          )}
        </div>

        {/* Form Selection */}
        <div className="space-y-2">
          <Label htmlFor="form-select" className="text-sm font-medium text-gray-700">
            Select Form
          </Label>
          <Select
            value={selectedFormId}
            onValueChange={setSelectedFormId}
            disabled={isLoadingForms || isLoading}
          >
            <SelectTrigger id="form-select" className="w-full">
              <SelectValue placeholder={isLoadingForms ? "Loading forms..." : "Choose a form to send"} />
            </SelectTrigger>
            <SelectContent>
              {availableForms.map((form) => (
                <SelectItem key={form.id || form._id} value={form.id || form._id || ""}>
                  <div className="flex items-center gap-2">
                    <span>{form.name}</span>
                    {form.status === "active" && (
                      <Badge variant="secondary" className="text-xs">
                        Active
                      </Badge>
                    )}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {availableForms.length === 0 && !isLoadingForms && (
            <p className="text-xs text-gray-500">
              No forms available. Please create a form first.
            </p>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="rounded-lg border border-red-200 bg-red-50 p-3"
          >
            <p className="text-sm text-red-600">{error}</p>
          </motion.div>
        )}

        {/* Send Button */}
        <Button
          onClick={handleSendFormRequest}
          disabled={!invitedEmail || !selectedFormId || isLoading || isLoadingForms}
          className="w-full gap-2"
        >
          {isLoading ? (
            <>
              <Loader2 className="size-4 animate-spin" />
              Sending...
            </>
          ) : (
            <>
              <Send className="size-4" />
              Send Form Request
            </>
          )}
        </Button>

        {/* Help Text */}
        <div className="text-center text-xs text-gray-500">
          <p>
            The founder will receive a magic link email to access and complete the form.
          </p>
          {deal.company_name && (
            <p className="mt-1">
              You'll receive a structured form to share more about {deal.company_name} with investors reviewing TractionX.
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  )
} 