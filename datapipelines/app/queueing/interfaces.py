"""
Queue Service Interfaces for TractionX Data Pipeline Service

This module defines the abstract interfaces for the queue system, providing
a pluggable architecture that supports multiple backends (RQ, Celery, Kafka, etc.)
with priority-based job processing and comprehensive monitoring.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Protocol, Union


class JobPriority(str, Enum):
    """Job priority levels for queue processing."""

    HIGH = "high"
    NORMAL = "normal"
    LOW = "low"


class JobStatus(str, Enum):
    """Job status enumeration."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


@dataclass
class JobProgress:
    """Progress information for long-running jobs."""

    current_step: str
    total_steps: int
    current_step_number: int
    step_progress: float  # 0.0 to 1.0
    overall_progress: float  # 0.0 to 1.0
    estimated_time_remaining: Optional[float] = None  # seconds
    step_details: Optional[Dict[str, Any]] = None


@dataclass
class QueueStats:
    """Statistics for a queue."""

    queue_name: str
    pending_count: int
    running_count: int
    completed_count: int
    failed_count: int
    total_processed: int
    avg_processing_time: Optional[float] = None
    last_job_processed: Optional[datetime] = None


@dataclass
class JobResult:
    """Result of a job execution."""

    job_id: str
    status: JobStatus
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    processing_time: Optional[float] = None
    retry_count: int = 0
    progress: Optional[JobProgress] = None


class QueueBackend(Protocol):
    """Protocol for queue backend implementations."""

    async def initialize(self) -> None:
        """Initialize the queue backend."""
        ...

    async def cleanup(self) -> None:
        """Clean up backend resources."""
        ...

    async def enqueue(
        self,
        queue_name: str,
        job_func: str,
        job_args: Dict[str, Any],
        job_id: Optional[str] = None,
        delay_seconds: int = 0,
        retry_config: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Enqueue a job to the specified queue."""
        ...

    async def dequeue(
        self,
        queue_names: List[str],
        timeout: int = 0,
    ) -> Optional[Dict[str, Any]]:
        """Dequeue a job from the specified queues (priority order)."""
        ...

    async def get_job_status(self, job_id: str) -> Optional[JobResult]:
        """Get the status of a job by ID."""
        ...

    async def update_job_progress(self, job_id: str, progress: JobProgress) -> bool:
        """Update progress for a running job."""
        ...

    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a pending or running job."""
        ...

    async def retry_job(self, job_id: str, delay_seconds: int = 0) -> bool:
        """Retry a failed job."""
        ...

    async def complete_job(
        self, job_id: str, result: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Mark a job as completed."""
        ...

    async def fail_job(
        self, job_id: str, error_message: str, retry: bool = True
    ) -> bool:
        """Mark a job as failed."""
        ...

    async def get_queue_stats(self, queue_name: str) -> QueueStats:
        """Get statistics for a specific queue."""
        ...

    async def list_queues(self) -> List[str]:
        """List all available queues."""
        ...

    async def purge_queue(self, queue_name: str) -> int:
        """Remove all jobs from a queue."""
        ...


class QueueService(ABC):
    """Abstract base class for queue service implementations."""

    def __init__(self, backend: QueueBackend):
        """Initialize the queue service with a backend."""
        self.backend = backend
        self.queues = [
            JobPriority.HIGH.value,
            JobPriority.NORMAL.value,
            JobPriority.LOW.value,
        ]

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the queue service."""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up queue service resources."""
        pass

    @abstractmethod
    async def enqueue_job(
        self,
        job_func: str,
        job_args: Dict[str, Any],
        priority: JobPriority = JobPriority.NORMAL,
        job_id: Optional[str] = None,
        delay_seconds: int = 0,
        retry_config: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Enqueue a job with the specified priority.

        Args:
            job_func: Function name to execute
            job_args: Arguments to pass to the function
            priority: Job priority level
            job_id: Optional custom job ID
            delay_seconds: Delay before job becomes available
            retry_config: Retry configuration

        Returns:
            Job ID
        """
        pass

    @abstractmethod
    async def get_job_status(self, job_id: str) -> Optional[JobResult]:
        """Get the status of a job by ID."""
        pass

    @abstractmethod
    async def update_job_progress(self, job_id: str, progress: JobProgress) -> bool:
        """Update progress for a running job."""
        pass

    @abstractmethod
    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a pending or running job."""
        pass

    @abstractmethod
    async def retry_job(self, job_id: str, delay_seconds: int = 0) -> bool:
        """Retry a failed job."""
        pass

    @abstractmethod
    async def get_queue_stats(
        self, queue_name: Optional[str] = None
    ) -> Union[QueueStats, Dict[str, QueueStats]]:
        """Get statistics for queues."""
        pass

    @abstractmethod
    async def list_queues(self) -> List[str]:
        """List all available queues."""
        pass

    @abstractmethod
    async def purge_queue(self, queue_name: str) -> int:
        """Remove all jobs from a queue."""
        pass

    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check on the queue system."""
        pass
