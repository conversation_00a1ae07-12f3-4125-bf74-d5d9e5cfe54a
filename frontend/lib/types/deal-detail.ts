// Extended types for deal detail page
import { <PERSON>, <PERSON> } from './deal';

export interface TimelineEvent {
  id: string;
  date: string;
  event: string;
  description?: string;
  user_id?: string;
  user_name?: string;
  type: 'system' | 'user' | 'score' | 'document' | 'status';
  metadata?: Record<string, any>;
}

export interface SignalScore {
  score: number;
  explanation: string;
  details?: Record<string, any>;
  criteria?: string[];
  supporting_data?: Record<string, any>;
  ai_insights?: string;
  last_updated?: string;
  sources?: Array<{
    title: string;
    url: string;
    type: 'linkedin' | 'crunchbase' | 'news' | 'report' | 'other';
  }>;
  sub_scores?: Record<string, number>;
  is_overridden?: boolean;
  override_reason?: string;
  override_by?: string;
  override_date?: string;
}

export interface ThesisMatchBreakdown {
  thesis_id: string;
  thesis_name: string;
  rules: Array<{
    question_id: string;
    question_label: string;
    answer: any;
    expected: any;
    match: boolean;
    weight: number;
    points: number;
  }>;
  bonus_scores?: Array<{
    rule_id: string;
    description: string;
    points: number;
  }>;
  total_score: number;
  max_possible_score: number;
}

export interface ScoreBreakdown {
  overall_score: number;
  signal_breakdown: {
    team_strength: SignalScore;
    market_signals: SignalScore;
    thesis_match: SignalScore;
    [key: string]: SignalScore; // Allow for extensible signals
  };
  thesis_breakdown?: ThesisMatchBreakdown;
  scoring_version?: string;
  last_updated: string;
  ai_summary?: string;
  key_insights?: Array<{
    type: 'positive' | 'negative' | 'neutral';
    message: string;
    confidence: number;
  }>;
  is_overridden?: boolean;
  override_history?: Array<{
    date: string;
    user: string;
    old_score: number;
    new_score: number;
    reason: string;
  }>;
}

// New comprehensive scoring interfaces
export interface ThesisScoring {
  thesis_id: string;
  thesis_name: string;
  total_score: number;
  normalized_score: number;
  max_possible_score: number;
  question_scores: Record<string, QuestionScore>;
  bonus_scores: Record<string, BonusScore>;
  scoring_details: ScoringDetail[];
}

export interface QuestionScore {
  rule_id: string;
  question_id: string;
  question_type: string;
  question_label?: string;
  user_answer?: any;
  expected_answer?: any;
  raw_score: number;
  weight: number;
  weighted_score: number;
  explanation: string;
  sources?: Array<{
    title: string;
    url: string;
    type: string;
  }>;
  ai_generated: boolean;
  aggregation_used: boolean;
  aggregation_type?: string;
}

export interface BonusScore {
  rule_id: string;
  bonus_points: number;
  explanation: string;
}

export interface ScoringDetail {
  rule_id: string;
  question_id: string;
  score: number;
  weight: number;
  weighted_score: number;
  explanation: string;
}

export interface FounderAnalysis {
  total_score: number;
  normalized_score: number;
  ai_analysis: string;
  key_insights: string[];
}

export interface MarketAnalysis {
  total_score: number;
  normalized_score: number;
  ai_analysis: string;
  key_insights: string[];
}

export interface ComprehensiveScoring {
  thesis: ThesisScoring;
  founders: FounderAnalysis;
  market: MarketAnalysis;
  metadata: {
    scoring_version: string;
    scored_at: number;
    total_rules_processed: number;
    ai_scoring_used: boolean;
  };
}

export interface ExternalSignal {
  id: string;
  headline: string;
  summary: string;
  source: string;
  source_logo?: string;
  source_url: string;
  date: string;
  type: 'news' | 'funding' | 'product' | 'hiring' | 'partnership' | 'market' | 'expansion';
  sentiment?: 'positive' | 'neutral' | 'negative';
  tags?: string[];
  confidence_score?: number;
  ai_analysis?: string;
  impact_score?: number;
}

// New Analyst Research Types
export interface AnalystCompetitor {
  name: string;
  website?: string;
  description: string;
  comparison: string;
  sources: string[];
}

export interface AnalystMarketTrend {
  summary: string;
  sources: string[];
}

export interface AnalystNewsSignal {
  headline: string;
  summary: string;
  url: string;
  date?: string;
}

export interface AnalystResearchData {
  deal_id: string;
  competitors?: {
    competitors: AnalystCompetitor[];
  };
  market?: {
    market_trends: AnalystMarketTrend[];
  };
  news?: {
    news_signals: AnalystNewsSignal[];
  };
  summary?: {
    executive_summary: string;
    sources: string[];
  };
  generated_at?: number;
  version: string;
}

export interface DealDocument {
  id: string;
  deal_id: string;
  org_id: string;
  source: 'investor_upload' | 'startup_submission' | 'pitch_deck';
  filename: string;
  document_type: 'pdf' | 'doc' | 'docx' | 'xls' | 'xlsx' | 'ppt' | 'pptx' | 'csv' | 'image' | 'other';
  file_size: number;
  status: 'uploading' | 'uploaded' | 'ready' | 'error' | 'deleted';
  uploaded_by_email: string;
  uploaded_by_name?: string;
  uploaded_by_role?: string;
  download_url?: string;
  preview_url?: string;
  tags: string[];
  download_count: number;
  last_accessed_at?: string;
  last_accessed_by?: string;
  created_at: string;
  updated_at: string;
  can_delete: boolean;
}

export interface ChatMessage {
  id: string;
  message: string;
  response: string;
  timestamp: string;
  user_id: string;
}

export interface DealDetailData extends Deal {
  timeline: TimelineEvent[];
  score_breakdown?: ScoreBreakdown;
  founders: Founder[];
  external_signals: ExternalSignal[];
  documents: DealDocument[];
  chat_history: ChatMessage[];
  comprehensive_scoring?: ComprehensiveScoring; // New comprehensive scoring data
  analyst_research?: AnalystResearchData; // New analyst research data
  short_description?: string;
}

// Mock data for development

