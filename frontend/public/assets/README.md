# Assets Directory

This directory contains static assets for the frontend application.

## Sample Excel Template

A sample Excel template file `sample-deal-upload.xlsx` should be created here with the following structure:

### Required Columns:
- `company_name` (Required) - The name of the company
- `stage` (Optional) - Company stage (e.g., Seed, Series A, etc.)
- `sector` (Optional) - Industry sector
- `status` (Optional) - Deal status (new, triage, reviewed, approved, excluded, closed)
- `company_website` (Optional) - Company website URL
- `short_description` (Optional) - Brief description of the company
- `invited_email` (Optional) - Email of invited person
- `notes` (Optional) - Additional notes
- `tags` (Optional) - Comma-separated tags

### Sample Data:
The template should include 2-3 example rows showing the correct format.

### File Format:
- Excel (.xlsx) format
- Maximum file size: 2MB
- Headers in the first row
- Data starting from the second row

### Instructions for Users:
1. Download the template
2. Fill in your deal data
3. Save as .xlsx or .csv
4. Upload through the Bulk Import feature

## Implementation Note:
The actual Excel file needs to be created manually and placed in this directory as `sample-deal-upload.xlsx`. 