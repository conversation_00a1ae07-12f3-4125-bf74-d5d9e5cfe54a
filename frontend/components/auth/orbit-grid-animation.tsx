"use client"

import { motion } from "framer-motion"
import { cn } from "@/lib/utils"
import { useEffect, useState } from "react"

interface OrbitGridAnimationProps {
  className?: string
  size?: number
  intensity?: "subtle" | "medium" | "high"
}

const DOTS = 15

export function OrbitAgentPulse({
  className = "",
  size = 160,
  intensity = "medium"
}: OrbitGridAnimationProps) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  const dots: JSX.Element[] = []
  const R = (DOTS - 1) / 2

  // Enhanced intensity settings
  const intensityConfig = {
    subtle: { scale: [1, 1.2, 0.9, 1], opacity: [0.3, 0.6, 0.2, 0.3], duration: 3 },
    medium: { scale: [1, 1.6, 0.7, 1], opacity: [0.4, 1, 0.3, 0.4], duration: 2.5 },
    high: { scale: [1, 2, 0.5, 1], opacity: [0.5, 1, 0.2, 0.5], duration: 2 }
  }

  const config = intensityConfig[intensity]

  if (isClient) {
    for (let y = 0; y < DOTS; y++) {
      for (let x = 0; x < DOTS; x++) {
        const dx = x - R
        const dy = y - R
        const distance = Math.sqrt(dx * dx + dy * dy)
        const inCircle = distance <= R + 0.5
        if (!inCircle) continue

        const delay = distance * 0.08
        const dotSize = Math.max(3, 7 - distance * 0.3)

        dots.push(
          <motion.div
            key={`${x}-${y}`}
            className="rounded-full bg-gray-900"
            style={{
              gridColumn: x + 1,
              gridRow: y + 1,
              zIndex: 1,
              width: `${dotSize}px`,
              height: `${dotSize}px`,
            }}
            animate={{
              scale: config.scale,
              opacity: config.opacity,
            }}
            transition={{
              duration: config.duration,
              repeat: Infinity,
              delay: delay,
              ease: "easeInOut"
            }}
          />
        )
      }
    }
  }

  return (
    <motion.div
      className={cn("relative flex items-center justify-center", className)}
      animate={{ scale: [1, 1.08, 1] }}
      transition={{ duration: 3.2, repeat: Infinity, ease: "easeInOut" }}
    >
      {/* Orbit rings */}
      <motion.svg
        className="pointer-events-none absolute size-[200px]"
        viewBox="0 0 200 200"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        style={{ zIndex: 0 }}
        animate={{
          rotate: [0, 360]
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "linear"
        }}
      >
        <circle 
          cx="100" 
          cy="100" 
          r="95" 
          stroke="currentColor" 
          strokeWidth="1.5" 
          opacity="0.2"
          className="text-foreground"
        />
        <circle 
          cx="100" 
          cy="100" 
          r="75" 
          stroke="currentColor" 
          strokeWidth="1.2" 
          opacity="0.15"
          className="text-foreground"
        />
        <circle 
          cx="100" 
          cy="100" 
          r="55" 
          stroke="currentColor" 
          strokeWidth="1" 
          opacity="0.1"
          className="text-foreground"
        />
      </motion.svg>

      {/* Dots grid */}
      <div
        className="relative grid"
        style={{
          gridTemplateColumns: `repeat(${DOTS}, 1fr)`,
          gridTemplateRows: `repeat(${DOTS}, 1fr)`,
          width: 160,
          height: 160,
          zIndex: 1,
        }}
      >
        {dots}
      </div>

      {/* Central glow */}
      <motion.div
        className="pointer-events-none absolute left-1/2 top-1/2 size-16 -translate-x-1/2 -translate-y-1/2 rounded-full bg-foreground/10 blur-2xl"
        style={{ zIndex: 2 }}
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.1, 0.3, 0.1]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </motion.div>
  )
}
  
