"""
Queue service package.

This package provides a flexible queue system for asynchronous job processing.
It supports multiple backends (currently Redis, with plans for SQS and Celery)
and provides a clean abstraction for job processing.
"""
from app.models.queue import (
    Job, JobStatus, JobPriority, QueueType, RetryStrategy, RetryConfig, JobMetadata, QueueStats
)
from app.services.queue.interfaces import QueueServiceInterface, QueueBackendInterface
from app.services.queue.worker_interface import WorkerInterface, WorkerPoolInterface, JobHandlerInterface
from app.services.queue.queue_service import QueueService
from app.services.queue.worker import QueueWorker
from app.services.queue.worker_pool import WorkerPool
from app.services.queue.factory import QueueFactory
from app.services.queue.handlers import HANDLERS

# For backward compatibility
from app.services.queue.redis_queue import RedisQueueService

__all__ = [
    # Models
    "Job", "JobStatus", "JobPriority", "QueueType", "RetryStrategy", "RetryConfig", "JobMetadata", "QueueStats",

    # Interfaces
    "QueueServiceInterface", "QueueBackendInterface", "WorkerInterface", "WorkerPoolInterface", "JobHandlerInterface",

    # Implementations
    "QueueService", "QueueWorker", "WorkerPool",

    # Factory
    "QueueFactory",

    # Handlers
    "HANDLERS",

    # Legacy
    "RedisQueueService",
]
