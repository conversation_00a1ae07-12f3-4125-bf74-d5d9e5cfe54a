"""
Comprehensive Company Data Enrichment Task for TractionX Data Pipeline Service.

This task orchestrates a complete company enrichment pipeline using barrier synchronization:
1. Initial barrier tasks: resolve_crunchbase_url_task, resolve_linkedin_url_task, generate_sitemap_task
2. Dependent tasks triggered by initial tasks:
   - resolve_crunchbase_url_task → process_brightdata_company_data
   - resolve_linkedin_url_task → process_brightdata_linkedin_data
   - generate_sitemap_task → generate_website_insights (for each URL)
3. Final merge when all tasks complete

The task uses cloud URLs for MongoDB and Redis connections.
"""

import asyncio
import time
from datetime import datetime, timezone
from typing import Any, Dict

from app.configs import get_logger
from app.configs.settings import settings
from app.queueing.barrier_sync import BarrierSync
from app.queueing.dispatcher import get_queue_service
from app.queueing.interfaces import JobPriority
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


async def enrich_company_data_comprehensive(payload: Dict[str, Any]) -> Dict[str, Any]:
    """Comprehensive company data enrichment using barrier orchestration."""
    start_time = time.time()

    try:
        # Validate required input - handle both direct and nested payload structures
        job_args = payload.get(
            "job_args", payload
        )  # Try nested first, fallback to direct

        required_fields = ["company_domain", "org_id"]
        for field in required_fields:
            if field not in job_args:
                return {
                    "success": False,
                    "error": f"Missing required field: {field}",
                    "processing_time": time.time() - start_time,
                }

        company_domain = job_args["company_domain"]
        org_id = job_args["org_id"]
        company_description = job_args.get("company_description", "")
        job_id = job_args.get("job_id", f"comprehensive_{int(time.time())}")

        # Create barrier group ID
        barrier_group_id = f"company:{org_id}:{company_domain}:{int(time.time())}"

        logger.info(f"Starting comprehensive enrichment for {company_domain}")

        # Initialize barrier sync with cloud URLs
        barrier_sync = BarrierSync(
            redis_url=settings.redis_connection_string,
            mongo_url=settings.mongo_connection_string,
        )
        await barrier_sync.initialize()

        # Initialize queue service
        queue_service = await get_queue_service()

        try:
            # Step 1: Register task completion handlers with barrier sync
            from app.queueing.task_completion_handlers import (
                register_all_completion_handlers,
            )

            register_all_completion_handlers(barrier_sync, queue_service)

            # Step 2: Register initial barrier with the four main tasks
            initial_tasks = [
                "resolve_crunchbase_url_task",
                "resolve_linkedin_url_task",
                "generate_sitemap_task",
                "process_apollo_company_data_task",
                "resolve_pitchbook_url_task",
            ]

            success = await barrier_sync.register_barrier(
                group_id=barrier_group_id,
                entity_type="company",
                entity_id=company_domain,
                initial_tasks=initial_tasks,
            )

            if not success:
                raise Exception(f"Failed to register barrier {barrier_group_id}")

            # Step 3: Enqueue initial tasks
            initial_task_payloads = {
                "resolve_crunchbase_url_task": {
                    "company_domain": company_domain,
                    "company_description": company_description,
                    "org_id": org_id,
                    "job_id": job_id,
                    "barrier_group_id": barrier_group_id,
                },
                "resolve_linkedin_url_task": {
                    "company_domain": company_domain,
                    "company_description": company_description,
                    "org_id": org_id,
                    "job_id": job_id,
                    "barrier_group_id": barrier_group_id,
                },
                "generate_sitemap_task": {
                    "domain": company_domain,
                    "page_title": company_description,
                    "org_id": org_id,
                    "barrier_group_id": barrier_group_id,
                    "company_domain": company_domain,
                },
                "process_apollo_company_data_task": {
                    "company_domain": company_domain,
                    "org_id": org_id,
                    "job_id": job_id,
                    "barrier_group_id": barrier_group_id,
                    "company_description": company_description,
                },
                "resolve_pitchbook_url_task": {
                    "company_domain": company_domain,
                    "company_description": company_description,
                    "org_id": org_id,
                    "job_id": job_id,
                    "barrier_group_id": barrier_group_id,
                },
            }

            # Enqueue initial tasks
            for task_name, task_payload in initial_task_payloads.items():
                await queue_service.enqueue_job(
                    job_func=task_name,
                    job_args=task_payload,
                    meta={
                        "barrier_group_id": barrier_group_id,
                        "task_name": task_name,
                        "entity_type": "company",
                        "is_initial_task": True,
                    },
                    priority=JobPriority.HIGH,
                )

            # Step 4: Store initial state to S3 for audit
            s3_storage = S3Storage()
            await s3_storage.initialize()

            s3_key = f"comprehensive_enrichment/{company_domain}/init.json"

            s3_data = {
                "barrier_group_id": barrier_group_id,
                "company_domain": company_domain,
                "org_id": org_id,
                "job_id": job_id,
                "initial_tasks": initial_tasks,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "status": "initialized",
            }

            success = await s3_storage.put_object(s3_key, s3_data)
            if not success:
                logger.error(f"Failed to store initial state to S3: {s3_key}")

            await s3_storage.cleanup()

            # Return success with barrier group ID for tracking
            return {
                "success": True,
                "barrier_group_id": barrier_group_id,
                "company_domain": company_domain,
                "org_id": org_id,
                "initial_tasks": initial_tasks,
                "status": "initialized",
                "processing_time": time.time() - start_time,
                "s3_key": s3_key,
            }

        finally:
            await barrier_sync.cleanup()

    except Exception as e:
        error_msg = f"Comprehensive company enrichment failed: {str(e)}"
        logger.error(error_msg, exc_info=True)

        return {
            "success": False,
            "error": error_msg,
            "processing_time": time.time() - start_time,
        }


async def merge_comprehensive_enrichment_results(
    job_args: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Merge function that runs when all comprehensive enrichment tasks are complete.

    This function extracts and stores specific data from:
    - LinkedIn scraping (process_brightdata_linkedin_data_task)
    - Crunchbase scraping (process_brightdata_company_data_task)
    - Website insights (generate_website_insights_task)

    Data is stored in S3 organized by company domain and URL for easy retrieval.
    """
    barrier_group_id = job_args.get("barrier_group_id")
    company_domain = job_args.get("company_domain")
    org_id = job_args.get("org_id")

    if not barrier_group_id:
        raise ValueError("barrier_group_id is required for merge function")

    # Extract company_domain from barrier_group_id if not provided
    if not company_domain and barrier_group_id:
        barrier_parts = barrier_group_id.split(":")
        if len(barrier_parts) >= 3:
            company_domain = barrier_parts[2]
            org_id = org_id or barrier_parts[1]

    if not company_domain:
        raise ValueError("company_domain is required for merge function")

    logger.info(f"Starting comprehensive merge for {company_domain}")
    start_time = time.time()

    try:
        # Initialize barrier sync
        barrier_sync = BarrierSync(
            redis_url=settings.redis_connection_string,
            mongo_url=settings.mongo_connection_string,
        )
        await barrier_sync.initialize()

        # Get barrier status
        barrier_status = await barrier_sync.get_barrier_status(barrier_group_id)
        if not barrier_status:
            raise ValueError(f"Barrier {barrier_group_id} not found")

        # Collect all task results
        task_results = {}
        for task_log in barrier_status.task_logs:
            if task_log.status == "success":
                task_results[task_log.task] = task_log.metadata or {}

        # Initialize S3 storage
        s3_storage = S3Storage()
        await s3_storage.initialize()

        # Extract all data into a single comprehensive structure
        comprehensive_data = {
            "company_domain": company_domain,
            "org_id": org_id,
            "barrier_group_id": barrier_group_id,
            "enrichment_timestamp": datetime.now(timezone.utc).isoformat(),
            "completed_tasks": barrier_status.completed_tasks,
            "total_tasks": len(barrier_status.expected_tasks),
            "status": "completed",
            "data": {},
        }

        # 1. Extract LinkedIn scraping data
        linkedin_task_name = "process_brightdata_linkedin_data_task"
        if linkedin_task_name in task_results:
            linkedin_result = task_results[linkedin_task_name].get("result", {})
            if linkedin_result.get("success"):
                # Get the S3 key for LinkedIn data
                linkedin_s3_key = linkedin_result.get("s3_key")
                if linkedin_s3_key:
                    try:
                        # Fetch data from S3
                        raw_data = await s3_storage.get_object(linkedin_s3_key)
                        # Convert bytes to string if needed, then parse as JSON
                        if isinstance(raw_data, bytes):
                            raw_data = raw_data.decode("utf-8")
                        if isinstance(raw_data, str):
                            import json

                            linkedin_raw_data = json.loads(raw_data)
                        else:
                            linkedin_raw_data = raw_data

                        # Extract processed data from the S3 data
                        linkedin_processed_data = {}
                        if isinstance(linkedin_raw_data, dict):
                            linkedin_processed_data = linkedin_raw_data.get(
                                "processed_data", {}
                            )

                        comprehensive_data["data"]["linkedin_scraping"] = {
                            "processed_data": linkedin_processed_data,
                            "raw_s3_data": linkedin_raw_data,
                            "source_s3_key": linkedin_s3_key,
                            "processing_time": linkedin_result.get("processing_time"),
                            "status": "success",
                        }
                        logger.info(
                            f"Retrieved LinkedIn data from S3: {linkedin_s3_key}"
                        )
                    except Exception as e:
                        logger.warning(f"Failed to retrieve LinkedIn data from S3: {e}")
                        comprehensive_data["data"]["linkedin_scraping"] = {
                            "status": "failed",
                            "error": f"Failed to retrieve from S3: {e}",
                            "source_s3_key": linkedin_s3_key,
                        }
                else:
                    comprehensive_data["data"]["linkedin_scraping"] = {
                        "status": "failed",
                        "error": "No S3 key provided",
                    }
                    logger.warning("LinkedIn scraping failed - no S3 key")
            else:
                comprehensive_data["data"]["linkedin_scraping"] = {
                    "status": "failed",
                    "error": linkedin_result.get("error", "Unknown error"),
                }
                logger.warning("LinkedIn scraping data extraction failed")

        # 2. Extract Crunchbase scraping data
        crunchbase_task_name = "process_brightdata_company_data_task"
        if crunchbase_task_name in task_results:
            crunchbase_result = task_results[crunchbase_task_name].get("result", {})
            if crunchbase_result.get("success"):
                # Get the S3 key for Crunchbase data
                crunchbase_s3_key = crunchbase_result.get("s3_key")
                if crunchbase_s3_key:
                    try:
                        # Fetch data from S3
                        raw_data = await s3_storage.get_object(crunchbase_s3_key)
                        # Convert bytes to string if needed, then parse as JSON
                        if isinstance(raw_data, bytes):
                            raw_data = raw_data.decode("utf-8")
                        if isinstance(raw_data, str):
                            import json

                            crunchbase_raw_data = json.loads(raw_data)
                        else:
                            crunchbase_raw_data = raw_data

                        # Extract processed data from the S3 data
                        crunchbase_processed_data = {}
                        if isinstance(crunchbase_raw_data, dict):
                            crunchbase_processed_data = crunchbase_raw_data.get(
                                "processed_data", {}
                            )

                        comprehensive_data["data"]["crunchbase_scraping"] = {
                            "processed_data": crunchbase_processed_data,
                            "raw_s3_data": crunchbase_raw_data,
                            "source_s3_key": crunchbase_s3_key,
                            "processing_time": crunchbase_result.get("processing_time"),
                            "status": "success",
                        }
                        logger.info(
                            f"Retrieved Crunchbase data from S3: {crunchbase_s3_key}"
                        )
                    except Exception as e:
                        logger.warning(
                            f"Failed to retrieve Crunchbase data from S3: {e}"
                        )
                        comprehensive_data["data"]["crunchbase_scraping"] = {
                            "status": "failed",
                            "error": f"Failed to retrieve from S3: {e}",
                            "source_s3_key": crunchbase_s3_key,
                        }
                else:
                    comprehensive_data["data"]["crunchbase_scraping"] = {
                        "status": "failed",
                        "error": "No S3 key provided",
                    }
                    logger.warning("Crunchbase scraping failed - no S3 key")
            else:
                comprehensive_data["data"]["crunchbase_scraping"] = {
                    "status": "failed",
                    "error": crunchbase_result.get("error", "Unknown error"),
                }
                logger.warning("Crunchbase scraping data extraction failed")

        # 3. Extract Website insights data
        website_insights_data = {}
        for task_name, task_metadata in task_results.items():
            if task_name.startswith("generate_website_insights_task_"):
                result = task_metadata.get("result", {})
                if result.get("success"):
                    # Get the S3 key for website insights data
                    s3_key = result.get("s3_key")
                    if s3_key:
                        try:
                            # Fetch data from S3
                            raw_data = await s3_storage.get_object(s3_key)
                            # Convert bytes to string if needed, then parse as JSON
                            if isinstance(raw_data, bytes):
                                raw_data = raw_data.decode("utf-8")
                            if isinstance(raw_data, str):
                                import json

                                insights_raw_data = json.loads(raw_data)
                            else:
                                insights_raw_data = raw_data

                            # Extract insights data from the S3 data
                            insights_data = {}
                            if isinstance(insights_raw_data, dict):
                                insights_data = insights_raw_data.get("output", {})

                            url = ""
                            if isinstance(insights_data, dict):
                                url = insights_data.get("source_url", "")
                            if not url:
                                # Try to get URL from input data
                                if isinstance(insights_raw_data, dict):
                                    input_data = insights_raw_data.get("input", {})
                                    if isinstance(input_data, dict):
                                        url = input_data.get(
                                            "url", f"unknown_url_{task_name}"
                                        )
                                    else:
                                        url = f"unknown_url_{task_name}"
                                else:
                                    url = f"unknown_url_{task_name}"

                            if url:
                                website_insights_data[url] = {
                                    "data": insights_data,
                                    "source_s3_key": s3_key,
                                    "processing_time": result.get("processing_time"),
                                    "status": "success",
                                }
                                logger.info(
                                    f"Retrieved website insights from S3: {s3_key}"
                                )
                        except Exception as e:
                            logger.warning(
                                f"Failed to retrieve website insights from S3: {e}"
                            )
                            url = f"unknown_url_{task_name}"
                            website_insights_data[url] = {
                                "status": "failed",
                                "error": f"Failed to retrieve from S3: {e}",
                                "source_s3_key": s3_key,
                            }
                    else:
                        # If we have a failed task, try to get the URL from task metadata
                        url = task_metadata.get("meta", {}).get(
                            "url", f"unknown_url_{task_name}"
                        )
                        website_insights_data[url] = {
                            "status": "failed",
                            "error": "No S3 key provided",
                        }
                        logger.warning(
                            f"Website insights failed - no S3 key for task: {task_name}"
                        )
                else:
                    # If we have a failed task, try to get the URL from task metadata
                    url = task_metadata.get("meta", {}).get(
                        "url", f"unknown_url_{task_name}"
                    )
                    website_insights_data[url] = {
                        "status": "failed",
                        "error": result.get("error", "Unknown error"),
                    }
                    logger.warning(
                        f"Website insights extraction failed for task: {task_name}"
                    )

        comprehensive_data["data"]["website_insights"] = website_insights_data

        # 4. Extract Apollo company data
        apollo_task_name = "process_apollo_company_data_task"
        if apollo_task_name in task_results:
            apollo_result = task_results[apollo_task_name].get("result", {})
            if apollo_result.get("success"):
                # Get the S3 key for Apollo data
                apollo_s3_key = apollo_result.get("s3_key")
                if apollo_s3_key:
                    try:
                        # Fetch data from S3
                        raw_data = await s3_storage.get_object(apollo_s3_key)
                        # Convert bytes to string if needed, then parse as JSON
                        if isinstance(raw_data, bytes):
                            raw_data = raw_data.decode("utf-8")
                        if isinstance(raw_data, str):
                            import json

                            apollo_raw_data = json.loads(raw_data)
                        else:
                            apollo_raw_data = raw_data

                        comprehensive_data["data"]["apollo_data"] = {
                            "company_data": apollo_result.get("company_data", {}),
                            "departments_count": apollo_result.get(
                                "departments_count", 0
                            ),
                            "raw_s3_data": apollo_raw_data,
                            "source_s3_key": apollo_s3_key,
                            "processing_time": apollo_result.get("processing_time"),
                            "status": "success",
                        }
                        logger.info(
                            f"Retrieved Apollo company data from S3: {apollo_s3_key}"
                        )
                    except Exception as e:
                        logger.warning(f"Failed to retrieve Apollo data from S3: {e}")
                        comprehensive_data["data"]["apollo_data"] = {
                            "status": "failed",
                            "error": f"Failed to retrieve from S3: {e}",
                            "source_s3_key": apollo_s3_key,
                        }
                else:
                    comprehensive_data["data"]["apollo_data"] = {
                        "status": "failed",
                        "error": "No S3 key provided",
                    }
                    logger.warning("Apollo company data failed - no S3 key")
            else:
                comprehensive_data["data"]["apollo_data"] = {
                    "status": "failed",
                    "error": apollo_result.get("error", "Unknown error"),
                }
                logger.warning("Apollo company data extraction failed")

        # Store everything in a single comprehensive file
        comprehensive_s3_key = (
            f"companies/{company_domain}/comprehensive_enrichment_data.json"
        )

        success = await s3_storage.put_object(comprehensive_s3_key, comprehensive_data)
        if success:
            logger.info(
                f"Stored comprehensive enrichment data to S3: {comprehensive_s3_key}"
            )
            logger.info(
                f"Data summary: LinkedIn={bool(comprehensive_data['data'].get('linkedin_scraping'))}, "
                f"Crunchbase={bool(comprehensive_data['data'].get('crunchbase_scraping'))}, "
                f"Apollo={bool(comprehensive_data['data'].get('apollo_data'))}, "
                f"Website Insights={len(comprehensive_data['data'].get('website_insights', {}))}"
            )
        else:
            logger.error(
                f"Failed to store comprehensive enrichment data to S3: {comprehensive_s3_key}"
            )

        await s3_storage.cleanup()

        # Update barrier status to done
        if barrier_sync.mongo_db_instance is not None:
            collection = barrier_sync.mongo_db_instance.barriers
            await asyncio.get_event_loop().run_in_executor(
                None,
                collection.update_one,
                {"_id": barrier_group_id},
                {
                    "$set": {
                        "status": "done",
                        "updated_at": time.time(),
                        "merge_triggered": True,
                    }
                },
            )

        merge_result = {
            "barrier_group_id": barrier_group_id,
            "company_domain": company_domain,
            "org_id": org_id,
            "completed_tasks": barrier_status.completed_tasks,
            "total_tasks": len(barrier_status.expected_tasks),
            "merge_timestamp": time.time(),
            "status": "completed",
            "comprehensive_data": comprehensive_data,
            "comprehensive_s3_key": comprehensive_s3_key,
            "processing_time": time.time() - start_time,
        }

        logger.info(f"Comprehensive merge completed for barrier {barrier_group_id}")
        logger.info(
            f"Data summary: LinkedIn={bool(comprehensive_data['data'].get('linkedin_scraping'))}, "
            f"Crunchbase={bool(comprehensive_data['data'].get('crunchbase_scraping'))}, "
            f"Apollo={bool(comprehensive_data['data'].get('apollo_data'))}, "
            f"Website Insights={len(comprehensive_data['data'].get('website_insights', {}))}"
        )

        # Trigger data processing task
        try:
            queue_service = await get_queue_service()

            processing_payload = {
                "enrichment_data": comprehensive_data,
                "company_domain": company_domain,
                "org_id": org_id,
                "barrier_group_id": barrier_group_id,
            }

            await queue_service.enqueue_job(
                job_func="process_comprehensive_enrichment_data_task",
                job_args=processing_payload,
                meta={
                    "task_name": "process_comprehensive_enrichment_data",
                    "entity_type": "company",
                    "dependent_on": "merge_comprehensive_enrichment_results",
                    "company_domain": company_domain,
                },
                priority=JobPriority.NORMAL,
            )

            logger.info(
                f"Triggered comprehensive data processing task for {company_domain}"
            )

        except Exception as e:
            logger.error(f"Failed to trigger data processing task: {e}")

        await barrier_sync.cleanup()
        return merge_result

    except Exception as e:
        logger.error(f"Comprehensive merge failed for barrier {barrier_group_id}: {e}")

        # Update barrier status to error
        try:
            if barrier_sync.mongo_db_instance is not None:
                collection = barrier_sync.mongo_db_instance.barriers
                await asyncio.get_event_loop().run_in_executor(
                    None,
                    collection.update_one,
                    {"_id": barrier_group_id},
                    {
                        "$set": {
                            "status": "error",
                            "updated_at": time.time(),
                        }
                    },
                )
        except Exception as update_error:
            logger.error(f"Failed to update barrier status to error: {update_error}")

        raise
    finally:
        # Ensure barrier sync is cleaned up even on error
        try:
            await barrier_sync.cleanup()
        except Exception as cleanup_error:
            logger.warning(f"Error cleaning up barrier sync in merge: {cleanup_error}")
