"""
Deal API Schemas

Pydantic models for Deal API requests and responses.
"""

from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, ConfigDict, Field

from app.models.base import TractionXModel
from app.models.deal import DealStatus
from app.utils.common import ObjectIdField, PyObjectId


class DealCreate(BaseModel):
    """Schema for creating a new deal."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "form_id": "507f1f77bcf86cd799439012",
                "submission_id": "507f1f77bcf86cd799439013",
                "company_name": "Acme Corp",
                "stage": "Series A",
                "sector": ["Technology", "SaaS"],
                "status": "new",
                "notes": "Promising early-stage SaaS company",
                "tags": ["AI", "B2B"],
            }
        }
    )

    form_id: Optional[str] = Field(None, description="Form ID")
    submission_id: Optional[str] = Field(None, description="Primary submission ID")

    # Core fields (can be extracted from submission or provided directly)
    company_name: Optional[str] = Field(None, description="Company name")
    stage: Optional[str] = Field(None, description="Company stage")
    sector: Optional[Union[str, List[str]]] = Field(
        None, description="Company sector(s)"
    )

    # Optional fields
    status: Optional[DealStatus] = Field(
        DealStatus.NEW, description="Initial deal status"
    )
    notes: Optional[str] = Field(None, description="Initial notes about the deal")
    tags: Optional[List[str]] = Field(
        default_factory=list, description="Tags for categorizing the deal"
    )

    # Additional fields
    company_website: Optional[str] = Field(None, description="Company website")
    pitch_deck_file: Optional[str] = Field(
        None, description="S3 key for pitch deck file"
    )

    # Processing results (set by backend)
    exclusion_filter_result: Optional[Dict[str, Any]] = Field(
        None, description="Exclusion filter processing result"
    )
    scoring: Optional[Dict[str, Any]] = Field(
        None, description="Thesis scoring results"
    )


class DealUpdate(BaseModel):
    """Schema for updating an existing deal."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "status": "reviewed",
                "notes": "Updated after initial review",
                "tags": ["AI", "B2B", "Enterprise"],
                "scoring": {
                    "total_score": 92,
                    "normalized_score": 92.0,
                    "thesis_matches": ["thesis1", "thesis2"],
                },
            }
        }
    )

    # Core fields
    company_name: Optional[str] = Field(None, description="Company name")
    stage: Optional[str] = Field(None, description="Company stage")
    sector: Optional[Union[str, List[str]]] = Field(
        None, description="Company sector(s)"
    )

    # Deal tracking fields
    status: Optional[DealStatus] = Field(None, description="Deal status")
    status_notes: Optional[str] = Field(None, description="Notes for status change")
    notes: Optional[str] = Field(None, description="General notes about the deal")

    # Tag management
    tags: Optional[List[str]] = Field(
        None, description="Complete list of tags (replaces existing)"
    )
    add_tags: Optional[List[str]] = Field(None, description="Tags to add")
    remove_tags: Optional[List[str]] = Field(None, description="Tags to remove")

    # System fields
    exclusion_filter_result: Optional[Dict[str, Any]] = Field(
        None, description="Exclusion filter results"
    )
    scoring: Optional[Dict[str, Any]] = Field(None, description="Scoring results")

    # Founder fields
    founders: Optional[List[Dict[str, Any]]] = Field(
        None, description="List of founders with their information"
    )

    # Add company_website for update support
    company_website: Optional[str] = Field(None, description="Company website")


class DealResponse(TractionXModel):
    """Schema for deal responses."""

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": "507f1f77bcf86cd799439011",
                "org_id": "507f1f77bcf86cd799439012",
                "form_id": "507f1f77bcf86cd799439013",
                "submission_ids": ["507f1f77bcf86cd799439014"],
                "company_name": "Acme Corp",
                "stage": "Series A",
                "sector": ["Technology", "SaaS"],
                "status": "new",
                "exclusion_filter_result": {"excluded": False, "reason": None},
                "scoring": {
                    "total_score": 85,
                    "normalized_score": 85.0,
                    "thesis_matches": ["thesis1", "thesis2"],
                },
                "assigned_user_ids": ["507f1f77bcf86cd799439015"],
                "notes": "Promising early-stage SaaS company",
                "tags": ["AI", "B2B"],
                "timeline": [
                    {
                        "date": "2024-03-20T10:00:00Z",
                        "event": "Deal created from submission",
                        "notes": None,
                    }
                ],
                "created_at": 1710936000,
                "updated_at": 1710936000,
            }
        },
    )

    id: ObjectIdField = Field(default_factory=PyObjectId, description="Deal ID")
    org_id: str = Field(..., description="Organization ID")
    form_id: Optional[str] = Field(None, description="Form ID")
    submission_ids: Optional[List[str]] = Field(
        None, description="List of submission IDs"
    )

    # Core fields
    company_name: Optional[str] = Field(None, description="Company name")
    stage: Optional[str] = Field(None, description="Company stage")
    sector: Optional[Union[str, List[str]]] = Field(
        None, description="Company sector(s)"
    )

    # Deal tracking fields
    status: DealStatus = Field(..., description="Current deal status")
    exclusion_filter_result: Optional[Dict[str, Any]] = Field(
        None, description="Exclusion filter results"
    )
    scoring: Optional[Dict[str, Any]] = Field(None, description="Scoring results")

    # Assignment tracking
    assigned_user_ids: List[str] = Field(
        default_factory=list, description="List of IDs of users assigned to this deal"
    )

    # Optional fields
    notes: Optional[str] = Field(None, description="Notes about the deal")
    tags: List[str] = Field(default_factory=list, description="Deal tags")
    timeline: List[Dict[str, Any]] = Field(
        default_factory=list, description="Deal timeline events"
    )

    favourites: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="List of favourites: each is a dict with user_id and timestamp",
    )

    # Metadata
    created_at: int = Field(..., description="Creation timestamp")
    updated_at: int = Field(..., description="Last update timestamp")


class DealListResponse(TractionXModel):
    """Schema for paginated deal list responses."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "deals": [
                    {
                        "id": "507f1f77bcf86cd799439011",
                        "company_name": "Acme Corp",
                        "stage": "Series A",
                        "sector": ["Technology", "SaaS"],
                        "status": "new",
                        "created_at": 1710936000,
                    }
                ],
                "total": 1,
                "skip": 0,
                "limit": 100,
                "has_more": False,
            }
        }
    )

    deals: List[DealResponse] = Field(..., description="List of deals")
    total: int = Field(..., description="Total number of deals matching filters")
    skip: int = Field(..., description="Number of deals skipped")
    limit: int = Field(..., description="Maximum number of deals returned")
    has_more: bool = Field(..., description="Whether there are more deals available")


class DealSummaryResponse(BaseModel):
    """Schema for deal summary/dashboard statistics."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "total_deals": 150,
                "by_status": {
                    "new": 45,
                    "triage": 30,
                    "reviewed": 25,
                    "rejected": 35,
                    "approved": 10,
                    "negotiating": 3,
                    "closed": 2,
                },
                "by_stage": {
                    "Pre-Seed": 60,
                    "Seed": 45,
                    "Series A": 30,
                    "Series B": 15,
                },
                "by_sector": {
                    "Technology": 80,
                    "Healthcare": 25,
                    "Fintech": 20,
                    "E-commerce": 15,
                    "Other": 10,
                },
                "recent_activity": {"last_7_days": 12, "last_30_days": 45},
            }
        }
    )

    total_deals: int = Field(..., description="Total number of deals")
    by_status: Dict[str, int] = Field(..., description="Deal counts by status")
    by_stage: Dict[str, int] = Field(..., description="Deal counts by stage")
    by_sector: Dict[str, int] = Field(..., description="Deal counts by sector")
    recent_activity: Dict[str, int] = Field(..., description="Recent activity metrics")


class DealTimelineEvent(BaseModel):
    """Schema for deal timeline events."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "date": "2024-03-20T10:00:00Z",
                "event": "Status changed to reviewed",
                "notes": "Initial review completed",
                "user_id": "507f1f77bcf86cd799439011",
            }
        }
    )

    date: str = Field(..., description="Event timestamp (ISO format)")
    event: str = Field(..., description="Event description")
    notes: Optional[str] = Field(None, description="Additional notes")
    user_id: Optional[str] = Field(None, description="User who triggered the event")


class AddTimelineEventRequest(BaseModel):
    """Schema for adding timeline events."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "event": "Meeting scheduled",
                "notes": "Initial pitch meeting scheduled for next week",
            }
        }
    )

    event: str = Field(
        ..., min_length=1, max_length=200, description="Event description"
    )
    notes: Optional[str] = Field(None, max_length=1000, description="Additional notes")


class DealNotesRequest(BaseModel):
    """Schema for adding/updating deal notes."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "notes": "Strong technical team with proven track record in AI/ML"
            }
        }
    )

    notes: str = Field(..., max_length=5000, description="Deal notes")


class BulkDealUpdateRequest(BaseModel):
    """Schema for bulk deal updates."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "deal_ids": ["507f1f77bcf86cd799439011", "507f1f77bcf86cd799439012"],
                "updates": {"status": "reviewed", "add_tags": ["batch-reviewed"]},
            }
        }
    )

    deal_ids: List[str] = Field(
        ..., min_items=1, max_items=100, description="List of deal IDs to update"
    )  # type: ignore
    updates: DealUpdate = Field(..., description="Updates to apply to all deals")


class DealAssignRequest(BaseModel):
    """Schema for assigning users to a deal."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "user_ids": ["64ec9b9fe4371ff1375aa111", "64ec9b9fe4371ff1375aa222"]
            }
        }
    )

    user_ids: List[str] = Field(
        ...,
        min_length=0,
        max_length=10,
        description="List of ObjectIds of users to assign to the deal (max 10 users)",
    )


class DealUpdateAssignmentsRequest(BaseModel):
    """Schema for updating assigned users on a deal."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {"user_ids": ["64ec9b9fe4371ff1375aa222"], "action": "replace"}
        }
    )

    user_ids: List[str] = Field(
        ...,
        min_length=1,
        max_length=10,
        description="List of ObjectIds of users to assign to the deal (max 10 users)",
    )
    action: str = Field(
        default="replace",
        pattern="^(replace|add|remove)$",
        description="Action to perform: 'replace' (default), 'add', or 'remove' users",
    )


class DealStatusUpdateRequest(BaseModel):
    """Schema for updating deal status."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "status": "reviewed",
                "note": "Completed first call with founder",
            }
        }
    )

    status: DealStatus = Field(..., description="New deal status")
    note: Optional[str] = Field(
        None, max_length=1000, description="Optional note explaining the status change"
    )


class DealSearchRequest(BaseModel):
    """Schema for advanced deal search."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "query": "AI startup",
                "filters": {
                    "status": ["new", "triage"],
                    "stage": ["Seed", "Series A"],
                    "sector": ["Technology"],
                    "tags": ["AI", "B2B"],
                },
                "date_range": {"start": "2024-01-01", "end": "2024-12-31"},
                "sort_by": "created_at",
                "sort_order": "desc",
                "favourites_only": True,
            }
        }
    )

    query: Optional[str] = Field(None, description="Search query for company name")
    filters: Optional[Dict[str, List[str]]] = Field(
        None, description="Filters to apply"
    )
    date_range: Optional[Dict[str, str]] = Field(None, description="Date range filter")
    sort_by: Optional[str] = Field("created_at", description="Field to sort by")
    sort_order: Optional[str] = Field("desc", description="Sort order (asc/desc)")
    favourites_only: Optional[bool] = Field(
        None, description="Filter to only favourite deals for current user"
    )


# Deal Submission Preview Schemas
class SubmissionQuestionAnswer(BaseModel):
    """Schema for a single question answer in a submission preview."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "question_id": "6847b66555bce11ad7dcd137",
                "label": "Company Name",
                "type": "short_text",
                "answer": "TractionX",
            }
        }
    )

    question_id: str = Field(..., description="Question ID")
    label: str = Field(..., description="Human-readable question label")
    type: str = Field(..., description="Question type")
    answer: Any = Field(
        ..., description="Answer value (mapped to display labels for select types)"
    )


class SubmissionRepeatableInstance(BaseModel):
    """Schema for a single instance of a repeatable section."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "index": 0,
                "answers": [
                    {
                        "question_id": "6847b66755bce11ad7dcd13c",
                        "label": "Founder Name",
                        "type": "short_text",
                        "answer": "Andy",
                    },
                    {
                        "question_id": "6847b66755bce11ad7dcd13d",
                        "label": "Role(s) / Title(s)",
                        "type": "multi_select",
                        "answer": ["CEO"],
                    },
                ],
            }
        }
    )

    index: int = Field(..., description="Instance index (0-based)")
    answers: List[SubmissionQuestionAnswer] = Field(
        ..., description="Answers for this instance"
    )


class SubmissionSection(BaseModel):
    """Schema for a section in a submission preview."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "section_id": "6847b66455bce11ad7dcd136",
                "title": "Company Overview",
                "repeatable": False,
                "questions": [
                    {
                        "question_id": "6847b66555bce11ad7dcd137",
                        "label": "Company Name",
                        "type": "short_text",
                        "answer": "TractionX",
                    }
                ],
            }
        }
    )

    section_id: str = Field(..., description="Section ID")
    title: str = Field(..., description="Section title")
    repeatable: bool = Field(
        default=False, description="Whether this is a repeatable section"
    )
    questions: Optional[List[SubmissionQuestionAnswer]] = Field(
        default=None, description="Questions for non-repeatable sections"
    )
    instances: Optional[List[SubmissionRepeatableInstance]] = Field(
        default=None, description="Instances for repeatable sections"
    )


class SubmissionPreview(BaseModel):
    """Schema for a single submission preview."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "submission_id": "6847e9465d483398169ce2d2",
                "submitted_at": "2025-06-13T17:26:54.204Z",
                "form_name": "AI Funds 2025",
                "form_version": 3,
                "sections": [
                    {
                        "section_id": "6847b66455bce11ad7dcd136",
                        "title": "Company Overview",
                        "repeatable": False,
                        "questions": [
                            {
                                "question_id": "6847b66555bce11ad7dcd137",
                                "label": "Company Name",
                                "type": "short_text",
                                "answer": "TractionX",
                            }
                        ],
                    }
                ],
            }
        }
    )

    submission_id: str = Field(..., description="Submission ID")
    submitted_at: str = Field(..., description="Submission timestamp (ISO format)")
    form_name: str = Field(..., description="Name of the form")
    form_version: int = Field(..., description="Version of the form")
    sections: List[SubmissionSection] = Field(
        ..., description="Form sections with answers"
    )


class DealSubmissionPreviewResponse(BaseModel):
    """Schema for deal submission preview response."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "deal_id": "684c5f5ee6c93eaf9eaf5942",
                "no_submissions": False,
                "submissions": [
                    {
                        "submission_id": "6847e9465d483398169ce2d2",
                        "submitted_at": "2025-06-13T17:26:54.204Z",
                        "form_name": "AI Funds 2025",
                        "form_version": 3,
                        "sections": [],
                    }
                ],
                "dropdown": [
                    {
                        "form_name": "AI Funds 2025",
                        "submission_id": "6847e9465d483398169ce2d2",
                        "submitted_at": "2025-06-13T17:26:54.204Z",
                    }
                ],
            }
        }
    )

    deal_id: str = Field(..., description="Deal ID")
    no_submissions: bool = Field(
        default=False, description="Whether there are no submissions"
    )
    submissions: List[SubmissionPreview] = Field(
        default_factory=list, description="List of submission previews"
    )
    dropdown: List[Dict[str, str]] = Field(
        default_factory=list,
        description="Dropdown options for frontend (form_name, submission_id, submitted_at)",
    )


class DealDashboardFilters(BaseModel):
    """Schema for deal dashboard filters that can be saved as user preferences."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "status": ["new", "triage", "reviewed"],
                "assigned_to_me": True,
                "created_at_start": "2024-01-01T00:00:00Z",
                "created_at_end": "2024-12-31T23:59:59Z",
                "sort_by": "updated_at",
                "sort_dir": "desc",
                "favourites_only": True,
            }
        }
    )

    status: Optional[List[str]] = Field(
        None, description="List of deal statuses to filter by"
    )
    assigned_to_me: Optional[bool] = Field(
        None, description="Filter to show only deals assigned to current user"
    )
    created_at_start: Optional[str] = Field(
        None, description="ISO string for deals created after this date"
    )
    created_at_end: Optional[str] = Field(
        None, description="ISO string for deals created before this date"
    )
    sort_by: Optional[str] = Field(
        None, description="Field to sort by (created_at, updated_at, status_updated_at)"
    )
    sort_dir: Optional[str] = Field(None, description="Sort direction (asc/desc)")
    favourites_only: Optional[bool] = Field(
        None, description="Filter to only favourite deals for current user"
    )


class UpdateUserPreferencesRequest(BaseModel):
    """Schema for updating user preferences."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "deal_dashboard_filters": {
                    "status": ["new", "triage"],
                    "assigned_to_me": True,
                    "sort_by": "updated_at",
                    "sort_dir": "desc",
                }
            }
        }
    )

    deal_dashboard_filters: Optional[DealDashboardFilters] = Field(
        None, description="Deal dashboard filter preferences"
    )
