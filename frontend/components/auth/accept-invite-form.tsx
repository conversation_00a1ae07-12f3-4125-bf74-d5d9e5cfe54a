"use client"

import { useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { motion } from "framer-motion"
import * as z from "zod"
import Link from "next/link"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { Icons } from "@/components/icons"
import { AuthCard } from "./auth-card"
import { AuthAPI } from "@/lib/api/auth-api"

const acceptInviteSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type AcceptInviteFormValues = z.infer<typeof acceptInviteSchema>

export function AcceptInviteForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams?.get("token")
  const [isLoading, setIsLoading] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)

  const form = useForm<AcceptInviteFormValues>({
    resolver: zodResolver(acceptInviteSchema),
    defaultValues: {
      name: "",
      password: "",
      confirmPassword: "",
    },
  })

  async function onSubmit(data: AcceptInviteFormValues) {
    if (!token) {
      toast({
        title: "Invalid invitation link",
        description: "This invitation link is invalid or has expired.",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)

    try {
      await AuthAPI.acceptInvitation(token, data.password, data.confirmPassword)
      setIsSuccess(true)
      toast({
        title: "Welcome to TractionX!",
        description: "Your account has been created successfully.",
      })
    } catch (error) {
      console.error("Accept invitation error:", error)
      toast({
        title: "Invitation failed",
        description: error instanceof Error ? error.message : "Please try again or contact support.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (!token) {
    return (
      <AuthCard>
        <div className="space-y-6 text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", duration: 0.6 }}
            className="mx-auto flex size-16 items-center justify-center rounded-full bg-gradient-to-r from-red-500 to-pink-500"
          >
            <Icons.alertCircle className="size-8 text-white" />
          </motion.div>
          
          <div className="space-y-2">
            <h3 className="text-xl font-semibold">Invalid invitation</h3>
            <p className="text-muted-foreground">
              This invitation link is invalid or has expired.
            </p>
          </div>

          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Please contact your administrator for a new invitation.
            </p>
            
            <Link href="/login">
              <Button className="w-full">
                Go to sign in
              </Button>
            </Link>
          </div>
        </div>
      </AuthCard>
    )
  }

  if (isSuccess) {
    return (
      <AuthCard>
        <div className="space-y-6 text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", duration: 0.6 }}
            className="mx-auto flex size-16 items-center justify-center rounded-full bg-gradient-to-r from-green-500 to-emerald-500"
          >
            <Icons.check className="size-8 text-white" />
          </motion.div>
          
          <div className="space-y-2">
            <h3 className="text-xl font-semibold">Welcome to TractionX!</h3>
            <p className="text-muted-foreground">
              Your account has been created successfully. You can now sign in and start exploring.
            </p>
          </div>

          <Link href="/login">
            <Button className="w-full">
              Continue to sign in
            </Button>
          </Link>
        </div>
      </AuthCard>
    )
  }

  return (
    <AuthCard>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-2 text-center">
          <h3 className="text-lg font-semibold">Complete your account</h3>
          <p className="text-sm text-muted-foreground">
            You've been invited to join TractionX. Set up your account to get started.
          </p>
          <div className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-xs text-blue-700 dark:bg-blue-900/30 dark:text-blue-300">
            <Icons.info className="mr-1 size-3" />
            Beta Access
          </div>
        </div>

        <div className="space-y-4">
          {/* Name Field */}
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-medium">
              Full name
            </Label>
            <Input
              id="name"
              type="text"
              placeholder="Enter your full name"
              autoComplete="name"
              disabled={isLoading}
              className="h-11 border-white/20 bg-white/50 focus:border-blue-500/50 focus:ring-blue-500/20 dark:border-white/10 dark:bg-black/50"
              {...form.register("name")}
            />
            {form.formState.errors.name && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.name.message}
              </motion.p>
            )}
          </div>

          {/* Password Field */}
          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm font-medium">
              Password
            </Label>
            <Input
              id="password"
              type="password"
              placeholder="Create a strong password"
              autoComplete="new-password"
              disabled={isLoading}
              className="h-11 border-white/20 bg-white/50 focus:border-blue-500/50 focus:ring-blue-500/20 dark:border-white/10 dark:bg-black/50"
              {...form.register("password")}
            />
            {form.formState.errors.password && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.password.message}
              </motion.p>
            )}
          </div>

          {/* Confirm Password Field */}
          <div className="space-y-2">
            <Label htmlFor="confirmPassword" className="text-sm font-medium">
              Confirm password
            </Label>
            <Input
              id="confirmPassword"
              type="password"
              placeholder="Confirm your password"
              autoComplete="new-password"
              disabled={isLoading}
              className="h-11 border-white/20 bg-white/50 focus:border-blue-500/50 focus:ring-blue-500/20 dark:border-white/10 dark:bg-black/50"
              {...form.register("confirmPassword")}
            />
            {form.formState.errors.confirmPassword && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.confirmPassword.message}
              </motion.p>
            )}
          </div>
        </div>

        <Button
          type="submit"
          disabled={isLoading}
          className="h-11 w-full bg-gradient-to-r from-blue-600 to-purple-600 font-medium text-white shadow-lg shadow-blue-500/25 transition-all duration-200 hover:from-blue-700 hover:to-purple-700"
        >
          {isLoading ? (
            <>
              <Icons.spinner className="mr-2 size-4 animate-spin" />
              Creating account...
            </>
          ) : (
            "Accept & Join"
          )}
        </Button>

        <div className="text-center text-xs text-muted-foreground">
          By creating an account, you agree to our{" "}
          <Link href="/terms" className="text-blue-600 hover:text-blue-500">
            Terms of Service
          </Link>{" "}
          and{" "}
          <Link href="/privacy" className="text-blue-600 hover:text-blue-500">
            Privacy Policy
          </Link>
        </div>
      </form>
    </AuthCard>
  )
}
