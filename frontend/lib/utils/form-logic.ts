// Form visibility and logic evaluation utilities

interface ConditionClause {
  question_id: string;
  value: any;
  section_instance_index?: number;
  operator?: string;
}

interface VisibilityCondition {
  operator: "and" | "or" | "not" | "==" | "!=" | ">" | "<" | ">=" | "<=";
  conditions: ConditionClause[];
}

// Helper function to evaluate equality based on value types
function evaluateEquality(actualValue: any, expectedValue: any): boolean {
  // Handle null/undefined cases
  if (actualValue == null || expectedValue == null) {
    return actualValue === expectedValue;
  }

  // Boolean questions: Handle both boolean and string representations
  if (typeof actualValue === 'boolean' || typeof expectedValue === 'boolean') {
    // Convert both values to boolean for comparison
    const actualBool = Boolean(actualValue === true || actualValue === 'true' || actualValue === 'Yes' || actualValue === 'yes');
    const expectedBool = Boolean(expectedValue === true || expectedValue === 'true' || expectedValue === 'Yes' || expectedValue === 'yes');
    return actualBool === expectedBool;
  }

  // Multi-select questions: Array handling
  if (Array.isArray(actualValue)) {
    if (Array.isArray(expectedValue)) {
      // Array to array comparison (exact match)
      return actualValue.length === expectedValue.length && 
             actualValue.every(val => expectedValue.includes(val));
    } else {
      // Check if array contains the expected single value
      return actualValue.includes(expectedValue);
    }
  }

  // Single-select and text questions: Case-insensitive string comparison
  if (typeof actualValue === 'string' && typeof expectedValue === 'string') {
    return actualValue.toLowerCase() === expectedValue.toLowerCase();
  }

  // Number questions: Numeric comparison
  if (typeof actualValue === 'number' || typeof expectedValue === 'number' || 
      (!isNaN(Number(actualValue)) && !isNaN(Number(expectedValue)))) {
    return Number(actualValue) === Number(expectedValue);
  }

  // Fallback to strict equality
  return actualValue === expectedValue;
}

// Operator mapping for condition evaluation
const OPERATOR_MAP = {
  "==": (a: any, b: any) => evaluateEquality(a, b),
  "!=": (a: any, b: any) => !evaluateEquality(a, b),
  ">": (a: any, b: any) => Number(a) > Number(b),
  "<": (a: any, b: any) => Number(a) < Number(b),
  ">=": (a: any, b: any) => Number(a) >= Number(b),
  "<=": (a: any, b: any) => Number(a) <= Number(b),
};

/**
 * Evaluates a single condition clause against current answers
 */
function evaluateConditionClause(
  clause: ConditionClause,
  answers: Record<string, any>,
  operator?: string
): boolean {
  const { question_id, value: expectedValue, section_instance_index } = clause;

  let actualValue: any;

  // Handle repeatable section answers
  if (section_instance_index !== undefined) {
    const repeatKey = `${question_id}_${section_instance_index}`;
    actualValue = answers[repeatKey];
  } else {
    // Look in regular answers first
    actualValue = answers[question_id];

    // If not found and no specific instance index, try to find in any repeatable instance
    if ((actualValue === undefined || actualValue === null)) {
      // Check if this question exists in any repeatable section instance
      const repeatableKeys = Object.keys(answers).filter(key => key.startsWith(`${question_id}_`));
      if (repeatableKeys.length > 0) {
        // Use the first instance's answer (instance 0)
        actualValue = answers[`${question_id}_0`];
      }
    }
  }

  // If no answer exists, condition is false
  if (actualValue === undefined || actualValue === null) {
    return false;
  }

  // Apply the appropriate operator (default to equality if no operator specified)
  const conditionOperator = clause.operator || operator || '==';
  
  switch (conditionOperator) {
    case '==':
    case 'eq':
      const eqResult = evaluateEquality(actualValue, expectedValue);
      return eqResult;
      
    case '!=':
    case 'ne':
      const neResult = !evaluateEquality(actualValue, expectedValue);
      return neResult;
      
    case '>':
    case 'gt':
      const gtResult = Number(actualValue) > Number(expectedValue);
      return gtResult;
      
    case '>=':
    case 'gte':
      const gteResult = Number(actualValue) >= Number(expectedValue);
      return gteResult;
      
    case '<':
    case 'lt':
      const ltResult = Number(actualValue) < Number(expectedValue);
      return ltResult;
      
    case '<=':
    case 'lte':
      const lteResult = Number(actualValue) <= Number(expectedValue);
      return lteResult;
      
    case 'in':
      const inResult = Array.isArray(expectedValue) && expectedValue.includes(actualValue);
      return inResult;
      
    case 'not_in':
      const notInResult = Array.isArray(expectedValue) && !expectedValue.includes(actualValue);
      return notInResult;
      
    case 'contains':
      const containsResult = String(actualValue).toLowerCase().includes(String(expectedValue).toLowerCase());
      return containsResult;
      
    case 'not_contains':
      const notContainsResult = !String(actualValue).toLowerCase().includes(String(expectedValue).toLowerCase());
      return notContainsResult;
      
    default:
      // Default to equality for backward compatibility
      const defaultResult = evaluateEquality(actualValue, expectedValue);
      return defaultResult;
  }
}

/**
 * Evaluates a visibility condition against current form state
 */
export function evaluateVisibility(
  condition: VisibilityCondition | null | undefined,
  answers: Record<string, any>
): boolean {
  // No condition means always visible
  if (!condition) {
    return true;
  }

  const { operator, conditions } = condition;

  // Handle different operators
  switch (operator) {
    case "and":
      const andResults = conditions.map(clause => {
        const result = evaluateConditionClause(clause, answers, operator);
        return result;
      });
      const andResult = andResults.every(r => r);
      return andResult;

    case "or":
      const orResults = conditions.map(clause => {
        const result = evaluateConditionClause(clause, answers, operator);
        return result;
      });
      const orResult = orResults.some(r => r);
      return orResult;

    case "not":
      // NOT operator - all conditions must be false
      const notResults = conditions.map(clause => {
        const result = evaluateConditionClause(clause, answers, operator);
        return result;
      });
      const notResult = !notResults.some(r => r);
      return notResult;

    case "==":
    case "!=":
    case ">":
    case "<":
    case ">=":
    case "<=":
      // Single comparison operator - evaluate first condition with this operator
      if (conditions.length === 0) return true;

      const clause = conditions[0];
      const { question_id, section_instance_index } = clause;

      let actualValue: any;

      // Handle repeatable section answers
      if (section_instance_index !== undefined) {
        const repeatKey = `${question_id}_${section_instance_index}`;
        actualValue = answers[repeatKey];
      } else {
        // Look in regular answers
        actualValue = answers[question_id];
      }

      // If no answer exists, condition is false
      if (actualValue === undefined || actualValue === null) {
        return false;
      }

      // Special handling for == and != with sophisticated type handling
      if (operator === "==" || operator === "!=") {
        const isEqual = evaluateEquality(actualValue, clause.value);
        const result = operator === "==" ? isEqual : !isEqual;
        return result;
      }

      // For other operators, use the standard comparison
      const operatorFn = OPERATOR_MAP[operator];
      if (!operatorFn) {
        return true;
      }

      const result = operatorFn(actualValue, clause.value);
      return result;

    default:
      return true;
  }
}

export function calculateFormProgress(form: any, answers: Record<string, any>): number {
  if (!form?.sections) return 0;

  const allQuestions = form.sections.flatMap((section: any) => {
    // Only count visible sections
    if (section.visibility_condition && !evaluateVisibility(section.visibility_condition, answers)) {
      return [];
    }

    return (section.questions || []).filter((question: any) => {
      // Only count visible questions
      return !question.visibility_condition || evaluateVisibility(question.visibility_condition, answers);
    });
  });

  const requiredQuestions = allQuestions.filter((q: any) => q.required);
  const answeredRequired = requiredQuestions.filter((q: any) => {
    const answer = answers[q._id];
    return answer !== undefined && answer !== null && answer !== '';
  });

  return requiredQuestions.length > 0
    ? (answeredRequired.length / requiredQuestions.length) * 100
    : 0;
}

export function getVisibleQuestions(form: any, answers: Record<string, any>): any[] {
  const visibleQuestions: any[] = [];
  
  if (!form?.sections) return visibleQuestions;

  // Build a map of sections for quick lookup
  const sectionMap: Record<string, any> = {};
  form.sections.forEach((section: any) => {
    if (section && (section._id || section.id)) {
      sectionMap[section._id || section.id] = section;
    }
  });

  // Process each section
  form.sections.forEach((section: any) => {
    // Skip repeatable sections - they'll be handled by their controller questions
    if (!section || section.repeatable) {
      return;
    }

    // Check section visibility
    if (section.visibility_condition && !evaluateVisibility(section.visibility_condition, answers)) {
      return;
    }

    section.questions?.forEach((question: any) => {
      if (!question) return;
      
      const questionId = question._id || question.id;
      if (!questionId) return;

      // Check if this question controls a repeatable section
      if (question.repeat_section_id) {
        const repeatSection = sectionMap[question.repeat_section_id];
        if (repeatSection) {
          // First, add the controller question if visible
          if (!question.visibility_condition || evaluateVisibility(question.visibility_condition, answers)) {
            visibleQuestions.push(question);
          }

          // Then handle the repeatable section instances
          const controllerAnswer = answers[questionId];
          const numInstances = Math.max(0, Math.min(Number(controllerAnswer) || 0, question.max_repeats || 10));
          
          for (let instanceIdx = 0; instanceIdx < numInstances; instanceIdx++) {
            repeatSection.questions?.forEach((subQuestion: any) => {
              if (!subQuestion) return;
              
              // Check visibility for this instance and add to visible questions
              if (!subQuestion.visibility_condition || evaluateVisibility(subQuestion.visibility_condition, answers)) {
                // Create a copy with the scoped ID for validation
                const scopedQuestion = {
                  ...subQuestion,
                  _id: `${subQuestion._id || subQuestion.id}_${instanceIdx}`
                };
                visibleQuestions.push(scopedQuestion);
              }
            });
          }
        }
      } else {
        // Regular question (not controlling a repeatable section)
        if (!question.visibility_condition || evaluateVisibility(question.visibility_condition, answers)) {
          visibleQuestions.push(question);
        }
      }
    });
  });

  return visibleQuestions;
}

export function validateAnswer(question: any, value: any): string | null {
  // Required validation
  if (question.required && (value === undefined || value === null || value === '')) {
    return `${question.label} is required`;
  }

  // Skip validation for empty optional fields
  if (!question.required && (value === undefined || value === null || value === '')) {
    return null;
  }

  // Type-specific validation
  switch (question.type) {
    case 'NUMBER':
      if (isNaN(Number(value))) {
        return 'Please enter a valid number';
      }
      if (question.validation?.min !== undefined && Number(value) < question.validation.min) {
        return `Value must be at least ${question.validation.min}`;
      }
      if (question.validation?.max !== undefined && Number(value) > question.validation.max) {
        return `Value must be at most ${question.validation.max}`;
      }
      break;

    case 'SHORT_TEXT':
    case 'LONG_TEXT':
      if (question.validation?.min && value.length < question.validation.min) {
        return `Must be at least ${question.validation.min} characters`;
      }
      if (question.validation?.max && value.length > question.validation.max) {
        return `Must be at most ${question.validation.max} characters`;
      }
      if (question.validation?.regex) {
        const regex = new RegExp(question.validation.regex);
        if (!regex.test(value)) {
          return question.validation.message || 'Invalid format';
        }
      }
      break;

    case 'MULTI_SELECT':
      if (Array.isArray(value)) {
        if (question.validation?.min && value.length < question.validation.min) {
          return `Please select at least ${question.validation.min} options`;
        }
        if (question.validation?.max && value.length > question.validation.max) {
          return `Please select at most ${question.validation.max} options`;
        }
      }
      break;
  }

  return null;
}
