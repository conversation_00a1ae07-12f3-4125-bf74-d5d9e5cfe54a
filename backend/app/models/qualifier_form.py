from typing import Dict, List, Optional, Any
from typing import Annotated
from pydantic import Field

from datetime import datetime
from app.models.base import TractionXModel
from app.models.base import ObjectIdField
from app.utils.model.helper import populate_reference

class QualifierFormConfig(TractionXModel):
    """Qualifier form configuration."""
    
    id: ObjectIdField = Field(default_factory=ObjectIdField, alias="_id")
    form_id: Annotated[ObjectIdField, populate_reference("Form")]
    org_id: ObjectIdField
    is_active: bool = True
    sharing_config_id: Optional[ObjectIdField] = None
    trigger_config_ids: List[ObjectIdField] = []
    created_at: int = Field(default_factory=lambda: int(
        datetime.utcnow().timestamp()))
    updated_at: int = Field(default_factory=lambda: int(
        datetime.utcnow().timestamp()))
    


class QualifierFormStats(TractionXModel):
    """Qualifier form statistics."""
    
    id: ObjectIdField = Field(default_factory=ObjectIdField, alias="_id")
    form_id: Annotated[ObjectIdField, populate_reference("Form")]
    views: int = 0
    submissions: int = 0
    conversion_rate: float = 0.0
    avg_completion_time: int = 0
    sharing_stats: Dict[str, Any] = {}
    created_at: int = Field(default_factory=lambda: int(
        datetime.utcnow().timestamp()))
    updated_at: int = Field(default_factory=lambda: int(
        datetime.utcnow().timestamp()))
