"use client"

import { motion } from "framer-motion"
import { Icons } from "@/components/icons"
import { cn } from "@/lib/utils"
import { AnimatedHero } from "@/components/auth/animated-hero"

interface AuthLayoutProps {
  children: React.ReactNode
  title: string
  subtitle: string
  showBackButton?: boolean
  backHref?: string
  className?: string
}

export function AuthLayout({
  children,
  title,
  subtitle,
  showBackButton = false,
  backHref = "/",
  className,
}: AuthLayoutProps) {
  return (
    <div className={cn(
      "flex min-h-screen flex-1 flex-col bg-gradient-to-br from-slate-50 via-white to-slate-100",
      "safe-top safe-bottom" // Respect device safe areas
    )}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-slate-200/20 to-transparent" />
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-slate-200/20 to-transparent" />
      </div>

      <div className="relative flex min-h-0 flex-1">
        {/* Left Side - Animated Branding Hero */}
        <div className="relative hidden flex-col items-center justify-center overflow-hidden p-12 lg:flex lg:w-1/2">
          {/* Enhanced Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-purple-600/5 to-pink-600/10" />
          <div className="absolute inset-0 bg-gradient-to-t from-slate-900/5 via-transparent to-slate-900/5 dark:from-slate-100/5 dark:to-slate-100/5" />
          
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.3 }}
            className="relative z-10 flex w-full items-center justify-center"
          >
            <AnimatedHero />
          </motion.div>
        </div>

        {/* Right Side - Auth Form - Mobile-first */}
        <div className={cn(
          "flex flex-1 items-center justify-center lg:w-1/2",
          // Mobile-first padding with safe areas
          "safe-top safe-bottom p-4 xs:p-6 md:p-8"
        )}>
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            className={cn(
              "w-full max-w-md space-y-6 md:space-y-8",
              className
            )}
          >
            {/* Mobile Logo */}
            <div className="flex justify-center lg:hidden">
              <Icons.logoFull className="h-8 text-foreground" />
            </div>

            {/* Header - Mobile-first typography */}
            <div className="space-y-2 text-center">
              <h2 className={cn(
                "font-bold tracking-tight",
                // Mobile-first heading size
                "text-2xl md:text-3xl"
              )}>
                {title}
              </h2>
              <p className={cn(
                "text-muted-foreground",
                // Mobile-first subtitle size
                "text-base md:text-lg"
              )}>
                {subtitle}
              </p>
            </div>

            {/* Form Content */}
            {children}
            
            {/* Footer */}
            <div className="text-center">
              <p className="text-xs text-muted-foreground">
                You're accessing TractionX Beta. 
                <br />
                Built with ❤️ for the future of investing.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
