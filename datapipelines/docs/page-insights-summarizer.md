# Page Insights Summarizer

The Page Insights Summarizer is a robust, per-page website summarization pipeline that extracts high-signal, structured insights from startup websites for investment analysis.

## Overview

This service is designed to help investors, VCs, and deal teams evaluate companies faster and more accurately by extracting structured insights from company websites. It's part of the larger Website Intelligence Pipeline and works seamlessly with the Sitemap Generator.

## Features

- **Text Extraction**: Removes scripts, styles, and non-visible content while prioritizing structured sections
- **Smart Chunking**: Splits content into optimal chunks for LLM processing (1000-8000 characters)
- **Investment-Focused Analysis**: Extracts insights relevant to startup investors and deal teams
- **JSON Output**: Returns structured data in a consistent format
- **Deduplication**: Automatically removes duplicate insights across chunks
- **Error Handling**: Robust error handling with detailed logging
- **Batch Processing**: Support for processing multiple pages efficiently

## Architecture

### Components

1. **Text Extraction** (`extract_visible_text`)
   - Removes `<script>`, `<style>`, `<head>`, `<noscript>`
   - Prioritizes structured tags like `<main>` or `<article>`
   - Fallback to full text if missing
   - Condenses excess line breaks

2. **Chunking** (`chunk_text_safely`)
   - Splits by 2+ line breaks, hard-break tokens, or length limit
   - Avoids breaking inside tag-heavy or meta sections
   - Fallbacks to char-based slicing if block parsing fails
   - Ensures chunks are between 1000–8000 characters

3. **Prompt Generation** (`generate_page_insights_prompt`)
   - Context: Investment analyst extracting structured insights
   - Explains output schema with examples
   - Emphasizes signal over noise
   - Instructs JSON-only output (no markdown or text)

4. **LLM Processing**
   - Model: `meta-llama/Llama-3.3-70B-Instruct-Turbo-Free`
   - Runs one chunk at a time
   - Each chunk is independently parsed, deduped, and merged

5. **JSON Fixing and Parsing** (`fix_and_parse_json`)
   - Removes markdown wrappers
   - Auto-fixes common bracket errors
   - Returns valid JSON or None

6. **Insight Merging**
   - Flatten all valid insights lists from parsed chunks
   - Merge under the source URL
   - Deduplicate based on label and value

## Usage

### Basic Usage

```python
from app.services.page_insights_summarizer import get_page_insights_summarizer

# Get the summarizer instance
summarizer = await get_page_insights_summarizer()

# Summarize a single page
result = await summarizer.summarize_page("https://example.com")

if result["success"]:
    insights = result["insights"]
    print(f"Extracted {len(insights)} insights")
else:
    print(f"Failed: {result['error']}")
```

### Batch Processing

```python
# Summarize multiple pages
urls = ["https://example.com", "https://example.com/about"]
batch_result = await summarizer.summarize_pages_batch(urls)

for url, page_result in batch_result["results"].items():
    if page_result["success"]:
        print(f"{url}: {len(page_result['insights'])} insights")
```

### Complete Pipeline Integration

```python
from app.services.sitemap_generator import get_sitemap_generator
from app.services.page_insights_summarizer import get_page_insights_summarizer

# Step 1: Generate sitemap
sitemap_generator = await get_sitemap_generator()
sitemap_result = await sitemap_generator.generate_sitemap("example.com")

if sitemap_result["success"]:
    urls = sitemap_result["urls"]
    
    # Step 2: Extract insights from each page
    page_summarizer = await get_page_insights_summarizer()
    all_insights = []
    
    for url in urls:
        page_result = await page_summarizer.summarize_page(url)
        if page_result["success"]:
            all_insights.extend(page_result["insights"])
```

## Output Format

The service returns structured JSON with the following format:

```json
{
  "success": true,
  "source_url": "https://example.com",
  "insights": [
    {
      "label": "Business Type",
      "value": "B2B SaaS platform for enterprise analytics",
      "type": "business"
    },
    {
      "label": "Target Market",
      "value": "Enterprise companies in fintech and healthcare",
      "type": "market"
    },
    {
      "label": "Team Size",
      "value": "50+ employees with offices in San Francisco and New York",
      "type": "team"
    }
  ],
  "total_insights": 3,
  "chunks_processed": 2,
  "processing_time": 15.23,
  "html_content_length": 25000,
  "text_content_length": 8000
}
```

## Insight Categories

The service extracts insights in the following categories:

- **Business Type**: B2B, B2C, or both
- **Legal/Entity**: Company legal name and structure
- **Geography**: Country or region of operation
- **Products**: Products and features offered
- **Target Customers**: Target industries or customer segments
- **Stage**: Startup stage and maturity
- **Sector**: Industry sector classification
- **Team**: Information about founders, team, and backgrounds
- **Pricing/GTM**: Pricing model and go-to-market strategy
- **Contact**: Contact information and social handles
- **Compliance**: GDPR, SOC2, and other compliance information
- **Fundraising**: Signs of fundraising, traction, or credibility

## Configuration

The service uses the following configuration from your environment:

- `BRIGHTDATA_API_KEY`: API key for BrightData Web Unlocker
- `BRIGHTDATA_WEB_UNLOCKER_ZONE`: BrightData zone for web scraping
- `TOGETHER_API_KEY`: API key for Together AI LLM

## Error Handling

The service includes comprehensive error handling:

- **Network Errors**: Automatic retries with exponential backoff
- **LLM Errors**: Graceful handling of API failures
- **JSON Parsing**: Auto-fixing of common JSON formatting issues
- **Content Issues**: Handling of empty or invalid content
- **Rate Limiting**: Built-in rate limiting and delays

## Performance

- **Processing Time**: Typically 10-30 seconds per page depending on content size
- **Chunking**: Optimized for LLM token limits and processing efficiency
- **Memory Usage**: Minimal memory footprint with streaming processing
- **Concurrency**: Supports batch processing with configurable delays

## Integration

The Page Insights Summarizer integrates seamlessly with:

- **Sitemap Generator**: Use discovered URLs for insight extraction
- **BrightData Client**: Leverages existing web scraping infrastructure
- **Together AI Client**: Uses unified LLM processing
- **Storage Services**: Can be extended to store results in S3 or databases

## Testing

Run the test script to verify functionality:

```bash
cd datapipelines
poetry run python test_page_insights_summarizer.py
```

Or run the complete pipeline example:

```bash
poetry run python example_website_intelligence_pipeline.py
```

## Future Enhancements

- **Signal Strength Scoring**: Add confidence scores to insights
- **Format Detection**: Auto-detect and tag known page formats (Privacy, Terms, etc.)
- **Chunk Metadata**: Store chunk-level metadata for tracing/debugging
- **Scoring Integration**: Plug into downstream scoring and sector tagging
- **Model Selection**: Support alternate summarization models for different budget tiers
- **Caching**: Implement result caching for improved performance 