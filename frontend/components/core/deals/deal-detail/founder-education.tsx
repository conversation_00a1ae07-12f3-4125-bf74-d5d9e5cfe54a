"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { GraduationCap, MapPin, Calendar, Award, Building2 } from "lucide-react"
import { motion } from "framer-motion"
import clsx from "clsx"

interface Education {
  id: string
  degree: string
  major: string
  institution: string
  location?: string
  startDate: string
  endDate?: string
  gpa?: number
  honors?: string[]
  type: 'bachelor' | 'master' | 'phd' | 'certificate' | 'other'
}

interface FounderEducationProps {
  education: Education[]
  className?: string
}

const getDegreeIcon = (type: string) => {
  switch (type) {
    case 'phd':
      return Award
    case 'master':
      return GraduationCap
    case 'bachelor':
      return GraduationCap
    case 'certificate':
      return Award
    default:
      return GraduationCap
  }
}

const getDegreeColor = (type: string) => {
  switch (type) {
    case 'phd':
      return 'bg-slate-100 border-slate-200 text-slate-700'
    case 'master':
      return 'bg-slate-100 border-slate-200 text-slate-700'
    case 'bachelor':
      return 'bg-slate-100 border-slate-200 text-slate-700'
    case 'certificate':
      return 'bg-slate-100 border-slate-200 text-slate-700'
    default:
      return 'bg-slate-100 border-slate-200 text-slate-700'
  }
}

const getDegreeText = (type: string) => {
  switch (type) {
    case 'phd':
      return 'PhD'
    case 'master':
      return 'Master'
    case 'bachelor':
      return 'Bachelor'
    case 'certificate':
      return 'Certificate'
    default:
      return 'Other'
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
}

const isTopUniversity = (institution: string) => {
  const topUniversities = [
    'harvard', 'stanford', 'mit', 'yale', 'princeton', 'columbia', 'university of pennsylvania',
    'california institute of technology', 'caltech', 'university of chicago', 'northwestern',
    'duke', 'johns hopkins', 'cornell', 'brown', 'university of california berkeley', 'uc berkeley',
    'university of michigan', 'university of virginia', 'georgetown', 'carnegie mellon',
    'university of southern california', 'usc', 'new york university', 'nyu', 'university of texas',
    'university of illinois', 'georgia institute of technology', 'gatech', 'university of washington'
  ]
  return topUniversities.some(uni => institution.toLowerCase().includes(uni))
}

export function FounderEducation({ education, className }: FounderEducationProps) {
  const sortedEducation = [...education].sort((a, b) => 
    new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
  )

  if (!education || education.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Card className={clsx(
          "border border-slate-200 bg-white/90 backdrop-blur-sm shadow-none hover:shadow-md transition-shadow duration-200",
          className
        )}>
          <CardHeader className="border-b border-slate-100 pb-4">
            <div className="flex items-center gap-3">
              <div className="rounded-2xl border border-slate-200 bg-slate-50 p-2">
                <GraduationCap className="size-4 text-slate-600" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-slate-900">
                  Education
                </CardTitle>
                <p className="mt-0.5 text-xs text-slate-500">
                  Academic background
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="py-8 text-center">
              <div className="mx-auto mb-4 flex size-16 items-center justify-center rounded-2xl border border-slate-200 bg-slate-50">
                <GraduationCap className="size-8 text-slate-400" />
              </div>
              <p className="text-sm text-slate-500">No education data available</p>
              <p className="mt-1 text-xs text-slate-400">Add founder education to see academic background</p>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.5 }}
    >
      <Card className={clsx(
        "border border-slate-200 bg-white/90 backdrop-blur-sm shadow-none hover:shadow-md transition-shadow duration-200",
        className
      )}>
        <CardHeader className="border-b border-slate-100 pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="rounded-2xl border border-slate-200 bg-slate-50 p-2">
                <GraduationCap className="size-4 text-slate-600" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-slate-900">
                  Education
                </CardTitle>
                <p className="mt-0.5 text-xs text-slate-500">
                  Academic background
                </p>
              </div>
            </div>
            
            <Badge variant="outline" className="border-slate-200 text-xs text-slate-600">
              {education.length} degree{education.length !== 1 ? 's' : ''}
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent className="p-6">
          <div className="space-y-4">
            {sortedEducation.map((edu, index) => {
              const Icon = getDegreeIcon(edu.type)
              const isTop = isTopUniversity(edu.institution)
              
              return (
                <motion.div
                  key={edu.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                  className="rounded-2xl border border-slate-200 bg-slate-50 p-4 transition-colors duration-200 hover:bg-slate-100"
                >
                  <div className="flex items-start gap-4">
                    {/* Degree Icon */}
                    <div className={clsx(
                      "p-3 rounded-2xl border flex items-center justify-center flex-shrink-0",
                      getDegreeColor(edu.type)
                    )}>
                      <Icon className="size-5" />
                    </div>
                    
                    {/* Education Details */}
                    <div className="min-w-0 flex-1">
                      <div className="mb-2 flex items-start justify-between">
                        <div className="min-w-0 flex-1">
                          <h3 className="truncate text-sm font-semibold text-slate-900">
                            {edu.degree}
                          </h3>
                          <p className="text-sm font-medium text-slate-600">
                            {edu.major}
                          </p>
                        </div>
                        
                        <div className="ml-2 flex shrink-0 items-center gap-2">
                          <Badge 
                            variant="outline" 
                            className="rounded-full border-slate-200 bg-white px-2 py-1 text-xs text-slate-600"
                          >
                            {getDegreeText(edu.type)}
                          </Badge>
                          
                          {isTop && (
                            <Badge 
                              className="rounded-full border-blue-200 bg-blue-50 px-2 py-1 text-xs text-blue-700"
                            >
                              Top University
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      {/* Institution */}
                      <div className="mb-2 flex items-center gap-2 text-sm text-slate-700">
                        <Building2 className="size-3 text-slate-500" />
                        <span className="font-medium">{edu.institution}</span>
                      </div>
                      
                      {/* Location and Dates */}
                      <div className="flex flex-wrap items-center gap-3 text-xs text-slate-500">
                        {edu.location && (
                          <div className="flex items-center gap-1">
                            <MapPin className="size-3" />
                            <span>{edu.location}</span>
                          </div>
                        )}
                        
                        <div className="flex items-center gap-1">
                          <Calendar className="size-3" />
                          <span>
                            {formatDate(edu.startDate)} – {edu.endDate ? formatDate(edu.endDate) : 'Present'}
                          </span>
                        </div>
                        
                        {edu.gpa && (
                          <div className="flex items-center gap-1">
                            <Award className="size-3" />
                            <span>GPA: {edu.gpa}</span>
                          </div>
                        )}
                      </div>
                      
                      {/* Honors */}
                      {edu.honors && edu.honors.length > 0 && (
                        <div className="mt-3 border-t border-slate-200 pt-3">
                          <div className="flex flex-wrap gap-1">
                            {edu.honors.map((honor, honorIndex) => (
                              <Badge 
                                key={honorIndex}
                                variant="outline" 
                                className="rounded-full border-slate-200 bg-white px-2 py-0.5 text-xs text-slate-600"
                              >
                                {honor}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

export default FounderEducation
