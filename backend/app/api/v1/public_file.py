"""
Public file API endpoints for shared form submission flow.

This module handles file upload and download operations for public users
submitting forms through shared tokens. All operations require proper
authentication and access control.
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, status
from pydantic import BaseModel, Field

from app.core.logging import get_logger
from app.dependencies.auth import get_current_public_user
from app.services.factory import (
    get_file_service,
    get_form_service,
    get_public_submission_service,
)
from app.services.file.interface import FileServiceInterface
from app.services.form.interfaces import FormServiceInterface
from app.services.public_submission.service import PublicSubmissionService

logger = get_logger(__name__)

router = APIRouter(prefix="/public/file", tags=["Public Files"])


class PresignUploadRequest(BaseModel):
    """Request model for generating presigned upload URL."""

    submission_id: str = Field(..., description="ID of the submission")
    question_id: str = Field(..., description="ID of the question")
    original_filename: str = Field(..., description="Original filename")
    mime_type: str = Field(..., description="MIME type of the file")
    file_size: int = Field(..., description="File size in bytes")


class PresignUploadResponse(BaseModel):
    """Response model for presigned upload URL."""

    presigned_url: str = Field(..., description="Presigned URL for upload")
    s3_key: str = Field(..., description="S3 key for the file")
    file_id: str = Field(..., description="ID of the file record")
    expires_in: int = Field(..., description="URL expiration time in seconds")
    upload_fields: Dict[str, str] = Field(..., description="Additional upload fields")


class ConfirmUploadRequest(BaseModel):
    """Request model for confirming file upload."""

    file_id: str = Field(..., description="ID of the file record")
    checksum: Optional[str] = Field(None, description="Optional file checksum")


class ConfirmUploadResponse(BaseModel):
    """Response model for upload confirmation."""

    file_id: str = Field(..., description="ID of the file record")
    s3_key: str = Field(..., description="S3 key for the file")
    status: str = Field(..., description="File status")
    message: str = Field(..., description="Confirmation message")


class PresignDownloadRequest(BaseModel):
    """Request model for generating presigned download URL."""

    file_id: str = Field(..., description="ID of the file")


class PresignDownloadResponse(BaseModel):
    """Response model for presigned download URL."""

    presigned_url: str = Field(..., description="Presigned URL for download")
    filename: str = Field(..., description="Original filename")
    file_size: int = Field(..., description="File size in bytes")
    mime_type: str = Field(..., description="MIME type")
    expires_in: int = Field(..., description="URL expiration time in seconds")


class DeleteFileRequest(BaseModel):
    """Request model for deleting a file."""

    filePath: str = Field(..., description="Path or ID of the file to delete")


class FileInfo(BaseModel):
    """File information model."""

    id: str
    original_filename: str
    file_size: int
    display_size: str
    mime_type: str
    file_extension: str
    question_id: str
    uploaded_at: int
    uploaded_by: str
    download_count: int
    last_accessed_at: Optional[int] = None


@router.post("/presign-upload", response_model=PresignUploadResponse)
async def generate_presigned_upload_url(
    request: PresignUploadRequest,
    http_request: Request,
    current_user: dict = Depends(get_current_public_user),
    file_service: FileServiceInterface = Depends(get_file_service),
    public_submission_service: PublicSubmissionService = Depends(
        get_public_submission_service
    ),
    form_service: FormServiceInterface = Depends(get_form_service),
) -> PresignUploadResponse:
    """
    Generate a presigned URL for file upload.

    This endpoint validates the user's access to the submission,
    validates the file against question constraints, and generates
    a presigned S3 URL for direct upload.
    """
    try:
        # Validate user owns the submission and can edit
        submission = await public_submission_service.get_submission_by_id(
            request.submission_id
        )
        if not submission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Submission not found"
            )

        if submission.public_user_email != current_user["email"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this submission",
            )

        if not submission.can_edit():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Submission cannot be edited",
            )

        # Get form and question details for validation
        form = await form_service.get_form_with_details(str(submission.form_id))
        if not form:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Form not found"
            )

        form = form.model_dump(by_alias=True)

        # Find the question to get validation rules
        question = None
        for section in form.get("sections", []):
            for q in section.get("questions", []):
                if str(q.get("_id")) == request.question_id:
                    question = q
                    break
            if question:
                break

        if not question:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Question not found"
            )

        if question.get("type") != "file":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Question does not support file uploads",
            )

        # Validate file upload
        validation_result = await file_service.validate_file_upload(
            request.original_filename,
            request.mime_type,
            request.file_size,
            question.get("validation"),
        )

        if not validation_result["valid"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File validation failed: {'; '.join(validation_result['errors'])}",
            )

        # Generate presigned upload URL
        result = await file_service.generate_presigned_upload_url(
            submission_id=request.submission_id,
            question_id=request.question_id,
            original_filename=request.original_filename,
            mime_type=request.mime_type,
            file_size=request.file_size,
            uploaded_by_user_id=str(submission.public_user_id),
            uploaded_by_email=submission.public_user_email,
            org_id=str(submission.org_id),
            access_type="public_user",
        )

        logger.info(f"Generated presigned upload URL for user {current_user['email']}")

        return PresignUploadResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating presigned upload URL: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate upload URL",
        )


@router.post("/confirm-upload", response_model=ConfirmUploadResponse)
async def confirm_file_upload(
    request: ConfirmUploadRequest,
    current_user: dict = Depends(get_current_public_user),
    file_service: FileServiceInterface = Depends(get_file_service),
) -> ConfirmUploadResponse:
    """
    Confirm that a file has been successfully uploaded to S3.

    This endpoint verifies the file exists in S3 and updates
    the file record status to indicate successful upload.
    """
    try:
        # Get file record directly (skip access control for confirm upload)
        # We'll verify ownership separately since confirm upload needs to work on UPLOADING files
        file_record = await file_service.get_file_by_id(request.file_id)

        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="File not found"
            )

        # Verify the current user is the one who uploaded the file
        if file_record.uploaded_by_email != current_user["email"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied - you can only confirm your own uploads",
            )

        # Confirm upload
        updated_file = await file_service.confirm_file_upload(
            request.file_id, request.checksum
        )

        if not updated_file:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to confirm file upload",
            )

        logger.info(
            f"Confirmed file upload {request.file_id} for user {current_user['email']}"
        )

        return ConfirmUploadResponse(
            file_id=request.file_id,
            s3_key=updated_file.s3_key,
            status=updated_file.status,
            message="File upload confirmed successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error confirming file upload: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to confirm file upload",
        )


@router.post("/presign-download", response_model=PresignDownloadResponse)
async def generate_presigned_download_url(
    request: PresignDownloadRequest,
    http_request: Request,
    current_user: dict = Depends(get_current_public_user),
    file_service: FileServiceInterface = Depends(get_file_service),
) -> PresignDownloadResponse:
    """
    Generate a presigned URL for file download.

    This endpoint validates the user's access to the file and generates
    a presigned S3 URL for direct download.
    """
    try:
        # Get client IP and user agent for audit logging
        client_ip = http_request.client.host if http_request.client else None
        user_agent = http_request.headers.get("user-agent")

        # Generate presigned download URL
        result = await file_service.generate_presigned_download_url(
            file_id=request.file_id,
            accessed_by_email=current_user["email"],
            access_type="public_user",
            ip_address=client_ip,
            user_agent=user_agent,
        )

        logger.info(
            f"Generated presigned download URL for file {request.file_id} by user {current_user['email']}"
        )

        return PresignDownloadResponse(**result)

    except PermissionError:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Access denied to this file"
        )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        logger.error(f"Error generating presigned download URL: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate download URL",
        )


@router.post("/delete")
async def delete_file(
    request: DeleteFileRequest,
    http_request: Request,
    current_user: dict = Depends(get_current_public_user),
    file_service: FileServiceInterface = Depends(get_file_service),
) -> Dict[str, Any]:
    """
    Delete a file (soft delete).

    Only the original uploader can delete their files.
    Files cannot be deleted after submission is finalized.
    """
    try:
        # Get client IP and user agent for audit logging
        client_ip = http_request.client.host if http_request.client else None
        user_agent = http_request.headers.get("user-agent")

        # Check if filePath is a valid ObjectId or an S3 key
        file_id = None
        s3_key = None

        # Try to use filePath as ObjectId first
        try:
            from app.utils.common import ObjectIdField

            file_id = str(ObjectIdField(request.filePath))  # type: ignore
        except Exception as e:
            logger.error(f"Error deleting file: {str(e)}")
            # If not a valid ObjectId, treat it as S3 key
            s3_key = request.filePath

        # Delete file using either file_id or s3_key
        if file_id:
            success = await file_service.delete_file(
                file_id=file_id,
                deleted_by_email=current_user["email"],
                access_type="public_user",
                ip_address=client_ip,
                user_agent=user_agent,
            )
        else:
            success = await file_service.delete_file_by_s3_key(
                s3_key=s3_key or "",
                deleted_by_email=current_user["email"],
                access_type="public_user",
                ip_address=client_ip,
                user_agent=user_agent,
            )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Cannot delete this file"
            )

        # Get the actual file_id for response (needed for S3 key lookups)
        if not file_id and s3_key:
            # Find the file record to get the actual file_id for the response
            from app.models.file import SubmissionFile

            file_record = await SubmissionFile.find_one({"s3_key": s3_key})
            if file_record:
                file_id = str(file_record.id)

        logger.info(f"Deleted file {file_id or s3_key} by user {current_user['email']}")

        return {
            "message": "File deleted successfully",
            "file_id": file_id or "unknown",
            "filePath": request.filePath,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting file: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete file",
        )


@router.get("/submission/{submission_id}/files", response_model=List[FileInfo])
async def list_submission_files(
    submission_id: str,
    question_id: Optional[str] = None,
    current_user: dict = Depends(get_current_public_user),
    file_service: FileServiceInterface = Depends(get_file_service),
    public_submission_service: PublicSubmissionService = Depends(
        get_public_submission_service
    ),
) -> List[FileInfo]:
    """
    List files for a submission.

    Returns all files the current user has access to for the specified submission.
    """
    try:
        # Validate user owns the submission
        submission = await public_submission_service.get_submission_by_id(submission_id)
        if not submission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Submission not found"
            )

        if submission.public_user_email != current_user["email"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this submission",
            )

        # List files
        files = await file_service.list_submission_files(
            submission_id=submission_id,
            question_id=question_id,
            accessed_by_email=current_user["email"],
            access_type="public_user",
        )

        return [FileInfo(**file_data) for file_data in files]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing submission files: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list files",
        )
