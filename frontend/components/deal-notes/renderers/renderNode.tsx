"use client"

/**
 * Custom Node Renderer for Deal Notes
 * 
 * Renders paragraph and mention elements in Slate.js editor
 */

import React from 'react';
import { RenderElementProps } from 'slate-react';
import { MentionElement, ParagraphElement } from '@/lib/types/deal-notes';
import { isMentionElement } from '@/lib/utils/mentions';
import { MentionElement as MentionChip } from '../MentionElement';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface RenderNodeProps extends RenderElementProps {
  element: ParagraphElement | MentionElement;
}

export function renderNode(props: RenderNodeProps) {
  const { element, children, attributes } = props;

  if (isMentionElement(element)) {
    return <MentionChip attributes={attributes} element={element} children={children} />;
  }

  if (element.type === 'paragraph') {
    return (
      <p {...attributes} className="mb-2">
        {children}
      </p>
    );
  }

  // Add support for bulleted and numbered lists
  const el: any = element;
  if (el.type === 'bulleted-list') {
    return <ul {...attributes} className="list-disc pl-6 mb-2">{children}</ul>;
  }
  if (el.type === 'numbered-list') {
    return <ol {...attributes} className="list-decimal pl-6 mb-2">{children}</ol>;
  }
  if (el.type === 'list-item') {
    return <li {...attributes} className="mb-1">{children}</li>;
  }

  // Fallback: render unknown block as <p>, never <div>
  return <p {...attributes}>{children}</p>;
}

interface MentionComponentProps {
  element: MentionElement;
  attributes: any;
  children: React.ReactNode;
}

function MentionComponent({ element, attributes, children }: MentionComponentProps) {
  const initials = element.name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span
            {...attributes}
            className="inline-flex items-center gap-1 bg-muted/60 hover:bg-muted/80 rounded-md px-2 py-0.5 text-sm text-muted-foreground cursor-pointer transition-colors"
            contentEditable={false}
          >
            <Avatar className="w-4 h-4">
              <AvatarFallback className="text-xs bg-primary/10 text-primary">
                {initials}
              </AvatarFallback>
            </Avatar>
            <span>@{element.name}</span>
            {children}
          </span>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-sm">
            <div className="font-medium">{element.name}</div>
            <div className="text-muted-foreground">@{element.name.toLowerCase().replace(/\s+/g, '')}</div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
} 