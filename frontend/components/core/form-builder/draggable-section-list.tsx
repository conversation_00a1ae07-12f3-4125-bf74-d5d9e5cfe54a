"use client"

import React from 'react';
import { Section, Question } from '@/lib/types/form';
import { toast } from '@/components/ui/use-toast';
import FormAPI from '@/lib/api/form-api';
import { SectionCard } from './section-card';
import { SortableContextProvider, SortableItem } from './sortable-context';
import { useOrderManager } from '@/hooks/use-order-manager';
import { Button } from '@/components/ui/button';
import { Save, Undo } from 'lucide-react';
import { cn } from '@/lib/utils';
import { DragEndEvent } from '@dnd-kit/core';

interface DraggableSectionListProps {
  sections: Section[];
  activeSection: string | null;
  setActiveSection: (value: string | null) => void;
  updateSection: (index: number, section: Partial<Section>) => Promise<void>;
  deleteSection: (index: number) => void;
  updateQuestions: (sectionIndex: number, questions: Question[]) => void;
  onReorderSections: (reorderedSections: Section[]) => void;
}

export function DraggableSectionList({
  sections: initialSections,
  activeSection,
  setActiveSection,
  updateSection,
  deleteSection,
  updateQuestions,
  onReorderSections,
}: DraggableSectionListProps) {
  // Gather all questions from all sections for visibility conditions
  const allQuestions: Question[] = initialSections.flatMap(section =>
    section.questions || []
  );

  // Use our order manager hook
  const {
    items: sections,
    hasUnsavedChanges,
    isSaving,
    saveOrder,
    undoLastChange,
    updateItems,
  } = useOrderManager<Section>({
    items: initialSections,
    onSave: async (updatedSections) => {
      try {
        // Create reorder items array for drag-and-drop operations
        const reorderItems = updatedSections
          .filter(section => section._id || section.id)
          .map((section, index) => ({
            id: section._id || section.id!,
            order: index
          }));

        // Call the reorder API for drag-and-drop operations
        await FormAPI.reorderSections(reorderItems);

        // Update parent component
        onReorderSections(updatedSections);

        toast({
          title: "Section order updated",
          description: "The section order has been saved successfully.",
        });
    } catch (error) {
        console.error('Error saving section order:', error);
        throw error; // Propagate error to trigger error handling
      }
    },
    getItemId: (section) => {
      const id = section._id || section.id;
      if (!id) {
        console.warn('Section missing ID:', section);
        return `temp-${Math.random()}`;
      }
      return id;
    },
    updateOrder: (section, order) => ({ ...section, order }),
    onError: (error) => {
      console.error('Error in section order management:', error);
      toast({
        title: "Error saving section order",
        description: "There was an error saving the section order. Please try again.",
        variant: "destructive",
      });
    },
    resourceType: 'section',
    parentId: initialSections[0]?.form_id || '',
  });

  // Get section IDs for sortable context
  const sectionIds = sections.map(section => {
    const id = section._id || section.id;
    if (!id) {
      console.warn('Section missing ID:', section);
      return `temp-${Math.random()}`;
    }
    return id;
  });

  return (
    <div className="space-y-6">
      {/* Order management controls */}
      {(hasUnsavedChanges || isSaving) && (
        <div className="flex items-center gap-2 rounded-lg border border-border/50 bg-muted/30 p-2">
          <Button
            size="sm"
            variant="outline"
            onClick={undoLastChange}
            disabled={isSaving}
            className="gap-1.5"
          >
            <Undo className="size-4" />
            Undo
          </Button>
          <Button
            size="sm"
            onClick={saveOrder}
            disabled={isSaving}
            className={cn(
              "gap-1.5",
              isSaving ? "bg-primary/80" : "bg-primary hover:bg-primary/90"
            )}
          >
            <Save className="size-4" />
            {isSaving ? "Saving..." : "Save Order"}
          </Button>
        </div>
      )}

      {/* Sortable sections */}
      <SortableContextProvider
        items={sections}
        itemIds={sectionIds}
        onDragEnd={(event: DragEndEvent) => {
          const { active, over } = event;
          if (!over || active.id === over.id) return;

          const oldIndex = sections.findIndex(s => (s._id || s.id) === active.id);
          const newIndex = sections.findIndex(s => (s._id || s.id) === over.id);
          
          if (oldIndex === -1 || newIndex === -1) return;

          const newSections = [...sections];
          const [movedSection] = newSections.splice(oldIndex, 1);
          newSections.splice(newIndex, 0, movedSection);

          // Update items with new order
          updateItems(newSections);
        }}
        strategy="vertical"
          >
            {sections.map((section, index) => {
          const sectionId = section._id || section.id || `temp-section-${index}`;
              const isExpanded = activeSection === `section-${index}`;

              return (
            <SortableItem
                  key={sectionId}
              id={sectionId}
              disabled={isSaving}
                    >
                      <SectionCard
                        section={section}
                        form={{
                          _id: 'temp-form',
                          name: 'Temporary Form',
                          description: 'Temporary form for section editing',
                          status: 'draft',
                          sections: sections,
                          is_active: true,
                          version: 1,
                        }}
                        isActive={activeSection === sectionId}
                        isSelected={false}
                        onUpdate={async (updates) => {
                          try {
                            // Use the regular update API for non-order updates
                            await updateSection(index, updates);
                          } catch (err) {
                            console.error('Error updating section:', err);
                            toast({
                              title: 'Error',
                              description: 'Failed to update section',
                              variant: 'destructive'
                            });
                            throw err;
                          }
                        }}
                        onDelete={() => deleteSection(index)}
                        onDuplicate={() => {
                          // TODO: Implement section duplication
                          toast({
                            title: 'Not implemented',
                            description: 'Section duplication is not implemented yet',
                            variant: 'destructive'
                          });
                        }}
                        onToggleActive={() => {
                          setActiveSection(activeSection === sectionId ? null : sectionId);
                        }}
                        onSelect={() => {
                          // TODO: Implement section selection
                        }}
                      />
            </SortableItem>
              );
            })}
      </SortableContextProvider>
          </div>
  );
}
