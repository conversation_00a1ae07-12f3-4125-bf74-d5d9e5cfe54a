from typing import Any, Type, get_type_hints, get_origin, get_args, Union, Annotated
from bson import ObjectId

from app.utils.common import ObjectIdField
from app.models.form import QuestionType

def cast_value_to_type(value: Any, target_type: Type) -> Any:
    """
    Cast a value to the target type, handling special cases like ObjectId.
    This is a utility function that can be used across the application for consistent type casting.

    Args:
        value: The value to cast
        target_type: The target type to cast to

    Returns:
        The casted value, or the original value if casting fails
    """
    if value is None:
        return None

    # Handle Annotated types by extracting the base type
    if get_origin(target_type) is Annotated:
        # Get the base type (first argument) from Annotated
        target_type = get_args(target_type)[0]

    # Handle Optional types
    if get_origin(target_type) is Union:
        # Get the non-None type from Optional
        non_none_types = [t for t in get_args(target_type) if t is not type(None)]
        if non_none_types:
            target_type = non_none_types[0]

    # Handle special types
    if target_type == ObjectIdField:
        if isinstance(value, str):
            try:
                return ObjectId(value)
            except Exception:
                return value
        elif isinstance(value, ObjectId):
            return value
        # Handle lists of ObjectIds
        elif isinstance(value, list):
            try:
                return [ObjectId(item) if isinstance(item, str) else item for item in value]
            except Exception:
                return value
        return value
    elif target_type == QuestionType:
        try:
            return QuestionType(value)
        except ValueError:
            return value
    elif target_type is bool:
        return bool(value)
    elif target_type is int:
        try:
            return int(value)
        except (ValueError, TypeError):
            return value
    elif target_type is float:
        try:
            return float(value)
        except (ValueError, TypeError):
            return value
    elif target_type is str:
        return str(value)
    elif target_type is list:
        return list(value) if isinstance(value, (list, tuple)) else value
    elif target_type is dict:
        return dict(value) if isinstance(value, dict) else value

    return value

def update_model_with_type_casting(model: Any, update: dict) -> Any:
    """
    Update a model's attributes with type casting.
    This utility function handles type casting for all fields during updates.

    Args:
        model: The model instance to update
        update: Dictionary of updates to apply with type casting

    Returns:
        The updated model instance
    """
    # Get the field type hints from the model
    field_types = get_type_hints(type(model))

    for key, value in update.items():
        if key in field_types:
            # Cast the value to the correct type
            casted_value = cast_value_to_type(value, field_types[key])
            setattr(model, key, casted_value)
        else:
            # For unknown fields, set as is
            setattr(model, key, value)

    # Return the updated model
    return model