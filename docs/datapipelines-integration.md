# TractionX Data Pipelines Integration Guide

## Data Flow and Integration Patterns

### Overview
This document details how data flows through the TractionX Data Pipelines system, integration patterns with external services, and how to extend the system with new data sources and pipelines.

## Data Flow Architecture

### 1. Data Ingestion Patterns

#### Form Submission Flow
```
Frontend Form → Backend API → Pipeline Trigger → RQ Job Queue
     ↓              ↓              ↓              ↓
User Input → Validation → Job Creation → Background Processing
```

**Process Steps:**
1. User submits deal form through frontend
2. Backend validates and stores form submission
3. Backend triggers pipeline via API call
4. Pipeline service queues enrichment jobs
5. Workers process jobs asynchronously

#### Webhook Integration Flow
```
External Service → Webhook Endpoint → Data Extraction → Job Queue
     ↓                   ↓               ↓              ↓
Clay/PDL/etc → Signature Verify → Parse Payload → Queue Jobs
```

**Process Steps:**
1. External service sends webhook notification
2. Webhook endpoint verifies signature
3. System extracts relevant data from payload
4. Appropriate pipeline jobs are queued
5. Workers process enrichment data

#### API Trigger Flow
```
External System → API Endpoint → Job Validation → Queue Management
     ↓               ↓              ↓              ↓
Manual/Scheduled → Authentication → Parameter Check → Job Dispatch
```

### 2. Enrichment Processing Flow

#### Sequential Processing Pattern
```
Company Data → Company Enrichment → Founder Enrichment → News Aggregation
     ↓               ↓                    ↓                    ↓
Base Info → Clay API Call → PDL API Call → Bing News API → Final Merge
```

#### Parallel Processing Pattern
```
Input Data
    ├── Company Enrichment (Clay)
    ├── Founder Enrichment (PDL)  
    ├── News Aggregation (Bing)
    └── Embedding Generation (OpenAI)
         ↓
    ETL Merge Process
         ↓
    Canonical Record
```

### 3. Storage Integration Flow

#### Multi-Storage Strategy
```
Raw Data → S3 Object Storage (Archive)
    ↓
Processed Data → RDS Database (Queryable)
    ↓
Text Content → OpenAI Embeddings → Qdrant Vector DB (Search)
    ↓
Job Status → Redis Cache (Temporary)
```

## External Service Integrations

### 1. Clay API Integration

#### Purpose
Clay provides comprehensive company data enrichment including:
- Company profiles and descriptions
- Financial information and funding history
- Technology stack and employee data
- Market positioning and competitive analysis

#### Integration Pattern
```python
class ClayAPIClient:
    async def enrich_company(self, company_name: str, domain: str = None):
        """
        Enrich company data using Clay API
        
        Args:
            company_name: Company name for enrichment
            domain: Company domain (optional)
            
        Returns:
            ClayCompanyData: Enriched company information
        """
        search_params = {"name": company_name}
        if domain:
            search_params["domain"] = domain
            
        response = await self.post("/companies/search", json=search_params)
        return ClayCompanyData.parse_obj(response.json())
```

#### Data Mapping
```python
# Clay API Response → Internal Model
clay_response = {
    "company_name": "Acme Corp",
    "domain": "acme.com",
    "industry": "Technology",
    "employee_count": 150,
    "funding_total": 5000000,
    "technologies": ["React", "Python", "AWS"]
}

# Mapped to CompanyEnrichmentData
company_data = CompanyEnrichmentData(
    company_id=company_id,
    org_id=org_id,
    clay_data=ClayCompanyData(**clay_response),
    enrichment_status={"clay": "success"},
    last_enriched=datetime.utcnow()
)
```

### 2. People Data Labs (PDL) Integration

#### Purpose
PDL provides founder and employee enrichment including:
- Professional backgrounds and experience
- Education and skill profiles
- Social media and contact information
- Career progression and achievements

#### Integration Pattern
```python
class PDLAPIClient:
    async def enrich_person(self, email: str = None, linkedin_url: str = None):
        """
        Enrich person data using PDL API
        
        Args:
            email: Person's email address
            linkedin_url: LinkedIn profile URL
            
        Returns:
            PDLFounderData: Enriched founder information
        """
        search_params = {}
        if email:
            search_params["email"] = email
        if linkedin_url:
            search_params["linkedin_url"] = linkedin_url
            
        response = await self.post("/person/enrich", json=search_params)
        return PDLFounderData.parse_obj(response.json())
```

### 3. Bing News API Integration

#### Purpose
Bing News provides company-related news and market intelligence:
- Recent news articles and press releases
- Market sentiment and analysis
- Industry trends and competitive intelligence
- Event detection and timeline tracking

#### Integration Pattern
```python
class BingNewsClient:
    async def search_company_news(self, company_name: str, days_back: int = 30):
        """
        Search for company-related news
        
        Args:
            company_name: Company name to search for
            days_back: Number of days to look back
            
        Returns:
            List[NewsArticle]: Relevant news articles
        """
        query = f'"{company_name}" AND (funding OR investment OR startup)'
        params = {
            "q": query,
            "count": 50,
            "freshness": f"Day{days_back}",
            "sortBy": "Date"
        }
        
        response = await self.get("/news/search", params=params)
        return [NewsArticle.parse_obj(article) for article in response.json()["value"]]
```

### 4. OpenAI Integration

#### Purpose
OpenAI provides text embedding generation for semantic search:
- Convert text content to vector embeddings
- Enable similarity search across all data
- Support AI-powered matching and recommendations
- Facilitate semantic analysis and clustering

#### Integration Pattern
```python
class OpenAIClient:
    async def generate_embeddings(self, texts: List[str], model: str = "text-embedding-ada-002"):
        """
        Generate embeddings for text content
        
        Args:
            texts: List of text strings to embed
            model: OpenAI embedding model to use
            
        Returns:
            List[TextEmbedding]: Generated embeddings
        """
        response = await self.post("/embeddings", json={
            "input": texts,
            "model": model
        })
        
        embeddings = []
        for i, embedding_data in enumerate(response.json()["data"]):
            embeddings.append(TextEmbedding(
                text=texts[i],
                vector=embedding_data["embedding"],
                model=model,
                dimensions=len(embedding_data["embedding"])
            ))
        
        return embeddings
```

## ETL and Data Merging

### 1. Canonical Data Model

#### Purpose
The ETL merge process creates a single, authoritative record for each company by combining data from multiple sources with defined precedence rules.

#### Merge Priority Rules
```python
# Data source priority (highest to lowest)
MERGE_PRIORITY = [
    EnrichmentSource.FORM_SUBMISSION,  # User-provided data (highest priority)
    EnrichmentSource.MANUAL,           # Manual corrections
    EnrichmentSource.CLAY,             # Clay API data
    EnrichmentSource.PDL,              # PDL API data
    EnrichmentSource.BING_NEWS,        # News data
    EnrichmentSource.OPENAI            # Generated content (lowest priority)
]
```

#### Merge Algorithm
```python
async def merge_company_data(company_id: str) -> CanonicalCompanyRecord:
    """
    Merge company data from all sources into canonical record
    
    Args:
        company_id: Company identifier
        
    Returns:
        CanonicalCompanyRecord: Merged company data
    """
    # Fetch data from all sources
    sources = await fetch_all_company_data(company_id)
    
    # Initialize canonical record
    canonical = CanonicalCompanyRecord(company_id=company_id)
    
    # Merge fields by priority
    for field_name in MERGEABLE_FIELDS:
        for source_priority in MERGE_PRIORITY:
            if source_priority in sources and hasattr(sources[source_priority], field_name):
                value = getattr(sources[source_priority], field_name)
                if value is not None:
                    setattr(canonical, field_name, value)
                    canonical.source_tracking[field_name] = source_priority
                    break  # Use first non-null value by priority
    
    return canonical
```

### 2. Source Tracking

#### Purpose
Track the origin of each data field for audit, debugging, and data quality purposes.

#### Implementation
```python
class CanonicalCompanyRecord(TractionXModel):
    # Core company data
    company_id: str
    company_name: Optional[str] = None
    domain: Optional[str] = None
    industry: Optional[str] = None
    
    # Source tracking for each field
    source_tracking: Dict[str, EnrichmentSource] = Field(default_factory=dict)
    
    # Confidence scores
    confidence_scores: Dict[str, float] = Field(default_factory=dict)
    
    # Last update timestamps
    last_updated: Dict[str, datetime] = Field(default_factory=dict)
```

### 3. Conflict Resolution

#### Strategies
1. **Priority-Based**: Use source priority to resolve conflicts
2. **Confidence-Based**: Use confidence scores when available
3. **Recency-Based**: Prefer more recent data for time-sensitive fields
4. **Manual Review**: Flag conflicts for human review

## Adding New Pipelines

### 1. Pipeline Development Pattern

#### Step 1: Define Data Models
```python
# models/new_source.py
class NewSourceData(TractionXModel):
    """Data model for new enrichment source"""
    source_id: str
    enriched_data: Dict[str, Any]
    confidence_score: float
    last_updated: datetime
```

#### Step 2: Implement Pipeline Class
```python
# pipelines/new_source.py
class NewSourcePipeline(BasePipeline):
    """Pipeline for new data source enrichment"""
    
    async def _initialize_pipeline(self) -> None:
        """Initialize API clients and resources"""
        self.api_client = NewSourceAPIClient(api_key=settings.NEW_SOURCE_API_KEY)
        self.storage = await get_storage_service()
    
    async def _validate_input(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """Validate input data"""
        required_fields = ["company_id", "company_name"]
        for field in required_fields:
            if field not in input_data:
                return ProcessingResult.error_result(f"Missing required field: {field}")
        return ProcessingResult.success_result({})
    
    async def _process_data(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """Core enrichment logic"""
        try:
            # Call external API
            enrichment_data = await self.api_client.enrich_company(
                input_data["company_name"]
            )
            
            # Store raw data
            await self.storage.put_object(
                f"new_source/{input_data['company_id']}/raw.json",
                enrichment_data.model_dump_json()
            )
            
            # Return processed data
            return ProcessingResult.success_result(
                data={"new_source_data": enrichment_data},
                source=EnrichmentSource.NEW_SOURCE
            )
            
        except Exception as e:
            return ProcessingResult.error_result(str(e))
```

#### Step 3: Create Task Function
```python
# tasks/new_source_enrichment.py
async def enrich_new_source_data(job_data: Dict[str, Any]) -> Dict[str, Any]:
    """Task function for new source enrichment"""
    pipeline = NewSourcePipeline("new_source_enrichment")
    
    try:
        await pipeline.initialize()
        result = await pipeline.process(job_data)
        
        if result.success:
            # Store in database
            await store_enrichment_result(job_data["company_id"], result.data)
            
        return format_job_result(result)
        
    finally:
        await pipeline.cleanup()

def enrich_new_source_data_sync(job_data: Dict[str, Any]) -> Dict[str, Any]:
    """Synchronous wrapper for RQ"""
    return asyncio.run(enrich_new_source_data(job_data))
```

#### Step 4: Register Pipeline
```python
# Update api/pipelines.py
elif pipeline_type == "new_source":
    job_id = await queue_client.enqueue_job(
        queue_name=queue_name,
        job_function="tasks.new_source_enrichment.enrich_new_source_data_sync",
        job_data=job_data,
        timeout=settings.PIPELINE_TIMEOUT
    )
```

### 2. Testing New Pipelines

#### Unit Tests
```python
# tests/test_new_source_pipeline.py
@pytest.mark.asyncio
async def test_new_source_pipeline():
    """Test new source pipeline"""
    pipeline = NewSourcePipeline("test_new_source")
    
    input_data = {
        "company_id": "test_company",
        "company_name": "Test Company"
    }
    
    result = await pipeline.process(input_data)
    
    assert result.success
    assert "new_source_data" in result.data
```

#### Integration Tests
```python
@pytest.mark.asyncio
async def test_new_source_task():
    """Test new source task function"""
    job_data = {
        "job_id": "test_job",
        "company_id": "test_company",
        "org_id": "test_org",
        "company_name": "Test Company"
    }
    
    result = await enrich_new_source_data(job_data)
    
    assert result["success"]
    assert result["company_id"] == "test_company"
```

## Best Practices

### 1. Error Handling
- Implement comprehensive error handling in all pipeline stages
- Use structured logging for debugging and monitoring
- Implement retry logic with exponential backoff
- Gracefully handle API rate limits and timeouts

### 2. Performance Optimization
- Use async/await for I/O operations
- Implement connection pooling for database operations
- Cache frequently accessed data
- Batch operations when possible

### 3. Security
- Validate all input data
- Use secure API key storage
- Implement webhook signature verification
- Audit all data access and modifications

### 4. Monitoring
- Track pipeline success/failure rates
- Monitor processing times and queue lengths
- Set up alerts for critical failures
- Implement health checks for all external dependencies

This integration guide provides the foundation for understanding data flow, extending the system with new sources, and maintaining high-quality data processing pipelines.
