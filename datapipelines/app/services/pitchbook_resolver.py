"""
PitchBook URL Resolver Service for TractionX Data Pipeline Service.

This module provides automatic resolution of company PitchBook URLs using:
1. Serper API for Google search
2. LLM agent for link selection
3. BrightData for company data scraping
"""

import time
from typing import Any, Dict, Optional

import httpx
from app.clients.together_client import TogetherClient
from app.configs import get_logger, settings
from app.models.pitchbook import PitchBookResolverInput, PitchBookResolverOutput
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class PitchBookResolverService:
    """
    PitchBook URL resolver service.

    Handles:
    - Serper API search for PitchBook URLs
    - LLM-based link selection and validation
    - BrightData scraping trigger
    - S3 storage of raw data
    """

    def __init__(self):
        self.logger = get_logger(f"{__name__}.PitchBookResolverService")

        # API clients
        self.serper_client: Optional[httpx.AsyncClient] = None
        self.brightdata_client: Optional[httpx.AsyncClient] = None
        self.together_client: Optional[TogetherClient] = None

        # Configuration
        self.serper_api_key = settings.SERPAPI_KEY
        self.brightdata_api_key = settings.BRIGHTDATA_API_KEY
        self.together_api_key = settings.TOGETHER_API_KEY
        self.brightdata_dataset_id = "gd_m4ijiqfp2n9oe3oluj"  # PitchBook dataset ID

        # S3 storage
        self.s3_storage: Optional[S3Storage] = None

        # Timeouts and retries
        self.timeout = 30.0
        self.max_retries = 3
        self.retry_delay = 2.0

    async def initialize(self) -> None:
        """Initialize the PitchBook resolver service."""
        try:
            self.logger.info("Starting PitchBook resolver service initialization")

            # Check API keys
            if not self.serper_api_key:
                raise ValueError("SERPAPI_KEY not configured")
            if not self.brightdata_api_key:
                raise ValueError("BRIGHTDATA_API_KEY not configured")
            if not self.together_api_key:
                raise ValueError("TOGETHER_API_KEY not configured")

            self.logger.info("API keys validated successfully")

            # Initialize HTTP clients
            self.serper_client = httpx.AsyncClient(
                base_url="https://google.serper.dev",
                headers={
                    "X-API-KEY": self.serper_api_key,
                    "Content-Type": "application/json",
                },
                timeout=self.timeout,
            )

            self.brightdata_client = httpx.AsyncClient(
                base_url="https://api.brightdata.com",
                headers={
                    "Authorization": f"Bearer {self.brightdata_api_key}",
                    "Content-Type": "application/json",
                },
                timeout=self.timeout,
            )

            # Initialize Together AI client
            self.together_client = TogetherClient()
            await self.together_client.initialize()

            # Initialize S3 storage
            self.s3_storage = S3Storage()
            await self.s3_storage.initialize()

            self.logger.info("PitchBook resolver service initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize PitchBook resolver service: {e}")
            raise

    async def cleanup(self) -> None:
        """Clean up service resources."""
        try:
            if self.serper_client:
                await self.serper_client.aclose()
            if self.brightdata_client:
                await self.brightdata_client.aclose()
            if self.together_client:
                await self.together_client.cleanup()
            if self.s3_storage:
                await self.s3_storage.cleanup()
            self.logger.info("PitchBook resolver service cleaned up")
        except Exception as e:
            self.logger.warning(f"Error cleaning up PitchBook resolver service: {e}")

    async def resolve_pitchbook_url(
        self, input_data: PitchBookResolverInput
    ) -> PitchBookResolverOutput:
        """
        Resolve PitchBook URL for a company.

        Args:
            input_data: Input data containing company domain and description

        Returns:
            Resolution result with status and data
        """
        start_time = time.time()

        try:
            self.logger.info(
                f"Starting PitchBook URL resolution for {input_data.company_domain}"
            )

            # Step 1: Search for PitchBook URLs using Serper
            search_results = await self._search_pitchbook_urls(
                input_data.company_domain
            )
            if not search_results:
                return PitchBookResolverOutput(
                    status="no_match",
                    error_message="No PitchBook URLs found in search results",
                    processing_time=time.time() - start_time,
                    pitchbook_url=None,
                    brightdata_snapshot_id=None,
                    llm_decision=None,
                    serper_results_count=0,
                    confidence_score=0.0,
                )

            # Step 2: Use LLM to select the best PitchBook URL
            selected_url = await self._select_pitchbook_url_with_llm(
                input_data.company_domain,
                input_data.company_description or "",
                search_results,
            )

            if not selected_url:
                return PitchBookResolverOutput(
                    status="no_match",
                    error_message="LLM could not select a valid PitchBook URL",
                    processing_time=time.time() - start_time,
                    pitchbook_url=None,
                    brightdata_snapshot_id=None,
                    llm_decision=None,
                    serper_results_count=len(search_results.get("organic", [])),
                    confidence_score=0.0,
                )

            # Step 3: Trigger BrightData scraping
            self.logger.info(
                f"About to trigger BrightData scraping for URL: {selected_url}"
            )
            snapshot_result = await self._trigger_brightdata_scrape(selected_url)
            self.logger.info(f"BrightData trigger result: {snapshot_result}")

            if snapshot_result.get("success"):
                return PitchBookResolverOutput(
                    status="triggered",
                    pitchbook_url=selected_url,
                    brightdata_snapshot_id=snapshot_result.get("snapshot_id"),
                    llm_decision="URL selected and BrightData scraping triggered",
                    serper_results_count=len(search_results.get("organic", [])),
                    confidence_score=0.8,
                    error_message=None,
                    processing_time=time.time() - start_time,
                )
            else:
                return PitchBookResolverOutput(
                    status="completed",
                    pitchbook_url=selected_url,
                    error_message="BrightData scraping failed to trigger",
                    llm_decision="URL selected but BrightData failed",
                    serper_results_count=len(search_results.get("organic", [])),
                    processing_time=time.time() - start_time,
                    brightdata_snapshot_id=None,
                    confidence_score=0.0,
                )

        except Exception as e:
            error_msg = f"PitchBook URL resolution failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            return PitchBookResolverOutput(
                status="error",
                error_message=error_msg,
                processing_time=time.time() - start_time,
                pitchbook_url=None,
                brightdata_snapshot_id=None,
                llm_decision=None,
                serper_results_count=0,
                confidence_score=0.0,
            )

    async def _search_pitchbook_urls(self, company_domain: str) -> Dict[str, Any]:
        """Search for PitchBook URLs using Serper API."""
        try:
            # Construct search query
            search_query = f"site:pitchbook.com {company_domain}"

            self.logger.info(f"Searching for PitchBook URLs with query: {search_query}")

            # Perform search
            response = await self.serper_client.post(  # type: ignore
                "/search",
                json={"q": search_query, "num": 10},
            )
            response.raise_for_status()

            search_results = response.json()

            if not search_results or not search_results.get("organic"):
                self.logger.warning("No search results found")
                return {}

            self.logger.info(f"Found {len(search_results['organic'])} search results")
            return search_results

        except Exception as e:
            self.logger.error(f"Error searching for PitchBook URLs: {e}")
            return {}

    async def _select_pitchbook_url_with_llm(
        self, domain: str, description: str, serper_response: Dict[str, Any]
    ) -> Optional[str]:
        """Use LLM to select the best PitchBook URL."""
        try:
            if not serper_response.get("organic"):
                return None

            # Prepare context for LLM
            context = f"""
            Company Domain: {domain}
            Company Description: {description or "Not provided"}
            
            Found PitchBook URLs:
            """

            for i, result in enumerate(
                serper_response["organic"][:5]
            ):  # Limit to top 5 results
                context += f"""
                {i + 1}. URL: {result.get("link", "")}
                   Title: {result.get("title", "")}
                   Snippet: {result.get("snippet", "")}
                """

            context += """
            
            Please select the best PitchBook URL for this company. Consider:
            1. URL should be a company profile page (not search results, blog posts, etc.)
            2. URL should match the company domain/name
            3. URL should be from the main PitchBook domain
            
            Respond with only the selected URL, or "NONE" if no suitable URL found.
            """

            # Get LLM response
            response = await self.together_client.create_chat_completion(  # type: ignore
                messages=[
                    {
                        "role": "system",
                        "content": "You are a helpful assistant that selects the most relevant PitchBook URLs for companies.",
                    },
                    {"role": "user", "content": context},
                ],
                model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
                temperature=0.1,
            )

            logger.info(f"LLM response for PitchBook URL selection: {response}")

            # Extract the response text from the LLM response
            if isinstance(response, dict):
                # Try to get content directly first (our wrapper format)
                selected_url = response.get("content", "").strip()

                # If not found, try the OpenAI format
                if not selected_url:
                    selected_url = (
                        response.get("choices", [{}])[0]
                        .get("message", {})
                        .get("content", "")
                        .strip()
                    )
            else:
                selected_url = str(response).strip()

            # Validate response
            if selected_url.upper() == "NONE" or not selected_url.startswith("http"):
                self.logger.info(f"LLM selected no valid URL: '{selected_url}'")
                return None

            self.logger.info(f"LLM selected URL: {selected_url}")
            return selected_url

        except Exception as e:
            self.logger.error(f"Error selecting PitchBook URL with LLM: {e}")
            return None

    async def _trigger_brightdata_scrape(self, pitchbook_url: str) -> Dict[str, Any]:
        """Trigger BrightData scraping for the selected PitchBook URL."""
        try:
            self.logger.info(f"Triggering BrightData scraping for: {pitchbook_url}")

            if not self.brightdata_client:
                error_msg = "BrightData client not initialized"
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg}

            # Create BrightData scraping job - using the correct API format
            payload = [{"url": pitchbook_url}]

            response = await self.brightdata_client.post(  # type: ignore
                "/datasets/v3/trigger",
                params={
                    "dataset_id": self.brightdata_dataset_id,
                    "include_errors": "true",
                },
                json=payload,
            )
            response.raise_for_status()

            result = response.json()
            self.logger.info(f"BrightData API response: {result}")

            # Extract snapshot ID from the response
            snapshot_id = None
            if isinstance(result, list) and len(result) > 0:
                snapshot_id = result[0].get("snapshot_id") or result[0].get("id")
            elif isinstance(result, dict):
                snapshot_id = result.get("snapshot_id") or result.get("id")

            if snapshot_id:
                self.logger.info(
                    f"BrightData scraping triggered with snapshot ID: {snapshot_id}"
                )
                return {"success": True, "snapshot_id": snapshot_id}
            else:
                self.logger.error(
                    f"Failed to create BrightData snapshot. Response: {result}"
                )
                return {"success": False, "error": "No snapshot ID returned"}

        except Exception as e:
            error_msg = f"Error triggering BrightData scraping: {e}"
            self.logger.error(error_msg, exc_info=True)
            return {"success": False, "error": error_msg}

    async def _download_brightdata_snapshot(self, snapshot_id: str) -> Dict[str, Any]:
        """Download BrightData snapshot data."""
        try:
            self.logger.info(f"Downloading BrightData snapshot: {snapshot_id}")

            # Download snapshot data using the correct API format
            response = await self.brightdata_client.get(  # type: ignore
                f"/datasets/v3/snapshot/{snapshot_id}",
                params={"format": "json"},
            )
            response.raise_for_status()

            data = response.json()

            if data:
                self.logger.info(
                    f"Successfully downloaded snapshot data for {snapshot_id}"
                )
                return {"success": True, "data": data}
            else:
                self.logger.error(f"Failed to download snapshot data for {snapshot_id}")
                return {"success": False, "error": "No data returned from BrightData"}

        except Exception as e:
            error_msg = f"Error downloading BrightData snapshot: {e}"
            self.logger.error(error_msg)
            return {"success": False, "error": error_msg}


# Global service instance
_pitchbook_resolver_service: Optional[PitchBookResolverService] = None


async def get_pitchbook_resolver_service() -> PitchBookResolverService:
    """Get or create the global PitchBook resolver service instance."""
    global _pitchbook_resolver_service

    if _pitchbook_resolver_service is None:
        _pitchbook_resolver_service = PitchBookResolverService()
        await _pitchbook_resolver_service.initialize()

    return _pitchbook_resolver_service
