import { useState, useCallback } from 'react';

export function useFormValidation(form: any, answers: Record<string, any>) {
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateField = useCallback((questionId: string, value: any) => {
    if (!form) return null;

    const question = form.sections
      ?.flatMap((s: any) => s.questions || [])
      ?.find((q: any) => q._id === questionId);

    if (!question) return null;

    // Required validation
    if (question.required && (value === undefined || value === null || value === '')) {
      return `${question.label} is required`;
    }

    // Type-specific validation
    if (value !== undefined && value !== null && value !== '') {
      switch (question.type) {
        case 'NUMBER':
          if (isNaN(Number(value))) {
            return 'Please enter a valid number';
          }
          if (question.validation?.min !== undefined && Number(value) < question.validation.min) {
            return `Value must be at least ${question.validation.min}`;
          }
          if (question.validation?.max !== undefined && Number(value) > question.validation.max) {
            return `Value must be at most ${question.validation.max}`;
          }
          break;

        case 'SHORT_TEXT':
        case 'LONG_TEXT':
          if (question.validation?.min && value.length < question.validation.min) {
            return `Must be at least ${question.validation.min} characters`;
          }
          if (question.validation?.max && value.length > question.validation.max) {
            return `Must be at most ${question.validation.max} characters`;
          }
          if (question.validation?.regex) {
            const regex = new RegExp(question.validation.regex);
            if (!regex.test(value)) {
              return question.validation.message || 'Invalid format';
            }
          }
          break;

        case 'MULTI_SELECT':
          if (Array.isArray(value) && question.validation?.min && value.length < question.validation.min) {
            return `Please select at least ${question.validation.min} options`;
          }
          if (Array.isArray(value) && question.validation?.max && value.length > question.validation.max) {
            return `Please select at most ${question.validation.max} options`;
          }
          break;
      }
    }

    return null;
  }, [form]);

  const validateForm = useCallback(() => {
    if (!form?.sections) return {};

    const newErrors: Record<string, string> = {};
    
    form.sections.forEach((section: any) => {
      section.questions?.forEach((question: any) => {
        const error = validateField(question._id, answers[question._id]);
        if (error) {
          newErrors[question._id] = error;
        }
      });
    });

    setErrors(newErrors);
    return newErrors;
  }, [form, answers, validateField]);

  return {
    errors,
    validateField,
    validateForm,
  };
}
