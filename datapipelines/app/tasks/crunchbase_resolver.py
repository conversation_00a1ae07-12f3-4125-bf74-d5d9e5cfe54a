"""
Crunchbase URL Resolver Task for TractionX Data Pipeline Service.

This task handles the complete Crunchbase URL resolution pipeline:
1. <PERSON><PERSON> search for Crunchbase URLs
2. LLM-based link selection
3. BrightData scraping trigger
4. Data storage and processing
"""

import time
from datetime import datetime, timezone
from typing import Any, Dict

from app.configs import get_logger
from app.models.crunchbase import CrunchbaseResolverInput
from app.services.crunchbase_resolver import get_crunchbase_resolver_service
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


async def resolve_crunchbase_url_task(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main task function for Crunchbase URL resolution.

    This function is called by the job queue system and handles:
    - Input validation
    - Service initialization
    - URL resolution
    - Result storage

    Args:
        payload: Job payload containing company domain and optional description

    Returns:
        Processing result with status and data
    """
    start_time = time.time()

    try:
        logger.info("Starting Crunchbase URL resolver task", payload=payload)

        # Handle payload structure - data might be in job_args
        if "job_args" in payload:
            data = payload["job_args"]
        else:
            data = payload

        # Validate input
        if "company_domain" not in data:
            return {
                "success": False,
                "error": "Missing required field: company_domain",
                "processing_time": time.time() - start_time,
            }

        # Create input model
        input_data = CrunchbaseResolverInput(
            company_domain=data["company_domain"],
            company_description=data.get("company_description"),
            org_id=data.get("org_id"),
            job_id=data.get("job_id"),
        )

        # Get resolver service
        try:
            logger.info("Getting Crunchbase resolver service")
            resolver_service = await get_crunchbase_resolver_service()
            logger.info("Crunchbase resolver service obtained successfully")
        except Exception as e:
            error_msg = f"Failed to get Crunchbase resolver service: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "processing_time": time.time() - start_time,
            }

        # Resolve Crunchbase URL
        try:
            logger.info("Starting Crunchbase URL resolution")
            result = await resolver_service.resolve_crunchbase_url(input_data)
            logger.info("Crunchbase URL resolution completed")
        except Exception as e:
            error_msg = f"Crunchbase URL resolution failed: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "processing_time": time.time() - start_time,
            }

        # Store result to S3 for audit
        s3_storage = S3Storage()
        await s3_storage.initialize()

        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        s3_key = f"crunchbase_resolver_results/{input_data.org_id or 'unknown'}/{input_data.company_domain}/{timestamp}_result.json"

        # Prepare data for S3 storage
        s3_data = {
            "input": input_data.model_dump(),
            "output": result.model_dump(),
            "processing_time": time.time() - start_time,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        # Store to S3
        success = await s3_storage.put_object(s3_key, s3_data)

        if success:
            logger.info(f"Successfully stored result to S3: {s3_key}")
            logger.info("S3 Data Summary:")
            logger.info(f"  - Input domain: {input_data.company_domain}")
            logger.info(f"  - Output status: {result.status}")
            logger.info(f"  - Crunchbase URL: {result.crunchbase_url or 'N/A'}")
            logger.info(
                f"  - BrightData Snapshot ID: {result.brightdata_snapshot_id or 'N/A'}"
            )
            logger.info(f"  - Processing time: {time.time() - start_time:.2f}s")
            logger.info(f"  - Error message: {result.error_message or 'None'}")
            logger.info(f"  - LLM Decision: {result.llm_decision or 'N/A'}")
            logger.info(f"  - Serper Results Count: {result.serper_results_count or 0}")
        else:
            logger.error(f"Failed to store result to S3: {s3_key}")

        await s3_storage.cleanup()

        # Prepare return result - only return S3 key, not the data
        return_result = {
            "success": result.status in ["triggered", "no_match", "completed"],
            "status": result.status,
            "s3_key": s3_key,
            "processing_time": time.time() - start_time,
        }

        # Log the return result
        logger.info("Returning result:")
        logger.info(f"  - Success: {return_result['success']}")
        logger.info(f"  - Status: {return_result['status']}")
        logger.info(f"  - Crunchbase URL: {result.crunchbase_url or 'N/A'}")
        logger.info(
            f"  - BrightData Snapshot ID: {result.brightdata_snapshot_id or 'N/A'}"
        )
        logger.info(f"  - Processing time: {return_result['processing_time']:.2f}s")
        logger.info(f"  - S3 Key: {s3_key}")

        return return_result

    except Exception as e:
        error_msg = f"Crunchbase resolver task failed: {str(e)}"
        logger.error(error_msg, exc_info=True)

        return {
            "success": False,
            "error": error_msg,
            "processing_time": time.time() - start_time,
        }


async def process_brightdata_company_data_task(
    payload: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Process BrightData company data from Crunchbase scraping.

    This task handles:
    - Polling for BrightData snapshot completion
    - Downloading BrightData snapshot
    - Processing and cleaning data
    - Storing to database
    - Updating enrichment status

    Args:
        payload: Job payload containing snapshot ID and metadata

    Returns:
        Processing result with status and data
    """
    start_time = time.time()

    try:
        logger.info("Starting BrightData company data processing task", payload=payload)

        # Handle payload structure - data might be in job_args
        if "job_args" in payload:
            data = payload["job_args"]
        else:
            data = payload

        # Validate input
        required_fields = ["snapshot_id", "company_domain", "org_id"]
        for field in required_fields:
            if field not in data:
                return {
                    "success": False,
                    "error": f"Missing required field: {field}",
                    "processing_time": time.time() - start_time,
                }

        snapshot_id = data["snapshot_id"]
        company_domain = data["company_domain"]
        org_id = data["org_id"]

        # Get resolver service for BrightData operations
        resolver_service = await get_crunchbase_resolver_service()

        # Step 1: Poll for snapshot completion
        logger.info(f"Polling for BrightData snapshot completion: {snapshot_id}")
        poll_result = await _poll_brightdata_snapshot_completion(
            snapshot_id, resolver_service
        )
        if not poll_result["success"]:
            return {
                "success": False,
                "error": f"Snapshot polling failed: {poll_result['error']}",
                "processing_time": time.time() - start_time,
                "brightdata_status": poll_result.get("status", "poll_failed"),
            }

        logger.info(f"BrightData snapshot ready: {snapshot_id}")

        # Step 2: Download snapshot data
        download_result = await resolver_service._download_brightdata_snapshot(
            snapshot_id
        )
        if not download_result["success"]:
            return {
                "success": False,
                "error": f"Failed to download snapshot: {download_result['error']}",
                "processing_time": time.time() - start_time,
            }

        raw_data = download_result["data"]

        # Store raw data to S3
        s3_storage = S3Storage()
        await s3_storage.initialize()

        s3_key = f"brightdata_crunchbase_data/{company_domain}.json"

        # Prepare data for S3 storage
        s3_data = {
            "snapshot_id": snapshot_id,
            "company_domain": company_domain,
            "org_id": org_id,
            "raw_data": raw_data,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        # Store to S3
        success = await s3_storage.put_object(s3_key, s3_data)

        if success:
            logger.info(f"Successfully stored BrightData raw data to S3: {s3_key}")
            logger.info("BrightData Data Summary:")
            logger.info(f"  - Snapshot ID: {snapshot_id}")
            logger.info(f"  - Company Domain: {company_domain}")
            logger.info(f"  - Raw data type: {type(raw_data)}")
            logger.info(
                f"  - Raw data size: {len(str(raw_data)) if raw_data else 0} characters"
            )
        else:
            logger.error(f"Failed to store BrightData raw data to S3: {s3_key}")

        # Process and clean data
        processed_data = await _process_company_data(
            raw_data, company_domain, org_id, snapshot_id, s3_key
        )

        # Update S3 data to include processed data
        updated_s3_data = {
            "snapshot_id": snapshot_id,
            "company_domain": company_domain,
            "org_id": org_id,
            "raw_data": raw_data,
            "processed_data": processed_data,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        # Store updated data to S3
        success = await s3_storage.put_object(s3_key, updated_s3_data)

        if success:
            logger.info(
                f"Successfully stored BrightData data with processed data to S3: {s3_key}"
            )
            logger.info("BrightData Data Summary:")
            logger.info(f"  - Snapshot ID: {snapshot_id}")
            logger.info(f"  - Company Domain: {company_domain}")
            logger.info(f"  - Raw data type: {type(raw_data)}")
            logger.info(
                f"  - Raw data size: {len(str(raw_data)) if raw_data else 0} characters"
            )
        else:
            logger.error(
                f"Failed to store BrightData data with processed data to S3: {s3_key}"
            )

        await s3_storage.cleanup()

        # Prepare return result - only return S3 key, not the data
        return_result = {
            "success": True,
            "status": "completed",
            "s3_key": s3_key,
            "processing_time": time.time() - start_time,
        }

        # Log the return result
        logger.info("BrightData processing completed:")
        logger.info(f"  - Success: {return_result['success']}")
        logger.info(f"  - Status: {return_result['status']}")
        logger.info(f"  - Processing time: {return_result['processing_time']:.2f}s")
        logger.info(f"  - S3 Key: {s3_key}")

        return return_result

    except Exception as e:
        error_msg = f"BrightData company data processing failed: {str(e)}"
        logger.error(error_msg, exc_info=True)

        return {
            "success": False,
            "error": error_msg,
            "processing_time": time.time() - start_time,
        }


async def _poll_brightdata_snapshot_completion(
    snapshot_id: str, resolver_service
) -> Dict[str, Any]:
    """
    Poll for BrightData snapshot completion.

    Args:
        snapshot_id: BrightData snapshot ID
        resolver_service: Crunchbase resolver service instance

    Returns:
        Dictionary with success status and error if any
    """
    import asyncio

    max_poll_time = 600  # 10 minutes
    poll_interval = 10  # 10 seconds
    start_time = time.time()

    while time.time() - start_time < max_poll_time:
        try:
            # Check snapshot progress using BrightData API
            if not resolver_service.brightdata_client:
                return {
                    "success": False,
                    "status": "error",
                    "error": "BrightData client not initialized",
                }

            response = await resolver_service.brightdata_client.get(
                f"/datasets/v3/progress/{snapshot_id}"
            )
            response.raise_for_status()
            result = response.json()

            status = result.get("status")
            logger.info(f"Polling status: {status} for {snapshot_id}")

            if status == "ready":
                return {
                    "success": True,
                    "status": "ready",
                }
            elif status in ["error", "failed"]:
                return {
                    "success": False,
                    "status": "error",
                    "error": f"Job failed with status: {status}",
                }

            # Wait before next poll
            await asyncio.sleep(poll_interval)

        except Exception as e:
            logger.warning(f"Poll error: {e}")
            await asyncio.sleep(poll_interval)

    # Timeout
    return {
        "success": False,
        "status": "timeout",
        "error": f"Polling timed out after {max_poll_time} seconds",
    }


async def _process_company_data(
    raw_data: Any, company_domain: str, org_id: str, snapshot_id: str, s3_key: str
) -> Dict[str, Any]:
    """Process and clean BrightData company data."""
    try:
        # Extract company information from raw data
        # This is a simplified version - you may need to adjust based on actual BrightData response format
        company_data = {
            "company_domain": company_domain,
            "org_id": org_id,
            "snapshot_id": snapshot_id,
            "s3_raw_data_key": s3_key,
            "processed_at": datetime.now(timezone.utc).isoformat(),
        }

        # Extract basic company info from raw data
        if isinstance(raw_data, list) and len(raw_data) > 0:
            profile = raw_data[0]

            # Map BrightData fields to our model
            company_data.update({
                "name": profile.get("name"),
                "description": profile.get("description"),
                "website": profile.get("website"),
                "industry": profile.get("industry"),
                "sub_industry": profile.get("sub_industry"),
                "stage": profile.get("stage"),
                "founded_year": profile.get("founded_year"),
                "employee_count": profile.get("employee_count"),
                "employee_count_range": profile.get("employee_count_range"),
                "headquarters": profile.get("headquarters"),
                "country": profile.get("country"),
                "city": profile.get("city"),
                "state": profile.get("state"),
                "funding_total": profile.get("funding_total"),
                "funding_rounds": profile.get("funding_rounds"),
                "last_funding_date": profile.get("last_funding_date"),
                "last_funding_amount": profile.get("last_funding_amount"),
                "valuation": profile.get("valuation"),
                "revenue": profile.get("revenue"),
                "linkedin_url": profile.get("linkedin_url"),
                "twitter_url": profile.get("twitter_url"),
                "facebook_url": profile.get("facebook_url"),
                "email": profile.get("email"),
                "phone": profile.get("phone"),
                "founders": profile.get("founders", []),
                "executives": profile.get("executives", []),
                "technologies": profile.get("technologies", []),
                "keywords": profile.get("keywords", []),
                "competitors": profile.get("competitors", []),
            })

        return company_data

    except Exception as e:
        logger.error(f"Error processing company data: {str(e)}", exc_info=True)
        return {
            "company_domain": company_domain,
            "org_id": org_id,
            "snapshot_id": snapshot_id,
            "s3_raw_data_key": s3_key,
            "error": str(e),
            "processed_at": datetime.now(timezone.utc).isoformat(),
        }
