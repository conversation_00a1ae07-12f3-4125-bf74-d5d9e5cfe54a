# Comprehensive Thesis Scoring Implementation

This document describes the implementation of the comprehensive thesis scoring system as specified in the PRD.

## Overview

The comprehensive thesis scoring system evaluates form submissions against investment theses using:
- **All question types**: Text (AI-powered), numeric, select, boolean, date, file
- **Repeatable section aggregation**: Support for complex aggregation logic
- **Bonus scoring**: Additional points for meeting specific criteria
- **Modular storage**: Separate scoring for thesis, founders, and market analysis

## Architecture

### Core Components

1. **ComprehensiveThesisScoringEngine** (`app/services/thesis/scoring.py`)
   - Main scoring orchestrator
   - Handles all question types and aggregation
   - Integrates with AI services for text evaluation

2. **AITextScoringService** (`app/services/ai/text_scoring.py`)
   - OpenAI-powered text evaluation
   - Semantic matching against good/bad references
   - Source extraction and explanation generation

3. **Enhanced Deal Model** (`app/models/deal.py`)
   - Modular scoring structure: `{thesis, founders, market}`
   - Backward compatibility with existing scoring
   - Detailed question-wise storage

4. **Updated Submission Processing** (`app/services/submission_processing/service.py`)
   - Integrates comprehensive scoring
   - Stores results in new modular format
   - Maintains async processing workflow

## Question Type Scoring

### Text Questions (SHORT_TEXT, LONG_TEXT)
- **Method**: AI-powered semantic evaluation using OpenAI GPT-4o-mini
- **Input**: User answer + good/bad reference examples
- **Output**: Score (0-1), explanation, sources, confidence
- **Configuration**: `condition.value = {good: "...", bad: "..."}`

### Numeric Questions (NUMBER, RANGE)
- **Method**: Operator-based comparison (>, <, =, between, etc.)
- **Operators**: eq, ne, lt, lte, gt, gte, between, not_between
- **Range handling**: Uses midpoint for comparison

### Select Questions (SINGLE_SELECT, MULTI_SELECT)
- **Single Select**: Exact matching with eq, ne, in, not_in operators
- **Multi Select**: Proportional scoring with contains, in, not_in operators
- **Proportional credit**: Overlap count / expected count

### Boolean Questions
- **Method**: Exact match comparison
- **Operators**: eq, ne

### Date Questions
- **Method**: Date comparison with various operators
- **Operators**: eq, ne, gt, lt, gte, lte, between
- **Format support**: ISO strings, timestamps

### File Questions
- **Scoring**: Not supported (returns 0 with explanation)
- **Future**: Document analysis capabilities planned

## Aggregation Types

For repeatable sections, the following aggregation methods are supported:

- **NONE**: No aggregation (single instance)
- **ANY**: Award full score if any instance matches
- **ALL**: Award full score if all instances match
- **COUNT**: Proportional score based on matching instances
- **PERCENTAGE**: Score if percentage threshold is met
- **AVG/SUM/MIN/MAX**: Numeric aggregation with operator comparison

## Bonus Scoring

- Applied after regular scoring
- References non-repeatable section questions only
- Multiple bonuses are summed
- Uses same condition evaluation as regular rules

## Data Storage Structure

### Comprehensive Scoring Format
```json
{
  "thesis": {
    "thesis_id": "...",
    "thesis_name": "...",
    "total_score": 85.5,
    "normalized_score": 85.5,
    "max_possible_score": 100.0,
    "question_scores": {
      "question_id": {
        "rule_id": "...",
        "raw_score": 0.85,
        "weight": 2.0,
        "weighted_score": 1.7,
        "explanation": "...",
        "sources": [...],
        "ai_generated": true,
        "aggregation_used": false
      }
    },
    "bonus_scores": {
      "bonus_rule_id": {
        "bonus_points": 10.0,
        "explanation": "..."
      }
    }
  },
  "founders": {
    "total_score": 90.0,
    "normalized_score": 90.0,
    "ai_analysis": "...",
    "key_insights": [...]
  },
  "market": {
    "total_score": 80.0,
    "normalized_score": 80.0,
    "ai_analysis": "...",
    "key_insights": [...]
  },
  "metadata": {
    "scoring_version": "v2.0",
    "scored_at": 1640995200,
    "total_rules_processed": 5,
    "ai_scoring_used": true
  }
}
```

## API Integration

### Backend Endpoints
- **Thesis Scoring**: Integrated into existing `ThesisService.calculate_score()`
- **Deal Analysis**: Enhanced `/deals/{id}/full-analysis` endpoint
- **Score Override**: `/deals/{id}/score-override` for investor adjustments

### Frontend Integration
- **Full Analysis Page**: Displays comprehensive scoring breakdown
- **Question-wise Details**: Shows individual rule evaluations
- **AI Indicators**: Highlights AI-generated scores and explanations
- **Source Links**: Displays external validation sources

## Configuration

### Environment Variables
```bash
# Required for AI text scoring
OPENAI_API_KEY=your_openai_api_key_here
```

### Thesis Rule Configuration
```python
# Text question scoring
ScoringRule(
    question_type=QuestionType.LONG_TEXT,
    condition=FilterCondition(
        question_id="market_analysis",
        operator=ConditionOperator.EQUALS,  # Ignored for text
        value={
            "good": "Strong market with clear demand signals...",
            "bad": "Unclear market with no validation..."
        }
    ),
    weight=2.0
)

# Numeric question scoring
ScoringRule(
    question_type=QuestionType.NUMBER,
    condition=FilterCondition(
        question_id="revenue",
        operator=ConditionOperator.GREATER_THAN,
        value=1000000
    ),
    weight=1.5
)
```

## Testing

### Test Script
Run the comprehensive scoring test:
```bash
cd backend
python test_comprehensive_scoring.py
```

### Expected Output
- Question-wise scoring breakdown
- AI evaluation results (if OpenAI key configured)
- Bonus point calculations
- Comprehensive scoring structure

## Future Enhancements

1. **File Scoring**: Document analysis and extraction
2. **Advanced Aggregation**: Custom formula-based scoring
3. **Market/Founder AI**: Comprehensive AI analysis for all signals
4. **Real-time Scoring**: Live scoring updates during form completion
5. **Scoring Analytics**: Performance metrics and optimization insights

## Troubleshooting

### Common Issues

1. **AI Scoring Fails**: Check OPENAI_API_KEY configuration
2. **Missing Scores**: Verify question_id mapping between rules and responses
3. **Aggregation Errors**: Ensure repeatable section data structure is correct
4. **Performance Issues**: Consider caching for frequently accessed scoring data

### Debug Mode
Enable detailed logging by setting `LOG_LEVEL=DEBUG` in environment variables.

## Migration Notes

- **Backward Compatibility**: Existing scoring data remains functional
- **Gradual Migration**: New comprehensive format is additive
- **Legacy Support**: Old scoring format still supported in APIs
- **Data Migration**: Run migration script to update existing deals (TODO)

---

For questions or issues, refer to the implementation files or contact the development team.
