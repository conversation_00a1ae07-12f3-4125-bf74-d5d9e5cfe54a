from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, EmailStr, Field, validator


class LoginRequest(BaseModel):
    """Schema for login request."""

    email: EmailStr = Field(..., description="User's email address")
    password: str = Field(..., min_length=8, description="User's password")
    device_info: Optional[Dict[str, Any]] = Field(
        None, description="Device information"
    )
    ip_address: Optional[str] = Field(None, description="User's IP address")

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "password123",
                "device_info": {
                    "device_type": "mobile",
                    "os": "iOS",
                    "browser": "Safari",
                },
                "ip_address": "***********",
            }
        }


class TokenResponse(BaseModel):
    """Schema for token response."""

    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field("bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    requires_password_reset: bool = Field(
        False, description="Whether user needs to reset password"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "token_type": "bearer",
                "expires_in": 1800,
                "requires_password_reset": False,
            }
        }


class TokenData(BaseModel):
    """Schema for token payload data."""

    user_id: str = Field(..., description="User's unique identifier")
    org_id: str = Field(..., description="Organization's unique identifier")
    role: str = Field(..., description="User's role")
    plan: str = Field(..., description="Organization's plan")
    exp: datetime = Field(..., description="Token expiration time")
    iat: datetime = Field(..., description="Token issuance time")
    jti: str = Field(..., description="JWT ID for token tracking")

    @validator("exp", "iat")
    def validate_datetime(cls, v):
        if not isinstance(v, datetime):
            raise ValueError("Must be a datetime object")
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "user_id": "507f1f77bcf86cd799439011",
                "org_id": "507f1f77bcf86cd799439012",
                "role": "admin",
                "plan": "pro",
                "exp": "2024-03-20T10:00:00Z",
                "iat": "2024-03-20T09:30:00Z",
                "jti": "507f1f77bcf86cd799439013",
            }
        }


class RefreshTokenRequest(BaseModel):
    """Schema for refresh token request."""

    refresh_token: str = Field(..., description="JWT refresh token")

    class Config:
        json_schema_extra = {
            "example": {"refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."}
        }


class ForgotPasswordRequest(BaseModel):
    """Schema for forgot password request."""

    email: EmailStr = Field(..., description="User's email address")

    class Config:
        json_schema_extra = {"example": {"email": "<EMAIL>"}}


class ResetPasswordRequest(BaseModel):
    """Schema for password reset request."""

    token: str = Field(..., description="Password reset token")
    password: str = Field(..., min_length=8, description="New password")
    confirm_password: str = Field(..., description="Password confirmation")

    @validator("confirm_password")
    def passwords_match(cls, v, values, **kwargs):
        if "password" in values and v != values["password"]:
            raise ValueError("Passwords do not match")
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "password": "newpassword123",
                "confirm_password": "newpassword123",
            }
        }


class InviteUserRequest(BaseModel):
    """Schema for user invitation request."""

    name: str = Field(..., description="User's name")
    email: EmailStr = Field(..., description="User's email address")
    role_id: str = Field(..., description="User's role in the organization")
    org_id: str = Field(..., description="Organization's unique identifier")
    invited_by: str = Field(..., description="ID of the user sending the invitation")

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "role_id": "507f1f77bcf86cd799439012",
                "org_id": "507f1f77bcf86cd799439012",
                "invited_by": "507f1f77bcf86cd799439011",
            }
        }


class AcceptInvitationRequest(BaseModel):
    """Schema for accepting user invitation."""

    token: str = Field(..., description="Invitation token")
    password: str = Field(..., min_length=8, description="New password")
    confirm_password: str = Field(..., description="Password confirmation")

    @validator("confirm_password")
    def passwords_match(cls, v, values, **kwargs):
        if "password" in values and v != values["password"]:
            raise ValueError("Passwords do not match")
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "password": "newpassword123",
                "confirm_password": "newpassword123",
            }
        }


class CreateInviteCodeRequest(BaseModel):
    """Schema for creating an invite code."""

    email: EmailStr = Field(..., description="Email address to reserve for this invite")
    org_name: Optional[str] = Field(
        None, description="Optional pre-filled organization name"
    )

    class Config:
        json_schema_extra = {
            "example": {"email": "<EMAIL>", "org_name": "Alpha Ventures"}
        }


class CreateInviteCodeResponse(BaseModel):
    """Schema for invite code creation response."""

    code: str = Field(..., description="Generated invite code")
    email: EmailStr = Field(..., description="Email address reserved for this invite")
    expires_at: int = Field(..., description="Expiration timestamp")
    onboard_url: str = Field(..., description="Full onboarding URL")

    class Config:
        json_schema_extra = {
            "example": {
                "code": "ABC123XY",
                "email": "<EMAIL>",
                "expires_at": 1234567890,
                "onboard_url": "https://tractionx.ai/onboard/ABC123XY",
            }
        }


class OnboardingStepOneRequest(BaseModel):
    """Schema for onboarding step 1 - organization creation."""

    org_name: str = Field(
        ..., min_length=2, max_length=100, description="Organization name"
    )
    subdomain: str = Field(
        ..., min_length=3, max_length=50, description="Unique subdomain"
    )
    website: Optional[str] = Field(None, description="Organization website")
    logo_url: Optional[str] = Field(None, description="S3 URL for uploaded logo")

    class Config:
        json_schema_extra = {
            "example": {
                "org_name": "Alpha Ventures",
                "subdomain": "alpha-ventures",
                "website": "https://alphaventures.com",
                "logo_url": "https://s3.amazonaws.com/bucket/logo.png",
            }
        }


class OnboardingStepTwoRequest(BaseModel):
    """Schema for onboarding step 2 - user profile creation."""

    name: str = Field(..., min_length=2, max_length=100, description="User's full name")
    password: str = Field(..., min_length=8, description="User's password")
    confirm_password: str = Field(..., description="Password confirmation")
    profile_picture_url: Optional[str] = Field(
        None, description="S3 URL for uploaded profile picture"
    )

    @validator("confirm_password")
    def passwords_match(cls, v, values, **kwargs):
        if "password" in values and v != values["password"]:
            raise ValueError("Passwords do not match")
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "name": "John Doe",
                "password": "securepassword123",
                "confirm_password": "securepassword123",
                "profile_picture_url": "https://s3.amazonaws.com/bucket/profile.jpg",
            }
        }


class OnboardingCompleteResponse(BaseModel):
    """Schema for completed onboarding response."""

    user_id: str = Field(..., description="Created user ID")
    org_id: str = Field(..., description="Created organization ID")
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    redirect_url: str = Field(..., description="URL to redirect to after onboarding")

    class Config:
        json_schema_extra = {
            "example": {
                "user_id": "507f1f77bcf86cd799439011",
                "org_id": "507f1f77bcf86cd799439012",
                "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "redirect_url": "/login",
            }
        }


class PeerInviteRequest(BaseModel):
    """Schema for inviting peers to join the organization."""

    emails: List[EmailStr] = Field(
        ...,
        min_length=1,
        max_length=10,
        description="List of email addresses to invite",
    )
    role_id: Optional[str] = Field(
        None, description="Role ID to assign to invited users"
    )
    message: Optional[str] = Field(
        None, max_length=500, description="Optional personal message"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "emails": ["<EMAIL>", "<EMAIL>"],
                "role_id": "507f1f77bcf86cd799439012",
                "message": "Join our team on TractionX!",
            }
        }
