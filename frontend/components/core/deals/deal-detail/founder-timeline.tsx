"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, MapPin, Users, Building2, GraduationCap, Briefcase } from "lucide-react"
import { motion } from "framer-motion"
import clsx from "clsx"

interface Experience {
  id: string
  title: string
  company: string
  location?: string
  startDate: string
  endDate?: string
  description?: string
  type: 'startup' | 'corporate' | 'internship' | 'education'
  companySize?: string
  industry?: string
}

interface FounderTimelineProps {
  experiences: Experience[]
  className?: string
}

const getExperienceIcon = (type: string) => {
  switch (type) {
    case 'startup':
      return Building2
    case 'corporate':
      return Building2
    case 'internship':
      return GraduationCap
    case 'education':
      return GraduationCap
    default:
      return Briefcase
  }
}

const getExperienceColor = (type: string) => {
  switch (type) {
    case 'startup':
      return 'bg-slate-100 border-slate-200 text-slate-700'
    case 'corporate':
      return 'bg-slate-100 border-slate-200 text-slate-700'
    case 'internship':
      return 'bg-slate-100 border-slate-200 text-slate-700'
    case 'education':
      return 'bg-slate-100 border-slate-200 text-slate-700'
    default:
      return 'bg-slate-100 border-slate-200 text-slate-700'
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
}

const calculateDuration = (startDate: string, endDate?: string) => {
  const start = new Date(startDate)
  const end = endDate ? new Date(endDate) : new Date()
  
  const years = end.getFullYear() - start.getFullYear()
  const months = end.getMonth() - start.getMonth()
  
  let totalMonths = years * 12 + months
  if (end.getDate() < start.getDate()) totalMonths--
  
  const yearsPart = Math.floor(totalMonths / 12)
  const monthsPart = totalMonths % 12
  
  if (yearsPart > 0 && monthsPart > 0) {
    return `${yearsPart}y ${monthsPart}m`
  } else if (yearsPart > 0) {
    return `${yearsPart}y`
  } else {
    return `${monthsPart}m`
  }
}

const isCurrent = (endDate?: string) => {
  return !endDate || new Date(endDate) > new Date()
}

export function FounderTimeline({ experiences, className }: FounderTimelineProps) {
  const sortedExperiences = [...experiences].sort((a, b) => 
    new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
  )

  if (!experiences || experiences.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Card className={clsx(
          "border border-slate-200 bg-white/90 backdrop-blur-sm shadow-none hover:shadow-md transition-shadow duration-200",
          className
        )}>
          <CardHeader className="border-b border-slate-100 pb-4">
            <div className="flex items-center gap-3">
              <div className="rounded-2xl border border-slate-200 bg-slate-50 p-2">
                <Calendar className="size-4 text-slate-600" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-slate-900">
                  Experience Timeline
                </CardTitle>
                <p className="mt-0.5 text-xs text-slate-500">
                  Professional background
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="py-8 text-center">
              <div className="mx-auto mb-4 flex size-16 items-center justify-center rounded-2xl border border-slate-200 bg-slate-50">
                <Calendar className="size-8 text-slate-400" />
              </div>
              <p className="text-sm text-slate-500">No experience data available</p>
              <p className="mt-1 text-xs text-slate-400">Add founder experience to see timeline</p>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.3 }}
    >
      <Card className={clsx(
        "border border-slate-200 bg-white/90 backdrop-blur-sm shadow-none hover:shadow-md transition-shadow duration-200",
        className
      )}>
        <CardHeader className="border-b border-slate-100 pb-4">
          <div className="flex items-center gap-3">
            <div className="rounded-2xl border border-slate-200 bg-slate-50 p-2">
              <Calendar className="size-4 text-slate-600" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold text-slate-900">
                Experience Timeline
              </CardTitle>
              <p className="mt-0.5 text-xs text-slate-500">
                Professional background
              </p>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="p-6">
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute inset-y-0 left-6 w-0.5 bg-slate-200" />
            
            <div className="space-y-6">
              {sortedExperiences.map((experience, index) => {
                const Icon = getExperienceIcon(experience.type)
                const duration = calculateDuration(experience.startDate, experience.endDate)
                const current = isCurrent(experience.endDate)
                
                return (
                  <motion.div
                    key={experience.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.5 }}
                    className="relative flex gap-4"
                  >
                    {/* Timeline dot */}
                    <div className="relative z-10 shrink-0">
                      <div className={clsx(
                        "w-12 h-12 rounded-2xl border-2 flex items-center justify-center",
                        getExperienceColor(experience.type)
                      )}>
                        <Icon className="size-5" />
                      </div>
                    </div>
                    
                    {/* Experience card */}
                    <div className="min-w-0 flex-1">
                      <div className="rounded-2xl border border-slate-200 bg-slate-50 p-4 transition-colors duration-200 hover:bg-slate-100">
                        <div className="mb-2 flex items-start justify-between">
                          <div className="min-w-0 flex-1">
                            <h4 className="truncate text-sm font-semibold text-slate-900">
                              {experience.title}
                            </h4>
                            <p className="text-sm font-medium text-slate-600">
                              {experience.company}
                            </p>
                          </div>
                          
                          {/* Duration badge */}
                          <div className="ml-2 shrink-0">
                            <Badge 
                              variant="outline" 
                              className={clsx(
                                "text-xs px-2 py-1 rounded-full border-slate-200",
                                current ? "bg-blue-50 text-blue-700 border-blue-200" : "bg-white text-slate-600"
                              )}
                            >
                              {current ? "Present" : duration}
                            </Badge>
                          </div>
                        </div>
                        
                        {/* Date range */}
                        <div className="mb-3 flex items-center gap-2 text-xs text-slate-500">
                          <Calendar className="size-3" />
                          <span>
                            {formatDate(experience.startDate)} – {current ? "Present" : formatDate(experience.endDate!)}
                          </span>
                          {!current && <span>• {duration}</span>}
                        </div>
                        
                        {/* Location and company info */}
                        <div className="flex flex-wrap items-center gap-3 text-xs text-slate-500">
                          {experience.location && (
                            <div className="flex items-center gap-1">
                              <MapPin className="size-3" />
                              <span>{experience.location}</span>
                            </div>
                          )}
                          
                          {experience.companySize && (
                            <div className="flex items-center gap-1">
                              <Users className="size-3" />
                              <span>{experience.companySize}</span>
                            </div>
                          )}
                          
                          {experience.industry && (
                            <Badge 
                              variant="outline" 
                              className="rounded-full border-slate-200 bg-white px-2 py-0.5 text-xs text-slate-600"
                            >
                              {experience.industry}
                            </Badge>
                          )}
                        </div>
                        
                        {/* Description */}
                        {experience.description && (
                          <div className="mt-3 border-t border-slate-200 pt-3">
                            <p className="text-xs leading-relaxed text-slate-600">
                              {experience.description}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                )
              })}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

export default FounderTimeline
