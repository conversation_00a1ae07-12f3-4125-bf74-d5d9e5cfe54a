"""
Generic webhook endpoint for TractionX Data Pipeline Service.
"""

from typing import Any, Dict

from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import JSONResponse

from app.configs import get_logger, settings
from app.util import QueueClient

logger = get_logger(__name__)
router = APIRouter(prefix="")


@router.post("/trigger")
async def generic_trigger_webhook(request: Request) -> JSONResponse:
    """
    Handle generic pipeline trigger webhook.

    This endpoint receives trigger data and queues appropriate pipeline jobs.
    """
    try:
        # Parse JSON payload
        try:
            payload = await request.json()
        except Exception as e:
            logger.error(f"Failed to parse webhook payload: {e}")
            raise HTTPException(status_code=400, detail="Invalid JSON payload")

        logger.info("Received generic webhook", payload_keys=list(payload.keys()))

        # Extract job type and data
        job_type = payload.get("job_type")
        if not job_type:
            logger.warning("No job_type specified in webhook payload")
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "job_type is required"},
            )

        # Handle different job types
        if job_type == "enrich_founder_data":
            job_result = await queue_founder_enrichment_job(payload)
        elif job_type == "enrich_company_data":
            job_result = await queue_company_enrichment_job(payload)
        else:
            logger.warning(f"Unknown job type: {job_type}")
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": f"Unknown job type: {job_type}"},
            )

        logger.info(
            f"{job_type} job queued",
            job_id=job_result.get("job_id"),
            company_id=payload.get("company_id"),
        )

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "message": f"{job_type} job queued",
                "job_id": job_result.get("job_id"),
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Generic webhook error: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


async def queue_founder_enrichment_job(payload: Dict[str, Any]) -> Dict[str, Any]:
    """Queue a founder enrichment job."""
    try:
        # Validate required fields
        required_fields = [
            "founder_id",
            "company_id",
            "org_id",
            "founder_name",
            "founder_linkedin",
        ]
        missing_fields = [field for field in required_fields if not payload.get(field)]

        if missing_fields:
            raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

        # Use our custom queue client
        queue_client = QueueClient()

        # Prepare job data
        job_data = {
            "founder_id": payload["founder_id"],
            "company_id": payload["company_id"],
            "org_id": payload["org_id"],
            "founder_name": payload["founder_name"],
            "founder_linkedin": payload["founder_linkedin"],
            "submission_id": payload.get("submission_id"),
            "form_data": payload.get("form_data"),
            "webhook_source": "generic_trigger",
        }

        # Queue the job
        job_id = await queue_client.enqueue_job(
            queue_name="default",
            job_function="enrich_founder_data",
            job_data=job_data,
            timeout=settings.PIPELINE_TIMEOUT,
        )

        return {"job_id": job_id, "status": "queued"}

    except Exception as e:
        logger.error(f"Failed to queue founder enrichment job: {e}")
        raise


async def queue_company_enrichment_job(payload: Dict[str, Any]) -> Dict[str, Any]:
    """Queue a company enrichment job."""
    try:
        # Validate required fields
        required_fields = ["company_id", "org_id", "company_name"]
        missing_fields = [field for field in required_fields if not payload.get(field)]

        if missing_fields:
            raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

        # Use our custom queue client
        queue_client = QueueClient()

        # Prepare job data
        job_data = {
            "company_id": payload["company_id"],
            "org_id": payload["org_id"],
            "company_name": payload["company_name"],
            "domain": payload.get("domain"),
            "form_data": payload.get("form_data"),
            "webhook_source": "generic_trigger",
        }

        # Queue the job
        job_id = await queue_client.enqueue_job(
            queue_name="default",
            job_function="enrich_company_data",
            job_data=job_data,
            timeout=settings.PIPELINE_TIMEOUT,
        )

        return {"job_id": job_id, "status": "queued"}

    except Exception as e:
        logger.error(f"Failed to queue company enrichment job: {e}")
        raise


@router.get("/status")
async def generic_webhook_status():
    """Get generic webhook status."""
    return {
        "status": "active",
        "webhook_type": "generic_trigger",
        "supported_job_types": ["enrich_founder_data", "enrich_company_data"],
    }
