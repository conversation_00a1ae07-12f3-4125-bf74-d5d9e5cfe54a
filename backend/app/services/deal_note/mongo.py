"""
MongoDB implementation of the Deal Note Service.

Provides CRUD operations for deal notes with team mentions,
timeline integration, and proper access control.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.logging import get_logger
from app.models.deal_note import DealNote
from app.models.user import User
from app.services.base import BaseService
from app.services.deal_note.interfaces import DealNoteServiceInterface

logger = get_logger(__name__)


class DealNoteService(BaseService, DealNoteServiceInterface):
    """MongoDB implementation of deal note service."""

    def __init__(self, db: Optional[AsyncIOMotorDatabase] = None):
        super().__init__()
        self.db = db

    async def initialize(self):
        # No-op for now
        pass

    async def cleanup(self):
        # No-op for now
        pass

    async def create_note(
        self,
        deal_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        created_by: Union[str, ObjectId],
        structured_content: List[Dict[str, Any]],
        tagged_user_ids: Optional[List[Union[str, ObjectId]]] = None,
        pinned: bool = False,
    ) -> Optional[DealNote]:
        """Create a new deal note."""
        try:
            # Convert IDs to ObjectId
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(created_by, str):
                created_by = ObjectId(created_by)

            # Validate tagged users
            validated_tagged_users = []
            if tagged_user_ids:
                validated_tagged_users = await self.validate_tagged_users(
                    tagged_user_ids, org_id
                )

            # Create note
            note = DealNote(
                deal_id=deal_id,  # type: ignore
                org_id=org_id,  # type: ignore
                created_by=created_by,  # type: ignore
                structured_content=structured_content,
                tagged_user_ids=validated_tagged_users,  # type: ignore
                pinned=pinned,
            )

            await note.save()
            logger.info(f"Created deal note {note.id} for deal {deal_id}")
            return note

        except Exception as e:
            await self.handle_error(
                e,
                {
                    "deal_id": str(deal_id),
                    "org_id": str(org_id),
                    "created_by": str(created_by),
                    "structured_content_length": len(structured_content),
                },
            )
            return None

    async def get_note(
        self, note_id: Union[str, ObjectId], org_id: Union[str, ObjectId]
    ) -> Optional[DealNote]:
        """Get a deal note by ID."""
        try:
            if isinstance(note_id, str):
                note_id = ObjectId(note_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            note = await DealNote.find_one({
                "_id": note_id,
                "org_id": org_id,
                "deleted_at": None,
            })
            return note

        except Exception as e:
            await self.handle_error(e, {"note_id": str(note_id), "org_id": str(org_id)})
            return None

    async def list_notes(
        self,
        deal_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        skip: int = 0,
        limit: int = 100,
        include_deleted: bool = False,
    ) -> tuple[List[DealNote], int]:
        """List notes for a deal."""
        try:
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            # Build query
            query = {"deal_id": ObjectId(deal_id), "org_id": ObjectId(org_id)}

            logger.info(f"Query: {query}")
            notes = await DealNote.find_many(
                query=query,
                sort=[("pinned", -1), ("created_at", -1)],
                skip=skip,
                limit=limit,
                include_deleted=include_deleted,
            )
            logger.info(f"Notes: {notes}")

            return notes, len(notes)

        except Exception as e:
            await self.handle_error(e, {"deal_id": str(deal_id), "org_id": str(org_id)})
            return [], 0

    async def update_note(
        self,
        note_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        updated_by: Union[str, ObjectId],
        structured_content: Optional[List[Dict[str, Any]]] = None,
        tagged_user_ids: Optional[List[Union[str, ObjectId]]] = None,
        pinned: Optional[bool] = None,
    ) -> Optional[DealNote]:
        """Update a deal note."""
        try:
            if isinstance(note_id, str):
                note_id = ObjectId(note_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(updated_by, str):
                updated_by = ObjectId(updated_by)

            # Get existing note
            note = await self.get_note(note_id, org_id)
            if not note:
                return None

            # Check if user can edit (creator or org admin)
            if str(note.created_by) != str(updated_by):
                # TODO: Add org admin check here
                logger.warning(
                    f"User {updated_by} attempted to edit note {note_id} created by {note.created_by}"
                )
                return None

            # Validate tagged users if provided
            validated_tagged_users = None
            if tagged_user_ids is not None:
                validated_tagged_users = await self.validate_tagged_users(
                    tagged_user_ids, org_id
                )

            # Update note
            update_data = {}
            if structured_content is not None:
                update_data["structured_content"] = structured_content
            if validated_tagged_users is not None:
                update_data["tagged_user_ids"] = validated_tagged_users
            if pinned is not None:
                update_data["pinned"] = pinned

            if update_data:
                update_data["updated_at"] = int(datetime.now(timezone.utc).timestamp())
                await DealNote.update_one({"_id": note_id}, {"$set": update_data})

                # Get updated note
                updated_note = await self.get_note(note_id, org_id)
                logger.info(f"Updated deal note {note_id}")
                return updated_note

            return note

        except Exception as e:
            await self.handle_error(
                e,
                {
                    "note_id": str(note_id),
                    "org_id": str(org_id),
                    "updated_by": str(updated_by),
                },
            )
            return None

    async def delete_note(
        self,
        note_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        deleted_by: Union[str, ObjectId],
    ) -> bool:
        """Soft delete a deal note."""
        try:
            if isinstance(note_id, str):
                note_id = ObjectId(note_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(deleted_by, str):
                deleted_by = ObjectId(deleted_by)

            # Get existing note
            note = await self.get_note(note_id, org_id)
            if not note:
                return False

            # Check if user can delete (creator or org admin)
            if str(note.created_by) != str(deleted_by):
                # TODO: Add org admin check here
                logger.warning(
                    f"User {deleted_by} attempted to delete note {note_id} created by {note.created_by}"
                )
                return False

            # Soft delete
            await DealNote.update_one(
                {"_id": note_id},
                {
                    "$set": {
                        "deleted_at": int(datetime.now(timezone.utc).timestamp()),
                        "deleted_by": deleted_by,
                        "updated_at": int(datetime.now(timezone.utc).timestamp()),
                    }
                },
            )

            logger.info(f"Soft deleted deal note {note_id}")
            return True

        except Exception as e:
            await self.handle_error(
                e,
                {
                    "note_id": str(note_id),
                    "org_id": str(org_id),
                    "deleted_by": str(deleted_by),
                },
            )
            return False

    async def restore_note(
        self,
        note_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        restored_by: Union[str, ObjectId],
    ) -> Optional[DealNote]:
        """Restore a soft-deleted deal note."""
        try:
            if isinstance(note_id, str):
                note_id = ObjectId(note_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(restored_by, str):
                restored_by = ObjectId(restored_by)

            # Get note including deleted ones
            note = await DealNote.find_one({"_id": note_id, "org_id": org_id})
            if not note:
                return None

            # Check if user can restore (creator or org admin)
            if str(note.created_by) != str(restored_by):
                # TODO: Add org admin check here
                logger.warning(
                    f"User {restored_by} attempted to restore note {note_id} created by {note.created_by}"
                )
                return None

            # Restore note
            await DealNote.update_one(
                {"_id": note_id},
                {
                    "$unset": {"deleted_at": "", "deleted_by": ""},
                    "$set": {"updated_at": int(datetime.now(timezone.utc).timestamp())},
                },
            )

            # Get restored note
            restored_note = await self.get_note(note_id, org_id)
            logger.info(f"Restored deal note {note_id}")
            return restored_note

        except Exception as e:
            await self.handle_error(
                e,
                {
                    "note_id": str(note_id),
                    "org_id": str(org_id),
                    "restored_by": str(restored_by),
                },
            )
            return None

    async def toggle_pinned(
        self,
        note_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        user_id: Union[str, ObjectId],
    ) -> Optional[DealNote]:
        """Toggle the pinned status of a note."""
        try:
            if isinstance(note_id, str):
                note_id = ObjectId(note_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(user_id, str):
                user_id = ObjectId(user_id)

            # Get existing note
            note = await self.get_note(note_id, org_id)
            if not note:
                return None

            # Check if user can toggle pin (creator or org admin)
            if str(note.created_by) != str(user_id):
                # TODO: Add org admin check here
                logger.warning(
                    f"User {user_id} attempted to toggle pin on note {note_id} created by {note.created_by}"
                )
                return None

            # Toggle pinned status
            new_pinned = not note.pinned
            await DealNote.update_one(
                {"_id": note_id},
                {
                    "$set": {
                        "pinned": new_pinned,
                        "updated_at": int(datetime.now(timezone.utc).timestamp()),
                    }
                },
            )

            # Get updated note
            updated_note = await self.get_note(note_id, org_id)
            logger.info(
                f"Toggled pinned status for deal note {note_id} to {new_pinned}"
            )
            return updated_note

        except Exception as e:
            await self.handle_error(
                e,
                {
                    "note_id": str(note_id),
                    "org_id": str(org_id),
                    "user_id": str(user_id),
                },
            )
            return None

    async def validate_tagged_users(
        self,
        tagged_user_ids: List[Union[str, ObjectId]],
        org_id: Union[str, ObjectId],
    ) -> List[Union[str, ObjectId]]:
        """Validate that tagged users exist and belong to the organization."""
        try:
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            # Convert string IDs to ObjectId
            object_ids = []
            for user_id in tagged_user_ids:
                if isinstance(user_id, str):
                    object_ids.append(ObjectId(user_id))
                else:
                    object_ids.append(user_id)

            # Check if users exist and belong to the organization
            valid_users = await User.find_many({
                "_id": {"$in": object_ids},
                "org_id": org_id,
            })

            valid_user_ids = [str(user.id) for user in valid_users]
            logger.info(
                f"Validated {len(valid_user_ids)} out of {len(tagged_user_ids)} tagged users"
            )
            return valid_user_ids  # type: ignore

        except Exception as e:
            await self.handle_error(
                e, {"tagged_user_ids": tagged_user_ids, "org_id": str(org_id)}
            )
            return []

    async def get_notes_by_user(
        self,
        user_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        skip: int = 0,
        limit: int = 100,
    ) -> tuple[List[DealNote], int]:
        """Get notes where a user is tagged."""
        try:
            if isinstance(user_id, str):
                user_id = ObjectId(user_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            # Build query
            query = {
                "tagged_user_ids": user_id,
                "org_id": org_id,
                "deleted_at": None,
            }

            # Get total count
            total = await DealNote.count(query)

            # Get notes with pagination and sorting
            pipeline = [
                {"$match": query},
                {
                    "$sort": {
                        "pinned": -1,  # Pinned notes first
                        "created_at": -1,  # Then by creation date descending
                    }
                },
                {"$skip": skip},
                {"$limit": limit},
            ]

            notes_data = await DealNote.aggregate(pipeline)
            notes = [DealNote(**note) for note in notes_data]

            return notes, total

        except Exception as e:
            await self.handle_error(e, {"user_id": str(user_id), "org_id": str(org_id)})
            return [], 0

    async def get_note_count(
        self, deal_id: Union[str, ObjectId], org_id: Union[str, ObjectId]
    ) -> int:
        """Get the total number of notes for a deal."""
        try:
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            count = await DealNote.count({
                "deal_id": deal_id,
                "org_id": org_id,
                "deleted_at": None,
            })
            return count

        except Exception as e:
            await self.handle_error(e, {"deal_id": str(deal_id), "org_id": str(org_id)})
            return 0
