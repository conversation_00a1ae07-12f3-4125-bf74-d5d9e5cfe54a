"""
Onboarding API endpoints for invite code generation and organization setup.

This module handles the viral onboarding flow:
1. Invite code generation
2. Organization creation
3. User profile setup
4. Peer invitations

This module provides endpoints for creating default forms and theses for new organizations.
"""

from typing import Any, Dict

from fastapi import Depends, HTTPException, status
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.api.base import BaseAPIRouter
from app.core.config import settings
from app.core.logging import get_logger
from app.dependencies.auth import get_current_user, get_database
from app.dependencies.org import get_org_context
from app.models.auth import TokenData
from app.models.user import User
from app.schemas.auth import (
    CreateInviteCodeRequest,
    CreateInviteCodeResponse,
    OnboardingCompleteResponse,
    OnboardingStepOneRequest,
    OnboardingStepTwoRequest,
    PeerInviteRequest,
)
from app.services.factory import (
    get_demo_deal_service,
    get_form_service,
    get_onboarding_service,
    get_role_service,
    get_thesis_service,
)
from app.services.form.interfaces import FormServiceInterface
from app.services.onboarding.interface import IOnboardingService
from app.services.thesis.interfaces import ThesisServiceInterface
from app.utils.common import PyObjectId

logger = get_logger(__name__)

# Public router for invite code generation and onboarding
public_router = BaseAPIRouter(
    prefix="", disable_auth=True, require_org=False, tags=["onboarding"]
)

# Protected router for peer invites
protected_router = BaseAPIRouter(prefix="", tags=["onboarding"])

demo_router = BaseAPIRouter(prefix="/demo", tags=["demo"])


def serialize_deal_for_response(deal: Any) -> Dict[str, Any]:
    """
    Serialize a deal object for API response, ensuring all ObjectIds are converted to strings.
    """
    try:
        # Convert the deal to a dict if it's a model
        if hasattr(deal, "model_dump"):
            deal_dict = deal.model_dump()
        elif hasattr(deal, "dict"):
            deal_dict = deal.dict()
        else:
            deal_dict = dict(deal)

        # Ensure ID fields are strings
        if "id" in deal_dict and deal_dict["id"]:
            deal_dict["id"] = str(deal_dict["id"])
        if "_id" in deal_dict and deal_dict["_id"]:
            deal_dict["_id"] = str(deal_dict["_id"])

        # Handle scoring field specifically
        if "scoring" in deal_dict and deal_dict["scoring"]:
            scoring = deal_dict["scoring"]
            if isinstance(scoring, dict):
                # Convert any ObjectIds in scoring to strings
                for key, value in scoring.items():
                    if (
                        hasattr(value, "__class__")
                        and value.__class__.__name__ == "ObjectId"
                    ):
                        scoring[key] = str(value)
                    elif isinstance(value, dict):
                        # Recursively handle nested dicts
                        for nested_key, nested_value in value.items():
                            if (
                                hasattr(nested_value, "__class__")
                                and nested_value.__class__.__name__ == "ObjectId"
                            ):
                                value[nested_key] = str(nested_value)

        # Handle other fields that might contain ObjectIds
        for key, value in deal_dict.items():
            if hasattr(value, "__class__") and value.__class__.__name__ == "ObjectId":
                deal_dict[key] = str(value)
            elif isinstance(value, list):
                # Handle lists that might contain ObjectIds
                deal_dict[key] = [
                    str(item)
                    if hasattr(item, "__class__")
                    and item.__class__.__name__ == "ObjectId"
                    else item
                    for item in value
                ]

        return deal_dict
    except Exception as e:
        logger.error(f"Error serializing deal: {str(e)}")
        # Fallback: return basic deal info
        return {
            "id": str(getattr(deal, "id", "")),
            "company_name": getattr(deal, "company_name", ""),
            "stage": getattr(deal, "stage", ""),
            "sector": getattr(deal, "sector", []),
            "status": getattr(deal, "status", ""),
            "tags": getattr(deal, "tags", []),
            "created_at": getattr(deal, "created_at", 0),
        }


@public_router.post(
    "/auth/public/create-invite-code", response_model=CreateInviteCodeResponse
)
async def create_invite_code(
    request: CreateInviteCodeRequest,
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Generate a unique invite code for organization onboarding.

    This endpoint creates a single-use invite code that allows the specified
    email to create a new organization and user account.
    """
    try:
        logger.info(f"Creating invite code for {request.email}")

        invite_code = await onboarding_service.create_invite_code(
            email=request.email, org_name=request.org_name
        )

        # Construct the onboarding URL
        base_url = settings.FRONTEND_URL.rstrip("/")
        if not base_url:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Frontend URL is not configured",
            )
        onboard_url = f"{base_url}/onboard/{invite_code.code}"

        # Send the invite email
        from app.services.factory import get_email_service

        email_service = await get_email_service()

        await email_service.send_invite_code_email(
            email=invite_code.email,
            invite_code=invite_code.code,
            onboard_url=onboard_url,
            org_name=invite_code.org_name,
        )

        return CreateInviteCodeResponse(
            code=invite_code.code,
            email=invite_code.email,
            expires_at=invite_code.expires_at,
            onboard_url=onboard_url,
        )

    except Exception as e:
        logger.error(f"Error creating invite code: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create invite code",
        )


@public_router.get("/onboard/{code}/validate")
async def validate_invite_code(
    code: str,
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Validate an invite code and return associated information.

    This endpoint checks if an invite code is valid and returns
    the associated email and organization name (if any).
    """
    try:
        invite_code = await onboarding_service.validate_invite_code(code)

        return {
            "valid": True,
            "email": invite_code.email,
            "org_name": invite_code.org_name,
            "expires_at": invite_code.expires_at,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating invite code: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate invite code",
        )


@public_router.post("/onboard/{code}/step-1")
async def onboarding_step_one(
    code: str,
    request: OnboardingStepOneRequest,
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Step 1: Create organization during onboarding.

    This endpoint creates the organization with the provided details
    and validates the subdomain availability.
    """
    try:
        logger.info(f"Onboarding step 1 for code {code}")

        # Validate invite code
        invite_code = await onboarding_service.validate_invite_code(code)

        # Create organization
        organization = await onboarding_service.create_organization(
            invite_code=invite_code,
            org_name=request.org_name,
            subdomain=request.subdomain,
            website=request.website,
            logo_url=request.logo_url,
        )
        role_service = await get_role_service()
        gp_role, analyst_role = await role_service.create_default_roles(  # type: ignore
            str(organization.id)
        )

        return {
            "success": True,
            "org_id": str(organization.id),
            "message": "Organization created successfully",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in onboarding step 1: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create organization",
        )


@public_router.post("/onboard/{code}/step-2", response_model=OnboardingCompleteResponse)
async def onboarding_step_two(
    code: str,
    request: OnboardingStepTwoRequest,
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Step 2: Create user profile and complete onboarding.

    This endpoint creates the user profile, completes the onboarding
    process, and returns authentication tokens.
    """
    try:
        logger.info(f"Onboarding step 2 for code {code}")

        # Validate invite code
        invite_code = await onboarding_service.validate_invite_code(code)

        # Get the organization created in step 1
        from app.models.organization import Organization

        organization = await Organization.find_one({"contact_email": invite_code.email})

        if not organization:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Organization not found. Please complete step 1 first.",
            )

        # Create user profile
        user = await onboarding_service.create_user_profile(
            invite_code=invite_code,
            organization=organization,
            name=request.name,
            password=request.password,
            profile_picture_url=request.profile_picture_url,
        )

        # Complete onboarding and get tokens
        result = await onboarding_service.complete_onboarding(
            invite_code=invite_code, user=user, organization=organization
        )

        return OnboardingCompleteResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in onboarding step 2: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to complete onboarding",
        )


@public_router.get("/onboard/check-subdomain/{subdomain}")
async def check_subdomain_availability(
    subdomain: str,
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Check if a subdomain is available for use.

    This endpoint validates subdomain format and checks availability.
    """
    try:
        available = await onboarding_service.check_subdomain_availability(subdomain)

        return {"subdomain": subdomain, "available": available}

    except Exception as e:
        logger.error(f"Error checking subdomain availability: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check subdomain availability",
        )


@protected_router.post("/auth/public/invite-peers")
async def invite_peers(
    request: PeerInviteRequest,
    current_user: TokenData = Depends(get_current_user),
    onboarding_service: IOnboardingService = Depends(get_onboarding_service),
):
    """
    Send invitations to peers to join the organization.

    This endpoint allows users to invite colleagues to their organization
    after completing the onboarding process.
    """
    try:
        logger.info(f"Sending peer invites from user {current_user.user_id}")

        result = await onboarding_service.send_peer_invites(
            org_id=current_user.org_id,
            invited_by=current_user.user_id,
            emails=request.emails,
            role_id=request.role_id,
            message=request.message,
        )

        return result

    except Exception as e:
        logger.error(f"Error sending peer invites: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send peer invites",
        )


@demo_router.post("/create-default-form", response_model=Dict[str, Any])
async def create_default_form(
    org_context: tuple[str, bool] = Depends(get_org_context),
    form_service: FormServiceInterface = Depends(get_form_service),
    db: AsyncIOMotorDatabase = Depends(get_database),
) -> Dict[str, Any]:
    """
    Create a default form for the organization.

    This endpoint creates a comprehensive demo form that demonstrates all available
    question types, visibility conditions, and validation rules.

    Returns:
        Dictionary containing the created form details
    """
    try:
        org_id = org_context[0]

        # Create default form
        default_form = await form_service.create_default_form(PyObjectId(org_id))

        if not default_form:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create default form",
            )

        logger.info(
            f"Created default form {default_form.get('_id')} for organization {org_id}"
        )

        return {
            "success": True,
            "message": "Default form created successfully",
            "form": default_form,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating default form: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create default form: {str(e)}",
        )


@demo_router.post("/create-demo-thesis", response_model=Dict[str, Any])
async def create_demo_thesis(
    form_id: str,
    org_context: tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
    db: AsyncIOMotorDatabase = Depends(get_database),
) -> Dict[str, Any]:
    """
    Create a demo thesis for the specified form.

    This endpoint creates a comprehensive demo thesis that demonstrates all available
    scoring features including match rules, scoring rules, bonus rules, and exclusion filters.

    Args:
        form_id: ID of the form to create thesis for

    Returns:
        Dictionary containing the created thesis details
    """
    try:
        org_id = org_context[0]

        # Create demo thesis
        demo_thesis = await thesis_service.create_demo_thesis(
            PyObjectId(org_id), form_id
        )

        if not demo_thesis:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create demo thesis",
            )

        logger.info(f"Created demo thesis {demo_thesis.id} for organization {org_id}")

        return {
            "success": True,
            "message": "Demo thesis created successfully",
            "thesis": {
                "id": str(demo_thesis.id),
                "name": demo_thesis.name,
                "description": demo_thesis.description,
                "status": demo_thesis.status,
                "is_active": demo_thesis.is_active,
            },
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating demo thesis: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create demo thesis: {str(e)}",
        )


@demo_router.post("/setup-organization", response_model=Dict[str, Any])
async def setup_organization(
    org_context: tuple[str, bool] = Depends(get_org_context),
    form_service: FormServiceInterface = Depends(get_form_service),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
    demo_deal_service=Depends(get_demo_deal_service),
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
) -> Dict[str, Any]:
    """
    Complete organization setup - creates default form, demo thesis, and demo deals.

    This endpoint is the main onboarding endpoint that creates everything needed
    for a new organization to start using the platform effectively.

    Returns:
        Dictionary containing the created form, thesis, and demo deals details
    """
    try:
        org_id = org_context[0]

        # Step 1: Create default form
        logger.info(f"Creating default form for organization {org_id}")
        default_form = await form_service.create_default_form(PyObjectId(org_id))

        if not default_form:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create default form",
            )

        form_id = default_form.get("_id")
        logger.info(f"Created default form {form_id}")

        # Step 2: Create demo thesis
        logger.info(f"Creating demo thesis for form {form_id}")
        demo_thesis = await thesis_service.create_demo_thesis(
            PyObjectId(org_id), str(form_id)
        )

        if not demo_thesis:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create demo thesis",
            )

        logger.info(f"Created demo thesis {demo_thesis.id}")

        # Step 3: Create demo deals to demonstrate value
        logger.info(f"Creating demo deals for form {form_id}")
        try:
            demo_deals = await demo_deal_service.create_multiple_demo_deals(
                org_id=org_id,
                form_id=str(form_id),
                created_by=current_user.id,
                count=1,  # Create 3 demo deals to show variety
            )
            logger.info(f"Created {len(demo_deals)} demo deals")
        except Exception as e:
            logger.error(f"Error creating demo deals: {str(e)}", exc_info=True)
            # Continue without demo deals - don't fail the entire setup
            demo_deals = []

        # Serialize deals with error handling
        serialized_deals = []
        for deal in demo_deals:
            try:
                serialized_deal = serialize_deal_for_response(deal)
                serialized_deals.append(serialized_deal)
            except Exception as e:
                logger.error(f"Error serializing deal {deal.id}: {str(e)}")
                # Add basic deal info as fallback
                serialized_deals.append({
                    "id": str(deal.id),
                    "company_name": getattr(deal, "company_name", "Demo Company"),
                    "stage": getattr(deal, "stage", "seed"),
                    "sector": getattr(deal, "sector", ["saas"]),
                    "status": getattr(deal, "status", "new"),
                    "tags": getattr(deal, "tags", ["demo"]),
                    "created_at": getattr(deal, "created_at", 0),
                })

        return {
            "success": True,
            "message": "Organization setup completed successfully",
            "form": {
                "id": str(form_id),
                "name": default_form.get("name"),
                "description": default_form.get("description"),
                "sections": len(default_form.get("sections", [])),
                "default_sections": len(default_form.get("default_section_ids", [])),
            },
            "thesis": {
                "id": str(demo_thesis.id),
                "name": demo_thesis.name,
                "description": demo_thesis.description,
                "status": demo_thesis.status,
                "is_active": demo_thesis.is_active,
            },
            "demo_deals": {
                "count": len(serialized_deals),
                "deals": serialized_deals,
            },
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error setting up organization: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to setup organization: {str(e)}",
        )


@demo_router.post("/create-demo-deal", response_model=Dict[str, Any])
async def create_demo_deal(
    form_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    demo_deal_service=Depends(get_demo_deal_service),
) -> Dict[str, Any]:
    """
    Create a demo deal with realistic data and proper scoring.

    This endpoint creates a demo deal that demonstrates the platform's
    scoring and analysis capabilities for new users.

    Args:
        form_id: ID of the form to create demo deal for

    Returns:
        Dictionary containing the created demo deal details
    """
    try:
        org_id = org_context[0]

        # Create demo deal
        demo_deal = await demo_deal_service.create_demo_deal(
            org_id=org_id,
            form_id=form_id,
            created_by=current_user.id,
        )

        if not demo_deal:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create demo deal",
            )

        logger.info(f"Created demo deal {demo_deal.id} for organization {org_id}")

        return {
            "success": True,
            "message": "Demo deal created successfully",
            "deal": serialize_deal_for_response(demo_deal),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating demo deal: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create demo deal: {str(e)}",
        )


@demo_router.post("/create-multiple-demo-deals", response_model=Dict[str, Any])
async def create_multiple_demo_deals(
    form_id: str,
    count: int = 3,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    demo_deal_service=Depends(get_demo_deal_service),
) -> Dict[str, Any]:
    """
    Create multiple demo deals with different characteristics.

    This endpoint creates multiple demo deals to give new users a better
    understanding of the platform's capabilities.

    Args:
        form_id: ID of the form to create demo deals for
        count: Number of demo deals to create (default: 3, max: 5)

    Returns:
        Dictionary containing the created demo deals details
    """
    try:
        org_id = org_context[0]

        # Limit count to prevent abuse
        count = min(max(count, 1), 5)

        # Create multiple demo deals
        demo_deals = await demo_deal_service.create_multiple_demo_deals(
            org_id=org_id,
            form_id=form_id,
            created_by=current_user.id,
            count=count,
        )

        logger.info(f"Created {len(demo_deals)} demo deals for organization {org_id}")

        # Serialize deals with error handling
        serialized_deals = []
        for deal in demo_deals:
            try:
                serialized_deal = serialize_deal_for_response(deal)
                serialized_deals.append(serialized_deal)
            except Exception as e:
                logger.error(f"Error serializing deal {deal.id}: {str(e)}")
                # Add basic deal info as fallback
                serialized_deals.append({
                    "id": str(deal.id),
                    "company_name": getattr(deal, "company_name", "Demo Company"),
                    "stage": getattr(deal, "stage", "seed"),
                    "sector": getattr(deal, "sector", ["saas"]),
                    "status": getattr(deal, "status", "new"),
                    "tags": getattr(deal, "tags", ["demo"]),
                    "created_at": getattr(deal, "created_at", 0),
                })

        return {
            "success": True,
            "message": f"Created {len(serialized_deals)} demo deals successfully",
            "deals": serialized_deals,
            "count": len(serialized_deals),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating multiple demo deals: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create demo deals: {str(e)}",
        )
