import React, { useEffect, useRef } from 'react';
import { OrgUser } from '@/lib/types/deal-notes';

interface MentionDropdownProps {
  users: OrgUser[];
  index: number;
  target: any; // Slate Range
  onSelect: (user: OrgUser) => void;
  onClose: () => void;
}

export function MentionDropdown({ users, index, target, onSelect, onClose }: MentionDropdownProps) {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (ref.current) {
      const el = ref.current.querySelector('[data-active]');
      if (el) (el as HTMLElement).scrollIntoView({ block: 'nearest' });
    }
  }, [index]);

  // TODO: Position dropdown at cursor using Slate/DOM APIs (portal if needed)
  // For now, render below input

  return (
    <div
      ref={ref}
      className="absolute z-50 mt-1 w-64 bg-white border border-gray-200 rounded-md shadow-lg overflow-auto max-h-60"
      style={{ left: 0, top: '100%' }}
      role="listbox"
      tabIndex={-1}
    >
      {users.length === 0 ? (
        <div className="px-4 py-2 text-sm text-muted-foreground">No users found</div>
      ) : (
        users.map((user, i) => (
          <div
            key={user._id}
            data-active={i === index ? true : undefined}
            className={`flex items-center gap-2 px-4 py-2 cursor-pointer text-sm ${
              i === index ? 'bg-blue-100 text-blue-800' : ''
            }`}
            onMouseDown={e => {
              e.preventDefault();
              onSelect(user);
            }}
            role="option"
            aria-selected={i === index}
          >
            <span className="font-medium">{user.name}</span>
            <span className="text-xs text-muted-foreground">{user.email}</span>
          </div>
        ))
      )}
    </div>
  );
} 