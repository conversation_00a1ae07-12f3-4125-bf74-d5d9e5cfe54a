import { useState, useEffect } from 'react';
import { DashboardAPI, DealInsights } from '@/lib/api/dashboard-api';

interface UseDashboardInsightsParams {
  date_range_start?: number;
  date_range_end?: number;
  assigned_user_ids?: string;
}

interface UseDashboardInsightsReturn {
  data: DealInsights | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useDashboardInsights(params?: UseDashboardInsightsParams): UseDashboardInsightsReturn {
  const [data, setData] = useState<DealInsights | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchInsights = async () => {
    try {
      setLoading(true);
      setError(null);
      const insights = await DashboardAPI.getInsights(params);
      setData(insights);
    } catch (err) {
      console.error('Error fetching dashboard insights:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch insights');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInsights();
  }, [params?.date_range_start, params?.date_range_end, params?.assigned_user_ids]);

  return {
    data,
    loading,
    error,
    refetch: fetchInsights,
  };
} 