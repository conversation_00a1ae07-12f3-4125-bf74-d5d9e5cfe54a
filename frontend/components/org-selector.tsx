"use client"

import * as React from "react"
import {Check, ChevronsUpDown} from "lucide-react"

import {cn} from "@/lib/utils"
import {But<PERSON>} from "@/components/ui/button"
import {Command, CommandEmpty, CommandGroup, CommandInput, CommandItem,} from "@/components/ui/command"
import {Popover, PopoverContent, PopoverTrigger,} from "@/components/ui/popover"
import {useAuth} from "@/lib/auth-context"

interface Organization {
  id: string
  name: string
  subdomain: string
  description?: string
  logo_url?: string
}

export function OrganizationSelector() {
  const {userOrganizations, orgId, setCurrentOrg} = useAuth()
  const [open, setOpen] = React.useState(false)

  // Find the current organization
  const currentOrg = userOrganizations.find(org => org.id === orgId) || userOrganizations[0]

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          {currentOrg ? (
            <div className="flex items-center">
              {currentOrg.logo_url ? (
                <img
                  src={currentOrg.logo_url}
                  alt={currentOrg.name}
                  className="mr-2 size-5 rounded-full"
                />
              ) : (
                <div
                  className="mr-2 flex size-5 items-center justify-center rounded-full bg-primary text-xs text-primary-foreground">
                  {currentOrg.name.charAt(0)}
                </div>
              )}
              <span>{currentOrg.name}</span>
            </div>
          ) : (
            "Select organization"
          )}
          <ChevronsUpDown className="ml-2 size-4 shrink-0 opacity-50"/>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0">
        <Command>
          <CommandInput placeholder="Search organization..."/>
          <CommandEmpty>No organization found.</CommandEmpty>
          <CommandGroup>
            {userOrganizations.map((org) => (
              <CommandItem
                key={org.name}
                value={org.id}
                onSelect={() => {
                  setCurrentOrg(org.id)
                  setOpen(false)
                }}
              >
                <div className="flex items-center">
                  {org.logo_url ? (
                    <img
                      src={org.logo_url}
                      alt={org.name}
                      className="mr-2 size-5 rounded-full"
                    />
                  ) : (
                    <div
                      className="mr-2 flex size-5 items-center justify-center rounded-full bg-primary text-xs text-primary-foreground">
                      {org.name.charAt(0)}
                    </div>
                  )}
                  <span>{org.name}</span>
                </div>
                <Check
                  className={cn(
                    "ml-auto size-4",
                    orgId === org.id ? "opacity-100" : "opacity-0"
                  )}
                />
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
