# Backend (TractionX Backend)

This folder contains the entire backend for the TractionX project. It is part of a monorepo (with a separate frontend folder) and is intended to be self-contained (including Docker, env, and configs) so that it can later be split into its own repository.

- **app/** – Backend source code.
- **tests/** – Backend tests.
- **pyproject.toml, poetry.lock** – Backend dependency and build configs.
- **Docker<PERSON>le, docker-compose.yml, .dockerignore** – Backend Docker configuration.
- **.coveragerc, pytest.ini, .pre-commit-config.yaml** – Backend tooling configs.
- **.env** (if present) – Backend environment variables.

 (If you run commands from the root, update your paths accordingly.)

## Features

- FastAPI-based REST API
- MongoDB database with Motor (async)
- Redis caching layer
- Asynchronous job queue system
- Sharing framework for resources
- Event-driven trigger system
- JWT-based authentication with RBAC
- Structured logging
- Docker support
- Comprehensive testing setup
- Code quality tools (black, isort, mypy)
- Swagger API documentation

## Prerequisites

- Python 3.11+
- Poetry
- Docker and Docker Compose
- MongoDB
- Redis

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd x-app
```

2. Install dependencies:
```bash
make install
```

3. Create a `.env` file:
```bash
cp .env.example .env
```

4. Update the `.env` file with your configuration.

## Development

Start the development server:
```bash
make start
```

Start the worker for background jobs:
```bash
make start-worker
```

The API will be available at `http://localhost:8000`

API documentation is available at:
- Swagger UI: `http://localhost:8000/docs`
- OpenAPI JSON: `http://localhost:8000/openapi.json`

## Docker

Start the entire stack:
```bash
make docker-up
```

Start the stack in detached mode:
```bash
make docker-up-detached
```

View logs:
```bash
make docker-logs
```

Stop the stack:
```bash
make docker-down
```

The Docker setup includes:
- API server
- Background worker for queue processing
- MongoDB database
- Redis for caching and queue

## Testing

Run tests:
```bash
make test
```

## Code Quality

Format code:
```bash
make format
```

Run linters:
```bash
make lint
```

## Project Structure

```
x-app/
├── app/
│   ├── api/
│   │   └── v1/
│   ├── core/
│   ├── models/
│   ├── schemas/
│   ├── services/
│   │   ├── queue/        # Queue system
│   │   ├── sharing/      # Sharing framework
│   │   ├── trigger/      # Trigger system
│   │   └── qualifier_form/ # Qualifier form module
│   ├── dependencies/
│   ├── utils/
│   └── worker.py         # Worker script for queue processing
├── tests/
├── Dockerfile
├── docker-compose.yml
├── Makefile
├── pyproject.toml
└── README.md
```

## Contributing

1. Create a new branch
2. Make your changes
3. Run tests and linters
4. Submit a pull request

## License

[Your License Here]