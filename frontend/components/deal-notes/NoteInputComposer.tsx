"use client"

/**
 * Note Input Composer
 * 
 * Slate.js-based editor for composing deal notes with mention support
 */

import React, { useCallback, useMemo, useState, useRef } from 'react';
import {
  createEditor,
  Descendant,
  Editor,
  Transforms,
  Element as SlateElement,
  Range,
} from 'slate';
import {
  Slate,
  Editable,
  withReact,
  useSlate,
  ReactEditor,
} from 'slate-react';
import { withHistory } from 'slate-history';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Loader2, Send, Pin, PinOff } from 'lucide-react';
import { cn } from '@/lib/utils';
import { NoteContent, OrgUser } from '@/lib/types/deal-notes';
import { 
  createInitialContent, 
  validateNoteContent, 
  insertMention,
  getMentionSearchQuery,
  extractMentions 
} from '@/lib/utils/mentions';
import { renderNode } from './renderers/renderNode';
import { MentionAutocomplete } from './MentionAutocomplete';
import { useToast } from '@/components/ui/use-toast';

interface NoteInputComposerProps {
  dealId: string;
  orgUsers: OrgUser[];
  onSubmit: (content: NoteContent, pinned: boolean) => Promise<void>;
  className?: string;
}

const withMentions = (editor: ReactEditor) => {
  const { isInline, isVoid } = editor;

  editor.isInline = (element) => {
    return element.type === 'mention' ? true : isInline(element);
  };

  editor.isVoid = (element) => {
    return element.type === 'mention' ? true : isVoid(element);
  };

  return editor;
};

export function NoteInputComposer({
  dealId,
  orgUsers,
  onSubmit,
  className,
}: NoteInputComposerProps) {
  const [value, setValue] = useState<NoteContent>(createInitialContent());
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [pinned, setPinned] = useState(false);
  const [target, setTarget] = useState<Range | null>(null);
  const [search, setSearch] = useState('');
  const [index, setIndex] = useState(0);
  const { toast } = useToast();

  const editor = useMemo(
    () => withMentions(withHistory(withReact(createEditor()))),
    []
  );

  const renderElement = useCallback((props: any) => renderNode(props), []);

  const handleChange = useCallback((newValue: Descendant[]) => {
    setValue(newValue as NoteContent);

    const { selection } = editor;

    if (selection && (Range.isCollapsed as any)(selection)) {
      const [start] = (Range.edges as any)(selection);
      const wordBefore = (Editor.before as any)(editor, start, { unit: 'word' });
      const before = wordBefore && (Editor.before as any)(editor, wordBefore);
      const beforeRange = before && (Editor.range as any)(editor, before, start);
      const beforeText = beforeRange && (Editor.string as any)(editor, beforeRange);
      const beforeMatch = beforeText && beforeText.match(/^@(+)$/);
      const after = (Editor.after as any)(editor, start);
      const afterRange = (Editor.range as any)(editor, start, after);
      const afterText = (Editor.string as any)(editor, afterRange);
      const afterMatch = afterText && afterText.match(/^(|$)/);

      if (beforeMatch && afterMatch) {
        setTarget(beforeRange);
        setSearch(beforeMatch[1]);
        setIndex(0);
        return;
      }
    }

    setTarget(null);
  }, [editor]);

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      // Robust backspace handling for mentions
      if (
        event.key === 'Backspace' &&
        editor.selection &&
        Range.isCollapsed(editor.selection)
      ) {
        const { selection } = editor;
        const [match] = (Editor.nodes as any)(editor, {
          match: (n: any) =>
            !(Editor.isEditor as any)(n) &&
            (SlateElement.isElement as any)(n) &&
            n.type === 'mention',
        });
        if (match) {
          event.preventDefault();
          (Transforms.delete as any)(editor);
          return;
        }
      }

      if (target) {
        switch (event.key) {
          case 'ArrowDown': {
            event.preventDefault();
            const prevIndex = index >= orgUsers.length - 1 ? 0 : index + 1;
            setIndex(prevIndex);
            break;
          }
          case 'ArrowUp': {
            event.preventDefault();
            const nextIndex = index <= 0 ? orgUsers.length - 1 : index - 1;
            setIndex(nextIndex);
            break;
          }
          case 'Tab':
          case 'Enter': {
            event.preventDefault();
            const filteredUsers = orgUsers.filter(user =>
              user.name.toLowerCase().includes(search.toLowerCase()) ||
              user.email.toLowerCase().includes(search.toLowerCase())
            );
            if (filteredUsers[index]) {
              insertMention(editor, filteredUsers[index]);
            }
            setTarget(null);
            break;
          }
          case 'Escape': {
            event.preventDefault();
            setTarget(null);
            break;
          }
        }
      }

      // Submit on Cmd+Enter
      if (event.key === 'Enter' && (event.metaKey || event.ctrlKey)) {
        event.preventDefault();
        handleSubmit();
      }
    },
    [target, index, search, orgUsers, editor]
  );

  const handleSubmit = async () => {
    if (isSubmitting) return;

    const validation = validateNoteContent(value, orgUsers);
    if (!validation.isValid) {
      toast({
        title: 'Invalid note',
        description: validation.errors.join(', '),
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(value, pinned);
      setValue(createInitialContent());
      setPinned(false);
      setTarget(null);
      toast({
        title: 'Note added',
        description: 'Your note has been saved successfully.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save note. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleMentionSelect = useCallback(
    (user: OrgUser) => {
      insertMention(editor, user);
      setTarget(null);
    },
    [editor]
  );

  const hasContent = useMemo(() => {
    return value.some(block => {
      if (block.type !== 'paragraph') return false;
      return block.children.some(child => {
        if ('type' in child && child.type === 'mention') return true;
        if ('text' in child && typeof child.text === 'string' && child.text.trim().length > 0) return true;
        return false;
      });
    });
  }, [value]);

  return (
    <div className={cn('space-y-4', className)}>
      <div className="relative">
        <Slate editor={editor} initialValue={value} onChange={handleChange}>
          <div className="relative">
            <Editable
              renderElement={renderElement}
              onKeyDown={handleKeyDown}
              placeholder="Type your note... Use @ to mention team members"
              className="min-h-[120px] max-h-[300px] overflow-y-auto p-4 border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              style={{
                resize: 'vertical',
              }}
            />
            
            {target && (
              <MentionAutocomplete
                target={target}
                search={search}
                index={index}
                users={orgUsers}
                onSelect={handleMentionSelect}
                onClose={() => setTarget(null)}
              />
            )}
          </div>
        </Slate>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setPinned(!pinned)}
            className={cn(
              'h-8 px-2',
              pinned && 'bg-primary/10 text-primary'
            )}
          >
            {pinned ? (
              <>
                <Pin className="w-4 h-4 mr-1" />
                Pinned
              </>
            ) : (
              <>
                <PinOff className="w-4 h-4 mr-1" />
                Pin
              </>
            )}
          </Button>
          
          {pinned && (
            <Badge variant="secondary" className="text-xs">
              Will pin to top
            </Badge>
          )}
        </div>

        <Button
          onClick={handleSubmit}
          disabled={!hasContent || isSubmitting}
          size="sm"
          className="h-8"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Send className="w-4 h-4 mr-2" />
              Post Note
            </>
          )}
        </Button>
      </div>
    </div>
  );
} 