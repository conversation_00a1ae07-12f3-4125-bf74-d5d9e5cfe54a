"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export function CompetitorSection() {
  return (
    <Card className="border-0 bg-gradient-to-br from-orange-50 to-red-50 shadow-xl backdrop-blur-md border border-orange-100">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            {/* <div className="h-5 w-5 text-orange-600">🎯</div> */}
            <CardTitle className="text-lg font-semibold text-gray-900">
              Competitor Benchmarking
            </CardTitle>
          </div>
          <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-700">
            Coming Soon
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="flex items-start gap-3">
          {/* <div className="h-5 w-5 text-orange-500 mt-0.5">📈</div> */}
          <div className="space-y-2">
            <p className="text-sm text-gray-700">
              Benchmarking competitors and market positioning analysis coming soon.
            </p>
            <p className="text-sm text-gray-600">
              Get insights into competitive landscape, market share, and differentiation opportunities.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 