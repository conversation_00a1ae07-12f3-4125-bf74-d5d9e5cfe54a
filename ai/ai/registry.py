"""
Proprietary and system agent registration for TractionX AI Agent system.
"""

from typing import Dict, List, Optional, Any
import logging
from datetime import datetime
from ai.models import Agent, AgentType, AgentVisibility, AgentStatus, PyObjectId
from ai.db import get_ai_database

logger = logging.getLogger(__name__)


class AgentRegistry:
    """Registry for proprietary and system agents."""

    def __init__(self):
        self.db = None
        self._proprietary_agents = {}
        self._system_agents = {}

    async def initialize(self) -> None:
        """Initialize agent registry."""
        try:
            async for database in get_ai_database():
                self.db = database
                break

            if self.db is None:
                raise Exception("Could not get database connection")

            # Register proprietary agents
            await self._register_proprietary_agents()

            logger.info("Agent registry initialized")

        except Exception as e:
            logger.error(f"Failed to initialize agent registry: {e}")
            raise

    async def _register_proprietary_agents(self) -> None:
        """Register proprietary agents."""

        proprietary_agents = [
            {
                "name": "Thesis Matching Agent",
                "description": "Analyzes investment deals against thesis criteria using LLM and rules-based matching",
                "config": {
                    "model": "gpt-4",
                    "temperature": 0.3,
                    "max_tokens": 2000,
                    "focus_areas": ["market_size", "team", "traction", "business_model"]
                },
                "tools": ["thesis_matcher", "scoring_engine", "vector_search", "web_search"],
                "system_prompt": """You are an expert investment analyst specializing in thesis matching.
        Your role is to analyze investment opportunities against specific investment thesis criteria.

        Key responsibilities:
        1. Evaluate deals against thesis matching rules
        2. Calculate match scores based on weighted criteria
        3. Identify strengths and weaknesses
        4. Provide actionable recommendations

        Always be objective, data-driven, and provide clear reasoning for your assessments.""",
                "instructions": "Analyze the provided deal against the thesis criteria and provide a detailed match assessment.",
                "category": "investment_analysis",
                "tags": ["thesis", "matching", "scoring", "investment"]
            },
            {
                "name": "Founder Signals Agent",
                "description": "Analyzes founder backgrounds and signals for investment potential",
                "config": {
                    "model": "gpt-4",
                    "temperature": 0.4,
                    "max_tokens": 2000,
                    "focus_areas": ["experience", "track_record", "network", "domain_expertise"]
                },
                "tools": ["founder_analyzer", "network_analyzer", "experience_scorer", "web_search"],
                "system_prompt": """You are an expert in founder assessment and human capital evaluation.
        Your role is to analyze founder profiles and identify key signals for investment potential.

        Key responsibilities:
        1. Evaluate founder experience and track record
        2. Assess domain expertise and market knowledge
        3. Analyze network strength and connections
        4. Identify red flags and positive signals

        Focus on objective metrics while considering qualitative factors that indicate founder quality.""",
                "instructions": "Analyze the founder profiles and provide a comprehensive assessment of their investment potential.",
                "category": "founder_analysis",
                "tags": ["founders", "signals", "experience", "assessment"]
            },
            {
                "name": "Market Momentum Agent",
                "description": "Analyzes market trends, news, and momentum for investment timing",
                "config": {
                    "model": "gpt-4",
                    "temperature": 0.5,
                    "max_tokens": 2000,
                    "focus_areas": ["market_trends", "news_sentiment", "competitive_landscape", "timing"]
                },
                "tools": ["news_analyzer", "trend_detector", "sentiment_analyzer", "market_data", "web_search"],
                "system_prompt": """You are an expert market analyst specializing in trend analysis and momentum detection.
        Your role is to analyze market conditions and timing for investment opportunities.

        Key responsibilities:
        1. Analyze market trends and momentum
        2. Assess news sentiment and market perception
        3. Evaluate competitive landscape dynamics
        4. Provide timing recommendations

        Stay current with market developments and provide actionable insights for investment timing.""",
                "instructions": "Analyze the market conditions and momentum for the given investment opportunity.",
                "category": "market_analysis",
                "tags": ["market", "trends", "momentum", "timing"]
            },
            {
                "name": "News Intelligence Agent",
                "description": "Advanced news intelligence with multi-source search, real-time analysis, and AI summarization",
                "config": {
                    "model": "gpt-4",
                    "temperature": 0.3,
                    "max_tokens": 3000,
                    "focus_areas": ["news_analysis", "real_time_search", "source_aggregation", "summarization"]
                },
                "tools": ["news_intelligence", "web_search", "news_analyzer", "sentiment_analyzer"],
                "system_prompt": """You are an expert news intelligence analyst specializing in real-time information gathering and analysis.
        Your role is to provide comprehensive, up-to-date news intelligence for investment decisions.

        Key responsibilities:
        1. Search and aggregate news from multiple credible sources
        2. Analyze and categorize news by relevance and impact
        3. Provide structured summaries with clear source attribution
        4. Identify key trends, funding events, and market developments
        5. Assess news sentiment and potential investment implications

        Always prioritize:
        - Real-time, credible sources
        - Clear source attribution with clickable links
        - Structured categorization (Funding, Product, Business, Leadership, Market)
        - Actionable insights for investment decisions
        - Transparency about information sources and limitations

        When users ask for news, latest developments, or recent information about companies, markets, or sectors,
        automatically use the news intelligence tool to provide comprehensive, well-sourced analysis.""",
                "instructions": "Provide comprehensive news intelligence analysis for the given query, including multi-source search, categorization, and actionable insights.",
                "category": "news_intelligence",
                "tags": ["news", "intelligence", "real-time", "analysis", "sources"]
            }
        ]

        for agent_data in proprietary_agents:
            await self._register_agent(agent_data, AgentType.PROPRIETARY)


    async def _register_agent(self, agent_data: Dict[str, Any], agent_type: AgentType) -> None:
        """Register a single agent."""

        try:
            # Check if agent already exists
            existing = await self.db.agents.find_one({
                "name": agent_data["name"],
                "type": agent_type
            })

            if existing:
                logger.debug(f"Agent already exists: {agent_data['name']}")
                return

            # Create agent
            agent = Agent(
                org_id=PyObjectId("000000000000000000000000"),  # System org
                user_id=None,  # System agent
                name=agent_data["name"],
                description=agent_data["description"],
                type=agent_type,
                visibility=AgentVisibility.PUBLIC,
                status=AgentStatus.ACTIVE,
                config=agent_data["config"],
                tools=agent_data["tools"],
                model=agent_data["config"].get("model", "gpt-4"),
                temperature=agent_data["config"].get("temperature", 0.7),
                max_tokens=agent_data["config"].get("max_tokens", 4000),
                system_prompt=agent_data["system_prompt"],
                instructions=agent_data["instructions"],
                category=agent_data.get("category"),
                tags=agent_data.get("tags", [])
            )

            # Insert into database
            result = await self.db.agents.insert_one(
                agent.dict(by_alias=True, exclude_none=True)
            )

            agent_id = str(result.inserted_id)
            logger.info(f"Registered {agent_type} agent: {agent_data['name']} ({agent_id})")

        except Exception as e:
            logger.error(f"Failed to register agent {agent_data['name']}: {e}")

    async def get_proprietary_agents(self, org_id: str) -> List[Dict[str, Any]]:
        """Get all proprietary agents available to an organization."""

        try:
            cursor = self.db.agents.find({
                "type": AgentType.PROPRIETARY,
                "status": AgentStatus.ACTIVE,
                "visibility": AgentVisibility.PUBLIC
            })

            agents = []
            async for agent in cursor:
                # Convert ObjectIds to strings
                agent["_id"] = str(agent["_id"])
                agent["org_id"] = str(agent["org_id"])
                if agent.get("user_id"):
                    agent["user_id"] = str(agent["user_id"])

                agents.append(agent)

            return agents

        except Exception as e:
            logger.error(f"Failed to get proprietary agents: {e}")
            return []

    async def get_agent_by_name(self, name: str, agent_type: AgentType) -> Optional[Dict[str, Any]]:
        """Get agent by name and type."""

        try:
            agent = await self.db.agents.find_one({
                "name": name,
                "type": agent_type,
                "status": AgentStatus.ACTIVE
            })

            if agent:
                # Convert ObjectIds to strings
                agent["_id"] = str(agent["_id"])
                agent["org_id"] = str(agent["org_id"])
                if agent.get("user_id"):
                    agent["user_id"] = str(agent["user_id"])

            return agent

        except Exception as e:
            logger.error(f"Failed to get agent {name}: {e}")
            return None

    async def update_agent_usage(self, agent_id: str) -> None:
        """Update agent usage statistics."""

        logger.debug(f"Updating usage statistics for agent: {agent_id}")
        try:
            result = await self.db.agents.update_one(
                {"_id": PyObjectId(agent_id)},
                {
                    "$inc": {"usage_count": 1},
                    "$set": {"last_used": datetime.utcnow()}
                }
            )

            if result.modified_count > 0:
                logger.debug(f"Successfully updated usage for agent {agent_id}")
            else:
                logger.warning(f"Agent usage update had no effect for {agent_id} - agent may not exist")

        except Exception as e:
            logger.error(f"Failed to update agent usage {agent_id}: {e}")


# Global agent registry instance
agent_registry = AgentRegistry()
