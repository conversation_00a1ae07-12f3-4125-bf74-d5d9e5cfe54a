# TractionX Data Pipelines Operations Guide

## Getting Started

### Prerequisites
- <PERSON><PERSON> and <PERSON>er Compose
- Python 3.11+
- Poetry (for local development)
- Redis server
- PostgreSQL database
- AWS S3 access (for storage)

### Quick Start

#### Using Docker (Recommended)
```bash
# Clone and navigate to datapipelines directory
cd datapipelines

# Copy environment template
cp .env.example .env
# Edit .env with your configuration

# Start all services
docker compose up --build

# Check service health
curl http://localhost:8001/health
```

#### Local Development
```bash
# Install dependencies
make install

# Start API server
make start

# Start worker (in separate terminal)
make start-worker

# Run tests
make test
```

### Environment Configuration

#### Required Environment Variables
```bash
# Service Configuration
SERVICE_NAME=tractionx-datapipelines
VERSION=0.1.0
ENVIRONMENT=development
DEBUG=true

# API Configuration
API_HOST=0.0.0.0
API_PORT=8001
API_PREFIX=/api/v1

# Redis Configuration (Required)
REDIS_URL=redis://localhost:6379/0

# Database Configuration (Required)
DATABASE_URL=postgresql://user:password@localhost:5432/tractionx

# AWS S3 Configuration (Required)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
S3_BUCKET=tractionx-data

# External API Keys (Optional but recommended)
CLAY_API_KEY=your_clay_api_key
PDL_API_KEY=your_pdl_api_key
OPENAI_API_KEY=your_openai_api_key
BING_NEWS_API_KEY=your_bing_api_key

# Qdrant Configuration (Optional)
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=your_qdrant_api_key

# Webhook Security (Recommended)
WEBHOOK_SECRET=your_webhook_secret
CLAY_WEBHOOK_SECRET=your_clay_webhook_secret

# Worker Configuration
WORKER_CONCURRENCY=4
WORKER_POLL_INTERVAL=5
WORKER_TIMEOUT=1800
MAX_RETRIES=3

# Pipeline Configuration
PIPELINE_TIMEOUT=3600
ENABLE_COMPANY_ENRICHMENT=true
ENABLE_FOUNDER_ENRICHMENT=true
ENABLE_NEWS_AGGREGATION=true
ENABLE_EMBEDDING_GENERATION=true
```

## Component Deep Dive

### 1. FastAPI Application (`app/main.py`)

#### Purpose
The FastAPI application serves as the HTTP interface for the data pipeline service, providing:
- Webhook endpoints for external integrations
- Management API for pipeline control
- Health checks and monitoring endpoints
- OpenAPI documentation

#### Key Features
- **CORS Support**: Configurable cross-origin resource sharing
- **Custom Documentation**: Swagger UI and ReDoc integration
- **Error Handling**: Comprehensive exception handling
- **Middleware**: Request logging, authentication, rate limiting

#### API Endpoints

**Health and Status**
- `GET /health` - Service health check
- `GET /` - Service information
- `GET /docs` - Swagger UI documentation
- `GET /redoc` - ReDoc documentation

**Webhooks**
- `POST /webhooks/clay` - Clay enrichment webhook
- `POST /webhooks/generic` - Generic pipeline trigger
- `GET /webhooks/clay/status` - Clay webhook status
- `GET /webhooks/generic/status` - Generic webhook status

**Pipeline Management**
- `POST /api/v1/pipelines/trigger` - Manual pipeline trigger
- `GET /api/v1/pipelines/status/{job_id}` - Job status check
- `GET /api/v1/pipelines/stats` - Pipeline statistics

**System Status**
- `GET /api/v1/status/health` - Detailed health check
- `GET /api/v1/status/metrics` - System metrics
- `GET /api/v1/status/config` - Configuration status

### 2. RQ Worker (`app/worker.py`)

#### Purpose
The RQ worker is responsible for processing background jobs from the Redis queue. It provides:
- Asynchronous job processing
- Automatic retry logic
- Graceful shutdown handling
- Hot-reload capability for development

#### Worker Architecture
```python
# Worker initialization
redis_conn = Redis.from_url(settings.REDIS_URL)
worker = rq.Worker(['default'], connection=redis_conn)

# Job processing loop
worker.work()  # Blocks and processes jobs
```

#### Job Handlers
The worker automatically discovers and registers job handlers from `app/tasks/`:
- `enrich_company_data_sync` - Company enrichment
- `enrich_founder_data_sync` - Founder enrichment  
- `aggregate_news_data_sync` - News aggregation
- `generate_embeddings_sync` - Embedding generation
- `merge_enrichment_data_sync` - ETL merge

#### Development Features
- **Hot Reload**: Automatic restart on code changes
- **File Watching**: Monitors `app/` directory for modifications
- **Graceful Shutdown**: Handles SIGINT/SIGTERM signals
- **Process Management**: Automatic worker restart on crashes

### 3. Pipeline Classes (`app/pipelines/`)

#### Base Pipeline (`base.py`)
All pipelines inherit from `BasePipeline` which provides:

```python
class BasePipeline(ABC):
    async def initialize(self) -> None:
        # Set up resources, connections, clients
        
    async def process(self, input_data: Dict[str, Any]) -> ProcessingResult:
        # Main processing logic with error handling
        
    async def cleanup(self) -> None:
        # Clean up resources
        
    @abstractmethod
    async def _validate_input(self, input_data: Dict[str, Any]) -> ProcessingResult:
        # Validate input data
        
    @abstractmethod  
    async def _process_data(self, input_data: Dict[str, Any]) -> ProcessingResult:
        # Core processing logic
```

#### Company Enrichment Pipeline (`company.py`)
- **Purpose**: Enrich company data using Clay API
- **Input**: Company name, domain, basic information
- **Process**: 
  1. Validate input data
  2. Call Clay API for company enrichment
  3. Transform and normalize data
  4. Store raw data in S3
  5. Calculate confidence scores
- **Output**: Enriched company data with metadata

#### Founder Enrichment Pipeline (`founder.py`)
- **Purpose**: Enrich founder data using People Data Labs API
- **Input**: Founder names, LinkedIn profiles, email addresses
- **Process**:
  1. Extract founder information from company data
  2. Call PDL API for each founder
  3. Aggregate founder profiles and experience
  4. Store enriched founder data
- **Output**: Founder profiles with professional background

#### News Aggregation Pipeline (`news.py`)
- **Purpose**: Collect and analyze news about companies
- **Input**: Company name, keywords, date range
- **Process**:
  1. Query Bing News API
  2. Filter and rank articles by relevance
  3. Extract key information and sentiment
  4. Store articles and analysis
- **Output**: Curated news articles with analysis

#### Embedding Pipeline (`embedding.py`)
- **Purpose**: Generate vector embeddings for semantic search
- **Input**: Text content from all data sources
- **Process**:
  1. Extract text fields from company/founder/news data
  2. Generate embeddings using OpenAI API
  3. Store vectors in Qdrant
  4. Create search indices
- **Output**: Vector embeddings for similarity search

### 4. Task Functions (`app/tasks/`)

#### Task Structure
Each task follows a consistent pattern:

```python
async def enrich_company_data(job_data: Dict[str, Any]) -> Dict[str, Any]:
    """Async task implementation"""
    try:
        # Extract job parameters
        company_id = job_data.get("company_id")
        
        # Initialize pipeline
        pipeline = CompanyEnrichmentPipeline("company_enrichment")
        await pipeline.initialize()
        
        # Process data
        result = await pipeline.process(job_data)
        
        # Store results
        if result.success:
            # Store in database
            # Update job status
            
        return format_job_result(result)
        
    except Exception as e:
        # Error handling and logging
        return format_error_result(e)
    finally:
        # Cleanup resources
        await pipeline.cleanup()

def enrich_company_data_sync(job_data: Dict[str, Any]) -> Dict[str, Any]:
    """Synchronous wrapper for RQ compatibility"""
    return asyncio.run(enrich_company_data(job_data))
```

#### Error Handling
- **Retry Logic**: Automatic retries with exponential backoff
- **Error Logging**: Detailed error context and stack traces
- **Graceful Degradation**: Continue processing even if some enrichments fail
- **Status Tracking**: Update job status throughout processing

### 5. Storage Layer (`app/storage/`)

#### Storage Interfaces
The storage layer uses abstract interfaces for different storage types:

**RelationalStorageInterface** (RDS)
```python
async def upsert(self, table_name: str, data: Dict, key_fields: List[str]) -> str
async def query(self, table_name: str, filters: Dict) -> List[Dict]
async def bulk_insert(self, table_name: str, records: List[Dict]) -> List[str]
```

**ObjectStorageInterface** (S3)
```python
async def put_object(self, key: str, data: Union[bytes, str, Dict]) -> bool
async def get_object(self, key: str) -> Optional[bytes]
async def list_objects(self, prefix: str) -> List[str]
```

**VectorStorageInterface** (Qdrant)
```python
async def upsert_vectors(self, collection: str, vectors: List[Dict]) -> bool
async def search_vectors(self, collection: str, vector: List[float], limit: int) -> List[Dict]
async def create_collection(self, collection: str, vector_size: int) -> bool
```

#### Implementation Benefits
- **Abstraction**: Easy to swap storage providers
- **Testing**: Mock implementations for unit tests
- **Consistency**: Uniform error handling and retry logic
- **Performance**: Optimized for each storage type

### 6. Data Models (`app/models/`)

#### Model Hierarchy
```python
TractionXModel (Base)
├── CompanyData
│   ├── CompanyEnrichmentData
│   └── ClayCompanyData
├── FounderData
│   ├── FounderEnrichmentData
│   └── PDLFounderData
├── NewsData
│   ├── NewsArticle
│   └── BingNewsData
├── EmbeddingData
│   ├── TextEmbedding
│   └── EmbeddingMetadata
└── PipelineJobMetadata
```

#### Key Features
- **Type Safety**: Full Pydantic validation
- **Serialization**: JSON encoding/decoding
- **Documentation**: Auto-generated API docs
- **Validation**: Input validation and error messages

## Operational Procedures

### Deployment

#### Production Deployment
```bash
# Build production image
make prod-build

# Deploy to container registry
docker tag tractionx-datapipelines your-registry/tractionx-datapipelines:latest
docker push your-registry/tractionx-datapipelines:latest

# Deploy to production environment
# (Use your orchestration tool: Kubernetes, ECS, etc.)
```

#### Environment-Specific Configuration
- **Development**: Volume mounts, debug logging, hot reload
- **Staging**: Production-like setup with test data
- **Production**: Optimized builds, monitoring, security

### Monitoring

#### Health Checks
```bash
# Service health
curl http://localhost:8001/health

# Detailed health check
curl http://localhost:8001/api/v1/status/health

# Pipeline statistics
curl http://localhost:8001/api/v1/pipelines/stats
```

#### Log Monitoring
```bash
# View service logs
docker compose logs -f api worker

# Filter by component
docker compose logs -f api | grep "company_enrichment"

# Monitor job processing
docker compose logs -f worker | grep "job_id"
```

#### Metrics Collection
- **Job Metrics**: Success rates, processing times, queue lengths
- **Storage Metrics**: Connection health, operation latencies
- **API Metrics**: Request rates, response times, error rates
- **Resource Metrics**: CPU, memory, disk usage

### Troubleshooting

#### Common Issues

**Redis Connection Errors**
```bash
# Check Redis connectivity
redis-cli -u $REDIS_URL ping

# Verify Redis configuration
docker compose logs redis
```

**Database Connection Issues**
```bash
# Test database connection
psql $DATABASE_URL -c "SELECT 1;"

# Check database logs
docker compose logs postgres
```

**External API Failures**
```bash
# Check API key configuration
curl -H "Authorization: Bearer $CLAY_API_KEY" https://api.clay.com/health

# Review API rate limits and quotas
```

**Worker Not Processing Jobs**
```bash
# Check worker status
docker compose ps worker

# View worker logs
docker compose logs -f worker

# Restart worker
docker compose restart worker
```

#### Performance Optimization

**Queue Management**
- Monitor queue length and processing rates
- Scale worker instances based on load
- Implement priority queues for urgent jobs

**Storage Optimization**
- Use connection pooling for database operations
- Implement caching for frequently accessed data
- Optimize S3 operations with multipart uploads

**API Rate Limiting**
- Implement backoff strategies for external APIs
- Cache API responses when appropriate
- Monitor API usage and quotas

This operational guide provides the foundation for successfully deploying, monitoring, and maintaining the TractionX Data Pipelines service in production environments.
