# Company Enrichment Database Schema

This document outlines the complete database schema for the Apollo-based company enrichment pipeline in TractionX.

## Overview

The company enrichment system uses a normalized relational database schema with the following tables:

1. **companies_enrichment** - Main company data
2. **company_keywords** - Company keywords (1:N relationship)
3. **company_technologies** - Technology stack with categories (1:N relationship)
4. **company_department_counts** - Department head counts (1:N relationship)

## Table Schemas

### 1. companies_enrichment

Main company information table with core business data.

```sql
CREATE TABLE companies_enrichment (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id VARCHAR(255) UNIQUE NOT NULL,
    org_id VARCHAR(255) NOT NULL,
    
    -- Apollo-specific fields
    apollo_id VARCHAR(255),
    
    -- Basic company info
    name VARCHAR(500),
    domain VARCHAR(255),
    website TEXT,
    description TEXT,
    
    -- Business details
    industry VARCHAR(255),
    sub_industry VARCHAR(255),
    employee_count INTEGER,
    employee_count_range VARCHAR(50),
    founded_year INTEGER,
    
    -- Location
    headquarters VARCHAR(500),
    country VARCHAR(100),
    city VARCHAR(100),
    state VARCHAR(100),
    
    -- Financial data
    funding_total DECIMAL(15,2),
    funding_rounds INTEGER,
    last_funding_date DATE,
    last_funding_amount DECIMAL(15,2),
    valuation DECIMAL(15,2),
    revenue DECIMAL(15,2),
    
    -- Social presence
    linkedin_url TEXT,
    twitter_url TEXT,
    facebook_url TEXT,
    
    -- Contact info
    email VARCHAR(255),
    phone VARCHAR(50),
    
    -- Metadata
    source VARCHAR(100) NOT NULL DEFAULT 'apollo',
    confidence_score FLOAT CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0),
    enrichment_date TIMESTAMPTZ,
    s3_raw_data_key TEXT,
    
    -- Apollo metadata (JSONB for flexible storage)
    apollo_metadata JSONB,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for optimal query performance
CREATE INDEX IF NOT EXISTS idx_companies_enrichment_company_id ON companies_enrichment(company_id);
CREATE INDEX IF NOT EXISTS idx_companies_enrichment_org_id ON companies_enrichment(org_id);
CREATE INDEX IF NOT EXISTS idx_companies_enrichment_org_company ON companies_enrichment(org_id, company_id);
CREATE INDEX IF NOT EXISTS idx_companies_enrichment_domain ON companies_enrichment(domain);
CREATE INDEX IF NOT EXISTS idx_companies_enrichment_industry ON companies_enrichment(industry);
CREATE INDEX IF NOT EXISTS idx_companies_enrichment_employee_count ON companies_enrichment(employee_count);
CREATE INDEX IF NOT EXISTS idx_companies_enrichment_founded_year ON companies_enrichment(founded_year);
CREATE INDEX IF NOT EXISTS idx_companies_enrichment_source ON companies_enrichment(source);
```

### 2. company_keywords

Normalized storage for company keywords and tags.

```sql
CREATE TABLE company_keywords (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id VARCHAR(255) NOT NULL REFERENCES companies_enrichment(company_id) ON DELETE CASCADE,
    keyword VARCHAR(255) NOT NULL,
    
    -- Prevent duplicate keywords per company
    UNIQUE(company_id, keyword)
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_company_keywords_company_id ON company_keywords(company_id);
CREATE INDEX IF NOT EXISTS idx_company_keywords_keyword ON company_keywords(keyword);
```

### 3. company_technologies

Technology stack information with categories.

```sql
CREATE TABLE company_technologies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id VARCHAR(255) NOT NULL REFERENCES companies_enrichment(company_id) ON DELETE CASCADE,
    technology VARCHAR(255) NOT NULL,
    category VARCHAR(100),
    
    -- Prevent duplicate technologies per company
    UNIQUE(company_id, technology)
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_company_technologies_company_id ON company_technologies(company_id);
CREATE INDEX IF NOT EXISTS idx_company_technologies_technology ON company_technologies(technology);
CREATE INDEX IF NOT EXISTS idx_company_technologies_category ON company_technologies(category);
```

### 4. company_department_counts

Department headcount information.

```sql
CREATE TABLE company_department_counts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id VARCHAR(255) NOT NULL REFERENCES companies_enrichment(company_id) ON DELETE CASCADE,
    department VARCHAR(255) NOT NULL,
    head_count INTEGER NOT NULL CHECK (head_count > 0),
    
    -- Prevent duplicate departments per company
    UNIQUE(company_id, department)
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_company_department_counts_company_id ON company_department_counts(company_id);
CREATE INDEX IF NOT EXISTS idx_company_department_counts_department ON company_department_counts(department);
CREATE INDEX IF NOT EXISTS idx_company_department_counts_head_count ON company_department_counts(head_count);
```

## Data Flow

1. **Raw Data Ingestion**: Apollo API response stored in S3 with key pattern: `companies/{org_id}/{company_id}_enrichment_{timestamp}.json`

2. **Data Transformation**: Apollo payload transformed using `transform_apollo_company_payload()` function

3. **Database Storage**: Normalized data inserted using `insert_company_with_relations()` method in a single transaction

4. **Relationship Management**: All related records (keywords, technologies, departments) are linked via `company_id` foreign key

## Key Features

- **Atomicity**: All related data inserted in a single transaction
- **Deduplication**: Unique constraints prevent duplicate entries
- **Audit Trail**: Raw data preserved in S3 with reference in database
- **Extensibility**: JSONB fields for flexible metadata storage
- **Performance**: Comprehensive indexing for fast queries
- **Data Integrity**: Foreign key constraints and check constraints

## Usage Examples

### Query company with all relations:
```sql
SELECT 
    c.*,
    array_agg(DISTINCT ck.keyword) as keywords,
    array_agg(DISTINCT ct.technology) as technologies,
    array_agg(DISTINCT cd.department || ': ' || cd.head_count) as departments
FROM companies_enrichment c
LEFT JOIN company_keywords ck ON c.company_id = ck.company_id
LEFT JOIN company_technologies ct ON c.company_id = ct.company_id  
LEFT JOIN company_department_counts cd ON c.company_id = cd.company_id
WHERE c.org_id = 'org_123'
GROUP BY c.id;
```

### Search by technology:
```sql
SELECT DISTINCT c.*
FROM companies_enrichment c
JOIN company_technologies ct ON c.company_id = ct.company_id
WHERE ct.technology ILIKE '%react%'
AND c.org_id = 'org_123';
```

### Aggregate department data:
```sql
SELECT 
    cd.department,
    COUNT(*) as company_count,
    AVG(cd.head_count) as avg_head_count,
    SUM(cd.head_count) as total_head_count
FROM company_department_counts cd
JOIN companies_enrichment c ON cd.company_id = c.company_id
WHERE c.org_id = 'org_123'
GROUP BY cd.department
ORDER BY total_head_count DESC;
```
