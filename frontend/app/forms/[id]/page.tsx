"use client"

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { ArrowLeft, Star } from 'lucide-react'

import { useAuth } from '@/lib/auth-context'
import { FormBuilder } from '@/components/core/form-builder'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { visualRetreat, mobileRetreat } from '@/lib/utils/responsive'

export default function EditFormPage() {
  const params = useParams()
  const router = useRouter()
  const { isAuthenticated } = useAuth()
  const [isLoading, setIsLoading] = useState(true)

  const formId = params?.id as string

  useEffect(() => {
    // Simulate initial loading for premium experience
    const timer = setTimeout(() => setIsLoading(false), 800)
    return () => clearTimeout(timer)
  }, [])

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    router.push('/login')
    return null
  }

  // Premium loading state
  if (isLoading) {
    return (
      <div className={cn(mobileRetreat.page.container, "bg-gradient-to-br from-gray-50 via-white to-gray-50/50")}>
        {/* Premium header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="safe-top sticky top-0 z-40 border-b border-gray-200/50 bg-white/95 backdrop-blur-xl"
        >
          <div className="mx-auto max-w-7xl p-4 md:px-6 lg:px-8">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
                className="touch-target transition-colors hover:bg-gray-50/80"
              >
                <ArrowLeft className="mr-2 size-4" />
                <span className="hidden sm:inline">Back to Forms</span>
                <span className="sm:hidden">Back</span>
              </Button>
              <div className="h-6 w-px bg-gray-200" />
              <h1 className="text-lg font-semibold text-gray-900">Form Builder</h1>
            </div>
          </div>
        </motion.div>

        {/* Premium loading content */}
        <div className="mx-auto max-w-7xl px-4 py-8 md:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className={cn(visualRetreat.card.base, visualRetreat.card.floating, "p-8 text-center")}
          >
            <div className="relative mb-6">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="border-3 mx-auto size-12 rounded-full border-blue-200 border-t-blue-600"
              />
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="absolute inset-0 flex items-center justify-center"
              >
                <Star className="size-6 text-blue-600" />
              </motion.div>
            </div>
            <h3 className="mb-2 text-xl font-semibold text-gray-900">
              Loading Form Builder
            </h3>
            <p className="text-gray-600">
              Preparing your premium form editing experience...
            </p>
          </motion.div>
        </div>
      </div>
    )
  }

  // Premium form builder interface
  return (
    <div className="flex flex-col min-h-full w-full bg-white">
      <FormBuilder formId={formId} />
    </div>
  )
}
