// API client for PDF export functionality
import apiClient from '@/lib/api-client'
import { FormattedSubmission } from '@/lib/types/submission-preview'

export interface PDFExportOptions {
  includeEmptyAnswers?: boolean
  includeMetadata?: boolean
  template?: 'standard' | 'premium' | 'minimal'
  branding?: 'tractionx' | 'custom'
}

export interface PDFExportResponse {
  success: boolean
  downloadUrl?: string
  fileName?: string
  fileSize?: number
  error?: string
}

export const PDFExportAPI = {
  /**
   * Export submission to PDF
   */
  async exportSubmissionToPDF(
    dealId: string, 
    submissionId: string, 
    options: PDFExportOptions = {}
  ): Promise<PDFExportResponse> {
    try {
      console.log(`Exporting submission ${submissionId} to PDF`)
      
      const response = await apiClient.post(`/deals/${dealId}/submissions/${submissionId}/export/pdf`, {
        options: {
          includeEmptyAnswers: options.includeEmptyAnswers ?? false,
          includeMetadata: options.includeMetadata ?? true,
          template: options.template ?? 'premium',
          branding: options.branding ?? 'tractionx'
        }
      })

      return response.data
    } catch (error: any) {
      console.error('Error exporting submission to PDF:', error)
      return {
        success: false,
        error: error.response?.data?.detail || error.message || 'Failed to export PDF'
      }
    }
  },

  /**
   * Generate PDF client-side using browser APIs
   * Fallback when backend PDF generation is not available
   */
  async generatePDFClientSide(
    submission: FormattedSubmission,
    dealData?: any,
    options: PDFExportOptions = {}
  ): Promise<void> {
    try {
      // Dynamic import to avoid SSR issues
      const jsPDFModule = await import('jspdf')
      const jsPDF = jsPDFModule.default
      
      const doc = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      })

      // PDF Configuration
      const pageWidth = doc.internal.pageSize.getWidth()
      const pageHeight = doc.internal.pageSize.getHeight()
      const margin = 20
      const contentWidth = pageWidth - (margin * 2)
      let currentY = margin

      // Helper function to add new page if needed
      const checkPageBreak = (requiredHeight: number) => {
        if (currentY + requiredHeight > pageHeight - margin) {
          doc.addPage()
          currentY = margin
          return true
        }
        return false
      }

      // Header with TractionX branding
      this.addPDFHeader(doc, pageWidth, margin)
      currentY += 40

      // Submission title and metadata
      doc.setFontSize(24)
      doc.setFont('helvetica', 'bold')
      doc.text(submission.formName, margin, currentY)
      currentY += 12

      doc.setFontSize(12)
      doc.setFont('helvetica', 'normal')
      doc.setTextColor(100, 100, 100)
      doc.text(`Version ${submission.formVersion} • Submitted ${submission.submittedAt.toLocaleDateString()}`, margin, currentY)
      currentY += 20

      // Add sections
      for (const section of submission.sections) {
        checkPageBreak(30)
        
        // Section header
        doc.setFontSize(16)
        doc.setFont('helvetica', 'bold')
        doc.setTextColor(0, 0, 0)
        doc.text(section.title, margin, currentY)
        currentY += 10

        if (section.isRepeatable && section.instances) {
          // Handle repeatable sections
          for (const instance of section.instances) {
            checkPageBreak(20)
            
            doc.setFontSize(14)
            doc.setFont('helvetica', 'bold')
            doc.text(`${instance.title}`, margin + 5, currentY)
            currentY += 8

            for (const answer of instance.answers) {
              if (answer.isEmpty && !options.includeEmptyAnswers) continue
              
              checkPageBreak(15)
              
              // Question label
              doc.setFontSize(11)
              doc.setFont('helvetica', 'bold')
              doc.setTextColor(60, 60, 60)
              doc.text(answer.label, margin + 10, currentY)
              currentY += 5

              // Answer value
              doc.setFont('helvetica', 'normal')
              doc.setTextColor(0, 0, 0)
              const answerText = this.formatAnswerForPDF(answer)
              const lines = doc.splitTextToSize(answerText, contentWidth - 15)
              doc.text(lines, margin + 10, currentY)
              currentY += (lines.length * 5) + 3
            }
            currentY += 5
          }
        } else if (section.questions) {
          // Handle regular sections
          for (const question of section.questions) {
            if (question.isEmpty && !options.includeEmptyAnswers) continue
            
            checkPageBreak(15)
            
            // Question label
            doc.setFontSize(11)
            doc.setFont('helvetica', 'bold')
            doc.setTextColor(60, 60, 60)
            doc.text(question.label, margin + 5, currentY)
            currentY += 5

            // Answer value
            doc.setFont('helvetica', 'normal')
            doc.setTextColor(0, 0, 0)
            const answerText = this.formatAnswerForPDF(question)
            const lines = doc.splitTextToSize(answerText, contentWidth - 10)
            doc.text(lines, margin + 5, currentY)
            currentY += (lines.length * 5) + 3
          }
        }
        currentY += 10
      }

      // Footer
      this.addPDFFooter(doc, pageWidth, pageHeight, margin)

      // Download the PDF
      const fileName = `${submission.formName.replace(/[^a-zA-Z0-9]/g, '_')}_submission.pdf`
      doc.save(fileName)

    } catch (error) {
      console.error('Error generating PDF client-side:', error)
      throw new Error('Failed to generate PDF')
    }
  },

  /**
   * Add TractionX header to PDF
   */
  addPDFHeader(doc: any, pageWidth: number, margin: number) {
    // TractionX logo area (placeholder)
    doc.setFillColor(30, 41, 59) // TractionX brand color
    doc.rect(margin, margin, 40, 8, 'F')
    
    doc.setFontSize(14)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(255, 255, 255)
    doc.text('TractionX', margin + 2, margin + 5.5)

    // Title
    doc.setFontSize(18)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(0, 0, 0)
    doc.text('Deal Submission Report', margin, margin + 20)

    // Horizontal line
    doc.setDrawColor(200, 200, 200)
    doc.line(margin, margin + 25, pageWidth - margin, margin + 25)
  },

  /**
   * Add footer to PDF
   */
  addPDFFooter(doc: any, pageWidth: number, pageHeight: number, margin: number) {
    const footerY = pageHeight - margin + 5
    
    // Horizontal line
    doc.setDrawColor(200, 200, 200)
    doc.line(margin, footerY - 10, pageWidth - margin, footerY - 10)
    
    // Footer text
    doc.setFontSize(9)
    doc.setFont('helvetica', 'normal')
    doc.setTextColor(100, 100, 100)
    doc.text('Powered by TractionX', margin, footerY)
    
    // Date
    const currentDate = new Date().toLocaleDateString()
    doc.text(`Generated on ${currentDate}`, pageWidth - margin - 40, footerY)
  },

  /**
   * Format answer for PDF display
   */
  formatAnswerForPDF(answer: any): string {
    if (answer.isEmpty) {
      return '—'
    }
    
    if (Array.isArray(answer.value)) {
      return answer.value.join(', ')
    }
    
    if (typeof answer.value === 'boolean') {
      return answer.value ? 'Yes' : 'No'
    }
    
    return String(answer.value)
  }
}
