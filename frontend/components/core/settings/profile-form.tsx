"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { motion } from "framer-motion"
import * as z from "zod"
import { Loader2, Lock, Mail, User, Save } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { toast } from "sonner"
import { AvatarUploader } from "./avatar-uploader"
import { ChangePasswordDialog } from "./change-password-dialog"
import { SettingsAPI, UserProfile } from "@/lib/api/settings-api"
import { useAuth } from "@/lib/auth-context"

const profileSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
})

type ProfileFormValues = z.infer<typeof profileSchema>

interface ProfileFormProps {
  profile: UserProfile
  onUpdate: (data: { name?: string; profile_picture?: string }) => Promise<void>
  onChangePassword: () => void
}

export function ProfileForm({
  profile,
  onUpdate,
  onChangePassword,
}: ProfileFormProps) {
  const { token } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [showPasswordDialog, setShowPasswordDialog] = useState(false)
  const [currentAvatar, setCurrentAvatar] = useState(profile.profile_picture)

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: profile.name,
      email: profile.email,
    },
  })

  const handleSubmit = async (data: ProfileFormValues) => {
    setIsLoading(true)
    try {
      await onUpdate({
        name: data.name,
        profile_picture: currentAvatar,
      })

      toast.success("Profile updated successfully!")
    } catch (error) {
      console.error('Profile update error:', error)
      toast.error(error instanceof Error ? error.message : "Failed to update profile")
    } finally {
      setIsLoading(false)
    }
  }

  const handleAvatarUpload = async (file: File) => {
    const avatarUrl = await SettingsAPI.uploadFile(file, 'avatar')
    setCurrentAvatar(avatarUrl)
    return avatarUrl
  }

  const handleAvatarRemove = async () => {
    setCurrentAvatar(undefined)
    // TODO: Implement avatar removal API call if needed
  }

  const isDirty = form.formState.isDirty || currentAvatar !== profile.profile_picture

  return (
    <div className="space-y-8">
      {/* Avatar Section */}
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Profile Picture
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            Upload a profile picture to personalize your account
          </p>
        </div>
        
        <AvatarUploader
          currentAvatar={currentAvatar}
          userName={profile.name}
          onUpload={handleAvatarUpload}
          onRemove={handleAvatarRemove}
          size="lg"
        />
      </div>

      <Separator className="bg-gray-200 dark:bg-gray-700" />

      {/* Profile Information */}
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Personal Information
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            Update your personal details and contact information
          </p>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          {/* Name Field */}
          <div className="space-y-2">
            <Label htmlFor="name" className="flex items-center text-sm font-medium">
              <User className="mr-2 size-4" />
              Full Name
            </Label>
            <Input
              id="name"
              type="text"
              placeholder="Enter your full name"
              className="h-11 border-white/20 bg-white/50 focus:border-blue-500/50 focus:ring-blue-500/20 dark:border-white/10 dark:bg-black/50"
              {...form.register("name")}
            />
            {form.formState.errors.name && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.name.message}
              </motion.p>
            )}
          </div>

          {/* Email Field (Read-only) */}
          <div className="space-y-2">
            <Label htmlFor="email" className="flex items-center text-sm font-medium">
              <Mail className="mr-2 size-4" />
              Email Address
            </Label>
            <Input
              id="email"
              type="email"
              readOnly
              className="h-11 cursor-not-allowed border-gray-200 bg-gray-50 text-gray-500 dark:border-gray-700 dark:bg-gray-800"
              {...form.register("email")}
            />
            <p className="text-xs text-gray-500">
              Email address cannot be changed. Contact support if needed.
            </p>
          </div>
        </div>

        {/* Save Button */}
        <div className="flex items-center justify-between pt-4">
          <div className="flex items-center space-x-4">
            <Button
              type="submit"
              disabled={!isDirty || isLoading}
              className="bg-gradient-to-r from-blue-600 to-purple-600 font-medium text-white shadow-lg shadow-blue-500/25 transition-all duration-200 hover:from-blue-700 hover:to-purple-700"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 size-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 size-4" />
                  Save Changes
                </>
              )}
            </Button>

            {isDirty && (
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-sm text-amber-600 dark:text-amber-400"
              >
                You have unsaved changes
              </motion.p>
            )}
          </div>
        </div>
      </form>

      <Separator className="bg-gray-200 dark:bg-gray-700" />

      {/* Security Section */}
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Security
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            Manage your account security and password
          </p>
        </div>

        <div className="flex items-center justify-between rounded-xl border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-800/50">
          <div className="flex items-center space-x-3">
            <div className="flex size-10 items-center justify-center rounded-lg bg-gradient-to-br from-green-500 to-emerald-600">
              <Lock className="size-5 text-white" />
            </div>
            <div>
              <p className="font-medium text-gray-900 dark:text-gray-100">
                Password
              </p>
              <p className="text-sm text-gray-500">
                Last updated 30 days ago
              </p>
            </div>
          </div>
          
          <Button
            variant="outline"
            onClick={onChangePassword}
            className="bg-white/70 backdrop-blur-sm dark:bg-black/70"
          >
            Change Password
          </Button>
        </div>
      </div>

      {/* Change Password Dialog */}
      <ChangePasswordDialog
        open={showPasswordDialog}
        onOpenChange={setShowPasswordDialog}
        onPasswordChange={async (currentPassword, newPassword) => {
          // TODO: Implement password change API call
          console.log('Password change:', { currentPassword, newPassword })
          toast.success("Password updated successfully!")
        }}
      />
    </div>
  )
}
