"""
Chat Service Implementation

Handles chat operations including thread management, message processing,
and integration with Perplexity AI for async completions.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple

from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.logging import get_logger
from app.models.chat import (
    ChatMessage,
    ChatMode,
    ChatSource,
    ChatThread,
    MessageRole,
    MessageStatus,
)
from app.schemas.chat import SendMessageRequest
from app.services.base import BaseService
from app.services.chat.interface import IChatService
from app.utils.common import PyObjectId

logger = get_logger(__name__)


class ChatService(BaseService, IChatService):
    """Implementation of chat service operations."""

    def __init__(self, db: AsyncIOMotorDatabase):
        super().__init__()
        self.db = db

        # Initialize AI clients
        from app.services.openai.client import OpenAIClient
        from app.services.perplexity.client import PerplexityClient

        self.perplexity_client = PerplexityClient()
        self.openai_client = OpenAIClient()

    async def initialize(self) -> None:
        """Initialize service resources."""
        # Initialize any resources needed by the chat service
        # The database and perplexity client are already initialized in __init__
        self.logger.info("Chat service initialized")

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        # Clean up any resources used by the chat service
        # Currently no cleanup needed for database or perplexity client
        self.logger.info("Chat service cleaned up")

    async def get_or_create_thread(
        self, deal_id: str, user_id: str, org_id: str, mode: ChatMode = ChatMode.CHAT
    ) -> ChatThread:
        """Get existing thread or create new one for deal/user/mode combination."""
        try:
            # Try to find existing thread for this mode
            existing_thread = await ChatThread.find_one({
                "deal_id": PyObjectId(deal_id),
                "user_id": PyObjectId(user_id),
                "org_id": PyObjectId(org_id),
                "mode": mode,
            })

            if existing_thread:
                logger.info(
                    f"Found existing chat thread: {existing_thread.id} (mode: {mode})"
                )
                return existing_thread

            # Create new thread for this mode
            mode_titles = {
                ChatMode.CHAT: "Chat",
                ChatMode.RESEARCH: "Deep Research",
                ChatMode.AGENT: "Agent Analysis",
            }

            thread = ChatThread(
                deal_id=PyObjectId(deal_id),  # type: ignore
                user_id=PyObjectId(user_id),  # type: ignore
                org_id=PyObjectId(org_id),  # type: ignore
                mode=mode,
                title=f"{mode_titles.get(mode, 'Chat')} - {deal_id}",
            )

            await thread.save()
            logger.info(f"Created new chat thread: {thread.id} (mode: {mode})")
            return thread

        except Exception as e:
            logger.error(f"Error getting/creating thread: {e}")
            raise

    async def get_thread_messages(
        self, thread_id: str, limit: int = 50, skip: int = 0
    ) -> List[ChatMessage]:
        """Get messages for a thread with pagination."""
        try:
            messages = await ChatMessage.find_many(
                query={"thread_id": PyObjectId(thread_id)},
                sort=[("created_at", 1)],
                skip=skip,
                limit=limit,
            )

            return messages

        except Exception as e:
            logger.error(f"Error getting thread messages: {e}")
            raise

    async def send_message(
        self,
        thread_id: str,
        user_id: str,
        request: SendMessageRequest,
        deal_context: Optional[Dict[str, Any]] = None,
    ) -> ChatMessage:
        """Send a new message and trigger AI completion based on mode."""
        try:
            # Create user message
            user_message = ChatMessage(
                thread_id=PyObjectId(thread_id),
                role=MessageRole.USER,
                content=request.message,
                mode=request.mode,
                deal_context=deal_context,
                ai_model=None,
                ai_provider=None,
                perplexity_request_id=None,
                perplexity_model=None,
                error_message=None,
                response_time_ms=None,
                token_count=None,
            )
            await user_message.save()

            # Get chat history for context
            chat_history = await self._get_chat_history_for_context(thread_id)

            # Route to appropriate AI service based on mode
            if request.mode == ChatMode.CHAT:
                ai_message = await self._handle_chat_mode(
                    thread_id, request, deal_context, chat_history
                )
            elif request.mode == ChatMode.RESEARCH:
                ai_message = await self._handle_research_mode(
                    thread_id, request, deal_context, chat_history
                )
            elif request.mode == ChatMode.AGENT:
                ai_message = await self._handle_agent_mode(
                    thread_id, request, deal_context, chat_history
                )
            else:
                raise ValueError(f"Unsupported chat mode: {request.mode}")

            # Update thread metadata
            await self._update_thread_metadata(thread_id, ai_message.ai_model)

            logger.info(
                f"Sent message and completed AI response: {ai_message.id} (mode: {request.mode})"
            )
            return ai_message

        except Exception as e:
            logger.error(f"Error sending message: {e}")

            # If we created a user message but failed to get AI response,
            # create a failed AI message for better UX
            try:
                if "user_message" in locals():
                    failed_ai_message = ChatMessage(
                        thread_id=PyObjectId(thread_id),
                        role=MessageRole.ASSISTANT,
                        content="",
                        mode=request.mode,
                        status=MessageStatus.FAILED,
                        error_message=str(e),
                        deal_context=deal_context,
                        ai_model=None,
                        ai_provider=None,
                        perplexity_request_id=None,
                        perplexity_model=None,
                        response_time_ms=None,
                        token_count=None,
                    )
                    await failed_ai_message.save()
                    await self._update_thread_metadata(thread_id)
            except Exception as save_error:
                logger.error(f"Error saving failed message: {save_error}")

            raise

    async def get_message_status(
        self, message_id: str
    ) -> Tuple[MessageStatus, Optional[str]]:
        """Get the current status of a message."""
        try:
            message = await ChatMessage.find_one({"_id": PyObjectId(message_id)})
            if not message:
                return MessageStatus.FAILED, "Message not found"

            return message.status, message.error_message

        except Exception as e:
            logger.error(f"Error getting message status: {e}")
            return MessageStatus.FAILED, str(e)

    async def update_message_completion(
        self,
        message_id: str,
        content: str,
        sources: List[Dict[str, Any]],
        status: MessageStatus = MessageStatus.COMPLETED,
    ) -> ChatMessage:
        """Update message with completion results."""
        try:
            # Convert sources to ChatSource objects
            chat_sources = []
            for source_data in sources:
                chat_source = ChatSource(**source_data)
                chat_sources.append(chat_source)

            # Update message
            message = await ChatMessage.find_one({"_id": PyObjectId(message_id)})
            if not message:
                raise ValueError(f"Message {message_id} not found")

            message.content = content
            message.sources = chat_sources
            message.status = status
            message.updated_at = int(datetime.now(timezone.utc).timestamp())

            await message.save()

            # Update thread metadata
            await self._update_thread_metadata(str(message.thread_id))

            logger.info(f"Updated message completion: {message_id}")
            return message

        except Exception as e:
            logger.error(f"Error updating message completion: {e}")
            raise

    async def mark_message_failed(
        self, message_id: str, error_message: str
    ) -> ChatMessage:
        """Mark message as failed with error details."""
        try:
            message = await ChatMessage.find_one({"_id": PyObjectId(message_id)})
            if not message:
                raise ValueError(f"Message {message_id} not found")

            message.status = MessageStatus.FAILED
            message.error_message = error_message
            message.updated_at = int(datetime.now(timezone.utc).timestamp())

            await message.save()

            logger.warning(f"Marked message as failed: {message_id} - {error_message}")
            return message

        except Exception as e:
            logger.error(f"Error marking message as failed: {e}")
            raise

    async def get_chat_stats(self, user_id: str, org_id: str) -> Dict[str, Any]:
        """Get chat statistics for a user."""
        try:
            # Count threads
            total_threads = await ChatThread.count({
                "user_id": PyObjectId(user_id),
                "org_id": PyObjectId(org_id),
            })

            # Count messages
            thread_ids = await ChatThread.distinct(
                "_id",
                {
                    "user_id": PyObjectId(user_id),
                    "org_id": PyObjectId(org_id),
                },
            )

            total_messages = await ChatMessage.count({"thread_id": {"$in": thread_ids}})

            # Count active threads (with messages in last 7 days)
            week_ago = int((datetime.now(timezone.utc).timestamp() - 7 * 24 * 3600))
            active_threads = await ChatThread.count({
                "user_id": PyObjectId(user_id),
                "org_id": PyObjectId(org_id),
                "last_message_at": {"$gte": week_ago},
            })

            return {
                "total_threads": total_threads,
                "total_messages": total_messages,
                "active_threads": active_threads,
                "avg_response_time": None,  # TODO: Calculate from completion jobs
            }

        except Exception as e:
            logger.error(f"Error getting chat stats: {e}")
            raise

    async def _get_chat_history_for_context(
        self, thread_id: str
    ) -> List[Dict[str, str]]:
        """Get recent chat history for context."""
        try:
            messages = await ChatMessage.find_many(
                query={
                    "thread_id": PyObjectId(thread_id),
                    "status": {"$in": [MessageStatus.COMPLETED, MessageStatus.FAILED]},
                },
                sort=[("created_at", -1)],
                limit=10,
            )

            # Reverse to get chronological order
            messages.reverse()

            history = []
            for msg in messages:
                # Skip failed messages with empty content
                if msg.status == MessageStatus.FAILED and not msg.content.strip():
                    logger.debug(
                        f"Skipping failed message with empty content: {msg.id}"
                    )
                    continue

                history.append({"role": msg.role.value, "content": msg.content})

            # Log the history for debugging
            logger.debug(
                f"Retrieved chat history for thread {thread_id}: {[msg['role'] for msg in history]}"
            )

            # Check for potential issues
            for i in range(len(history) - 1):
                if (
                    history[i]["role"] == history[i + 1]["role"]
                    and history[i]["role"] != "system"
                ):
                    logger.warning(
                        f"Found consecutive {history[i]['role']} messages in history: "
                        f"message {i} and {i + 1}"
                    )

            return history

        except Exception as e:
            logger.error(f"Error getting chat history: {e}")
            return []

    async def _update_thread_metadata(
        self, thread_id: str, last_model: Optional[str] = None
    ):
        """Update thread metadata after new messages."""
        try:
            thread = await ChatThread.find_one({"_id": PyObjectId(thread_id)})
            if not thread:
                return

            # Count messages
            message_count = await ChatMessage.count({
                "thread_id": PyObjectId(thread_id)
            })

            # Update thread
            thread.message_count = message_count
            thread.last_message_at = int(datetime.now(timezone.utc).timestamp())
            thread.updated_at = int(datetime.now(timezone.utc).timestamp())

            if last_model:
                thread.last_model_used = last_model

            await thread.save()

        except Exception as e:
            logger.error(f"Error updating thread metadata: {e}")

    async def _handle_chat_mode(
        self,
        thread_id: str,
        request: SendMessageRequest,
        deal_context: Optional[Dict[str, Any]],
        chat_history: List[Dict[str, str]],
    ) -> ChatMessage:
        """Handle Chat mode using OpenAI GPT."""
        try:
            # Build prompt for Chat mode
            messages = self.openai_client.build_investment_prompt(
                user_message=request.message,
                deal_context=deal_context,
                chat_history=chat_history,
                mode="chat",
            )

            # Create completion with OpenAI
            completion_data = await self.openai_client.create_chat_completion(
                messages=messages, model="gpt-4o", max_tokens=1000, temperature=0.7
            )

            # Extract content and performance metadata
            content = self.openai_client.extract_content_from_completion(
                completion_data
            )
            performance = self.openai_client.extract_performance_metadata(
                completion_data
            )

            # Create AI response message
            ai_message = ChatMessage(
                thread_id=PyObjectId(thread_id),
                role=MessageRole.ASSISTANT,
                content=content,
                mode=ChatMode.CHAT,
                status=MessageStatus.COMPLETED,
                sources=[],  # Chat mode doesn't provide sources
                ai_model=performance.get("model", "gpt-4o"),
                ai_provider="openai",
                response_time_ms=performance.get("response_time_ms"),
                token_count=performance.get("token_count"),
                deal_context=deal_context,
                perplexity_request_id=None,
                perplexity_model=None,
                error_message=None,
            )
            await ai_message.save()

            return ai_message

        except Exception as e:
            logger.error(f"Error in Chat mode: {e}")
            raise

    async def _handle_research_mode(
        self,
        thread_id: str,
        request: SendMessageRequest,
        deal_context: Optional[Dict[str, Any]],
        chat_history: List[Dict[str, str]],
    ) -> ChatMessage:
        """Handle Research mode using Perplexity."""
        try:
            # Build prompt for Research mode
            messages = self.perplexity_client.build_investment_prompt(
                user_message=request.message,
                deal_context=deal_context,
                chat_history=chat_history,
                mode="research",
            )

            # Create completion with Perplexity
            completion_data = await self.perplexity_client.create_chat_completion(
                messages=messages,
                model="sonar-pro",  # Use pro model for research
                max_tokens=2000,  # Increased for more comprehensive responses
                temperature=0.2,
            )

            # Extract content and sources
            raw_content = self.perplexity_client.extract_content_from_completion(
                completion_data
            )
            sources = self.perplexity_client.extract_sources_from_completion(
                completion_data
            )

            # Format content for better display in Orbit AI
            formatted_content = self._format_research_content(raw_content, sources)

            # Create AI response message
            ai_message = ChatMessage(
                thread_id=PyObjectId(thread_id),
                role=MessageRole.ASSISTANT,
                content=formatted_content,
                mode=ChatMode.RESEARCH,
                status=MessageStatus.COMPLETED,
                sources=sources,
                ai_model="sonar-pro",
                ai_provider="perplexity",
                deal_context=deal_context,
                perplexity_request_id=None,
                perplexity_model=None,
                error_message=None,
                response_time_ms=None,
                token_count=None,
            )
            await ai_message.save()

            return ai_message

        except Exception as e:
            logger.error(f"Error in Research mode: {e}")
            raise

    def _format_research_content(
        self, raw_content: str, sources: List[ChatSource]
    ) -> str:
        """
        Format research content for better display in Orbit AI client.

        Args:
            raw_content: Raw AI response content
            sources: List of sources used in the response

        Returns:
            Formatted content optimized for Orbit AI display
        """
        try:
            # If content is already well-structured, return as is
            if "## " in raw_content or "**" in raw_content:
                return raw_content

            # Otherwise, try to structure the content better
            lines = raw_content.split("\n")
            formatted_lines = []

            # Add a header if not present
            if not any(line.strip().startswith("#") for line in lines[:3]):
                formatted_lines.append("# Investment Research Analysis")
                formatted_lines.append("")

            # Process each line
            for line in lines:
                line = line.strip()
                if not line:
                    formatted_lines.append("")
                    continue

                # Convert numbered lists to markdown
                if line.startswith(("1.", "2.", "3.", "4.", "5.")):
                    formatted_lines.append(f"- {line[2:].strip()}")
                # Convert bullet points to markdown
                elif line.startswith(("•", "-", "*")) and len(line) > 1:
                    formatted_lines.append(f"- {line[1:].strip()}")
                # Add emphasis to key terms
                elif any(
                    keyword in line.lower()
                    for keyword in [
                        "market",
                        "funding",
                        "valuation",
                        "revenue",
                        "growth",
                    ]
                ):
                    # Bold important metrics
                    import re

                    line = re.sub(r"(\$[\d,]+)", r"**\1**", line)
                    line = re.sub(r"(\d+%)", r"**\1**", line)
                    formatted_lines.append(line)
                else:
                    formatted_lines.append(line)

            # Add sources section if sources are available
            if sources:
                formatted_lines.append("")
                formatted_lines.append("---")
                formatted_lines.append("")
                formatted_lines.append("**Sources:**")
                for i, source in enumerate(sources, 1):
                    formatted_lines.append(f"{i}. [{source.title}]({source.url})")
                    if source.snippet:
                        formatted_lines.append(f"   *{source.snippet[:100]}...*")

            return "\n".join(formatted_lines)

        except Exception as e:
            logger.error(f"Error formatting research content: {e}")
            return raw_content  # Return original content if formatting fails

    async def _handle_agent_mode(
        self,
        thread_id: str,
        request: SendMessageRequest,
        deal_context: Optional[Dict[str, Any]],
        chat_history: List[Dict[str, str]],
    ) -> ChatMessage:
        """Handle Agent mode (future implementation)."""
        # For now, return a placeholder message
        ai_message = ChatMessage(
            thread_id=PyObjectId(thread_id),
            role=MessageRole.ASSISTANT,
            content="Agent mode is coming soon! This will enable autonomous, multi-step analysis and proactive insights. For now, try Chat mode for quick responses or Research mode for sourced analysis.",
            mode=ChatMode.AGENT,
            status=MessageStatus.COMPLETED,
            sources=[],
            ai_model="placeholder",
            ai_provider="tractionx",
            deal_context=deal_context,
            perplexity_request_id=None,
            perplexity_model=None,
            error_message=None,
            response_time_ms=None,
            token_count=None,
        )
        await ai_message.save()

        return ai_message
