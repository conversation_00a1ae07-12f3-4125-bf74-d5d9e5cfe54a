"""
Chat Service Interface

Defines the interface for chat-related operations.
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Tuple, Dict, Any

from app.models.chat import ChatThread, ChatMessage, MessageStatus, ChatMode
from app.schemas.chat import SendMessageRequest


class IChatService(ABC):
    """Interface for chat service operations."""
    
    @abstractmethod
    async def get_or_create_thread(
        self,
        deal_id: str,
        user_id: str,
        org_id: str,
        mode: ChatMode = ChatMode.CHAT
    ) -> ChatThread:
        """Get existing thread or create new one for deal/user/mode combination."""
        pass
    
    @abstractmethod
    async def get_thread_messages(
        self, 
        thread_id: str, 
        limit: int = 50, 
        skip: int = 0
    ) -> List[ChatMessage]:
        """Get messages for a thread with pagination."""
        pass
    
    @abstractmethod
    async def send_message(
        self,
        thread_id: str,
        user_id: str,
        request: SendMessageRequest,
        deal_context: Optional[Dict[str, Any]] = None
    ) -> ChatMessage:
        """Send a new message and trigger AI completion."""
        pass
    
    @abstractmethod
    async def get_message_status(self, message_id: str) -> Tuple[MessageStatus, Optional[str]]:
        """Get the current status of a message."""
        pass
    
    @abstractmethod
    async def update_message_completion(
        self,
        message_id: str,
        content: str,
        sources: List[Dict[str, Any]],
        status: MessageStatus = MessageStatus.COMPLETED
    ) -> ChatMessage:
        """Update message with completion results."""
        pass
    
    @abstractmethod
    async def mark_message_failed(
        self,
        message_id: str,
        error_message: str
    ) -> ChatMessage:
        """Mark message as failed with error details."""
        pass
    
    @abstractmethod
    async def get_chat_stats(self, user_id: str, org_id: str) -> Dict[str, Any]:
        """Get chat statistics for a user."""
        pass
