import hotToast from 'react-hot-toast'

interface ToastOptions {
  title?: string
  description?: string
  variant?: 'default' | 'destructive' | 'success'
  duration?: number
}

// Compatibility function to match the old API
export function toast({ title, description, variant = 'default', duration }: ToastOptions) {
  const message = title ? (description ? `${title}: ${description}` : title) : description || ''
  
  const opts = duration ? { duration } : undefined

  switch (variant) {
    case 'destructive':
      return hotToast.error(message, opts)
    case 'success':
      return hotToast.success(message, opts)  
    default:
      return hotToast(message, opts)
  }
}

export function useToast() {
  return {
    toast
  }
} 
