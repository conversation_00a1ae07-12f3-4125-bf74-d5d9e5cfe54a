"use client"

import {useState} from "react"
import {motion, AnimatePresence} from "framer-motion"
import Link from "next/link"
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card"
import {Badge} from "@/components/ui/badge"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {<PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip"
import {Progress} from "@/components/ui/progress"
import {
  AlertTriangle,
  Calendar,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  DollarSign,
  ExternalLink,
  FileText,
  Hash,
  Shield,
  Target,
  ToggleLeft,
  ToggleRight,
  Wand2,
  XCircle,
  Zap,
  Edit3,
  ChevronRight,
  Info,
  Award,
  BarChart3,
  TrendingUp,
  TrendingDown
} from "lucide-react"
import {cn} from "@/lib/utils"
import {DealDetailData} from "@/lib/types/deal-detail"
import {EmptyPlaceholder} from "@/components/empty-placeholder"
import { BonusPenaltySection } from "@/components/core/deals/deal-detail/bonus-penalty-section"
import { QuestionBreakdown } from "@/components/core/deals/deal-detail/question-breakdown"
import { visualRetreat } from "@/lib/utils/responsive"

interface ThesisMatchTabProps {
  deal: DealDetailData
  fullAnalysisData?: any
}

const getQuestionTypeIcon = (type: string) => {
  switch (type) {
    case 'short_text':
    case 'long_text':
      return FileText
    case 'multi_select':
    case 'single_select':
      return ToggleLeft
    case 'boolean':
      return ToggleRight
    case 'number':
      return Hash
    case 'date':
      return Calendar
    default:
      return Target
  }
}

const getQuestionTypeColor = (type: string) => {
  switch (type) {
    case 'short_text':
    case 'long_text':
      return 'bg-orange-100 text-orange-700 border-orange-200'
    case 'multi_select':
    case 'single_select':
      return 'bg-blue-100 text-blue-700 border-blue-200'
    case 'boolean':
      return 'bg-purple-100 text-purple-700 border-purple-200'
    case 'number':
      return 'bg-green-100 text-green-700 border-green-200'
    case 'date':
      return 'bg-pink-100 text-pink-700 border-pink-200'
    default:
      return 'bg-gray-100 text-gray-700 border-gray-200'
  }
}

const getScoreColor = (score: number) => {
  if (score >= 0.8) return 'text-green-600 bg-green-50 border-green-200'
  if (score >= 0.5) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
  return 'text-red-600 bg-red-50 border-red-200'
}

const getScoreIcon = (score: number) => {
  if (score >= 0.8) return CheckCircle
  if (score >= 0.5) return AlertTriangle
  return XCircle
}

const getScoreBackground = (score: number) => {
  if (score >= 80) return 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200/50'
  if (score >= 60) return 'bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200/50'
  if (score >= 40) return 'bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200/50'
  return 'bg-gradient-to-br from-red-50 to-red-50 border-red-200/50'
}

export function ThesisMatchTab({deal, fullAnalysisData}: ThesisMatchTabProps) {
  const [showAllQuestions, setShowAllQuestions] = useState(false)
  const [expandedQuestions, setExpandedQuestions] = useState<Set<string>>(new Set())
  const [editingQuestion, setEditingQuestion] = useState<string | null>(null)

  // Debug: Log all available data sources
  console.log('ThesisMatchTab Debug:', {
    deal_scoring: (deal as any).scoring,
    deal_exclusion_filter_result: (deal as any).exclusion_filter_result,
    fullAnalysisData: fullAnalysisData
  })

  // Extract all fields directly from the backend deal object
  const { company_name, scoring, exclusion_filter_result } = deal as any;
  const thesis = scoring?.thesis as any;
  const thesisScore = thesis?.score || {};
  const questionScores = Object.values(thesis?.question_scores || {});
  const bonusScoresRaw = thesis?.bonus_scores || {};
  const bonusScores = Array.isArray(bonusScoresRaw)
    ? Object.fromEntries(bonusScoresRaw.map((b: any, i: number) => [b.rule_id || `bonus_${i}`, b]))
    : bonusScoresRaw;
  const lastScored = thesis?.last_scored_at ? new Date(thesis.last_scored_at * 1000).toLocaleDateString() : '';
  const thesisMatchPercent = thesisScore.normalized_percent ? Number(thesisScore.normalized_percent.toFixed(2)) : 0;
  const coreScore = thesisScore.core ?? 0;
  const bonusTotal = thesisScore.bonus ?? 0;
  const penaltyTotal = thesisScore.penalty ?? 0;
  const maxPossible = thesisScore.max_possible ?? 0;
  const thesisName = thesis?.thesis_name || 'Thesis';

  if (!thesis) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="target"/>
        <EmptyPlaceholder.Title>No Thesis Analysis Available</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          This deal hasn't been analyzed against any investment thesis yet.
          Analysis will appear here once thesis matching is complete.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    )
  }

  const formatScore = (score: number) => {
    return `${(score * 100).toFixed(2)}%`
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getMatchStatusBadge = (score: number) => {
    if (score >= 0.6) return {text: 'Strong Match', color: 'bg-green-100 text-green-800 border-green-200'}
    if (score >= 0.4) return {text: 'Good Match', color: 'bg-blue-100 text-blue-800 border-blue-200'}
    if (score >= 0.2) return {text: 'Partial Match', color: 'bg-yellow-100 text-yellow-800 border-yellow-200'}
    return {text: 'Weak Match', color: 'bg-red-100 text-red-800 border-red-200'}
  }

  // Toggle question expansion
  const toggleQuestionExpansion = (questionId: string) => {
    setExpandedQuestions(prev => {
      const newSet = new Set(prev)
      if (newSet.has(questionId)) {
        newSet.delete(questionId)
      } else {
        newSet.add(questionId)
      }
      return newSet
    })
  }

  // Handle edit button click - expand card and show edit options
  const handleEditClick = (questionId: string, event: React.MouseEvent) => {
    event.stopPropagation() // Prevent card expansion from card click
    
    // Expand the card when edit is clicked
    if (!expandedQuestions.has(questionId)) {
      setExpandedQuestions(prev => new Set([...Array.from(prev), questionId]))
    }
    
    setEditingQuestion(questionId)
    // TODO: Implement edit functionality - navigate to thesis editor
    console.log('Edit question:', questionId)
  }

  // Handle navigation to thesis editor
  const handleEditThesis = () => {
    // If you have a thesis editor route, use it here. Otherwise, fallback to /theses
    window.open(`/theses`, '_blank')
  }

  return (
    <div className="space-y-8">
      {/* Debug Panel - Remove in production */}
      {/* {process.env.NODE_ENV === 'development' && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="text-sm text-yellow-800">Debug Info (Dev Only)</CardTitle>
          </CardHeader>
          <CardContent>
            <details className="text-xs">
              <summary className="cursor-pointer font-medium text-yellow-700">View Raw Data</summary>
              <pre className="mt-2 max-h-40 overflow-auto rounded bg-yellow-100 p-2 text-yellow-900">
                {JSON.stringify({
                  hasThesisData: !!thesis,
                  hasExclusionResult: !!exclusionResult,
                  thesisDataKeys: thesis ? Object.keys(thesis) : [],
                  questionCount: thesis ? Object.keys(thesis.question_scores || {}).length : 0
                }, null, 2)}
              </pre>
            </details>
          </CardContent>
        </Card>
      )} */}
      {/* Exclusion Filter Results */}
      {exclusion_filter_result && (
        <motion.div
          initial={{opacity: 0, y: 20}}
          animate={{opacity: 1, y: 0}}
          transition={{duration: 0.3}}
        >
          <Card className={cn(
            "border-2",
            exclusion_filter_result.excluded
              ? "border-red-200 bg-red-50/50"
              : "border-green-200 bg-green-50/50"
          )}>
            <CardHeader className="pb-4">
              <div className="flex items-center gap-3">
                {exclusion_filter_result.excluded ? (
                  <XCircle className="size-6 text-red-600"/>
                ) : (
                  <CheckCircle className="size-6 text-green-600"/>
                )}
                <div>
                  <CardTitle className={cn(
                    "text-lg",
                    exclusion_filter_result.excluded ? "text-red-900" : "text-green-900"
                  )}>
                    {exclusion_filter_result.excluded ? "Excluded by Filter" : "Passed Exclusion Filters"}
                  </CardTitle>
                  {exclusion_filter_result.reason && (
                    <p className={cn(
                      "mt-1 text-sm",
                      exclusion_filter_result.excluded ? "text-red-700" : "text-green-700"
                    )}>
                      {exclusion_filter_result.reason}
                    </p>
                  )}
                </div>
              </div>
            </CardHeader>
          </Card>
        </motion.div>
      )}

      {/* Thesis Match Results */}
      {thesis && (
        <motion.div
          initial={{opacity: 0, y: 20}}
          animate={{opacity: 1, y: 0}}
          transition={{duration: 0.3, delay: 0.1}}
          className="space-y-6"
        >
          {/* Thesis Header */}
          <Card className="border-0 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-lg">
            <CardHeader className="pb-6">
              <div className="flex items-start justify-between">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Shield className="size-8 text-blue-600"/>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">
                        {thesisName}
                      </h2>
                      <div className="mt-2 flex items-center gap-2">
                        <Badge className="border-blue-200 bg-blue-100 text-blue-800">
                          Full Analysis View
                        </Badge>
                        <Badge className={getMatchStatusBadge(thesisMatchPercent / 100).color}>
                          {getMatchStatusBadge(thesisMatchPercent / 100).text}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-6 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <FileText className="size-4"/>
                      <span>{Object.keys(thesis.question_scores || {}).length} Questions</span>
                    </div>
                    {lastScored && (
                      <div className="flex items-center gap-2">
                        <Calendar className="size-4"/>
                        <span>Last Updated: {lastScored}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2 text-right">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipContent>
                        <p className="max-w-xs">
                          Normalized score calculated
                          from {Object.keys(thesis.question_scores || {}).length} thesis criteria.
                          Score reflects weighted average of all matching rules.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  <div className="flex flex-col gap-2">
                    <Button
                      onClick={handleEditThesis}
                      className="gap-2 bg-blue-600 hover:bg-blue-700"
                      title="Edit Thesis Rules"
                    >
                      <Edit3 className="size-4" />
                      Edit Thesis Rules
                    </Button>
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Score Summary Section */}
          <div className="grid gap-6 md:grid-cols-2">
            {bonusTotal > 0 && (
              <Card className="border-0 bg-green-50/80 shadow-sm backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3">
                    <div className="rounded-lg bg-green-100 p-2 text-green-600">
                      <TrendingUp className="size-5" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-green-600">Bonus Points</p>
                      <p className="text-2xl font-bold text-green-900">+{bonusTotal}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {penaltyTotal > 0 && (
              <Card className="border-0 bg-red-50/80 shadow-sm backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3">
                    <div className="rounded-lg bg-red-100 p-2 text-red-600">
                      <TrendingDown className="size-5" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-red-600">Penalty Points</p>
                      <p className="text-2xl font-bold text-red-900">-{penaltyTotal}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Question Breakdown */}
          <Card className={cn("border-0 shadow-sm", visualRetreat.card.base)}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-xl">
                <Award className="size-6 text-blue-600"/>
                Question-by-Question Analysis
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Click on any question to expand details. This is a read-only view of how this deal matches against each thesis criterion. 
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {(() => {
                const questions = Object.entries(thesis.question_scores || {})
                const shouldCollapse = questions.length > 5
                const questionsToShow = shouldCollapse && !showAllQuestions
                  ? questions.slice(0, 5)
                  : questions

                return (
                  <>
                    {questionsToShow.map(([questionId, scoreData], index) => {
                      const typedScoreData = scoreData as any
                      const Icon = getQuestionTypeIcon(typedScoreData.question_type)
                      const ScoreIcon = getScoreIcon(typedScoreData.raw_score)
                      const isExpanded = expandedQuestions.has(questionId)
                      const isEditing = editingQuestion === questionId

                      return (
                        <motion.div
                          key={questionId}
                          initial={{opacity: 0, x: -20}}
                          animate={{opacity: 1, x: 0}}
                          transition={{duration: 0.3, delay: index * 0.05}}
                        >
                          <Card 
                            className={cn(
                              "cursor-pointer border border-gray-200 transition-all duration-200",
                              "hover:border-gray-300 hover:shadow-md",
                              isExpanded && "border-blue-200 bg-blue-50/30 shadow-md"
                            )}
                            onClick={() => toggleQuestionExpansion(questionId)}
                          >
                            <CardContent className="p-4 sm:p-6">
                              {/* Question Header - Always Visible */}
                                <div className="flex items-start justify-between">
                                <div className="flex min-w-0 flex-1 items-start gap-3">
                                    <div className={cn(
                                    "shrink-0 rounded-lg border p-2",
                                      getQuestionTypeColor(typedScoreData.question_type)
                                    )}>
                                      <Icon className="size-4"/>
                                    </div>
                                  <div className="min-w-0 flex-1">
                                    <div className="flex items-start justify-between gap-2">
                                      <h4 className="truncate text-base font-semibold text-gray-900 sm:text-lg">
                                        {typedScoreData.question_label || `Question ${questionId}`}
                                      </h4>
                                      <div className="flex shrink-0 items-center gap-2">
                                        {/* Edit Thesis Button - Only show when expanded */}
                                        {isExpanded && (
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={(e) => {
                                              e.stopPropagation()
                                              handleEditThesis()
                                            }}
                                            className="h-8 px-3 text-xs hover:border-blue-300 hover:bg-blue-50"
                                            title="Edit Thesis Rules"
                                          >
                                            <Edit3 className="mr-1 size-3" />
                                            Edit Thesis
                                          </Button>
                                        )}
                                        <motion.div
                                          animate={{ rotate: isExpanded ? 90 : 0 }}
                                          transition={{ duration: 0.2 }}
                                        >
                                          <ChevronRight className="size-4 text-gray-400" />
                                        </motion.div>
                                      </div>
                                    </div>
                                    <div className="mt-1 flex flex-wrap items-center gap-2">
                                        <Badge variant="outline" className="text-xs">
                                          {typedScoreData.question_type.replace('_', ' ')}
                                        </Badge>
                                        <Badge variant="secondary" className="text-xs">
                                          Weight: {typedScoreData.weight}
                                        </Badge>
                                        {typedScoreData.ai_generated && (
                                          <Badge className="border-purple-200 bg-purple-100 text-xs text-purple-700">
                                            <Zap className="mr-1 size-3"/>
                                            AI
                                          </Badge>
                                        )}
                                    </div>
                                  </div>
                                </div>

                                <div className="ml-4 flex shrink-0 items-center gap-3">
                                  <div className={cn(
                                    "flex items-center gap-2 rounded-lg border px-3 py-2 font-semibold",
                                    getScoreColor(typedScoreData.raw_score)
                                  )}>
                                    <ScoreIcon className="size-4"/>
                                    {formatScore(typedScoreData.raw_score)}
                                  </div>
                                </div>
                                      </div>

                              {/* Expandable Content */}
                              <AnimatePresence>
                                {isExpanded && (
                                  <motion.div
                                    initial={{ opacity: 0, height: 0 }}
                                    animate={{ opacity: 1, height: "auto" }}
                                    exit={{ opacity: 0, height: 0 }}
                                    transition={{ duration: 0.3, ease: "easeInOut" }}
                                    className="overflow-hidden"
                                  >
                                    <div className="mt-4 space-y-4 border-t border-gray-200 pt-4">
                                      {/* User Answer */}
                                      {typedScoreData.user_answer && (
                                        <div className="rounded-md border border-blue-200 bg-blue-50 p-3 sm:p-4">
                                          <p className="mb-2 text-xs font-medium text-blue-800">User Answer:</p>
                                          <p className="break-words text-sm text-blue-700">
                                            {Array.isArray(typedScoreData.user_answer)
                                              ? typedScoreData.user_answer.join(', ')
                                              : typeof typedScoreData.user_answer === 'boolean'
                                                ? typedScoreData.user_answer ? 'Yes' : 'No'
                                                : String(typedScoreData.user_answer)
                                            }
                                          </p>
                                        </div>
                                      )}

                                      {/* Expected Answer */}
                                      {typedScoreData.expected_answer && (
                                        <div className="rounded-md border border-green-200 bg-green-50 p-3 sm:p-4">
                                          <p className="mb-2 text-xs font-medium text-green-800">Expected Answer:</p>
                                          <p className="break-words text-sm text-green-700">
                                            {Array.isArray(typedScoreData.expected_answer)
                                              ? typedScoreData.expected_answer.join(', ')
                                              : typeof typedScoreData.expected_answer === 'boolean'
                                                ? typedScoreData.expected_answer ? 'Yes' : 'No'
                                                : String(typedScoreData.expected_answer)
                                            }
                                          </p>
                                    </div>
                                      )}

                                {/* Explanation */}
                                      <div className="rounded-lg bg-gray-50 p-3 sm:p-4">
                                  <p className="text-sm leading-relaxed text-gray-700">
                                    {typedScoreData.explanation || 'No explanation available'}
                                  </p>
                                </div>

                                {/* Score Details */}
                                      <div className="flex items-center justify-between border-t pt-3 text-xs text-gray-500">
                                  <span>Weighted Score: {typedScoreData.weighted_score.toFixed(2)}</span>
                                  {typedScoreData.aggregation_used && (
                                    <span>Aggregation: {typedScoreData.aggregation_type}</span>
                                  )}
                                      </div>
                              </div>
                                  </motion.div>
                                )}
                              </AnimatePresence>
                            </CardContent>
                          </Card>
                        </motion.div>
                      )
                    })}

                    {/* Show More/Less Button */}
                    {shouldCollapse && (
                      <div className="flex justify-center pt-4">
                        <Button
                          variant="outline"
                          onClick={() => setShowAllQuestions(!showAllQuestions)}
                          className="gap-2"
                        >
                          {showAllQuestions ? (
                            <>
                              <ChevronUp className="size-4"/>
                              Show Less ({questions.length - 5} hidden)
                            </>
                          ) : (
                            <>
                              <ChevronDown className="size-4"/>
                              Show All Questions ({questions.length - 5} more)
                            </>
                          )}
                        </Button>
                      </div>
                    )}
                  </>
                )
              })()}
            </CardContent>
          </Card>

          {/* Bonus & Penalty Section */}
          {thesis.bonus_scores && Object.keys(thesis.bonus_scores).length > 0 && (
            <BonusPenaltySection bonusScores={bonusScores} className="mt-6" />
          )}

          {/* Metadata Section */}
          <Card className={cn("border-0 shadow-sm", visualRetreat.card.base)}>
            <CardHeader className="p-6">
              <CardTitle className="flex items-center gap-3 text-lg">
                <Info className="size-5 text-gray-600" />
                Scoring Metadata
                </CardTitle>
              </CardHeader>
            <CardContent className="p-6">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div className="rounded-xl bg-gray-50 p-4 text-center">
                  <p className="mb-1 text-sm text-gray-600">Thesis Name</p>
                  <p className="font-semibold">{thesis.thesis_name || 'N/A'}</p>
                </div>
                <div className="rounded-xl bg-gray-50 p-4 text-center">
                  <p className="mb-1 text-sm text-gray-600">Questions Scored</p>
                  <p className="font-semibold">{Object.keys(thesis.question_scores || {}).length}</p>
                </div>
                <div className="rounded-xl bg-gray-50 p-4 text-center">
                  <p className="mb-1 text-sm text-gray-600">AI Scoring</p>
                  <p className="font-semibold">
                    {thesis.ai_scoring ? 'Enabled' : 'Disabled'}
                  </p>
                </div>
                <div className="rounded-xl bg-gray-50 p-4 text-center">
                  <p className="mb-1 text-sm text-gray-600">Last Updated</p>
                  <p className="text-xs font-semibold">
                    {lastScored || 'N/A'}
                  </p>
                </div>
                    </div>
              </CardContent>
            </Card>
        </motion.div>
      )}
    </div>
  )
}
