from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional


class QualifierFormServiceInterface(ABC):
    """Interface for qualifier form services."""

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize qualifier form service resources."""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup qualifier form service resources."""
        pass

    @abstractmethod
    async def create_qualifier_form(
        self,
        name: str,
        description: str,
        org_id: str,
        sections: Optional[List[str]] = None,
        default_section_ids: Optional[List[str]] = None,
        is_active: bool = True,
        sharing_config_id: Optional[str] = None,
        trigger_config_ids: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Create a new qualifier form.

        Args:
            name: Name of the form
            description: Description of the form
            org_id: Organization ID (from X-ORG-ID header)
            sections: List of section IDs
            default_section_ids: List of default section IDs
            is_active: Whether the form is active
            sharing_config_id: ID of the sharing configuration
            trigger_config_ids: List of trigger configuration IDs

        Returns:
            Qualifier form details
        """
        pass

    @abstractmethod
    async def get_qualifier_form(
        self,
        form_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get qualifier form by ID.

        Args:
            form_id: ID of the qualifier form

        Returns:
            Qualifier form details or None if not found
        """
        pass

    @abstractmethod
    async def get_qualifier_form_with_details(
        self,
        form_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get qualifier form with all details (sections, questions, sharing, triggers).

        Args:
            form_id: ID of the qualifier form

        Returns:
            Qualifier form details or None if not found
        """
        pass

    @abstractmethod
    async def update_qualifier_form(
        self,
        form_id: str,
        updates: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Update a qualifier form.

        Args:
            form_id: ID of the qualifier form
            updates: Fields to update

        Returns:
            Updated qualifier form or None if not found
        """
        pass

    @abstractmethod
    async def delete_qualifier_form(
        self,
        form_id: str
    ) -> bool:
        """
        Delete a qualifier form.

        Args:
            form_id: ID of the qualifier form

        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    async def list_qualifier_forms(
        self,
        org_id: str,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> List[Dict[str, Any]]:
        """
        List qualifier forms for an organization.

        Args:
            org_id: Organization ID (from X-ORG-ID header)
            skip: Number of forms to skip
            limit: Maximum number of forms to return
            search: Search term for form name/description
            is_active: Filter by active status

        Returns:
            List of qualifier forms
        """
        pass

    @abstractmethod
    async def configure_sharing(
        self,
        form_id: str,
        sharing_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Configure sharing for a qualifier form.

        Args:
            form_id: ID of the qualifier form
            sharing_config: Sharing configuration

        Returns:
            Updated qualifier form with sharing configuration
        """
        pass

    @abstractmethod
    async def configure_triggers(
        self,
        form_id: str,
        trigger_configs: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Configure triggers for a qualifier form.

        Args:
            form_id: ID of the qualifier form
            trigger_configs: List of trigger configurations

        Returns:
            Updated qualifier form with trigger configurations
        """
        pass

    @abstractmethod
    async def get_qualifier_form_stats(
        self,
        form_id: str
    ) -> Dict[str, Any]:
        """
        Get statistics for a qualifier form.

        Args:
            form_id: ID of the qualifier form

        Returns:
            Qualifier form statistics
        """
        pass

    @abstractmethod
    async def submit_qualifier_form(
        self,
        form_id: str,
        answers: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Submit a qualifier form.

        Args:
            form_id: ID of the qualifier form
            answers: Form answers
            metadata: Optional metadata to include with the submission

        Returns:
            Submission details
        """
        pass

    @abstractmethod
    async def get_qualifier_form_submissions(
        self,
        form_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get submissions for a qualifier form.

        Args:
            form_id: ID of the qualifier form
            skip: Number of submissions to skip
            limit: Maximum number of submissions to return

        Returns:
            List of submissions
        """
        pass
