"use client"

/**
 * SimpleGlow - Ultra-simple, guaranteed visible premium animation
 * 
 * Features:
 * - Simple animated gradient background
 * - Floating geometric elements
 * - Guaranteed visibility
 * - Zero performance impact
 */
export default function SimpleGlow() {
  return (
    <div className="relative size-full overflow-hidden">
      {/* Animated gradient background */}
      <div 
        className="absolute inset-0 animate-pulse bg-gradient-to-br from-amber-100/40 via-orange-50/30 to-amber-50/20"
        style={{ animationDuration: '4s' }}
      />
      
      {/* Floating dots */}
      <div className="absolute left-1/4 top-1/4 size-4 animate-bounce rounded-full bg-amber-400/60 shadow-lg" style={{ animationDelay: '0s', animationDuration: '3s' }} />
      <div className="absolute right-1/3 top-1/3 size-3 animate-bounce rounded-full bg-orange-400/50 shadow-md" style={{ animationDelay: '1s', animationDuration: '4s' }} />
      <div className="absolute bottom-1/3 left-1/2 size-5 animate-bounce rounded-full bg-amber-500/40 shadow-lg" style={{ animationDelay: '2s', animationDuration: '5s' }} />
      
      {/* Animated border lines */}
      <div className="absolute left-0 top-0 h-1 w-full animate-pulse bg-gradient-to-r from-transparent via-amber-400/50 to-transparent" />
      <div className="absolute bottom-0 left-0 h-1 w-full animate-pulse bg-gradient-to-r from-transparent via-orange-400/40 to-transparent" style={{ animationDelay: '2s' }} />
      
      {/* Central glow effect */}
      <div 
        className="bg-gradient-radial absolute left-1/2 top-1/2 size-32 -translate-x-1/2 -translate-y-1/2 animate-ping rounded-full from-amber-200/30 via-orange-100/20 to-transparent"
        style={{ animationDuration: '6s' }}
      />
    </div>
  )
}
