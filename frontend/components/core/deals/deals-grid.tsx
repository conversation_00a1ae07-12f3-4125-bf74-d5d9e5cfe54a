"use client"

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Deal } from '@/lib/types/deal';
import { getDealId } from '@/lib/utils/deal-id';
import { DealCard } from './deal-card';
import { NewDealCard } from './new-deal-card';
import { DealModal } from './deal-modal';
import { Skeleton } from '@/components/ui/skeleton';
import { EmptyPlaceholder } from '@/components/empty-placeholder';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

interface DealsGridProps {
  deals: Deal[];
  loading: boolean;
  error: string | null;
  onNewDeal?: () => void;
  onDeleteDeal?: (dealId: string) => Promise<boolean>;
}

export function DealsGrid({ deals, loading, error, onNewDeal, onDeleteDeal }: DealsGridProps) {
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleDealClick = (deal: Deal) => {
    setSelectedDeal(deal);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setTimeout(() => setSelectedDeal(null), 300); // Wait for animation to complete
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="grid auto-rows-fr gap-8 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 8 }).map((_, index) => (
          <DealCardSkeleton key={index} />
        ))}
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <Alert variant="destructive" className="mx-auto max-w-md">
        <AlertCircle className="size-4" />
        <AlertDescription>
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  // Empty state
  if (!deals || deals.length === 0) {
    return (
      <div className="space-y-8">
        {/* Show New Deal Card prominently when empty */}
        {onNewDeal && (
          <div className="grid auto-rows-fr gap-8 md:grid-cols-2 lg:grid-cols-3">
            <NewDealCard
              onClick={onNewDeal}
              index={0}
            />
          </div>
        )}

        <EmptyPlaceholder>
          <EmptyPlaceholder.Icon name="post" />
          <EmptyPlaceholder.Title>No deals yet. Add your first deal.</EmptyPlaceholder.Title>
          <EmptyPlaceholder.Description>
            Your investment opportunities will appear here once they're added to your pipeline.
          </EmptyPlaceholder.Description>
        </EmptyPlaceholder>
      </div>
    );
  }

  // Deals grid - Responsive with better spacing
  return (
    <>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid auto-rows-fr gap-8 md:grid-cols-2 lg:grid-cols-3"
      >
        {/* New Deal Card - Always first */}
        {onNewDeal && (
          <NewDealCard
            onClick={onNewDeal}
            index={0}
          />
        )}

        {/* Existing Deals */}
        {deals.map((deal, index) => {
          const dealId = getDealId(deal);
          if (!dealId) {
            console.warn('Skipping deal with missing ID:', deal);
            return null;
          }

          return (
            <DealCard
              key={dealId}
              deal={deal}
              index={onNewDeal ? index + 1 : index}
              onDelete={onDeleteDeal}
            />
          );
        })}
      </motion.div>

      {/* Coming Soon Modal */}
      <DealModal
        deal={selectedDeal}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </>
  );
}

// Skeleton component for loading state
function DealCardSkeleton() {
  return (
    <div className="rounded-lg border border-gray-200 bg-white p-8">
      {/* Header with Avatar and Company Info */}
      <div className="mb-6 flex items-start gap-4">
        <Skeleton className="size-12 shrink-0 rounded-full" />
        <div className="min-w-0 flex-1">
          <Skeleton className="mb-3 h-6 w-3/4" />
          <div className="flex gap-3">
            <Skeleton className="h-6 w-20 rounded-full" />
            <Skeleton className="h-6 w-24 rounded-full" />
          </div>
        </div>
        <Skeleton className="size-8 rounded" />
      </div>

      {/* Sector Tags */}
      <div className="mb-6 flex gap-3">
        <Skeleton className="h-6 w-24 rounded-full" />
        <Skeleton className="h-6 w-28 rounded-full" />
      </div>

      {/* Summary */}
      <div className="mb-6 space-y-3">
        <Skeleton className="h-5 w-full" />
        <Skeleton className="h-5 w-2/3" />
      </div>

      {/* Bottom Row */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Skeleton className="h-6 w-10 rounded-full" />
          <Skeleton className="h-6 w-20 rounded-full" />
        </div>
        <Skeleton className="h-5 w-16" />
      </div>
    </div>
  );
}
