# Form Sharing Integration Guide

This guide explains how to implement the sharing functionality for forms in your frontend application. The sharing framework allows forms to be distributed via links, embeds, and QR codes.

## Overview

The sharing framework provides three main methods for distributing forms:

1. **Sharing Links**: Unique URLs that can be shared via email, messaging, etc.
2. **Embed Codes**: HTML snippets for embedding forms in websites
3. **QR Codes**: Visual codes for accessing forms from mobile devices

## API Endpoints

### Creating a Sharing Configuration

Before you can share a form, you need to create a sharing configuration:

```bash
# Create a sharing configuration
curl -X POST "http://localhost:8000/api/v1/sharing" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "resource_type": "form",
    "resource_id": "6123456789abcdef01234567",
    "enabled": true,
    "sharing_types": ["link", "embed", "qr_code"],
    "allowed_domains": ["example.com", "trusted-partner.com"],
    "embed_type": "inline",
    "custom_styles": {"theme": "light", "primaryColor": "#007bff"},
    "tracking_enabled": true
  }'
```

Response:
```json
{
  "_id": "6523456789abcdef01234567",
  "resource_type": "form",
  "resource_id": "6123456789abcdef01234567",
  "org_id": "5123456789abcdef01234567",
  "enabled": true,
  "sharing_types": ["link", "embed", "qr_code"],
  "allowed_domains": ["example.com", "trusted-partner.com"],
  "embed_type": "inline",
  "custom_styles": {"theme": "light", "primaryColor": "#007bff"},
  "tracking_enabled": true,
  "created_at": 1678901240,
  "updated_at": 1678901240
}
```

### Generating a Sharing Link

Once you have a sharing configuration, you can generate a sharing link:

```bash
# Generate a sharing link
curl -X POST "http://localhost:8000/api/v1/sharing/6523456789abcdef01234567/link" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "expires_at": "2023-12-31T23:59:59Z",
    "metadata": {"source": "dashboard", "campaign": "spring2023"}
  }'
```

Response:
```json
{
  "_id": "6623456789abcdef01234567",
  "config_id": "6523456789abcdef01234567",
  "token": "abcdef123456",
  "url": "http://localhost:8000/shared/form/abcdef123456",
  "expires_at": "2023-12-31T23:59:59Z",
  "metadata": {"source": "dashboard", "campaign": "spring2023"},
  "created_by": "5023456789abcdef01234567",
  "created_at": 1678901241,
  "view_count": 0
}
```

### Generating an Embed Code

To embed a form in a website:

```bash
# Generate an embed code
curl -X POST "http://localhost:8000/api/v1/sharing/6523456789abcdef01234567/embed" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "embed_type": "modal",
    "custom_styles": {"width": "100%", "height": "500px"}
  }'
```

Response:
```json
{
  "_id": "6723456789abcdef01234567",
  "config_id": "6523456789abcdef01234567",
  "token": "ghijkl789012",
  "embed_type": "modal",
  "custom_styles": {"width": "100%", "height": "500px"},
  "html_code": "<div data-tx-form=\"ghijkl789012\" style=\"width:100%;height:500px\"></div><script src=\"http://localhost:8000/shared/embed.js\"></script>",
  "created_by": "5023456789abcdef01234567",
  "created_at": 1678901242,
  "view_count": 0
}
```

### Generating a QR Code

To generate a QR code for a form:

```bash
# Generate a QR code
curl -X POST "http://localhost:8000/api/v1/sharing/6523456789abcdef01234567/qr" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "size": 300,
    "error_correction_level": "M",
    "metadata": {"location": "conference"}
  }'
```

Response:
```json
{
  "_id": "6823456789abcdef01234567",
  "config_id": "6523456789abcdef01234567",
  "token": "mnopqr345678",
  "url": "http://localhost:8000/shared/form/mnopqr345678",
  "qr_code_url": "http://localhost:8000/api/v1/sharing/qr/6823456789abcdef01234567.png",
  "size": 300,
  "error_correction_level": "M",
  "metadata": {"location": "conference"},
  "created_by": "5023456789abcdef01234567",
  "created_at": 1678901243,
  "view_count": 0
}
```

### Getting Sharing Statistics

To get statistics for a sharing configuration:

```bash
# Get sharing statistics
curl -X GET "http://localhost:8000/api/v1/sharing/6523456789abcdef01234567/stats" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID"
```

Response:
```json
{
  "total_views": 42,
  "unique_views": 28,
  "by_sharing_type": {
    "link": 25,
    "embed": 15,
    "qr_code": 2
  },
  "by_date": [
    {"date": "2023-06-01", "views": 10},
    {"date": "2023-06-02", "views": 15},
    {"date": "2023-06-03", "views": 17}
  ],
  "conversion_rate": 0.35,
  "average_time_spent": 180
}
```

## Frontend Implementation

### Sharing Links

To implement sharing links in your frontend:

1. **Create a sharing configuration** when a form is created or when the user enables sharing
2. **Generate a sharing link** when the user clicks a "Share" button
3. **Display the link** to the user with a copy button
4. **Track link usage** to see how many people have viewed the form

Example React component:

```jsx
import React, { useState } from 'react';
import { Button, Input, message } from 'antd';
import { CopyOutlined } from '@ant-design/icons';

const ShareLinkGenerator = ({ formId, apiClient }) => {
  const [sharingLink, setSharingLink] = useState('');
  const [loading, setLoading] = useState(false);

  const generateLink = async () => {
    setLoading(true);
    try {
      // First, ensure we have a sharing configuration
      const configResponse = await apiClient.post('/api/v1/sharing', {
        resource_type: 'form',
        resource_id: formId,
        enabled: true,
        sharing_types: ['link'],
        tracking_enabled: true
      });
      
      // Then generate a link
      const linkResponse = await apiClient.post(
        `/api/v1/sharing/${configResponse.data._id}/link`,
        {
          expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          metadata: { source: 'dashboard' }
        }
      );
      
      setSharingLink(linkResponse.data.url);
    } catch (error) {
      message.error('Failed to generate sharing link');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const copyLink = () => {
    navigator.clipboard.writeText(sharingLink);
    message.success('Link copied to clipboard');
  };

  return (
    <div>
      <Button 
        type="primary" 
        onClick={generateLink} 
        loading={loading}
      >
        Generate Sharing Link
      </Button>
      
      {sharingLink && (
        <div style={{ marginTop: 16 }}>
          <Input 
            value={sharingLink} 
            readOnly 
            addonAfter={
              <CopyOutlined onClick={copyLink} style={{ cursor: 'pointer' }} />
            }
          />
        </div>
      )}
    </div>
  );
};

export default ShareLinkGenerator;
```

### Embed Codes

To implement embed codes in your frontend:

1. **Create a sharing configuration** with embed enabled
2. **Generate an embed code** when the user wants to embed the form
3. **Display the HTML code** to the user with a copy button
4. **Provide a preview** of how the embedded form will look

Example React component:

```jsx
import React, { useState } from 'react';
import { Button, Input, Radio, Card, message } from 'antd';
import { CopyOutlined } from '@ant-design/icons';

const EmbedCodeGenerator = ({ formId, apiClient }) => {
  const [embedCode, setEmbedCode] = useState('');
  const [embedType, setEmbedType] = useState('inline');
  const [loading, setLoading] = useState(false);

  const generateEmbedCode = async () => {
    setLoading(true);
    try {
      // First, ensure we have a sharing configuration
      const configResponse = await apiClient.post('/api/v1/sharing', {
        resource_type: 'form',
        resource_id: formId,
        enabled: true,
        sharing_types: ['embed'],
        allowed_domains: ['*'],
        embed_type: embedType,
        tracking_enabled: true
      });
      
      // Then generate an embed code
      const embedResponse = await apiClient.post(
        `/api/v1/sharing/${configResponse.data._id}/embed`,
        {
          embed_type: embedType,
          custom_styles: { width: '100%', height: '500px' }
        }
      );
      
      setEmbedCode(embedResponse.data.html_code);
    } catch (error) {
      message.error('Failed to generate embed code');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const copyCode = () => {
    navigator.clipboard.writeText(embedCode);
    message.success('Embed code copied to clipboard');
  };

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Radio.Group 
          value={embedType} 
          onChange={e => setEmbedType(e.target.value)}
        >
          <Radio.Button value="inline">Inline</Radio.Button>
          <Radio.Button value="modal">Modal</Radio.Button>
          <Radio.Button value="popup">Popup</Radio.Button>
        </Radio.Group>
      </div>
      
      <Button 
        type="primary" 
        onClick={generateEmbedCode} 
        loading={loading}
      >
        Generate Embed Code
      </Button>
      
      {embedCode && (
        <div style={{ marginTop: 16 }}>
          <Input.TextArea 
            value={embedCode} 
            rows={4} 
            readOnly 
          />
          <Button 
            icon={<CopyOutlined />} 
            onClick={copyCode}
            style={{ marginTop: 8 }}
          >
            Copy Code
          </Button>
          
          <Card title="Preview" style={{ marginTop: 16 }}>
            <div dangerouslySetInnerHTML={{ __html: embedCode }} />
          </Card>
        </div>
      )}
    </div>
  );
};

export default EmbedCodeGenerator;
```

### QR Codes

To implement QR codes in your frontend:

1. **Create a sharing configuration** with QR code enabled
2. **Generate a QR code** when the user wants to share via QR
3. **Display the QR code image** to the user with a download button

Example React component:

```jsx
import React, { useState } from 'react';
import { Button, Card, Select, InputNumber, message } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';

const QRCodeGenerator = ({ formId, apiClient }) => {
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [size, setSize] = useState(300);
  const [errorLevel, setErrorLevel] = useState('M');
  const [loading, setLoading] = useState(false);

  const generateQRCode = async () => {
    setLoading(true);
    try {
      // First, ensure we have a sharing configuration
      const configResponse = await apiClient.post('/api/v1/sharing', {
        resource_type: 'form',
        resource_id: formId,
        enabled: true,
        sharing_types: ['qr_code'],
        tracking_enabled: true
      });
      
      // Then generate a QR code
      const qrResponse = await apiClient.post(
        `/api/v1/sharing/${configResponse.data._id}/qr`,
        {
          size: size,
          error_correction_level: errorLevel,
          metadata: { source: 'dashboard' }
        }
      );
      
      setQrCodeUrl(qrResponse.data.qr_code_url);
    } catch (error) {
      message.error('Failed to generate QR code');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const downloadQRCode = () => {
    const link = document.createElement('a');
    link.href = qrCodeUrl;
    link.download = `form-qr-code-${formId}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', gap: 16 }}>
          <div>
            <label>Size:</label>
            <InputNumber 
              min={100} 
              max={1000} 
              value={size} 
              onChange={value => setSize(value)} 
              style={{ width: 100 }}
            />
          </div>
          <div>
            <label>Error Correction:</label>
            <Select 
              value={errorLevel} 
              onChange={value => setErrorLevel(value)}
              style={{ width: 100 }}
            >
              <Select.Option value="L">Low</Select.Option>
              <Select.Option value="M">Medium</Select.Option>
              <Select.Option value="Q">Quartile</Select.Option>
              <Select.Option value="H">High</Select.Option>
            </Select>
          </div>
        </div>
      </div>
      
      <Button 
        type="primary" 
        onClick={generateQRCode} 
        loading={loading}
      >
        Generate QR Code
      </Button>
      
      {qrCodeUrl && (
        <Card style={{ marginTop: 16, textAlign: 'center' }}>
          <img 
            src={qrCodeUrl} 
            alt="QR Code" 
            style={{ maxWidth: '100%' }} 
          />
          <Button 
            icon={<DownloadOutlined />} 
            onClick={downloadQRCode}
            style={{ marginTop: 16 }}
          >
            Download QR Code
          </Button>
        </Card>
      )}
    </div>
  );
};

export default QRCodeGenerator;
```

## Best Practices

1. **Security**:
   - Limit allowed domains for embeds to prevent unauthorized usage
   - Set appropriate expiration dates for sharing links
   - Use HTTPS for all shared resources

2. **User Experience**:
   - Provide clear instructions for how to use sharing features
   - Show previews of how shared forms will appear
   - Include branding and styling options

3. **Analytics**:
   - Track usage of shared forms to measure effectiveness
   - Analyze conversion rates to optimize form design
   - Monitor for unusual activity that might indicate abuse

4. **Accessibility**:
   - Ensure embedded forms are accessible to all users
   - Provide alternative methods for accessing forms
   - Test with screen readers and other assistive technologies

5. **Performance**:
   - Optimize embedded forms for fast loading
   - Minimize dependencies in embed code
   - Use lazy loading for embedded resources
