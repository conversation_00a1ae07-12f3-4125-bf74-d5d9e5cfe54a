import { useState, useEffect, useCallback, useRef } from 'react';
import { usePublicAuth } from '@/lib/contexts/public-auth-context';
import { PublicSubmissionAPI } from '@/lib/api/public-submission';
import { getVisibleQuestions, validateAnswer } from '@/lib/utils/form-logic';

// Debounce utility
function useDebounce<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay]
  ) as T;

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedCallback;
}

export function usePublicFormProgress(token: string, form: any) {
  const { isAuthenticated, user, accessToken, submission, updateSubmission } = usePublicAuth();
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [progress, setProgress] = useState(0);
  const [canSubmit, setCanSubmit] = useState(false);
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isHydrated, setIsHydrated] = useState(false);

  // Mark as hydrated after first render
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Load initial data from submission
  useEffect(() => {
    if (!isHydrated || !submission) return;

    if (submission.answers) {
      setAnswers(submission.answers);
    }
    setProgress(submission.progress || 0);
  }, [submission, isHydrated]);

  // Calculate progress and validation
  useEffect(() => {
    if (!form?.sections || !isHydrated) {
      return;
    }

    try {
      let totalRequired = 0;
      let totalAnswered = 0;

      // Build a map of sections for quick lookup
      const sectionMap: Record<string, any> = {};
      form.sections.forEach((section: any) => {
        if (section && (section._id || section.id)) {
          sectionMap[section._id || section.id] = section;
        }
      });

      // Get all visible questions based on current answers
      const visibleQuestions = getVisibleQuestions(form, answers);

      // Count required questions and answered questions
      visibleQuestions.forEach((question: any) => {
        if (question.required) {
          totalRequired++;
          
          const answer = answers[question._id || question.id];
          if (answer !== undefined && answer !== null && answer !== '') {
            // For arrays (multi-select), check if not empty
            if (Array.isArray(answer)) {
              if (answer.length > 0) {
                totalAnswered++;
              }
            } else {
              totalAnswered++;
            }
          }
        }
      });

      const progressPercent = totalRequired > 0 ? (totalAnswered / totalRequired) * 100 : 100;
      const canSubmitForm = totalRequired === 0 || totalAnswered === totalRequired;
      
      setProgress(Math.round(progressPercent));
      setCanSubmit(canSubmitForm);

      // Debug logging (only in development)
      if (process.env.NODE_ENV === 'development') {
        console.log('Public form progress calculation:', {
          totalRequired,
          totalAnswered,
          progressPercent: Math.round(progressPercent),
          canSubmit: canSubmitForm,
          answers: Object.keys(answers).length,
          submissionId: submission?.id
        });
      }
    } catch (error) {
      console.error('Error calculating progress:', error);
    }
  }, [answers, form, isHydrated, submission]);

  // Save progress to backend (debounced) - Enhanced version
  const saveProgressToBackend = useCallback(async (
    answersToSave: Record<string, any>,
    progressToSave: number
  ) => {
    if (!isAuthenticated || !user || !submission?.id || !submission.can_edit) {
      return;
    }

    try {
      setSaving(true);
      
      await PublicSubmissionAPI.saveProgress(
        submission.id,
        {
          answers: answersToSave,
          progress: progressToSave,
        },
        accessToken || undefined
      );

      // Update local submission state
      updateSubmission({
        ...submission,
        answers: answersToSave,
        progress: progressToSave,
      });

      setLastSaved(new Date());
      console.log('Progress saved to backend:', { progress: progressToSave });
    } catch (error: any) {
      console.error('Failed to save progress to backend:', error);
      
      // Show user-friendly error message for token issues
      if (error.message?.includes('expired') || error.message?.includes('unauthorized')) {
        console.warn('Authentication issue detected, user may need to refresh the page');
        // Could dispatch a toast notification here if you have a toast system
      }
    } finally {
      setSaving(false);
    }
  }, [isAuthenticated, user, submission, accessToken, updateSubmission]);

  // Debounced save function
  const debouncedSave = useDebounce(saveProgressToBackend, 2000); // 2 second delay

  // Auto-save when answers change
  useEffect(() => {
    if (!isHydrated || !isAuthenticated || !submission?.can_edit) {
      return;
    }

    // Only auto-save if we have answers and the form is loaded
    if (Object.keys(answers).length > 0 && form?.sections) {
      debouncedSave(answers, progress);
    }
  }, [answers, progress, isHydrated, isAuthenticated, submission?.can_edit, form?.sections]); // eslint-disable-line react-hooks/exhaustive-deps

  // Update answer function
  const updateAnswer = useCallback((questionId: string, value: any, repeatIndex?: number) => {
    const key = repeatIndex !== undefined ? `${questionId}_${repeatIndex}` : questionId;
    setAnswers(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  // Remove answers for repeatable sections
  const removeRepeatAnswers = useCallback((sectionId: string, repeatIndex: number) => {
    if (!form) return;

    setAnswers(prev => {
      const newAnswers = { ...prev };
      
      // Find the section and remove answers for all its questions at the given repeat index
      const section = form.sections.find((s: any) => (s._id || s.id) === sectionId);
      if (section) {
        section.questions?.forEach((question: any) => {
          const key = `${question._id || question.id}_${repeatIndex}`;
          delete newAnswers[key];
        });
      }
      
      return newAnswers;
    });
  }, [form]);

  // Manual save function
  const saveProgress = useCallback(async () => {
    if (!isAuthenticated || !submission?.id || !submission.can_edit) {
      return;
    }

    await saveProgressToBackend(answers, progress);
  }, [isAuthenticated, submission, answers, progress, saveProgressToBackend]);

  // Submit form function
  const submitForm = useCallback(async () => {
    if (!isAuthenticated || !submission?.id || !submission.can_edit || !canSubmit) {
      throw new Error('Cannot submit form');
    }

    try {
      // First save the latest progress
      await saveProgressToBackend(answers, progress);

      // Then submit the form
      const result = await PublicSubmissionAPI.submitSubmission(
        submission.id,
        accessToken || undefined
      );

      // Update submission status
      updateSubmission({
        ...submission,
        status: 'submitted',
        can_edit: false,
      });

      return result;
    } catch (error) {
      console.error('Failed to submit form:', error);
      throw error;
    }
  }, [
    isAuthenticated,
    submission,
    canSubmit,
    answers,
    progress,
    accessToken,
    saveProgressToBackend,
    updateSubmission
  ]);

  return {
    answers,
    updateAnswer,
    progress,
    canSubmit,
    saving,
    lastSaved,
    saveProgress,
    submitForm,
    removeRepeatAnswers,
    isAuthenticated,
    canEdit: submission?.can_edit || false,
    submissionStatus: submission?.status || 'draft',
  };
}
