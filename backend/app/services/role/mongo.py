from datetime import datetime
from typing import Dict, <PERSON>, Optional, <PERSON><PERSON>

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.database import get_database
from app.core.errors import DatabaseError, handle_error
from app.models.role import Permission, Role
from app.services.base import BaseService
from app.services.role.interfaces import RoleServiceInterface
from app.utils.common import PyObjectId
from app.utils.rbac.rbac import get_resources_and_actions


class RoleService(BaseService, RoleServiceInterface):
    """MongoDB implementation of RoleService."""

    def __init__(self, db: AsyncIOMotorDatabase, app=None):
        super().__init__()
        self.db = db
        self._app = app

    async def initialize(self) -> None:
        """Initialize database connection."""
        if self.db is None:
            self.db = await get_database()
        # Initialize resources and actions if app is provided
        if self._app:
            self._permissions = get_resources_and_actions(self._app)

    async def cleanup(self) -> None:
        """Cleanup database connection."""
        pass

    def _build_query(self, org_id: Optional[str] = None) -> Dict:
        """Build MongoDB query with optional org_id filter."""
        query = {}
        if org_id:
            query["org_id"] = ObjectId(org_id)
        return query

    async def create_role(
        self,
        name: str,
        description: str,
        permissions: List[Permission],
        is_system: bool = False,
        org_id: Optional[str] = None,
    ) -> Role:
        """Create a new role."""
        self.logger.info(f"Creating new role: {name} for org: {org_id}")
        try:
            role = Role(
                name=name,
                description=description,
                permissions=permissions,
                is_system=is_system,
                org_id=PyObjectId(org_id) if org_id else None,  # type: ignore
                created_at=int(datetime.now().timestamp()),
            )
            role = await role.save()
            self.logger.info(f"Role created with ID: {role.id}")
            return role
        except Exception as e:
            error = DatabaseError(
                message="Error creating role",
                details={
                    "name": name,
                    "description": description,
                    "is_system": is_system,
                    "org_id": org_id,
                    "error": str(e),
                },
                should_alert=True,
            )
            await handle_error(error)
            raise error.to_http_exception()

    async def get_role(
        self, role_id: str, org_id: Optional[str] = None
    ) -> Optional[Role]:
        """Get a role by ID."""
        self.logger.info(f"Getting role: {role_id} for org: {org_id}")
        try:
            query = {"_id": ObjectId(role_id)}
            if org_id:
                query["org_id"] = ObjectId(org_id)

            role_data = await self.db.roles.find_one(query)
            if not role_data:
                self.logger.warning(f"Role not found: {role_id}")
                return None
            return Role(**role_data)
        except Exception as e:
            error = DatabaseError(
                message="Error retrieving role",
                details={"role_id": role_id, "org_id": org_id, "error": str(e)},
                should_alert=True,
            )
            await handle_error(error)
            raise error.to_http_exception()

    async def get_roles_by_org(self, org_id: str) -> List[Role]:
        """Get all roles for an organization."""
        self.logger.info(f"Getting roles for org: {org_id}")
        try:
            roles = await Role.find_many({"org_id": ObjectId(org_id)})
            return roles
        except Exception as e:
            error = DatabaseError(
                message="Error retrieving organization roles",
                details={"org_id": org_id, "error": str(e)},
                should_alert=True,
            )
            await handle_error(error)
            raise error.to_http_exception()

    async def update_role(
        self,
        role_id: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        permissions: Optional[List[Permission]] = None,
        org_id: Optional[str] = None,
    ) -> Optional[Role]:
        """Update an existing role."""
        self.logger.info(f"Updating role: {role_id} for org: {org_id}")
        try:
            # Build query with org_id if provided
            query = {"_id": ObjectId(role_id)}
            if org_id:
                query["org_id"] = ObjectId(org_id)

            update_data = {}
            if name:
                update_data["name"] = name
            if description:
                update_data["description"] = description
            if permissions:
                update_data["permissions"] = [p.dict() for p in permissions]
            update_data["updated_at"] = datetime.utcnow()

            if not update_data:
                self.logger.warning("No update data provided")
                return None

            result = await self.db.roles.find_one_and_update(
                query, {"$set": update_data}, return_document=True
            )

            if not result:
                self.logger.warning(f"Role not found: {role_id}")
                return None

            self.logger.info(f"Role updated successfully: {role_id}")
            return Role(**result)
        except Exception as e:
            error = DatabaseError(
                message="Error updating role",
                details={
                    "role_id": role_id,
                    "org_id": org_id,
                    "update_data": update_data,
                    "error": str(e),
                },
                should_alert=True,
            )
            await handle_error(error)
            raise error.to_http_exception()

    async def delete_role(self, role_id: str, org_id: Optional[str] = None) -> bool:
        """Delete a role."""
        self.logger.info(f"Deleting role: {role_id} for org: {org_id}")
        try:
            # Build query with org_id if provided
            query = {"_id": ObjectId(role_id)}
            if org_id:
                query["org_id"] = ObjectId(org_id)

            # Check if role exists and is not a system role
            role = await self.get_role(role_id, org_id)
            if not role:
                self.logger.warning(f"Role not found: {role_id}")
                return False
            if role.is_system:
                self.logger.warning(f"Cannot delete system role: {role_id}")
                return False

            result = await self.db.roles.delete_one(query)
            success = result.deleted_count > 0
            self.logger.info(f"Role deletion {'successful' if success else 'failed'}")
            return success
        except Exception as e:
            error = DatabaseError(
                message="Error deleting role",
                details={"role_id": role_id, "org_id": org_id, "error": str(e)},
                should_alert=True,
            )
            await handle_error(error)
            raise error.to_http_exception()

    async def add_permission(
        self, role_id: str, permission: Permission, org_id: Optional[str] = None
    ) -> Optional[Role]:
        """Add a permission to a role."""
        self.logger.info(f"Adding permission to role: {role_id} for org: {org_id}")
        try:
            role = await self.get_role(role_id, org_id)
            if not role:
                self.logger.warning(f"Role not found: {role_id}")
                return None

            if permission not in role.permissions:
                role.permissions.append(permission)
                query = {"_id": ObjectId(role_id)}
                if org_id:
                    query["org_id"] = ObjectId(org_id)

                await self.db.roles.update_one(
                    query,
                    {
                        "$set": {
                            "permissions": [p.dict() for p in role.permissions],
                            "updated_at": datetime.utcnow(),
                        }
                    },
                )
            return role
        except Exception as e:
            error = DatabaseError(
                message="Error adding permission",
                details={
                    "role_id": role_id,
                    "org_id": org_id,
                    "permission": permission.dict(),
                    "error": str(e),
                },
                should_alert=True,
            )
            await handle_error(error)
            raise error.to_http_exception()

    async def remove_permission(
        self, role_id: str, permission: Permission, org_id: Optional[str] = None
    ) -> Optional[Role]:
        """Remove a permission from a role."""
        self.logger.info(f"Removing permission from role: {role_id} for org: {org_id}")
        try:
            role = await self.get_role(role_id, org_id)
            if not role:
                self.logger.warning(f"Role not found: {role_id}")
                return None

            if permission in role.permissions:
                role.permissions.remove(permission)
                query = {"_id": ObjectId(role_id)}
                if org_id:
                    query["org_id"] = ObjectId(org_id)

                await self.db.roles.update_one(
                    query,
                    {
                        "$set": {
                            "permissions": [p.dict() for p in role.permissions],
                            "updated_at": datetime.utcnow(),
                        }
                    },
                )
            return role
        except Exception as e:
            error = DatabaseError(
                message="Error removing permission",
                details={
                    "role_id": role_id,
                    "org_id": org_id,
                    "permission": permission.dict(),
                    "error": str(e),
                },
                should_alert=True,
            )
            await handle_error(error)
            raise error.to_http_exception()

    async def check_permission(
        self, role_id: str, permission: Permission, org_id: Optional[str] = None
    ) -> bool:
        """Check if a role has a specific permission."""
        self.logger.info(f"Checking permission for role: {role_id} for org: {org_id}")
        try:
            role = await self.get_role(role_id, org_id)
            if not role:
                self.logger.warning(f"Role not found: {role_id}")
                return False
            return permission in role.permissions
        except Exception as e:
            error = DatabaseError(
                message="Error checking permission",
                details={
                    "role_id": role_id,
                    "org_id": org_id,
                    "permission": permission.dict(),
                    "error": str(e),
                },
                should_alert=True,
            )
            await handle_error(error)
            raise error.to_http_exception()

    async def get_all_roles(
        self,
        org_id: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
        sort: Optional[List[tuple]] = None,
        include_deleted: bool = False,
    ) -> List[Dict]:
        """Get all roles in the system."""
        self.logger.info(f"Fetching all roles for org: {org_id}")
        try:
            query = {}
            if org_id:
                query["org_id"] = org_id
            roles = await Role.find_many(
                query=query,
                skip=skip,
                limit=limit,
                sort=sort,
                include_deleted=include_deleted,
            )
            # Convert to Role objects to ensure proper serialization of nested permissions

            return [role.model_dump(by_alias=True) for role in roles]
        except Exception as e:
            error = DatabaseError(
                message="Error fetching all roles",
                details={"org_id": org_id, "error": str(e)},
                should_alert=True,
            )
            await handle_error(error)
            raise error.to_http_exception()

    async def get_role_permissions(
        self, role_id: str, org_id: Optional[str] = None
    ) -> List[Dict]:
        """Get all permissions for a specific role."""
        self.logger.info(f"Fetching permissions for role: {role_id} for org: {org_id}")
        try:
            role = await self.get_role(role_id, org_id)
            if not role:
                self.logger.warning(f"Role not found: {role_id}")
                return []
            return [p.dict() for p in role.permissions]
        except Exception as e:
            error = DatabaseError(
                message="Error fetching role permissions",
                details={"role_id": role_id, "org_id": org_id, "error": str(e)},
                should_alert=True,
            )
            await handle_error(error)
            raise error.to_http_exception()

    async def create_default_roles(self, org_id: str) -> Tuple[Role, Role]:
        """
        Create default roles for a new organization:
        1. General Partner - Full access to everything
        2. Analyst - Read/Write access but cannot invite users
        """
        self.logger.info(f"Creating default roles for org: {org_id}")
        try:
            # Create General Partner role with all permissions
            gp_permissions = [
                Permission(resource=permission["resource"], action=permission["action"])
                for permission in self._permissions
            ]
            self.logger.info(
                f"Creating General Partner role with {len(gp_permissions)} permissions"
            )

            gp_role = await self.create_role(
                name="General Partner",
                description="Full access to all organization resources",
                permissions=gp_permissions,
                is_system=True,
                org_id=org_id,
            )

            # Create Analyst role with all permissions except invite
            analyst_permissions = [
                Permission(resource=permission["resource"], action=permission["action"])
                for permission in self._permissions
                if permission["resource"] != "invite"  # Exclude invite action
            ]
            analyst_role = await self.create_role(
                name="Analyst",
                description="Read/Write access to all resources except user management",
                permissions=analyst_permissions,
                is_system=True,
                org_id=org_id,
            )

            self.logger.info(f"Created default roles for org: {org_id}")
            return gp_role, analyst_role
        except Exception as e:
            error = DatabaseError(
                message="Error creating default roles",
                details={"org_id": org_id, "error": str(e)},
                should_alert=True,
            )
            await handle_error(error)
            raise error.to_http_exception()
