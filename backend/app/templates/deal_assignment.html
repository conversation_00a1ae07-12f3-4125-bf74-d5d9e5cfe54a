<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deal Assignment - TractionX</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1e293b;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 520px;
            margin: 32px auto;
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(30, 41, 59, 0.08);
            overflow: hidden;
        }
        .header {
            background: #1d4ed8;
            color: #fff;
            padding: 32px 24px 16px 24px;
            text-align: center;
        }
        .logo {
            font-size: 28px;
            font-weight: 700;
            letter-spacing: -1px;
        }
        .tagline {
            font-size: 16px;
            opacity: 0.85;
            margin-top: 4px;
        }
        .content {
            padding: 32px 24px;
        }
        .greeting {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #1e293b;
        }
        .message {
            font-size: 16px;
            color: #334155;
            margin-bottom: 24px;
            line-height: 1.6;
        }
        .deal-info {
            background: #f1f5f9;
            border-radius: 12px;
            padding: 20px;
            margin: 24px 0;
            border-left: 4px solid #1d4ed8;
        }
        .deal-name {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 6px;
        }
        .assigned-by {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 12px;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(90deg, #1d4ed8 0%, #2563eb 100%);
            color: #fff;
            text-decoration: none;
            padding: 12px 28px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin-top: 8px;
            box-shadow: 0 2px 8px rgba(30, 64, 175, 0.10);
            transition: background 0.2s;
        }
        .cta-button:hover {
            background: #2563eb;
        }
        .footer {
            background: #f8fafc;
            padding: 20px 24px;
            text-align: center;
            font-size: 13px;
            color: #64748b;
        }
        @media (max-width: 600px) { 
            .container { margin: 12px; }
            .content, .header, .footer { padding: 16px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">TractionX</div>
            <div class="tagline">Deal Assignment Notification</div>
        </div>
        <div class="content">
            <div class="greeting">Hi {{user_name}},</div>
            <div class="message">
                You've just been assigned a new deal to review.<br>
                <strong>{{assigned_by_name}}</strong> has assigned you to work on this opportunity.
            </div>
            <div class="deal-info">
                <div class="deal-name">{{company_name}}</div>
                <div class="assigned-by">Assigned by {{assigned_by_name}}</div>
                <a href="{{deal_url}}" style="color:#ffffff;text-decoration:none;" class="cta-button">View Deal</a>
            </div>
            <div class="message">
                Click the button above to access the deal details, review the submission, and begin your analysis. You can add notes, update the status, and collaborate with your team directly in the platform.
            </div>
        </div>
        <div class="footer">
            This email was sent by TractionX. If you have any questions, please contact our support team.<br>
            <a href="#" style="color:#1d4ed8;text-decoration:none;">Privacy Policy</a> &nbsp;|&nbsp;
            <a href="#" style="color:#1d4ed8;text-decoration:none;">Terms of Service</a> &nbsp;|&nbsp;
            <a href="#" style="color:#1d4ed8;text-decoration:none;">Support</a>
        </div>
    </div>
</body>
</html>
