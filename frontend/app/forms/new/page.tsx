"use client"

import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/core/form-builder";
import { DashboardShell } from "@/components/shell";
import { DashboardHeader } from "@/components/header";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function NewFormPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Create New Form"
        text="Create a new form to collect data from your users."
      >
        <Link href="/forms">
          <Button variant="outline">
            <ArrowLeft className="mr-2 size-4" />
            Back to Forms
          </Button>
        </Link>
      </DashboardHeader>
      <FormBuilder />
    </DashboardShell>
  );
}
