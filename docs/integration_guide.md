# Form and Thesis Integration Guide

## Introduction

This guide provides a comprehensive overview of how to integrate the Form and Thesis systems with your frontend application. It serves as an index to the detailed documentation available in the other files.

## Documentation Structure

We've organized the documentation into several focused guides:

1. **[Form and Thesis Integration Guide](form_thesis_integration.md)** - Overview and basic API usage
2. **[Form and Thesis Technical Implementation](form_thesis_technical.md)** - Technical details and data models
3. **[Form Sharing Guide](form_sharing_guide.md)** - Sharing forms via links, embeds, and QR codes
4. **[Form Engine Examples](form_engine_examples.md)** - Detailed examples of using the Form Engine API

## System Architecture

The system consists of several interconnected components:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│   Form System   │────▶│  Form Engine    │────▶│  Sharing System │
│                 │     │                 │     │                 │
└────────┬────────┘     └─────────────────┘     └─────────────────┘
         │
         │
         ▼
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│ Thesis System   │────▶│  Scoring Engine │
│                 │     │                 │
└─────────────────┘     └─────────────────┘
```

## Integration Workflow

### 1. Form Creation and Management

1. Create a form with sections and questions
2. Configure repeatable sections and visibility conditions
3. Use the Form Engine API to render the form dynamically
4. Process form submissions

### 2. Thesis Configuration

1. Create an investment thesis linked to a form
2. Configure scoring rules for relevant questions
3. Set up match rules for filtering
4. Preview and test the thesis configuration

### 3. Form Sharing

1. Create a sharing configuration for a form
2. Generate sharing links, embed codes, or QR codes
3. Track usage and analytics

### 4. Scoring and Matching

1. Submit form responses
2. Calculate scores against theses
3. Find matching theses for a submission
4. Visualize results

## API Endpoints Summary

### Form System

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/forms` | POST | Create a new form |
| `/api/v1/forms/{form_id}` | GET | Get a form by ID |
| `/api/v1/forms/{form_id}/details` | GET | Get form with all sections and questions |
| `/api/v1/forms/{form_id}/sections` | POST | Create a section in a form |
| `/api/v1/forms/sections/{section_id}/questions` | POST | Add a question to a section |
| `/api/v1/forms/{form_id}/engine` | POST | Use the Form Engine API |
| `/api/v1/forms/{form_id}/submit` | POST | Submit a form |

### Thesis System

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/scoring/thesis` | POST | Create a new thesis |
| `/api/v1/scoring/thesis/{thesis_id}` | GET | Get a thesis by ID |
| `/api/v1/scoring/thesis/{thesis_id}` | PUT | Update a thesis |
| `/api/v1/scoring/thesis/preview/{form_id}` | GET | Preview form questions for thesis configuration |
| `/api/v1/scoring/thesis/{thesis_id}/calculate-score` | POST | Calculate score for a form submission |
| `/api/v1/scoring/thesis/find-matching` | POST | Find matching theses for a submission |

### Sharing System

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/sharing` | POST | Create a sharing configuration |
| `/api/v1/sharing/{config_id}/link` | POST | Generate a sharing link |
| `/api/v1/sharing/{config_id}/embed` | POST | Generate an embed code |
| `/api/v1/sharing/{config_id}/qr` | POST | Generate a QR code |
| `/api/v1/sharing/{config_id}/stats` | GET | Get sharing statistics |

## Frontend Implementation Guide

### Required Components

1. **Form Builder**:
   - Interface for creating and editing forms
   - Section and question management
   - Repeatable section configuration
   - Visibility condition setup

2. **Form Renderer**:
   - Dynamic form rendering based on Form Engine API
   - Support for both sequential and full modes
   - Handling of repeatable sections
   - Implementation of visibility conditions

3. **Thesis Configuration**:
   - Interface for creating and editing theses
   - Scoring rule configuration
   - Match rule setup
   - Preview and testing

4. **Sharing Interface**:
   - Generation of sharing links, embeds, and QR codes
   - Preview of shared forms
   - Analytics dashboard

5. **Scoring Visualization**:
   - Display of scores and breakdowns
   - Matching thesis results
   - Recommendation engine

### Implementation Steps

1. **Setup Authentication**:
   - Implement JWT token handling
   - Include organization context in all requests

2. **Create Base API Client**:
   - Wrapper for API calls with error handling
   - Authentication header management
   - Response parsing and validation

3. **Implement Form Management**:
   - Form creation and editing
   - Section and question management
   - Form submission handling

4. **Integrate Form Engine**:
   - Dynamic form rendering
   - Conditional visibility
   - Repeatable section handling

5. **Implement Thesis Management**:
   - Thesis creation and configuration
   - Scoring and match rule setup
   - Testing and validation

6. **Add Sharing Functionality**:
   - Sharing configuration
   - Link, embed, and QR code generation
   - Analytics tracking

7. **Implement Scoring Visualization**:
   - Score calculation and display
   - Matching thesis results
   - Recommendation engine

## Example Implementation

Here's a simplified React implementation of a form renderer using the Form Engine API:

```jsx
import React, { useState, useEffect } from 'react';
import axios from 'axios';

const FormRenderer = ({ formId, apiBaseUrl, authToken, orgId }) => {
  const [form, setForm] = useState(null);
  const [answers, setAnswers] = useState({});
  const [currentSection, setCurrentSection] = useState(null);
  const [visibleQuestions, setVisibleQuestions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Initialize the form
  useEffect(() => {
    fetchFormData();
  }, [formId]);

  // Fetch form data from the Form Engine API
  const fetchFormData = async () => {
    setLoading(true);
    try {
      const response = await axios.post(
        `${apiBaseUrl}/api/v1/forms/${formId}/engine`,
        {
          answers,
          mode: 'sequential',
          include_dependencies: true,
          include_validation: true,
          include_visibility_rules: true
        },
        {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'X-ORG-ID': orgId,
            'Content-Type': 'application/json'
          }
        }
      );
      
      setForm(response.data.form);
      setCurrentSection(response.data.current_section);
      setVisibleQuestions(response.data.visible_questions);
      setLoading(false);
    } catch (err) {
      setError('Failed to load form. Please try again.');
      setLoading(false);
      console.error(err);
    }
  };

  // Handle answer changes
  const handleAnswerChange = (questionId, value) => {
    const newAnswers = { ...answers, [questionId]: value };
    setAnswers(newAnswers);
    
    // Refetch form data to update visibility based on new answers
    fetchFormData();
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      const response = await axios.post(
        `${apiBaseUrl}/api/v1/forms/${formId}/submit`,
        { answers },
        {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'X-ORG-ID': orgId,
            'Content-Type': 'application/json'
          }
        }
      );
      
      // Handle successful submission
      console.log('Form submitted successfully:', response.data);
    } catch (err) {
      // Handle submission error
      console.error('Form submission failed:', err);
    }
  };

  // Render loading state
  if (loading) {
    return <div>Loading form...</div>;
  }

  // Render error state
  if (error) {
    return <div>{error}</div>;
  }

  // Render the form
  return (
    <div className="form-container">
      <h1>{form.name}</h1>
      <p>{form.description}</p>
      
      {currentSection && (
        <div className="section">
          <h2>{currentSection.title}</h2>
          <p>{currentSection.description}</p>
          
          {visibleQuestions.map(question => (
            <div key={question._id} className="question">
              <label>{question.label}</label>
              {question.help_text && <p className="help-text">{question.help_text}</p>}
              
              {/* Render different input types based on question type */}
              {renderQuestionInput(question, answers, handleAnswerChange)}
              
              {question.required && <span className="required">*</span>}
            </div>
          ))}
        </div>
      )}
      
      <div className="form-actions">
        <button onClick={handleSubmit}>Submit</button>
      </div>
    </div>
  );
};

// Helper function to render the appropriate input for each question type
const renderQuestionInput = (question, answers, handleAnswerChange) => {
  const value = answers[question._id] || '';
  
  switch (question.type) {
    case 'short_text':
      return (
        <input
          type="text"
          value={value}
          onChange={(e) => handleAnswerChange(question._id, e.target.value)}
        />
      );
    
    case 'long_text':
      return (
        <textarea
          value={value}
          onChange={(e) => handleAnswerChange(question._id, e.target.value)}
        />
      );
    
    case 'number':
      return (
        <input
          type="number"
          value={value}
          onChange={(e) => handleAnswerChange(question._id, parseInt(e.target.value, 10))}
        />
      );
    
    case 'single_select':
      return (
        <select
          value={value}
          onChange={(e) => handleAnswerChange(question._id, e.target.value)}
        >
          <option value="">Select an option</option>
          {question.options.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      );
    
    // Add cases for other question types
    
    default:
      return <div>Unsupported question type: {question.type}</div>;
  }
};

export default FormRenderer;
```

## Best Practices

1. **Error Handling**:
   - Implement robust error handling for all API calls
   - Provide clear error messages to users
   - Log errors for debugging

2. **Performance Optimization**:
   - Cache form structures to reduce API calls
   - Use pagination for large datasets
   - Implement lazy loading for form sections

3. **Security**:
   - Validate all user inputs
   - Implement proper authentication and authorization
   - Use HTTPS for all API calls

4. **User Experience**:
   - Provide clear navigation and instructions
   - Implement form validation with helpful error messages
   - Save form progress to prevent data loss

5. **Accessibility**:
   - Ensure all form elements have proper labels
   - Support keyboard navigation
   - Test with screen readers

6. **Mobile Optimization**:
   - Design responsive interfaces
   - Optimize for touch interactions
   - Test on various devices and screen sizes

## Conclusion

By following this integration guide, you can build a robust frontend application that leverages the Form and Thesis systems to create, manage, share, and score forms. The modular architecture allows for flexible implementation and customization to meet your specific requirements.

For detailed information on specific aspects of the integration, refer to the specialized documentation files listed at the beginning of this guide.
