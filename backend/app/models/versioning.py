"""
Resource Versioning Models

This module defines the data models for resource versioning, allowing for
historical snapshots of resources to be stored and retrieved.
"""

from datetime import datetime, timezone
from typing import Dict, Any, Optional, ClassVar
from pydantic import Field

from app.models.base import TractionXModel
from app.utils.common import ObjectIdField


class ResourceVersion(TractionXModel):
    """
    Represents a historical snapshot of a resource.

    This model stores immutable snapshots of resources at specific points in time,
    allowing for version history tracking and rollback capabilities.
    """
    extra_model_config: ClassVar[dict] = {
        "json_schema_extra": {
            "example": {
                "resource_type": "Form",
                "resource_id": "507f1f77bcf86cd799439011",
                "version": 1,
                "snapshot": {
                    "name": "Sample Form",
                    "description": "A sample form",
                    "version": 1,
                    "is_active": True
                },
                "created_at": 1609459200,
                "created_by": "507f1f77bcf86cd799439012"
            }
        }
    }

    id: ObjectIdField = Field(default_factory=ObjectIdField, alias="_id")
    resource_type: str = Field(..., description="Type of resource (e.g., 'Form', 'Thesis')")
    resource_id: ObjectIdField = Field(..., description="ID of the main resource")
    version: int = Field(..., description="Version number of the snapshot")
    snapshot: Dict[str, Any] = Field(..., description="Complete serialized resource data")
    new_resource_id: Optional[ObjectIdField] = Field(
        default=None,
        description="ID of the new resource created from this version (if applicable)"
    )
    created_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp()),
        description="When this snapshot was created (UTC timestamp)"
    )
    updated_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp()),
        description="When this snapshot was last updated (UTC timestamp)"
    )
    created_by: Optional[ObjectIdField] = Field(
        default=None,
        description="ID of the user who created this snapshot"
    )

    # Define the collection name
    model_config = {"collection": "resource_versions"}
