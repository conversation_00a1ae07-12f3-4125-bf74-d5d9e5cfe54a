"use client"

import { useState, useCallback, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  XCircle, 
  Download,
  AlertCircle,
  Loader2,
  X
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useToast } from '@/components/ui/use-toast'
import { DealAPI } from '@/lib/api/deal-api'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface ExcelUploadModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onUploadComplete?: () => void
}

interface UploadJob {
  id: string
  status: 'queued' | 'processing' | 'completed' | 'failed'
  created_at?: string
}

interface UploadResult {
  created: number
  failed: number
  errors: Array<{
    row: number
    reason: string
  }>
}

export function ExcelUploadModal({ 
  open, 
  onOpenChange, 
  onUploadComplete 
}: ExcelUploadModalProps) {
  const { toast } = useToast()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isDragOver, setIsDragOver] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadJob, setUploadJob] = useState<UploadJob | null>(null)
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null)
  const [showSuccess, setShowSuccess] = useState(false)
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null)
  const [sampleFileMissing, setSampleFileMissing] = useState(false)

  // Download sample template
  const downloadSampleTemplate = () => {
    fetch('/assets/sample-deal-upload.xlsx', { method: 'HEAD' })
      .then(res => {
        if (res.ok) {
          setSampleFileMissing(false)
          const link = document.createElement('a')
          link.href = '/assets/sample-deal-upload.xlsx'
          link.download = 'sample-deal-upload.xlsx'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        } else {
          setSampleFileMissing(true)
        }
      })
      .catch(() => setSampleFileMissing(true))
  }

  // Handle file selection
  const handleFileSelect = useCallback((file: File) => {
    if (!file) return

    // Validate file type
    const validTypes = ['.xlsx', '.csv']
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
    
    if (!validTypes.includes(fileExtension)) {
      toast({
        title: "Invalid file type",
        description: "Please upload a .xlsx or .csv file only.",
        variant: "destructive",
      })
      return
    }

    // Validate file size (2MB limit)
    if (file.size > 2 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please upload a file smaller than 2MB.",
        variant: "destructive",
      })
      return
    }

    uploadFile(file)
  }, [toast])

  // Upload file to backend
  const uploadFile = async (file: File) => {
    try {
      setIsUploading(true)
      
      const formData = new FormData()
      formData.append('file', file)
      
      const response = await DealAPI.uploadExcelDeals(formData)
      
      if (response.job?.id) {
        setUploadJob({
          id: response.job.id,
          status: response.job.status as any,
          created_at: response.job.created_at
        })
        
        // Start polling for job status
        startPolling(response.job.id)
        
        toast({
          title: "Upload queued",
          description: "Your Excel file has been uploaded and is being processed.",
        })
      }
    } catch (error: any) {
      console.error('Upload error:', error)
      toast({
        title: "Upload failed",
        description: error.message || "Failed to upload file. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  // Start polling for job status
  const startPolling = (jobId: string) => {
    const interval = setInterval(async () => {
      try {
        const jobStatus = await DealAPI.getJobStatus(jobId)
        
        if (jobStatus.status === 'completed') {
          clearInterval(interval)
          setPollingInterval(null)
          
          // Fetch results
          if (jobStatus.result?.results) {
            setUploadResult(jobStatus.result.results)
            setShowSuccess(true)
          }
          
          onUploadComplete?.()
          
        } else if (jobStatus.status === 'failed') {
          clearInterval(interval)
          setPollingInterval(null)
          
          toast({
            title: "Processing failed",
            description: "Failed to process your Excel file. Please try again.",
            variant: "destructive",
          })
        }
        
        setUploadJob(prev => prev ? { ...prev, status: jobStatus.status as any } : null)
        
      } catch (error) {
        console.error('Polling error:', error)
      }
    }, 5000) // Poll every 5 seconds
    
    setPollingInterval(interval)
  }

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }, [handleFileSelect])

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  // Download error report
  const downloadErrorReport = () => {
    if (!uploadResult) return
    
    const errorData = {
      upload_summary: {
        created: uploadResult.created,
        failed: uploadResult.failed,
        total_rows: uploadResult.created + uploadResult.failed
      },
      errors: uploadResult.errors
    }
    
    const blob = new Blob([JSON.stringify(errorData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'upload-error-report.json'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // Close modal and cleanup
  const handleClose = () => {
    if (pollingInterval) {
      clearInterval(pollingInterval)
      setPollingInterval(null)
    }
    setUploadJob(null)
    setUploadResult(null)
    setShowSuccess(false)
    setIsUploading(false)
    onOpenChange(false)
  }

  // Available deal statuses for reference
  const availableStatuses = [
    'new', 'triage', 'reviewed', 'approved', 'excluded', 'closed'
  ]

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto p-0">
        <DialogHeader className="sticky top-0 z-10 bg-white/80 backdrop-blur-md px-6 pt-6 pb-2 border-b">
          <DialogTitle className="flex items-center gap-2 text-xl">
            <FileText className="h-5 w-5" />
            Bulk Import Deals
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 px-6 py-6">
          {/* Sample Template Download */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200 gap-3">
            <div>
              <h3 className="font-medium text-blue-900 text-base">Need a template?</h3>
              <p className="text-sm text-blue-700">
                Download our sample Excel template with the correct column headers
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={downloadSampleTemplate}
              className="border-blue-300 text-blue-700 hover:bg-blue-100"
            >
              <Download className="h-4 w-4 mr-2" />
              Download Template
            </Button>
          </div>
          {sampleFileMissing && (
            <Alert variant="destructive" className="mb-2">
              <AlertDescription>
                <span className="font-medium">Sample file not found.</span> Please add <code>sample-deal-upload.xlsx</code> to <code>/public/assets/</code>.
              </AlertDescription>
            </Alert>
          )}

          {/* Required Format Info */}
          <Card className="max-w-2xl mx-auto">
            <CardContent className="p-4">
              <h4 className="font-medium mb-3 text-base">Required Excel Format</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="font-medium">company_name</span>
                  <Badge variant="secondary" className="ml-2">Required</Badge>
                </div>
                <div>
                  <span className="font-medium">stage</span>
                  <Badge variant="outline" className="ml-2">Optional</Badge>
                </div>
                <div>
                  <span className="font-medium">sector</span>
                  <Badge variant="outline" className="ml-2">Optional</Badge>
                </div>
                <div>
                  <span className="font-medium">status</span>
                  <Badge variant="outline" className="ml-2">Optional</Badge>
                </div>
                <div>
                  <span className="font-medium">company_website</span>
                  <Badge variant="outline" className="ml-2">Optional</Badge>
                </div>
                <div>
                  <span className="font-medium">short_description</span>
                  <Badge variant="outline" className="ml-2">Optional</Badge>
                </div>
                <div>
                  <span className="font-medium">invited_email</span>
                  <Badge variant="outline" className="ml-2">Optional</Badge>
                </div>
                <div>
                  <span className="font-medium">notes</span>
                  <Badge variant="outline" className="ml-2">Optional</Badge>
                </div>
                <div>
                  <span className="font-medium">tags</span>
                  <Badge variant="outline" className="ml-2">Optional</Badge>
                </div>
              </div>
              <div className="mt-3 p-3 bg-gray-50 rounded text-xs">
                <p className="font-medium mb-1">Available status values:</p>
                <p className="text-gray-600">{availableStatuses.join(', ')}</p>
              </div>
            </CardContent>
          </Card>

          {/* Upload Area */}
          {!uploadJob && !showSuccess && (
            <Card className="max-w-2xl mx-auto">
              <CardContent className="p-6">
                <div
                  className={cn(
                    "border-2 border-dashed rounded-lg p-8 text-center transition-colors flex flex-col items-center justify-center",
                    isDragOver 
                      ? "border-blue-500 bg-blue-50" 
                      : "border-gray-300 hover:border-gray-400"
                  )}
                  style={{ minHeight: 220 }}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Upload className={cn(
                    "h-12 w-12 mb-4 transition-colors",
                    isDragOver ? "text-blue-500" : "text-gray-400"
                  )} />
                  <h3 className="text-lg font-medium mb-2">
                    {isDragOver ? "Drop your file here" : "Drop your Excel file here"}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    or click to browse files
                  </p>
                  <p className="text-sm text-gray-500">
                    Supports .xlsx and .csv files (max 2MB)
                  </p>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".xlsx,.csv"
                    onChange={handleFileInputChange}
                    className="hidden"
                  />
                </div>
                <div className="mt-4 flex justify-center">
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploading}
                    className="w-full max-w-xs"
                  >
                    {isUploading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Uploading...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4 mr-2" />
                        Choose File
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Processing Status */}
          {uploadJob && uploadJob.status === 'processing' && (
            <Card className="max-w-2xl mx-auto">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                  <div className="flex-1">
                    <h3 className="font-medium">Processing your Excel upload...</h3>
                    <p className="text-sm text-gray-600">
                      This may take ~1.5 minutes. You can continue browsing.
                    </p>
                  </div>
                  <Badge variant="secondary">In progress</Badge>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Success State */}
          {showSuccess && uploadResult && (
            <Card className="border-green-200 bg-green-50 max-w-2xl mx-auto">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <CheckCircle className="h-8 w-8 text-green-500" />
                  <div>
                    <h3 className="font-medium text-green-900">Upload Complete! 🎉</h3>
                    <p className="text-sm text-green-700">
                      Your deals have been processed successfully
                    </p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center p-3 bg-green-100 rounded">
                    <div className="text-2xl font-bold text-green-700">
                      {uploadResult.created}
                    </div>
                    <div className="text-sm text-green-600">Deals Created</div>
                  </div>
                  <div className="text-center p-3 bg-red-100 rounded">
                    <div className="text-2xl font-bold text-red-700">
                      {uploadResult.failed}
                    </div>
                    <div className="text-sm text-red-600">Failed Rows</div>
                  </div>
                </div>
                {uploadResult.failed > 0 && (
                  <div className="mb-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={downloadErrorReport}
                      className="w-full max-w-xs"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download Error Report
                    </Button>
                  </div>
                )}
                <div className="flex space-x-3">
                  <Button
                    onClick={handleClose}
                    className="flex-1"
                  >
                    View My Deals
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowSuccess(false)
                      setUploadResult(null)
                    }}
                    className="flex-1"
                  >
                    Upload Another
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
} 