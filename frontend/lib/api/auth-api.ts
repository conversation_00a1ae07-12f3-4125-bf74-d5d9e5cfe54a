import axios from 'axios';
import { env } from '@/env.mjs';

// Define the API base URL - ensure it matches the CORS configuration in the backend
const API_BASE_URL = env.NEXT_PUBLIC_API_URL;

// Note: For refresh token calls, we use raw axios to avoid circular dependency
// All other calls should use apiClient for consistent auth handling

// Log the API URL being used
console.log('Using API URL:', API_BASE_URL);

/**
 * Helper function to create auth headers for API requests
 * @param token Access token
 * @param orgId Organization ID
 * @returns Headers object with auth and org ID
 */
export const createAuthHeaders = (token?: string, orgId?: string): Record<string, string> => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  if (orgId) {
    headers['X-ORG-ID'] = orgId;
  }

  return headers;
};

// Export API_BASE_URL for other modules
export { API_BASE_URL };

// Define interfaces for request and response types
interface LoginRequest {
  email: string;
  password: string;
  device_info?: Record<string, any>;
  ip_address?: string;
}

interface TokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  requires_password_reset: boolean;
}

interface UserResponse {
  id: string;
  name: string;
  email: string;
  org_id: string;
  role_id: string | null;
  status: string;
  is_active: boolean;
  is_superuser: boolean;
}

interface organisationResponse {
  id: string;
  name: string;
  subdomain: string;
  description: string;
  logo_url: string;
}

// Create the auth API client
const AuthAPI = {
  /**
   * Login with email and password
   * @param credentials Login credentials
   * @returns Token response and headers
   */
  async login(credentials: LoginRequest): Promise<{
    data: TokenResponse;
    headers: Record<string, string>;
  }> {
    try {
      console.log('Sending login request for email:', credentials.email);

      // Validate credentials
      if (!credentials.email || !credentials.password) {
        throw new Error('Email and password are required');
      }

      const response = await axios.post(`${API_BASE_URL}/login`, credentials, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Log success but don't log the full response (for security)
      console.log('Login successful for:', credentials.email);
      console.log('Response status:', response.status);

      // Check if we have the expected data
      if (!response.data.access_token || !response.data.refresh_token) {
        console.error('Invalid login response - missing tokens');
        throw new Error('Authentication failed - invalid response from server');
      }

      // Store tokens in localStorage
      localStorage.setItem('token', response.data.access_token);
      localStorage.setItem('refreshToken', response.data.refresh_token);

      // If the response includes an organization ID, store it
      if (response.data.org_id) {
        localStorage.setItem('orgId', response.data.org_id);
        console.log('Stored organization ID from login response:', response.data.org_id);
      }

      return {
        data: response.data,
        headers: response.headers as Record<string, string>,
      };
    } catch (error: any) {
      // Enhanced error logging
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Login error - server response:', {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        });
      } else if (error.request) {
        // The request was made but no response was received
        console.error('Login error - no response received:', error.request);
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Login error:', error.message);
      }
      throw error;
    }
  },

  /**
   * Get current user details
   * @param token Access token
   * @param orgId Organization ID
   * @returns User details
   */
  async getCurrentUser(token: string, orgId: string): Promise<UserResponse> {
    try {
      console.log('Getting current user details');

      const response = await axios.get(`${API_BASE_URL}/users/me`, {
        headers: createAuthHeaders(token, orgId),
      });

      console.log('Current user response:', response);

      // Store the user's organization ID if available
      if (response.data && response.data.org_id) {
        localStorage.setItem('orgId', response.data.org_id);
        console.log('Stored organization ID from user response:', response.data.org_id);
      }

      return response.data;
    } catch (error) {
      console.error('Get user error:', error);
      throw error;
    }
  },

  /**
   * Get organizations
   * @param token Access token
   * @param organization_id
   * @returns Organizations
   */
  async getOrganizations(token: string, organization_id: string): Promise<organisationResponse[]> {
    try {
      console.log('Getting organizations');

      const response = await axios.get(`${API_BASE_URL}/organizations/${organization_id}`, {
        headers: createAuthHeaders(token, organization_id),
      });

      console.log('Organizations response:', response);

      // Store the organization ID in localStorage if we have a valid response
      if (response.data && response.data.length > 0) {
        // Store the first organization's ID as the current org
        const orgId = response.data[0].id;
        localStorage.setItem('orgId', orgId);
        localStorage.setItem('orgName', response.data[0].name);
        localStorage.setItem('orgSubdomain', response.data[0].subdomain);
        localStorage.setItem('orgDescription', response.data[0].description);
        localStorage.setItem('orgLogoUrl', response.data[0].logo_url);
        console.log('Stored organization ID in localStorage:', orgId);
      }

      return response.data;
    } catch (error) {
      console.error('Get organizations error:', error);
      throw error;
    }
  },

  /**
   * Refresh access token
   * @param refreshToken Refresh token
   * @returns New token response
   */
  async refreshToken(refreshToken: string): Promise<{
    data: TokenResponse;
    headers: Record<string, string>;
  }> {
    try {
      console.log('Attempting to refresh token');

      // Send refresh_token in the request body as expected by the backend
      // const response = await axios.post(
      //   `${API_BASE_URL}/refresh`,
      //   { refresh_token: refreshToken },
      //   {
      //     headers: {
      //       'Content-Type': 'application/json',
      //     },
      //   }
      // );
      const response = await axios.post(
        `${API_BASE_URL}/refresh?refresh_token=${refreshToken}`,
        {}, // Empty body
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      console.log('Refresh token response received');

      if (!response.data.access_token) {
        console.error('Invalid refresh response - missing tokens');
        throw new Error('Token refresh failed - invalid response from server');
      }

      return {
        data: response.data,
        headers: response.headers as Record<string, string>,
      };
    } catch (error: any) {
      console.error('Token refresh error:', error);
      // If we get a 401, the refresh token is invalid/expired
      if (error.response?.status === 401) {
        console.log('Refresh token is invalid or expired');

        // Get the current token and orgId from localStorage before clearing
        const currentToken = localStorage.getItem('token');
        const currentOrgId = localStorage.getItem('orgId');

        // Try to call logout API if we have a token
        if (currentToken) {
          try {
            console.log('Calling logout API to invalidate tokens');
            await this.logout(currentToken, currentOrgId || undefined);
            console.log('Logout API call successful');
          } catch (logoutError) {
            console.error('Error during logout API call:', logoutError);
            // Continue with local cleanup even if API call fails
          }
        }

        // Clear auth state
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('orgId');
        localStorage.removeItem('user');

        // Redirect to login
        window.location.href = '/login';
      }
      throw error;
    }
  },

  /**
   * Logout user
   * @param token Access token
   * @param orgId Organization ID
   * @returns Logout response
   */
  async logout(token: string, orgId?: string): Promise<{ message: string }> {
    try {
      console.log('Calling logout API with token');

      const response = await axios.post(
        `${API_BASE_URL}/logout`,
        {},
        {
          headers: createAuthHeaders(token, orgId),
        }
      );

      console.log('Logout response:', response);
      return response.data;
    } catch (error) {
      console.error('Logout error:', error);
      // Don't throw the error, just return a success message
      // This ensures the user is logged out locally even if the API call fails
      return { message: "Logged out locally" };
    }
  },

  /**
   * Send forgot password email
   * @param email User's email address
   * @returns Success message
   */
  async forgotPassword(email: string): Promise<{ message: string }> {
    try {
      console.log('Sending forgot password request for email:', email);

      const response = await axios.post(`${API_BASE_URL}/forgot-password`, {
        email,
      }, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('Forgot password response:', response);
      return response.data;
    } catch (error: any) {
      console.error('Forgot password error:', error);
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail);
      }
      throw new Error('Failed to send password reset email');
    }
  },

  /**
   * Reset password using token
   * @param token Reset token
   * @param password New password
   * @param confirmPassword Password confirmation
   * @returns Success message
   */
  async resetPassword(token: string, password: string, confirmPassword: string): Promise<{ message: string }> {
    try {
      console.log('Sending reset password request');

      const response = await axios.post(`${API_BASE_URL}/reset-password`, {
        token,
        password,
        confirm_password: confirmPassword,
      }, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('Reset password response:', response);
      return response.data;
    } catch (error: any) {
      console.error('Reset password error:', error);
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail);
      }
      throw new Error('Failed to reset password');
    }
  },

  /**
   * Accept invitation and set password
   * @param token Invitation token
   * @param password New password
   * @param confirmPassword Password confirmation
   * @returns Success message
   */
  async acceptInvitation(token: string, password: string, confirmPassword: string): Promise<{ message: string }> {
    try {
      console.log('Sending accept invitation request');

      const response = await axios.post(`${API_BASE_URL}/accept-invitation`, {
        token,
        password,
        confirm_password: confirmPassword,
      }, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('Accept invitation response:', response);
      return response.data;
    } catch (error: any) {
      console.error('Accept invitation error:', error);
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail);
      }
      throw new Error('Failed to accept invitation');
    }
  },
};

export default AuthAPI;
export { AuthAPI };
