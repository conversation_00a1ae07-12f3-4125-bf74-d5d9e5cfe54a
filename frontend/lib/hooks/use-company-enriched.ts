import { useState, useEffect, useCallback } from 'react'
import { DealDetailAPI } from '@/lib/api/deal-detail-api'
import { useToast } from '@/components/ui/use-toast'
import { useAuth } from '@/lib/auth-context'

interface CompanyEnrichedData {
  company: {
    id: string
    companyId: string
    orgId: string
    apolloId: string
    name: string
    domain: string
    website: string
    description: string
    industry: string
    subIndustry: string | null
    employeeCount: number
    employeeCountRange: string | null
    foundedYear: number
    headquarters: string
    country: string
    city: string
    state: string | null
    fundingTotal: number | null
    fundingRounds: any[] | null
    lastFundingDate: string | null
    lastFundingAmount: number | null
    valuation: number | null
    revenue: number | null
    linkedinUrl: string | null
    twitterUrl: string | null
    facebookUrl: string | null
    email: string | null
    phone: string | null
    source: string
    confidenceScore: number | null
    enrichmentDate: string
    s3RawDataKey: string
    apolloMetadata: string
    createdAt: string
    updatedAt: string
  }
  keywords: string[]
  technologies: Array<{
    name: string
    category: string
  }>
  departments: Array<{
    department: string
    headCount: number
  }>
}

interface UseCompanyEnrichedReturn {
  data: CompanyEnrichedData | null
  loading: boolean
  error: string | null
  refreshing: boolean
  hasData: boolean
  refresh: () => Promise<void>
  reload: () => Promise<void>
}

export function useCompanyEnriched({ dealId }: { dealId: string }): UseCompanyEnrichedReturn {
  const [data, setData] = useState<CompanyEnrichedData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)
  const { toast } = useToast()
  const { isAuthenticated } = useAuth()

  const fetchCompanyEnriched = useCallback(async () => {
    // Don't fetch if not authenticated
    if (!isAuthenticated) {
      setError('Authentication required. Please log in to view company data.')
      setLoading(false)
      return
    }

    try {
      setError(null)
      console.log(`🔍 Fetching company enriched data for deal ${dealId}`)
      const companyData = await DealDetailAPI.getCompanyEnriched(dealId)
      console.log('✅ Company enriched data received:', companyData)
      setData(companyData)
    } catch (err: any) {
      // Handle different types of errors gracefully
      let errorMessage = 'Failed to fetch company enriched data'
      
      if (err?.response?.status === 401) {
        errorMessage = 'Authentication required. Please log in to view company data.'
      } else if (err?.response?.status === 403) {
        errorMessage = 'Access denied. You do not have permission to view this company data.'
      } else if (err?.response?.status === 404) {
        errorMessage = 'Company data not found for this deal.'
      } else if (err?.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.'
      } else if (err?.message) {
        errorMessage = err.message
      }
      
      console.error('❌ Error fetching company enriched data:', {
        error: err,
        message: errorMessage,
        status: err?.response?.status,
        dealId
      })
      
      setError(errorMessage)
      
      // Only show toast for non-authentication errors
      if (err?.response?.status !== 401) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        })
      }
    }
  }, [dealId, isAuthenticated, toast])

  const load = useCallback(async () => {
    setLoading(true)
    await fetchCompanyEnriched()
    setLoading(false)
  }, [fetchCompanyEnriched])

  const refresh = useCallback(async () => {
    setRefreshing(true)
    await fetchCompanyEnriched()
    setRefreshing(false)
  }, [fetchCompanyEnriched])

  const reload = useCallback(async () => {
    setLoading(true)
    setError(null)
    await fetchCompanyEnriched()
    setLoading(false)
  }, [fetchCompanyEnriched])

  useEffect(() => {
    load()
  }, [load])

  return {
    data,
    loading,
    error,
    refreshing,
    hasData: !!data,
    refresh,
    reload
  }
} 