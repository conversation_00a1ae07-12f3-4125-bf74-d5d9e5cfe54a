"use client"

import { motion, AnimatePresence } from 'framer-motion';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, Star, Target } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PremiumProgressBarProps {
  completed: number;
  total: number;
  percentage: number;
  isComplete?: boolean;
  className?: string;
}

export function PremiumProgressBar({
  completed,
  total,
  percentage,
  isComplete = false,
  className
}: PremiumProgressBarProps) {
  return (
    <motion.div
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className={cn(
        "fixed inset-x-0 top-0 z-50 border-b border-white/20 bg-white/80 shadow-lg backdrop-blur-xl",
        className
      )}
    >
      <div className="mx-auto max-w-4xl p-4">
        <div className="flex items-center justify-between gap-4">
          {/* Progress Info */}
          <div className="flex items-center gap-3">
            <motion.div
              animate={isComplete ? { rotate: [0, 360] } : {}}
              transition={{ duration: 0.6, ease: "easeInOut" }}
              className={cn(
                "flex size-8 items-center justify-center rounded-full transition-all duration-300",
                isComplete
                  ? "bg-gradient-to-r from-emerald-500 to-green-500"
                  : "bg-gradient-to-r from-indigo-500 to-cyan-500"
              )}
            >
              {isComplete ? (
                <CheckCircle className="size-5 text-white" />
              ) : (
                <Target className="size-5 text-white" />
              )}
            </motion.div>

            <div className="hidden sm:block">
              <div className="flex items-center gap-2">
                <span className="text-sm font-semibold text-slate-900">
                  {completed} of {total} complete
                </span>
                <AnimatePresence>
                  {isComplete && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Star className="size-4 text-yellow-500" />
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
              <p className="flex items-center gap-1 text-xs text-slate-500">
                {isComplete ? (
                  <>
                    <span className="text-sm">🎉</span>
                    <span>All done!</span>
                  </>
                ) : (
                  "Keep going, you're doing great!"
                )}
              </p>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="max-w-md flex-1">
            <div className="flex items-center gap-3">
              <div className="relative flex-1">
                <Progress
                  value={percentage}
                  className="h-3 bg-slate-100"
                />
                <motion.div
                  className="absolute inset-0 h-3 overflow-hidden rounded-full"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <motion.div
                    className={cn(
                      "h-full rounded-full transition-all duration-500",
                      isComplete
                        ? "bg-gradient-to-r from-emerald-500 to-green-500"
                        : "bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500"
                    )}
                    initial={{ width: 0 }}
                    animate={{ width: `${percentage}%` }}
                    transition={{ duration: 0.8, ease: "easeOut" }}
                  />
                  {!isComplete && (
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                      animate={{ x: ["-100%", "100%"] }}
                      transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                    />
                  )}
                </motion.div>
              </div>

              <motion.span
                key={percentage}
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.3 }}
                className={cn(
                  "min-w-12 text-right text-sm font-bold transition-colors duration-300",
                  isComplete ? "text-emerald-600" : "text-indigo-600"
                )}
              >
                {Math.round(percentage)}%
              </motion.span>
            </div>
          </div>
        </div>

        {/* Mobile Progress Info */}
        <div className="mt-3 text-center sm:hidden">
          <div className="flex items-center justify-center gap-2">
            <span className="text-sm font-semibold text-slate-900">
              {completed} of {total} complete
            </span>
            <AnimatePresence>
              {isComplete && (
                <motion.div
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <Star className="size-4 text-yellow-500" />
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          <p className="mt-1 flex items-center justify-center gap-1 text-xs text-slate-500">
            {isComplete ? (
              <>
                <span className="text-sm">🎉</span>
                <span>All done!</span>
              </>
            ) : (
              "Keep going, you're doing great!"
            )}
          </p>
        </div>
      </div>

      {/* Completion Celebration */}
      <AnimatePresence>
        {isComplete && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="pointer-events-none absolute inset-0"
          >
            {/* Confetti Effect */}
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute size-2 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500"
                initial={{
                  x: Math.random() * window.innerWidth,
                  y: -10,
                  rotate: 0,
                  scale: 0
                }}
                animate={{
                  y: 100,
                  rotate: 360,
                  scale: [0, 1, 0]
                }}
                transition={{
                  duration: 2,
                  delay: Math.random() * 0.5,
                  ease: "easeOut"
                }}
              />
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
