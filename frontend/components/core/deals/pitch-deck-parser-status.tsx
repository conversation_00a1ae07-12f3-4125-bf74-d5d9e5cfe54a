"use client"

import { useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Loader2, 
  CheckCircle, 
  AlertCircle, 
  FileText, 
  Clock,
  RefreshCw,
  ArrowLeft
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useParseJobStore, useJobPolling } from '@/lib/stores/parse-job-store'
import { QueueAPI } from '@/lib/api/queue-api'
import { useToast } from '@/components/ui/use-toast'

interface PitchDeckParserStatusProps {
  dealId?: string
  onComplete?: () => void
  onBack?: () => void
  onRefresh?: () => void
  className?: string
}

export function PitchDeckParserStatus({ 
  dealId, 
  onComplete, 
  onBack,
  onRefresh,
  className 
}: PitchDeckParserStatusProps) {
  const { jobState, pollJob, pollInterval, shouldPoll } = useJobPolling()
  const { resetJob } = useParseJobStore()
  const { toast } = useToast()
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Check if polling has stopped but job is still pending/processing
  const pollingStopped = jobState.status === 'processing' && !shouldPoll

  // Set up polling (stable interval)
  useEffect(() => {
    if (jobState.status === 'processing' && shouldPoll && 'jobId' in jobState) {
      pollJob() // Initial poll
      pollIntervalRef.current = setInterval(() => {
        pollJob()
      }, pollInterval)
      return () => {
        if (pollIntervalRef.current) clearInterval(pollIntervalRef.current)
      }
    } else if (pollIntervalRef.current) {
      clearInterval(pollIntervalRef.current)
    }
    // Only rerun if jobState status, shouldPoll, or pollInterval changes
  }, [jobState.status, shouldPoll, pollInterval])

  // Handle completion
  useEffect(() => {
    if (jobState.status === 'completed') {
      toast({
        title: "Pitch deck processed successfully!",
        description: "Your pitch deck has been analyzed and the deal has been updated.",
      })
      onComplete?.()
      // Force refresh the deals list to show updated data
      onRefresh?.()
    }
  }, [jobState.status, onComplete, onRefresh, toast])

  // Handle failure
  useEffect(() => {
    if (jobState.status === 'failed') {
      toast({
        title: "Processing failed",
        description: jobState.error || "There was an error processing your pitch deck.",
        variant: "destructive"
      })
    }
  }, [jobState.status, jobState, toast])

  // Handle polling timeout
  useEffect(() => {
    if (pollingStopped) {
      toast({
        title: "Processing continues in background",
        description: "The job is still running but we've stopped polling. You can check back later or use the 'Check Status' button.",
      })
    }
  }, [pollingStopped, toast])

  const handleRetry = async () => {
    if (jobState.status !== 'failed') return

    try {
      const token = localStorage.getItem('token')
      const orgId = localStorage.getItem('orgId')
      
      if (!token || !orgId) {
        throw new Error('Authentication required')
      }

      await QueueAPI.retryJob(jobState.jobId, 0, token, orgId)
      
      toast({
        title: "Job retried",
        description: "The processing job has been restarted.",
      })
      
      // Reset to processing state
      useParseJobStore.getState().startJob(jobState.jobId, '', dealId)
      
    } catch (error) {
      console.error('Retry failed:', error)
      toast({
        title: "Retry failed",
        description: "Unable to retry the job. Please try uploading again.",
        variant: "destructive"
      })
    }
  }

  const handleReset = () => {
    resetJob()
  }

  const getStatusMessage = () => {
    if (pollingStopped) {
      return "Processing is taking longer than expected. You can check back later or refresh the page."
    }
    
    switch (jobState.status) {
      case 'processing':
        return "Parsing your pitch deck... This usually takes ~1.5 minutes"
      case 'completed':
        return "Pitch deck processed successfully!"
      case 'failed':
        return "Processing failed. You can retry or upload a different file."
      default:
        return "Preparing to process your pitch deck..."
    }
  }

  const getSubMessage = () => {
    if (pollingStopped) {
      return "The job is still running in the background. You can safely navigate away and return later."
    }
    
    switch (jobState.status) {
      case 'processing':
        return "You can continue working, we'll notify you when it's done."
      case 'completed':
        return "Your deal has been updated with the extracted information."
      case 'failed':
        return "The AI encountered an issue while processing your file."
      default:
        return ""
    }
  }

  const getStatusIcon = () => {
    if (pollingStopped) {
      return <Clock className="size-8 text-orange-600" />
    }
    
    switch (jobState.status) {
      case 'processing':
        return <Loader2 className="size-8 animate-spin text-blue-600" />
      case 'completed':
        return <CheckCircle className="size-8 text-green-600" />
      case 'failed':
        return <AlertCircle className="size-8 text-red-600" />
      default:
        return <FileText className="size-8 text-gray-400" />
    }
  }

  const getProgressValue = () => {
    if (jobState.status === 'completed') return 100
    if (jobState.status === 'failed') return 0
    
    // Estimate progress based on time elapsed
    if (jobState.status === 'processing') {
      const startTime = useParseJobStore.getState().lastPollTime
      if (startTime) {
        const elapsed = Date.now() - startTime
        const estimatedTotal = 180000 // 3 minutes (more realistic for complex decks)
        return Math.min((elapsed / estimatedTotal) * 100, 90) // Cap at 90% until actually complete
      }
    }
    
    return 0
  }

  // Don't render if no active job
  if (jobState.status === 'idle') {
    return null
  }

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className={cn("w-full", className)}
      >
        <Card className="border-2 border-dashed border-primary/20 bg-primary/5">
          <CardContent className="p-6">
            <div className="space-y-4">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getStatusIcon()}
                  <div>
                    <h3 className="text-sm font-semibold">
                      {jobState.status === 'processing' ? 'Processing Pitch Deck' : 
                       jobState.status === 'completed' ? 'Processing Complete' :
                       'Processing Failed'}
                    </h3>
                    <p className="text-xs text-muted-foreground">
                      {getStatusMessage()}
                    </p>
                  </div>
                </div>
                
                                 {(jobState.status === 'processing' || pollingStopped) && (
                   <Badge variant="secondary" className="text-xs">
                     <Clock className="size-3 mr-1" />
                     {pollingStopped ? 'Background' : 'Processing'}
                   </Badge>
                 )}
              </div>

                             {/* Progress Bar */}
               {(jobState.status === 'processing' || pollingStopped) && (
                 <div className="space-y-2">
                   {/* <Progress value={getProgressValue()} className="h-2" /> */}
                   {/* <p className="text-xs text-muted-foreground"> */}
                     {/* {pollingStopped ? 'Processing in background...' : `${Math.round(getProgressValue())}% complete`} */}
                   {/* </p> */}
                 </div>
               )}

              {/* Sub Message */}
              {getSubMessage() && (
                <p className="text-xs text-muted-foreground">
                  {getSubMessage()}
                </p>
              )}

              {/* Action Buttons */}
              <div className="flex items-center gap-2 pt-2">
                                 {(jobState.status === 'processing' || pollingStopped) && onBack && (
                   <>
                     <Button
                       variant="ghost"
                       size="sm"
                       onClick={onBack}
                       className="gap-1"
                     >
                       <ArrowLeft className="size-4" />
                       Go back to deal
                     </Button>
                     {pollingStopped && (
                       <Button
                         variant="outline"
                         size="sm"
                         onClick={pollJob}
                         className="gap-1"
                       >
                         <RefreshCw className="size-4" />
                         Check Status
                       </Button>
                     )}
                   </>
                 )}
                
                {jobState.status === 'failed' && (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleRetry}
                      className="gap-1"
                    >
                      <RefreshCw className="size-4" />
                      Retry
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleReset}
                    >
                      Upload Different File
                    </Button>
                  </>
                )}
                
                {jobState.status === 'completed' && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleReset}
                  >
                    Close
                  </Button>
                )}
              </div>

                             {/* Processing Tips */}
               {(jobState.status === 'processing' || pollingStopped) && (
                 <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                   <p className="text-xs text-blue-700">
                     <strong>Still processing?</strong> Some decks take longer due to size or format complexity. 
                     {pollingStopped && " You can safely navigate away and return later to check the status."}
                   </p>
                 </div>
               )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </AnimatePresence>
  )
} 