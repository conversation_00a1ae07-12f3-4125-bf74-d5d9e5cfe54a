import json
import logging
import sys
from typing import Any, Dict

import structlog  # type: ignore
from structlog.stdlib import ProcessorFormatter  # type: ignore

from app.core.config import settings


class ReadableJSONRenderer:
    """Custom JSON renderer that produces more readable output."""

    def __init__(self, indent: int = 2):
        self.indent = indent

    def __call__(
        self, logger: logging.Logger, method_name: str, event_dict: Dict[str, Any]
    ) -> str:
        # Extract the actual event if it's a JSON string
        if isinstance(event_dict.get("event"), str):
            try:
                # Try to parse if it's a JSON string
                event = json.loads(event_dict["event"])
                if isinstance(event, dict) and "event" in event:
                    # If it's a nested JSON, use the inner event
                    event_dict["event"] = event["event"]
                    # Merge other fields if they exist
                    for key, value in event.items():
                        if key != "event" and key not in event_dict:
                            event_dict[key] = value
            except json.JSONDecodeError:
                # If it's not JSON, keep it as is
                pass

        # Remove redundant logger name if it's the same as the parent
        if "logger" in event_dict and event_dict["logger"].startswith(
            "app.services.queue"
        ):
            event_dict["logger"] = event_dict["logger"].replace(
                "app.services.queue.", ""
            )

        # Format the output
        return json.dumps(event_dict, indent=self.indent, ensure_ascii=False)


def configure_logging() -> None:
    """Configure structured logging for the application."""
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=settings.LOG_LEVEL,
    )

    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            ReadableJSONRenderer(),  # Use our custom renderer
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    # Configure root logger
    root_logger = logging.getLogger()
    handler = logging.StreamHandler()
    handler.setFormatter(
        ProcessorFormatter(
            processor=ReadableJSONRenderer(),  # Use our custom renderer here too
            foreign_pre_chain=[
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.processors.TimeStamper(fmt="iso"),
            ],
        )
    )
    root_logger.addHandler(handler)


def get_logger(name: str) -> structlog.BoundLogger:
    """Get a structured logger instance."""
    return structlog.get_logger(name)
