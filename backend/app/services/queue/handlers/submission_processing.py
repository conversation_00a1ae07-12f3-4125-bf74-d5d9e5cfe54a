"""
Submission Processing Job Handler

This module handles the complete submission processing workflow including:
- Exclusion filter checking
- Thesis matching and scoring
- Deal creation
- Enrichment job queuing
"""

from typing import Any, Dict, Optional

from bson import ObjectId

from app.core.logging import get_logger
from app.models.deal import Deal
from app.models.job import JobStatus
from app.models.queue import Job
from app.services.context_block.service import ContextBlockService
from app.services.factory import (
    get_context_block_service,
    get_job_service,
    get_submission_processing_service,
)
from app.services.queue.handlers.base import BaseJobHandler
from app.services.submission_processing.service import (
    SubmissionProcessingService,
)

logger = get_logger(__name__)


class SubmissionProcessingHandler(BaseJobHandler):
    """Handler for submission processing jobs."""

    def __init__(self):
        """Initialize the handler."""
        super().__init__()

        self.logger.debug("SubmissionProcessingHandler initialized")

    async def _initialize_services(self) -> None:
        """Initialize handler services."""
        self.logger.debug("Initializing services for SubmissionProcessingHandler")
        self.submission_processing_service: SubmissionProcessingService = (
            await get_submission_processing_service()
        )  # type: ignore
        from app.services.job.mongo import JobService

        self.job_service: JobService = await get_job_service()  # type: ignore

        await self.submission_processing_service.initialize()
        await self.job_service.initialize()
        self.logger.debug("Services initialized successfully")

    async def cleanup(self) -> None:
        """Cleanup handler resources."""
        if self.submission_processing_service:
            await self.submission_processing_service.cleanup()
        if self.job_service:
            await self.job_service.cleanup()

    async def process(self, job: Job) -> Optional[Dict[str, Any]]:
        """
        Process a submission processing job.

        Args:
            job: The job to process

        Returns:
            Processing result
        """
        payload = job.payload
        self.logger.info(f"Processing submission processing job: {job.id}")

        # Extract data from payload
        submission_id = payload.get("submission_id")
        form_id = payload.get("form_id")
        org_id = payload.get("org_id")
        deal_id = payload.get("deal_id")
        answers = payload.get("answers")
        metadata = payload.get("metadata", {})
        created_by = payload.get("created_by")
        tracked_job_id = payload.get("tracked_job_id")

        self.logger.debug(
            f"Extracted job data: submission_id={submission_id}, form_id={form_id}, org_id={org_id}, tracked_job_id={tracked_job_id}"
        )

        if not all([submission_id, form_id, org_id, answers]):
            self.logger.error(
                "Missing required fields in payload", extra={"payload": payload}
            )
            raise ValueError("Missing required fields in payload")

        # Services are initialized via BaseJobHandler.handle() method

        try:
            # Update tracked job status to in progress
            if tracked_job_id:
                try:
                    self.logger.debug(
                        f"Updating job status to in progress for {tracked_job_id}"
                    )
                    await self.job_service.update_job_status(
                        job_id=tracked_job_id,
                        status=JobStatus.IN_PROGRESS,
                        progress=0.1,
                    )
                except Exception as e:
                    self.logger.error(
                        f"Failed to update job status: {str(e)}", exc_info=True
                    )

            # Process the submission through the complete workflow
            self.logger.info(f"Starting submission processing for {submission_id}")
            result = await self.submission_processing_service.process_submission(
                submission_id=str(submission_id),
                form_id=str(form_id),
                deal_id=str(deal_id) if deal_id else None,
                org_id=str(org_id),
                answers=answers or {},
                metadata=metadata,
                created_by=created_by,
            )

            # Update tracked job with results
            if tracked_job_id:
                try:
                    if result.success:
                        self.logger.debug(
                            f"Updating job status to completed for {tracked_job_id}"
                        )
                        await self.job_service.update_job_status(
                            job_id=tracked_job_id,
                            status=JobStatus.COMPLETED,
                            progress=1.0,
                            output=result.to_dict() if result else None,
                        )
                    else:
                        self.logger.debug(
                            f"Updating job status to failed for {tracked_job_id}"
                        )
                        await self.job_service.update_job_status(
                            job_id=tracked_job_id,
                            status=JobStatus.FAILED,
                            error=result.error,
                        )
                except Exception as e:
                    self.logger.error(
                        f"Failed to update job status: {str(e)}", exc_info=True
                    )

            if result.success:
                self.logger.info(
                    f"Successfully processed submission {submission_id}, created deal {result.deal_id}"
                )
                return {
                    "success": True,
                    "submission_id": submission_id,
                    "deal_id": result.deal_id,
                    "excluded": result.excluded,
                    "exclusion_reason": result.exclusion_reason,
                    "matching_theses": result.matching_theses,
                    "enrichment_job_id": result.enrichment_job_id,
                }
            else:
                self.logger.error(
                    f"Failed to process submission {submission_id}: {result.error}"
                )
                return {
                    "success": False,
                    "submission_id": submission_id,
                    "error": result.error,
                }

        except Exception as e:
            self.logger.error(
                f"Error processing submission {submission_id}: {str(e)}", exc_info=True
            )

            # Update tracked job status to failed
            if tracked_job_id:
                try:
                    await self.job_service.update_job_status(
                        job_id=tracked_job_id, status=JobStatus.FAILED, error=str(e)
                    )
                except Exception as job_error:
                    self.logger.error(
                        f"Failed to update job status for failed submission: {str(job_error)}",
                        exc_info=True,
                    )

            raise


def create_submission_processing_handler() -> SubmissionProcessingHandler:
    """Create a new submission processing handler instance."""
    logger.debug("Creating new SubmissionProcessingHandler instance")
    return SubmissionProcessingHandler()


# PRD 2: Context Block Generation Handler
class ContextBlockHandler(BaseJobHandler):
    """Handler for context block generation jobs."""

    def __init__(self):
        """Initialize the handler."""
        super().__init__()
        self.logger.debug("ContextBlockHandler initialized")

    async def _initialize_services(self) -> None:
        """Initialize handler services."""
        self.logger.debug("Initializing services for ContextBlockHandler")
        self.context_service: ContextBlockService = await get_context_block_service()  # type: ignore
        await self.context_service.initialize()
        self.logger.debug("Services initialized successfully")

    async def cleanup(self) -> None:
        """Cleanup handler resources."""
        if self.context_service:
            await self.context_service.cleanup()

    async def process(self, job: Job) -> Optional[Dict[str, Any]]:
        """
        Process a context block generation job.

        Args:
            job: The job to process

        Returns:
            Processing result
        """
        payload = job.payload
        self.logger.info(f"Processing context block generation job: {job.id}")

        try:
            deal_id = payload.get("deal_id")
            if not deal_id:
                raise ValueError("Missing deal_id in payload")

            self.logger.info(f"Generating context block for deal {deal_id}")

            # Get deal
            deal = await Deal.find_one(query={"_id": ObjectId(deal_id)})
            if not deal:
                raise ValueError(f"Deal not found: {deal_id}")

            # Generate and store context block
            context_url = await self.context_service.update_context_block(deal)

            if context_url:
                self.logger.info(
                    f"Successfully generated context block for deal {deal_id} at {context_url}"
                )
                return {
                    "success": True,
                    "deal_id": deal_id,
                    "context_url": context_url,
                }
            else:
                raise Exception("Failed to generate context block")

        except Exception as e:
            self.logger.error(
                f"Error generating context block for deal {payload.get('deal_id')}: {str(e)}",
                exc_info=True,
            )
            return {
                "success": False,
                "deal_id": payload.get("deal_id"),
                "error": str(e),
            }


def create_context_block_handler() -> ContextBlockHandler:
    """Create a new context block handler instance."""
    logger.debug("Creating new ContextBlockHandler instance")
    return ContextBlockHandler()


# Register handlers
HANDLERS = {
    "process_submission": create_submission_processing_handler,
    "submission_processing": create_submission_processing_handler,
    "generate_context_block": create_context_block_handler,
}
