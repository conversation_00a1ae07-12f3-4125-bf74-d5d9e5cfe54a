"""
Founder signal generation task.
Triggers after founder enrichment to generate LLM-based scoring and analysis.
"""

import async<PERSON>
from typing import Any, Dict

from app.configs import get_logger
from app.services.signal_generation import SignalGenerationService
from app.storage.rds_storage import RDSStorage


async def generate_founder_signals(founder_id: str) -> Dict[str, Any]:
    """
    Generate founder signals for a given founder ID.

    Args:
        founder_id: Founder identifier

    Returns:
        Processing result
    """
    logger = get_logger(__name__)

    try:
        logger.info(f"Starting signal generation for founder {founder_id}")

        # Initialize storage
        rds_storage = RDSStorage()
        await rds_storage.initialize()

        # Initialize signal service (without LLM client for now - will use mock)
        signal_service = SignalGenerationService()

        try:
            # Fetch founder data from RDS
            founder_data = await rds_storage.get_founder_with_relations(founder_id)

            if not founder_data:
                error_msg = f"Founder {founder_id} not found in database"
                logger.error(error_msg)
                return {"success": False, "error": error_msg, "founder_id": founder_id}

            # Generate signals using LLM
            signal_record = await signal_service.generate_founder_signals(
                founder_data, founder_id
            )

            if not signal_record:
                error_msg = f"Failed to generate signals for founder {founder_id}"
                logger.error(error_msg)
                return {"success": False, "error": error_msg, "founder_id": founder_id}

            # Store signal record in database
            signal_data = signal_record.model_dump()
            logger.info(f"Signal data: {signal_data}")
            await rds_storage.insert("founder_signals", signal_data)

            logger.info(
                f"Successfully generated signals for founder {founder_id}",
                score=signal_record.score,
                tags=signal_record.tags,
            )

            return {
                "success": True,
                "founder_id": founder_id,
                "score": signal_record.score,
                "tags": signal_record.tags,
                "processing_time": signal_record.processing_time,
            }

        finally:
            await rds_storage.cleanup()

    except Exception as e:
        error_msg = f"Signal generation failed for founder {founder_id}: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return {"success": False, "error": error_msg, "founder_id": founder_id}


def generate_founder_signals_sync(founder_id: str) -> Dict[str, Any]:
    """
    Synchronous wrapper for the async signal generation task.
    This is needed for RQ compatibility.
    """
    return asyncio.run(generate_founder_signals(founder_id))
