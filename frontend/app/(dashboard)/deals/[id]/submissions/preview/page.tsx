"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  ChevronLeft,
  ChevronDown,
  FileText,
  Calendar,
  AlertCircle,
  Loader2,
  Download,
  Building2,
  Globe
} from "lucide-react"
import { cn } from "@/lib/utils"
import { SubmissionPreviewAPI } from "@/lib/api/submission-preview-api"
import { PDFExportAPI } from "@/lib/api/pdf-export-api"
import {
  DealSubmissionPreviewResponse,
  FormattedSubmission
} from "@/lib/types/submission-preview"
import { toast } from "@/components/ui/use-toast"
import { SubmissionPreviewContent } from "@/components/core/deals/submission-preview/submission-preview-content"
import { SubmissionSelector } from "@/components/core/deals/submission-preview/submission-selector"
import { EmptySubmissionState } from "@/components/core/deals/submission-preview/empty-submission-state"

export default function SubmissionPreviewPage() {
  const params = useParams()
  const router = useRouter()
  const dealId = params?.id as string

  const [previewData, setPreviewData] = useState<DealSubmissionPreviewResponse | null>(null)
  const [selectedSubmissionId, setSelectedSubmissionId] = useState<string>("")
  const [selectedSubmission, setSelectedSubmission] = useState<FormattedSubmission | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isExportingPDF, setIsExportingPDF] = useState(false)

  // Fetch submission preview data
  useEffect(() => {
    const fetchPreviewData = async () => {
      if (!dealId) return

      try {
        setIsLoading(true)
        setError(null)
        
        const data = await SubmissionPreviewAPI.getSubmissionPreview(dealId)
        setPreviewData(data)

        // Auto-select first submission if available
        if (data.submissions.length > 0) {
          const firstSubmission = data.submissions[0]
          setSelectedSubmissionId(firstSubmission.submission_id)
          setSelectedSubmission(SubmissionPreviewAPI.formatSubmissionForUI(firstSubmission))
        }
      } catch (err: any) {
        console.error('Error fetching submission preview:', err)
        setError(err.message || 'Failed to load submission preview')
      } finally {
        setIsLoading(false)
      }
    }

    fetchPreviewData()
  }, [dealId])

  // Handle submission selection change
  const handleSubmissionChange = (submissionId: string) => {
    if (!previewData) return

    const submission = previewData.submissions.find(s => s.submission_id === submissionId)
    if (submission) {
      setSelectedSubmissionId(submissionId)
      setSelectedSubmission(SubmissionPreviewAPI.formatSubmissionForUI(submission))
    }
  }

  // Handle PDF export
  const handlePDFExport = async () => {
    if (!selectedSubmission) return

    try {
      setIsExportingPDF(true)

      toast({
        title: "Generating PDF",
        description: "Creating your submission report...",
      })

      // Try backend export first, fallback to client-side
      try {
        const result = await PDFExportAPI.exportSubmissionToPDF(dealId, selectedSubmission.id, {
          template: 'premium',
          includeMetadata: true,
          branding: 'tractionx'
        })

        if (result.success && result.downloadUrl) {
          // Backend export successful
          window.open(result.downloadUrl, '_blank')
          toast({
            title: "PDF Ready",
            description: "Your submission report has been generated successfully.",
          })
        } else {
          throw new Error(result.error || 'Backend export failed')
        }
      } catch (backendError) {
        console.log('Backend export failed, using client-side generation:', backendError)

        // Fallback to client-side PDF generation
        await PDFExportAPI.generatePDFClientSide(selectedSubmission, null, {
          template: 'premium',
          includeMetadata: true,
          branding: 'tractionx'
        })

        toast({
          title: "PDF Downloaded",
          description: "Your submission report has been downloaded successfully.",
        })
      }
    } catch (error: any) {
      console.error('PDF export failed:', error)
      toast({
        title: "Export Failed",
        description: error.message || "Failed to generate PDF. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsExportingPDF(false)
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50/30">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex items-center gap-3 rounded-2xl border border-gray-200/50 bg-white/80 px-6 py-4 shadow-sm backdrop-blur-sm"
        >
          <Loader2 className="size-5 animate-spin text-blue-600" />
          <span className="text-sm font-medium text-gray-700">Loading submission preview...</span>
        </motion.div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50/30">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mx-auto max-w-md rounded-2xl border border-gray-200/50 bg-white/80 px-6 py-8 text-center shadow-sm backdrop-blur-sm"
        >
          <AlertCircle className="mx-auto mb-4 size-12 text-red-500" />
          <h3 className="mb-2 text-lg font-semibold text-gray-900">Could not load submission preview</h3>
          <p className="mb-6 text-sm text-gray-600">{error}</p>
          <div className="flex justify-center gap-3">
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="gap-2"
            >
              <ChevronLeft className="size-4" />
              Go Back
            </Button>
            <Button
              onClick={() => window.location.reload()}
              className="gap-2"
            >
              Try Again
            </Button>
          </div>
        </motion.div>
      </div>
    )
  }

  // No submissions state
  if (!previewData || previewData.no_submissions) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50/50 via-white to-gray-50/30">
        {/* Header */}
        <div className="sticky top-0 z-20 border-b border-gray-200/40 bg-white/95 shadow-sm backdrop-blur-xl">
          <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="gap-2 rounded-xl px-4 py-2 text-gray-600 hover:bg-gray-100/50 hover:text-gray-900"
              >
                <ChevronLeft className="size-4" />
                <span className="hidden sm:inline">Back to Deal</span>
                <span className="sm:hidden">Back</span>
              </Button>
              <div className="h-6 w-px bg-gray-200/60" />
              <div className="flex items-center gap-3">
                <div className="flex size-8 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-blue-600">
                  <FileText className="size-4 text-white" />
                </div>
                <h1 className="text-2xl font-bold text-gray-900">Submission Preview</h1>
              </div>
            </div>
          </div>
        </div>

        {/* Empty state */}
        <div className="flex min-h-[60vh] flex-1 items-center justify-center p-8">
          <EmptySubmissionState />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50/50 via-white to-gray-50/30">
      {/* Premium Header */}
      <div className="sticky top-0 z-20 border-b border-gray-200/40 bg-white/95 shadow-sm backdrop-blur-xl">
        <div className="mx-auto max-w-7xl px-4 pb-6 pt-0 sm:px-6 lg:px-8">
          {/* Top Navigation Row */}
          <div className="mb-6 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="gap-2 rounded-xl px-4 py-2 text-gray-600 hover:bg-gray-100/50 hover:text-gray-900"
              >
                <ChevronLeft className="size-4" />
                <span className="hidden sm:inline">Back to Deal</span>
                <span className="sm:hidden">Back</span>
              </Button>
              <div className="h-6 w-px bg-gray-200/60" />
              <div className="flex items-center gap-3">
                <div className="flex size-8 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-blue-600">
                  <FileText className="size-4 text-white" />
                </div>
                <h1 className="text-2xl font-bold text-gray-900">Submission Preview</h1>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-3">
              {/* PDF Download Button */}
              <Button
                onClick={handlePDFExport}
                disabled={isExportingPDF || !selectedSubmission}
                className="gap-2 rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 px-6 text-white shadow-lg transition-all duration-200 hover:from-blue-700 hover:to-blue-800 hover:shadow-xl disabled:from-gray-400 disabled:to-gray-500"
              >
                {isExportingPDF ? (
                  <Loader2 className="size-4 animate-spin" />
                ) : (
                  <Download className="size-4" />
                )}
                <span className="hidden sm:inline">
                  {isExportingPDF ? 'Generating...' : 'Download PDF'}
                </span>
                <span className="sm:hidden">
                  {isExportingPDF ? '...' : 'PDF'}
                </span>
              </Button>

              {/* Submission Selector */}
              {previewData.submissions.length > 1 && (
                <SubmissionSelector
                  submissions={previewData.submissions}
                  selectedSubmissionId={selectedSubmissionId}
                  onSubmissionChange={handleSubmissionChange}
                />
              )}
            </div>
          </div>

          {/* Submission Header Card */}
          {selectedSubmission && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="rounded-2xl border border-gray-200/50 bg-white/80 p-6 shadow-sm backdrop-blur-sm"
            >
              <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                  <div className="flex items-center gap-3">
                    <div className="flex size-12 items-center justify-center rounded-xl bg-gradient-to-br from-green-500 to-emerald-600">
                      <Building2 className="size-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-gray-900">{selectedSubmission.formName}</h2>
                      <p className="text-sm text-gray-600">Version {selectedSubmission.formVersion}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <Calendar className="size-4" />
                      <span>Submitted {selectedSubmission.submittedAt.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}</span>
                    </div>
                  </div>
                </div>

                {/* Quick Stats */}
                <div className="flex items-center gap-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">{selectedSubmission.sections.length}</div>
                    <div className="text-xs text-gray-600">Sections</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {Math.round((SubmissionPreviewAPI.getSubmissionSummary(selectedSubmission).answeredQuestions /
                        SubmissionPreviewAPI.getSubmissionSummary(selectedSubmission).totalQuestions) * 100)}%
                    </div>
                    <div className="text-xs text-gray-600">Complete</div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Main Content Area */}
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <AnimatePresence mode="wait">
          {selectedSubmission && (
            <SubmissionPreviewContent
              key={selectedSubmission.id}
              submission={selectedSubmission}
            />
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}
