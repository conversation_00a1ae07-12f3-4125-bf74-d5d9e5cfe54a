# Web Search Tool Integration Guide

## Overview

The Web Search Tool provides a modular, pluggable architecture for integrating multiple search providers into TractionX AI agents. Currently supports NewsAPI with planned support for SerpAPI, GNews, and other sources.

## Current Implementation

### Supported Sources

1. **NewsAPI** (Primary) - News articles and headlines
2. **SerpAPI** (Planned) - Google search results
3. **GNews** (Planned) - Google News results

### Architecture

```
WebSearchTool
├── _search_newsapi()     # NewsAPI implementation
├── _search_serpapi()     # SerpAPI placeholder
├── _search_gnews()       # GNews placeholder
└── execute()             # Main execution with fallback logic
```

## Setup Instructions

### 1. Get API Keys

#### NewsAPI (Required)
1. Visit [NewsAPI.org](https://newsapi.org/)
2. Sign up for free account (500 requests/day)
3. Get your API key
4. Add to `.env`: `NEWSAPI_KEY=your_key_here`

#### SerpAPI (Optional - Future)
1. Visit [SerpAPI.com](https://serpapi.com/)
2. Sign up for account
3. Get your API key
4. Add to `.env`: `SERPAPI_KEY=your_key_here`

#### GNews (Optional - Future)
1. Visit [GNews.io](https://gnews.io/)
2. Sign up for account
3. Get your API key
4. Add to `.env`: `GNEWS_KEY=your_key_here`

### 2. Environment Configuration

Add to your `.env` file:
```bash
# Web Search API Keys
NEWSAPI_KEY=your_newsapi_key
SERPAPI_KEY=your_serpapi_key  # Optional
GNEWS_KEY=your_gnews_key      # Optional
```

### 3. Agent Configuration

Add `web_search` to your agent's tools:
```json
{
  "name": "Research Agent",
  "tools": ["web_search", "vector_search"],
  "system_prompt": "You can search for recent news and information..."
}
```

## Usage Examples

### Basic News Search
```python
# Via agent execution
{
  "message": "Find recent news about AI startups",
  "context_data": {
    "search_source": "newsapi",
    "time_range": "7d"
  }
}
```

### Advanced Search Parameters
```python
# Direct tool usage
{
  "query": "venture capital funding",
  "source": "newsapi",
  "limit": 10,
  "language": "en",
  "date_range": "30d",
  "domains": "techcrunch.com,bloomberg.com"
}
```

### Agent Prompts for Web Search
```
"Search for the latest funding news about [company] in the past week"
"Find recent market trends in the fintech sector"
"What are the top headlines about AI regulation?"
"Research recent developments in [industry]"
```

## Adding New Search Sources

### Step 1: Add API Key Configuration

1. **Environment Variable**: Add to `.env.example` and `ai/config.py`
```python
# In ai/config.py
NEW_SEARCH_API_KEY: Optional[str] = None
```

2. **Tool Initialization**: Add to `WebSearchTool.__init__()`
```python
self.new_search_key = os.getenv("NEW_SEARCH_API_KEY")
```

### Step 2: Add Source to Supported List

```python
# In ai/tools/websearch.py
SUPPORTED_SOURCES = ["newsapi", "serpapi", "gnews", "new_source"]
```

### Step 3: Implement Search Method

Create a new method following the naming pattern:
```python
async def _search_new_source(self, input_data: WebSearchInput) -> List[SearchResult]:
    """
    Search using New Search API.
    
    New Search API provides:
    - Feature 1
    - Feature 2
    - Feature 3
    """
    
    if not self.new_search_key:
        raise ValueError("New Search API key not configured")
    
    # Build API request
    url = "https://api.newsearch.com/v1/search"
    params = {
        "q": input_data.query,
        "api_key": self.new_search_key,
        "limit": input_data.limit,
        "lang": input_data.language
    }
    
    # Add optional parameters
    if input_data.date_range:
        params["date_range"] = input_data.date_range
    
    try:
        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        
        results = []
        for item in data.get("results", []):
            result = SearchResult(
                title=item["title"],
                url=item["url"],
                description=item.get("description", ""),
                source=item.get("source", "Unknown"),
                published_at=self._parse_datetime(item.get("published_at")),
                image_url=item.get("image_url"),
                author=item.get("author")
            )
            results.append(result)
        
        return results
        
    except requests.RequestException as e:
        logger.error(f"New Search API request failed: {e}")
        raise ValueError(f"New Search API request failed: {str(e)}")
```

### Step 4: Add to Execution Logic

```python
# In execute() method
elif input_data.source == "new_source":
    results = await self._search_new_source(input_data)
```

### Step 5: Update API Key Checking

```python
# In _has_api_key() method
key_mapping = {
    "newsapi": self.newsapi_key,
    "serpapi": self.serpapi_key,
    "gnews": self.gnews_key,
    "new_source": self.new_search_key  # Add this line
}
```

### Step 6: Add Tests

Create tests in `ai/tests/test_websearch.py`:
```python
@pytest.mark.asyncio
async def test_search_new_source_success(web_search_tool):
    """Test successful New Source search."""
    
    input_data = WebSearchInput(query="test", source="new_source")
    
    # Mock API response
    mock_response = {
        "results": [
            {
                "title": "Test Article",
                "url": "https://example.com",
                "description": "Test description",
                "source": "Test Source"
            }
        ]
    }
    
    with patch('ai.tools.websearch.requests.get') as mock_get:
        mock_get.return_value.json.return_value = mock_response
        mock_get.return_value.raise_for_status.return_value = None
        
        with patch.object(web_search_tool, 'new_search_key', 'test_key'):
            results = await web_search_tool._search_new_source(input_data)
        
        assert len(results) == 1
        assert results[0].title == "Test Article"
```

## SerpAPI Integration Example

Here's how to implement SerpAPI when ready:

### 1. Install SerpAPI Package
```bash
pip install google-search-results
```

### 2. Implementation
```python
async def _search_serpapi(self, input_data: WebSearchInput) -> List[SearchResult]:
    """Search using SerpAPI (Google Search)."""
    
    from serpapi import GoogleSearch
    
    if not self.serpapi_key:
        raise ValueError("SerpAPI key not configured")
    
    search_params = {
        "q": input_data.query,
        "api_key": self.serpapi_key,
        "num": input_data.limit,
        "hl": input_data.language,
        "gl": "us"  # Country
    }
    
    # Add date range if specified
    if input_data.date_range:
        search_params["tbs"] = f"qdr:{input_data.date_range[0]}"  # e.g., "qdr:d" for day
    
    try:
        search = GoogleSearch(search_params)
        results_data = search.get_dict()
        
        results = []
        
        # Parse organic results
        for item in results_data.get("organic_results", []):
            result = SearchResult(
                title=item.get("title", ""),
                url=item.get("link", ""),
                description=item.get("snippet", ""),
                source="Google Search"
            )
            results.append(result)
        
        # Parse news results if available
        for item in results_data.get("news_results", []):
            result = SearchResult(
                title=item.get("title", ""),
                url=item.get("link", ""),
                description=item.get("snippet", ""),
                source=item.get("source", "News"),
                published_at=self._parse_datetime(item.get("date"))
            )
            results.append(result)
        
        return results[:input_data.limit]
        
    except Exception as e:
        logger.error(f"SerpAPI request failed: {e}")
        raise ValueError(f"SerpAPI request failed: {str(e)}")
```

## Testing

### Unit Tests
```bash
# Run web search tests
pytest ai/tests/test_websearch.py -v

# Run with coverage
pytest ai/tests/test_websearch.py --cov=ai.tools.websearch
```

### Integration Tests
```bash
# Test with real API (requires API keys)
python test_websearch_tool.py
```

### Manual Testing
```bash
# Create agent with web search
curl -X POST "http://localhost:8002/api/ai/agents/" \
  -H "X-ORG-ID: 000000000000000000000000" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "News Researcher",
    "tools": ["web_search"],
    "system_prompt": "You can search for recent news..."
  }'

# Test web search
curl -X POST "http://localhost:8002/api/ai/agents/{agent_id}/run" \
  -H "X-ORG-ID: 000000000000000000000000" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Find recent AI funding news"
  }'
```

## Best Practices

### 1. Error Handling
- Always check API key availability
- Implement fallback to available sources
- Handle rate limits gracefully
- Provide meaningful error messages

### 2. Response Formatting
- Include clickable URLs in agent responses
- Cite sources properly
- Format results for readability
- Include publication dates when available

### 3. Performance
- Set appropriate timeouts
- Limit result counts to avoid long responses
- Cache results when possible
- Monitor API usage and costs

### 4. Security
- Store API keys securely
- Validate input parameters
- Sanitize search queries
- Log usage for audit purposes

## Troubleshooting

### Common Issues

1. **No API Key Error**
   - Check `.env` file configuration
   - Verify API key is valid
   - Ensure key has proper permissions

2. **Rate Limit Exceeded**
   - Check API usage limits
   - Implement retry logic with backoff
   - Consider upgrading API plan

3. **No Results Found**
   - Verify search query is valid
   - Check date range parameters
   - Try different search sources

4. **Timeout Errors**
   - Increase timeout values
   - Check network connectivity
   - Verify API endpoint availability

### Debug Mode

Enable debug logging to troubleshoot issues:
```python
import logging
logging.getLogger("ai.tools.websearch").setLevel(logging.DEBUG)
```

## Future Enhancements

1. **Additional Sources**
   - Bing News API
   - Reddit API
   - Twitter API
   - Custom RSS feeds

2. **Advanced Features**
   - Result caching
   - Sentiment analysis
   - Content summarization
   - Duplicate detection

3. **UI Integration**
   - Source selection in agent config
   - Real-time search preview
   - Result filtering options
   - Search history
