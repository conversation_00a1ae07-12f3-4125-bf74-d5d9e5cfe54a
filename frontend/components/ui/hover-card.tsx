"use client"

import * as React from "react"
import {
  Root as HoverCard,
  <PERSON><PERSON> as HoverCardTrigger,
  Content as HoverCardContent,
} from "@radix-ui/react-hover-card"

import { cn } from "@/lib/utils"

const HoverCardContentComponent = React.forwardRef<
  React.ElementRef<typeof HoverCardContent>,
  React.ComponentPropsWithoutRef<typeof HoverCardContent>
>(({ className, align = "center", sideOffset = 4, ...props }, ref) => (
  <HoverCardContent
    ref={ref}
    align={align}
    sideOffset={sideOffset}
    className={cn(
      "z-50 w-64 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none animate-in zoom-in-90",
      className
    )}
    {...props}
  />
))
HoverCardContentComponent.displayName = "HoverCardContent"

export { HoverCard, HoverCardTrigger, HoverCardContentComponent as Ho<PERSON><PERSON>ardContent }
