import asyncio
import logging
from typing import Any, Dict

from datapipelines.app.queueing.backends.redis_backend import RedisQueueBackend
from datapipelines.app.tasks.comprehensive_company_enrichment import (
    merge_comprehensive_enrichment_results,
)
from datapipelines.app.tasks.comprehensive_data_processor import (
    process_comprehensive_enrichment_data_task,
)

logger = logging.getLogger(__name__)


async def retry_merge_and_process(
    job_id: str, redis_url: str = "redis://localhost:6379/0"
) -> Dict[str, Any]:
    """
    Retry merge_comprehensive_enrichment_results and trigger process_comprehensive_enrichment_data_task.

    Args:
        job_id: The job ID of the merge_comprehensive_enrichment_results job
        redis_url: Redis connection URL

    Returns:
        Dict containing the results of both operations
    """
    try:
        # Initialize Redis backend
        queue_backend = RedisQueueBackend(redis_url)
        await queue_backend.initialize()

        # Get the original job data
        job_data = await queue_backend._get_job_data(job_id)
        if not job_data:
            error_msg = f"No job found for job_id: {job_id}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

        # Extract job arguments
        job_args = job_data.get("job_args") or job_data.get("payload") or job_data
        logger.info(
            f"Re-triggering merge_comprehensive_enrichment_results for job_id: {job_id}"
        )

        # Re-run the merge operation
        merge_result = await merge_comprehensive_enrichment_results(job_args)
        logger.info(f"Merge completed: {merge_result.get('success', False)}")

        # Check if merge was successful and extract enrichment data
        if not merge_result.get("success", False):
            error_msg = f"Merge failed: {merge_result.get('error', 'Unknown error')}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg, "merge_result": merge_result}

        enrichment_data = merge_result.get("comprehensive_data")
        if not enrichment_data:
            error_msg = "No comprehensive_data found in merge result"
            logger.error(error_msg)
            return {"success": False, "error": error_msg, "merge_result": merge_result}

        # Trigger the processing task
        logger.info("Triggering process_comprehensive_enrichment_data_task...")
        process_result = await process_comprehensive_enrichment_data_task({
            "enrichment_data": enrichment_data,
            "company_domain": job_args.get("company_domain"),
            "barrier_group_id": job_args.get("barrier_group_id"),
        })

        logger.info(f"Processing completed: {process_result.get('success', False)}")

        return {
            "success": True,
            "job_id": job_id,
            "merge_result": merge_result,
            "process_result": process_result,
        }

    except Exception as e:
        error_msg = f"Error in retry_merge_and_process: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return {"success": False, "error": error_msg}

    finally:
        # Cleanup
        try:
            await queue_backend.cleanup()
        except Exception as e:
            logger.warning(f"Error during cleanup: {e}")


async def retry_merge_and_process_cli():
    """CLI wrapper for the utility function."""
    import sys

    if len(sys.argv) != 2:
        print("Usage: python retry_merge_and_process.py <job_id>")
        sys.exit(1)

    job_id = sys.argv[1]
    result = await retry_merge_and_process(job_id)

    if result.get("success"):
        print("✅ Successfully retried merge and process")
        print(f"Job ID: {result['job_id']}")
        print(f"Merge success: {result['merge_result'].get('success', False)}")
        print(f"Process success: {result['process_result'].get('success', False)}")
    else:
        print("❌ Failed to retry merge and process")
        print(f"Error: {result.get('error', 'Unknown error')}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(retry_merge_and_process_cli())
