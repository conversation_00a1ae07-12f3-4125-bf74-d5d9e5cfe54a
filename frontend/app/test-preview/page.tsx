"use client"

import { FormPreview } from '@/components/core/form-preview/form-preview';
import { FormWithDetails, QuestionType } from '@/lib/types/form';

// Mock form data for testing
const mockForm: FormWithDetails = {
  _id: "test-form-id",
  name: "Test Form",
  description: "This is a test form to verify the form preview component works correctly.",
  status: "active",
  is_active: true,
  version: 1,
  sections: [
    {
      _id: "section-1",
      title: "Personal Information",
      description: "Please provide your basic information",
      order: 1,
      repeatable: false,
      questions: [
        {
          _id: "question-1",
          section_id: "section-1",
          type: QuestionType.SHORT_TEXT,
          label: "What is your full name?",
          help_text: "Please enter your first and last name",
          required: true,
          order: 1
        },
        {
          _id: "question-2",
          section_id: "section-1",
          type: QuestionType.SINGLE_SELECT,
          label: "What is your preferred contact method?",
          required: true,
          options: [
            { label: "Email", value: "email" },
            { label: "Phone", value: "phone" },
            { label: "Text Message", value: "sms" }
          ],
          order: 2
        },
        {
          _id: "question-3",
          section_id: "section-1",
          type: QuestionType.BOOLEAN,
          label: "Would you like to receive newsletters?",
          required: false,
          order: 3
        }
      ]
    },
    {
      _id: "section-2",
      title: "Feedback",
      description: "Please share your thoughts",
      order: 2,
      repeatable: false,
      questions: [
        {
          _id: "question-4",
          section_id: "section-2",
          type: QuestionType.LONG_TEXT,
          label: "What do you think about our service?",
          help_text: "Please provide detailed feedback",
          required: true,
          order: 1
        },
        {
          _id: "question-5",
          section_id: "section-2",
          type: QuestionType.RANGE,
          label: "How would you rate our service?",
          help_text: "1 = Poor, 10 = Excellent",
          required: true,
          validation: {
            min: 1,
            max: 10
          },
          order: 2
        }
      ]
    }
  ]
};

export default function TestPreviewPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
      <FormPreview form={mockForm} />
    </div>
  );
} 