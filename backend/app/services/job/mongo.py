"""
MongoDB Implementation of Job Tracking Service

This module provides a MongoDB implementation of the job tracking service.
"""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Type, Union

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.logging import get_logger
from app.models.job import EntityType, JobStatus, JobType, TrackedJob
from app.services.base import BaseService
from app.services.factory import get_queue_service
from app.services.job.interfaces import JobServiceInterface
from app.services.queue.queue_service import QueueService

logger = get_logger(__name__)


class JobService(BaseService, JobServiceInterface):
    """MongoDB implementation of the job tracking service."""

    def __init__(self, db: Optional[AsyncIOMotorDatabase] = None):
        """Initialize the service."""
        super().__init__()
        self.db = db

    async def initialize(self) -> None:
        """Initialize the service."""
        # if self.db is None:
        #     self.db = await next(get_database())

        self.queue_service: QueueService = await get_queue_service()  # type: ignore

    async def cleanup(self) -> None:
        """Clean up resources used by the service."""
        pass

    async def create_job(
        self,
        entity_type: Union[str, EntityType],
        entity_id: Union[str, ObjectId],
        job_type: Union[str, JobType],
        payload: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
        config: Optional[Dict[str, Any]] = None,
        priority: int = 0,
        parent_job_id: Optional[Union[str, ObjectId]] = None,
        depends_on: Optional[List[Union[str, ObjectId]]] = None,
        scheduled_at: Optional[int] = None,
        queue_priority: Optional[Any] = None,
        queue_type: Optional[Any] = None,
        queue_job_type: Optional[str] = None,
    ) -> TrackedJob:
        """Create a new tracked job."""
        # Convert string values to enums if needed
        if isinstance(entity_type, str):
            entity_type = EntityType(entity_type)

        if isinstance(job_type, str):
            job_type = JobType(job_type)

        # Convert string ID to ObjectId if needed
        if isinstance(entity_id, str):
            entity_id = ObjectId(entity_id)

        if isinstance(parent_job_id, str) and parent_job_id:
            parent_job_id = ObjectId(parent_job_id)

        # Process depends_on list
        processed_depends_on = []
        if depends_on:
            for job_id in depends_on:
                if isinstance(job_id, str):
                    processed_depends_on.append(ObjectId(job_id))
                else:
                    processed_depends_on.append(job_id)

        # Generate a unique job ID
        queue_job_id = str(uuid.uuid4())

        # Create the tracked job
        job = TrackedJob(
            job_id=queue_job_id,
            entity_type=entity_type,
            entity_id=entity_id,  # type: ignore
            job_type=job_type,
            status=JobStatus.QUEUED,
            metadata=metadata or {},
            config=config or {},
            priority=priority,
            parent_job_id=parent_job_id,  # type: ignore
            depends_on=processed_depends_on,
            scheduled_at=scheduled_at,
        )

        # Save the job to the database
        await job.save()

        # Add job ID to the payload
        payload["tracked_job_id"] = str(job.id)

        # Set default queue job type if not provided
        if queue_job_type is None:
            queue_job_type = f"{job_type.value}"

        # Import queue types if provided
        from app.services.queue import JobPriority, QueueType

        # Set default queue priority and type if not provided
        if queue_priority is None:
            queue_priority = JobPriority.NORMAL

        if queue_type is None:
            queue_type = QueueType.DEFAULT

        # Enqueue the job in the queue system
        await self.queue_service.enqueue_job(
            job_type=queue_job_type,
            payload=payload,
            job_id=queue_job_id,
            priority=queue_priority,
            queue_type=queue_type,
            delay_seconds=0
            if scheduled_at is None
            else max(0, scheduled_at - int(datetime.now(timezone.utc).timestamp())),
        )

        # Link the job to the entity if possible
        await self.link_job_to_entity(job.id, entity_type, entity_id)

        return job

    async def update_job_status(
        self,
        job_id: Union[str, ObjectId],
        status: Union[str, JobStatus],
        error: Optional[str] = None,
        progress: Optional[float] = None,
        output: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        config: Optional[Dict[str, Any]] = None,
    ) -> Optional[TrackedJob]:
        """Update the status of a tracked job."""
        # Convert string ID to ObjectId if needed
        if isinstance(job_id, str):
            job_id = ObjectId(job_id)

        # Convert string status to enum if needed
        if isinstance(status, str):
            status = JobStatus(status)

        # Get the job
        job = await TrackedJob.get_by_id(job_id)
        if not job:
            logger.warning(f"Job not found: {job_id}")
            return None

        # Update the job
        job.status = status
        job.updated_at = int(datetime.now(timezone.utc).timestamp())

        if error is not None:
            job.error = error

        if progress is not None:
            job.progress = progress

        if output is not None:
            job.output = output

        if metadata is not None:
            # Merge metadata instead of replacing
            job.metadata.update(metadata)

        if config is not None:
            # Merge config instead of replacing
            job.config.update(config)

        # Set timestamps based on status
        now = int(datetime.now(timezone.utc).timestamp())

        # Set started_at if the job is in progress and started_at is not set
        if (
            status == JobStatus.IN_PROGRESS
            or status == JobStatus.PROCESSING
            and job.started_at is None
        ):
            job.started_at = now

        # Set completed_at if the job is completed or failed
        if (
            status in [JobStatus.COMPLETED, JobStatus.FAILED]
            and job.completed_at is None
        ):
            job.completed_at = now

            # Unlink the job from the entity if it's completed or failed
            await self.unlink_job_from_entity(job.id, job.entity_type, job.entity_id)

        # Save the job
        await job.save(is_update=True)

        return job

    async def get_job(self, job_id: Union[str, ObjectId]) -> Optional[TrackedJob]:
        """Get a tracked job by ID."""
        # Convert string ID to ObjectId if needed
        if isinstance(job_id, str):
            job_id = ObjectId(job_id)

        return await TrackedJob.get_by_id(job_id)

    async def get_entity_jobs(
        self,
        entity_type: Union[str, EntityType],
        entity_id: Union[str, ObjectId],
        status: Optional[Union[str, JobStatus]] = None,
        job_type: Optional[Union[str, JobType]] = None,
    ) -> List[TrackedJob]:
        """Get all jobs for an entity."""
        # Convert string values to enums if needed
        if isinstance(entity_type, str):
            entity_type = EntityType(entity_type)

        if isinstance(status, str) and status:
            status = JobStatus(status)

        if isinstance(job_type, str) and job_type:
            job_type = JobType(job_type)

        # Convert string ID to ObjectId if needed
        if isinstance(entity_id, str):
            entity_id = ObjectId(entity_id)

        # Build query
        query = {"entity_type": entity_type, "entity_id": entity_id}

        if status:
            query["status"] = status

        if job_type:
            query["job_type"] = job_type

        # Get jobs
        jobs = await TrackedJob.find_many(query=query, sort=[("created_at", -1)])

        return jobs

    async def link_job_to_entity(
        self,
        job_id: Union[str, ObjectId],
        entity_type: Union[str, EntityType],
        entity_id: Union[str, ObjectId],
    ) -> bool:
        """Link a job to an entity by adding it to the entity's active_jobs list."""
        # Convert string ID to ObjectId if needed
        if isinstance(job_id, str):
            job_id = ObjectId(job_id)

        if isinstance(entity_id, str):
            entity_id = ObjectId(entity_id)

        # Convert string entity_type to enum if needed
        if isinstance(entity_type, str):
            entity_type = EntityType(entity_type)

        # Get the entity model class based on entity_type
        entity_model = self._get_entity_model(entity_type)
        if not entity_model:
            logger.warning(f"Unknown entity type: {entity_type}")
            return False

        # Get the entity
        entity = await entity_model.get_by_id(entity_id)
        if not entity:
            logger.warning(f"Entity not found: {entity_id}")
            return False

        # Check if the entity has active_jobs field
        if not hasattr(entity, "active_jobs"):
            logger.warning(
                f"Entity does not have active_jobs field: {entity_type} {entity_id}"
            )
            return False

        # Add the job to the entity's active_jobs list
        if job_id not in entity.active_jobs:
            entity.active_jobs.append(job_id)
            await entity.save(is_update=True)

        return True

    async def unlink_job_from_entity(
        self,
        job_id: Union[str, ObjectId],
        entity_type: Union[str, EntityType],
        entity_id: Union[str, ObjectId],
    ) -> bool:
        """Unlink a job from an entity by removing it from the entity's active_jobs list."""
        # Convert string ID to ObjectId if needed
        if isinstance(job_id, str):
            job_id = ObjectId(job_id)

        if isinstance(entity_id, str):
            entity_id = ObjectId(entity_id)

        # Convert string entity_type to enum if needed
        if isinstance(entity_type, str):
            entity_type = EntityType(entity_type)

        # Get the entity model class based on entity_type
        entity_model = self._get_entity_model(entity_type)
        if not entity_model:
            logger.warning(f"Unknown entity type: {entity_type}")
            return False

        # Get the entity
        entity = await entity_model.get_by_id(entity_id)
        if not entity:
            logger.warning(f"Entity not found: {entity_id}")
            return False

        # Check if the entity has active_jobs field
        if not hasattr(entity, "active_jobs"):
            logger.warning(
                f"Entity does not have active_jobs field: {entity_type} {entity_id}"
            )
            return False

        # Remove the job from the entity's active_jobs list
        if job_id in entity.active_jobs:
            entity.active_jobs.remove(job_id)
            await entity.save(is_update=True)

        return True

    def _get_entity_model(self, entity_type: EntityType) -> Optional[Type]:
        """Get the entity model class based on entity_type."""
        from app.models.form import Submission
        from app.models.organization import Organization
        from app.models.thesis import InvestmentThesis
        from app.models.user import User

        entity_models = {
            EntityType.FORM_SUBMISSION: Submission,
            EntityType.THESIS: InvestmentThesis,
            EntityType.ORGANIZATION: Organization,
            EntityType.USER: User,
        }

        return entity_models.get(entity_type)
