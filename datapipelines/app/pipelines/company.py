"""
Company enrichment pipeline for TractionX Data Pipeline Service.
"""

from typing import Any, Dict, Optional

import httpx

from app.configs import settings
from app.models.base import EnrichmentSource, ProcessingResult
from app.models.company import ApolloCompanyData
from app.pipelines.base import BasePipeline


class CompanyEnrichmentPipeline(BasePipeline):
    """Pipeline for enriching company data using Apollo API."""

    def __init__(self):
        super().__init__("company_enrichment")
        self.apollo_client: Optional[httpx.AsyncClient] = None

    async def _initialize_pipeline(self) -> None:
        """Initialize the company enrichment pipeline."""
        # Initialize Apollo API client if configured
        if settings.APOLLO_API_KEY:
            self.apollo_client = httpx.AsyncClient(
                base_url=settings.APOLLO_BASE_URL,
                headers={
                    "x-api-key": settings.APOLLO_API_KEY,
                    "Content-Type": "application/json",
                    "accept": "application/json",
                    "Cache-Control": "no-cache",
                },
                timeout=30.0,
            )
            self.logger.info("Apollo API client initialized")
        else:
            self.logger.warning(
                "Apollo API key not configured, Apollo enrichment will be skipped"
            )

    async def _cleanup_pipeline(self) -> None:
        """Clean up pipeline resources."""
        if self.apollo_client:
            await self.apollo_client.aclose()
            self.apollo_client = None

    def _get_required_fields(self) -> list[str]:
        """Get required input fields."""
        return ["company_id", "org_id"]

    async def _validate_input(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """Validate input data for company enrichment."""
        # Check required fields
        validation_result = self._validate_required_fields(input_data)
        if not validation_result.success:
            return validation_result

        # Validate that we have domain for Apollo enrichment
        domain = input_data.get("domain", "").strip()

        if not domain:
            return ProcessingResult.error_result(
                error_message="Domain is required for Apollo company enrichment",
                error_type="ValidationError",
            )

        return ProcessingResult.success_result({})

    async def _process_data(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """Process company data through Apollo enrichment."""
        company_id = input_data["company_id"]
        org_id = input_data["org_id"]
        company_name = input_data.get("company_name")
        domain = input_data.get("domain")

        self.logger.info(
            "Starting company enrichment",
            company_id=company_id,
            company_name=company_name,
            domain=domain,
            org_id=org_id,
        )

        enrichment_data = {}

        # Apollo enrichment
        if settings.ENABLE_COMPANY_ENRICHMENT and self.apollo_client:
            try:
                apollo_data = await self._enrich_with_apollo(company_name, domain)
                if apollo_data:
                    enrichment_data["apollo_data"] = apollo_data
                    self.logger.info("Apollo enrichment completed successfully")
                else:
                    self.logger.warning("No data returned from Apollo enrichment")
            except Exception as e:
                self.logger.error(f"Apollo enrichment failed: {e}")
                # Continue with other enrichments even if Apollo fails

        # Store raw enrichment data
        if enrichment_data:
            self._store_raw_data(
                enrichment_data,
                f"apollo_enrichment/{company_id}.json",
            )

        # Calculate confidence score
        confidence_score = self._calculate_confidence_score(enrichment_data)

        return ProcessingResult.success_result(
            data=enrichment_data,
            metadata={
                "company_id": company_id,
                "org_id": org_id,
                "company_name": company_name,
                "domain": domain,
                "confidence_score": confidence_score,
                "enrichment_sources": list(enrichment_data.keys()),
            },
            source=EnrichmentSource.APOLLO,
        )

    async def _enrich_with_apollo(
        self, company_name: Optional[str] = None, domain: Optional[str] = None
    ) -> Optional[ApolloCompanyData]:
        """Enrich company data using Apollo API."""
        if not self.apollo_client:
            self.logger.warning("Apollo client not initialized")
            return None

        try:
            # Prepare search parameters - Apollo enrich endpoint requires domain
            search_params = {}

            if domain:
                search_params["domain"] = domain
            else:
                self.logger.warning(
                    "No domain provided for Apollo enrichment - domain is required"
                )
                return None

            # Make API call to Apollo enrichment endpoint
            response = await self.apollo_client.get(
                "/api/v1/organizations/enrich", params=search_params
            )

            response.raise_for_status()
            apollo_response = response.json()
            self.logger.info(
                "Apollo response", apollo_response=apollo_response or "No response"
            )

            # Extract company data from Apollo response
            # Apollo enrich endpoint returns a single organization object, not an array
            if apollo_response.get("organization"):
                company_data = apollo_response["organization"]

                # Convert to ApolloCompanyData model
                apollo_data = ApolloCompanyData(
                    apollo_id=company_data.get("id"),
                    name=company_data.get("name"),
                    domain=company_data.get("primary_domain"),
                    website=company_data.get("website_url"),
                    description=company_data.get("description"),
                    industry=company_data.get("industry"),
                    sub_industry=company_data.get("sub_industry"),
                    employee_count=company_data.get("estimated_num_employees"),
                    employee_count_range=company_data.get("employee_count_range"),
                    founded_year=company_data.get("founded_year"),
                    headquarters=company_data.get("headquarters"),
                    country=company_data.get("country"),
                    city=company_data.get("city"),
                    state=company_data.get("state"),
                    funding_total=company_data.get("total_funding"),
                    funding_rounds=company_data.get("funding_rounds"),
                    valuation=company_data.get("valuation"),
                    revenue=company_data.get("annual_revenue"),
                    last_funding_date=company_data.get("last_funding_date"),
                    last_funding_amount=company_data.get("last_funding_amount"),
                    technologies=company_data.get("technologies", []),
                    tech_stack=company_data.get("tech_stack", {}),
                    keywords=company_data.get("keywords", []),
                    departmental_head_count=company_data.get(
                        "departmental_head_count", {}
                    ),
                    linkedin_url=company_data.get("linkedin_url"),
                    twitter_url=company_data.get("twitter_url"),
                    facebook_url=company_data.get("facebook_url"),
                    email=company_data.get("email"),
                    phone=company_data.get("phone"),
                    apollo_metadata={
                        "search_params": search_params,
                        "api_response": apollo_response,
                        "confidence_score": apollo_response.get("confidence", 1.0),
                    },
                )

                return apollo_data

            return None

        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                self.logger.info(
                    f"Company not found in Apollo: {company_name or domain}"
                )
                return None
            elif e.response.status_code == 429:
                self.logger.warning("Apollo API rate limit exceeded")
                raise Exception("Apollo API rate limit exceeded")
            else:
                self.logger.error(
                    f"Apollo API error: {e.response.status_code} - {e.response.text}"
                )
                raise
        except Exception as e:
            self.logger.error(f"Apollo enrichment error: {e}")
            raise
