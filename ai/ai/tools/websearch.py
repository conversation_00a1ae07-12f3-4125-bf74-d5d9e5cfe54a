"""
Modular Web Search Tool for TractionX AI Agent system.
Supports NewsAPI by default with pluggable architecture for SerpAPI, GNews, and other sources.
"""

import os
import requests
import asyncio
import aiohttp
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from pydantic import BaseModel, Field
import logging
import json
import feedparser
from urllib.parse import quote_plus

from ai.tools.base import BaseTool, ToolInput, ToolOutput, tool
from ai.models import AgentContext
from ai.config import settings

logger = logging.getLogger(__name__)


class WebSearchInput(ToolInput):
    """Input for web search tool."""
    query: str = Field(..., description="Search query")
    source: str = Field(default="newsapi", description="Search source (newsapi, serpapi, gnews)")
    limit: int = Field(default=5, ge=1, le=20, description="Maximum number of results")
    language: str = Field(default="en", description="Language for search results")
    sort_by: str = Field(default="relevancy", description="Sort order (relevancy, popularity, publishedAt)")
    date_range: Optional[str] = Field(None, description="Date range (1d, 7d, 30d)")
    domains: Optional[str] = Field(None, description="Specific domains to search")


class SearchResult(BaseModel):
    """Individual search result."""
    title: str
    url: str
    description: str
    source: str
    published_at: Optional[datetime] = None
    image_url: Optional[str] = None
    author: Optional[str] = None


class WebSearchOutput(ToolOutput):
    """Output for web search tool."""
    results: Optional[List[SearchResult]] = Field(None, description="Search results")
    total_results: Optional[int] = Field(None, description="Total number of results found")
    search_query: Optional[str] = Field(None, description="Original search query")
    source_used: Optional[str] = Field(None, description="Search source that was used")


@tool(
    name="web_search",
    description="Searches news and web content using multiple sources (NewsAPI, SerpAPI, GNews)",
    category="search",
    tags=["web", "search", "news", "api", "content"]
)
class WebSearchTool(BaseTool):
    """
    Modular web search tool supporting multiple search providers.

    Currently supports:
    - NewsAPI (default): News articles and headlines
    - SerpAPI (future): General web search results
    - GNews (future): Google News results

    To add new sources:
    1. Add API key to environment variables
    2. Implement search method in _search_[source] format
    3. Add source to SUPPORTED_SOURCES
    4. Update documentation
    """

    SUPPORTED_SOURCES = ["newsapi", "serpapi", "gnews", "bing_news", "google_news_rss"]

    def __init__(self):
        super().__init__()
        
        # Load API keys from environment
        self.newsapi_key = os.getenv("NEWSAPI_KEY")
        self.serpapi_key = os.getenv("SERPAPI_KEY")
        self.gnews_key = os.getenv("GNEWS_KEY")
        self.bing_news_key = os.getenv("BING_NEWS_API_KEY")

        # Validate at least one API key is available
        available_keys = [self.newsapi_key, self.serpapi_key, self.gnews_key, self.bing_news_key]
        if not any(available_keys):
            logger.warning("No web search API keys found. Web search functionality will be limited.")
    
    async def execute(
        self,
        input_data: WebSearchInput,
        context: AgentContext
    ) -> WebSearchOutput:
        """Execute web search with specified source."""
        
        try:
            # Validate source
            if input_data.source not in self.SUPPORTED_SOURCES:
                return WebSearchOutput(
                    success=False,
                    error=f"Unsupported search source: {input_data.source}. Supported: {self.SUPPORTED_SOURCES}"
                )
            
            # Check if API key is available for the requested source
            if not self._has_api_key(input_data.source):
                # Fallback to available source
                fallback_source = self._get_available_source()
                if not fallback_source:
                    return WebSearchOutput(
                        success=False,
                        error="No API keys available for any search source"
                    )
                
                logger.warning(f"API key not found for {input_data.source}, falling back to {fallback_source}")
                input_data.source = fallback_source
            
            # Perform search based on source
            if input_data.source == "newsapi":
                results = await self._search_newsapi(input_data)
            elif input_data.source == "serpapi":
                results = await self._search_serpapi(input_data)
            elif input_data.source == "gnews":
                results = await self._search_gnews(input_data)
            else:
                return WebSearchOutput(
                    success=False,
                    error=f"Search method not implemented for source: {input_data.source}"
                )
            
            return WebSearchOutput(
                success=True,
                data={
                    "results": [result.dict() for result in results],
                    "query": input_data.query,
                    "source": input_data.source
                },
                results=results,
                total_results=len(results),
                search_query=input_data.query,
                source_used=input_data.source,
                metadata={
                    "search_source": input_data.source,
                    "query": input_data.query,
                    "limit": input_data.limit,
                    "org_id": str(context.org_id)
                }
            )
            
        except Exception as e:
            return await self._handle_error(e, input_data)
    
    def _has_api_key(self, source: str) -> bool:
        """Check if API key is available for the specified source."""
        key_mapping = {
            "newsapi": self.newsapi_key,
            "serpapi": self.serpapi_key,
            "gnews": self.gnews_key,
            "bing_news": self.bing_news_key,
            "google_news_rss": True  # RSS doesn't require API key
        }
        return bool(key_mapping.get(source))
    
    def _get_available_source(self) -> Optional[str]:
        """Get the first available source with API key."""
        for source in self.SUPPORTED_SOURCES:
            if self._has_api_key(source):
                return source
        return None
    
    async def _search_newsapi(self, input_data: WebSearchInput) -> List[SearchResult]:
        """
        Search using NewsAPI.
        
        NewsAPI provides:
        - Breaking news headlines
        - Historical articles
        - Source filtering
        - Language support
        """
        
        if not self.newsapi_key:
            raise ValueError("NewsAPI key not configured")
        
        # Build NewsAPI request
        url = "https://newsapi.org/v2/everything"
        params = {
            "q": input_data.query,
            "apiKey": self.newsapi_key,
            "language": input_data.language,
            "pageSize": min(input_data.limit, 100),  # NewsAPI max is 100
            "sortBy": input_data.sort_by
        }
        
        # Add date range if specified
        if input_data.date_range:
            days_ago = self._parse_date_range(input_data.date_range)
            if days_ago:
                from_date = (datetime.now() - timedelta(days=days_ago)).strftime("%Y-%m-%d")
                params["from"] = from_date
        
        # Add domain filtering if specified
        if input_data.domains:
            params["domains"] = input_data.domains
        
        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get("status") != "ok":
                raise ValueError(f"NewsAPI error: {data.get('message', 'Unknown error')}")
            
            articles = data.get("articles", [])
            
            results = []
            for article in articles:
                # Skip articles with missing essential data
                if not article.get("title") or not article.get("url"):
                    continue
                
                result = SearchResult(
                    title=article["title"],
                    url=article["url"],
                    description=article.get("description", ""),
                    source=article.get("source", {}).get("name", "Unknown"),
                    published_at=self._parse_datetime(article.get("publishedAt")),
                    image_url=article.get("urlToImage"),
                    author=article.get("author")
                )
                results.append(result)
            
            return results
            
        except requests.RequestException as e:
            logger.error(f"NewsAPI request failed: {e}")
            raise ValueError(f"NewsAPI request failed: {str(e)}")
    
    async def _search_serpapi(self, input_data: WebSearchInput) -> List[SearchResult]:
        """
        Search using SerpAPI (Google Search).
        
        TODO: Implement SerpAPI integration
        
        SerpAPI provides:
        - Google search results
        - News results
        - Shopping results
        - Image results
        - Local results
        
        Implementation steps:
        1. Install serpapi package: pip install google-search-results
        2. Import: from serpapi import GoogleSearch
        3. Configure search parameters
        4. Parse results into SearchResult format
        
        Example implementation:
        ```python
        from serpapi import GoogleSearch
        
        search = GoogleSearch({
            "q": input_data.query,
            "api_key": self.serpapi_key,
            "num": input_data.limit,
            "hl": input_data.language
        })
        
        results = search.get_dict()
        # Parse results["organic_results"] or results["news_results"]
        ```
        """
        
        # Placeholder implementation
        logger.warning("SerpAPI integration not yet implemented")
        return [
            SearchResult(
                title=f"SerpAPI Result for: {input_data.query}",
                url="https://serpapi.com",
                description="SerpAPI integration coming soon",
                source="SerpAPI"
            )
        ]
    
    async def _search_gnews(self, input_data: WebSearchInput) -> List[SearchResult]:
        """
        Search using GNews API.
        
        TODO: Implement GNews integration
        
        GNews provides:
        - Google News results
        - Multiple languages
        - Country-specific news
        - Category filtering
        
        Implementation steps:
        1. Install gnews package: pip install gnews
        2. Import: from gnews import GNews
        3. Configure search parameters
        4. Parse results into SearchResult format
        
        Example implementation:
        ```python
        from gnews import GNews
        
        google_news = GNews(
            language=input_data.language,
            country='US',
            max_results=input_data.limit
        )
        
        news = google_news.get_news(input_data.query)
        # Parse news results
        ```
        """
        
        # Placeholder implementation
        logger.warning("GNews integration not yet implemented")
        return [
            SearchResult(
                title=f"GNews Result for: {input_data.query}",
                url="https://news.google.com",
                description="GNews integration coming soon",
                source="Google News"
            )
        ]

    async def _search_bing_news(self, input_data: WebSearchInput) -> List[SearchResult]:
        """
        Search using Bing News API.

        Bing News provides:
        - Real-time news articles
        - Global coverage
        - Category filtering
        - Freshness controls
        """

        if not self.bing_news_key:
            raise ValueError("Bing News API key not configured")

        # Build Bing News API request
        url = "https://api.bing.microsoft.com/v7.0/news/search"
        headers = {
            "Ocp-Apim-Subscription-Key": self.bing_news_key
        }
        params = {
            "q": input_data.query,
            "count": min(input_data.limit, 100),  # Bing max is 100
            "mkt": f"{input_data.language}-US",  # Market code
            "sortBy": "Date" if input_data.sort_by == "publishedAt" else "Relevance"
        }

        # Add freshness if date range specified
        if input_data.date_range:
            freshness_mapping = {
                "1d": "Day",
                "7d": "Week",
                "30d": "Month"
            }
            if input_data.date_range in freshness_mapping:
                params["freshness"] = freshness_mapping[input_data.date_range]

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, params=params, timeout=30) as response:
                    response.raise_for_status()
                    data = await response.json()

            articles = data.get("value", [])

            results = []
            for article in articles:
                # Skip articles with missing essential data
                if not article.get("name") or not article.get("url"):
                    continue

                result = SearchResult(
                    title=article["name"],
                    url=article["url"],
                    description=article.get("description", ""),
                    source=article.get("provider", [{}])[0].get("name", "Unknown"),
                    published_at=self._parse_datetime(article.get("datePublished")),
                    image_url=article.get("image", {}).get("thumbnail", {}).get("contentUrl")
                )
                results.append(result)

            return results

        except Exception as e:
            logger.error(f"Bing News API request failed: {e}")
            raise ValueError(f"Bing News API request failed: {str(e)}")

    async def _search_google_news_rss(self, input_data: WebSearchInput) -> List[SearchResult]:
        """
        Search using Google News RSS feed.

        Google News RSS provides:
        - Free access to Google News
        - Real-time updates
        - No API key required
        - Limited customization
        """

        # Build Google News RSS URL
        query_encoded = quote_plus(input_data.query)
        url = f"https://news.google.com/rss/search?q={query_encoded}&hl={input_data.language}&gl=US&ceid=US:{input_data.language}"

        try:
            # Parse RSS feed
            feed = feedparser.parse(url)

            if feed.bozo:
                logger.warning(f"RSS feed parsing warning: {feed.bozo_exception}")

            results = []
            entries = feed.entries[:input_data.limit]  # Limit results

            for entry in entries:
                # Skip entries with missing essential data
                if not entry.get("title") or not entry.get("link"):
                    continue

                # Extract source from title (Google News format: "Title - Source")
                title = entry["title"]
                source = "Google News"
                if " - " in title:
                    title_parts = title.rsplit(" - ", 1)
                    if len(title_parts) == 2:
                        title, source = title_parts

                result = SearchResult(
                    title=title,
                    url=entry["link"],
                    description=entry.get("summary", ""),
                    source=source,
                    published_at=self._parse_datetime(entry.get("published"))
                )
                results.append(result)

            return results

        except Exception as e:
            logger.error(f"Google News RSS request failed: {e}")
            raise ValueError(f"Google News RSS request failed: {str(e)}")

    def _parse_date_range(self, date_range: str) -> Optional[int]:
        """Parse date range string to days ago."""
        range_mapping = {
            "1d": 1,
            "7d": 7,
            "30d": 30,
            "1w": 7,
            "1m": 30,
            "3m": 90
        }
        return range_mapping.get(date_range.lower())
    
    def _parse_datetime(self, date_str: Optional[str]) -> Optional[datetime]:
        """Parse datetime string from API response."""
        if not date_str:
            return None
        
        try:
            # Handle ISO format from NewsAPI
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        except (ValueError, AttributeError):
            return None
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for LangChain integration."""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query for news or web content"
                    },
                    "source": {
                        "type": "string",
                        "enum": self.SUPPORTED_SOURCES,
                        "default": "newsapi",
                        "description": "Search source to use"
                    },
                    "limit": {
                        "type": "integer",
                        "minimum": 1,
                        "maximum": 20,
                        "default": 5,
                        "description": "Maximum number of results"
                    },
                    "language": {
                        "type": "string",
                        "default": "en",
                        "description": "Language for search results"
                    },
                    "date_range": {
                        "type": "string",
                        "enum": ["1d", "7d", "30d", "1w", "1m", "3m"],
                        "description": "Date range for results"
                    }
                },
                "required": ["query"]
            }
        }
