"""
HTML Cleaning Utilities for TractionX Data Pipeline Service.

This module provides utilities for cleaning and processing HTML content
from web scraping operations.
"""

import re
from typing import List
from urllib.parse import urljoin

from bs4 import BeautifulSoup

from app.configs import get_logger

logger = get_logger(__name__)


def clean_html_to_text(html_content: str) -> str:
    """
    Clean HTML content and extract meaningful text.

    Args:
        html_content: Raw HTML content

    Returns:
        Cleaned text content
    """
    try:
        if not html_content:
            return ""

        # Remove script and style elements
        html_content = re.sub(
            r"<script[^>]*>.*?</script>",
            "",
            html_content,
            flags=re.DOTALL | re.IGNORECASE,
        )
        html_content = re.sub(
            r"<style[^>]*>.*?</style>",
            "",
            html_content,
            flags=re.DOTALL | re.IGNORECASE,
        )

        # Remove other non-content elements
        html_content = re.sub(
            r"<noscript[^>]*>.*?</noscript>",
            "",
            html_content,
            flags=re.DOTALL | re.IGNORECASE,
        )
        html_content = re.sub(
            r"<iframe[^>]*>.*?</iframe>",
            "",
            html_content,
            flags=re.DOTALL | re.IGNORECASE,
        )
        html_content = re.sub(r"<embed[^>]*>", "", html_content, flags=re.IGNORECASE)
        html_content = re.sub(
            r"<object[^>]*>.*?</object>",
            "",
            html_content,
            flags=re.DOTALL | re.IGNORECASE,
        )

        # Remove comments
        html_content = re.sub(r"<!--.*?-->", "", html_content, flags=re.DOTALL)

        # Replace common HTML entities
        html_content = html_content.replace("&nbsp;", " ")
        html_content = html_content.replace("&amp;", "&")
        html_content = html_content.replace("&lt;", "<")
        html_content = html_content.replace("&gt;", ">")
        html_content = html_content.replace("&quot;", '"')
        html_content = html_content.replace("&#39;", "'")

        # Remove all HTML tags
        html_content = re.sub(r"<[^>]+>", "", html_content)

        # Clean up whitespace
        html_content = re.sub(r"\s+", " ", html_content)
        html_content = html_content.strip()

        # Remove common cookie banners and popup text
        cookie_patterns = [
            r"cookie",
            r"privacy policy",
            r"accept all",
            r"decline",
            r"continue without accepting",
            r"this website uses cookies",
            r"we use cookies",
            r"by continuing to use this site",
        ]

        for pattern in cookie_patterns:
            html_content = re.sub(pattern, "", html_content, flags=re.IGNORECASE)

        # Clean up extra whitespace again
        html_content = re.sub(r"\s+", " ", html_content)
        html_content = html_content.strip()

        return html_content

    except Exception as e:
        logger.error(f"Error cleaning HTML: {str(e)}")
        return html_content if html_content else ""


def extract_page_title(html_content: str) -> str:
    """
    Extract page title from HTML content.

    Args:
        html_content: Raw HTML content

    Returns:
        Page title or empty string
    """
    try:
        if not html_content:
            return ""

        # Look for title tag
        title_match = re.search(
            r"<title[^>]*>(.*?)</title>", html_content, re.IGNORECASE | re.DOTALL
        )
        if title_match:
            title = title_match.group(1).strip()
            return clean_html_to_text(title)

        # Look for h1 tag as fallback
        h1_match = re.search(
            r"<h1[^>]*>(.*?)</h1>", html_content, re.IGNORECASE | re.DOTALL
        )
        if h1_match:
            h1 = h1_match.group(1).strip()
            return clean_html_to_text(h1)

        return ""

    except Exception as e:
        logger.error(f"Error extracting page title: {str(e)}")
        return ""


def extract_meta_description(html_content: str) -> str:
    """
    Extract meta description from HTML content.

    Args:
        html_content: Raw HTML content

    Returns:
        Meta description or empty string
    """
    try:
        if not html_content:
            return ""

        # Look for meta description
        meta_match = re.search(
            r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']*)["\']',
            html_content,
            re.IGNORECASE,
        )
        if meta_match:
            description = meta_match.group(1).strip()
            return clean_html_to_text(description)

        return ""

    except Exception as e:
        logger.error(f"Error extracting meta description: {str(e)}")
        return ""


def is_js_rendered_app(html_content: str, cleaned_text: str) -> bool:
    """
    Determine if the page is likely a JavaScript-rendered SPA.

    Args:
        html_content: Raw HTML content
        cleaned_text: Cleaned text content

    Returns:
        True if likely a JS-rendered app
    """
    try:
        # Check if cleaned text is very short (likely JS-rendered)
        if len(cleaned_text.strip()) < 100:
            return True

        # Check for common SPA indicators
        spa_indicators = [
            r'<div[^>]*id=["\']root["\']',
            r'<div[^>]*id=["\']app["\']',
            r'<div[^>]*id=["\']main["\']',
            r'<div[^>]*class=["\'][^"\']*root[^"\']*["\']',
            r'<div[^>]*class=["\'][^"\']*app[^"\']*["\']',
            r'<div[^>]*class=["\'][^"\']*main[^"\']*["\']',
            r'<script[^>]*src=["\'][^"\']*react[^"\']*["\']',
            r'<script[^>]*src=["\'][^"\']*vue[^"\']*["\']',
            r'<script[^>]*src=["\'][^"\']*angular[^"\']*["\']',
            r'<script[^>]*src=["\'][^"\']*svelte[^"\']*["\']',
        ]

        for pattern in spa_indicators:
            if re.search(pattern, html_content, re.IGNORECASE):
                return True

        # Check for minimal content
        if len(cleaned_text.split()) < 20:
            return True

        return False

    except Exception as e:
        logger.error(f"Error checking JS-rendered app: {str(e)}")
        return False


def chunk_text_by_chars(text: str, max_chars: int = 10000) -> list[str]:
    """
    Chunk text by characters while preserving natural breakpoints.

    Args:
        text: Text to chunk
        max_chars: Maximum characters per chunk

    Returns:
        List of text chunks
    """
    try:
        if not text or len(text) <= max_chars:
            return [text] if text else []

        # Split by paragraphs first
        paragraphs = re.split(r"\n{2,}", text)
        if len(paragraphs) < 5:
            # If not enough paragraphs, split by lines
            paragraphs = text.strip().splitlines()

        chunks = []
        current_chunk = ""

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # If paragraph itself is too long, break it
            while len(paragraph) > max_chars:
                # Find a good break point
                break_point = max_chars
                for i in range(max_chars, max(0, max_chars - 100), -1):
                    if paragraph[i] in " .!?":
                        break_point = i + 1
                        break

                chunks.append(paragraph[:break_point].strip())
                paragraph = paragraph[break_point:].strip()

            # Check if adding this paragraph would exceed max_chars
            if len(current_chunk) + len(paragraph) + 2 > max_chars:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = paragraph
            else:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph

        # Add the last chunk
        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        logger.info(f"Created {len(chunks)} text chunks")
        return chunks

    except Exception as e:
        logger.error(f"Error chunking text: {str(e)}")
        return [text] if text else []


def extract_all_urls_from_html(html_text: str, page_url: str) -> List[str]:
    """
    Extract all absolute, deduplicated URLs from anchor tags in BrightData HTML.

    Args:
        html_text: Raw HTML content from BrightData
        page_url: Base URL (used to resolve relative links)

    Returns:
        List of resolved, unique absolute URLs from all <a> tags in the HTML
    """
    try:
        soup = BeautifulSoup(html_text, "html.parser")

        urls = set()
        for a_tag in soup.find_all("a", href=True):
            full_url = urljoin(page_url, a_tag["href"])
            urls.add(full_url)

        return sorted(urls)

    except Exception as e:
        print(f"Failed to extract URLs from {page_url}: {e}")
        return []
