"""
Exclusion Filters API

This module provides API endpoints for managing exclusion filters.
"""

from typing import Any, Dict, List, Optional, Tuple

from fastapi import Depends, HTTPException, Path, Query, status
from pydantic import BaseModel, Field

from app.api.base import BaseAPIRouter
from app.core.logging import get_logger
from app.dependencies.org import get_org_context
from app.models.exclusion_filter import ExclusionFilter
from app.services.exclusion_filter.interfaces import ExclusionFilterServiceInterface
from app.services.factory import get_exclusion_filter_service
from app.utils.model.helper import partial_model
from app.utils.rbac.rbac import rbac_register

logger = get_logger(__name__)


# Request and response models using partial_model decorator
@partial_model()
class ExclusionFilterCreate(ExclusionFilter):
    """Model for creating an exclusion filter."""

    pass


@partial_model(suffix="Update")
class ExclusionFilterUpdate(ExclusionFilter):
    """Model for updating an exclusion filter."""

    pass


class ExclusionCheckRequest(BaseModel):
    """Model for checking if a submission should be excluded."""

    form_id: str = Field(..., description="Form ID")
    answers: Dict[str, Any] = Field(..., description="Form answers")


class ExclusionCheckResponse(BaseModel):
    """Model for exclusion check response."""

    excluded: bool = Field(..., description="Whether the submission should be excluded")
    filter_id: Optional[str] = Field(
        None, description="ID of the matching filter (if excluded)"
    )
    filter_name: Optional[str] = Field(
        None, description="Name of the matching filter (if excluded)"
    )
    reason: Optional[str] = Field(
        None, description="Reason for exclusion (if excluded)"
    )


# Router
router = BaseAPIRouter(
    prefix="/exclusion-filters", tags=["exclusion-filters"], require_org=True
)


@router.post("", response_model=ExclusionFilter, status_code=status.HTTP_201_CREATED)
@rbac_register(
    resource="exclusion_filter",
    action="create",
    group="Exclusion Filters",
    description="Create exclusion filter",
)
async def create_exclusion_filter(
    filter_data: ExclusionFilterCreate,
    org_context: Tuple[str, bool] = Depends(get_org_context),
    exclusion_filter_service: ExclusionFilterServiceInterface = Depends(
        get_exclusion_filter_service
    ),
) -> ExclusionFilter:
    """
    Create a new exclusion filter.

    Args:
        filter_data: Filter data
        org_context: Tuple of (organization_id, is_cross_org)
        exclusion_filter_service: Exclusion filter service

    Returns:
        Created exclusion filter
    """
    try:
        org_id, _ = org_context

        # Convert filter_data to dict and add org_id
        filter_dict = filter_data.model_dump(exclude_unset=True)
        logger.info(f"Creating exclusion filter with data: {filter_data}")
        filter_dict["org_id"] = org_id

        # Create filter
        exclusion_filter = await exclusion_filter_service.create_exclusion_filter(
            **filter_dict
        )

        return exclusion_filter
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create exclusion filter: {str(e)}",
        )


@router.get("", response_model=List[ExclusionFilter])
@rbac_register(
    resource="exclusion_filter",
    action="view",
    group="Exclusion Filters",
    description="List exclusion filters",
)
async def list_exclusion_filters(
    form_id: Optional[str] = Query(None, description="Filter by form ID"),
    include_deleted: bool = Query(False, description="Include deleted filters"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    exclusion_filter_service: ExclusionFilterServiceInterface = Depends(
        get_exclusion_filter_service
    ),
) -> list[dict[str, Any]]:
    """
    List exclusion filters for the organization.

    Args:
        form_id: Optional form ID to filter by
        include_deleted: Whether to include deleted filters
        org_context: Tuple of (organization_id, is_cross_org)
        exclusion_filter_service: Exclusion filter service

    Returns:
        List of exclusion filters
    """
    try:
        org_id, _ = org_context

        # List filters
        filters = await exclusion_filter_service.list_exclusion_filters(
            org_id=org_id, form_id=form_id, include_deleted=include_deleted
        )

        return [filter.model_dump(by_alias=True) for filter in filters]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list exclusion filters: {str(e)}",
        )


@router.get("/{filter_id}", response_model=ExclusionFilter)
@rbac_register(
    resource="exclusion_filter",
    action="view",
    group="Exclusion Filters",
    description="Get exclusion filter",
)
async def get_exclusion_filter(
    filter_id: str = Path(..., description="Exclusion filter ID"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    exclusion_filter_service: ExclusionFilterServiceInterface = Depends(
        get_exclusion_filter_service
    ),
) -> ExclusionFilter:
    """
    Get an exclusion filter by ID.

    Args:
        filter_id: Exclusion filter ID
        org_context: Tuple of (organization_id, is_cross_org)
        exclusion_filter_service: Exclusion filter service

    Returns:
        Exclusion filter
    """
    try:
        # Get filter
        exclusion_filter = await exclusion_filter_service.get_exclusion_filter(
            filter_id
        )

        if not exclusion_filter:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Exclusion filter not found",
            )

        # Check organization access
        org_id, is_cross_org = org_context
        if str(exclusion_filter.org_id) != org_id and not is_cross_org:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have access to this exclusion filter",
            )

        return exclusion_filter
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get exclusion filter: {str(e)}",
        )


@router.patch("/{filter_id}", response_model=ExclusionFilter)
@rbac_register(
    resource="exclusion_filter",
    action="update",
    group="Exclusion Filters",
    description="Update exclusion filter",
)
async def update_exclusion_filter(
    update_data: ExclusionFilterUpdate,
    filter_id: str = Path(..., description="Exclusion filter ID"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    exclusion_filter_service: ExclusionFilterServiceInterface = Depends(
        get_exclusion_filter_service
    ),
) -> ExclusionFilter:
    """
    Update an exclusion filter.

    Args:
        update_data: Data to update
        filter_id: Exclusion filter ID
        org_context: Tuple of (organization_id, is_cross_org)
        exclusion_filter_service: Exclusion filter service

    Returns:
        Updated exclusion filter
    """
    try:
        # Get filter
        exclusion_filter = await exclusion_filter_service.get_exclusion_filter(
            filter_id
        )

        if not exclusion_filter:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Exclusion filter not found",
            )

        # Check organization access
        org_id, is_cross_org = org_context
        if str(exclusion_filter.org_id) != org_id and not is_cross_org:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have access to this exclusion filter",
            )

        # Update filter - use model_dump with exclude_unset=True to only include fields that were set
        update_dict = update_data.model_dump(exclude_unset=True)
        updated_filter = await exclusion_filter_service.update_exclusion_filter(
            filter_id=filter_id, update_data=update_dict
        )

        if not updated_filter:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Exclusion filter not found",
            )

        return updated_filter
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update exclusion filter: {str(e)}",
        )


@router.delete("/{filter_id}", status_code=status.HTTP_204_NO_CONTENT)
@rbac_register(
    resource="exclusion_filter",
    action="delete",
    group="Exclusion Filters",
    description="Delete exclusion filter",
)
async def delete_exclusion_filter(
    filter_id: str = Path(..., description="Exclusion filter ID"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    exclusion_filter_service: ExclusionFilterServiceInterface = Depends(
        get_exclusion_filter_service
    ),
) -> None:
    """
    Delete an exclusion filter.

    Args:
        filter_id: Exclusion filter ID
        org_context: Tuple of (organization_id, is_cross_org)
        exclusion_filter_service: Exclusion filter service
    """
    try:
        # Get filter
        exclusion_filter = await exclusion_filter_service.get_exclusion_filter(
            filter_id
        )

        if not exclusion_filter:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Exclusion filter not found",
            )

        # Check organization access
        org_id, is_cross_org = org_context
        if str(exclusion_filter.org_id) != org_id and not is_cross_org:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have access to this exclusion filter",
            )

        # Delete filter
        success = await exclusion_filter_service.delete_exclusion_filter(filter_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete exclusion filter",
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete exclusion filter: {str(e)}",
        )


@router.post("/check", response_model=ExclusionCheckResponse)
@rbac_register(
    resource="exclusion_filter",
    action="view",
    group="Exclusion Filters",
    description="Check exclusion",
)
async def check_exclusion(
    request: ExclusionCheckRequest,
    org_context: Tuple[str, bool] = Depends(get_org_context),
    exclusion_filter_service: ExclusionFilterServiceInterface = Depends(
        get_exclusion_filter_service
    ),
) -> ExclusionCheckResponse:
    """
    Check if a submission should be excluded based on exclusion filters.

    Args:
        request: Check request
        org_context: Tuple of (organization_id, is_cross_org)
        exclusion_filter_service: Exclusion filter service

    Returns:
        Exclusion check result
    """
    try:
        org_id, _ = org_context

        # Check exclusion
        result = await exclusion_filter_service.check_exclusion(
            org_id=org_id, form_id=request.form_id, answers=request.answers
        )

        return ExclusionCheckResponse(**result)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check exclusion: {str(e)}",
        )
