import uuid
import hashlib
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status

import boto3
from botocore.exceptions import ClientError

from app.core.config import settings
from app.core.logging import get_logger
from app.services.upload.interface import IUploadService

logger = get_logger(__name__)


class UploadService(IUploadService):
    """Service for handling file uploads to S3."""

    def __init__(self):
        self.s3_client = None
        self.bucket_name = getattr(settings, 'S3_BUCKET_NAME', 'tractionx-uploads')
        self.region = getattr(settings, 'AWS_REGION', 'us-east-1')
        
        # Initialize S3 client if credentials are available
        try:
            aws_access_key = getattr(settings, 'AWS_ACCESS_KEY_ID', None)
            aws_secret_key = getattr(settings, 'AWS_SECRET_ACCESS_KEY', None)
            
            if aws_access_key and aws_secret_key:
                self.s3_client = boto3.client(
                    's3',
                    aws_access_key_id=aws_access_key,
                    aws_secret_access_key=aws_secret_key,
                    region_name=self.region
                )
                logger.info("S3 client initialized successfully")
            else:
                logger.warning("S3 credentials not found, upload service will use mock responses")
        except Exception as e:
            logger.error(f"Failed to initialize S3 client: {str(e)}")

    def _generate_secure_filename(self, original_filename: str, upload_type: str) -> str:
        """Generate a secure, unique filename."""
        # Extract file extension
        file_ext = original_filename.split('.')[-1].lower() if '.' in original_filename else ''
        
        # Create unique identifier
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        unique_id = str(uuid.uuid4())[:8]
        hash_part = hashlib.md5(f"{original_filename}{timestamp}".encode()).hexdigest()[:8]
        
        # Construct secure filename
        secure_name = f"{upload_type}/{timestamp}_{unique_id}_{hash_part}"
        if file_ext:
            secure_name += f".{file_ext}"
            
        return secure_name

    async def validate_file(
        self,
        filename: str,
        file_type: str,
        file_size: int,
        upload_type: str = "general"
    ) -> bool:
        """Validate file before upload."""
        # File size limits (in bytes)
        size_limits = {
            "avatar": 5 * 1024 * 1024,  # 5MB
            "logo": 5 * 1024 * 1024,    # 5MB
            "document": 10 * 1024 * 1024,  # 10MB
            "general": 5 * 1024 * 1024   # 5MB
        }
        
        # Allowed file types
        allowed_types = {
            "avatar": ["image/jpeg", "image/png", "image/webp"],
            "logo": ["image/jpeg", "image/png", "image/svg+xml", "image/webp"],
            "document": ["application/pdf", "image/jpeg", "image/png"],
            "general": ["image/jpeg", "image/png", "image/webp", "application/pdf"]
        }
        
        max_size = size_limits.get(upload_type, size_limits["general"])
        valid_types = allowed_types.get(upload_type, allowed_types["general"])
        
        # Validate file size
        if file_size > max_size:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File size exceeds limit of {max_size // (1024*1024)}MB"
            )
        
        # Validate file type
        if file_type not in valid_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type {file_type} not allowed for {upload_type}"
            )
        
        # Validate filename
        if not filename or len(filename) > 255:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid filename"
            )
        
        return True

    async def generate_presigned_url(
        self,
        filename: str,
        file_type: str,
        file_size: int,
        upload_type: str = "general"
    ) -> Dict[str, Any]:
        """Generate a presigned URL for S3 upload."""
        try:
            # Validate file first
            await self.validate_file(filename, file_type, file_size, upload_type)
            
            # Generate secure filename
            secure_filename = self._generate_secure_filename(filename, upload_type)
            
            # If S3 client is not available, return mock response for development
            if not self.s3_client:
                logger.warning("S3 client not available, returning mock presigned URL")
                base_url = "https://mock-s3-bucket.s3.amazonaws.com"
                public_url = f"{base_url}/{secure_filename}"
                
                return {
                    "presigned_url": f"{base_url}/upload-mock/{secure_filename}",
                    "public_url": public_url,
                    "filename": secure_filename,
                    "expires_in": 3600,
                    "fields": {}
                }
            
            # Generate presigned URL for S3
            try:
                presigned_data = self.s3_client.generate_presigned_post(
                    Bucket=self.bucket_name,
                    Key=secure_filename,
                    Fields={
                        "Content-Type": file_type,
                        "Content-Length-Range": f"1,{file_size}"
                    },
                    Conditions=[
                        {"Content-Type": file_type},
                        ["content-length-range", 1, file_size]
                    ],
                    ExpiresIn=3600  # 1 hour
                )
                
                public_url = f"https://{self.bucket_name}.s3.{self.region}.amazonaws.com/{secure_filename}"
                
                return {
                    "presigned_url": presigned_data["url"],
                    "public_url": public_url,
                    "filename": secure_filename,
                    "expires_in": 3600,
                    "fields": presigned_data["fields"]
                }
                
            except ClientError as e:
                logger.error(f"S3 presigned URL generation failed: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to generate upload URL"
                )
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Upload service error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Upload service error"
            )

    async def delete_file(self, file_url: str) -> bool:
        """Delete a file from S3."""
        try:
            if not self.s3_client:
                logger.warning("S3 client not available, mock file deletion")
                return True
            
            # Extract key from URL
            if self.bucket_name in file_url:
                key = file_url.split(f"{self.bucket_name}.s3.{self.region}.amazonaws.com/")[-1]
                
                self.s3_client.delete_object(Bucket=self.bucket_name, Key=key)
                logger.info(f"Deleted file: {key}")
                return True
            else:
                logger.warning(f"Invalid S3 URL format: {file_url}")
                return False
                
        except ClientError as e:
            logger.error(f"S3 file deletion failed: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"File deletion error: {str(e)}")
            return False
