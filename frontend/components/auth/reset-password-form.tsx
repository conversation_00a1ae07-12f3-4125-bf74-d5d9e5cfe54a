"use client"

import { useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { motion } from "framer-motion"
import * as z from "zod"
import Link from "next/link"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { Icons } from "@/components/icons"
import { AuthCard } from "./auth-card"
import { AuthAPI } from "@/lib/api/auth-api"

const resetPasswordSchema = z.object({
  password: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>

export function ResetPasswordForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams?.get("token")
  const [isLoading, setIsLoading] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)

  const form = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  })

  async function onSubmit(data: ResetPasswordFormValues) {
    if (!token) {
      toast({
        title: "Invalid reset link",
        description: "This reset link is invalid or has expired.",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)

    try {
      await AuthAPI.resetPassword(token, data.password, data.confirmPassword)
      setIsSuccess(true)
      toast({
        title: "Password reset successful!",
        description: "Your password has been updated. You can now sign in.",
      })
    } catch (error) {
      console.error("Reset password error:", error)
      toast({
        title: "Reset failed",
        description: error instanceof Error ? error.message : "Please try again or request a new reset link.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (!token) {
    return (
      <AuthCard>
        <div className="space-y-6 text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", duration: 0.6 }}
            className="mx-auto flex size-16 items-center justify-center rounded-full bg-gradient-to-r from-red-500 to-pink-500"
          >
            <Icons.alertCircle className="size-8 text-white" />
          </motion.div>
          
          <div className="space-y-2">
            <h3 className="text-xl font-semibold">Invalid reset link</h3>
            <p className="text-muted-foreground">
              This password reset link is invalid or has expired.
            </p>
          </div>

          <div className="space-y-4">
            <Link href="/forgot-password">
              <Button className="w-full">
                Request new reset link
              </Button>
            </Link>
            
            <Link
              href="/login"
              className="block text-sm text-blue-600 transition-colors hover:text-blue-500"
            >
              ← Back to sign in
            </Link>
          </div>
        </div>
      </AuthCard>
    )
  }

  if (isSuccess) {
    return (
      <AuthCard>
        <div className="space-y-6 text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", duration: 0.6 }}
            className="mx-auto flex size-16 items-center justify-center rounded-full bg-gradient-to-r from-green-500 to-emerald-500"
          >
            <Icons.check className="size-8 text-white" />
          </motion.div>
          
          <div className="space-y-2">
            <h3 className="text-xl font-semibold">Password updated!</h3>
            <p className="text-muted-foreground">
              Your password has been successfully reset. You can now sign in with your new password.
            </p>
          </div>

          <Link href="/login">
            <Button className="w-full">
              Continue to sign in
            </Button>
          </Link>
        </div>
      </AuthCard>
    )
  }

  return (
    <AuthCard>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-2 text-center">
          <h3 className="text-lg font-semibold">Create new password</h3>
          <p className="text-sm text-muted-foreground">
            Enter a strong password for your account.
          </p>
        </div>

        <div className="space-y-4">
          {/* New Password Field */}
          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm font-medium">
              New password
            </Label>
            <Input
              id="password"
              type="password"
              placeholder="Enter your new password"
              autoComplete="new-password"
              disabled={isLoading}
              className="h-11 border-white/20 bg-white/50 focus:border-blue-500/50 focus:ring-blue-500/20 dark:border-white/10 dark:bg-black/50"
              {...form.register("password")}
            />
            {form.formState.errors.password && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.password.message}
              </motion.p>
            )}
          </div>

          {/* Confirm Password Field */}
          <div className="space-y-2">
            <Label htmlFor="confirmPassword" className="text-sm font-medium">
              Confirm password
            </Label>
            <Input
              id="confirmPassword"
              type="password"
              placeholder="Confirm your new password"
              autoComplete="new-password"
              disabled={isLoading}
              className="h-11 border-white/20 bg-white/50 focus:border-blue-500/50 focus:ring-blue-500/20 dark:border-white/10 dark:bg-black/50"
              {...form.register("confirmPassword")}
            />
            {form.formState.errors.confirmPassword && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.confirmPassword.message}
              </motion.p>
            )}
          </div>
        </div>

        <Button
          type="submit"
          disabled={isLoading}
          className="h-11 w-full bg-gradient-to-r from-blue-600 to-purple-600 font-medium text-white shadow-lg shadow-blue-500/25 transition-all duration-200 hover:from-blue-700 hover:to-purple-700"
        >
          {isLoading ? (
            <>
              <Icons.spinner className="mr-2 size-4 animate-spin" />
              Updating password...
            </>
          ) : (
            "Update password"
          )}
        </Button>

        <div className="text-center">
          <Link
            href="/login"
            className="text-sm text-blue-600 transition-colors hover:text-blue-500"
          >
            ← Back to sign in
          </Link>
        </div>
      </form>
    </AuthCard>
  )
}
