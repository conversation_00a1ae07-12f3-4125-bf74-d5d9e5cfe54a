/**
 * Exclusion Filter API Client
 *
 * API client for managing exclusion filters, following the same patterns as form-api.ts
 */

import apiClient from '@/lib/api-client';
import {
  ExclusionFilter,
  ExclusionFilterCreateRequest,
  ExclusionFilterUpdateRequest,
  ExclusionCheckRequest,
  ExclusionCheckResponse
} from '@/lib/types/exclusion-filter';

/**
 * Exclusion Filter API service for interacting with the backend
 */
const ExclusionFilterAPI = {
  /**
   * Create a new exclusion filter
   * @param filterData Filter creation data
   * @returns Created exclusion filter
   */
  async createExclusionFilter(filterData: ExclusionFilterCreateRequest): Promise<ExclusionFilter> {
    console.log('Creating exclusion filter with data:', filterData);

    // Validate required fields
    if (!filterData.form_id || !filterData.name) {
      throw new Error('Form ID and filter name are required');
    }

    const response = await apiClient.post('/exclusion-filters', filterData);

    // Validate response contains _id
    if (!response.data._id && !response.data.id) {
      console.error('Exclusion filter creation response missing _id:', response.data);
      throw new Error('Exclusion filter creation failed: Missing filter ID in response');
    }

    console.log('Exclusion filter created successfully:', response.data);
    return response.data;
  },

  /**
   * Get all exclusion filters for a form
   * @param formId Form ID to filter by
   * @param includeDeleted Whether to include deleted filters
   * @returns List of exclusion filters
   */
  async listExclusionFilters(formId?: string, includeDeleted: boolean = false): Promise<ExclusionFilter[]> {
    console.log(`Fetching exclusion filters for form: ${formId}, includeDeleted: ${includeDeleted}`);

    const params = new URLSearchParams();
    if (formId) {
      params.append('form_id', formId);
    }
    if (includeDeleted) {
      params.append('include_deleted', 'true');
    }

    const url = `/exclusion-filters?${params.toString()}`;
    console.log('Making request to:', url);

    try {
      const response = await apiClient.get(url);
      console.log('Exclusion filters API response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching exclusion filters:', error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
        console.error('Response headers:', error.response.headers);

        // Handle specific error cases
        if (error.response.status === 403) {
          throw new Error('Access denied: You may not have permission to view exclusion filters');
        } else if (error.response.status === 404) {
          throw new Error('Exclusion filters endpoint not found');
        } else if (error.response.status === 401) {
          throw new Error('Authentication required');
        }
      }
      throw error;
    }
  },

  /**
   * Get a specific exclusion filter by ID
   * @param filterId Filter ID
   * @returns Exclusion filter
   */
  async getExclusionFilter(filterId: string): Promise<ExclusionFilter> {
    console.log(`Fetching exclusion filter with ID: ${filterId}`);

    const response = await apiClient.get(`/exclusion-filters/${filterId}`);
    console.log('Exclusion filter fetched:', response.data);

    return response.data;
  },

  /**
   * Update an exclusion filter
   * @param filterId Filter ID
   * @param filterData Updated filter data
   * @returns Updated exclusion filter
   */
  async updateExclusionFilter(filterId: string, filterData: ExclusionFilterUpdateRequest): Promise<ExclusionFilter> {
    console.log(`Updating exclusion filter ${filterId} with data:`, filterData);

    const response = await apiClient.patch(`/exclusion-filters/${filterId}`, filterData);
    console.log('Exclusion filter updated successfully:', response.data);

    return response.data;
  },

  /**
   * Delete an exclusion filter (soft delete)
   * @param filterId Filter ID
   * @returns Success status
   */
  async deleteExclusionFilter(filterId: string): Promise<boolean> {
    console.log(`Deleting exclusion filter with ID: ${filterId}`);

    try {
      await apiClient.delete(`/exclusion-filters/${filterId}`);
      console.log('Exclusion filter deleted successfully');
      return true;
    } catch (error) {
      console.error('Error deleting exclusion filter:', error);
      throw error;
    }
  },

  /**
   * Check if a submission should be excluded based on exclusion filters
   * @param request Check request with form ID and answers
   * @returns Exclusion check result
   */
  async checkExclusion(request: ExclusionCheckRequest): Promise<ExclusionCheckResponse> {
    console.log('Checking exclusion for submission:', request);

    const response = await apiClient.post('/exclusion-filters/check', request);
    console.log('Exclusion check result:', response.data);

    return response.data;
  }
};

export default ExclusionFilterAPI;

// Also export as named export for consistency
export { ExclusionFilterAPI };
