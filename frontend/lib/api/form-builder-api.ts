/**
 * Form Builder API Client
 *
 * Modern API client that matches the backend endpoints exactly.
 * Uses individual endpoints for each entity type as per backend design.
 */

import apiClient from '@/lib/api-client';
import {
  Form,
  FormWithDetails,
  Section,
  Question,
  FormCreateRequest,
  SectionCreateRequest,
  QuestionCreateRequest
} from '@/lib/types/form';

export class FormBuilderAPI {
  // ===== FORM OPERATIONS =====
  
  // Track ongoing form creation requests to prevent duplicates
  private static ongoingFormCreation: Promise<Form> | null = null;

  /**
   * Create a new form
   */
  static async createForm(data: FormCreateRequest): Promise<Form> {
    if (process.env.NODE_ENV === 'development') {
      console.log('Creating form:', data);
    }
    
    // If there's already an ongoing form creation, return that promise
    if (this.ongoingFormCreation) {
      if (process.env.NODE_ENV === 'development') {
        console.log('⚠️ Form creation already in progress, returning existing promise');
      }
      return this.ongoingFormCreation;
    }
    
    // Create new form creation promise
    this.ongoingFormCreation = (async () => {
      try {
        const response = await apiClient.post('/forms', data);
        if (process.env.NODE_ENV === 'development') {
          console.log('Form created:', response.data);
        }
        return response.data;
      } finally {
        // Clear the ongoing request when done
        this.ongoingFormCreation = null;
      }
    })();
    
    return this.ongoingFormCreation;
  }

  /**
   * Get form with all details (sections and questions)
   */
  static async getFormDetails(formId: string): Promise<FormWithDetails> {
    console.log('Fetching form details:', formId);
    const response = await apiClient.get(`/forms/${formId}/details`);
    console.log('Form details fetched:', response.data);
    return response.data;
  }

  /**
   * Update form metadata
   */
  static async updateForm(formId: string, data: Partial<FormCreateRequest>): Promise<Form> {
    console.log('Updating form:', formId, data);
    const response = await apiClient.put(`/forms/${formId}`, data);
    console.log('Form updated:', response.data);
    return response.data;
  }

  /**
   * Delete form (archive)
   */
  static async deleteForm(formId: string): Promise<void> {
    console.log('Deleting form:', formId);
    await apiClient.delete(`/forms/${formId}`);
    console.log('Form deleted');
  }

  // ===== SECTION OPERATIONS =====

  /**
   * Create a new section
   */
  static async createSection(formId: string, data: SectionCreateRequest): Promise<Section> {
    console.log('Creating section for form:', formId, data);
    const response = await apiClient.post(`/forms/${formId}/sections`, data);
    console.log('Section created:', response.data);
    return response.data;
  }

  /**
   * Update section
   */
  static async updateSection(sectionId: string, data: Partial<SectionCreateRequest>): Promise<Section> {
    console.log('Updating section:', sectionId, data);
    const response = await apiClient.patch(`/forms/sections/${sectionId}`, data);
    console.log('Section updated:', response.data);
    return response.data;
  }

  /**
   * Delete section
   */
  static async deleteSection(sectionId: string): Promise<void> {
    console.log('Deleting section:', sectionId);
    await apiClient.delete(`/forms/sections/${sectionId}`);
    console.log('Section deleted');
  }

  /**
   * Duplicate section
   */
  static async duplicateSection(formId: string, sectionId: string): Promise<Section> {
    console.log('Duplicating section:', sectionId);

    // Get the section details first
    const formDetails = await this.getFormDetails(formId);
    const originalSection = formDetails.sections.find(s => (s._id || s.id) === sectionId);

    if (!originalSection) {
      throw new Error('Section not found');
    }

    // Create duplicate section
    const duplicateData: SectionCreateRequest = {
      title: `${originalSection.title} (Copy)`,
      description: originalSection.description || '',
      order: originalSection.order + 1,
      repeatable: originalSection.repeatable
    };

    const newSection = await this.createSection(formId, duplicateData);

    // Duplicate all questions
    for (const question of originalSection.questions) {
      const questionData: QuestionCreateRequest = {
        section_id: newSection._id || newSection.id!,
        type: question.type,
        label: question.label,
        help_text: question.help_text,
        required: question.required,
        options: question.options,
        validation: question.validation,
        visibility_condition: question.visibility_condition,
        repeat_section_id: question.repeat_section_id,
        max_repeats: question.max_repeats,
        order: question.order
      };

      await this.createQuestion(newSection._id || newSection.id!, questionData);
    }

    return newSection;
  }

  // ===== QUESTION OPERATIONS =====

  /**
   * Create a new question
   */
  static async createQuestion(sectionId: string, data: QuestionCreateRequest): Promise<FormWithDetails> {
    console.log('Creating question for section:', sectionId, data);
    const response = await apiClient.post(`/forms/sections/${sectionId}/questions`, data);
    console.log('Question created, returning full form:', response.data);
    return response.data;
  }

  /**
   * Update question
   */
  static async updateQuestion(questionId: string, data: Partial<QuestionCreateRequest>): Promise<Question> {
    console.log('Updating question:', questionId, data);
    const response = await apiClient.put(`/forms/questions/${questionId}`, data);
    console.log('Question updated:', response.data);
    return response.data;
  }

  /**
   * Delete question
   */
  static async deleteQuestion(questionId: string): Promise<void> {
    console.log('Deleting question:', questionId);
    await apiClient.delete(`/forms/questions/${questionId}`);
    console.log('Question deleted');
  }

  /**
   * Duplicate question
   */
  static async duplicateQuestion(sectionId: string, questionId: string): Promise<FormWithDetails> {
    console.log('Duplicating question:', questionId);

    // Get the question details first
    const response = await apiClient.get(`/forms/questions/${questionId}`);
    const originalQuestion = response.data;

    // Create duplicate question
    const duplicateData: QuestionCreateRequest = {
      section_id: sectionId,
      type: originalQuestion.type,
      label: `${originalQuestion.label} (Copy)`,
      help_text: originalQuestion.help_text,
      required: originalQuestion.required,
      options: originalQuestion.options,
      validation: originalQuestion.validation,
      visibility_condition: originalQuestion.visibility_condition,
      repeat_section_id: originalQuestion.repeat_section_id,
      max_repeats: originalQuestion.max_repeats,
      order: originalQuestion.order + 1
    };

    return await this.createQuestion(sectionId, duplicateData);
  }

  // ===== ORDER OPERATIONS =====
  // Note: Order operations now use atomic reorder APIs in FormAPI
  // Individual order updates are deprecated for drag-and-drop operations

  // ===== UTILITY METHODS =====

  /**
   * Get all questions from a form (flattened)
   */
  static getAllQuestions(form: FormWithDetails): Question[] {
    return form.sections.flatMap(section => section.questions);
  }

  /**
   * Get questions that can be used for visibility conditions
   * (all questions that appear before the given question in form order)
   */
  static getAvailableQuestionsForVisibility(form: FormWithDetails, currentQuestionId?: string): Question[] {
    const allQuestions = this.getAllQuestions(form);

    if (!currentQuestionId) {
      return allQuestions;
    }

    // Find the current question's position
    let currentQuestionFound = false;
    const availableQuestions: Question[] = [];

    for (const section of form.sections) {
      for (const question of section.questions) {
        const questionId = question._id || question.id;

        if (questionId === currentQuestionId) {
          currentQuestionFound = true;
          break;
        }

        availableQuestions.push(question);
      }

      if (currentQuestionFound) break;
    }

    return availableQuestions;
  }
}

export default FormBuilderAPI;
