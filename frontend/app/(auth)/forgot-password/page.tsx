import { Metada<PERSON> } from "next"
import { Suspense } from "react"

import { AuthLayout } from "@/components/auth/auth-layout"
import { ForgotPasswordForm } from "@/components/auth/forgot-password-form"
import { mobileRetreat } from "@/lib/utils/responsive"

export const metadata: Metadata = {
  title: "Reset your password - TractionX",
  description: "Reset your TractionX account password to regain access to your investment platform.",
}

export default function ForgotPasswordPage() {
  return (
    <AuthLayout
      title="Reset your password"
      subtitle="We'll help you get back into your account"
    >
      <Suspense fallback={
        <div className={mobileRetreat.loading.container}>
          <div className={mobileRetreat.loading.spinner}></div>
          <p className={mobileRetreat.loading.text}>Loading...</p>
        </div>
      }>
        <ForgotPasswordForm />
      </Suspense>
    </AuthLayout>
  )
}
