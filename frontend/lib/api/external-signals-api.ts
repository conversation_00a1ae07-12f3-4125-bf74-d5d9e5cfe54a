/**
 * External Signals API Client
 * 
 * Handles communication with the backend research service for analyst-grade
 * external signals including competitors, market trends, news, and executive summaries.
 */

import apiClient from "../api-client"



// Types for External Signals Research
export interface CompetitorInfo {
  name: string
  website?: string
  description: string
  comparison: string
  sources: string[]
}

export interface CompetitorsData {
  competitors: CompetitorInfo[]
}

export interface MarketTrend {
  summary: string
  sources: string[]
}

export interface MarketData {
  market_trends: MarketTrend[]
}

export interface NewsSignal {
  headline: string
  summary: string
  url: string
  date?: string
}

export interface NewsData {
  news_signals: NewsSignal[]
}

export interface ExecutiveSummaryData {
  executive_summary: string
  sources: string[]
}

export interface ExternalSignalsResponse {
  deal_id: string
  competitors?: CompetitorsData
  market?: MarketData
  news?: NewsData
  summary?: ExecutiveSummaryData
  generated_at?: number
  version: string
}

export interface RefreshResponse {
  success: boolean
  message: string
  job_id: string
  deal_id: string
}

export const ExternalSignalsAPI = {
  /**
   * Get external signals research for a deal
   */
  async getExternalSignals(dealId: string): Promise<ExternalSignalsResponse> {
    console.log(`Fetching external signals for deal ${dealId}`)
    
    try {
      const response = await apiClient.get(`/deals/${dealId}/external-signals`)
      console.log('External signals fetched:', response.data)
      return response.data
    } catch (error) {
      console.error('Error fetching external signals:', error)
      throw error
    }
  },

  /**
   * Force refresh external signals research for a deal
   */
  async refreshExternalSignals(dealId: string): Promise<RefreshResponse> {
    console.log(`Refreshing external signals for deal ${dealId}`)
    
    try {
      const response = await apiClient.post(`/deals/${dealId}/external-signals/refresh`)
      console.log('External signals refresh initiated:', response.data)
      return response.data
    } catch (error) {
      console.error('Error refreshing external signals:', error)
      throw error
    }
  },

  /**
   * Check if external signals are available for a deal
   */
  async hasExternalSignals(dealId: string): Promise<boolean> {
    try {
      const signals = await this.getExternalSignals(dealId)
      return !!(signals.competitors || signals.market || signals.news || signals.summary)
    } catch (error) {
      console.error('Error checking external signals availability:', error)
      return false
    }
  }
}
