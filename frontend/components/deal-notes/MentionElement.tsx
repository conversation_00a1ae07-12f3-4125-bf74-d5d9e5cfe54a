import React from 'react';
import { RenderElementProps } from 'slate-react';

export const MentionElement = ({ attributes, children, element }: RenderElementProps) => {
  // Only render name if this is a mention element
  if (element.type === 'mention') {
    return (
      <span
        {...attributes}
        contentEditable={false}
        className="px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-medium"
      >
        @{element.name}
        {children}
      </span>
    );
  }
  // Fallback for non-mention elements (should not happen)
  return <span {...attributes}>{children}</span>;
}; 