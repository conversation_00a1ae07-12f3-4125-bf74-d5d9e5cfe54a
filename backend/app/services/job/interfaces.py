"""
Job Tracking Service Interfaces

This module defines the interfaces for job tracking services.
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union
from bson import ObjectId

from app.models.job import TrackedJob, JobStatus, JobType, EntityType


class JobServiceInterface(ABC):
    """Interface for job tracking services."""

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the service."""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up resources used by the service."""
        pass

    @abstractmethod
    async def create_job(
        self,
        entity_type: Union[str, EntityType],
        entity_id: Union[str, ObjectId],
        job_type: Union[str, JobType],
        payload: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
        config: Optional[Dict[str, Any]] = None,
        priority: int = 0,
        parent_job_id: Optional[Union[str, ObjectId]] = None,
        depends_on: Optional[List[Union[str, ObjectId]]] = None,
        scheduled_at: Optional[int] = None,
        queue_priority: Optional[Any] = None,
        queue_type: Optional[Any] = None,
        queue_job_type: Optional[str] = None
    ) -> TrackedJob:
        """
        Create a new tracked job.

        Args:
            entity_type: Type of entity this job is associated with
            entity_id: ID of the entity this job is associated with
            job_type: Type of job being performed
            payload: Job payload to be passed to the queue
            metadata: Additional metadata about the job
            config: Configuration for the job
            priority: Priority of the job (higher values = higher priority)
            parent_job_id: ID of the parent job if this is a child job
            depends_on: IDs of jobs this job depends on
            scheduled_at: When the job is scheduled to run
            queue_priority: Priority in the queue system
            queue_type: Type of queue to use
            queue_job_type: Type of job in the queue system

        Returns:
            The created job
        """
        pass

    @abstractmethod
    async def update_job_status(
        self,
        job_id: Union[str, ObjectId],
        status: Union[str, JobStatus],
        error: Optional[str] = None,
        progress: Optional[float] = None,
        output: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> Optional[TrackedJob]:
        """
        Update the status of a tracked job.

        Args:
            job_id: ID of the job to update
            status: New status of the job
            error: Error message if the job failed
            progress: Progress of the job (0.0 to 1.0)
            output: Output of the job (results, data, etc.)
            metadata: Additional metadata about the job
            config: Configuration for the job

        Returns:
            The updated job, or None if the job was not found
        """
        pass

    @abstractmethod
    async def get_job(
        self,
        job_id: Union[str, ObjectId]
    ) -> Optional[TrackedJob]:
        """
        Get a tracked job by ID.

        Args:
            job_id: ID of the job to get

        Returns:
            The job, or None if not found
        """
        pass

    @abstractmethod
    async def get_entity_jobs(
        self,
        entity_type: Union[str, EntityType],
        entity_id: Union[str, ObjectId],
        status: Optional[Union[str, JobStatus]] = None,
        job_type: Optional[Union[str, JobType]] = None
    ) -> List[TrackedJob]:
        """
        Get all jobs for an entity.

        Args:
            entity_type: Type of entity
            entity_id: ID of the entity
            status: Optional filter by job status
            job_type: Optional filter by job type

        Returns:
            List of jobs for the entity
        """
        pass

    @abstractmethod
    async def link_job_to_entity(
        self,
        job_id: Union[str, ObjectId],
        entity_type: Union[str, EntityType],
        entity_id: Union[str, ObjectId]
    ) -> bool:
        """
        Link a job to an entity by adding it to the entity's active_jobs list.

        Args:
            job_id: ID of the job to link
            entity_type: Type of entity
            entity_id: ID of the entity

        Returns:
            True if the job was linked, False otherwise
        """
        pass

    @abstractmethod
    async def unlink_job_from_entity(
        self,
        job_id: Union[str, ObjectId],
        entity_type: Union[str, EntityType],
        entity_id: Union[str, ObjectId]
    ) -> bool:
        """
        Unlink a job from an entity by removing it from the entity's active_jobs list.

        Args:
            job_id: ID of the job to unlink
            entity_type: Type of entity
            entity_id: ID of the entity

        Returns:
            True if the job was unlinked, False otherwise
        """
        pass
