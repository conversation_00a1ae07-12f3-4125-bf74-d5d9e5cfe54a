# Deal Notes Feature

A beautiful, collaborative notes system for deal discussions with team mentions, pinning, and real-time updates.

## 🎯 Overview

The Deal Notes feature provides a Notion-lite experience for recording critical notes from internal meetings, tagging teammates with mentions, and maintaining a structured history of discussions per deal.

## ✨ Features

- **Rich Text Notes**: Support for markdown formatting, mentions, and tags
- **Team Mentions**: Tag teammates with @mentions that auto-resolve to user IDs
- **Pinning System**: Pin important notes to keep them at the top
- **Real-time Updates**: Instant feedback and optimistic updates
- **Glassmorphic Design**: Beautiful, modern UI with smooth animations
- **Mobile-First**: Responsive design that works perfectly on all devices
- **Access Control**: Only users in the same org can view/write notes
- **Soft Deletion**: Notes are soft-deleted with audit trail

## 🏗️ Architecture

### Components Structure

```
frontend/components/core/deals/deal-detail/
├── notes-tab.tsx              # Main notes tab container
└── notes/
    ├── note-card.tsx          # Individual note display
    └── note-input-composer.tsx # Input composer with mentions
```

### API Integration

```
frontend/lib/api/
├── deal-notes-api.ts          # API client for deal notes
└── hooks/
    └── use-mentions.ts        # Mentions management hook
```

## 🚀 Usage

### Adding Notes Tab to Deal Page

The Notes tab is automatically added to the deal tabs. It appears as the last tab with a message square icon.

### Creating Notes

1. Navigate to any deal page
2. Click on the "Notes" tab
3. Use the input composer at the bottom to write your note
4. Use `@` to mention teammates (autocomplete will appear)
5. Click "Post" or press `Cmd+Enter` to submit

### Mentioning Teammates

- Type `@` followed by a name or email
- Autocomplete dropdown will show matching users
- Click on a user to insert the mention
- Mentions are highlighted in blue with user icons

### Pinning Notes

- Hover over any note to reveal action buttons
- Click the three-dot menu
- Select "Pin" to pin the note to the top
- Pinned notes have a gold border and "Pinned" badge

### Editing Notes

- Only the author can edit their own notes
- Hover over a note and click the three-dot menu
- Select "Edit" to enter edit mode
- Make changes and click "Save"

### Deleting Notes

- Only the author can delete their own notes
- Hover over a note and click the three-dot menu
- Select "Delete" to soft-delete the note
- Confirmation dialog will appear

## 🎨 Design System

### Color Palette

- **Primary**: Blue (#3B82F6) for mentions and actions
- **Success**: Green for successful operations
- **Warning**: Amber for pinned notes
- **Error**: Red for destructive actions
- **Neutral**: Gray scale for text and borders

### Typography

- **Headings**: Inter font, semibold weight
- **Body**: Inter font, regular weight
- **Mono**: For code snippets and technical content

### Spacing

- **Card Padding**: 16px (p-4)
- **Component Gap**: 12px (gap-3)
- **Section Spacing**: 24px (space-y-6)

### Animations

- **Hover**: Scale 1.01 with smooth transition
- **Enter/Exit**: Fade and slide animations
- **Stagger**: Notes animate in with 50ms delays
- **Loading**: Smooth spinners and skeleton states

## 🔧 API Reference

### DealNotesAPI

```typescript
// List notes for a deal
DealNotesAPI.listNotes(dealId, skip, limit, includeDeleted)

// Create a new note
DealNotesAPI.createNote(dealId, { content, tagged_user_ids, pinned })

// Update a note
DealNotesAPI.updateNote(noteId, { content, tagged_user_ids, pinned })

// Delete a note (soft delete)
DealNotesAPI.deleteNote(dealId, noteId)

// Toggle pin status
DealNotesAPI.togglePin(dealId, noteId, pinned)

// Get notes where user is tagged
DealNotesAPI.getNotesByUser(skip, limit)
```

### Data Types

```typescript
interface DealNote {
  _id: string
  deal_id: string
  org_id: string
  created_by: {
    _id: string
    name: string
    email: string
  }
  content: string
  tagged_user_ids: string[]
  pinned: boolean
  deleted_at?: number
  deleted_by?: string
  created_at: number
  updated_at: number
}
```

## 🎯 Keyboard Shortcuts

- **Cmd+Enter**: Submit note
- **Enter**: Expand input (when collapsed)
- **Escape**: Collapse input (when expanded)
- **@**: Trigger mentions autocomplete

## 📱 Mobile Experience

- **Touch Targets**: Minimum 44px for all interactive elements
- **Swipe Gestures**: Smooth scrolling and interactions
- **Responsive Layout**: Adapts to different screen sizes
- **Keyboard Handling**: Optimized for mobile keyboards

## 🔒 Security & Permissions

### Access Control

- **View**: Users in the same organization
- **Create**: Users in the same organization
- **Edit**: Only the note author
- **Delete**: Only the note author
- **Pin**: Only the note author

### Data Validation

- Content must not be empty
- Tagged users must exist in the organization
- Deal must exist and be accessible
- User must be authenticated

## 🚀 Performance Optimizations

- **Lazy Loading**: Notes are loaded in batches of 50
- **Optimistic Updates**: UI updates immediately, syncs with server
- **Debounced Input**: Mentions search is debounced
- **Virtual Scrolling**: Ready for large note lists
- **Image Optimization**: Avatar images are optimized

## 🧪 Testing Strategy

### Unit Tests (Recommended)

```typescript
// Test note creation
test('creates note with mentions', async () => {
  const note = await createNote({
    content: 'Meeting with @john about funding',
    tagged_user_ids: ['john-id']
  })
  expect(note.tagged_user_ids).toContain('john-id')
})

// Test mention extraction
test('extracts mentions from content', () => {
  const mentions = extractMentions('Discuss with @john and @jane')
  expect(mentions).toEqual(['john', 'jane'])
})
```

### Integration Tests

- Test API endpoints with real data
- Test component interactions
- Test error handling and edge cases

### E2E Tests

- Test complete user workflows
- Test mobile responsiveness
- Test accessibility features

## 🔮 Future Enhancements

### Phase 2 Features

- **Threaded Replies**: Reply to specific notes
- **Rich Media**: Support for images, files, and links
- **Advanced Formatting**: Tables, code blocks, and more
- **Notifications**: Real-time notifications for mentions
- **Search**: Full-text search across notes
- **Export**: Export notes to PDF or markdown

### Phase 3 Features

- **AI Integration**: Smart note suggestions and summaries
- **Templates**: Pre-defined note templates
- **Collaboration**: Real-time collaborative editing
- **Analytics**: Note engagement and usage metrics
- **Integrations**: Slack, email, and calendar integration

## 🐛 Troubleshooting

### Common Issues

1. **Mentions not working**: Check that users exist in the organization
2. **Notes not loading**: Verify deal ID and user permissions
3. **Edit not working**: Ensure you're the note author
4. **Mobile layout issues**: Check responsive breakpoints

### Debug Mode

Enable debug logging by setting `DEBUG=deal-notes` in your environment variables.

## 📚 Related Documentation

- [Backend Deal Notes API](../backend/docs/deal_notes_implementation.md)
- [Authentication System](../auth/README.md)
- [Design System](../design/README.md)
- [API Client](../lib/api/README.md) 