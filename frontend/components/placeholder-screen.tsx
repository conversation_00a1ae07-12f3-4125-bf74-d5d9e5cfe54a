// "use client"

// import { motion } from 'framer-motion'
// import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
// import { Button } from '@/components/ui/button'
// import { Badge } from '@/components/ui/badge'
// import { 
//   ArrowRight, 
//   Wand2, 
//   TrendingUp, 
//   Users, 
//   Target,
//   Zap
// } from 'lucide-react'
// import { cn } from '@/lib/utils'

// // Remove premium-wave import since it's deleted
// // const PremiumWave = dynamic(() => import("@/components/ui/premium-wave"), { ssr: false })

// interface PlaceholderScreenProps {
//   title: string
//   description: string
//   actionText?: string
//   actionHref?: string
//   showAnimation?: boolean
//   className?: string
// }

// export function PlaceholderScreen({
//   title,
//   description,
//   actionText,
//   actionHref,
//   showAnimation = false,
//   className
// }: PlaceholderScreenProps) {
//   return (
//     <div className={cn("min-h-screen bg-tx-surface flex items-center justify-center p-4", className)}>
//       <div className="max-w-2xl mx-auto text-center space-y-8">
//         {/* Animated background placeholder */}
//         {showAnimation && (
//           <div className="relative w-full h-64 rounded-2xl bg-gradient-to-br from-primary/10 to-secondary/10 overflow-hidden">
//             <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/5 to-transparent animate-shimmer" />
//             <div className="absolute inset-0 flex items-center justify-center">
//               <Zap className="w-16 h-16 text-primary/30" />
//             </div>
//           </div>
//         )}

//         <motion.div
//           initial={{ opacity: 0, y: 20 }}
//           animate={{ opacity: 1, y: 0 }}
//           transition={{ duration: 0.6 }}
//           className="space-y-6"
//         >
//           <div className="space-y-4">
//             <div className="flex items-center justify-center gap-2">
//               <Wand2 className="w-6 h-6 text-primary" />
//               <Badge variant="outline" className="border-primary/20 text-primary">
//                 Premium Feature
//               </Badge>
//             </div>
            
//             <h1 className="text-3xl font-bold text-text">
//               {title}
//             </h1>
            
//             <p className="text-lg text-muted max-w-lg mx-auto">
//               {description}
//             </p>
//           </div>

//           {/* Feature preview cards */}
//           <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
//             <Card className="border-0 bg-surface shadow-institutional rounded-xl">
//               <CardContent className="p-4">
//                 <TrendingUp className="w-8 h-8 text-primary mx-auto mb-2" />
//                 <h3 className="font-semibold text-text text-sm">Advanced Analytics</h3>
//                 <p className="text-xs text-muted mt-1">Real-time insights</p>
//               </CardContent>
//             </Card>
            
//             <Card className="border-0 bg-surface shadow-institutional rounded-xl">
//               <CardContent className="p-4">
//                 <Users className="w-8 h-8 text-secondary mx-auto mb-2" />
//                 <h3 className="font-semibold text-text text-sm">Team Collaboration</h3>
//                 <p className="text-xs text-muted mt-1">Enhanced workflows</p>
//               </CardContent>
//             </Card>
            
//             <Card className="border-0 bg-surface shadow-institutional rounded-xl">
//               <CardContent className="p-4">
//                 <Target className="w-8 h-8 text-primary mx-auto mb-2" />
//                 <h3 className="font-semibold text-text text-sm">AI-Powered</h3>
//                 <p className="text-xs text-muted mt-1">Smart automation</p>
//               </CardContent>
//             </Card>
//           </div>

//           {actionText && actionHref && (
//             <Button size="lg" className="mt-6">
//               {actionText}
//               <ArrowRight className="w-4 h-4 ml-2" />
//             </Button>
//           )}
//         </motion.div>
//       </div>
//     </div>
//   )
// }

"use client"
import Image from "next/image"
import Link from "next/link"
import { motion } from 'framer-motion'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils";

interface PlaceholderScreenProps {
  title: string
  description: string
  imagePath: string
  buttonText?: string
  buttonLink?: string
}

import dynamic from "next/dynamic"
const PremiumWave = dynamic(() => import("@/components/ui/premium-wave"), { ssr: false })

// Keeping ParticleOrbitField for future use
// const ParticleOrbitField = dynamic(() => import("@/components/ui/particle-fields"), {
//   ssr: false,
// })

// PremiumWave component available for future use
// const PremiumWave = dynamic(() => import("@/components/ui/premium-wave"), {
//   ssr: false,
// })

const ClientOrbitPulseGrid = dynamic(() => import("@/components/core/orbit-ai/orbit-icon").then(mod => ({ default: mod.OrbitPulseGrid })), {
  ssr: false,
})

export default function OrbitHero() {
  return (
    <section className="relative flex min-h-screen w-full flex-col overflow-hidden">
      {/* Gradient background (bulletproof, always fills viewport) */}
      <div className="pointer-events-none fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-neutral-50 via-white to-neutral-100" />
        <div 
          className="absolute inset-0 opacity-[0.015]"
          style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgb(0 0 0) 1px, transparent 0)`,
            backgroundSize: '60px 60px'
          }}
        />
      </div>

      {/* Premium Wave - positioned at top, above gradient */}
      <div className="pointer-events-none absolute inset-x-0 top-0 z-10 h-[140px] sm:h-[180px] lg:h-[200px]">
        <PremiumWave />
        {/* Fade-out overlay for seamless blend */}
        <div className="absolute inset-x-0 bottom-0 z-20 h-8 bg-gradient-to-b from-transparent to-[rgba(245,245,245,0.95)] sm:h-12" />
      </div>

      {/* Main Content Container */}
      <div className="relative z-20 flex flex-1 flex-col">
        {/* Header Section - Fixed at top */}
        <header className="flex items-center justify-between px-4 pt-6 sm:px-6 sm:pt-8 lg:px-8 lg:pt-10">
          {/* TractionX Logo */}
          <Link href="/" className="flex items-center gap-2 transition-opacity hover:opacity-80">
            <Image
              src="/images/traction.svg"
              alt="TractionX"
              width={150}
              height={150}
              className="h-auto w-24 drop-shadow-sm sm:w-28 lg:w-32"
            />
          </Link>

          {/* Login Button */}
          <Button 
            asChild
            variant="outline"
            size="sm"
            className="border-neutral-200 bg-white/80 text-sm backdrop-blur-sm hover:border-neutral-300"
          >
            <Link href="/login">Login</Link>
          </Button>
        </header>

        {/* Main Content - Perfectly Centered and fills screen */}
        <main className="flex flex-1 flex-col items-center justify-center px-4 pb-8 pt-16 sm:px-6 sm:pb-12 sm:pt-20 lg:px-8 lg:pb-16 lg:pt-24">
          {/* Content Wrapper with max-width for better readability */}
          <div className="mx-auto w-full max-w-6xl text-center">
            {/* Main Headline */}
            <div className="mb-12 sm:mb-16 lg:mb-20">
              <h1
                className="lg:text-7.5xl text-5xl font-bold leading-tight tracking-tight text-neutral-900 sm:text-6xl md:text-7xl xl:text-[6rem]"
                style={{ fontFamily: "'Playfair Display', 'Inter', serif" }}
              >
                <span className="mb-3 block">Access to Private Markets,</span>
                <span className="bg-gradient-to-r from-neutral-900 via-neutral-700 to-neutral-900 bg-clip-text text-transparent">
                  the only place you need to be!
                </span>
              </h1>
            </div>
            {/* Orbit & Status Section */}
            <div className="mb-16 flex flex-col items-center justify-center gap-8 sm:mb-20 sm:gap-10 lg:mb-24 lg:flex-row lg:gap-16">
              {/* OrbitPulseGrid */}
              <div className="relative order-2 lg:order-1">
                <div className="relative z-10">
                  <ClientOrbitPulseGrid
                    size={100}
                    dotCount={16}
                    duration={1500}
                    className="drop-shadow-lg sm:size-20 lg:size-24 xl:size-28"
                  />
                </div>
                {/* Subtle glow effect */}
                <div className="absolute inset-0 z-0 rounded-full bg-neutral-900/5 blur-2xl sm:blur-3xl" />
              </div>
              {/* Status Text */}
              <div className="max-w-md text-center md:text-left">
                <p className="mb-1 font-mono text-lg font-semibold tracking-widest text-neutral-700">
                  Orbit is live.
                </p>
                <p className="text-lg font-light leading-relaxed text-neutral-600">
                  The edge investors need to excel in all private market lifecycles
                </p>
              </div>
            </div>
            {/* CTA Button */}
            <div className="relative">
              <Link href="/login">
                <motion.button
                  className={cn(
                    "group relative rounded-full px-8 py-4 sm:px-10 sm:py-5 lg:px-12 lg:py-6",
                    "bg-gradient-to-r from-neutral-900 via-neutral-800 to-neutral-900",
                    "border border-amber-400/30 hover:border-amber-400/60",
                    "font-mono text-sm font-medium tracking-wide sm:text-base lg:text-lg",
                    "text-amber-50",
                    "transition-all duration-500 ease-out",
                    "hover:shadow-3xl shadow-2xl shadow-neutral-900/25 hover:shadow-neutral-900/40",
                    "overflow-hidden",
                    "hover:scale-105 active:scale-95",
                    "w-full max-w-sm sm:w-auto sm:max-w-none"
                  )}
                  whileHover={{ 
                    boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(251, 191, 36, 0.1)"
                  }}
                  whileTap={{ scale: 0.95 }}
                >
                  {/* Animated shimmer effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-amber-400/20 to-transparent"
                    animate={{
                      x: ["-100%", "200%"]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      repeatDelay: 3,
                      ease: "easeInOut"
                    }}
                  />

                  {/* Button content */}
                  <span className="relative z-10 flex items-center justify-center space-x-3">
                    <span className="relative text-center tracking-wider">
                      {/* Glow effect for the main text */}
                      <span className="absolute inset-0 hidden text-amber-400/30 blur-sm sm:block">
                        WORLD'S FIRST AGENTIC OS FOR PRIVATE MARKETS
                      </span>
                      <span className="absolute inset-0 text-amber-400/30 blur-sm sm:hidden">
                        AGENTIC OS FOR PRIVATE MARKETS
                      </span>
                      <span className="relative z-10 hidden sm:block">
                        WORLD'S FIRST AGENTIC OS FOR PRIVATE MARKETS
                      </span>
                      <span className="relative z-10 sm:hidden">
                        AGENTIC OS FOR PRIVATE MARKETS
                      </span>
                    </span>
                    <motion.span 
                      className="text-lg text-amber-400 sm:text-xl"
                      animate={{
                        x: [0, 4, 0]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      →
                    </motion.span>
                  </span>

                  {/* Subtle inner glow */}
                  <div className="absolute inset-0 rounded-full bg-gradient-to-r from-amber-400/5 via-transparent to-amber-400/5 opacity-0 transition-opacity duration-500 group-hover:opacity-100" />
                </motion.button>
              </Link>
            </div>
          </div>
        </main>
      </div>
    </section>
  )
}

export function PlaceholderScreen({
  title,
  description,
  imagePath,
  buttonText = "Go to Dashboard",
  buttonLink = "/dashboard",
}: PlaceholderScreenProps) {
  return (
    <div className="flex min-h-0 flex-1 flex-col items-center justify-center p-4 text-center sm:p-6 lg:p-8">
      <Card className="mx-auto w-full max-w-3xl">
        <CardHeader className="space-y-4">
          <CardTitle className="text-2xl font-bold sm:text-3xl lg:text-4xl">{title}</CardTitle>
          <CardDescription className="text-base sm:text-lg lg:text-xl">{description}</CardDescription>
        </CardHeader>
        <CardContent className="px-4 sm:px-6 lg:px-8">
          <div className="relative mx-auto aspect-video w-full max-w-2xl overflow-hidden rounded-lg">
            <Image
              src={imagePath}
              alt="Coming Soon"
              fill
              className="object-cover"
              priority
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-center px-4 pb-6 sm:px-6 lg:px-8">
          <Button asChild size="lg" className="w-full sm:w-auto">
            <Link href={buttonLink}>{buttonText}</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}