"""
TractionX Data Pipeline Service - FastAPI Application

This is the main FastAPI application that provides webhook endpoints
and API endpoints for the data pipeline service.
"""

import uvicorn  # type: ignore
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.docs import get_redoc_html, get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
from fastapi.responses import JSONResponse

from app.api import router as api_router
from app.configs import configure_logging, get_logger, settings
from app.webhooks import router as webhooks_router

# Configure logging
configure_logging()
logger = get_logger(__name__)


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""

    app = FastAPI(
        title="TractionX Data Pipeline Service",
        description="Data pipeline service for enriching company, founder, and news data",
        version=settings.VERSION,
        docs_url=None,  # We'll use custom docs endpoint
        redoc_url=None,  # We'll use custom redoc endpoint
        openapi_url=None,  # We'll use custom openapi endpoint
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add routers ^
    app.include_router(webhooks_router, prefix="/webhooks", tags=["webhooks"])
    app.include_router(api_router, prefix=settings.API_PREFIX, tags=["api"])

    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "service": settings.SERVICE_NAME,
            "version": settings.VERSION,
            "environment": settings.ENVIRONMENT,
        }

    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "service": settings.SERVICE_NAME,
            "version": settings.VERSION,
            "description": "TractionX Data Pipeline Service",
            "docs_url": "/docs",
            "redoc_url": "/redoc",
            "openapi_url": "/openapi.json",
        }

    # Global exception handler
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception):
        """Global exception handler."""
        logger.error(
            "Unhandled exception",
            path=request.url.path,
            method=request.method,
            error=str(exc),
            exc_info=True,
        )

        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal server error",
                "message": "An unexpected error occurred",
            },
        )

    # Startup event
    @app.on_event("startup")
    async def startup_event():
        """Application startup event."""
        logger.info(
            "Starting TractionX Data Pipeline Service",
            version=settings.VERSION,
            environment=settings.ENVIRONMENT,
            debug=settings.DEBUG,
        )

    # Shutdown event
    @app.on_event("shutdown")
    async def shutdown_event():
        """Application shutdown event."""
        logger.info("Shutting down TractionX Data Pipeline Service")

    # Custom OpenAPI documentation endpoints
    @app.get("/docs", include_in_schema=False)
    async def custom_swagger_ui_html():
        """Custom Swagger UI documentation."""
        return get_swagger_ui_html(
            openapi_url="/openapi.json",
            title=f"{settings.SERVICE_NAME} - API Documentation",
            swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui-bundle.js",
            swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui.css",
        )

    @app.get("/redoc", include_in_schema=False)
    async def redoc_html():
        """ReDoc documentation."""
        return get_redoc_html(
            openapi_url="/openapi.json",
            title=f"{settings.SERVICE_NAME} - API Documentation",
            redoc_js_url="https://cdn.jsdelivr.net/npm/redoc@next/bundles/redoc.standalone.js",
        )

    @app.get("/openapi.json", include_in_schema=False)
    async def get_openapi_endpoint():
        """OpenAPI JSON schema endpoint."""
        return get_openapi(
            title="TractionX Data Pipeline Service",
            version=settings.VERSION,
            description="Data pipeline service for enriching company, founder, and news data",
            routes=app.routes,
        )

    return app


# Create the app instance
app = create_app()


if __name__ == "__main__":
    # Run the application
    uvicorn.run(
        "main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=settings.DEBUG,
        reload_dirs=["./"] if settings.DEBUG else None,
    )
