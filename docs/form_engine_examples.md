# Form Engine API Examples

This document provides detailed examples of using the Form Engine API, with a focus on handling complex scenarios like repeatable sections and conditional visibility.

## Basic Form Engine Usage

The Form Engine API provides a unified interface for rendering forms in different modes:

- **Sequential Mode**: Returns questions step-by-step, only showing currently visible questions
- **Full Mode**: Returns the entire form structure with visibility information
- **Next Mode**: Returns only the next question based on the current state

### Sequential Mode Example

```bash
# Get form in sequential mode
curl -X POST "http://localhost:8000/api/v1/forms/6123456789abcdef01234567/engine" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "answers": {},
    "mode": "sequential",
    "include_dependencies": true,
    "include_validation": true,
    "include_visibility_rules": true
  }'
```

Response (initial state with no answers):
```json
{
  "form": {
    "_id": "6123456789abcdef01234567",
    "name": "Startup Qualification Form",
    "description": "Form to collect startup information and qualify for investment"
  },
  "current_section": {
    "_id": "6223456789abcdef01234567",
    "title": "Company Information",
    "description": "Basic information about your company"
  },
  "visible_questions": [
    {
      "_id": "6323456789abcdef01234567",
      "type": "short_text",
      "label": "Company Name",
      "required": true,
      "order": 0
    },
    {
      "_id": "6323456789abcdef01234568",
      "type": "single_select",
      "label": "Industry",
      "required": true,
      "options": [
        {"value": "technology", "label": "Technology"},
        {"value": "healthcare", "label": "Healthcare"},
        {"value": "finance", "label": "Finance"}
      ],
      "order": 1
    }
  ],
  "progress": {
    "current_section_index": 0,
    "total_sections": 3,
    "answered_questions": 0,
    "total_questions": 15
  },
  "navigation": {
    "next_section_id": "6223456789abcdef01234568",
    "prev_section_id": null
  }
}
```

### Full Mode Example

```bash
# Get form in full mode
curl -X POST "http://localhost:8000/api/v1/forms/6123456789abcdef01234567/engine" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "answers": {
      "6323456789abcdef01234567": "Acme Inc.",
      "6323456789abcdef01234568": "technology",
      "6323456789abcdef01234569": 3
    },
    "mode": "full",
    "include_dependencies": true,
    "include_validation": true,
    "include_visibility_rules": true
  }'
```

Response (with some answers provided):
```json
{
  "form": {
    "_id": "6123456789abcdef01234567",
    "name": "Startup Qualification Form",
    "description": "Form to collect startup information and qualify for investment"
  },
  "sections": [
    {
      "_id": "6223456789abcdef01234567",
      "title": "Company Information",
      "description": "Basic information about your company",
      "order": 0,
      "questions": [
        {
          "_id": "6323456789abcdef01234567",
          "type": "short_text",
          "label": "Company Name",
          "required": true,
          "order": 0,
          "visible": true,
          "value": "Acme Inc."
        },
        {
          "_id": "6323456789abcdef01234568",
          "type": "single_select",
          "label": "Industry",
          "required": true,
          "options": [
            {"value": "technology", "label": "Technology"},
            {"value": "healthcare", "label": "Healthcare"},
            {"value": "finance", "label": "Finance"}
          ],
          "order": 1,
          "visible": true,
          "value": "technology"
        },
        {
          "_id": "6323456789abcdef01234569",
          "type": "number",
          "label": "Number of Founders",
          "help_text": "How many founders does your company have?",
          "required": true,
          "order": 2,
          "visible": true,
          "value": 3,
          "repeat_section_id": "6223456789abcdef01234568",
          "max_repeats": 10
        }
      ],
      "visible": true
    },
    {
      "_id": "6223456789abcdef01234568",
      "title": "Founder Information",
      "description": "Details about each founder",
      "order": 1,
      "repeatable": true,
      "repeat_count": 3,
      "questions": [
        {
          "_id": "6323456789abcdef01234570",
          "type": "short_text",
          "label": "Founder Name",
          "required": true,
          "order": 0,
          "visible": true,
          "repeat_indices": [0, 1, 2]
        },
        {
          "_id": "6323456789abcdef01234571",
          "type": "short_text",
          "label": "Founder Role",
          "required": true,
          "order": 1,
          "visible": true,
          "repeat_indices": [0, 1, 2]
        }
      ],
      "visible": true
    }
  ],
  "dependencies": {
    "6323456789abcdef01234569": {
      "controls_visibility": ["6223456789abcdef01234568"],
      "type": "repeat_section"
    }
  }
}
```

## Working with Repeatable Sections

Repeatable sections are a powerful feature that allows collecting multiple sets of the same information. Here's how to work with them:

### Creating a Form with Repeatable Sections

1. First, create a regular form:

```bash
# Create a form
curl -X POST "http://localhost:8000/api/v1/forms" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Startup Team Form",
    "description": "Form to collect information about startup team members",
    "is_active": true
  }'
```

2. Create a main section for basic information:

```bash
# Create main section
curl -X POST "http://localhost:8000/api/v1/forms/6123456789abcdef01234567/sections" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Basic Information",
    "description": "General information about your startup",
    "order": 0,
    "repeatable": false
  }'
```

3. Create a repeatable section for team members:

```bash
# Create repeatable section
curl -X POST "http://localhost:8000/api/v1/forms/6123456789abcdef01234567/sections" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Team Member",
    "description": "Information about each team member",
    "order": 1,
    "repeatable": true
  }'
```

4. Add a control question to the main section that determines how many times the repeatable section appears:

```bash
# Add control question
curl -X POST "http://localhost:8000/api/v1/forms/sections/6223456789abcdef01234567/questions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "number",
    "label": "Number of Team Members",
    "help_text": "How many team members do you want to add?",
    "required": true,
    "order": 0,
    "repeat_section_id": "6223456789abcdef01234568",
    "max_repeats": 10
  }'
```

5. Add questions to the repeatable section:

```bash
# Add questions to repeatable section
curl -X POST "http://localhost:8000/api/v1/forms/sections/6223456789abcdef01234568/questions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "short_text",
    "label": "Name",
    "required": true,
    "order": 0
  }'
```

```bash
curl -X POST "http://localhost:8000/api/v1/forms/sections/6223456789abcdef01234568/questions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "short_text",
    "label": "Role",
    "required": true,
    "order": 1
  }'
```

### Using the Form Engine with Repeatable Sections

When using the Form Engine API with a form that has repeatable sections, the response will include information about how many times each section should be repeated based on the answers provided.

```bash
# Get form with repeatable sections
curl -X POST "http://localhost:8000/api/v1/forms/6123456789abcdef01234567/engine" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "answers": {
      "6323456789abcdef01234567": 3
    },
    "mode": "full",
    "include_dependencies": true
  }'
```

In the response, you'll see:

1. The repeatable section will have a `repeat_count` property set to the value of the control question (3 in this example)
2. Questions in the repeatable section will have a `repeat_indices` array with indices from 0 to repeat_count-1
3. The `dependencies` object will show which question controls which section

### Submitting a Form with Repeatable Sections

When submitting a form with repeatable sections, you need to include answers for each repeated question with an index suffix:

```bash
# Submit form with repeatable sections
curl -X POST "http://localhost:8000/api/v1/forms/6123456789abcdef01234567/submit" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "answers": {
      "6323456789abcdef01234567": 3,
      "6323456789abcdef01234568_0": "John Doe",
      "6323456789abcdef01234569_0": "CEO",
      "6323456789abcdef01234568_1": "Jane Smith",
      "6323456789abcdef01234569_1": "CTO",
      "6323456789abcdef01234568_2": "Bob Johnson",
      "6323456789abcdef01234569_2": "CFO"
    }
  }'
```

Note the naming convention for repeated questions:
- Original question ID + underscore + index (starting from 0)
- Example: `question_id_0`, `question_id_1`, etc.

## Conditional Visibility

The Form Engine also supports conditional visibility, where questions are shown or hidden based on answers to other questions.

### Creating Questions with Visibility Conditions

```bash
# Add a question with visibility condition
curl -X POST "http://localhost:8000/api/v1/forms/sections/6223456789abcdef01234567/questions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "single_select",
    "label": "Do you have funding?",
    "required": true,
    "options": [
      {"value": "yes", "label": "Yes"},
      {"value": "no", "label": "No"}
    ],
    "order": 1
  }'
```

```bash
# Add a dependent question
curl -X POST "http://localhost:8000/api/v1/forms/sections/6223456789abcdef01234567/questions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "number",
    "label": "How much funding have you raised?",
    "required": true,
    "order": 2,
    "visibility_condition": {
      "operator": "==",
      "conditions": [
        {
          "question_id": "6323456789abcdef01234570",
          "value": "yes"
        }
      ]
    }
  }'
```

### Using the Form Engine with Visibility Conditions

When using the Form Engine API, questions with visibility conditions will only be included in the `visible_questions` array if their conditions are met:

```bash
# Get form with visibility conditions
curl -X POST "http://localhost:8000/api/v1/forms/6123456789abcdef01234567/engine" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "answers": {
      "6323456789abcdef01234570": "yes"
    },
    "mode": "sequential",
    "include_visibility_rules": true
  }'
```

In the response, you'll see:
1. The funding amount question will be included in `visible_questions` because the condition is met
2. If you change the answer to "no", the funding amount question will be excluded

## Complex Example: Repeatable Sections with Conditional Visibility

You can combine repeatable sections with conditional visibility for complex forms:

```bash
# Get a complex form
curl -X POST "http://localhost:8000/api/v1/forms/6123456789abcdef01234567/engine" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "answers": {
      "6323456789abcdef01234567": 2,
      "6323456789abcdef01234570": "yes",
      "6323456789abcdef01234568_0": "John Doe",
      "6323456789abcdef01234569_0": "CEO",
      "6323456789abcdef01234572_0": "yes",
      "6323456789abcdef01234573_0": 5,
      "6323456789abcdef01234568_1": "Jane Smith",
      "6323456789abcdef01234569_1": "CTO",
      "6323456789abcdef01234572_1": "no"
    },
    "mode": "full",
    "include_dependencies": true,
    "include_visibility_rules": true
  }'
```

In this example:
1. We have a form with 2 team members (controlled by question 6323456789abcdef01234567)
2. Each team member has a "Has experience?" question (6323456789abcdef01234572)
3. If "Has experience?" is "yes", a "Years of experience" question (6323456789abcdef01234573) appears
4. The first team member has experience (5 years), so their experience question is visible
5. The second team member doesn't have experience, so their experience question is hidden

The response will include all this information, allowing you to build a dynamic form interface that adapts to user input.

## Best Practices for Frontend Implementation

1. **Progressive Loading**:
   - Load form sections as needed rather than all at once
   - Use the sequential mode for step-by-step forms
   - Cache form structure to improve performance

2. **Repeatable Sections**:
   - Create a component that can be dynamically repeated
   - Use array indices in field names (e.g., `name_0`, `name_1`)
   - Update the UI when the control question's value changes

3. **Conditional Visibility**:
   - Listen for changes to controlling questions
   - Show/hide dependent questions based on answers
   - Validate only visible questions

4. **Validation**:
   - Implement client-side validation matching server rules
   - Show validation errors inline
   - Prevent submission if required fields are empty

5. **Accessibility**:
   - Ensure all form elements have proper labels
   - Use ARIA attributes for dynamic content
   - Test with screen readers
