#!/bin/bash
# Script to watch for changes and restart the backend worker

cd /app
echo "Starting backend worker with auto-reload..."

# Function to run the worker
run_worker() {
    echo "Starting worker process..."
    poetry run python -m app.worker --no-watch &
    WORKER_PID=$!
    echo "Worker started with PID: $WORKER_PID"
}

# Function to handle cleanup
cleanup() {
    echo "Stopping worker process..."
    if [ -n "$WORKER_PID" ]; then
        kill -TERM $WORKER_PID 2>/dev/null
        sleep 2
        # Force kill if still running
        kill -KILL $WORKER_PID 2>/dev/null || true
    fi
    exit 0
}

# Set up trap for cleanup
trap cleanup SIGINT SIGTERM

# Check if watchdog is enabled
if [ "$WATCHDOG_ENABLED" != "1" ]; then
    echo "WATCHDOG_ENABLED is not set to 1, running worker normally..."
    exec poetry run python -m app.worker
fi

# Install inotify-tools if not available (for file watching)
if ! command -v inotifywait >/dev/null 2>&1; then
    echo "Installing inotify-tools for file watching..."
    apt-get update >/dev/null 2>&1 && apt-get install -y inotify-tools >/dev/null 2>&1 || {
        echo "Warning: Could not install inotify-tools, falling back to polling mode"
    }
fi

# Start the worker initially
run_worker

# Watch for changes and restart
while true; do
    echo "Watching for changes in app/ directory..."

    # Use inotifywait if available, otherwise use polling
    if command -v inotifywait >/dev/null 2>&1; then
        # Watch for changes in relevant directories
        inotifywait -r -e modify,create,delete,move \
            --include '\.(py)$' \
            ./app/services/queue/ \
            ./app/services/submission_processing/ \
            ./app/services/deal/ \
            ./app/services/ \
            ./app/services/form/ \
            ./app/services/base.py \
            ./app/worker.py \
            ./app/core/ 2>/dev/null || {
            echo "inotifywait failed, falling back to polling"
            sleep 5
            continue
        }
    else
        # Simple polling as fallback
        sleep 10
        CHANGES=$(find ./app/services/queue/ ./app/services/submission_processing/ ./app/services/deal/ ./app/services/form/ ./app/core/ ./app/worker.py -type f -name "*.py" -newer /tmp/last_check 2>/dev/null || true)
        touch /tmp/last_check

        if [ -z "$CHANGES" ]; then
            continue
        fi
        
        echo "Changes detected in: $CHANGES"
    fi

    echo "Changes detected, restarting worker..."

    # Kill the current worker process gracefully
    if [ -n "$WORKER_PID" ]; then
        echo "Sending SIGTERM to worker PID: $WORKER_PID"
        kill -TERM $WORKER_PID 2>/dev/null || true
        
        # Wait for graceful shutdown
        for i in {1..10}; do
            if ! kill -0 $WORKER_PID 2>/dev/null; then
                echo "Worker shut down gracefully"
                break
            fi
            sleep 1
        done
        
        # Force kill if still running
        if kill -0 $WORKER_PID 2>/dev/null; then
            echo "Force killing worker PID: $WORKER_PID"
            kill -KILL $WORKER_PID 2>/dev/null || true
        fi
        
        wait $WORKER_PID 2>/dev/null || true
    fi

    # Short delay before restarting
    sleep 2

    # Start the worker again
    run_worker
done 