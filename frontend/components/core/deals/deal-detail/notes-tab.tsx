"use client"

import { useState, useEffect, useCallback } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { useAuth } from "@/lib/auth-context"
import { DealDetailData } from "@/lib/types/deal-detail"
import { DealNotesAPI, DealNote } from "@/lib/api/deal-notes-api"
import { NoteCard } from "./notes/note-card"
import { SlateNoteEditor } from '@/components/deal-notes/SlateNoteEditor';
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { EmptyPlaceholder } from "@/components/empty-placeholder"
import { 
  MessageSquare, 
  Loader2, 
  AlertCircle, 
  RefreshCw,
  Pin,
  Users
} from "lucide-react"
import { cn } from "@/lib/utils"
import { toast } from "@/components/ui/use-toast"
import { convertTextToStructuredContent } from "@/lib/utils/mention-converter"
import { NoteContent } from "@/lib/types/deal-notes"
import { useOrgMembers } from '@/lib/hooks/use-org-members';

interface NotesTabProps {
  deal: DealDetailData
  onDealUpdate?: (deal: DealDetailData) => void
}

export function NotesTab({ deal, onDealUpdate }: NotesTabProps) {
  const { user } = useAuth()
  const { members: orgUsers, isLoading: isLoadingUsers } = useOrgMembers();
  const [notes, setNotes] = useState<DealNote[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [hasMore, setHasMore] = useState(false)
  const [skip, setSkip] = useState(0)
  const [showPinnedOnly, setShowPinnedOnly] = useState(false)

  const dealId = deal.id

  // Fetch notes
  const fetchNotes = useCallback(async (reset: boolean = false) => {
    if (!dealId) return

    try {
      setError(null)
      const currentSkip = reset ? 0 : skip
      
      const response = await DealNotesAPI.listNotes(
        dealId,
        currentSkip,
        50, // Load 50 notes at a time
        false // Don't include deleted notes
      )

      if (reset) {
        setNotes(response.notes)
        setSkip(50)
      } else {
        setNotes(prev => [...prev, ...response.notes])
        setSkip(prev => prev + 50)
      }
      
      setHasMore(response.has_more)
    } catch (err: any) {
      console.error('Error fetching notes:', err)
      setError('Failed to load notes. Please try again.')
      toast({
        title: "Error",
        description: "Failed to load notes. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }, [dealId, skip])

  // Initial load
  useEffect(() => {
    fetchNotes(true)
  }, [dealId])

  // Create new note
  const handleCreateNote = async (content: NoteContent, pinned: boolean) => {
    if (!dealId || !content) return;
    try {
      setIsCreating(true);
      await DealNotesAPI.createNote(dealId, {
        structured_content: content,
        pinned,
      });
      await fetchNotes(true);
      toast({ title: 'Note created', description: 'Your note has been added successfully.' });
    } catch (err) {
      toast({ title: 'Error', description: 'Failed to create note. Please try again.', variant: 'destructive' });
    } finally {
      setIsCreating(false);
    }
  };

  // Update note
  const handleUpdateNote = async (noteId: string, content: string, taggedUserIds: string[] = [], mentions: {[key: string]: string} = {}) => {
    if (!dealId || !content.trim()) return

    try {
      // Convert plain text to structured content
      const structuredContent = convertTextToStructuredContent(content.trim(), mentions)
      
      await DealNotesAPI.updateNote(dealId, noteId, {
        structured_content: structuredContent,
        tagged_user_ids: taggedUserIds,
      })

      // Update the note in the local state
      setNotes(prev => prev.map(note => 
        note._id === noteId 
          ? { ...note, structured_content: structuredContent, tagged_user_ids: taggedUserIds, updated_at: Date.now() }
          : note
      ))

      toast({
        title: "Note updated",
        description: "Your note has been updated successfully.",
      })
    } catch (err: any) {
      console.error('Error updating note:', err)
      toast({
        title: "Error",
        description: "Failed to update note. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Delete note
  const handleDeleteNote = async (noteId: string) => {
    if (!dealId) return

    try {
      const response = await DealNotesAPI.deleteNote(dealId, noteId)

      if (response.success) {
        // Remove the note from local state
        setNotes(prev => prev.filter(note => note._id !== noteId))
        
        toast({
          title: "Note deleted",
          description: "Your note has been deleted successfully.",
        })
      }
    } catch (err: any) {
      console.error('Error deleting note:', err)
      toast({
        title: "Error",
        description: "Failed to delete note. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Toggle pin
  const handleTogglePin = async (noteId: string, pinned: boolean) => {
    if (!dealId) return

    try {
      await DealNotesAPI.togglePin(dealId, noteId, pinned)

      // Update the note in the local state
      setNotes(prev => prev.map(note => 
        note._id === noteId 
          ? { ...note, pinned, updated_at: Date.now() }
          : note
      ))

      toast({
        title: pinned ? "Note pinned" : "Note unpinned",
        description: pinned ? "Note has been pinned to the top." : "Note has been unpinned.",
      })
    } catch (err: any) {
      console.error('Error toggling pin:', err)
      toast({
        title: "Error",
        description: "Failed to update pin status. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Load more notes
  const handleLoadMore = () => {
    fetchNotes(false)
  }

  // Filter notes
  const filteredNotes = showPinnedOnly 
    ? notes.filter(note => note.pinned)
    : notes

  // Sort notes: pinned first, then by creation date (newest first)
  const sortedNotes = [...filteredNotes]
    .filter(note => note._id && note._id.trim() !== '') // Filter out notes without valid IDs
    .sort((a, b) => {
      if (a.pinned && !b.pinned) return -1
      if (!a.pinned && b.pinned) return 1
      return b.created_at - a.created_at
    })

  // Debug: Log notes to check for missing IDs
  console.log('Notes to render:', sortedNotes.map(note => ({
    _id: note._id,
    structured_content: note.structured_content,
    hasStructuredContent: !!note.structured_content,
    structuredContentLength: note.structured_content?.length || 0,
    hasId: !!note._id,
    idLength: note._id?.length || 0
  })))

  if (loading && notes.length === 0) {
    return (
      <div className="flex h-full flex-col space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <MessageSquare className="size-5 text-gray-500" />
            <h2 className="text-lg font-semibold">Notes</h2>
            <Skeleton className="h-6 w-12 rounded-full" />
          </div>
          <Skeleton className="h-9 w-24 rounded-lg" />
        </div>

        {/* Loading skeleton */}
        <div className="flex-1 space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="rounded-xl border border-gray-200 bg-white p-4">
              <div className="flex items-start gap-3">
                <Skeleton className="size-8 rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (error && notes.length === 0) {
    return (
      <div className="flex h-full flex-col space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <MessageSquare className="size-5 text-gray-500" />
            <h2 className="text-lg font-semibold">Notes</h2>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="size-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>

        <Button onClick={() => fetchNotes(true)} variant="outline" className="w-fit">
          <RefreshCw className="mr-2 size-4" />
          Try Again
        </Button>
      </div>
    )
  }

  if (isLoadingUsers || !orgUsers || orgUsers.length === 0) {
    return <Skeleton className="h-32 w-full" />;
  }

  return (
    <div className="flex h-full flex-col">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 pb-4">
        <div className="flex items-center gap-3">
          <MessageSquare className="size-5 text-gray-500" />
          <h2 className="text-lg font-semibold">Notes</h2>
          <span className="rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600">
            {notes.length}
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant={showPinnedOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setShowPinnedOnly(!showPinnedOnly)}
            className="gap-2"
          >
            <Pin className="size-4" />
            {showPinnedOnly ? "Show All" : "Pinned Only"}
          </Button>
        </div>
      </div>

      {/* Notes List */}
      <div className="flex-1 space-y-4 overflow-y-auto py-4 px-2 md:px-8">
        <AnimatePresence mode="popLayout">
          {sortedNotes.length === 0 ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex h-full items-center justify-center"
            >
              <EmptyPlaceholder>
                <EmptyPlaceholder.Icon name="messageSquare" />
                <EmptyPlaceholder.Title>
                  {showPinnedOnly ? "No pinned notes" : "No notes yet"}
                </EmptyPlaceholder.Title>
                <EmptyPlaceholder.Description>
                  {showPinnedOnly 
                    ? "Pin important notes to keep them at the top."
                    : "Start the conversation by adding your first note below."
                  }
                </EmptyPlaceholder.Description>
              </EmptyPlaceholder>
            </motion.div>
          ) : (
            sortedNotes.map((note, index) => (
              <motion.div
                key={note._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ 
                  duration: 0.3,
                  delay: index * 0.05 // Stagger animation
                }}
              >
                <NoteCard
                  note={note}
                  currentUserId={user?.id}
                  onUpdate={handleUpdateNote}
                  onDelete={handleDeleteNote}
                  onTogglePin={handleTogglePin}
                  orgUsers={orgUsers}
                />
              </motion.div>
            ))
          )}
        </AnimatePresence>

        {/* Load More Button */}
        {hasMore && (
          <div className="flex justify-center pt-4">
            <Button
              variant="outline"
              onClick={handleLoadMore}
              disabled={loading}
              className="gap-2"
            >
              {loading ? (
                <Loader2 className="size-4 animate-spin" />
              ) : (
                <RefreshCw className="size-4" />
              )}
              Load More
            </Button>
          </div>
        )}
      </div>

      {/* Input Composer */}
      <div className="border-t border-gray-200 pt-4">
        {orgUsers && orgUsers.length > 0 ? (
          <SlateNoteEditor orgUsers={orgUsers} onSubmit={handleCreateNote} />
        ) : (
          <Skeleton className="h-32 w-full" />
        )}
      </div>
    </div>
  )
} 