from typing import List, Optional, Dict, Tuple
import logging

from bson import ObjectId
from fastapi import FastAPI

from app.models.role import Role, Permission
from app.core.errors import (
    DatabaseError,
    handle_error
)
from app.core.database import get_database
from app.services.base import BaseService
from app.services.rbac.interfaces import RBACServiceInterface
from app.core.cache import cache
from app.utils.rbac.rbac import get_resources_and_actions

logger = logging.getLogger(__name__)


class RBACService(BaseService, RBACServiceInterface):
    """
    MongoDB implementation of RBACService.
    Handles permission checking and user-role relationships.
    If request.state.pass_through is True, all permission checks are bypassed.
    """

    def __init__(self, db=None, app: Optional[FastAPI] = None):
        super().__init__()
        self.db = db
        self._cache_ttl = 300  # 5 minutes
        self._app = app
        self._resources = None
        self._actions = None

    async def initialize(self) -> None:
        """Initialize database connection."""
        if self.db is None:
            async for db in get_database():
                self.db = db
                break

        # Initialize resources and actions if app is provided
        if self._app:
            resource_map = get_resources_and_actions(self._app)
            self._resources = sorted(
                {permission["resource"] for permission in resource_map if permission["resource"]})
            self._actions = sorted(
                {permission["action"] for permission in resource_map if permission["action"]})

    async def cleanup(self) -> None:
        """Cleanup database connection."""
        pass

    async def get_user_role(self, user_id: str) -> Optional[Tuple[Role, bool]]:
        """Get user's role with permissions."""
        logger.info(f"Getting role for user: {user_id}")
        try:
            # Check cache first
            cache_key = f"user_role:{user_id}"
            cached_role = await cache.get(cache_key)
            if cached_role:
                return Role(**cached_role["role"]), cached_role["is_superuser"]

            user = await self.db.users.find_one({"_id": ObjectId(user_id)})
            if not user:
                logger.warning(f"User not found: {user_id}")
                return None

            # Check if user is superuser first
            is_superuser = user.get("is_superuser", False)
            if is_superuser:
                logger.info(
                    f"User {user_id} is superuser, granting full access")
                # Create a role with all permissions for superuser
                superuser_role = Role(
                    name="Superuser",
                    id=ObjectId(),
                    description="Superuser role with full access",
                    permissions=[],  # Empty list as superuser bypasses permission checks
                    is_system=True
                )
                # Cache the result
                await cache.set(
                    cache_key,
                    {"role": superuser_role.dict(), "is_superuser": True},
                    ttl=self._cache_ttl
                )
                return superuser_role, True

            logger.info(f"Found user, checking role_id: {user.get('role_id')}")
            if not user.get("role_id"):
                logger.warning(f"No role assigned to user: {user_id}")
                return None

            role = await self.db.roles.find_one({"_id": user["role_id"]})
            if not role:
                logger.warning(f"Role not found for user: {user_id}")
                return None

            logger.info(f"Found role for user: {user_id}")
            role_obj = Role(**role)
            # Cache the result
            await cache.set(
                cache_key,
                {"role": role_obj.dict(), "is_superuser": False},
                ttl=self._cache_ttl
            )
            return role_obj, False
        except Exception as e:
            error = DatabaseError(
                message="Error retrieving user role",
                details={"user_id": str(user_id), "error": str(e)},
                should_alert=True
            )
            await handle_error(error)
            raise error.to_http_exception()

    async def check_permission(self, user_id: str, resource: str, action: str, request=None) -> bool:
        """Check if user has permission for a specific resource and action."""
        # Short-circuit if pass_through is enabled for this org
        if request is not None and getattr(request.state, "pass_through", False):
            return True

        try:
            # Check cache first
            cache_key = f"permission:{user_id}:{resource}:{action}"
            cached_permission = await cache.get(cache_key)
            if cached_permission is not None:
                return cached_permission

            user = await self.db.users.find_one({"_id": ObjectId(user_id)})
            if not user or not user.get("role_id"):
                await cache.set(cache_key, False, ttl=self._cache_ttl)
                return False

            role = await self.db.roles.find_one({"_id": user["role_id"]})
            if not role:
                await cache.set(cache_key, False, ttl=self._cache_ttl)
                return False

            # Check if role has the permission
            permission = await self.db.permissions.find_one({
                "role_id": role["_id"],
                "resource": resource,
                "action": action
            })

            has_permission = bool(permission)
            # Cache the result
            await cache.set(cache_key, has_permission, ttl=self._cache_ttl)
            return has_permission
        except Exception as e:
            await self.handle_error(e, {
                "user_id": user_id,
                "resource": resource,
                "action": action
            })

    async def assign_role(self, user_id: str, role_id: str) -> bool:
        """Assign a role to a user."""
        logger.info(f"Assigning role {role_id} to user {user_id}")
        try:
            result = await self.db.users.update_one(
                {"_id": ObjectId(user_id)},
                {"$set": {"role_id": ObjectId(role_id)}}
            )
            success = result.modified_count > 0
            logger.info(
                f"Role assignment {'successful' if success else 'failed'}")

            # Invalidate user role cache
            await cache.delete(f"user_role:{user_id}")
            return success
        except Exception as e:
            error = DatabaseError(
                message="Error assigning role",
                details={
                    "user_id": str(user_id),
                    "role_id": str(role_id),
                    "error": str(e)
                },
                should_alert=True
            )
            await handle_error(error)
            raise error.to_http_exception()

    async def get_user_permissions(self, user_id: str) -> List[Dict]:
        """Get all permissions for a user."""
        try:
            # Convert string to ObjectId
            user_object_id = ObjectId(user_id)
            role, is_superuser = await self.get_user_role(user_object_id)
            if not role:
                return []

            if is_superuser:
                # Superuser has all permissions
                resources = await self._resources
                actions = await self._actions
                return [
                    {"resource": resource, "action": action}
                    for resource in resources
                    for action in actions
                ]

            return [
                {"resource": p.resource, "action": p.action}
                for p in role.permissions
            ]
        except Exception as e:
            error = DatabaseError(
                message="Error getting user permissions",
                details={"user_id": user_id, "error": str(e)},
                should_alert=True
            )
            await handle_error(error)
            raise error.to_http_exception()

    async def create_role(
        self,
        name: str,
        description: str,
        permissions: List[Permission],
        is_system: bool = False
    ) -> Role:
        """Create a new role."""
        logger.info(f"Creating new role: {name}")
        try:
            role = Role(
                name=name,
                description=description,
                permissions=permissions,
                is_system=is_system
            )
            result = await self.db.roles.insert_one(role.dict(by_alias=True))
            role.id = result.inserted_id
            logger.info(f"Role created with ID: {role.id}")
            return role
        except Exception as e:
            error = DatabaseError(
                message="Error creating role",
                details={
                    "name": name,
                    "description": description,
                    "is_system": is_system,
                    "error": str(e)
                },
                should_alert=True
            )
            await handle_error(error)
            raise error.to_http_exception()

    async def update_role(
        self,
        role_id: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        permissions: Optional[List[Permission]] = None
    ) -> Optional[Role]:
        """Update an existing role."""
        logger.info(f"Updating role: {role_id}")
        try:
            update_data = {}
            if name:
                update_data["name"] = name
            if description:
                update_data["description"] = description
            if permissions:
                update_data["permissions"] = [p.dict() for p in permissions]

            if not update_data:
                logger.warning("No update data provided")
                return None

            result = await self.db.roles.find_one_and_update(
                {"_id": ObjectId(role_id)},
                {"$set": update_data},
                return_document=True
            )

            if not result:
                logger.warning(f"Role not found: {role_id}")
                return None

            logger.info(f"Role updated successfully: {role_id}")
            role = Role(**result)
            return role
        except Exception as e:
            error = DatabaseError(
                message="Error updating role",
                details={
                    "role_id": role_id,
                    "update_data": update_data,
                    "error": str(e)
                },
                should_alert=True
            )
            await handle_error(error)
            raise error.to_http_exception()

    async def get_system_roles(self) -> List[Role]:
        """Get all system roles."""
        logger.info("Fetching system roles")
        try:
            cursor = self.db.roles.find({"is_system": True})
            roles = await cursor.to_list(length=None)
            logger.info(f"Found {len(roles)} system roles")
            return [Role(**role) for role in roles]
        except Exception as e:
            error = DatabaseError(
                message="Error fetching system roles",
                details={"error": str(e)},
                should_alert=True
            )
            await handle_error(error)
            raise error.to_http_exception()

    async def get_all_roles(self) -> List[Dict]:
        """Get all roles in the system."""
        try:
            roles = await self.db.roles.find().to_list(length=None)
            return [
                {
                    "id": str(r["_id"]),
                    "name": r["name"],
                    "description": r.get("description", ""),
                    "is_system": r.get("is_system", False)
                }
                for r in roles
            ]
        except Exception as e:
            error = DatabaseError(
                message="Error fetching all roles",
                details={"error": str(e)},
                should_alert=True
            )
            await handle_error(error)
            raise error.to_http_exception()

    async def get_role_permissions(self, role_id: str) -> List[Dict]:
        """Get all permissions for a specific role."""
        try:
            permissions = await self.db.permissions.find({
                "role_id": ObjectId(role_id)
            }).to_list(length=None)

            return [
                {
                    "resource": p["resource"],
                    "action": p["action"]
                }
                for p in permissions
            ]
        except Exception as e:
            error = DatabaseError(
                message="Error fetching role permissions",
                details={"role_id": role_id, "error": str(e)},
                should_alert=True
            )
            await handle_error(error)
            raise error.to_http_exception()
