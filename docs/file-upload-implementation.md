# File Upload & Download Implementation

This document describes the implementation of per-question file upload functionality for TractionX form submissions, following the PRD specifications.

## Architecture Overview

### Core Flow
1. **User Context**: Startup founder accessing form via `/share/[token]`
2. **Authentication**: User has `submissionId` from backend after magic link login
3. **File Storage**: Files stored in S3 under `submissions/{submissionId}/files/{userFilename}`
4. **Answer Integration**: S3 keys included in form answers for backend processing

## Backend Implementation

### Models (`backend/app/models/file.py`)
- **SubmissionFile**: Tracks file metadata, S3 location, access control
- **FileAccessLog**: Audit trail for file access events
- **FileStatus**: Enum for file processing states
- **FileAccessType**: User type classification

### Services (`backend/app/services/file/`)
- **FileServiceInterface**: Abstract interface for file operations
- **FileService**: S3-based implementation with presigned URLs
- **Key Features**:
  - Presigned URL generation (upload/download)
  - File validation against question constraints
  - Access control and audit logging
  - Orphaned file cleanup

### API Endpoints (`backend/app/api/v1/public_file.py`)
- `POST /api/v1/public/file/presign-upload` - Generate upload URL
- `POST /api/v1/public/file/confirm-upload` - Confirm upload completion
- `POST /api/v1/public/file/presign-download` - Generate download URL
- `DELETE /api/v1/public/file/{fileId}` - Delete file (soft delete)
- `GET /api/v1/public/file/submission/{submissionId}/files` - List files

### Configuration
Add to `.env`:
```bash
# S3 File Storage
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
S3_BUCKET_SUBMISSIONS=tractionx-submissions
S3_PRESIGNED_URL_EXPIRY=600

# File Upload Limits
MAX_FILE_SIZE_MB=50
ALLOWED_FILE_EXTENSIONS=.pdf,.doc,.docx,.txt,.rtf,.jpg,.jpeg,.png,.gif,.bmp,.xls,.xlsx,.csv,.ppt,.pptx,.zip,.rar,.7z
```

## Frontend Implementation

### API Client (`frontend/lib/api/file.ts`)
- **FileAPI**: Complete client for file operations
- **Methods**:
  - `generateUploadUrl()` - Get presigned upload URL
  - `uploadFile()` - Direct S3 upload with progress
  - `confirmUpload()` - Confirm upload completion
  - `generateDownloadUrl()` - Get presigned download URL
  - `deleteFile()` - Delete file
  - `uploadFileComplete()` - Complete upload flow

### Components

#### FileUpload (`frontend/components/core/form-share/file-upload.tsx`)
- **Purpose**: Interactive file upload for form questions
- **Features**:
  - Drag & drop support
  - Upload progress tracking
  - File validation
  - Error handling
  - Real-time S3 upload

#### FileDisplay (`frontend/components/core/form-share/file-display.tsx`)
- **Purpose**: Display uploaded files with download/preview
- **Features**:
  - File type icons
  - Download/preview buttons
  - File metadata display
  - Edit/view mode support

#### QuestionRenderer Updates
- **File Question Support**: Integrated FileUpload component
- **Mode Support**: Edit vs view mode handling
- **Props**: Added `submissionId` and `accessToken`

### Deal Page Integration (`frontend/components/core/deal/submission-files.tsx`)
- **SubmissionFiles**: Display files for single submission
- **SubmissionFilesGrid**: Display files across multiple submissions
- **FileStatistics**: File upload statistics dashboard

## Security & Privacy

### Access Control
- **Upload**: Requires valid `submissionId` and authentication
- **Download**: Permission checks based on user type and submission status
- **Investors**: Can only access files from `SUBMITTED` submissions
- **Users**: Can access their own files regardless of status

### URL Security
- **Presigned URLs**: Short-lived (10 minutes default)
- **No Exposure**: S3 keys/URLs never stored in frontend
- **Audit Trail**: All access attempts logged

### File Validation
- **Size Limits**: Configurable per system and per question
- **Type Restrictions**: Based on question validation schema
- **Virus Scanning**: Placeholder for future implementation

## Usage Examples

### Form Question Configuration
```typescript
// Question with file upload
{
  type: "file",
  label: "Upload your pitch deck",
  validation: {
    max: 25, // 25MB limit
    regex: ".pdf,.ppt,.pptx" // Allowed types
  }
}
```

### Form Answer Structure
```typescript
// File answer stored in form submission
{
  questionId: {
    file: "submissions/sub123/files/pitch_deck.pdf", // S3 key
    filename: "pitch_deck.pdf",
    size: 2048576,
    type: "application/pdf",
    uploaded_at: "2024-01-15T10:30:00Z"
  }
}
```

### Component Usage
```tsx
// In form rendering
<QuestionRenderer
  question={question}
  value={answers[questionId]}
  onChange={handleAnswerChange}
  submissionId={submission.id}
  accessToken={user.accessToken}
  mode="edit"
/>

// In deal page
<SubmissionFiles
  submission={submission}
  form={form}
  accessToken={investorToken}
  mode="view"
/>
```

## Error Handling

### Upload Errors
- **Validation Failures**: File size/type restrictions
- **Network Issues**: Retry mechanisms and user feedback
- **S3 Errors**: Graceful degradation with error messages

### Download Errors
- **Expired Links**: Automatic refresh and retry
- **Permission Denied**: Clear error messages
- **File Not Found**: Fallback handling

### Edge Cases
- **Submission Status Changes**: Disable editing when locked
- **Token Expiry**: Automatic refresh with fallback
- **Orphaned Files**: Background cleanup process

## Testing

### Backend Tests
- File service unit tests
- API endpoint integration tests
- Access control validation
- S3 integration tests

### Frontend Tests
- Component rendering tests
- File upload flow tests
- Error handling tests
- Accessibility tests

## Deployment Considerations

### S3 Setup
1. Create S3 bucket with appropriate permissions
2. Configure CORS for direct uploads
3. Set up lifecycle policies for cleanup
4. Enable versioning if needed

### Environment Variables
- Ensure all S3 credentials are properly configured
- Set appropriate file size limits
- Configure allowed file extensions

### Monitoring
- Track file upload success rates
- Monitor S3 storage usage
- Log access patterns for security

## Future Enhancements

### Planned Features
- **Virus Scanning**: Integration with antivirus services
- **Image Processing**: Thumbnail generation and optimization
- **Bulk Operations**: Multi-file upload and download
- **File Versioning**: Track file changes over time

### Performance Optimizations
- **CDN Integration**: CloudFront for faster downloads
- **Compression**: Automatic file compression
- **Caching**: Intelligent caching strategies
- **Background Processing**: Async file processing
