# TractionX RQ/Worker Architecture Documentation

## Overview

TractionX uses a custom queue system built on top of Redis for asynchronous job processing. The system supports both the main backend application and a separate data pipelines service, each with their own worker implementations and job types.

## 1. RQ/Queue Architecture

### Queue Initialization & Configuration

**Main Backend Queue Service** (`backend/app/services/queue/`)
- **Primary Configuration**: `backend/app/services/queue/redis_queue.py`
- **Backend Implementation**: `backend/app/services/queue/backends/redis_backend.py`
- **Factory Pattern**: `backend/app/services/queue/factory.py`

```python
# Queue service initialization
queue_service = RedisQueueService()
await queue_service.initialize()

# Backend configuration
backend = RedisQueueBackend(
    redis_url=settings.REDIS_URL,
    namespace="txqueue",
    job_expiration=86400 * 7,  # 7 days
    processing_timeout=1800    # 30 minutes
)
```

**Data Pipelines Queue Service** (`datapipelines/app/worker.py`)
- **Configuration**: Uses vanilla RQ with Redis connection
- **Queue Names**: `['default']`
- **Redis Connection**: Direct Redis client from settings

```python
# Data pipelines worker setup
redis_conn = Redis.from_url(settings.redis_connection_string, decode_responses=True)
worker = rq.Worker(['default'], connection=redis_conn)
```

### Redis Setup

**Backend Redis Configuration** (`backend/app/core/config.py`)
```python
REDIS_HOST: str = "localhost"
REDIS_PORT: int = 6379
REDIS_DB: int = 0
REDIS_PASSWORD: Optional[str] = None
REDIS_URL: str = "redis://localhost:6379/0"
REDIS_KEY_PREFIX: str = "tx"
```

**Data Pipelines Redis Configuration** (`datapipelines/app/configs/settings.py`)
```python
REDIS_HOST: Optional[str]
REDIS_PORT: Optional[int]
REDIS_USERNAME: Optional[str]
REDIS_DB: Optional[str]
REDIS_ENDPOINT: Optional[str] = None
REDIS_PASSWORD: Optional[str] = None
REDIS_KEY_PREFIX: str = "txpipeline"
```

### Queue Types & Priorities

**Backend Queue Types** (`backend/app/models/queue.py`)
```python
class QueueType(str, Enum):
    DEFAULT = "default"
    HIGH_PRIORITY = "high_priority"
    LOW_PRIORITY = "low_priority"
    SCHEDULED = "scheduled"

class JobPriority(str, Enum):
    HIGH = "high"
    NORMAL = "normal"
    LOW = "low"
```

**Data Pipelines**: Uses single `default` queue with vanilla RQ

### Job Meta Fields

**Backend Job Structure** (`backend/app/models/queue.py`)
```python
class Job(BaseModel):
    id: str
    type: str
    payload: Dict[str, Any]
    queue: QueueType = QueueType.DEFAULT
    priority: JobPriority = JobPriority.NORMAL
    status: JobStatus = JobStatus.PENDING
    created_at: int
    updated_at: int
    scheduled_for: Optional[int] = None
    max_attempts: int = 3
    attempt_count: int = 0
    retry_delay: int = 60
    metadata: JobMetadata = Field(default_factory=JobMetadata)
```

**Data Pipelines**: Uses standard RQ job structures

## 2. Worker Logic & Structure

### Worker Startup

**Backend Workers** (`backend/app/worker.py`)
```bash
# Docker Compose
docker-compose up worker

# Manual
python -m app.worker

# Worker pool configuration
worker_pool = QueueFactory.create_worker_pool(
    queue_service=queue_service,
    queue_types=[QueueType.DEFAULT, QueueType.HIGH_PRIORITY],
    poll_interval=5.0,
    shutdown_timeout=60
)
```

**Data Pipelines Workers** (`datapipelines/app/worker.py`)
```bash
# Docker Compose
docker-compose up datapipeline-worker

# Manual
python worker.py

# RQ worker configuration
worker = rq.Worker(['default'], connection=redis_conn)
worker.work()
```

### Task Registration

**Backend Handler Registration** (`backend/app/services/queue/handlers/__init__.py`)
```python
# Handler registry pattern
HANDLERS: Dict[str, Union[JobHandlerInterface, Callable]] = {
    "process_resource_event": process_resource_event,
    "ai_summary_job": process_ai_summary,
    "ai_analysis_job": process_ai_summary,
    "generic_job": process_generic_job
}

# Module-based registration
worker.register_module('app.services.queue.handlers.form_submission')
```

**Data Pipelines Task Registration** (`datapipelines/app/tasks/`)
- **Company Enrichment**: `enrich_company_data()`
- **Founder Enrichment**: `enrich_founder_data()`
- **News Aggregation**: Not yet implemented
- **Embedding Generation**: Not yet implemented

### Job Dependencies

**Backend**: No built-in job dependencies - orchestration handled at application level
**Data Pipelines**: No job dependencies - uses simple task execution

## 3. Codebase Structure

### Queue Enqueueing Locations

**Public Submission Flow** (`backend/app/api/v1/public.py`)
```python
# Public submission endpoint - CURRENT IMPLEMENTATION
@router.post("/submission/{submission_id}/submit")
async def submit_public_submission(
    submission_id: str,
    sharing_service: SharingServiceInterface = Depends(get_sharing_service),
    form_service: FormServiceInterface = Depends(get_form_service),
    public_submission_service: PublicSubmissionService = Depends(get_public_submission_service),
) -> Dict[str, Any]:
    """
    Submit a public submission (convert from DRAFT to SUBMITTED).

    Current Flow:
    1. Get public submission by ID
    2. Validate sharing token and form
    3. Create actual Submission object
    4. Update public submission status to SUBMITTED
    5. NO QUEUE JOBS ENQUEUED (yet)

    Future Enhancement:
    - Add queue job enqueueing for AI processing, enrichment, etc.
    """
    # Get the public submission
    public_submission = await public_submission_service.get_submission_by_id(submission_id)

    # Create the actual submission
    submission = Submission(
        org_id=str(public_submission.org_id),
        form_id=str(public_submission.form_id),
        answers=public_submission.answers,
        metadata={
            "submitted_via": "shared_form",
            "sharing_token": public_submission.sharing_token,
            "public_user_email": public_submission.public_user_email,
            "public_submission_id": str(public_submission.id),
        },
    )

    # Update the public submission to mark as submitted
    await public_submission_service.finalize_submission(
        public_submission_id=submission_id,
        final_submission_id=str(submission.id)
    )

    # TODO: Add queue job enqueueing here
    # await queue_service.enqueue_job(
    #     job_type="process_public_submission",
    #     payload={
    #         "submission_id": str(submission.id),
    #         "public_submission_id": submission_id,
    #         "org_id": str(submission.org_id),
    #         "form_id": str(submission.form_id),
    #         "answers": submission.answers
    #     }
    # )

    return {
        "id": str(submission.id),
        "status": "submitted",
        "message": "Submission completed successfully",
        "public_submission_id": submission_id,
    }
```

**Forms API Submission** (`backend/app/api/v1/forms.py`)
```python
@router.api_route("/{form_id}/submit", methods=["POST", "PUT"])
async def submit_form(
    form_id: str,
    request: Union[SubmitFormRequest, UpdateSubmissionRequest],
    http_request: Request,
    org_context: tuple[str, bool] = Depends(get_org_context),
    form_service: FormServiceInterface = Depends(get_form_service),
    queue_service = Depends(get_queue_service),
    job_service = Depends(get_job_service)
) -> Submission:
    """
    Submit a new form or update an existing submission.

    Current Flow:
    1. Validate form and organization context
    2. Create or update submission
    3. Uses job_service for background processing (not queue_service directly)

    Note: This endpoint is for authenticated organization users,
    different from public submission flow.
    """
    # Form validation and submission logic
    # ...

    # Background job enqueueing (current implementation uses job_service)
    # TODO: Migrate to queue_service for consistency
```

### Worker Implementation Locations

**Backend Workers** (`backend/app/services/queue/`)
```
├── handlers/
│   ├── __init__.py           # Handler registry
│   ├── base.py              # Base handler class
│   ├── form_submission.py   # Form submission handlers
│   ├── generic_job.py       # Generic job processor
│   ├── job_tracking.py      # AI processing handlers
│   └── qualifier_form.py    # Resource event processor
├── backends/
│   └── redis_backend.py     # Redis queue backend
├── worker.py                # Individual worker
├── worker_pool.py           # Worker pool management
└── interfaces.py            # Queue interfaces
```

**Data Pipelines Workers** (`datapipelines/app/`)
```
├── tasks/
│   ├── company_enrichment.py    # Clay API integration
│   ├── founder_enrichment.py    # PDL API integration (placeholder)
│   └── news_aggregation.py      # Not yet implemented
├── pipelines/
│   ├── company.py               # Company enrichment pipeline
│   ├── founder.py               # Founder enrichment pipeline
│   └── base.py                  # Base pipeline class
└── worker.py                    # RQ worker startup
```

### Logging & Auditing

**Backend**: Uses structured logging with `app.core.logging`
**Data Pipelines**: Uses structured logging with `app.configs.get_logger`

Both systems log to stdout/stderr with JSON formatting for production.

## 4. Error/Retry Handling

### Backend Error Handling

**Built-in Retry System** (`backend/app/services/queue/worker.py`)
```python
async def _fail_job(
    self,
    job: Job,
    error: Union[str, Exception],
    retry: bool = True,
    error_context: Optional[Dict[str, Any]] = None
) -> None:
    # Centralized error handling with retry logic
    await backend.fail(job.id, error_message, retry=retry)
```

**Retry Configuration**
```python
job = await queue_service.enqueue_job(
    job_type="process_form_submission",
    payload=payload,
    retry_config={
        "max_attempts": 5,
        "backoff_factor": 2
    }
)
```

### Data Pipelines Error Handling

**RQ Built-in Retry**: Uses standard RQ failed job system
**Custom Logic**: Pipeline-specific error handling in task functions

```python
# Data pipelines retry configuration
MAX_RETRIES: int = 3
RETRY_BACKOFF_FACTOR: int = 2
WORKER_TIMEOUT: int = 1800  # 30 minutes
```

### Error Reporting

**Backend**: Structured logging with error context
**Data Pipelines**: Structured logging + optional Sentry integration

```python
# Sentry configuration (data pipelines)
SENTRY_DSN: Optional[str] = None
```

## 5. Task State & Results

### Backend State Storage

**Redis Storage**: Job metadata and status stored in Redis
**MongoDB**: Final results may be stored in MongoDB collections
**State Keys**: `{namespace}:job:{job_id}` pattern

### Data Pipelines State Storage

**RDS Storage**: Results stored in PostgreSQL via `app.storage.rds_storage`
**S3 Storage**: Large data artifacts stored in S3
**Qdrant**: Vector embeddings stored in Qdrant vector database

### Results Distribution

**Backend**: Results available via queue service API
**Data Pipelines**: Results pushed to backend via API integration

```python
# Backend integration (data pipelines)
BACKEND_API_URL: str = "http://localhost:8000/api/v1"
BACKEND_API_KEY: Optional[str] = None
```

## 6. Integration Points

### External Service Credentials

**Data Pipelines External APIs** (`datapipelines/app/configs/settings.py`)
```python
# Clay (Company Enrichment)
CLAY_API_KEY: Optional[str] = None
CLAY_BASE_URL: str = "https://api.clay.com/v1"

# People Data Labs (Founder Enrichment)
PDL_API_KEY: Optional[str] = None
PDL_BASE_URL: str = "https://api.peopledatalabs.com/v5"

# News APIs
NEWSAPI_KEY: Optional[str] = None
GNEWS_KEY: str
SERPAPI_KEY: str

# AI Services
OPENAI_API_KEY: Optional[str] = None
OPENAI_EMBEDDING_MODEL: str = "text-embedding-3-small"
OPENAI_MODEL: str
```

**Backend External APIs** (`backend/app/core/config.py`)
```python
# Email Services
RESEND_API_KEY: str
SMTP_* settings

# File Storage
AWS_ACCESS_KEY_ID: Optional[str] = None
AWS_SECRET_ACCESS_KEY: Optional[str] = None
S3_BUCKET_SUBMISSIONS: str = "tractionx-submissions"
```

### Service Call Patterns

**Data Pipelines**: Synchronous external API calls within workers
**Backend**: Mixed sync/async patterns depending on service

### Webhook Support

**Data Pipelines Webhooks** (`datapipelines/app/configs/settings.py`)
```python
WEBHOOK_SECRET: Optional[str] = None
CLAY_WEBHOOK_SECRET: Optional[str] = None
```

## 7. Available Tasks & Naming Conventions

### Backend Job Types

**Current Handlers** (`backend/app/services/queue/handlers/__init__.py`)
- `process_resource_event` - Resource event processing
- `ai_summary_job` - AI summary generation
- `ai_analysis_job` - AI analysis processing
- `generic_job` - Generic job processor

### Data Pipelines Job Types

**Company Enrichment** (`datapipelines/app/tasks/company_enrichment.py`)
- `enrich_company_data` - Clay API company enrichment

**Founder Enrichment** (`datapipelines/app/tasks/founder_enrichment.py`)
- `enrich_founder_data` - PDL API founder enrichment (placeholder)

### Queue Naming Conventions

**Backend Queues**:
- `txqueue:queue:default`
- `txqueue:queue:high_priority`
- `txqueue:queue:low_priority`
- `txqueue:queue:scheduled`

**Data Pipelines Queues**:
- `default` (RQ standard)

### Job ID Patterns

**Backend**: UUID4 format
**Data Pipelines**: RQ-generated job IDs

## Missing Components

### Not Yet Implemented

1. **RQ Scheduler**: Neither backend nor data pipelines use RQ Scheduler
2. **Job Dependencies**: No built-in job chaining/dependencies
3. **Comprehensive Error Reporting**: No Sentry integration in backend
4. **Advanced Monitoring**: Basic logging only, no metrics/dashboards
5. **Data Pipeline Tasks**: News aggregation and embedding generation not implemented
6. **LinkedIn Scraper**: Not implemented
7. **Advanced Queue Management**: No queue prioritization or throttling
8. **Public Submission Queue Integration**: Public submissions don't trigger queue jobs yet

### Current Limitations

1. **Dual Queue Systems**: Backend uses custom queue system, data pipelines use vanilla RQ
2. **No Cross-Service Job Coordination**: Backend and data pipelines operate independently
3. **Limited Job Visibility**: No unified dashboard for monitoring all job types
4. **Manual Worker Management**: No auto-scaling or dynamic worker allocation
5. **Basic Error Recovery**: Limited retry strategies and error handling

### Integration Gaps

1. **Public Submission Processing**: No automatic AI processing or enrichment after public form submission
2. **File Processing**: Uploaded files not automatically processed for content extraction
3. **Real-time Updates**: No real-time status updates for long-running jobs
4. **Cross-Service Communication**: Limited communication between backend and data pipeline workers

## Future Enhancements

### Immediate Priorities

1. **Public Submission Queue Integration**: Add job enqueueing to public submission flow
2. **File Processing Pipeline**: Automatic processing of uploaded files (PDF extraction, etc.)
3. **Unified Error Handling**: Consistent error reporting across all services
4. **Basic Monitoring**: Queue depth and job status monitoring

### Medium-term Goals

1. **Queue Monitoring Dashboard**: Real-time queue status and job monitoring
2. **Advanced Retry Logic**: Exponential backoff, circuit breakers
3. **Job Dependencies**: Task orchestration and workflow management
4. **Performance Metrics**: Queue throughput, job duration analytics

### Long-term Vision

1. **Auto-scaling**: Dynamic worker scaling based on queue depth
2. **Unified Queue System**: Single queue system across all services
3. **Advanced Orchestration**: Complex workflow management and job dependencies
4. **Real-time Processing**: Stream processing for real-time data updates
5. **ML Pipeline Integration**: Automated model training and inference pipelines

## Deployment & Operations

### Current Deployment

**Backend Workers**
```bash
# Docker Compose
docker-compose up worker

# Production (example)
docker run -d --name tx-worker \
  -e REDIS_URL=redis://redis:6379/0 \
  -e MONGODB_URL=mongodb://mongo:27017/tractionx \
  tractionx/backend:latest python -m app.worker
```

**Data Pipeline Workers**
```bash
# Docker Compose
docker-compose up datapipeline-worker

# Production (example)
docker run -d --name tx-pipeline-worker \
  -e REDIS_HOST=redis \
  -e REDIS_PORT=6379 \
  tractionx/datapipelines:latest python worker.py
```

### Monitoring & Health Checks

**Current Status**: Basic logging to stdout/stderr
**Recommended**: Add health check endpoints and metrics collection

```python
# Proposed health check endpoints
GET /api/v1/queue/health
GET /api/v1/queue/stats
GET /api/v1/workers/status
```

### Scaling Considerations

1. **Redis Scaling**: Consider Redis Cluster for high availability
2. **Worker Scaling**: Horizontal scaling based on queue depth
3. **Resource Management**: CPU/memory limits for worker containers
4. **Network Optimization**: Minimize Redis round-trips for better performance
