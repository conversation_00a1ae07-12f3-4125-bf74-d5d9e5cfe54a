[tool.poetry]
name = "tractionx-ai-agents"
version = "1.0.0"
description = "TractionX AI Agent Foundation - LangChain-based AI Agent system"
authors = ["TractionX Team <<EMAIL>>"]
readme = "README.md"
packages = [{include = "ai"}]

[tool.poetry.dependencies]
python = "~3.12"
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
pydantic = "^2.5.0"
motor = "^3.3.2"
pymongo = "^4.6.0"
langchain = "^0.1.16"
langchain-openai = "^0.0.8"
openai = "^1.3.0"
pydantic-settings = "^2.1.0"  
qdrant-client = "^1.7.0"
redis = "^5.0.1"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.6"
email-validator = "^2.1.0"
requests = "^2.31.0"
httpx = "^0.25.0"
tiktoken = ">=0.5.2,<0.6.0"
python-dotenv = "^1.0.0"
aiohttp = "^3.9.0"
feedparser = "^6.0.10"
# Document processing dependencies
pymupdf = "^1.23.0"
python-pptx = "^0.6.23"
python-docx = "^1.1.0"
langchain-community = "^0.0.38"
# PDF generation dependencies
weasyprint = "^60.0"
jinja2 = "^3.1.2"
# Image processing for charts/tables
pillow = "^10.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
pytest-mock = "^3.12.0"
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.7.0"
pre-commit = "^3.6.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 100
known_first_party = ["ai"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "motor.*",
    "pymongo.*",
    "qdrant_client.*",
    "langchain.*",
    "langchain_openai.*",
    "openai.*"
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
