"""
Base job handler.

This module provides a base class for job handlers.
"""

from abc import abstractmethod
from typing import Any, Dict, Optional

from app.core.logging import get_logger
from app.models.queue import Job
from app.services.queue.worker_interface import JobHandlerInterface

logger = get_logger(__name__)


class BaseJob<PERSON>andler(JobHandlerInterface):
    """Base class for job handlers."""

    def __init__(self):
        """Initialize the job handler."""
        self.logger = logger
        self._initialized = False
        self.logger.debug(f"Initializing {self.__class__.__name__}")

    async def initialize(self) -> None:
        """
        Initialize the handler.

        This method should be called before processing any jobs.
        Subclasses should override this method to initialize their specific services.
        """
        self.logger.debug(f"Starting initialization for {self.__class__.__name__}")
        if not self._initialized:
            try:
                self.logger.debug(
                    f"Calling _initialize_services for {self.__class__.__name__}"
                )
                await self._initialize_services()
                self._initialized = True
                self.logger.debug(f"Successfully initialized {self.__class__.__name__}")
            except Exception as e:
                self.logger.error(
                    f"Failed to initialize {self.__class__.__name__}: {str(e)}",
                    exc_info=True,
                )
                raise
        else:
            self.logger.debug(f"{self.__class__.__name__} already initialized")

    async def _initialize_services(self) -> None:
        """
        Initialize handler services.

        This method should be implemented by subclasses to initialize their specific services.
        """
        self.logger.debug(
            f"Base _initialize_services called for {self.__class__.__name__}"
        )
        pass

    async def handle(self, job: Job) -> Dict[str, Any]:
        """
        Handle a job.

        This method ensures the handler is initialized before processing the job.

        Args:
            job: The job to handle

        Returns:
            Result of job processing
        """
        self.logger.info(
            f"Handling job {job.id} of type {job.type} with {self.__class__.__name__}"
        )

        try:
            # Ensure handler is initialized
            self.logger.debug(f"Ensuring initialization for job {job.id}")
            await self.initialize()

            # Call the process method
            self.logger.debug(f"Processing job {job.id} with {self.__class__.__name__}")
            result = await self.process(job)

            # Return the result
            self.logger.debug(f"Job {job.id} completed successfully")
            return result or {}
        except Exception as e:
            self.logger.error(
                f"Error handling job {job.id} with {self.__class__.__name__}: {str(e)}",
                exc_info=True,
                extra={
                    "job_id": job.id,
                    "job_type": job.type,
                    "handler_class": self.__class__.__name__,
                },
            )
            raise

    @abstractmethod
    async def process(self, job: Job) -> Optional[Dict[str, Any]]:
        """
        Process a job.

        This method should be implemented by subclasses.

        Args:
            job: The job to process

        Returns:
            Optional result of job processing
        """
        pass
