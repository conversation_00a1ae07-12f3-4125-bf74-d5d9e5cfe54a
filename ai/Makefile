# TractionX AI Agent Foundation - Makefile

.PHONY: help install dev test lint format clean docker-build docker-run

# Default target
help:
	@echo "TractionX AI Agent Foundation"
	@echo ""
	@echo "Available commands:"
	@echo "  install      Install dependencies"
	@echo "  dev          Run development server"
	@echo "  test         Run tests"
	@echo "  lint         Run linting"
	@echo "  format       Format code"
	@echo "  clean        Clean cache files"
	@echo "  docker-build Build Docker image"
	@echo "  docker-run   Run Docker container"
	@echo "  docker-dev   Run development environment with Docker"

# Install dependencies
install:
	poetry install

# Run development server
dev:
	poetry run uvicorn ai.main:app --reload --port 8002

# Run tests
test:
	poetry run pytest

# Run tests with coverage
test-cov:
	poetry run pytest --cov=ai --cov-report=html --cov-report=term

# Run linting
lint:
	poetry run flake8 ai/
	poetry run mypy ai/

# Format code
format:
	poetry run black ai/
	poetry run isort ai/

# Clean cache files
clean:
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +
	rm -rf htmlcov/
	rm -rf .coverage

# Docker commands
docker-build:
	docker build -t tractionx-ai-agents .

docker-run:
	docker run -p 8002:8002 --env-file .env tractionx-ai-agents

docker-dev:
	docker-compose up --build

# Database commands
db-init:
	poetry run python -c "import asyncio; from ai.db import ensure_indexes; asyncio.run(ensure_indexes())"

# Health check
health:
	curl -f http://localhost:8002/health || echo "Service not running"

# Setup development environment
setup-dev: install
	cp .env.example .env
	@echo "Please edit .env file with your configuration"
	@echo "Then run: make db-init && make dev"
