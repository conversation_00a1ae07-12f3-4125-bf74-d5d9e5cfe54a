"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tag } from "lucide-react"

interface CompanyTagsSectionProps {
  keywords: string[]
}

export function CompanyTagsSection({ keywords }: CompanyTagsSectionProps) {
  // Group keywords by relevance (simple grouping for now)
  const groupedKeywords = keywords.reduce((acc, keyword) => {
    const lowerKeyword = keyword.toLowerCase()
    
    if (lowerKeyword.includes('ai') || lowerKeyword.includes('artificial intelligence')) {
      acc.ai.push(keyword)
    } else if (lowerKeyword.includes('cloud') || lowerKeyword.includes('infrastructure') || lowerKeyword.includes('kubernetes')) {
      acc.infrastructure.push(keyword)
    } else if (lowerKeyword.includes('security') || lowerKeyword.includes('compliance')) {
      acc.security.push(keyword)
    } else if (lowerKeyword.includes('enterprise') || lowerKeyword.includes('b2b')) {
      acc.enterprise.push(keyword)
    } else {
      acc.other.push(keyword)
    }
    
    return acc
  }, {
    ai: [] as string[],
    infrastructure: [] as string[],
    security: [] as string[],
    enterprise: [] as string[],
    other: [] as string[]
  })

  // Get top keywords from each group (max 15 total)
  const topKeywords = [
    ...groupedKeywords.ai.slice(0, 3),
    ...groupedKeywords.infrastructure.slice(0, 4),
    ...groupedKeywords.security.slice(0, 2),
    ...groupedKeywords.enterprise.slice(0, 2),
    ...groupedKeywords.other.slice(0, 4)
  ].slice(0, 15)

  if (!keywords || keywords.length === 0) {
    return null
  }

  return (
    <div className="animate-in fade-in duration-500">
      <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-md">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-2">
            <Tag className="h-5 w-5 text-gray-600" />
            <CardTitle className="text-lg font-semibold text-gray-900">
              Company Keywords
            </CardTitle>
            <Badge variant="secondary" className="text-xs">
              {keywords.length} total
            </Badge>
          </div>
        </CardHeader>

        <CardContent>
          <div className="flex flex-wrap gap-2">
            {topKeywords.map((keyword, index) => (
              <div
                key={keyword}
                className="animate-in fade-in scale-in-95 duration-300"
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <Badge 
                  variant="outline" 
                  className="text-xs px-3 py-1 bg-gray-50 hover:bg-gray-100 transition-colors"
                >
                  {keyword}
                </Badge>
              </div>
            ))}
            
            {keywords.length > topKeywords.length && (
              <Badge 
                variant="secondary" 
                className="text-xs px-3 py-1"
              >
                +{keywords.length - topKeywords.length} more
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 