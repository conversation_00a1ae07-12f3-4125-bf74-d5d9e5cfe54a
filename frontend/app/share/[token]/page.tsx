"use client"

import { useParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { AlertCircle, Star } from 'lucide-react'

import { SharedFormRenderer } from '@/components/core/form-share/shared-form-renderer'
import { ShareErrorBoundary } from '@/components/core/form-share/share-error-boundary'
import { PublicAuthProvider } from '@/lib/contexts/public-auth-context'
import { cn } from '@/lib/utils'
import { visualRetreat, mobileRetreat } from '@/lib/utils/responsive'

export default function ShareFormPage() {
  const params = useParams()
  const token = params?.token as string

  // Premium invalid token state
  if (!token) {
    return (
      <div className={cn(mobileRetreat.page.container, "bg-gradient-to-br from-gray-50 via-white to-blue-50/30")}>
        <div className={mobileRetreat.error.container}>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className={cn(visualRetreat.card.base, visualRetreat.card.floating, "mx-auto max-w-md p-8 text-center")}
          >
            <div className="mx-auto mb-6 flex size-16 items-center justify-center rounded-full bg-red-50">
              <AlertCircle className="size-8 text-red-500" />
            </div>

            <h1 className="mb-4 text-2xl font-bold text-gray-900">
              Invalid Form Link
            </h1>
            <p className="mb-6 text-gray-600">
              This form sharing link appears to be invalid or malformed. Please check the URL and try again.
            </p>

            <div className="space-y-3">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => window.location.reload()}
                className="touch-target w-full rounded-xl bg-blue-600 px-6 py-3 font-medium text-white transition-colors hover:bg-blue-700"
              >
                Reload Page
              </motion.button>

              <p className="text-sm text-gray-500">
                If this issue persists, please contact the organization that shared this form.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    )
  }

  // Premium form sharing experience
  return (
    <div className={cn(mobileRetreat.page.container, "bg-gradient-to-br from-gray-50 via-white to-blue-50/30")}>
      <ShareErrorBoundary>
        <PublicAuthProvider>
          <SharedFormRenderer token={token} />
        </PublicAuthProvider>
      </ShareErrorBoundary>

      {/* Premium footer */}
      <motion.footer
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="mt-16 border-t border-gray-200/50 py-8"
      >
        <div className="mx-auto max-w-4xl px-4 text-center md:px-6">
          <div className="mb-2 flex items-center justify-center gap-2 text-sm text-gray-500">
            <Star className="size-4" />
            <span>Powered by TractionX</span>
          </div>
          <p className="text-xs text-gray-400">
            Taking Private market investing to the next era
          </p>
        </div>
      </motion.footer>
    </div>
  )
}
