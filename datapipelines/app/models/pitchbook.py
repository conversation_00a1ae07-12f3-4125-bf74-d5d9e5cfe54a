"""
PitchBook Models for TractionX Data Pipeline Service.

This module contains Pydantic models for PitchBook URL resolution:
- PitchBookResolverInput: Input model for resolver requests
- PitchBookResolverOutput: Output model for resolver responses
"""

from typing import Optional

from pydantic import BaseModel, Field


class PitchBookResolverInput(BaseModel):
    """Input model for PitchBook URL resolution requests."""

    company_domain: str = Field(..., description="Company domain to resolve")
    company_description: Optional[str] = Field(
        None, description="Optional company description"
    )
    org_id: Optional[str] = Field(None, description="Organization ID")
    job_id: Optional[str] = Field(None, description="Job ID for tracking")


class PitchBookResolverOutput(BaseModel):
    """Output model for PitchBook URL resolution responses."""

    status: str = Field(..., description="Resolution status")
    pitchbook_url: Optional[str] = Field(None, description="Resolved PitchBook URL")
    brightdata_snapshot_id: Optional[str] = Field(
        None, description="BrightData snapshot ID"
    )
    error_message: Optional[str] = Field(
        None, description="Error message if resolution failed"
    )
    llm_decision: Optional[str] = Field(
        None, description="LLM decision for URL selection"
    )
    serper_results_count: Optional[int] = Field(
        None, description="Number of Serper search results"
    )
    confidence_score: Optional[float] = Field(
        None, description="Confidence score for the resolution"
    )
    processing_time: Optional[float] = Field(
        None, description="Processing time in seconds"
    )
