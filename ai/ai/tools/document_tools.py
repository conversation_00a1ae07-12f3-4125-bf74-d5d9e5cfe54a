"""
Document processing tools for TractionX AI Agent system.
Handles PDF, PPTX, and DOCX extraction and processing for deal memo generation.
"""

import os
import io
import asyncio
import logging
from typing import Dict, Any, List, Optional, Union, BinaryIO
from datetime import datetime
from pathlib import Path
import tempfile
import base64

from pydantic import BaseModel, Field
import fitz  # PyMuPDF
from pptx import Presentation
from docx import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document as LangChainDocument

from ai.tools.base import BaseTool, ToolInput, ToolOutput, tool
from ai.models import AgentContext
from ai.config import settings

logger = logging.getLogger(__name__)


class DocumentUpload(BaseModel):
    """Document upload model."""
    filename: str = Field(..., description="Original filename")
    content: bytes = Field(..., description="File content as bytes")
    content_type: str = Field(..., description="MIME type")
    size: int = Field(..., description="File size in bytes")


class ExtractedContent(BaseModel):
    """Extracted content from document."""
    text: str = Field(..., description="Extracted text content")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Document metadata")
    pages: Optional[List[Dict[str, Any]]] = Field(None, description="Page-level content")
    slides: Optional[List[Dict[str, Any]]] = Field(None, description="Slide-level content (PPTX)")
    tables: Optional[List[Dict[str, Any]]] = Field(None, description="Extracted tables")
    images: Optional[List[Dict[str, Any]]] = Field(None, description="Image metadata")


class DocumentExtractionInput(ToolInput):
    """Input for document extraction tool."""
    document: DocumentUpload = Field(..., description="Document to extract")
    extract_tables: bool = Field(default=True, description="Extract table content")
    extract_images: bool = Field(default=False, description="Extract image metadata")
    chunk_text: bool = Field(default=True, description="Split text into chunks")


class DocumentExtractionOutput(ToolOutput):
    """Output from document extraction tool."""
    extracted_content: Optional[ExtractedContent] = Field(None, description="Extracted content")
    chunks: Optional[List[LangChainDocument]] = Field(None, description="Text chunks for RAG")
    processing_time: float = Field(default=0.0, description="Processing time in seconds")


class PitchBookExtractionInput(ToolInput):
    """Input for pitch book extraction tool."""
    document: DocumentUpload = Field(..., description="Pitch deck document")
    extract_structured_data: bool = Field(default=True, description="Extract structured company data")


class PitchBookData(BaseModel):
    """Structured data extracted from pitch book."""
    company_name: Optional[str] = Field(None, description="Company name")
    tagline: Optional[str] = Field(None, description="Company tagline/description")
    founders: List[str] = Field(default_factory=list, description="Founder names")
    team_size: Optional[int] = Field(None, description="Team size")
    market_size: Optional[str] = Field(None, description="Market size information")
    traction: List[str] = Field(default_factory=list, description="Traction metrics")
    funding_ask: Optional[str] = Field(None, description="Funding ask amount")
    use_of_funds: List[str] = Field(default_factory=list, description="Use of funds")
    business_model: Optional[str] = Field(None, description="Business model")
    competitive_advantage: List[str] = Field(default_factory=list, description="Competitive advantages")
    financial_projections: Optional[Dict[str, Any]] = Field(None, description="Financial data")
    contact_info: Optional[Dict[str, str]] = Field(None, description="Contact information")
    key_slides: List[Dict[str, Any]] = Field(default_factory=list, description="Important slides")


class PitchBookExtractionOutput(ToolOutput):
    """Output from pitch book extraction tool."""
    pitch_data: Optional[PitchBookData] = Field(None, description="Structured pitch book data")
    extracted_content: Optional[ExtractedContent] = Field(None, description="Raw extracted content")
    processing_time: float = Field(default=0.0, description="Processing time in seconds")


@tool(
    name="document_extraction",
    description="Extract text, tables, and metadata from PDF, PPTX, and DOCX documents",
    category="document",
    tags=["pdf", "pptx", "docx", "extraction", "text", "tables"]
)
class DocumentExtractionTool(BaseTool):
    """
    Tool for extracting content from various document formats.

    Supports:
    - PDF documents (text, tables, metadata)
    - PowerPoint presentations (slides, text, notes)
    - Word documents (text, tables, metadata)
    """

    def __init__(self):
        super().__init__()
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=settings.DOC_CHUNK_SIZE,
            chunk_overlap=settings.DOC_CHUNK_OVERLAP,
            length_function=len,
        )
    
    async def execute(
        self,
        input_data: DocumentExtractionInput,
        context: AgentContext
    ) -> DocumentExtractionOutput:
        """Extract content from uploaded document."""
        
        start_time = datetime.now()
        logger.info(f"Starting document extraction for: {input_data.document.filename}")
        
        try:
            # Validate file
            if not self._validate_document(input_data.document):
                return DocumentExtractionOutput(
                    success=False,
                    error="Invalid document format or size"
                )
            
            # Extract content based on file type
            file_ext = Path(input_data.document.filename).suffix.lower()
            
            if file_ext == ".pdf":
                extracted_content = await self._extract_pdf(input_data.document, input_data)
            elif file_ext == ".pptx":
                extracted_content = await self._extract_pptx(input_data.document, input_data)
            elif file_ext == ".docx":
                extracted_content = await self._extract_docx(input_data.document, input_data)
            else:
                return DocumentExtractionOutput(
                    success=False,
                    error=f"Unsupported file format: {file_ext}"
                )
            
            # Create text chunks if requested
            chunks = None
            if input_data.chunk_text and extracted_content.text:
                chunks = self._create_chunks(extracted_content, input_data.document.filename)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return DocumentExtractionOutput(
                extracted_content=extracted_content,
                chunks=chunks,
                processing_time=processing_time,
                success=True
            )
            
        except Exception as e:
            logger.error(f"Document extraction failed: {e}")
            return DocumentExtractionOutput(
                success=False,
                error=str(e),
                processing_time=(datetime.now() - start_time).total_seconds()
            )
    
    def _validate_document(self, document: DocumentUpload) -> bool:
        """Validate document format and size."""
        # Check file size
        if document.size > settings.DOC_MAX_FILE_SIZE:
            logger.warning(f"Document too large: {document.size} bytes")
            return False
        
        # Check file extension
        file_ext = Path(document.filename).suffix.lower()
        if file_ext not in settings.DOC_ALLOWED_EXTENSIONS:
            logger.warning(f"Unsupported file extension: {file_ext}")
            return False
        
        return True
    
    async def _extract_pdf(
        self, 
        document: DocumentUpload, 
        input_data: DocumentExtractionInput
    ) -> ExtractedContent:
        """Extract content from PDF document."""
        logger.debug(f"Extracting PDF content from {document.filename}")
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_file:
            temp_file.write(document.content)
            temp_path = temp_file.name
        
        try:
            # Open PDF with PyMuPDF
            pdf_doc = fitz.open(temp_path)
            
            text_content = []
            pages = []
            tables = []
            images = []
            
            for page_num in range(len(pdf_doc)):
                page = pdf_doc[page_num]
                
                # Extract text
                page_text = page.get_text()
                text_content.append(page_text)
                
                # Store page info
                page_info = {
                    "page_number": page_num + 1,
                    "text": page_text,
                    "char_count": len(page_text)
                }
                pages.append(page_info)
                
                # Extract tables if requested
                if input_data.extract_tables:
                    try:
                        page_tables = page.find_tables()
                        for table in page_tables:
                            table_data = table.extract()
                            tables.append({
                                "page": page_num + 1,
                                "data": table_data,
                                "rows": len(table_data),
                                "cols": len(table_data[0]) if table_data else 0
                            })
                    except Exception as e:
                        logger.debug(f"Table extraction failed on page {page_num + 1}: {e}")
                
                # Extract image metadata if requested
                if input_data.extract_images:
                    try:
                        image_list = page.get_images()
                        for img_index, img in enumerate(image_list):
                            images.append({
                                "page": page_num + 1,
                                "index": img_index,
                                "width": img[2],
                                "height": img[3],
                                "colorspace": img[4]
                            })
                    except Exception as e:
                        logger.debug(f"Image extraction failed on page {page_num + 1}: {e}")
            
            # Combine all text
            full_text = "\n\n".join(text_content)
            
            # Extract metadata
            metadata = {
                "filename": document.filename,
                "page_count": len(pdf_doc),
                "title": pdf_doc.metadata.get("title", ""),
                "author": pdf_doc.metadata.get("author", ""),
                "subject": pdf_doc.metadata.get("subject", ""),
                "creator": pdf_doc.metadata.get("creator", ""),
                "creation_date": pdf_doc.metadata.get("creationDate", ""),
                "modification_date": pdf_doc.metadata.get("modDate", ""),
                "char_count": len(full_text),
                "word_count": len(full_text.split())
            }
            
            pdf_doc.close()
            
            return ExtractedContent(
                text=full_text,
                metadata=metadata,
                pages=pages,
                tables=tables if tables else None,
                images=images if images else None
            )
            
        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_path)
            except Exception as e:
                logger.warning(f"Failed to delete temp file: {e}")

    async def _extract_pptx(
        self,
        document: DocumentUpload,
        input_data: DocumentExtractionInput
    ) -> ExtractedContent:
        """Extract content from PowerPoint presentation."""
        logger.debug(f"Extracting PPTX content from {document.filename}")

        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=".pptx", delete=False) as temp_file:
            temp_file.write(document.content)
            temp_path = temp_file.name

        try:
            # Open presentation
            prs = Presentation(temp_path)

            text_content = []
            slides = []
            tables = []

            for slide_num, slide in enumerate(prs.slides):
                slide_text = []
                slide_tables = []

                # Extract text from shapes
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text:
                        slide_text.append(shape.text)

                    # Extract tables if requested
                    if input_data.extract_tables and shape.has_table:
                        try:
                            table_data = []
                            for row in shape.table.rows:
                                row_data = [cell.text for cell in row.cells]
                                table_data.append(row_data)

                            table_info = {
                                "slide": slide_num + 1,
                                "data": table_data,
                                "rows": len(table_data),
                                "cols": len(table_data[0]) if table_data else 0
                            }
                            tables.append(table_info)
                            slide_tables.append(table_info)
                        except Exception as e:
                            logger.debug(f"Table extraction failed on slide {slide_num + 1}: {e}")

                # Extract notes
                notes_text = ""
                if slide.has_notes_slide:
                    try:
                        notes_text = slide.notes_slide.notes_text_frame.text
                    except Exception as e:
                        logger.debug(f"Notes extraction failed on slide {slide_num + 1}: {e}")

                # Combine slide text
                slide_full_text = "\n".join(slide_text)
                if notes_text:
                    slide_full_text += f"\n\nNotes: {notes_text}"

                text_content.append(slide_full_text)

                # Store slide info
                slide_info = {
                    "slide_number": slide_num + 1,
                    "title": slide_text[0] if slide_text else "",
                    "text": slide_full_text,
                    "notes": notes_text,
                    "tables": slide_tables,
                    "char_count": len(slide_full_text)
                }
                slides.append(slide_info)

            # Combine all text
            full_text = "\n\n".join(text_content)

            # Extract metadata
            core_props = prs.core_properties
            metadata = {
                "filename": document.filename,
                "slide_count": len(prs.slides),
                "title": core_props.title or "",
                "author": core_props.author or "",
                "subject": core_props.subject or "",
                "created": str(core_props.created) if core_props.created else "",
                "modified": str(core_props.modified) if core_props.modified else "",
                "char_count": len(full_text),
                "word_count": len(full_text.split())
            }

            return ExtractedContent(
                text=full_text,
                metadata=metadata,
                slides=slides,
                tables=tables if tables else None
            )

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_path)
            except Exception as e:
                logger.warning(f"Failed to delete temp file: {e}")

    async def _extract_docx(
        self,
        document: DocumentUpload,
        input_data: DocumentExtractionInput
    ) -> ExtractedContent:
        """Extract content from Word document."""
        logger.debug(f"Extracting DOCX content from {document.filename}")

        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=".docx", delete=False) as temp_file:
            temp_file.write(document.content)
            temp_path = temp_file.name

        try:
            # Open document
            doc = Document(temp_path)

            text_content = []
            tables = []

            # Extract paragraphs
            for para in doc.paragraphs:
                if para.text.strip():
                    text_content.append(para.text)

            # Extract tables if requested
            if input_data.extract_tables:
                for table_num, table in enumerate(doc.tables):
                    try:
                        table_data = []
                        for row in table.rows:
                            row_data = [cell.text for cell in row.cells]
                            table_data.append(row_data)

                        tables.append({
                            "table_number": table_num + 1,
                            "data": table_data,
                            "rows": len(table_data),
                            "cols": len(table_data[0]) if table_data else 0
                        })
                    except Exception as e:
                        logger.debug(f"Table extraction failed for table {table_num + 1}: {e}")

            # Combine all text
            full_text = "\n\n".join(text_content)

            # Extract metadata
            core_props = doc.core_properties
            metadata = {
                "filename": document.filename,
                "paragraph_count": len(doc.paragraphs),
                "table_count": len(doc.tables),
                "title": core_props.title or "",
                "author": core_props.author or "",
                "subject": core_props.subject or "",
                "created": str(core_props.created) if core_props.created else "",
                "modified": str(core_props.modified) if core_props.modified else "",
                "char_count": len(full_text),
                "word_count": len(full_text.split())
            }

            return ExtractedContent(
                text=full_text,
                metadata=metadata,
                tables=tables if tables else None
            )

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_path)
            except Exception as e:
                logger.warning(f"Failed to delete temp file: {e}")

    def _create_chunks(
        self,
        content: ExtractedContent,
        filename: str
    ) -> List[LangChainDocument]:
        """Create text chunks for RAG processing."""
        if not content.text:
            return []

        # Split text into chunks
        chunks = self.text_splitter.split_text(content.text)

        # Create LangChain documents
        documents = []
        for i, chunk in enumerate(chunks):
            doc = LangChainDocument(
                page_content=chunk,
                metadata={
                    "source": filename,
                    "chunk_id": i,
                    "total_chunks": len(chunks),
                    "char_count": len(chunk),
                    "document_metadata": content.metadata
                }
            )
            documents.append(doc)

        return documents


@tool(
    name="pitch_book_extraction",
    description="Extract structured company data from pitch decks and one-pagers",
    category="document",
    tags=["pitch", "deck", "extraction", "structured", "company", "llm"]
)
class PitchBookExtractionTool(BaseTool):
    """
    Tool for extracting structured data from pitch decks and one-pagers.

    Uses LLM-powered analysis to extract:
    - Company information
    - Team details
    - Market and traction data
    - Financial information
    - Key slides and content
    """

    def __init__(self):
        super().__init__()
        self.document_tool = DocumentExtractionTool()

        # Initialize OpenAI client for structured extraction
        from openai import AsyncOpenAI
        self.openai_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)

    async def execute(
        self,
        input_data: PitchBookExtractionInput,
        context: AgentContext
    ) -> PitchBookExtractionOutput:
        """Extract structured data from pitch book."""

        start_time = datetime.now()
        logger.info(f"Starting pitch book extraction for: {input_data.document.filename}")

        try:
            # First extract raw content
            extraction_input = DocumentExtractionInput(
                document=input_data.document,
                extract_tables=True,
                extract_images=False,
                chunk_text=False
            )

            extraction_result = await self.document_tool.execute(extraction_input, context)

            if not extraction_result.success or not extraction_result.extracted_content:
                return PitchBookExtractionOutput(
                    success=False,
                    error="Failed to extract document content"
                )

            extracted_content = extraction_result.extracted_content

            # Extract structured data if requested
            pitch_data = None
            if input_data.extract_structured_data:
                pitch_data = await self._extract_structured_data(extracted_content, context)

            processing_time = (datetime.now() - start_time).total_seconds()

            return PitchBookExtractionOutput(
                pitch_data=pitch_data,
                extracted_content=extracted_content,
                processing_time=processing_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Pitch book extraction failed: {e}")
            return PitchBookExtractionOutput(
                success=False,
                error=str(e),
                processing_time=(datetime.now() - start_time).total_seconds()
            )

    async def _extract_structured_data(
        self,
        content: ExtractedContent,
        context: AgentContext
    ) -> PitchBookData:
        """Extract structured data using LLM analysis."""
        logger.debug("Extracting structured data from pitch book content")

        try:
            # Prepare content for analysis
            analysis_text = content.text

            # Add slide/page information if available
            if content.slides:
                slide_info = "\n\n".join([
                    f"Slide {slide['slide_number']}: {slide['title']}\n{slide['text']}"
                    for slide in content.slides[:10]  # Limit to first 10 slides
                ])
                analysis_text = slide_info
            elif content.pages:
                page_info = "\n\n".join([
                    f"Page {page['page_number']}:\n{page['text']}"
                    for page in content.pages[:10]  # Limit to first 10 pages
                ])
                analysis_text = page_info

            # Create extraction prompt
            prompt = self._create_extraction_prompt(analysis_text)

            # Call OpenAI for structured extraction
            response = await self.openai_client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "You are an expert investment analyst specializing in pitch deck analysis and data extraction."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=2000,
                temperature=0.1,  # Low temperature for factual extraction
                timeout=60
            )

            if response.choices and response.choices[0].message:
                # Parse the structured response
                return self._parse_extraction_response(response.choices[0].message.content)
            else:
                logger.warning("Empty response from OpenAI for pitch book extraction")
                return self._create_fallback_pitch_data(content)

        except Exception as e:
            logger.error(f"Structured data extraction failed: {e}")
            return self._create_fallback_pitch_data(content)

    def _create_extraction_prompt(self, content: str) -> str:
        """Create prompt for structured data extraction."""
        return f"""Analyze the following pitch deck content and extract structured information. Return the data in JSON format with the following structure:

{{
    "company_name": "Company name",
    "tagline": "Company tagline or description",
    "founders": ["Founder 1", "Founder 2"],
    "team_size": number,
    "market_size": "Market size information",
    "traction": ["Metric 1", "Metric 2"],
    "funding_ask": "Funding amount requested",
    "use_of_funds": ["Use 1", "Use 2"],
    "business_model": "Business model description",
    "competitive_advantage": ["Advantage 1", "Advantage 2"],
    "financial_projections": {{"revenue_2024": "amount", "revenue_2025": "amount"}},
    "contact_info": {{"email": "email", "website": "website"}},
    "key_slides": [{{"slide_number": 1, "title": "Title", "content": "Summary"}}]
}}

Extract as much information as possible from the content. If information is not available, use null or empty arrays. Focus on:

1. Company basics (name, tagline, description)
2. Team information (founders, key team members, team size)
3. Market and opportunity (market size, target market, problem/solution)
4. Traction and metrics (revenue, users, growth, partnerships)
5. Financial information (funding ask, use of funds, projections, valuation)
6. Business model and competitive advantages
7. Contact information
8. Key slides with important information

Content to analyze:
{content[:8000]}  # Limit content to avoid token limits

Return only the JSON object, no additional text."""

    def _parse_extraction_response(self, response: str) -> PitchBookData:
        """Parse LLM response into structured data."""
        try:
            import json

            # Try to extract JSON from response
            response = response.strip()
            if response.startswith("```json"):
                response = response[7:]
            if response.endswith("```"):
                response = response[:-3]

            data = json.loads(response)

            return PitchBookData(
                company_name=data.get("company_name"),
                tagline=data.get("tagline"),
                founders=data.get("founders", []),
                team_size=data.get("team_size"),
                market_size=data.get("market_size"),
                traction=data.get("traction", []),
                funding_ask=data.get("funding_ask"),
                use_of_funds=data.get("use_of_funds", []),
                business_model=data.get("business_model"),
                competitive_advantage=data.get("competitive_advantage", []),
                financial_projections=data.get("financial_projections"),
                contact_info=data.get("contact_info"),
                key_slides=data.get("key_slides", [])
            )

        except Exception as e:
            logger.warning(f"Failed to parse extraction response: {e}")
            return PitchBookData()

    def _create_fallback_pitch_data(self, content: ExtractedContent) -> PitchBookData:
        """Create basic pitch data when LLM extraction fails."""
        # Extract basic information using simple text analysis
        text = content.text.lower()

        # Try to find company name from title or first slide
        company_name = None
        if content.metadata.get("title"):
            company_name = content.metadata["title"]
        elif content.slides and content.slides[0].get("title"):
            company_name = content.slides[0]["title"]

        # Basic keyword extraction for traction
        traction = []
        if "revenue" in text:
            traction.append("Revenue mentioned")
        if "users" in text or "customers" in text:
            traction.append("User/customer metrics mentioned")
        if "growth" in text:
            traction.append("Growth metrics mentioned")

        return PitchBookData(
            company_name=company_name,
            traction=traction,
            key_slides=[
                {
                    "slide_number": i + 1,
                    "title": slide.get("title", f"Slide {i + 1}"),
                    "content": slide.get("text", "")[:200] + "..." if len(slide.get("text", "")) > 200 else slide.get("text", "")
                }
                for i, slide in enumerate(content.slides[:5])
            ] if content.slides else []
        )
