# QA Test Plan: File Upload & Submission Flow

## Phase 1: Startup Side Testing

### A. Invitation and Access Flow

#### Test Case 1.1: Valid Magic Link Flow
**Scenario**: Founder receives valid `/share/[token]` link
**Steps**:
1. Navigate to `/share/valid-token-123`
2. Verify branded login page displays investor info
3. Enter valid email address
4. Click "Send Magic Link"
5. Check email for magic link
6. Click magic link within expiry window
7. Verify redirect to form with submissionId

**Expected Results**:
- ✅ Branded login page loads with investor branding
- ✅ Magic link email sent successfully
- ✅ Magic link validation succeeds
- ✅ User redirected to form with valid session
- ✅ submissionId available in context

#### Test Case 1.2: Invalid/Expired Token
**Scenario**: <PERSON> uses invalid or expired token
**Steps**:
1. Navigate to `/share/invalid-token`
2. Attempt to enter email
3. Navigate to `/share/expired-token`
4. Attempt login flow

**Expected Results**:
- ❌ Invalid token shows clear error message
- ❌ Expired token shows expiry message with re-request option
- ✅ No form access without valid token

#### Test Case 1.3: Magic Link Expiry
**Sc<PERSON>rio**: Magic link expires before use
**Steps**:
1. Request magic link
2. Wait for expiry (15 minutes)
3. Click expired magic link
4. Attempt to re-login with same email

**Expected Results**:
- ❌ Expired magic link shows error
- ✅ Re-login generates new magic link
- ✅ New magic link works correctly

### B. Form Drafting and File Upload

#### Test Case 2.1: File Upload - Valid Files
**Scenario**: Upload files within size/type limits
**Test Data**:
- PDF file (2MB, .pdf)
- Word document (5MB, .docx)
- Image file (1MB, .png)
- Spreadsheet (3MB, .xlsx)

**Steps**:
1. Navigate to file upload question
2. Drag and drop valid file
3. Verify upload progress
4. Confirm file appears in UI
5. Verify file metadata display
6. Test download functionality

**Expected Results**:
- ✅ Upload progress shows correctly
- ✅ File appears with correct name/size
- ✅ S3 key stored in form answer
- ✅ Download generates presigned URL
- ✅ File accessible via download link

#### Test Case 2.2: File Upload - Invalid Files
**Scenario**: Attempt to upload restricted files
**Test Data**:
- Oversized file (60MB, exceeds 50MB limit)
- Wrong type (.exe file when only .pdf allowed)
- Empty file (0 bytes)
- File with special characters in name

**Steps**:
1. Attempt to upload each invalid file
2. Verify error messages
3. Confirm no partial uploads
4. Test error recovery

**Expected Results**:
- ❌ Oversized file rejected with size error
- ❌ Wrong type rejected with format error
- ❌ Empty file rejected
- ✅ Special characters handled gracefully
- ✅ Clear error messages displayed
- ✅ No orphaned files in S3

#### Test Case 2.3: Multiple File Management
**Scenario**: Questions allowing multiple files
**Steps**:
1. Upload first file successfully
2. Upload second file
3. Remove first file
4. Upload replacement file
5. Verify final file list

**Expected Results**:
- ✅ Multiple files display correctly
- ✅ File removal works instantly
- ✅ Replacement files upload successfully
- ✅ Form answer reflects current file list

### C. Form Validation and Conditional Logic

#### Test Case 3.1: Required Field Validation
**Scenario**: Submit form with missing required fields
**Steps**:
1. Fill out form partially
2. Leave required text field empty
3. Leave required file upload empty
4. Attempt to submit
5. Verify validation errors

**Expected Results**:
- ❌ Submit button disabled until all required fields filled
- ✅ Clear error messages for missing fields
- ✅ Form scrolls to first error
- ✅ Progress indicator shows completion status

#### Test Case 3.2: Conditional Visibility Logic
**Scenario**: Questions appear/disappear based on answers
**Test Data**:
- If "Sector" = "AI" → Show "AI Framework" question
- If "Revenue" > 1M → Show "Revenue Breakdown" file upload

**Steps**:
1. Answer trigger question with condition value
2. Verify dependent question appears
3. Answer dependent question
4. Change trigger question answer
5. Verify dependent question disappears
6. Verify dependent answer is cleared

**Expected Results**:
- ✅ Questions appear when conditions met
- ✅ Questions disappear when conditions not met
- ✅ Dependent answers cleared when hidden
- ✅ Form validation updates correctly

### D. Save & Resume Functionality

#### Test Case 4.1: Auto-save During Editing
**Scenario**: Form saves progress automatically
**Steps**:
1. Start filling out form
2. Enter text in various fields
3. Upload a file
4. Wait for auto-save (should be throttled)
5. Verify save indicators

**Expected Results**:
- ✅ Auto-save triggers after field changes
- ✅ Save indicator shows "Saving..." then "Saved"
- ✅ No excessive API calls (throttled)
- ✅ All field types auto-save correctly

#### Test Case 4.2: Resume After Browser Close
**Scenario**: Close browser and resume later
**Steps**:
1. Fill out form partially
2. Upload files
3. Close browser completely
4. Reopen and navigate to same `/share/[token]`
5. Re-authenticate if needed
6. Verify form state restored

**Expected Results**:
- ✅ All text answers restored
- ✅ File uploads still present
- ✅ Progress indicator accurate
- ✅ No data loss

#### Test Case 4.3: Session Expiry and Recovery
**Scenario**: Session expires during form filling
**Steps**:
1. Start form with valid session
2. Wait for session expiry
3. Attempt to save/submit
4. Handle re-authentication
5. Verify form state preserved

**Expected Results**:
- ✅ Session expiry detected
- ✅ Re-authentication prompt appears
- ✅ Form state preserved during re-auth
- ✅ Seamless continuation after re-auth

### E. Final Submission Flow

#### Test Case 5.1: Successful Submission
**Scenario**: Complete and submit valid form
**Steps**:
1. Fill all required fields
2. Upload all required files
3. Verify submit button enabled
4. Click submit
5. Verify confirmation screen
6. Test file download from confirmation

**Expected Results**:
- ✅ Submit button enables when form complete
- ✅ Submission processes successfully
- ✅ Confirmation screen shows all answers
- ✅ Files downloadable from confirmation
- ✅ Form locked for editing
- ✅ Status changed to "SUBMITTED"

#### Test Case 5.2: Submission Validation Errors
**Scenario**: Attempt submission with validation errors
**Steps**:
1. Fill form with some invalid data
2. Leave some required fields empty
3. Attempt to submit
4. Verify error handling

**Expected Results**:
- ❌ Submit button remains disabled
- ✅ Validation errors clearly displayed
- ✅ Form scrolls to first error
- ✅ Error messages are specific and helpful

## Phase 2: Investor Side Testing

### A. Deal Page File Access

#### Test Case 6.1: Investor File Download
**Scenario**: Investor accesses submitted files
**Steps**:
1. Login as investor
2. Navigate to deal page
3. View submission with files
4. Click download on each file type
5. Verify file access permissions

**Expected Results**:
- ✅ Only SUBMITTED submissions visible
- ✅ File downloads work correctly
- ✅ Presigned URLs generated securely
- ✅ No access to DRAFT submissions

#### Test Case 6.2: File Preview Functionality
**Scenario**: Preview files in browser
**Steps**:
1. Access submission with image files
2. Access submission with PDF files
3. Click preview buttons
4. Verify in-browser display

**Expected Results**:
- ✅ Images open in new tab for preview
- ✅ PDFs open in browser viewer
- ✅ Other files trigger download
- ✅ Preview URLs are secure

### B. Security and Access Control

#### Test Case 7.1: Cross-User File Access
**Scenario**: Attempt unauthorized file access
**Steps**:
1. Get file ID from one submission
2. Attempt to access from different user account
3. Try direct S3 URL access
4. Test API endpoint security

**Expected Results**:
- ❌ Cross-user access denied
- ❌ Direct S3 URLs not accessible
- ❌ API returns 403 for unauthorized access
- ✅ Audit logs record access attempts

#### Test Case 7.2: File Access After Status Change
**Scenario**: File access when submission status changes
**Steps**:
1. Access files in DRAFT status (as owner)
2. Submit the form
3. Access files in SUBMITTED status (as investor)
4. Verify access patterns

**Expected Results**:
- ✅ Owner can access files in any status
- ✅ Investor can only access SUBMITTED files
- ✅ Status changes reflected immediately
- ✅ Access control enforced consistently

## Performance and Edge Cases

### Test Case 8.1: Large File Handling
**Scenario**: Upload maximum allowed file sizes
**Steps**:
1. Upload 50MB file (at limit)
2. Monitor upload progress
3. Verify completion
4. Test download performance

**Expected Results**:
- ✅ Large files upload successfully
- ✅ Progress tracking accurate
- ✅ No timeout errors
- ✅ Download performance acceptable

### Test Case 8.2: Network Interruption
**Scenario**: Network issues during upload
**Steps**:
1. Start file upload
2. Disconnect network mid-upload
3. Reconnect network
4. Verify error handling and recovery

**Expected Results**:
- ✅ Upload failure detected
- ✅ Clear error message displayed
- ✅ Retry mechanism available
- ✅ No orphaned files created

### Test Case 8.3: Concurrent User Access
**Scenario**: Multiple users accessing same form
**Steps**:
1. Multiple users access same shared form
2. Upload files simultaneously
3. Submit forms
4. Verify data integrity

**Expected Results**:
- ✅ Each user has separate submission
- ✅ No file conflicts
- ✅ All submissions processed correctly
- ✅ No data corruption

## Browser and Device Compatibility

### Test Case 9.1: Cross-Browser Testing
**Browsers**: Chrome, Firefox, Safari, Edge
**Features to Test**:
- File upload (drag & drop)
- Form validation
- Auto-save functionality
- File download/preview

### Test Case 9.2: Mobile Device Testing
**Devices**: iOS Safari, Android Chrome
**Features to Test**:
- Touch-friendly file upload
- Form navigation
- File preview on mobile
- Responsive design

## Accessibility Testing

### Test Case 10.1: Screen Reader Compatibility
**Steps**:
1. Navigate form using screen reader
2. Upload files using keyboard only
3. Verify all labels and descriptions
4. Test error message announcements

### Test Case 10.2: Keyboard Navigation
**Steps**:
1. Navigate entire form using Tab key
2. Upload files using keyboard
3. Submit form without mouse
4. Verify focus indicators

## Data Integrity and Backup

### Test Case 11.1: Data Persistence
**Scenario**: Verify data is never lost
**Steps**:
1. Fill form over multiple sessions
2. Upload files at different times
3. Verify all data persists
4. Check database consistency

### Test Case 11.2: File Backup and Recovery
**Scenario**: S3 file integrity
**Steps**:
1. Upload files
2. Verify S3 storage
3. Test file recovery
4. Verify metadata consistency

## User Story Acceptance Criteria

### Epic: File Upload & Submission System

#### User Story 1: Startup File Upload
**As a** startup founder
**I want to** upload files as part of my application submission
**So that** I can provide required documents to investors

**Acceptance Criteria**:
- ✅ I can access the form via a unique `/share/[token]` link
- ✅ I can authenticate using magic link sent to my email
- ✅ I can upload files for questions that require them
- ✅ I can see upload progress and file validation errors
- ✅ I can preview and download uploaded files
- ✅ I can delete and re-upload files while in draft status
- ✅ My form auto-saves as I work
- ✅ I can resume my work after closing the browser
- ✅ I can submit my completed application
- ✅ I cannot edit files after submission (unless reopened)

#### User Story 2: Investor File Access
**As an** investor
**I want to** access and download files from submitted applications
**So that** I can review startup documentation

**Acceptance Criteria**:
- ✅ I can only access files from submitted applications
- ✅ I cannot access draft applications
- ✅ I can download files securely via presigned URLs
- ✅ I can preview images and PDFs in browser
- ✅ I cannot delete or modify startup files
- ✅ All my file access is logged for audit purposes

#### User Story 3: File Security & Validation
**As a** system administrator
**I want** files to be securely stored and validated
**So that** the platform is safe and reliable

**Acceptance Criteria**:
- ✅ Files are stored in S3 with secure access patterns
- ✅ File types are validated against question requirements
- ✅ File sizes are enforced (system and per-question limits)
- ✅ Malicious files are rejected
- ✅ Presigned URLs expire after 10 minutes
- ✅ Cross-user file access is prevented
- ✅ All file operations are audit logged

## Test Execution Checklist

### Pre-Test Setup
- [ ] Backend services running (API, database, Redis)
- [ ] Frontend application running
- [ ] S3 bucket configured with proper permissions
- [ ] Test data and files prepared
- [ ] Environment variables configured
- [ ] Test user accounts created

### Startup Flow Tests
- [ ] Valid token access works
- [ ] Invalid/expired token handling
- [ ] Magic link generation and validation
- [ ] Form loading with proper session
- [ ] File upload for all supported types
- [ ] File validation (size, type, security)
- [ ] Upload progress tracking
- [ ] File preview and download
- [ ] File deletion and replacement
- [ ] Auto-save functionality
- [ ] Session persistence across browser restarts
- [ ] Form validation (required fields, conditional logic)
- [ ] Final submission process
- [ ] Post-submission file access

### Investor Flow Tests
- [ ] Deal page access with proper authentication
- [ ] File listing for submitted applications
- [ ] File download via presigned URLs
- [ ] File preview functionality
- [ ] Access control (no draft access)
- [ ] Audit logging verification

### Security Tests
- [ ] Cross-user access prevention
- [ ] Direct S3 URL access blocked
- [ ] Token validation and expiry
- [ ] Session management and timeout
- [ ] File permission enforcement
- [ ] Audit trail completeness

### Performance Tests
- [ ] Large file upload (up to 50MB)
- [ ] Multiple concurrent uploads
- [ ] Network interruption handling
- [ ] Browser compatibility (Chrome, Firefox, Safari, Edge)
- [ ] Mobile device compatibility
- [ ] Accessibility compliance

### Error Handling Tests
- [ ] Network connectivity issues
- [ ] Server errors and timeouts
- [ ] Invalid file types and sizes
- [ ] Session expiry during operations
- [ ] Malformed requests
- [ ] S3 service interruptions

## Success Metrics

### Functional Requirements
- **File Upload Success Rate**: >99% for valid files
- **Authentication Success Rate**: >99% for valid tokens
- **Form Submission Success Rate**: >99% for complete forms
- **File Access Success Rate**: >99% for authorized users

### Performance Requirements
- **File Upload Speed**: <30 seconds for 50MB files
- **Form Load Time**: <3 seconds
- **File Download Time**: <10 seconds for 50MB files
- **Auto-save Response**: <2 seconds

### Security Requirements
- **Zero Cross-user Access**: 0 unauthorized file access incidents
- **Token Security**: 0 token bypass incidents
- **Audit Coverage**: 100% of file operations logged
- **Data Integrity**: 0 data loss incidents

### User Experience Requirements
- **Error Message Clarity**: All errors have clear, actionable messages
- **Progress Feedback**: All long operations show progress
- **Accessibility**: WCAG 2.1 AA compliance
- **Mobile Compatibility**: Full functionality on mobile devices
