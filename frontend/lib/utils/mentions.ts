/**
 * Mention Utilities
 * 
 * Functions for parsing, extracting, and validating mentions in Slate.js content
 */

import { Node, NodeEntry, Range, Point } from 'slate';
import { 
  NoteContent, 
  MentionElement, 
  ExtractedMentions, 
  MentionToken,
  NoteValidationResult,
  OrgUser 
} from '@/lib/types/deal-notes';

/**
 * Extract all mentions from Slate.js content
 */
export function extractMentions(content: NoteContent): ExtractedMentions {
  const user_ids: string[] = [];
  const tokens: MentionToken[] = [];

  const walk = (nodes: Node[]) => {
    for (const node of nodes) {
      if (isMentionElement(node)) {
        const user_id = node.user_id;
        if (user_id && !user_ids.includes(user_id)) {
          user_ids.push(user_id);
          tokens.push({
            user_id,
            name: node.name,
            element: node,
          });
        }
      }

      if ('children' in node && Array.isArray((node as any).children)) {
        walk((node as any).children);
      }
    }
  };

  walk(content);
  return { user_ids, tokens };
}

/**
 * Check if a node is a mention element
 */
export function isMentionElement(node: Node): node is MentionElement {
  return (
    typeof node === 'object' &&
    node !== null &&
    'type' in node &&
    node.type === 'mention' &&
    'user_id' in node &&
    'name' in node
  );
}

/**
 * Validate note content before submission
 */
export function validateNoteContent(
  content: NoteContent,
  orgUsers: OrgUser[]
): NoteValidationResult {
  const errors: string[] = [];
  const orgUserIds = new Set(orgUsers.map(u => u._id));

  // Check if content is empty
  if (!content || content.length === 0) {
    errors.push('Note cannot be empty');
    return { isValid: false, errors };
  }

  // Check if all paragraphs are empty
  const hasContent = content.some(block => {
    if (block.type !== 'paragraph') return false;
    return block.children.some(child => {
      if (isMentionElement(child)) return true;
      return child.text && child.text.trim().length > 0;
    });
  });

  if (!hasContent) {
    errors.push('Note must contain some text or mentions');
  }

  // Check content length limits
  const totalLength = JSON.stringify(content).length;
  if (totalLength > 10000) {
    errors.push('Note is too long (max 10,000 characters)');
  }

  if (content.length > 100) {
    errors.push('Note has too many blocks (max 100)');
  }

  // Validate mentions
  const { user_ids } = extractMentions(content);
  for (const user_id of user_ids) {
    if (!orgUserIds.has(user_id)) {
      errors.push(`Mentioned user ${user_id} is not in this organization`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Create initial empty content
 */
export function createInitialContent(): NoteContent {
  return [
    {
      type: 'paragraph',
      children: [{ text: '' }],
    },
  ];
}

/**
 * Find mention at cursor position
 */
export function findMentionAtCursor(
  editor: any,
  target: Range
): { mention: MentionElement; path: number[] } | null {
  const [block, path] = editor.node(target);
  
  if (block.type !== 'paragraph') return null;

  let start = 0;
  for (const [child, childPath] of Array.from(Node.children(block, path))) {
    const end = start + Node.string(child).length;

    if (
      Point.isPoint(target) &&
      start <= target.offset &&
      target.offset <= end
    ) {
      if (isMentionElement(child)) {
        return { mention: child, path: childPath };
      }
    }

    start = end + 1; // +1 for space
  }

  return null;
}

/**
 * Get mention search query from cursor position
 */
export function getMentionSearchQuery(editor: any, target: Range): string {
  const [block] = editor.node(target);
  
  if (block.type !== 'paragraph') return '';

  let search = '';
  let start = 0;

  for (const child of block.children) {
    const end = start + Node.string(child).length;

    if (Point.isPoint(target) && start <= target.offset && target.offset <= end) {
      if (isMentionElement(child)) {
        return child.name;
      } else {
        // Find the @ symbol and extract search query
        const text = child.text;
        const beforeCursor = text.slice(0, target.offset - start);
        const atIndex = beforeCursor.lastIndexOf('@');
        
        if (atIndex !== -1) {
          search = beforeCursor.slice(atIndex + 1);
        }
      }
    }

    start = end + 1;
  }

  return search;
}

/**
 * Filter users for mention autocomplete
 */
export function filterUsersForMention(
  users: OrgUser[],
  search: string
): OrgUser[] {
  if (!search) return users.slice(0, 10);

  const query = search.toLowerCase();
  return users
    .filter(user => 
      user.name.toLowerCase().includes(query) ||
      user.email.toLowerCase().includes(query)
    )
    .slice(0, 10);
}

/**
 * Create a mention element
 */
export function createMentionElement(user: OrgUser): MentionElement {
  return {
    type: 'mention',
    user_id: user._id,
    name: user.name,
    children: [{ text: '' }],
  };
}

/**
 * Insert mention at cursor position
 */
export function insertMention(editor: any, user: OrgUser) {
  const mention = createMentionElement(user);
  
  // Remove the @ symbol and search text
  const { selection } = editor;
  if (selection) {
    const [block] = editor.node(selection);
    const text = Node.string(block);
    const beforeCursor = text.slice(0, selection.offset);
    const atIndex = beforeCursor.lastIndexOf('@');
    
    if (atIndex !== -1) {
      // Delete from @ to cursor
      editor.deleteBackward('character', { distance: selection.offset - atIndex });
    }
  }

  // Insert the mention
  editor.insertNode(mention);
  editor.insertText(' ');
}

/**
 * Convert legacy content to structured content
 */
export function convertLegacyContent(content: string): NoteContent {
  if (!content || typeof content !== 'string') {
    return createInitialContent();
  }

  // Simple conversion: wrap in paragraph
  return [
    {
      type: 'paragraph',
      children: [{ text: content }],
    },
  ];
}

/**
 * Get plain text from structured content (for previews, etc.)
 */
export function getPlainText(content: NoteContent): string {
  const texts: string[] = [];

  const walk = (nodes: Node[]) => {
    for (const node of nodes) {
      if (isMentionElement(node)) {
        texts.push(`@${node.name}`);
      } else if (Node.string(node)) {
        texts.push(Node.string(node));
      }

      if ('children' in node && Array.isArray((node as any).children)) {
        walk((node as any).children);
      }
    }
  };

  walk(content);
  return texts.join(' ');
} 