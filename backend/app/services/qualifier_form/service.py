import time
from typing import Any, Dict, List, Optional

from bson import ObjectId

from app.models.form import Form
from app.models.qualifier_form import QualifierFormConfig, QualifierFormStats
from app.services.base import BaseService
from app.services.qualifier_form.interfaces import QualifierFormServiceInterface
from app.core.logging import get_logger
from app.services.factory import (
    get_form_service,
    get_sharing_service,
    get_trigger_service,
    get_queue_service
)


logger = get_logger(__name__)


class QualifierFormService(BaseService, QualifierFormServiceInterface):
    """Implementation of the qualifier form service."""

    def __init__(self, db=None):
        super().__init__()
        self.db = db
        self.form_service = None
        self.sharing_service = None
        self.trigger_service = None
        self.queue_service = None

    async def initialize(self) -> None:
        """Initialize service resources."""
        if self.db is None:
            from app.core.database import get_database
            # Get the database instance from the async generator
            async for db in get_database():
                self.db = db
                break
            if self.db is None:
                raise RuntimeError("Failed to initialize database connection")

        # Initialize other services
        self.form_service = await get_form_service()
        self.sharing_service = await get_sharing_service()
        self.trigger_service = await get_trigger_service()
        self.queue_service = await get_queue_service()

        self.logger.info("Qualifier form service initialized")

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        pass

    def _timestamp_ms(self) -> int:
        """Get current timestamp in milliseconds."""
        return int(time.time() * 1000)

    async def create_qualifier_form(
        self,
        name: str,
        description: str,
        org_id: str,
        sections: Optional[List[str]] = None,
        default_section_ids: Optional[List[str]] = None,
        is_active: bool = True,
        sharing_config_id: Optional[str] = None,
        trigger_config_ids: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Create a new qualifier form."""
        try:
            # First, create the base form
            form = await self.form_service.create_form(
                name=name,
                description=description,
                sections=[ObjectId(s) for s in (sections or [])],
                default_section_ids=[ObjectId(s) for s in (default_section_ids or [])],
                is_active=is_active,
                org_id=org_id  # Pass org_id from X-ORG-ID header
            )

            # Create qualifier form config
            now = self._timestamp_ms()
            qualifier_config = {
                "form_id": form.id,
                "org_id": ObjectId(org_id),
                "is_active": is_active,
                "sharing_config_id": ObjectId(sharing_config_id) if sharing_config_id else None,
                "trigger_config_ids": [ObjectId(t) for t in (trigger_config_ids or [])],
                "created_at": now,
                "updated_at": now
            }

            # Insert into database
            qualifier_config = QualifierFormConfig(**qualifier_config)
            await qualifier_config.save()

            # Create stats record
            stats = {
                "form_id": form.id,
                "views": 0,
                "submissions": 0,
                "conversion_rate": 0.0,
                "avg_completion_time": 0,
                "sharing_stats": {},
                "updated_at": now
            }
            stats = QualifierFormStats(**stats)
            await stats.save()

            # Return combined result
            return {
                "id": str(form.id),
                "config_id": str(qualifier_config.id),
                "name": name,
                "description": description,
                "org_id": org_id,
                "is_active": is_active,
                "sections": sections or [],
                "default_section_ids": default_section_ids or [],
                "sharing_config_id": sharing_config_id,
                "trigger_config_ids": trigger_config_ids or [],
                "created_at": now,
                "updated_at": now
            }
        except Exception as e:
            await self.handle_error(e, {
                "name": name,
                "description": description,
                "org_id": org_id,
                "error": "Failed to create qualifier form"
            })

    async def get_qualifier_form(
        self,
        form_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get qualifier form by ID."""
        try:
            # Get base form
            logger.info(f"Getting qualifier form with ID: {form_id}")
            form = await Form.find_one({"_id": form_id})
            if not form:
                return None

            # Get qualifier form config using the model's find_one method
            config_model = await QualifierFormConfig.find_one({"form_id": ObjectId(form_id)})
            if not config_model:
                return None

            # Convert to dict with proper serialization
            config = config_model.model_dump(by_alias=True)

            # Combine and return
            result = {
                "id": str(form.id),
                "config_id": config["_id"],  # Already a string from model_dump
                "name": form.name,
                "description": form.description,
                "org_id": str(form.org_id),
                "is_active": form.is_active and config["is_active"],
                "version": form.version,
                "sections": [str(s) for s in form.sections],
                "default_section_ids": [str(s) for s in form.default_section_ids],
                "sharing_config_id": config["sharing_config_id"],  # Already a string from model_dump
                "trigger_config_ids": config["trigger_config_ids"],  # Already strings from model_dump
                "created_at": form.created_at,
                "updated_at": form.updated_at
            }

            return result
        except Exception as e:
            await self.handle_error(e, {
                "form_id": form_id,
                "error": "Failed to get qualifier form"
            })

    async def get_qualifier_form_with_details(
        self,
        form_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get qualifier form with all details including sections, questions, sharing config, and triggers.
        Uses populate functionality for efficient data retrieval.
        """
        try:
            # Get base form
            logger.info(f"Attempting to get form with ID: {form_id}")
            try:
                form = await Form.find_one({"_id": ObjectId(form_id)})
                logger.info(f"Form query result: {form}")
            except Exception as form_error:
                logger.error(f"Error getting form: {str(form_error)}")
                return None

            if not form:
                logger.warning(f"No form found with ID: {form_id}")
                return None

            # Get qualifier form config
            logger.info(f"Attempting to get qualifier form config for form ID: {form_id}")
            try:
                config_model = await QualifierFormConfig.find_one({"form_id": ObjectId(form_id)})
                logger.info(f"Config query result: {config_model}")
            except Exception as config_error:
                logger.error(f"Error getting config: {str(config_error)}")
                return None

            if not config_model:
                logger.warning(f"No config found for form ID: {form_id}")
                return None

            # Convert to dict with proper serialization
            config = config_model.model_dump(by_alias=True)
            logger.info(f"Serialized config: {config}")

            # Populate form with sections and questions
            try:
                await form.populate(["sections"], depth=2)  # depth=2 to get questions within sections
                logger.info(f"Form populated with sections: {form.sections}")
            except Exception as populate_error:
                logger.error(f"Error populating form: {str(populate_error)}")
                return None

            # Convert sections to dict format with populated questions
            populated_sections = []
            for section in form.sections:
                try:
                    section_dict = section.model_dump(by_alias=True)
                    section_dict["_id"] = str(section_dict["_id"])
                    section_dict["form_id"] = str(section_dict["form_id"])

                    # Convert questions to dict format
                    questions = []
                    for question in section.questions:
                        question_dict = question.model_dump(by_alias=True)
                        question_dict["_id"] = str(question_dict["_id"])
                        question_dict["section_id"] = str(question_dict["section_id"])
                        questions.append(question_dict)

                    section_dict["questions"] = questions
                    populated_sections.append(section_dict)
                except Exception as section_error:
                    logger.error(f"Error processing section: {str(section_error)}")
                    continue

            # Get sharing config if available
            sharing_config = None
            if config.get("sharing_config_id"):
                try:
                    sharing_config = await self.sharing_service.get_sharing_config(str(config["sharing_config_id"]))
                    logger.info(f"Retrieved sharing config: {sharing_config}")
                except Exception as sharing_error:
                    logger.error(f"Error getting sharing config: {str(sharing_error)}")

            # Get trigger configs if available
            trigger_configs = []
            for trigger_id in config.get("trigger_config_ids", []):
                try:
                    trigger_config = await self.trigger_service.get_trigger_config(str(trigger_id))
                    if trigger_config:
                        trigger_configs.append(trigger_config)
                except Exception as trigger_error:
                    logger.error(f"Error getting trigger config: {str(trigger_error)}")

            # Get stats
            try:
                stats_model = await QualifierFormStats.find_one({"form_id": ObjectId(form_id)})
                stats = stats_model.model_dump(by_alias=True) if stats_model else None
                logger.info(f"Retrieved stats: {stats}")
            except Exception as stats_error:
                logger.error(f"Error getting stats: {str(stats_error)}")
                stats = None

            # Get form data
            try:
                form_data = form.model_dump(by_alias=True)
                logger.info(f"Serialized form data: {form_data}")
            except Exception as form_data_error:
                logger.error(f"Error serializing form data: {str(form_data_error)}")
                return None

            # Combine and return
            result = {
                "id": form_data["_id"],
                "config_id": config["_id"],
                "name": form_data["name"],
                "description": form_data["description"],
                "org_id": form_data["org_id"],
                "is_active": form_data["is_active"] and config["is_active"],
                "version": form_data["version"],
                "sections": populated_sections,
                "default_section_ids": [str(s) for s in form.default_section_ids],
                "sharing_config": sharing_config,
                "trigger_configs": trigger_configs,
                "stats": stats,
                "created_at": form_data["created_at"],
                "updated_at": form_data["updated_at"]
            }

            logger.info(f"Final result: {result}")
            return result

        except Exception as e:
            logger.error(f"Unexpected error in get_qualifier_form_with_details: {str(e)}")
            await self.handle_error(e, {
                "form_id": form_id,
                "error": "Failed to get qualifier form with details"
            })
            return None

    async def update_qualifier_form(
        self,
        form_id: str,
        updates: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Update a qualifier form."""
        try:
            # Separate updates for base form and qualifier config
            form_updates = {}
            config_updates = {}

            # Map updates to the appropriate object
            if "name" in updates:
                form_updates["name"] = updates["name"]
            if "description" in updates:
                form_updates["description"] = updates["description"]
            if "is_active" in updates:
                form_updates["is_active"] = updates["is_active"]
                config_updates["is_active"] = updates["is_active"]
            if "sections" in updates:
                form_updates["sections"] = [ObjectId(s) for s in updates["sections"]]
            if "default_section_ids" in updates:
                form_updates["default_section_ids"] = [ObjectId(s) for s in updates["default_section_ids"]]
            if "sharing_config_id" in updates:
                config_updates["sharing_config_id"] = ObjectId(updates["sharing_config_id"]) if updates["sharing_config_id"] else None
            if "trigger_config_ids" in updates:
                config_updates["trigger_config_ids"] = [ObjectId(t) for t in updates["trigger_config_ids"]]

            # Update base form if needed
            if form_updates:
                form_updates["updated_at"] = self._timestamp_ms()
                form = await self.form_service.update_form(form_id, form_updates)
                if not form:
                    return None

            # Update qualifier config if needed
            if config_updates:
                config_updates["updated_at"] = self._timestamp_ms()
                result = await self.db.qualifier_form_configs.find_one_and_update(
                    {"form_id": ObjectId(form_id)},
                    {"$set": config_updates},
                    return_document=True
                )
                if not result:
                    return None

            # Return updated form
            return await self.get_qualifier_form(form_id)
        except Exception as e:
            await self.handle_error(e, {
                "form_id": form_id,
                "updates": updates,
                "error": "Failed to update qualifier form"
            })

    async def delete_qualifier_form(
        self,
        form_id: str
    ) -> bool:
        """Delete a qualifier form."""
        try:
            # Get qualifier form config
            config = await self.db.qualifier_form_configs.find_one({"form_id": ObjectId(form_id)})
            if not config:
                return False

            # Delete sharing config if exists
            if config.get("sharing_config_id"):
                await self.sharing_service.delete_sharing_config(str(config["sharing_config_id"]))

            # Delete trigger configs
            for trigger_id in config.get("trigger_config_ids", []):
                await self.trigger_service.delete_trigger_config(str(trigger_id))

            # Delete qualifier form config
            await self.db.qualifier_form_configs.delete_one({"_id": config["_id"]})

            # Delete stats
            await self.db.qualifier_form_stats.delete_one({"form_id": ObjectId(form_id)})

            # Delete base form
            success = await self.form_service.delete_form(form_id)

            return success
        except Exception as e:
            await self.handle_error(e, {
                "form_id": form_id,
                "error": "Failed to delete qualifier form"
            })

    async def list_qualifier_forms(
        self,
        org_id: str,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> List[Dict[str, Any]]:
        """List qualifier forms for an organization."""
        try:
            # Get qualifier form configs for this org using the model's find_many method
            config_models = await QualifierFormConfig.find_many({"org_id": ObjectId(org_id)})
            if not config_models:
                return []

            # Convert to dicts with proper serialization
            configs = [config.model_dump(by_alias=True) for config in config_models]

            # Get form IDs
            form_ids = [config["form_id"] for config in configs]

            # Get base forms using the model's find_many method
            forms_query = {"_id": {"$in": form_ids}}
            if search:
                forms_query["$or"] = [
                    {"name": {"$regex": search, "$options": "i"}},
                    {"description": {"$regex": search, "$options": "i"}}
                ]
            if is_active is not None:
                forms_query["is_active"] = is_active

            form_models = await Form.find_many(
                query=forms_query,
                sort=[("created_at", -1)],
                skip=skip,
                limit=limit
            )

            # Convert to dicts with proper serialization
            forms = [form.model_dump(by_alias=True) for form in form_models]

            # Create a map of form ID to config
            config_map = {config["form_id"]: config for config in configs}

            # Combine and return
            results = []
            for form in forms:
                config = config_map.get(form["_id"])
                if config:
                    results.append({
                        "id": form["_id"],  # Already a string from model_dump
                        "config_id": config["_id"],  # Already a string from model_dump
                        "name": form["name"],
                        "description": form["description"],
                        "org_id": form["org_id"],  # Already a string from model_dump
                        "is_active": form["is_active"] and config["is_active"],
                        "version": form["version"],
                        "sharing_config_id": config["sharing_config_id"],  # Already a string from model_dump
                        "trigger_config_ids": config["trigger_config_ids"],  # Already strings from model_dump
                        "created_at": form["created_at"],
                        "updated_at": form["updated_at"]
                    })

            return results
        except Exception as e:
            await self.handle_error(e, {
                "org_id": org_id,
                "error": "Failed to list qualifier forms"
            })

    async def configure_sharing(
        self,
        form_id: str,
        sharing_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Configure sharing for a qualifier form."""
        try:
            # Get qualifier form config
            config = await self.db.qualifier_form_configs.find_one({"form_id": ObjectId(form_id)})
            if not config:
                raise ValueError(f"Qualifier form config not found for form {form_id}")

            # Get or create sharing config
            if config.get("sharing_config_id"):
                # Update existing sharing config
                sharing = await self.sharing_service.update_sharing_config(
                    str(config["sharing_config_id"]),
                    sharing_config
                )
            else:
                # Create new sharing config
                sharing = await self.sharing_service.create_sharing_config(
                    resource_type="qualifier_form",
                    resource_id=form_id,
                    org_id=str(config["org_id"]),
                    **sharing_config
                )

                # Update qualifier form config with sharing config ID
                await self.db.qualifier_form_configs.update_one(
                    {"_id": config["_id"]},
                    {"$set": {
                        "sharing_config_id": sharing["_id"],
                        "updated_at": self._timestamp_ms()
                    }}
                )

            # Return updated form
            return await self.get_qualifier_form_with_details(form_id)
        except Exception as e:
            await self.handle_error(e, {
                "form_id": form_id,
                "sharing_config": sharing_config,
                "error": "Failed to configure sharing"
            })

    async def configure_triggers(
        self,
        form_id: str,
        trigger_configs: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Configure triggers for a qualifier form."""
        try:
            # Get qualifier form config
            config = await self.db.qualifier_form_configs.find_one({"form_id": ObjectId(form_id)})
            if not config:
                raise ValueError(f"Qualifier form config not found for form {form_id}")

            # Get existing trigger IDs
            existing_trigger_ids = config.get("trigger_config_ids", [])

            # Process each trigger config
            new_trigger_ids = []
            for trigger_config in trigger_configs:
                trigger_id = trigger_config.pop("id", None)

                if trigger_id:
                    # Update existing trigger
                    trigger = await self.trigger_service.update_trigger_config(
                        trigger_id,
                        trigger_config
                    )
                    if trigger:
                        new_trigger_ids.append(trigger["_id"])
                else:
                    # Create new trigger
                    trigger = await self.trigger_service.create_trigger_config(
                        resource_type="qualifier_form",
                        resource_id=form_id,
                        org_id=str(config["org_id"]),
                        **trigger_config
                    )
                    new_trigger_ids.append(trigger["_id"])

            # Delete triggers that are no longer needed
            for trigger_id in existing_trigger_ids:
                if trigger_id not in new_trigger_ids:
                    await self.trigger_service.delete_trigger_config(str(trigger_id))

            # Update qualifier form config with new trigger IDs
            await self.db.qualifier_form_configs.update_one(
                {"_id": config["_id"]},
                {"$set": {
                    "trigger_config_ids": new_trigger_ids,
                    "updated_at": self._timestamp_ms()
                }}
            )

            # Return updated form
            return await self.get_qualifier_form_with_details(form_id)
        except Exception as e:
            await self.handle_error(e, {
                "form_id": form_id,
                "trigger_configs": trigger_configs,
                "error": "Failed to configure triggers"
            })

    async def get_qualifier_form_stats(
        self,
        form_id: str
    ) -> Dict[str, Any]:
        """Get statistics for a qualifier form."""
        try:
            # Get stats using the model's find_one method
            stats_model = await QualifierFormStats.find_one({"form_id": ObjectId(form_id)})

            if not stats_model:
                return {
                    "form_id": form_id,
                    "views": 0,
                    "submissions": 0,
                    "conversion_rate": 0.0,
                    "avg_completion_time": 0,
                    "sharing_stats": {}
                }

            # Convert to dict with proper serialization
            stats = stats_model.model_dump(by_alias=True)

            # Get sharing stats
            sharing_stats = await self.sharing_service.get_sharing_stats("qualifier_form", form_id)

            # Combine and return
            result = {
                "form_id": form_id,
                "views": stats.get("views", 0),
                "submissions": stats.get("submissions", 0),
                "conversion_rate": stats.get("conversion_rate", 0.0),
                "avg_completion_time": stats.get("avg_completion_time", 0),
                "sharing_stats": sharing_stats
            }

            return result
        except Exception as e:
            await self.handle_error(e, {
                "form_id": form_id,
                "error": "Failed to get qualifier form stats"
            })

    async def submit_qualifier_form(
        self,
        form_id: str,
        answers: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Submit a qualifier form.

        Args:
            form_id: ID of the qualifier form
            answers: Form answers
            metadata: Optional metadata to include with the submission

        Returns:
            Submission details
        """
        try:
            # Validate submission
            validation = await self.form_service.validate_submission(form_id, answers)
            if not validation["valid"]:
                raise ValueError(f"Invalid submission: {validation['errors']}")

            # Submit to base form with metadata
            submission = await self.form_service.submit_form(
                form_id=form_id,
                answers=answers,
                metadata=metadata
            )

            # Get qualifier form config
            config = await self.db.qualifier_form_configs.find_one({"form_id": ObjectId(form_id)})
            if not config:
                raise ValueError(f"Qualifier form config not found for form {form_id}")

            # Update stats
            now = self._timestamp_ms()
            await self.db.qualifier_form_stats.update_one(
                {"form_id": ObjectId(form_id)},
                {"$inc": {"submissions": 1}, "$set": {"updated_at": now}}
            )

            # Calculate conversion rate
            stats = await self.db.qualifier_form_stats.find_one({"form_id": ObjectId(form_id)})
            if stats and stats.get("views", 0) > 0:
                conversion_rate = (stats.get("submissions", 0) / stats.get("views", 0)) * 100
                await self.db.qualifier_form_stats.update_one(
                    {"form_id": ObjectId(form_id)},
                    {"$set": {"conversion_rate": conversion_rate}}
                )

            # Process triggers
            if config.get("trigger_config_ids"):
                # Queue event processing
                await self.queue_service.enqueue_job(
                    job_type="process_resource_event",
                    payload={
                        "resource_type": "qualifier_form",
                        "resource_id": form_id,
                        "event_type": "submission",
                        "event_data": {
                            "submission_id": str(submission.id),
                            "form_id": form_id,
                            "answers": answers,
                            "metadata": metadata or {},
                            "timestamp": now
                        }
                    }
                )

            # Add to queue for processing (including exclusion filter check)
            await self.queue_service.enqueue_job(
                job_type="process_form_submission",
                payload={
                    "submission_id": str(submission.id),
                    "form_id": form_id,
                    "org_id": str(config["org_id"]),
                    "answers": answers  # Include answers for exclusion filter check
                }
            )

            return {
                "submission_id": str(submission.id),
                "form_id": form_id,
                "timestamp": now,
                "metadata": metadata or {},
                "status": "success"
            }
        except ValueError as e:
            raise e
        except Exception as e:
            await self.handle_error(e, {
                "form_id": form_id,
                "error": "Failed to submit qualifier form"
            })

    async def get_qualifier_form_submissions(
        self,
        form_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get submissions for a qualifier form."""
        try:
            # Get submissions from base form
            submissions = await self.form_service.get_form_submissions(form_id, skip, limit)

            # Convert to dicts
            results = []
            for submission in submissions:
                results.append({
                    "id": str(submission.id),
                    "form_id": str(submission.form_id),
                    "answers": submission.answers,
                    "created_at": submission.created_at,
                    "updated_at": submission.updated_at
                })

            return results
        except Exception as e:
            await self.handle_error(e, {
                "form_id": form_id,
                "error": "Failed to get qualifier form submissions"
            })
