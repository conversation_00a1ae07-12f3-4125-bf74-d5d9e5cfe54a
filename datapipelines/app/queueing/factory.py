"""
Queue Service Factory

This module provides factory functions for creating queue services with different
backend configurations.
"""

from typing import Optional

from app.configs import get_logger, settings
from app.queueing.backends.redis_backend import RedisQueueBackend
from app.queueing.interfaces import QueueBackend
from app.queueing.service import TractionXQueueService

logger = get_logger(__name__)


async def create_redis_backend(
    redis_url: Optional[str] = None,
    key_prefix: str = settings.REDIS_KEY_PREFIX,
) -> RedisQueueBackend:
    """
    Create a Redis queue backend.

    Args:
        redis_url: Redis connection URL (defaults to settings.redis_connection_string)
        key_prefix: Prefix for Redis keys

    Returns:
        Configured Redis backend
    """
    if redis_url is None:
        redis_url = settings.redis_connection_string

    backend = RedisQueueBackend(redis_url=redis_url, key_prefix=key_prefix)
    logger.info(f"Created Redis queue backend with URL: {redis_url}")

    return backend


async def create_queue_service(
    backend: Optional[QueueBackend] = None, backend_type: str = "redis", **kwargs
) -> TractionXQueueService:
    """
    Create a queue service with the specified backend.

    Args:
        backend: Pre-configured backend instance
        backend_type: Type of backend to create ("redis")
        **kwargs: Additional arguments for backend creation

    Returns:
        Configured queue service
    """
    if backend is None:
        if backend_type == "redis":
            backend = await create_redis_backend(**kwargs)  # type: ignore
        else:
            raise ValueError(f"Unsupported backend type: {backend_type}")

    service = TractionXQueueService(backend)  # type: ignore
    logger.info(f"Created queue service with {backend_type} backend")

    return service


# Convenience functions for common configurations


async def create_development_queue_service() -> TractionXQueueService:
    """Create a queue service optimized for development."""
    return await create_queue_service(
        backend_type="redis",
        key_prefix="dev:queue",
    )


async def create_production_queue_service() -> TractionXQueueService:
    """Create a queue service optimized for production."""
    return await create_queue_service(
        backend_type="redis",
        key_prefix="prod:queue",
    )


async def create_test_queue_service() -> TractionXQueueService:
    """Create a queue service optimized for testing."""
    return await create_queue_service(
        backend_type="redis",
        key_prefix="test:queue",
    )
