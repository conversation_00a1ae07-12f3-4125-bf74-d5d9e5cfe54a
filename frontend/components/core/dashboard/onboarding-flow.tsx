"use client"

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Star, 
  Target, 
  FileText, 
  Share2,
  ArrowRight,
  CheckCircle,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
// Onboarding steps defined inline
const onboardingSteps = [
  {
    id: 1,
    title: 'Create Your First Form',
    description: 'Set up a submission form to start collecting startup applications',
    completed: false,
    action: 'Create Form',
    icon: 'FileText',
    href: '/forms/new'
  },
  {
    id: 2, 
    title: 'Build Investment Thesis',
    description: 'Define your investment criteria and scoring rules',
    completed: false,
    action: 'Build Thesis',
    icon: 'Target',
    href: '/thesis/new'
  },
  {
    id: 3,
    title: 'Share Your Form',
    description: 'Get your unique form link and start receiving submissions',
    completed: false,
    action: 'Get Link',
    icon: 'Share2',
    href: '/forms'
  }
];
import Link from 'next/link';
import { OnboardingStatus } from '@/lib/api/dashboard-api';

interface OnboardingFlowProps {
  className?: string;
  userName?: string;
  onboardingStatus: OnboardingStatus;
  onDismiss?: () => void;
  onRefresh?: () => void;
}

const iconMap = {
  welcome: Star,
  target: Target,
  form: FileText,
  share: Share2,
};

export function OnboardingFlow({
  className,
  userName,
  onboardingStatus,
  onDismiss,
  onRefresh
}: OnboardingFlowProps) {
  const [isVisible, setIsVisible] = useState(true);

  // Determine current step and completed steps based on onboarding status
  const completedSteps: number[] = [];
  let currentStep = 0;

  if (onboardingStatus.has_form) {
    completedSteps.push(1);
    currentStep = 1;
  }

  if (onboardingStatus.has_thesis) {
    completedSteps.push(2);
    currentStep = 2;
  }

  // If both are complete, show the sharing step
  if (onboardingStatus.has_form && onboardingStatus.has_thesis) {
    currentStep = 2; // Share step is the final one
  }

  const handleStepComplete = (stepId: number) => {
    // Refresh dashboard data after completing a step
    if (onRefresh) {
      onRefresh();
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    setTimeout(() => {
      onDismiss?.();
    }, 300);
  };

  const progress = (completedSteps.length / onboardingSteps.length) * 100;

  const containerVariants = {
    hidden: { opacity: 0, scale: 0.95, y: 20 },
    visible: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: {
        duration: 0.5,
        ease: [0.25, 0.46, 0.45, 0.94] as const
      }
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      y: -20,
      transition: {
        duration: 0.3,
        ease: [0.25, 0.46, 0.45, 0.94] as const
      }
    }
  };

  const stepVariants = {
    hidden: { opacity: 0, x: 20 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: {
        duration: 0.4,
        ease: [0.25, 0.46, 0.45, 0.94] as const
      }
    },
    exit: {
      opacity: 0,
      x: -20,
      transition: {
        duration: 0.3,
        ease: [0.25, 0.46, 0.45, 0.94] as const
      }
    }
  };

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        className={cn("relative", className)}
      >
        <Card className="relative overflow-hidden border-2 border-slate-200 dark:border-slate-700">
          {/* Background gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950/20 dark:via-blue-950/20 dark:to-indigo-950/20" />
          
          {/* Dismiss button */}
          <Button
            onClick={handleDismiss}
            variant="ghost"
            size="sm"
            className="absolute right-4 top-4 z-10 size-8 p-0"
          >
            <X className="size-4" />
          </Button>

          <CardHeader className="relative pb-6">
            <div className="flex items-center gap-4">
              <div className="rounded-xl bg-blue-100 p-3 dark:bg-blue-900">
                <Star className="size-8 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <CardTitle className="text-2xl font-bold text-foreground">
                  Welcome{userName ? `, ${userName}` : ''}! Let's get you started
                </CardTitle>
                <p className="mt-1 text-base text-muted-foreground">
                  Set up your investment workflow in just a few steps
                </p>
              </div>
            </div>

            {/* Progress bar */}
            <div className="mt-6">
              <div className="mb-3 flex items-center justify-between">
                <span className="text-base font-semibold text-foreground">
                  Progress
                </span>
                <span className="text-base text-muted-foreground">
                  {completedSteps.length} of {onboardingSteps.length} completed
                </span>
              </div>
              <Progress value={progress} className="h-3" />
            </div>
          </CardHeader>

          <CardContent className="relative space-y-6">
            {/* Steps */}
            <div className="space-y-4">
              {onboardingSteps.map((step, index) => {
                const IconComponent = iconMap[step.icon as keyof typeof iconMap] || Star;
                const isCompleted = completedSteps.includes(step.id);
                const isCurrent = index === currentStep;
                const isAccessible = index <= currentStep || isCompleted;

                return (
                  <AnimatePresence key={step.id}>
                    <motion.div
                      variants={stepVariants}
                      initial="hidden"
                      animate="visible"
                      className={cn(
                        "flex items-center gap-6 rounded-xl border p-6 transition-all duration-200",
                        isCompleted && "border-emerald-200 bg-emerald-50 dark:border-emerald-800 dark:bg-emerald-950/20",
                        isCurrent && !isCompleted && "border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20",
                        !isAccessible && "opacity-50"
                      )}
                    >
                      {/* Step icon */}
                      <div className={cn(
                        "flex size-12 shrink-0 items-center justify-center rounded-full",
                        isCompleted && "bg-emerald-100 dark:bg-emerald-900",
                        isCurrent && !isCompleted && "bg-blue-100 dark:bg-blue-900",
                        !isAccessible && "bg-muted"
                      )}>
                        {isCompleted ? (
                          <CheckCircle className="size-6 text-emerald-600 dark:text-emerald-400" />
                        ) : (
                          <IconComponent className={cn(
                            "size-6",
                            isCurrent && "text-blue-600 dark:text-blue-400",
                            !isAccessible && "text-muted-foreground"
                          )} />
                        )}
                      </div>

                      {/* Step content */}
                      <div className="min-w-0 flex-1">
                        <div className="mb-2 flex items-center gap-3">
                          <h4 className={cn(
                            "text-lg font-semibold",
                            isCompleted && "text-emerald-700 dark:text-emerald-300",
                            isCurrent && !isCompleted && "text-blue-700 dark:text-blue-300"
                          )}>
                            {step.title}
                          </h4>
                          {isCurrent && !isCompleted && (
                            <Badge variant="secondary" className="px-3 py-1 text-sm">
                              Current
                            </Badge>
                          )}
                          {isCompleted && (
                            <Badge variant="secondary" className="bg-emerald-100 px-3 py-1 text-sm text-emerald-700">
                              Complete
                            </Badge>
                          )}
                        </div>
                        <p className="text-base text-muted-foreground">
                          {step.description}
                        </p>
                      </div>

                      {/* Action button */}
                      {step.action && step.href && isAccessible && !isCompleted && (
                        <div className="shrink-0">
                          <Link href={step.href}>
                            <Button
                              size="sm"
                              variant={isCurrent ? "default" : "outline"}
                              onClick={() => handleStepComplete(step.id)}
                              className="gap-2"
                            >
                              {step.action}
                              <ArrowRight className="size-4" />
                            </Button>
                          </Link>
                        </div>
                      )}
                    </motion.div>
                  </AnimatePresence>
                );
              })}
            </div>

            {/* Completion message */}
            {completedSteps.length === onboardingSteps.length && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="rounded-lg border border-emerald-200 bg-emerald-50 p-4 text-center dark:border-emerald-800 dark:bg-emerald-950/20"
              >
                <CheckCircle className="mx-auto mb-2 size-8 text-emerald-600 dark:text-emerald-400" />
                <h4 className="mb-1 font-medium text-emerald-700 dark:text-emerald-300">
                  Setup Complete!
                </h4>
                <p className="text-sm text-emerald-600 dark:text-emerald-400">
                  You're all set to start using TractionX for your investment workflow.
                </p>
              </motion.div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
}
