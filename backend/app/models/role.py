from datetime import datetime
from typing import List

from pydantic import Field, field_validator

from app.models.base import TractionXModel
from app.utils.common import ObjectIdField, PyObjectId


class Permission(TractionXModel):
    """
    Represents a permission for a specific resource and action within the system.
    """

    resource: str  # users, forms, theses, deals, scoring_config, submissions, settings
    action: str  # create, view, edit, delete, share, assign, comment, score

    @field_validator("resource")
    def normalize_resource(cls, v: str) -> str:
        """Normalize resource name to lowercase and remove special characters."""
        return v.lower().replace("-", "_").replace(" ", "_")

    @field_validator("action")
    def normalize_action(cls, v: str) -> str:
        """Normalize action name to lowercase and remove special characters."""
        return v.lower().replace("-", "_").replace(" ", "_")


class Role(TractionXModel):
    """
    Represents a user role within an organization, including permissions and system status.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    org_id: ObjectIdField
    name: str
    description: str
    permissions: List[Permission] = []
    is_system: bool = False  # True for system roles (admin, analyst, viewer)
    created_at: int = Field(default_factory=lambda: int(datetime.utcnow().timestamp()))
    updated_at: int = Field(default_factory=lambda: int(datetime.utcnow().timestamp()))
