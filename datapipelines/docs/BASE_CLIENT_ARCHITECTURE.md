# Base Client Architecture - TractionX Data Pipeline Service

This document describes the centralized client architecture for the TractionX Data Pipeline Service, providing a unified approach to API client management.

## Overview

The base client system provides a foundation for all API clients in the platform, offering:
- **Async HTTP client management** using httpx
- **Automatic retries** with exponential backoff
- **Rate limiting** support
- **Error handling** and logging
- **Response validation**
- **Health checks**
- **Centralized client management**

## Architecture

```
BaseClient (Abstract)
├── BrightDataClient
├── TogetherClient
├── ExampleAPIClient
└── [Future Clients]

ClientManager
└── Manages all client instances
```

## Core Components

### 1. BaseClient (`app/services/base_client.py`)

**Abstract base class** that all API clients inherit from.

#### Key Features:
- **Async HTTP client** using httpx
- **Automatic retries** with exponential backoff
- **Rate limiting** with configurable limits
- **Error handling** for different HTTP status codes
- **Request/response logging**
- **Health check** interface
- **Context manager** support

#### Configuration Options:
```python
super().__init__(
    base_url="https://api.example.com/v1",
    api_key="your_api_key",
    timeout=60.0,
    max_retries=3,
    retry_delay=2.0,
    rate_limit=100,  # requests per minute
    headers={"Content-Type": "application/json"}
)
```

#### HTTP Methods:
```python
await client.get("/endpoint", params={"key": "value"})
await client.post("/endpoint", data={"key": "value"})
await client.put("/endpoint", data={"key": "value"})
await client.delete("/endpoint")
await client.patch("/endpoint", data={"key": "value"})
```

#### Response Format:
```python
{
    "success": True,
    "data": response_data,
    "status_code": 200,
    "response_time_ms": 150,
    "attempts": 1
}
```

### 2. ClientManager (`app/services/base_client.py`)

**Centralized manager** for multiple client instances.

#### Features:
- **Client registration** and lifecycle management
- **Bulk initialization** and cleanup
- **Health check** aggregation
- **Error handling** for client operations

#### Usage:
```python
from app.services.base_client import get_client_manager

manager = get_client_manager()
manager.register_client("brightdata", brightdata_client)
manager.register_client("together", together_client)

await manager.initialize_all()
health_status = await manager.health_check_all()
await manager.cleanup_all()
```

## Implementation Examples

### 1. BrightData Client

```python
class BrightDataClient(BaseClient):
    def __init__(self):
        super().__init__(
            base_url=settings.BRIGHTDATA_BASE_URL,
            api_key=settings.BRIGHTDATA_API_KEY,
            timeout=60.0,  # Longer timeout for JS rendering
            max_retries=3,
            retry_delay=2.0,
            rate_limit=settings.BRIGHTDATA_RATE_LIMIT,
            headers={"Content-Type": "application/json"}
        )
        self.web_unlocker_zone = settings.BRIGHTDATA_WEB_UNLOCKER_ZONE

    async def fetch_webpage(self, url: str, render_js: bool = True):
        data = {
            "zone": self.web_unlocker_zone,
            "url": url,
            "render": render_js,
            "format": "raw"
        }
        return await self.post("/request", data=data)

    async def health_check(self) -> Dict[str, Any]:
        result = await self.get("/status")
        return {
            "success": result["success"],
            "service": "brightdata",
            "status": "healthy" if result["success"] else "unhealthy"
        }
```

### 2. Together AI Client

```python
class TogetherClient(BaseClient):
    def __init__(self):
        super().__init__(
            base_url="https://api.together.xyz/v1",
            api_key=settings.TOGETHER_API_KEY,
            timeout=120.0,  # Longer timeout for LLM generation
            max_retries=3,
            retry_delay=2.0,
            rate_limit=None,  # Together AI handles rate limiting
            headers={"Content-Type": "application/json"}
        )
        self.default_model = "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free"

    async def create_chat_completion(self, messages: List[Dict[str, str]]):
        data = {
            "model": self.default_model,
            "messages": messages,
            "max_tokens": 2000,
            "temperature": 0.3
        }
        return await self.post("/chat/completions", data=data)
```

## Usage Patterns

### 1. Context Manager (Recommended)

```python
async with BrightDataClient() as client:
    result = await client.fetch_webpage("https://example.com")
    if result["success"]:
        print(f"Fetched: {result['data']}")
```

### 2. Manual Initialization

```python
client = BrightDataClient()
try:
    await client.initialize()
    result = await client.fetch_webpage("https://example.com")
    if result["success"]:
        print(f"Fetched: {result['data']}")
finally:
    await client.cleanup()
```

### 3. Global Client Instances

```python
# Get global instance
client = await get_brightdata_client()
result = await client.fetch_webpage("https://example.com")

# Cleanup when done
await cleanup_brightdata_client()
```

### 4. Client Manager

```python
from app.services.base_client import get_client_manager

manager = get_client_manager()

# Register clients
manager.register_client("brightdata", brightdata_client)
manager.register_client("together", together_client)

# Initialize all
await manager.initialize_all()

# Use clients
brightdata = manager.get_client("brightdata")
together = manager.get_client("together")

# Health check all
health_status = await manager.health_check_all()

# Cleanup all
await manager.cleanup_all()
```

## Error Handling

### Automatic Retries

The base client automatically retries on:
- **Server errors** (5xx status codes)
- **Network timeouts**
- **Connection errors**

**Retry Strategy:**
- Exponential backoff: `delay * (2^attempt)`
- Maximum retries: Configurable (default: 3)
- Only retries on server errors, not client errors (4xx)

### Error Response Format

```python
{
    "success": False,
    "error": "HTTP error 500: Internal Server Error",
    "status_code": 500,
    "response_time_ms": 2500
}
```

### Custom Error Handling

```python
result = await client.get("/endpoint")
if not result["success"]:
    if result["status_code"] == 404:
        # Handle not found
        pass
    elif result["status_code"] == 429:
        # Handle rate limit
        pass
    else:
        # Handle other errors
        pass
```

## Rate Limiting

### Built-in Rate Limiting

```python
# Configure rate limit (requests per minute)
super().__init__(
    base_url="https://api.example.com/v1",
    rate_limit=100  # 100 requests per minute
)
```

### Rate Limit Behavior

- **Automatic enforcement** of rate limits
- **Sliding window** implementation
- **Automatic waiting** when limit is reached
- **Logging** of rate limit events

### Custom Rate Limiting

```python
class CustomClient(BaseClient):
    async def _rate_limit_check(self) -> None:
        # Override for custom rate limiting logic
        await super()._rate_limit_check()
        # Add custom logic here
```

## Health Checks

### Implementing Health Checks

```python
async def health_check(self) -> Dict[str, Any]:
    try:
        result = await self.get("/health")
        return {
            "success": result["success"],
            "service": "my_service",
            "status": "healthy" if result["success"] else "unhealthy",
            "response_time_ms": result.get("response_time_ms", 0)
        }
    except Exception as e:
        return {
            "success": False,
            "service": "my_service",
            "status": "error",
            "error": str(e)
        }
```

### Health Check Aggregation

```python
# Check all clients
manager = get_client_manager()
health_status = await manager.health_check_all()

# Results format
{
    "brightdata": {
        "success": True,
        "service": "brightdata",
        "status": "healthy"
    },
    "together": {
        "success": True,
        "service": "together_ai",
        "status": "healthy"
    }
}
```

## Response Validation

### Built-in Validation

```python
def validate_response(self, response: Dict[str, Any]) -> bool:
    if not super().validate_response(response):
        return False
    
    # Add custom validation logic
    if response.get("success"):
        data = response.get("data", {})
        if "required_field" not in data:
            return False
    
    return True
```

### Using Validation

```python
result = await client.get("/endpoint")
if client.validate_response(result):
    # Process valid response
    pass
else:
    # Handle invalid response
    pass
```

## Logging

### Automatic Logging

The base client automatically logs:
- **Request details** (URL, method, data size)
- **Response details** (status code, response time, size)
- **Error conditions** (retries, failures, timeouts)
- **Rate limiting** events

### Log Levels

- **DEBUG**: Request/response details
- **INFO**: Successful operations
- **WARNING**: Retries, rate limits
- **ERROR**: Failures, exceptions

### Custom Logging

```python
class CustomClient(BaseClient):
    def __init__(self):
        super().__init__(...)
        self.logger = get_logger(f"{__name__}.CustomClient")
```

## Performance Considerations

### Connection Pooling

The base client uses httpx connection pooling:
```python
httpx.AsyncClient(
    limits=httpx.Limits(
        max_keepalive_connections=20,
        max_connections=100
    )
)
```

### Timeout Management

- **Request timeout**: Configurable per client
- **Connection timeout**: Handled by httpx
- **Read timeout**: Handled by httpx

### Memory Management

- **Automatic cleanup** of HTTP clients
- **Context manager** support for resource management
- **Connection pooling** for efficiency

## Best Practices

### 1. Use Context Managers

```python
# Good
async with BrightDataClient() as client:
    result = await client.fetch_webpage(url)

# Avoid
client = BrightDataClient()
await client.initialize()
# ... use client
await client.cleanup()
```

### 2. Handle Errors Gracefully

```python
result = await client.get("/endpoint")
if not result["success"]:
    logger.error(f"Request failed: {result['error']}")
    # Handle error appropriately
    return None
```

### 3. Implement Health Checks

```python
async def health_check(self) -> Dict[str, Any]:
    # Always implement health checks
    # Use lightweight endpoints when possible
    pass
```

### 4. Configure Rate Limits

```python
# Always configure appropriate rate limits
super().__init__(
    rate_limit=100  # Adjust based on API limits
)
```

### 5. Use Type Hints

```python
async def fetch_data(self, url: str) -> Dict[str, Any]:
    # Always use type hints for better code clarity
    pass
```

## Migration Guide

### From Requests-based Clients

**Before:**
```python
import requests

class OldClient:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({"Authorization": f"Bearer {api_key}"})
    
    def get_data(self, url):
        response = self.session.get(url)
        response.raise_for_status()
        return response.json()
```

**After:**
```python
from app.services.base_client import BaseClient

class NewClient(BaseClient):
    def __init__(self):
        super().__init__(
            base_url="https://api.example.com/v1",
            api_key=api_key
        )
    
    async def get_data(self, url):
        result = await self.get(url)
        return result["data"] if result["success"] else None
```

### Key Changes

1. **Inherit from BaseClient**
2. **Use async/await**
3. **Handle response format** (success/error dict)
4. **Implement health_check method**
5. **Use context managers**

## Testing

### Unit Testing

```python
import pytest
from unittest.mock import AsyncMock, patch

@pytest.mark.asyncio
async def test_client_request():
    with patch('httpx.AsyncClient.request') as mock_request:
        mock_request.return_value = AsyncMock(
            status_code=200,
            json=lambda: {"data": "test"}
        )
        
        client = TestClient()
        await client.initialize()
        
        result = await client.get("/test")
        assert result["success"] is True
        assert result["data"]["data"] == "test"
```

### Integration Testing

```python
@pytest.mark.asyncio
async def test_client_integration():
    async with TestClient() as client:
        result = await client.get("/health")
        assert result["success"] is True
```

## Future Enhancements

### Planned Features

1. **Circuit Breaker Pattern**: Automatic service isolation
2. **Request/Response Middleware**: Custom processing hooks
3. **Metrics Collection**: Performance monitoring
4. **Caching Layer**: Response caching
5. **Load Balancing**: Multiple endpoint support

### Extension Points

1. **Custom Retry Logic**: Override retry behavior
2. **Custom Rate Limiting**: Implement service-specific limits
3. **Custom Validation**: Add response validation rules
4. **Custom Logging**: Add service-specific logging

This base client architecture provides a robust, scalable foundation for all API client interactions in the TractionX Data Pipeline Service, ensuring consistency, reliability, and maintainability across all external service integrations. 