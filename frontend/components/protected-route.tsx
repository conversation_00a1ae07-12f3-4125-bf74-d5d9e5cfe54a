"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth-context'
import { Loading } from '@/components/ui/loading'

interface ProtectedRouteProps {
  children: React.ReactNode
}

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { isAuthenticated, loading } = useAuth()
  const router = useRouter()
  const [isRedirecting, setIsRedirecting] = useState(false)

  useEffect(() => {
    // Check if we have auth tokens in localStorage as a backup check
    const hasToken = !!localStorage.getItem('token')

    console.log('ProtectedRoute auth check:', {
      isAuthenticated,
      loading,
      hasToken,
      isRedirecting
    })

    if (!loading && !isAuthenticated && !hasToken && !isRedirecting) {
      console.log('Not authenticated, redirecting to login')
      setIsRedirecting(true)

      // Use window.location for a more reliable redirect
      window.location.href = '/login'
    }
  }, [isAuthenticated, loading, isRedirecting])

  // Check localStorage directly as a fallback
  const hasTokenInStorage = typeof window !== 'undefined' && !!localStorage.getItem('token')

  // Show loading state or nothing while checking authentication
  if (loading || (!isAuthenticated && !hasTokenInStorage)) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="mb-4 size-12 animate-spin rounded-full border-y-2 border-primary"></div>
          {/* <Loading size="lg" className="mb-4" /> */}
          <p className="text-sm text-muted-foreground">Checking authentication...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
