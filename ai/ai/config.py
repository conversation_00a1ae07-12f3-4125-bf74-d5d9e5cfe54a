"""
Configuration management for TractionX AI Agent system.
"""

from typing import Optional, List
from pydantic_settings import BaseSettings
from pydantic import validator

class AISettings(BaseSettings):
    """AI Agent system configuration."""

    # API Settings
    API_V1_STR: str = "/api/ai"
    PROJECT_NAME: str = "TractionX AI Agents"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "TractionX AI Agent Foundation"

    # Security (inherit from backend)
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # MongoDB (inherit from backend)
    MONGODB_URL: str
    MONGODB_DB_NAME: str = "x_app"

    # Qdrant Vector Database
    QDRANT_URL: str = "http://localhost:6333"
    QDRANT_API_KEY: Optional[str] = None
    QDRANT_TIMEOUT: int = 30
    QDRANT_VECTOR_SIZE: int = 1536

    # OpenAI Configuration
    OPENAI_API_KEY: str
    OPENAI_MODEL: str = "gpt-4"
    OPENAI_EMBEDDING_MODEL: str = "text-embedding-3-small"
    OPENAI_MAX_TOKENS: int = 4000
    OPENAI_TEMPERATURE: float = 0.7

    # LangChain Configuration
    LANGCHAIN_TRACING_V2: bool = False
    LANGCHAIN_API_KEY: Optional[str] = None
    LANGCHAIN_PROJECT: str = "tractionx-ai-agents"

    # Web Search API Keys
    NEWSAPI_KEY: Optional[str] = None
    SERPAPI_KEY: Optional[str] = None
    GNEWS_KEY: Optional[str] = None
    BING_NEWS_API_KEY: Optional[str] = None
    TWITTER_API_KEY: Optional[str] = None

    # News Intelligence Configuration
    NEWS_SEARCH_TIMEOUT: int = 8  # 8 seconds max for news searches
    NEWS_MAX_SOURCES: int = 3  # Maximum number of sources to query in parallel
    NEWS_DEDUP_THRESHOLD: float = 0.8  # Similarity threshold for deduplication

    # Document Processing Configuration
    DOC_MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB max file size
    DOC_ALLOWED_EXTENSIONS: List[str] = [".pdf", ".pptx", ".docx", ".txt"]
    DOC_CHUNK_SIZE: int = 1000  # Characters per chunk for processing
    DOC_CHUNK_OVERLAP: int = 200  # Overlap between chunks
    DOC_EXTRACTION_TIMEOUT: int = 60  # 1 minute max for document extraction

    # PDF Export Configuration
    PDF_TEMPLATE_DIR: str = "templates/pdf"
    PDF_LOGO_PATH: str = "assets/logo.png"
    PDF_FONT_SIZE: int = 12
    PDF_MARGIN: str = "1in"
    PDF_PAGE_SIZE: str = "A4"

    # Agent Configuration
    AGENT_TIMEOUT: int = 300  # 5 minutes
    MAX_AGENT_ITERATIONS: int = 10
    AGENT_MEMORY_SIZE: int = 100

    # Audit Configuration
    AUDIT_ENABLED: bool = True
    AUDIT_LOG_INPUTS: bool = True
    AUDIT_LOG_OUTPUTS: bool = True
    AUDIT_LOG_TOOLS: bool = True

    # Development/Testing
    TEST_MODE: bool = True
    DEBUG: bool = True
    LOG_LEVEL: str = "INFO"

    # Redis (inherit from backend)
    REDIS_URL: str
    REDIS_KEY_PREFIX: str = "txai"

    # CORS
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8000",
        "http://localhost:8080"
    ]

    @property
    def mongodb_connection_string(self) -> str:
        """Get MongoDB connection string."""
        return f"{self.MONGODB_URL}/{self.MONGODB_DB_NAME}"

    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v, info=None):
        """Parse CORS origins from string or list."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = AISettings()
