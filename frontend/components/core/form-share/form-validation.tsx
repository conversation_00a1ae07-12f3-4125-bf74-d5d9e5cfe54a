"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { AlertCircle, CheckCircle, Clock, FileX } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';

interface FormValidationProps {
  errors: Record<string, string>;
  progress: number;
  canSubmit: boolean;
  hasAttemptedSubmit: boolean;
  onScrollToError: () => void;
}

export function FormValidation({
  errors,
  progress,
  canSubmit,
  hasAttemptedSubmit,
  onScrollToError
}: FormValidationProps) {
  const errorCount = Object.keys(errors).length;
  const hasErrors = errorCount > 0;

  if (!hasAttemptedSubmit && !hasErrors) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className="mb-6"
    >
      {hasErrors ? (
        <Alert variant="destructive" className="border-red-200 bg-red-50">
          <AlertCircle className="size-4" />
          <AlertDescription className="flex items-center justify-between">
            <div>
              <span className="font-medium">
                {errorCount} error{errorCount > 1 ? 's' : ''} found
              </span>
              <p className="mt-1 text-sm">
                Please fix the following issues before submitting:
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={onScrollToError}
              className="ml-4 border-red-300 text-red-700 hover:bg-red-100"
            >
              Show Errors
            </Button>
          </AlertDescription>
        </Alert>
      ) : canSubmit ? (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="size-4 text-green-600" />
          <AlertDescription>
            <span className="font-medium text-green-800">
              Form is ready to submit
            </span>
            <p className="mt-1 text-sm text-green-700">
              All required fields have been completed.
            </p>
          </AlertDescription>
        </Alert>
      ) : (
        <Alert className="border-blue-200 bg-blue-50">
          <Clock className="size-4 text-blue-600" />
          <AlertDescription>
            <div className="flex items-center justify-between">
              <div>
                <span className="font-medium text-blue-800">
                  Form in progress
                </span>
                <p className="mt-1 text-sm text-blue-700">
                  {Math.round(progress)}% complete
                </p>
              </div>
              <Badge variant="secondary" className="ml-4">
                {Math.round(progress)}%
              </Badge>
            </div>
            <Progress value={progress} className="mt-3 h-2" />
          </AlertDescription>
        </Alert>
      )}
    </motion.div>
  );
}

interface FileValidationErrorProps {
  error: string;
  filename: string;
  onRetry?: () => void;
  onRemove?: () => void;
}

export function FileValidationError({
  error,
  filename,
  onRetry,
  onRemove
}: FileValidationErrorProps) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className="rounded-lg border border-red-200 bg-red-50 p-4"
    >
      <div className="flex items-start space-x-3">
        <FileX className="mt-0.5 size-5 shrink-0 text-red-600" />
        <div className="min-w-0 flex-1">
          <h4 className="text-sm font-medium text-red-800">
            Upload Failed: {filename}
          </h4>
          <p className="mt-1 text-sm text-red-700">{error}</p>
          <div className="mt-3 flex items-center space-x-2">
            {onRetry && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRetry}
                className="border-red-300 text-red-700 hover:bg-red-100"
              >
                Try Again
              </Button>
            )}
            {onRemove && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onRemove}
                className="text-red-600 hover:bg-red-100"
              >
                Remove
              </Button>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
}

interface QuestionValidationProps {
  question: {
    _id: string;
    label: string;
    required: boolean;
    type: string;
  };
  error?: string;
  value?: any;
}

export function QuestionValidation({
  question,
  error,
  value
}: QuestionValidationProps) {
  if (!error) return null;

  const getErrorIcon = () => {
    switch (question.type) {
      case 'file':
        return <FileX className="size-4" />;
      default:
        return <AlertCircle className="size-4" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      className="mt-2 flex items-center space-x-2 text-sm text-red-600"
      data-testid={`error-${question._id}`}
    >
      {getErrorIcon()}
      <span>{error}</span>
    </motion.div>
  );
}

interface FormProgressIndicatorProps {
  progress: number;
  totalQuestions: number;
  answeredQuestions: number;
  requiredQuestions: number;
  answeredRequired: number;
}

export function FormProgressIndicator({
  progress,
  totalQuestions,
  answeredQuestions,
  requiredQuestions,
  answeredRequired
}: FormProgressIndicatorProps) {
  return (
    <div className="rounded-lg border bg-white p-4 shadow-sm">
      <div className="mb-3 flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-900">Form Progress</h3>
        <Badge variant={progress === 100 ? "default" : "secondary"}>
          {Math.round(progress)}%
        </Badge>
      </div>
      
      <Progress value={progress} className="mb-3" />
      
      <div className="grid grid-cols-2 gap-4 text-xs text-gray-600">
        <div>
          <span className="font-medium">Questions:</span>
          <span className="ml-1">
            {answeredQuestions} / {totalQuestions}
          </span>
        </div>
        <div>
          <span className="font-medium">Required:</span>
          <span className="ml-1">
            {answeredRequired} / {requiredQuestions}
          </span>
        </div>
      </div>
    </div>
  );
}

// Validation utility functions
export const validateFileUpload = (
  file: File,
  maxSizeMB: number,
  allowedTypes: string
): string | null => {
  // Check file size
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  if (file.size > maxSizeBytes) {
    return `File size (${(file.size / (1024 * 1024)).toFixed(1)}MB) exceeds maximum allowed size (${maxSizeMB}MB)`;
  }
  
  if (file.size === 0) {
    return 'File is empty and cannot be uploaded';
  }
  
  // Check file type
  const fileName = file.name.toLowerCase();
  const allowedExtensions = allowedTypes.split(',').map(ext => ext.trim().toLowerCase());
  const fileExtension = '.' + fileName.split('.').pop();
  
  if (!allowedExtensions.some(ext => 
    fileName.endsWith(ext.replace('.', '')) || fileExtension === ext
  )) {
    return `File type not allowed. Supported formats: ${allowedTypes}`;
  }
  
  // Check filename validity
  if (!file.name || file.name.length > 255) {
    return 'Invalid filename';
  }
  
  // Check for potentially dangerous files
  const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com'];
  if (dangerousExtensions.some(ext => fileName.endsWith(ext))) {
    return 'This file type is not allowed for security reasons';
  }
  
  return null;
};

export const validateRequiredField = (
  value: any,
  questionType: string
): string | null => {
  if (value === null || value === undefined || value === '') {
    return 'This field is required';
  }
  
  if (questionType === 'file') {
    if (typeof value !== 'object' || !value.file) {
      return 'Please upload a file';
    }
  }
  
  if (questionType === 'multi_select') {
    if (!Array.isArray(value) || value.length === 0) {
      return 'Please select at least one option';
    }
  }
  
  return null;
};

export const validateFieldConstraints = (
  value: any,
  validation: any,
  questionType: string
): string | null => {
  if (!validation || value === null || value === undefined || value === '') {
    return null;
  }
  
  // Number validation
  if (questionType === 'number' && typeof value === 'number') {
    if (validation.min !== undefined && value < validation.min) {
      return `Value must be at least ${validation.min}`;
    }
    if (validation.max !== undefined && value > validation.max) {
      return `Value must be no more than ${validation.max}`;
    }
  }
  
  // Text length validation
  if ((questionType === 'short_text' || questionType === 'long_text') && typeof value === 'string') {
    if (validation.min !== undefined && value.length < validation.min) {
      return `Must be at least ${validation.min} characters`;
    }
    if (validation.max !== undefined && value.length > validation.max) {
      return `Must be no more than ${validation.max} characters`;
    }
  }
  
  // Regex validation
  if (validation.regex && typeof value === 'string') {
    try {
      const regex = new RegExp(validation.regex);
      if (!regex.test(value)) {
        return 'Invalid format';
      }
    } catch (e) {
      console.warn('Invalid regex pattern:', validation.regex);
    }
  }
  
  return null;
};
