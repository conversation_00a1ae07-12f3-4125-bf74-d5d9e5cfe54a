from datetime import datetime, timezone
from typing import Dict, List, Optional

from pydantic import Field, field_validator

from app.models.base import TractionXModel
from app.utils.common import ObjectIdField, PyObjectId


class ThesisConfig(TractionXModel):
    """
    Stores thesis configuration for an organization, including investment preferences
    for geography, sector, stage, and business model.
    """

    geography: List[str] = Field(
        default_factory=list,
        description="List of geographic regions (max 5 entries)",
        max_length=5
    )
    sector: List[str] = Field(
        default_factory=list,
        description="List of sectors/industries (max 5 entries)",
        max_length=5
    )
    stage: List[str] = Field(
        default_factory=list,
        description="List of investment stages (max 5 entries)",
        max_length=5
    )
    business_model: List[str] = Field(
        default_factory=list,
        description="List of business models (max 5 entries)",
        max_length=5
    )

    @field_validator("geography", "sector", "stage", "business_model")
    @classmethod
    def validate_thesis_fields(cls, v: List[str]) -> List[str]:
        """Validate thesis config fields."""
        if not isinstance(v, list):
            raise ValueError("Field must be a list of strings")

        if len(v) > 5:
            raise ValueError("Maximum 5 entries allowed per field")

        # Strip whitespace and validate each entry
        cleaned_values = []
        for item in v:
            if not isinstance(item, str):
                raise ValueError("All entries must be strings")

            cleaned_item = item.strip()
            if not cleaned_item:
                continue  # Skip empty strings

            # Normalize "Other" entries
            if cleaned_item.lower().startswith("other"):
                if not cleaned_item.startswith("Other: "):
                    # Extract the custom value after "other"
                    custom_value = cleaned_item[5:].strip() if len(cleaned_item) > 5 else ""
                    if custom_value.startswith(":"):
                        custom_value = custom_value[1:].strip()
                    if not custom_value:
                        raise ValueError("Other entries must include a custom value: 'Other: <CustomValue>'")
                    cleaned_item = f"Other: {custom_value}"

            cleaned_values.append(cleaned_item)

        return cleaned_values


class OrganizationSettings(TractionXModel):
    """
    Stores settings specific to an organization, such as plan, branding, and feature flags.
    """

    plan: str = Field(
        default="basic", description="Organization plan (basic, advanced, enterprise)"
    )
    subdomain_enabled: bool = Field(
        default=True, description="Whether subdomain access is enabled"
    )
    features: Dict[str, bool] = Field(default_factory=dict, description="Feature flags")
    custom_domain: Optional[str] = Field(
        None, description="Custom domain for the organization"
    )
    branding: Dict[str, str] = Field(
        default_factory=dict, description="Branding settings"
    )
    pass_through: bool = Field(
        default=False,
        description="If True, disables all auth, RBAC, and tenant checks for this org",
    )


class Organization(TractionXModel):
    """
    Represents an organization, with metadata, members, admins, and settings.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    name: str
    subdomain: str = Field(..., description="Unique subdomain for organization")
    description: Optional[str] = None
    website: Optional[str] = None
    logo_url: Optional[str] = None
    address: Optional[str] = None
    contact_email: Optional[str] = None
    created_at: int = Field(default_factory=lambda: int(datetime.now(timezone.utc).timestamp()))
    updated_at: int = Field(default_factory=lambda: int(datetime.now(timezone.utc).timestamp()))
    is_active: bool = True
    members: List[ObjectIdField] = []
    admins: List[ObjectIdField] = []
    contact_phone: Optional[str] = None
    created_by: ObjectIdField = Field(default_factory=PyObjectId, alias="created_by")
    created_at: int = Field(default_factory=lambda: int(datetime.now(timezone.utc).timestamp()))
    updated_at: int = Field(default_factory=lambda: int(datetime.now(timezone.utc).timestamp()))
    is_active: bool = True
    user_ids: List[ObjectIdField] = []
    settings: OrganizationSettings = Field(
        default_factory=OrganizationSettings,  # type: ignore
        description="Organization-specific settings",
    )
    thesis_config: Optional[ThesisConfig] = Field(
        default=None,
        description="Investment thesis configuration for the organization",
    )

    # @validator("subdomain")
    # def validate_subdomain(cls, v):
    #     if not v.isalnum():
    #         raise ValueError("Subdomain must be alphanumeric")
    #     return v.lower()

    @field_validator("settings")
    @classmethod
    def validate_settings(cls, v):
        if v.plan not in ["basic", "advanced", "enterprise"]:
            raise ValueError("Invalid plan type")
        return v
