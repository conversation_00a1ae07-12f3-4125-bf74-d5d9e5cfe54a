"use client"

/**
 * Organization Members Hook
 * 
 * Fetches and provides organization members for mention functionality
 */

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth-context';
import { OrgUser } from '@/lib/types/deal-notes';
import DealAPI from '@/lib/api/deal-api';

interface UseOrgMembersReturn {
  members: OrgUser[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useOrgMembers(): UseOrgMembersReturn {
  const { user } = useAuth();
  const [members, setMembers] = useState<OrgUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchMembers = async () => {
    if (!user?.org_id) {
      setError('No organization context');
      setIsLoading(false);
      return;
    }

    try {
      setError(null);
      setIsLoading(true);
      
      // Use the existing DealAPI method to fetch organization members
      const users = await DealAPI.getOrgUsers();
      
      // Transform User[] to OrgUser[] for compatibility
      const orgUsers: OrgUser[] = users.map(user => ({
        _id: user.id,
        name: user.name || user.email,
        email: user.email,
        avatar_url: undefined // User type doesn't have avatar_url
      }));
      
      setMembers(orgUsers);
    } catch (err) {
      console.error('Error fetching org members:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch members');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchMembers();
  }, [user?.org_id]);

  const refetch = async () => {
    await fetchMembers();
  };

  return {
    members,
    isLoading,
    error,
    refetch,
  };
} 