"use client"

import { useAuth } from "@/lib/auth-context"

interface DashboardHeaderProps {
  heading: string
  text?: string
  children?: React.ReactNode
  showOrgName?: boolean
}

export function DashboardHeader({
  heading,
  text,
  children,
  showOrgName = false,
}: DashboardHeaderProps) {
  const { userOrganizations, orgId } = useAuth()

  // Find current organization
  const currentOrg = userOrganizations.find(org => org.id === orgId) || userOrganizations[0]
  const orgName = currentOrg?.name || "Organization"

  return (
    <div className="flex items-center justify-between">
      <div className="grid gap-3">
        {showOrgName && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>{orgName}</span>
            <span>•</span>
            <span>{heading}</span>
          </div>
        )}
        <h1 className="font-heading text-4xl font-bold text-gray-900">{heading}</h1>
        {text && <p className="text-xl text-gray-600">{text}</p>}
      </div>
      {children}
    </div>
  )
}
