"""
Context builder and Qdrant helper for TractionX AI Agent system.
Provides multi-tenant vector operations with org-level isolation.
"""

from typing import List, Dict, Any, Optional
import logging
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.exceptions import ResponseHandlingException
from ai.config import settings
from ai.models import AgentContext, PyObjectId

logger = logging.getLogger(__name__)


class QdrantHelper:
    """Multi-tenant Qdrant vector database helper."""
    
    def __init__(self):
        self.client = None
        self._collections_cache = set()
        logger.debug("QdrantHelper initialized - client not yet connected")
    
    async def initialize(self) -> None:
        """Initialize Qdrant client with graceful fallback."""
        logger.info("Initializing Qdrant client connection...")
        try:
            logger.debug(f"Connecting to Qdrant at: {settings.QDRANT_URL}")
            self.client = QdrantClient(
                url=settings.QDRANT_URL,
                api_key=settings.QDRANT_API_KEY,
                timeout=settings.QDRANT_TIMEOUT
            )
            logger.debug("Qdrant client created, testing connection...")
            
            # Test connection
            collections = self.client.get_collections()
            logger.info(f"Connected to Qdrant: {len(collections.collections)} collections")
            logger.debug(f"Available collections: {[c.name for c in collections.collections]}")
            
        except Exception as e:
            logger.error(f"Failed to connect to Qdrant: {e}")
            logger.warning("Qdrant is unavailable - vector operations will be disabled")
            logger.info("This is normal for first-time setup or when Qdrant service is not running")
            
            # Set client to None to indicate it's unavailable
            self.client = None
            logger.debug("Qdrant client set to None for graceful degradation")
            
            # Don't raise the exception - allow the system to continue without vector capabilities
            # This enables graceful degradation for first-time setup or service outages
    
    def _get_collection_name(self, org_id: str) -> str:
        """Get org-specific collection name."""
        collection_name = f"org_{org_id}_vectors"
        logger.debug(f"Generated collection name for org {org_id}: {collection_name}")
        return collection_name
    
    async def ensure_collection(self, org_id: str) -> str:
        """Ensure org-specific collection exists."""
        logger.debug(f"Ensuring collection exists for org: {org_id}")
        collection_name = self._get_collection_name(org_id)
        
        # Check if Qdrant client is available
        if not self.client:
            logger.debug(f"Qdrant client unavailable - cannot ensure collection: {collection_name}")
            raise Exception("Qdrant client is not available")
        
        if collection_name in self._collections_cache:
            logger.debug(f"Collection found in cache: {collection_name}")
            return collection_name
        
        logger.debug(f"Collection not in cache, checking if exists in Qdrant: {collection_name}")
        try:
            # Check if collection exists
            self.client.get_collection(collection_name)
            self._collections_cache.add(collection_name)
            logger.debug(f"Collection exists: {collection_name}")
            logger.debug(f"Added to cache. Cache now contains: {len(self._collections_cache)} collections")
            
        except ResponseHandlingException as e:
            # Collection doesn't exist, create it
            logger.info(f"Collection {collection_name} doesn't exist, creating it...")
            logger.debug(f"ResponseHandlingException details: {e}")
            try:
                logger.debug(f"Creating collection with vector size: {settings.QDRANT_VECTOR_SIZE}")
                self.client.create_collection(
                    collection_name=collection_name,
                    vectors_config=models.VectorParams(
                        size=settings.QDRANT_VECTOR_SIZE,
                        distance=models.Distance.COSINE
                    )
                )
                self._collections_cache.add(collection_name)
                logger.info(f"Created collection: {collection_name}")
                logger.debug(f"Collection created successfully. Cache now contains: {len(self._collections_cache)} collections")
                
            except Exception as e:
                logger.error(f"Failed to create collection {collection_name}: {e}")
                logger.debug(f"Collection creation failed with error type: {type(e).__name__}")
                raise
        except Exception as e:
            logger.error(f"Unexpected error checking collection {collection_name}: {e}")
            logger.debug(f"Unexpected error type: {type(e).__name__}")
            raise
        
        logger.debug(f"Collection {collection_name} is ready for use")
        return collection_name
    
    async def upsert_vectors(
        self,
        org_id: str,
        vectors: List[Dict[str, Any]]
    ) -> bool:
        """Upsert vectors to org-specific collection."""
        logger.debug(f"Starting upsert operation for org {org_id} with {len(vectors)} vectors")
        
        # Check if Qdrant client is available
        if not self.client:
            logger.debug("Qdrant client unavailable - cannot upsert vectors")
            return False
            
        try:
            collection_name = await self.ensure_collection(org_id)
            logger.debug(f"Collection ready for upsert: {collection_name}")
            
            logger.debug("Converting vector data to Qdrant points...")
            points = []
            for i, vector_data in enumerate(vectors):
                vector_id = vector_data.get("id")
                vector = vector_data.get("vector")
                payload = vector_data.get("payload", {})
                
                logger.debug(f"Processing vector {i+1}/{len(vectors)}: id={vector_id}, vector_size={len(vector) if vector else 'None'}, payload_keys={list(payload.keys()) if payload else 'None'}")
                
                point = models.PointStruct(
                    id=vector_id,
                    vector=vector,
                    payload=payload
                )
                points.append(point)
            
            logger.debug(f"Executing upsert operation with {len(points)} points...")
            self.client.upsert(
                collection_name=collection_name,
                points=points
            )
            
            logger.debug(f"Upserted {len(points)} vectors to {collection_name}")
            logger.info(f"Successfully upserted {len(points)} vectors for org {org_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to upsert vectors for org {org_id}: {e}")
            logger.debug(f"Upsert failed with error type: {type(e).__name__}")
            return False
    
    async def search_vectors(
        self,
        org_id: str,
        query_vector: List[float],
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Search vectors in org-specific collection."""
        logger.debug(f"Starting vector search for org {org_id}: query_size={len(query_vector)}, limit={limit}, filters={filters}")
        
        # Check if Qdrant client is available
        if not self.client:
            logger.debug("Qdrant client unavailable - returning empty search results")
            return []
            
        try:
            collection_name = await self.ensure_collection(org_id)
            logger.debug(f"Collection ready for search: {collection_name}")
            
            # Build filter
            query_filter = None
            if filters:
                logger.debug(f"Building query filter from {len(filters)} filter conditions...")
                query_filter = models.Filter(
                    must=[
                        models.FieldCondition(
                            key=key,
                            match=models.MatchValue(value=value)
                        )
                        for key, value in filters.items()
                    ]
                )
                logger.debug(f"Query filter built with {len(query_filter.must)} conditions")
            else:
                logger.debug("No filters provided, searching all vectors")
            
            logger.debug("Executing vector search...")
            results = self.client.search(
                collection_name=collection_name,
                query_vector=query_vector,
                query_filter=query_filter,
                limit=limit,
                with_payload=True,
                with_vectors=False
            )
            
            logger.debug(f"Raw search returned {len(results)} results")
            
            # Convert to dict format
            search_results = []
            for i, result in enumerate(results):
                result_dict = {
                    "id": result.id,
                    "score": result.score,
                    "payload": result.payload
                }
                search_results.append(result_dict)
                logger.debug(f"Result {i+1}: id={result.id}, score={result.score:.4f}, payload_keys={list(result.payload.keys()) if result.payload else 'None'}")
            
            logger.debug(f"Found {len(search_results)} vectors in {collection_name}")
            logger.info(f"Vector search completed for org {org_id}: {len(search_results)} results")
            return search_results
            
        except Exception as e:
            logger.error(f"Failed to search vectors for org {org_id}: {e}")
            logger.debug(f"Search failed with error type: {type(e).__name__}")
            return []
    
    async def delete_vectors(
        self,
        org_id: str,
        vector_ids: List[str]
    ) -> bool:
        """Delete vectors from org-specific collection."""
        logger.debug(f"Starting vector deletion for org {org_id}: {len(vector_ids)} vectors to delete")
        logger.debug(f"Vector IDs to delete: {vector_ids}")
        
        # Check if Qdrant client is available
        if not self.client:
            logger.debug("Qdrant client unavailable - cannot delete vectors")
            return False
            
        try:
            collection_name = await self.ensure_collection(org_id)
            logger.debug(f"Collection ready for deletion: {collection_name}")
            
            logger.debug("Executing vector deletion...")
            self.client.delete(
                collection_name=collection_name,
                points_selector=models.PointIdsList(
                    points=vector_ids
                )
            )
            
            logger.debug(f"Deleted {len(vector_ids)} vectors from {collection_name}")
            logger.info(f"Successfully deleted {len(vector_ids)} vectors for org {org_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete vectors for org {org_id}: {e}")
            logger.debug(f"Deletion failed with error type: {type(e).__name__}")
            return False
    
    async def health_check(self) -> bool:
        """Check Qdrant health."""
        logger.debug("Starting Qdrant health check...")
        try:
            if not self.client:
                logger.debug("Health check failed: client is None")
                return False
            
            logger.debug("Attempting to fetch collections for health check...")
            collections = self.client.get_collections()
            logger.debug(f"Health check successful: {len(collections.collections)} collections available")
            logger.info("Qdrant health check passed")
            return True
        except Exception as e:
            logger.error(f"Qdrant health check failed: {e}")
            logger.debug(f"Health check failed with error type: {type(e).__name__}")
            return False


class ContextBuilder:
    """Builds execution context for AI agents."""
    
    def __init__(self, qdrant_helper: QdrantHelper):
        self.qdrant = qdrant_helper
        logger.debug("ContextBuilder initialized with QdrantHelper")
    
    async def build_context(
        self,
        user_id: str,
        org_id: str,
        session_id: Optional[str] = None,
        deal_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> AgentContext:
        """Build agent execution context with graceful first-time setup."""
        
        logger.info(f"Building agent context for user {user_id}, org {org_id}")
        logger.debug(f"Context parameters: session_id={session_id}, deal_id={deal_id}, metadata_keys={list(metadata.keys()) if metadata else 'None'}")
        
        # Create context object - this should always work
        logger.debug("Creating AgentContext object...")
        try:
            context = AgentContext(
                user_id=PyObjectId(user_id),
                org_id=PyObjectId(org_id),
                session_id=session_id,
                deal_id=PyObjectId(deal_id) if deal_id else None,
                metadata=metadata or {}
            )
            logger.debug(f"AgentContext created successfully: user_id={context.user_id}, org_id={context.org_id}")
        except Exception as e:
            logger.error(f"Failed to create AgentContext: {e}")
            logger.debug(f"Context creation failed with error type: {type(e).__name__}")
            raise
        
        # Ensure vector collection exists - handle gracefully for first-time setup
        logger.debug(f"Attempting to ensure vector collection for org: {org_id}")
        try:
            await self.qdrant.ensure_collection(org_id)
            logger.debug(f"Vector collection ready for org: {org_id}")
            # Vector capabilities are available
            context.metadata["vector_enabled"] = True
            logger.debug("Vector capabilities enabled and added to context metadata")
        except Exception as e:
            # Don't fail the entire context building if vector setup fails
            # This allows the system to work even if Qdrant is unavailable
            logger.warning(f"Failed to ensure vector collection for org {org_id}: {e}")
            logger.info("Context will be created without vector capabilities - this is normal for first-time setup or when Qdrant is unavailable")
            logger.debug(f"Vector setup failed with error type: {type(e).__name__}")
            
            # Add metadata to indicate vector capabilities are unavailable
            context.metadata["vector_enabled"] = False
            context.metadata["vector_error"] = str(e)
            logger.debug("Vector capabilities disabled and error details added to context metadata")
        
        logger.debug(f"Context metadata final state: {context.metadata}")
        logger.info(f"Successfully built context for user {user_id}, org {org_id} - vector_enabled: {context.metadata.get('vector_enabled', False)}")
        return context
    
    async def get_deal_context(
        self,
        context: AgentContext,
        deal_id: str
    ) -> Dict[str, Any]:
        """Get deal-specific context data with graceful fallbacks."""
        
        logger.info(f"Building deal context for deal {deal_id}, org {context.org_id}")
        logger.debug(f"Base context: user_id={context.user_id}, vector_enabled={context.metadata.get('vector_enabled', False)}")
        
        # Initialize with basic structure
        logger.debug("Initializing deal context structure...")
        deal_context = {
            "deal_id": deal_id,
            "deal_data": {},
            "related_vectors": [],
            "context_status": {
                "database_available": True,
                "vectors_available": context.metadata.get("vector_enabled", False)
            }
        }
        logger.debug(f"Initial deal context structure created: {list(deal_context.keys())}")
        
        # Deal data retrieval section
        logger.debug(f"Attempting to retrieve deal data for: {deal_id}")
        try:
            # TODO: Implement deal data retrieval from main database
            # This should have its own error handling for database connection issues
            logger.debug(f"Retrieving deal data for: {deal_id}")
            
            # For now, provide empty structure that won't break downstream code
            logger.debug("Creating placeholder deal data structure...")
            deal_context["deal_data"] = {
                "id": deal_id,
                "status": "unknown",  # Placeholder
                "metadata": {}
            }
            logger.debug("Placeholder deal data created successfully")
            
        except Exception as e:
            logger.warning(f"Failed to retrieve deal data for {deal_id}: {e}")
            logger.debug(f"Deal data retrieval failed with error type: {type(e).__name__}")
            deal_context["context_status"]["database_available"] = False
            deal_context["deal_data"] = {"error": str(e)}
            logger.debug("Deal context marked as database unavailable")
        
        # Vector operations section
        vector_enabled = context.metadata.get("vector_enabled", False)
        logger.debug(f"Checking vector operations: vector_enabled={vector_enabled}")
        
        # Only attempt vector operations if vectors are enabled
        if vector_enabled:
            logger.debug("Vector operations are enabled, attempting to retrieve related vectors...")
            try:
                # TODO: Implement vector retrieval for deal context
                logger.debug(f"Retrieving related vectors for deal: {deal_id}")
                
                # Example: search for vectors related to this deal
                # related_vectors = await self.qdrant.search_vectors(
                #     org_id=str(context.org_id),
                #     query_vector=...,  # Would need to compute query vector
                #     filters={"deal_id": deal_id}
                # )
                # deal_context["related_vectors"] = related_vectors
                
                logger.debug("Vector retrieval placeholder completed (TODO: implement actual retrieval)")
                
            except Exception as e:
                logger.warning(f"Failed to retrieve vectors for deal {deal_id}: {e}")
                logger.debug(f"Vector retrieval failed with error type: {type(e).__name__}")
                deal_context["related_vectors"] = []
                deal_context["context_status"]["vectors_available"] = False
                logger.debug("Vector availability marked as false due to retrieval failure")
        else:
            logger.debug("Vector operations skipped - vectors not available")
        
        logger.debug(f"Final deal context status: {deal_context['context_status']}")
        logger.info(f"Deal context built successfully for deal {deal_id}: database_available={deal_context['context_status']['database_available']}, vectors_available={deal_context['context_status']['vectors_available']}")
        return deal_context


# Global instances
logger.debug("Creating global QdrantHelper instance")
qdrant_helper = QdrantHelper()
logger.debug("Global QdrantHelper instance created")

logger.debug("Creating global ContextBuilder instance")
context_builder = ContextBuilder(qdrant_helper)
logger.debug("Global ContextBuilder instance created")
