"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Slider } from "@/components/ui/slider"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  AlertTriangle, 
  Shield, 
  Edit3,
  CheckCircle,
  Loader2
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"
import { DealDetailAPI } from "@/lib/api/deal-detail-api"

interface ScoreOverrideModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  dealId: string
  signalType: string | null
  currentScore: number
  onSuccess: () => void
}

export function ScoreOverrideModal({
  open,
  onOpenChange,
  dealId,
  signalType,
  currentScore,
  onSuccess
}: ScoreOverrideModalProps) {
  const [newScore, setNewScore] = useState([currentScore])
  const [reason, setReason] = useState("")
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!signalType) return
    
    if (reason.trim().length < 10) {
      toast({
        title: "Invalid reason",
        description: "Please provide a reason with at least 10 characters.",
        variant: "destructive"
      })
      return
    }

    try {
      setLoading(true)

      await DealDetailAPI.overrideScore(dealId, {
        signal_type: signalType,
        new_score: newScore[0],
        reason: reason.trim()
      })

      toast({
        title: "Score overridden successfully",
        description: `${signalType.replace('_', ' ')} score updated to ${newScore[0]}`,
      })
      
      onSuccess()
      onOpenChange(false)
      
      // Reset form
      setReason("")
      setNewScore([currentScore])
      
    } catch (error) {
      console.error('Error overriding score:', error)
      toast({
        title: "Override failed",
        description: "Failed to override score. Please try again.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-50 border-green-200'
    if (score >= 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  const getScoreLabel = (score: number) => {
    if (score >= 80) return 'Excellent'
    if (score >= 60) return 'Good'
    if (score >= 40) return 'Fair'
    return 'Poor'
  }

  const formatSignalName = (signal: string) => {
    return signal?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || ''
  }

  if (!signalType) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit3 className="size-5 text-primary" />
            Override Score
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Signal Info */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">
                {formatSignalName(signalType)}
              </Label>
              <Badge variant="outline" className={cn("font-mono", getScoreColor(currentScore))}>
                Current: {currentScore}
              </Badge>
            </div>
          </div>

          {/* Score Slider */}
          <div className="space-y-4">
            <Label className="text-sm font-medium">New Score</Label>
            <div className="space-y-3">
              <Slider
                value={newScore}
                onValueChange={setNewScore}
                max={100}
                min={0}
                step={1}
                className="w-full"
              />
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge className={cn("text-lg font-bold", getScoreColor(newScore[0]))}>
                    {newScore[0]}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    {getScoreLabel(newScore[0])}
                  </span>
                </div>
                <div className="text-xs text-muted-foreground">
                  Change: {newScore[0] > currentScore ? '+' : ''}{newScore[0] - currentScore}
                </div>
              </div>
            </div>
          </div>

          {/* Reason */}
          <div className="space-y-2">
            <Label htmlFor="reason" className="text-sm font-medium">
              Reason for Override *
            </Label>
            <Textarea
              id="reason"
              placeholder="Explain why you're overriding this score (minimum 10 characters)..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="min-h-[100px] resize-none"
              required
            />
            <div className="text-xs text-muted-foreground">
              {reason.length}/10 characters minimum
            </div>
          </div>

          {/* Warning */}
          <Alert className="border-orange-200 bg-orange-50">
            <AlertTriangle className="size-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              This override will be logged in the deal timeline and audit trail. 
              Make sure your reason is clear and justified.
            </AlertDescription>
          </Alert>

          {/* Actions */}
          <div className="flex gap-3 pt-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="flex-1"
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1"
              disabled={loading || reason.trim().length < 10}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 size-4 animate-spin" />
                  Applying...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 size-4" />
                  Apply Override
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
