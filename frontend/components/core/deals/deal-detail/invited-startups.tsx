"use client"

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Mail, 
  ExternalLink, 
  Clock, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  Eye,
  MousePointer
} from 'lucide-react';
// import { DealInviteAPI } from '@/lib/api/deal-invite-api';
import { useToast } from '@/components/ui/use-toast';

interface InvitedStartupsProps {
  dealId: string;
  inviteData?: {
    invited_email: string;
    invite_status: string;
    invite_sent_at?: number;
    company_name: string;
    company_website?: string;
  };
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'sent':
      return <Mail className="size-4" />;
    case 'opened':
      return <Eye className="size-4" />;
    case 'clicked':
      return <MousePointer className="size-4" />;
    case 'submitted':
      return <CheckCircle className="size-4" />;
    case 'failed':
      return <XCircle className="size-4" />;
    default:
      return <Clock className="size-4" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'sent':
      return 'bg-blue-100 text-blue-800';
    case 'opened':
      return 'bg-yellow-100 text-yellow-800';
    case 'clicked':
      return 'bg-orange-100 text-orange-800';
    case 'submitted':
      return 'bg-green-100 text-green-800';
    case 'failed':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const formatTimestamp = (timestamp?: number) => {
  if (!timestamp) return 'Not sent';
  return new Date(timestamp * 1000).toLocaleString();
};

export function InvitedStartups({ dealId, inviteData }: InvitedStartupsProps) {
  const [isResending, setIsResending] = useState(false);
  const { toast } = useToast();

  const handleResendInvite = async () => {
    console.log('handleResendInvite', dealId);
  }
  //   try {
  //     setIsResending(true);
  //     await DealInviteAPI.resendInvite(dealId);
      
  //     toast({
  //       title: 'Success',
  //       description: 'Invite email resent successfully.'
  //     });
  //   } catch (error) {
  //     console.error('Error resending invite:', error);
  //     toast({
  //       title: 'Error',
  //       description: 'Failed to resend invite. Please try again.',
  //       variant: 'destructive'
  //     });
  //   } finally {
  //     setIsResending(false);
  //   }
  // };

  if (!inviteData?.invited_email) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Invited Startups</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="py-8 text-center text-gray-500">
            <Mail className="mx-auto mb-4 size-12 text-gray-300" />
            <p className="text-sm">No invites sent for this deal yet.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Invited Startups</CardTitle>
      </CardHeader>
      <CardContent>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          {/* Invite Entry */}
          <div className="space-y-3 rounded-lg border p-4">
            {/* Header with company info */}
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <h4 className="font-medium text-gray-900">
                  {inviteData.company_name}
                </h4>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Mail className="size-4" />
                  <span>{inviteData.invited_email}</span>
                </div>
                {inviteData.company_website && (
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <ExternalLink className="size-4" />
                    <a 
                      href={inviteData.company_website.startsWith('http') 
                        ? inviteData.company_website 
                        : `https://${inviteData.company_website}`
                      }
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-blue-600 hover:underline"
                    >
                      {inviteData.company_website}
                    </a>
                  </div>
                )}
              </div>

              {/* Status Badge */}
              <Badge 
                variant="secondary" 
                className={`${getStatusColor(inviteData.invite_status)} flex items-center space-x-1`}
              >
                {getStatusIcon(inviteData.invite_status)}
                <span className="capitalize">{inviteData.invite_status}</span>
              </Badge>
            </div>

            {/* Timeline */}
            <div className="space-y-2">
              <div className="text-sm text-gray-600">
                <strong>Sent:</strong> {formatTimestamp(inviteData.invite_sent_at)}
              </div>
              
              {/* Status-specific information */}
              {inviteData.invite_status === 'submitted' && (
                <div className="rounded-md border border-green-200 bg-green-50 p-3">
                  <div className="flex items-center space-x-2 text-green-800">
                    <CheckCircle className="size-4" />
                    <span className="text-sm font-medium">Application Submitted</span>
                  </div>
                  <p className="mt-1 text-sm text-green-700">
                    The startup has completed and submitted their application form.
                  </p>
                </div>
              )}

              {inviteData.invite_status === 'failed' && (
                <div className="rounded-md border border-red-200 bg-red-50 p-3">
                  <div className="flex items-center space-x-2 text-red-800">
                    <XCircle className="size-4" />
                    <span className="text-sm font-medium">Delivery Failed</span>
                  </div>
                  <p className="mt-1 text-sm text-red-700">
                    The invite email could not be delivered. Please check the email address.
                  </p>
                </div>
              )}

              {['pending', 'sent'].includes(inviteData.invite_status) && (
                <div className="rounded-md border border-blue-200 bg-blue-50 p-3">
                  <div className="flex items-center space-x-2 text-blue-800">
                    <Clock className="size-4" />
                    <span className="text-sm font-medium">Awaiting Response</span>
                  </div>
                  <p className="mt-1 text-sm text-blue-700">
                    The invite has been sent. Waiting for the startup to open and complete the form.
                  </p>
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between border-t pt-2">
              <div className="text-xs text-gray-500">
                Invite ID: {dealId.slice(-8)}
              </div>
              
              {['failed', 'sent', 'pending'].includes(inviteData.invite_status) && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleResendInvite}
                  disabled={isResending}
                  className="text-xs"
                >
                  {isResending ? (
                    <>
                      <RefreshCw className="mr-1 size-3 animate-spin" />
                      Resending...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="mr-1 size-3" />
                      Resend Invite
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>

          {/* Summary Stats */}
          <div className="rounded-lg bg-gray-50 p-4">
            <h5 className="mb-2 text-sm font-medium text-gray-900">Invite Summary</h5>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Status:</span>
                <span className="ml-2 font-medium capitalize">{inviteData.invite_status}</span>
              </div>
              <div>
                <span className="text-gray-600">Sent:</span>
                <span className="ml-2 font-medium">
                  {inviteData.invite_sent_at ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
          </div>
        </motion.div>
      </CardContent>
    </Card>
  );
}
