from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from enum import Enum


class TriggerType(str, Enum):
    """Types of triggers available."""
    EMAIL_NOTIFICATION = "email_notification"
    IN_APP_NOTIFICATION = "in_app_notification"
    THESIS_FORM = "thesis_form"
    WEBHOOK = "webhook"


class TriggerStatus(str, Enum):
    """Status of a trigger execution."""
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"
    RETRYING = "retrying"


class TriggerServiceInterface(ABC):
    """Interface for trigger services."""
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize trigger service resources."""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup trigger service resources."""
        pass
    
    @abstractmethod
    async def create_trigger_config(
        self,
        resource_type: str,
        resource_id: str,
        org_id: str,
        type: TriggerType,
        name: str,
        description: Optional[str] = None,
        enabled: bool = True,
        config: Optional[Dict[str, Any]] = None,
        conditions: Optional[Dict[str, Any]] = None,
        retry_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create a trigger configuration.
        
        Args:
            resource_type: Type of resource (e.g., 'form', 'report')
            resource_id: ID of the resource
            org_id: Organization ID
            type: Type of trigger
            name: Name of the trigger
            description: Description of the trigger
            enabled: Whether the trigger is enabled
            config: Type-specific configuration
            conditions: Conditions for when to execute
            retry_config: Retry configuration
            
        Returns:
            Trigger configuration details
        """
        pass
    
    @abstractmethod
    async def get_trigger_config(
        self,
        trigger_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get trigger configuration by ID.
        
        Args:
            trigger_id: ID of the trigger configuration
            
        Returns:
            Trigger configuration details or None if not found
        """
        pass
    
    @abstractmethod
    async def update_trigger_config(
        self,
        trigger_id: str,
        updates: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Update a trigger configuration.
        
        Args:
            trigger_id: ID of the trigger configuration
            updates: Fields to update
            
        Returns:
            Updated trigger configuration or None if not found
        """
        pass
    
    @abstractmethod
    async def delete_trigger_config(
        self,
        trigger_id: str
    ) -> bool:
        """
        Delete a trigger configuration.
        
        Args:
            trigger_id: ID of the trigger configuration
            
        Returns:
            True if successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def get_resource_triggers(
        self,
        resource_type: str,
        resource_id: str
    ) -> List[Dict[str, Any]]:
        """
        Get all trigger configurations for a resource.
        
        Args:
            resource_type: Type of resource
            resource_id: ID of the resource
            
        Returns:
            List of trigger configurations
        """
        pass
    
    @abstractmethod
    async def execute_trigger(
        self,
        trigger_id: str,
        event_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute a trigger.
        
        Args:
            trigger_id: ID of the trigger configuration
            event_data: Data for the trigger execution
            
        Returns:
            Execution details
        """
        pass
    
    @abstractmethod
    async def process_resource_event(
        self,
        resource_type: str,
        resource_id: str,
        event_type: str,
        event_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Process an event for a resource and execute matching triggers.
        
        Args:
            resource_type: Type of resource
            resource_id: ID of the resource
            event_type: Type of event
            event_data: Data for the event
            
        Returns:
            List of execution details
        """
        pass
    
    @abstractmethod
    async def get_execution_history(
        self,
        trigger_id: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        status: Optional[TriggerStatus] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Get execution history.
        
        Args:
            trigger_id: Optional filter by trigger ID
            resource_type: Optional filter by resource type
            resource_id: Optional filter by resource ID
            status: Optional filter by execution status
            limit: Maximum number of executions to return
            offset: Offset for pagination
            
        Returns:
            List of execution details
        """
        pass
    
    @abstractmethod
    async def retry_execution(
        self,
        execution_id: str
    ) -> Dict[str, Any]:
        """
        Retry a failed execution.
        
        Args:
            execution_id: ID of the execution to retry
            
        Returns:
            Updated execution details
        """
        pass
