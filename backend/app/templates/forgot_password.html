<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Oops! Password Reset - TractionX</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
        
        body {
            margin: 0;
            padding: 0;
            font-family: 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #1a1a1a;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
        }
        
        .logo {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }
        
        .tagline {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .emoji-header {
            font-size: 48px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .title {
            font-size: 24px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 20px;
            color: #1a1a1a;
        }
        
        .subtitle {
            font-size: 16px;
            color: #666;
            text-align: center;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        
        .funny-section {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            margin: 30px 0;
            border-left: 4px solid #667eea;
        }
        
        .funny-text {
            font-size: 14px;
            color: #4a5568;
            line-height: 1.6;
            font-style: italic;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            transition: transform 0.2s ease;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
        }
        
        .cta-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .security-note {
            background: #fef7e0;
            border: 1px solid #f6cc4d;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        
        .security-text {
            font-size: 13px;
            color: #92400e;
            line-height: 1.5;
        }
        
        .footer {
            background: #f8fafc;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer-text {
            font-size: 12px;
            color: #718096;
            line-height: 1.5;
        }
        
        .brand-footer {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }
        
        .brand-logo {
            font-size: 16px;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .brand-tagline {
            font-size: 11px;
            color: #a0aec0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 12px;
            }
            
            .header, .content, .footer {
                padding: 20px;
            }
            
            .title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">TractionX</div>
            <div class="tagline">The Intelligence Era of Investing</div>
        </div>
        
        <!-- Content -->
        <div class="content">
            <div class="emoji-header">🤦‍♂️</div>
            
            <h1 class="title">Oops! Forgot Something?</h1>
            
            <p class="subtitle">
                Don't worry, it happens to the best of us! Even the most brilliant investors sometimes forget their passwords. 
                We've got your back. 💪
            </p>
            
            <div class="funny-section">
                <p class="funny-text">
                    <strong>Fun fact:</strong> Studies show that 73% of people forget their passwords right after creating them. 
                    The other 27% are lying. 😅 You're in excellent company with Warren Buffett, who once forgot his Berkshire Hathaway login 
                    and had to call his assistant. (Okay, we made that up, but it could totally happen!)
                </p>
            </div>
            
            <p style="font-size: 16px; line-height: 1.6; margin: 20px 0;">
                No judgment here! We know you're busy analyzing the next unicorn 🦄, evaluating market trends 📈, 
                and making those million-dollar decisions. Sometimes passwords just slip through the cracks.
            </p>
            
            <div class="cta-container">
                <a href="{{reset_url}}" class="cta-button">
                    🔐 Reset My Password
                </a>
            </div>
            
            <div class="security-note">
                <p class="security-text">
                    <strong>🛡️ Security Note:</strong> This link will expire in 2 hours for your protection. 
                    If you didn't request this reset, just ignore this email and your account will remain secure.
                </p>
            </div>
            
            <p style="font-size: 14px; color: #666; margin-top: 30px;">
                Once you're back in, maybe consider using a password manager? Your future self will thank you! 
                (And so will we, because we care about your digital wellbeing.) ❤️
            </p>
            
            <p style="font-size: 14px; color: #666; margin-top: 20px;">
                Still having trouble? Our support team is standing by, probably drinking coffee ☕ and ready to help. 
                Just reply to this email and we'll sort you out faster than you can say "due diligence."
            </p>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p class="footer-text">
                This email was sent to <strong>{{email}}</strong> because you requested a password reset for your TractionX account.
                <br><br>
                If you didn't make this request, you can safely ignore this email. Your account remains secure.
            </p>
            
            <div class="brand-footer">
                <div class="brand-logo">TractionX</div>
                <div class="brand-tagline">Powered by AI • Built for Investors • Made with ❤️</div>
            </div>
        </div>
    </div>
</body>
</html>
