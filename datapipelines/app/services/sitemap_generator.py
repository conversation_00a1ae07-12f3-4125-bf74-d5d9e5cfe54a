"""
Sitemap Generator Service for TractionX Data Pipeline Service.

This module provides the main orchestrator for website sitemap extraction,
combining BrightData Web Unlocker, HTML cleaning, and LLM analysis.
"""

import time
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from app.clients.brightdata_client import get_brightdata_client
from app.clients.together_client import get_together_client
from app.configs import get_logger
from app.storage.s3_storage import S3Storage
from app.utils.common import get_root_domain
from app.utils.html_cleaner import (
    extract_all_urls_from_html,
    extract_page_title,
)

logger = get_logger(__name__)


class SitemapGenerator:
    """
    Main sitemap generator service.

    Handles:
    - BrightData Web Unlocker integration
    - HTML cleaning and text extraction
    - BeautifulSoup-based URL extraction
    - LLM-based URL filtering (optional)
    - Result storage and validation
    """

    def __init__(self):
        self.logger = get_logger(f"{__name__}.SitemapGenerator")
        self.s3_storage: Optional[S3Storage] = None

    async def initialize(self) -> None:
        """Initialize the sitemap generator."""
        self.s3_storage = S3Storage()
        await self.s3_storage.initialize()
        self.logger.info("Sitemap generator initialized")
        return None

    async def cleanup(self) -> None:
        """Clean up service resources."""
        if self.s3_storage:
            await self.s3_storage.cleanup()
        self.logger.info("Sitemap generator cleaned up")

    async def generate_sitemap(
        self, domain: str, page_title: str = "", org_id: str = "unknown"
    ) -> Dict[str, Any]:
        """
        Generate sitemap for a given domain using BeautifulSoup URL extraction.

        Args:
            domain: Company domain (e.g., "tractionx.ai", "https://www.tractionx.ai")
            page_title: Optional page title for context
            org_id: Organization identifier

        Returns:
            Dictionary with sitemap results
        """
        start_time = time.time()

        try:
            # Clean and validate domain using get_root_domain
            clean_domain = get_root_domain(domain)
            if not clean_domain:
                return {
                    "success": False,
                    "error": f"Invalid domain format: {domain}",
                    "processing_time": time.time() - start_time,
                }

            self.logger.info(
                "Starting sitemap generation",
                original_domain=domain,
                clean_domain=clean_domain,
                page_title=page_title,
                org_id=org_id,
            )

            # Step 1: Fetch webpage with BrightData
            webpage_result = await self._fetch_webpage(clean_domain)
            if not webpage_result["success"]:
                return {
                    "success": False,
                    "error": f"Failed to fetch webpage: {webpage_result['error']}",
                    "processing_time": time.time() - start_time,
                }

            html_content = webpage_result["html_content"]
            s3_key = webpage_result["s3_key"]
            page_url = webpage_result["page_url"]

            # Step 2: Extract page title if not provided
            if not page_title:
                page_title = extract_page_title(html_content)

            # Step 3: Extract URLs using BeautifulSoup
            self.logger.info("Extracting URLs using BeautifulSoup", domain=clean_domain)
            extracted_urls = extract_all_urls_from_html(html_content, page_url)

            self.logger.info(
                f"Extracted {len(extracted_urls)} URLs from HTML",
                domain=clean_domain,
                url_count=len(extracted_urls),
            )

            # Step 4: Check if it's a JS-rendered app (if very few URLs extracted)
            if len(extracted_urls) < 5:
                self.logger.info(
                    "Few URLs extracted, likely JS-rendered app",
                    domain=clean_domain,
                    url_count=len(extracted_urls),
                )
                return await self._create_js_rendered_result(
                    clean_domain, org_id, s3_key, time.time() - start_time
                )
            logger.info(f"Extracted URLs: {extracted_urls}")
            # Step 5: Process and validate URLs
            final_urls = self._process_urls(clean_domain, extracted_urls)

            # Step 6: Optional LLM filtering for junk URLs (if too many URLs)
            if len(final_urls) > 50:
                self.logger.info(
                    "Large number of URLs detected, applying LLM filtering",
                    domain=clean_domain,
                    url_count=len(final_urls),
                )
                filtered_urls = await self._filter_urls_with_llm(
                    clean_domain, final_urls
                )
                final_urls = filtered_urls if filtered_urls else final_urls

            # Step 7: Store final result
            final_result = await self._store_sitemap_result(
                clean_domain,
                org_id,
                final_urls,
                s3_key,
                time.time() - start_time,
                js_rendered_detected=False,
            )

            self.logger.info(
                "Sitemap generation completed successfully",
                original_domain=domain,
                clean_domain=clean_domain,
                url_count=len(final_urls),
                processing_time=time.time() - start_time,
            )

            return final_result

        except Exception as e:
            error_msg = f"Sitemap generation failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            return {
                "success": False,
                "error": error_msg,
                "processing_time": time.time() - start_time,
            }

    async def _fetch_webpage(self, domain: str) -> Dict[str, Any]:
        """Fetch webpage using BrightData Web Unlocker."""
        try:
            # Ensure domain has protocol - use www version for better compatibility
            if not domain.startswith(("http://", "https://")):
                url = f"https://www.{domain}"
            else:
                url = domain

            # Get BrightData client
            brightdata_client = await get_brightdata_client()

            # Fetch webpage
            result = await brightdata_client.fetch_and_store_webpage(
                url=url,
                org_id="sitemap_generator",
                render_js=True,
            )

            if not result["success"]:
                return result

            # Extract HTML content from response
            response_data = result["data"]
            html_content = ""

            if isinstance(response_data, dict):
                # Try different possible keys for HTML content
                for key in ["html", "content", "body", "data"]:
                    if key in response_data:
                        html_content = response_data[key]
                        break
            elif isinstance(response_data, str):
                html_content = response_data
            else:
                html_content = str(response_data)

            return {
                "success": True,
                "html_content": html_content,
                "s3_key": result.get("s3_key", ""),
                "page_url": url,
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to fetch webpage: {str(e)}",
            }

    async def _filter_urls_with_llm(
        self, domain: str, urls: List[str]
    ) -> Optional[List[str]]:
        """
        Use LLM to filter out junk URLs from a large list.

        Args:
            domain: Company domain
            urls: List of URLs to filter

        Returns:
            Filtered list of URLs or None if filtering failed
        """
        try:
            # Get Together AI client
            together_client = await get_together_client()

            # Create a simple prompt for URL filtering
            url_list = "\n".join(urls[:100])  # Limit to first 100 URLs

            prompt = f"""
            You are analyzing URLs from the website {domain}.
            
            Below is a list of URLs extracted from the website. Please identify and return ONLY the URLs that are:
            1. Main navigation pages (About, Contact, Services, etc.)
            2. Product/service pages
            3. Blog/article pages
            4. Important company pages
            5. Include privacy pages too, as it may contain legal information
            
            EXCLUDE:
            - Social media links (facebook.com, twitter.com, linkedin.com, etc.)
            - External ad/tracking links
            - Cookie pages
            - Login/logout pages
            - Admin/backend pages
            - Duplicate URLs with different parameters
            - Obvious junk URLs
            
            URLs to analyze:
            {url_list}
            
            Return only the valid URLs, one per line:
            """

            # Use the LLM to filter URLs
            messages = [{"role": "user", "content": prompt}]

            result = await together_client.create_chat_completion(
                messages=messages,
                max_tokens=2000,
                temperature=0.1,
            )

            if not result["success"]:
                self.logger.warning("LLM URL filtering failed, using original URLs")
                return None

            # Parse the response to extract URLs
            filtered_urls = []
            response_text = result["data"]["choices"][0]["message"]["content"]
            for line in response_text.split("\n"):
                line = line.strip()
                if line and line in urls:
                    filtered_urls.append(line)

            self.logger.info(
                f"LLM filtered {len(urls)} URLs down to {len(filtered_urls)}",
                domain=domain,
                original_count=len(urls),
                filtered_count=len(filtered_urls),
            )

            return filtered_urls

        except Exception as e:
            self.logger.warning(f"LLM URL filtering failed: {str(e)}")
            return None

    def _process_urls(self, domain: str, urls: List[str]) -> List[str]:
        """Process and validate URLs."""
        try:
            # Remove duplicates and invalid URLs
            valid_urls = []
            seen_urls = set()

            for url in urls:
                url = url.strip()

                # Skip empty or invalid URLs
                if not url or not url.startswith(("http://", "https://")):
                    continue

                # Skip if already seen
                if url in seen_urls:
                    continue

                # Validate URL format
                if self._is_valid_internal_url(domain, url):
                    valid_urls.append(url)
                    seen_urls.add(url)

            # Sort URLs for consistency
            valid_urls.sort()

            # Ensure homepage is included (prefer www version if available, otherwise add non-www)
            www_homepage = f"https://www.{domain}"
            non_www_homepage = f"https://{domain}"

            # Check if either homepage version already exists
            has_www = www_homepage in valid_urls
            has_non_www = non_www_homepage in valid_urls

            # Add homepage if neither version exists
            if not has_www and not has_non_www:
                # Prefer www version for better compatibility
                valid_urls.insert(0, www_homepage)
                self.logger.info(f"Added homepage URL: {www_homepage}")
            elif has_www and not has_non_www:
                self.logger.info(f"Homepage already exists (www): {www_homepage}")
            elif has_non_www and not has_www:
                self.logger.info(
                    f"Homepage already exists (non-www): {non_www_homepage}"
                )
            else:
                self.logger.info(
                    f"Both homepage versions already exist: {www_homepage}, {non_www_homepage}"
                )

            return valid_urls

        except Exception as e:
            self.logger.error(f"Error processing URLs: {str(e)}")
            return []

    def _is_valid_internal_url(self, domain: str, url: str) -> bool:
        """Check if URL is a valid internal URL for the domain."""
        try:
            # Extract domain from URL
            if url.startswith("http://"):
                url_domain = url[7:].split("/")[0]
            elif url.startswith("https://"):
                url_domain = url[8:].split("/")[0]
            else:
                return False

            # Use get_root_domain for consistent domain comparison
            clean_domain = get_root_domain(domain)
            clean_url_domain = get_root_domain(url_domain)

            return clean_domain == clean_url_domain

        except Exception:
            return False

    async def _create_js_rendered_result(
        self, domain: str, org_id: str, s3_key: str, processing_time: float
    ) -> Dict[str, Any]:
        """Create result for JS-rendered apps."""
        result = {
            "success": True,
            "js_rendered_app": True,
            "urls": [],
            "domain": domain,
            "org_id": org_id,
            "s3_key": s3_key,
            "processing_time": processing_time,
            "generated_at": datetime.now(timezone.utc).isoformat(),
        }

        # Store result to S3
        await self._store_sitemap_to_s3(domain, org_id, result)

        return result

    async def _store_sitemap_result(
        self,
        domain: str,
        org_id: str,
        urls: List[str],
        s3_key: str,
        processing_time: float,
        js_rendered_detected: bool = False,
    ) -> Dict[str, Any]:
        """Store sitemap result and return final response."""
        result = {
            "success": True,
            "js_rendered_app": js_rendered_detected,
            "urls": urls,
            "domain": domain,
            "org_id": org_id,
            "s3_key": s3_key,
            "processing_time": processing_time,
            "generated_at": datetime.now(timezone.utc).isoformat(),
        }

        # Store result to S3
        await self._store_sitemap_to_s3(domain, org_id, result)

        return result

    async def _store_sitemap_to_s3(
        self, domain: str, org_id: str, result: Dict[str, Any]
    ) -> str:
        """Store sitemap result to S3."""
        try:
            if not self.s3_storage:
                raise RuntimeError("S3 storage not initialized")

            # Generate S3 key
            s3_key = f"company_sitemaps/{domain}/sitemap.json"

            # Store result
            success = await self.s3_storage.put_object(s3_key, result)
            if success:
                self.logger.info(f"Sitemap stored to S3: {s3_key}")
            else:
                self.logger.error(f"Failed to store sitemap to S3: {s3_key}")

            return s3_key

        except Exception as e:
            self.logger.error(f"Failed to store sitemap to S3: {str(e)}")
            return ""


# Global service instance
_sitemap_generator: Optional[SitemapGenerator] = None


async def get_sitemap_generator() -> SitemapGenerator:
    """Get or create the global sitemap generator instance."""
    global _sitemap_generator

    if _sitemap_generator is None:
        _sitemap_generator = SitemapGenerator()
        await _sitemap_generator.initialize()

    return _sitemap_generator


async def cleanup_sitemap_generator():
    """Clean up the global sitemap generator."""
    global _sitemap_generator

    if _sitemap_generator:
        await _sitemap_generator.cleanup()
        _sitemap_generator = None
