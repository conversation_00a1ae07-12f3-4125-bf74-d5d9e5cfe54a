"""
Barrier-aware task handlers for TractionX Data Pipeline Service.

This module provides decorators and utilities for making existing tasks
barrier-aware without modifying their core logic.
"""

import asyncio
import functools
import time
from typing import Any, Callable, Dict, Optional

from app.configs import get_logger
from app.configs.settings import settings
from app.queueing.barrier_sync import BarrierSync

logger = get_logger(__name__)


def barrier_aware_task(
    barrier_group_id: str,
    task_name: str,
    entity_type: str = "company",
    entity_id: Optional[str] = None,
):
    """
    Decorator to make a task barrier-aware.

    Args:
        barrier_group_id: Unique barrier group ID (e.g., "company:uuid")
        task_name: Name of this task for barrier tracking
        entity_type: Entity type (e.g., "company", "founder")
        entity_id: Entity ID (defaults to extracting from barrier_group_id)
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(job_args: Dict[str, Any]) -> Any:
            # Initialize barrier sync
            barrier_sync = BarrierSync(
                redis_url=settings.redis_connection_string,
                mongo_url=settings.mongo_connection_string,
            )
            await barrier_sync.initialize()

            # Extract entity_id if not provided
            if entity_id is None:
                entity_id_value = (
                    barrier_group_id.split(":", 1)[1]
                    if ":" in barrier_group_id
                    else barrier_group_id
                )
            else:
                entity_id_value = entity_id

            logger.info(f"Entity ID: {entity_id_value}")

            start_time = time.time()
            result = None
            error_message = None

            try:
                # Execute the original task
                if asyncio.iscoroutinefunction(func):
                    result = await func(job_args)
                else:
                    # Run sync function in thread pool
                    loop = asyncio.get_running_loop()
                    result = await loop.run_in_executor(None, func, job_args)

                processing_time = time.time() - start_time

                # Mark task as complete
                await barrier_sync.mark_task_complete(
                    group_id=barrier_group_id,
                    task_name=task_name,
                    metadata={
                        "processing_time": processing_time,
                        "result_keys": list(result.keys())
                        if isinstance(result, dict)
                        else None,
                    },
                )

                return result

            except Exception as e:
                error_message = str(e)
                logger.error(
                    f"Task {task_name} failed for barrier {barrier_group_id}: {error_message}"
                )

                # Mark task as failed
                await barrier_sync.mark_task_complete(
                    group_id=barrier_group_id,
                    task_name=task_name,
                    error_message=error_message,
                )

                raise

            finally:
                await barrier_sync.cleanup()

        return wrapper

    return decorator


async def register_dynamic_tasks(
    barrier_group_id: str,
    new_tasks: list[str],
    task_payloads: Dict[str, Dict[str, Any]],
    queue_service,
) -> None:
    """
    Register new tasks to an existing barrier and enqueue them.

    Args:
        barrier_group_id: Barrier group ID
        new_tasks: List of new task names to register
        task_payloads: Dict mapping task names to their job arguments
        queue_service: Queue service instance for enqueueing jobs
    """
    try:
        # Initialize barrier sync
        barrier_sync = BarrierSync(
            redis_url=settings.redis_connection_string,
            mongo_url=settings.mongo_connection_string,
        )
        await barrier_sync.initialize()

        # Register new tasks with barrier
        added_count = await barrier_sync.register_tasks(
            group_id=barrier_group_id, new_tasks=new_tasks
        )

        if added_count > 0:
            # Enqueue the new tasks
            for task_name in new_tasks:
                if task_name in task_payloads:
                    job_args = task_payloads[task_name]

                    # Add barrier metadata
                    meta = {
                        "barrier_group_id": barrier_group_id,
                        "task_name": task_name,
                        "entity_type": "company",  # Default, can be overridden
                    }

                    # Enqueue the job
                    await queue_service.enqueue_job(
                        job_func=f"{task_name}",
                        job_args=job_args,
                        meta=meta,
                        priority="normal",
                    )

                    logger.info(
                        f"Enqueued dynamic task {task_name} for barrier {barrier_group_id}"
                    )

        await barrier_sync.cleanup()

    except Exception as e:
        logger.error(
            f"Failed to register dynamic tasks for barrier {barrier_group_id}: {e}"
        )
        raise


async def merge_enrichment_results(job_args: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge function that runs when all barrier tasks are complete.

    This is the final step in the barrier orchestration process.
    """
    barrier_group_id = job_args.get("barrier_group_id")
    entity_type = job_args.get("entity_type", "company")
    entity_id = job_args.get("entity_id")

    if not barrier_group_id:
        raise ValueError("barrier_group_id is required for merge function")

    logger.info(f"Starting merge for barrier {barrier_group_id}")

    try:
        # Initialize barrier sync
        barrier_sync = BarrierSync(
            redis_url=settings.redis_connection_string,
            mongo_url=settings.mongo_connection_string,
        )
        await barrier_sync.initialize()

        # Get barrier status
        barrier_status = await barrier_sync.get_barrier_status(barrier_group_id)
        if not barrier_status:
            raise ValueError(f"Barrier {barrier_group_id} not found")

        # Update barrier status to done
        collection = barrier_sync.mongo_db_instance.barriers  # type: ignore
        await asyncio.get_event_loop().run_in_executor(
            None,
            collection.update_one,
            {"_id": barrier_group_id},
            {
                "$set": {
                    "status": "done",
                    "updated_at": time.time(),
                    "merge_triggered": True,
                }
            },
        )

        # Here you would implement the actual merge logic
        # For now, we'll just return a summary
        merge_result = {
            "barrier_group_id": barrier_group_id,
            "entity_type": entity_type,
            "entity_id": entity_id,
            "completed_tasks": barrier_status.completed_tasks,
            "total_tasks": len(barrier_status.expected_tasks),
            "merge_timestamp": time.time(),
            "status": "completed",
        }

        logger.info(f"Merge completed for barrier {barrier_group_id}")

        await barrier_sync.cleanup()
        return merge_result

    except Exception as e:
        logger.error(f"Merge failed for barrier {barrier_group_id}: {e}")

        # Update barrier status to error
        try:
            collection = barrier_sync.mongo_db_instance.barriers  # type: ignore
            await asyncio.get_event_loop().run_in_executor(
                None,
                collection.update_one,
                {"_id": barrier_group_id},
                {
                    "$set": {
                        "status": "error",
                        "updated_at": time.time(),
                    }
                },
            )
        except Exception as update_error:
            logger.error(f"Failed to update barrier status to error: {update_error}")

        raise


# Example usage of barrier-aware tasks
@barrier_aware_task(
    barrier_group_id="company:example-uuid",
    task_name="linkedin",
    entity_type="company",
    entity_id="example-uuid",
)
async def enrich_linkedin_barrier_aware(job_args: Dict[str, Any]) -> Dict[str, Any]:
    """
    Example of a barrier-aware LinkedIn enrichment task.

    This would be your existing LinkedIn enrichment logic wrapped with
    barrier coordination.
    """
    # Your existing LinkedIn enrichment logic here
    company_id = job_args.get("company_id")

    # Simulate some work
    await asyncio.sleep(2)

    return {
        "company_id": company_id,
        "linkedin_data": {
            "profile_url": "https://linkedin.com/company/example",
            "employee_count": 100,
            "industry": "Technology",
        },
        "enrichment_source": "linkedin",
    }


@barrier_aware_task(
    barrier_group_id="company:example-uuid",
    task_name="crunchbase",
    entity_type="company",
    entity_id="example-uuid",
)
async def enrich_crunchbase_barrier_aware(job_args: Dict[str, Any]) -> Dict[str, Any]:
    """
    Example of a barrier-aware Crunchbase enrichment task.
    """
    company_id = job_args.get("company_id")

    # Simulate some work
    await asyncio.sleep(1.5)

    return {
        "company_id": company_id,
        "crunchbase_data": {
            "funding_rounds": 3,
            "total_funding": "$10M",
            "investors": ["VC1", "VC2"],
        },
        "enrichment_source": "crunchbase",
    }


@barrier_aware_task(
    barrier_group_id="company:example-uuid",
    task_name="website",
    entity_type="company",
    entity_id="example-uuid",
)
async def enrich_website_barrier_aware(job_args: Dict[str, Any]) -> Dict[str, Any]:
    """
    Example of a barrier-aware website enrichment task.
    """
    company_id = job_args.get("company_id")
    website_url = job_args.get("website_url")

    # Simulate website analysis
    await asyncio.sleep(3)

    return {
        "company_id": company_id,
        "website_data": {
            "url": website_url,
            "technologies": ["React", "Node.js", "AWS"],
            "contact_info": {
                "email": "<EMAIL>",
                "phone": "******-0123",
            },
        },
        "enrichment_source": "website",
    }
