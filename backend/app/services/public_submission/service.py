from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union

from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.logging import get_logger
from app.models.public_submission import PublicSubmission, PublicSubmissionAccess
from app.services.base import BaseService
from app.services.public_submission.interface import PublicSubmissionServiceInterface
from app.utils.common import PyObjectId

logger = get_logger(__name__)


class PublicSubmissionService(BaseService, PublicSubmissionServiceInterface):
    """MongoDB implementation of PublicSubmissionService."""

    def __init__(self, db: AsyncIOMotorDatabase):
        super().__init__()
        self.db = db

    async def initialize(self) -> None:
        """Initialize database connection and indexes."""
        # Create indexes for efficient queries
        await self.db.publicsubmissions.create_index(
            [("sharing_token", 1), ("public_user_email", 1)], unique=True
        )
        await self.db.publicsubmissions.create_index("public_user_id")
        await self.db.publicsubmissions.create_index("sharing_token")
        await self.db.publicsubmissions.create_index("status")
        await self.db.publicsubmissions.create_index("token_expires_at")

        # Indexes for access tracking
        await self.db.publicsubmissions.create_index("public_submission_id")
        await self.db.publicsubmissions.create_index("timestamp")

    async def cleanup(self) -> None:
        """Cleanup database connection."""
        pass

    async def get_or_create_submission(
        self,
        public_user_id: str,
        public_user_email: str,
        sharing_token: str,
        form_id: str,
        org_id: str,
        token_expires_at: Optional[int] = None,
    ) -> Union[PublicSubmission, None]:
        """Get existing submission or create new one for public user + token."""
        try:
            # Try to find existing submission
            existing = await PublicSubmission.find_one({
                "sharing_token": sharing_token,
                "public_user_email": public_user_email,
            })

            if existing:
                logger.info(
                    f"Found existing submission for {public_user_email} with token {sharing_token}"
                )
                return existing

            # Create new submission
            submission = PublicSubmission(
                public_user_id=PyObjectId(public_user_id),
                public_user_email=public_user_email,
                sharing_token=sharing_token,
                form_id=PyObjectId(form_id),
                org_id=PyObjectId(org_id),
                token_expires_at=token_expires_at,
            )

            await submission.save()
            logger.info(
                f"Created new public submission for {public_user_email} with token {sharing_token}"
            )
            return submission

        except Exception as e:
            await self.handle_error(
                e,
                {
                    "public_user_id": public_user_id,
                    "public_user_email": public_user_email,
                    "sharing_token": sharing_token,
                    "form_id": form_id,
                    "org_id": org_id,
                },
            )

    async def get_submission_by_token_and_email(
        self, sharing_token: str, public_user_email: str
    ) -> Optional[PublicSubmission]:
        """Get submission by sharing token and public user email."""
        try:
            submission = await PublicSubmission.find_one({
                "sharing_token": sharing_token,
                "public_user_email": public_user_email,
            })
            return submission
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "sharing_token": sharing_token,
                    "public_user_email": public_user_email,
                },
            )

    async def update_submission_progress(
        self,
        submission_id: str,
        answers: Dict[str, Any],
        progress: float,
        last_question: Optional[str] = None,
    ) -> Optional[PublicSubmission]:
        """Update the progress of a submission."""
        try:
            submission = await PublicSubmission.find_one({
                "_id": PyObjectId(submission_id)
            })
            if not submission:
                return None

            if not submission.can_edit():
                logger.warning(
                    f"Attempted to edit non-editable submission {submission_id}"
                )
                return None

            submission.update_progress(answers, progress, last_question)
            await submission.save(is_update=True)

            logger.info(f"Updated progress for submission {submission_id}: {progress}%")
            return submission

        except Exception as e:
            await self.handle_error(
                e, {"submission_id": submission_id, "progress": progress}
            )

    async def finalize_submission(
        self, public_submission_id: str, final_submission_id: str
    ) -> Optional[PublicSubmission]:
        """Mark a public submission as finalized with the actual submission ID."""
        try:
            submission = await PublicSubmission.find_one({
                "_id": PyObjectId(public_submission_id)
            })
            if not submission:
                return None

            if not submission.can_edit():
                logger.warning(
                    f"Attempted to finalize non-editable submission {public_submission_id}"
                )
                return None

            submission.mark_submitted(final_submission_id)
            await submission.save(is_update=True)

            logger.info(
                f"Finalized public submission {public_submission_id} -> {final_submission_id}"
            )
            return submission

        except Exception as e:
            await self.handle_error(
                e,
                {
                    "public_submission_id": public_submission_id,
                    "final_submission_id": final_submission_id,
                },
            )

    async def validate_submission_access(
        self, sharing_token: str, public_user_email: str
    ) -> Union[Dict[str, Any], None]:
        """Validate if a public user can access a submission via token."""
        try:
            submission = await self.get_submission_by_token_and_email(
                sharing_token, public_user_email
            )

            if not submission:
                return {
                    "valid": False,
                    "reason": "No submission found for this token and email",
                    "can_create": True,
                }

            if not submission.is_active:
                return {
                    "valid": False,
                    "reason": "Submission is inactive",
                    "can_create": False,
                }

            # Check token expiration
            now = int(datetime.now(timezone.utc).timestamp())
            if submission.token_expires_at and submission.token_expires_at < now:
                return {
                    "valid": False,
                    "reason": "Token has expired",
                    "can_create": False,
                }

            return {
                "valid": True,
                "submission": submission,
                "can_edit": submission.can_edit(),
                "status": submission.status,
            }

        except Exception as e:
            await self.handle_error(
                e,
                {
                    "sharing_token": sharing_token,
                    "public_user_email": public_user_email,
                },
            )

    async def track_access(
        self,
        public_submission_id: str,
        public_user_email: str,
        sharing_token: str,
        access_type: str,
        success: bool,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        error_message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Union[PublicSubmissionAccess, None]:
        """Track access to a public submission."""
        try:
            access_record = PublicSubmissionAccess(
                public_submission_id=PyObjectId(public_submission_id),
                public_user_email=public_user_email,
                sharing_token=sharing_token,
                access_type=access_type,
                success=success,
                ip_address=ip_address,
                user_agent=user_agent,
                error_message=error_message,
                metadata=metadata or {},
            )

            await access_record.save()
            return access_record

        except Exception as e:
            await self.handle_error(
                e,
                {
                    "public_submission_id": public_submission_id,
                    "access_type": access_type,
                    "success": success,
                },
            )

    async def get_submission_by_id(
        self, submission_id: str
    ) -> Optional[PublicSubmission]:
        """Get public submission by ID."""
        try:
            return await PublicSubmission.find_one({"_id": PyObjectId(submission_id)})
        except Exception as e:
            await self.handle_error(e, {"submission_id": submission_id})

    async def list_submissions_by_user(
        self, public_user_email: str, skip: int = 0, limit: int = 100
    ) -> Union[List[PublicSubmission], None]:
        """List all submissions by a public user."""
        try:
            submissions = await PublicSubmission.find_many(
                query={"public_user_email": public_user_email},
                skip=skip,
                limit=limit,
            )
            return submissions or []
        except Exception as e:
            await self.handle_error(e, {"public_user_email": public_user_email})

    async def list_submissions_by_token(
        self, sharing_token: str, skip: int = 0, limit: int = 100
    ) -> Union[List[PublicSubmission], None]:
        """List all submissions for a sharing token."""
        try:
            submissions = await PublicSubmission.find_many(
                {"sharing_token": sharing_token},
                skip=skip,
                limit=limit,
            )
            return submissions
        except Exception as e:
            await self.handle_error(e, {"sharing_token": sharing_token})

    async def cleanup_expired_submissions(self) -> Union[int, None]:
        """Clean up expired submissions and return count of cleaned up items."""
        try:
            now = int(datetime.now(timezone.utc).timestamp())

            # Mark expired submissions as inactive
            result = await self.db.publicsubmissions.update_many(
                {"token_expires_at": {"$lt": now}, "is_active": True},
                {"$set": {"is_active": False, "updated_at": now}},
            )

            logger.info(
                f"Marked {result.modified_count} expired submissions as inactive"
            )
            return result.modified_count

        except Exception as e:
            await self.handle_error(e, {})
