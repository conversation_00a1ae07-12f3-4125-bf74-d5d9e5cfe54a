"use client"

import React, { useState } from 'react';
import { Shell } from "@/components/shell"
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertTriangle, Play, RefreshCw } from 'lucide-react';
import { MinimalThesisBuilder } from "@/components/core/thesis-builder/minimal-thesis-builder"
import { ThesisBuilder } from "@/components/core/thesis-builder/thesis-builder"
import { useAuth } from "@/lib/auth-context"
import { TextScoringDebug } from '@/components/core/thesis-builder/text-scoring-debug';

type TestMode = 'minimal' | 'full' | 'none';

export default function TextScoringTestPage() {
  return (
    <div className="container mx-auto max-w-6xl py-6">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Text Scoring Rule Testing</h1>
          <p className="text-muted-foreground">
            Isolated testing environment for text-based scoring rule functionality
          </p>
        </div>
        
        <TextScoringDebug />
      </div>
    </div>
  );
}
