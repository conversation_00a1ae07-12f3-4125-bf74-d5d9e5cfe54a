# TractionX Data Pipelines - Complete System Overview

## Table of Contents
1. [System Purpose and Architecture](#system-purpose-and-architecture)
2. [Core Components Explained](#core-components-explained)
3. [Pipelines vs Tasks - The Key Distinction](#pipelines-vs-tasks---the-key-distinction)
4. [Data Flow and Processing](#data-flow-and-processing)
5. [Storage Strategy](#storage-strategy)
6. [External Integrations](#external-integrations)
7. [Quick Start Guide](#quick-start-guide)
8. [Documentation Index](#documentation-index)

## System Purpose and Architecture

### What is TractionX Data Pipelines?

The TractionX Data Pipelines service is a **standalone, modular data processing system** designed specifically for investment firms to automate the collection, enrichment, and analysis of deal-related data. It serves as the data backbone for the TractionX investment platform.

### Why Do We Need This System?

**The Investment Data Challenge:**
- Investment firms receive deal submissions from multiple channels (forms, emails, APIs)
- Each deal requires enrichment from various external sources (company databases, founder profiles, news)
- Data comes in different formats and needs to be standardized
- Processing must be fast, reliable, and scalable
- Results need to be searchable and analyzable

**The Solution:**
- **Automated enrichment** from external APIs (Clay, PDL, Bing News, OpenAI)
- **Asynchronous processing** using Redis Queue for high throughput
- **Multi-storage architecture** optimized for different data types
- **Standardized data models** for consistent analysis
- **Extensible framework** for adding new data sources

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    TractionX Data Pipelines                     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────┐  │
│  │ FastAPI App │    │ RQ Workers  │    │   Storage Layer     │  │
│  │             │    │             │    │                     │  │
│  │ • Webhooks  │◄──►│ • Pipelines │◄──►│ • PostgreSQL (RDS)  │  │
│  │ • API       │    │ • Tasks     │    │ • S3 Object Store   │  │
│  │ • Health    │    │ • Jobs      │    │ • Qdrant Vectors    │  │
│  │             │    │             │    │ • Redis Cache       │  │
│  └─────────────┘    └─────────────┘    └─────────────────────┘  │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│                     External Integrations                      │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │   Clay API  │  │   PDL API   │  │ Bing News   │  │ OpenAI  │ │
│  │ (Company)   │  │ (Founders)  │  │ (News)      │  │ (AI)    │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Core Components Explained

### 1. FastAPI Application (`app/main.py`)
**Role**: HTTP interface for the entire system
- **Webhooks**: Receive data from external services
- **API Endpoints**: Manual pipeline triggers and status checks
- **Health Monitoring**: System health and metrics
- **Documentation**: Auto-generated API docs

### 2. RQ Workers (`app/worker.py`)
**Role**: Background job processing engine
- **Job Queue**: Process jobs from Redis queue
- **Parallel Processing**: Multiple workers for scalability
- **Error Handling**: Retry logic and failure management
- **Hot Reload**: Development-friendly auto-restart

### 3. Pipelines (`app/pipelines/`)
**Role**: Core business logic for data processing
- **Object-Oriented**: Reusable, stateful classes
- **Lifecycle Management**: Initialize → Process → Cleanup
- **Error Recovery**: Built-in validation and error handling
- **External API Integration**: Calls to Clay, PDL, etc.

### 4. Tasks (`app/tasks/`)
**Role**: Job queue interface layer
- **RQ Integration**: Bridge between job queue and pipelines
- **Serialization**: Handle job data formatting
- **Result Management**: Format results for storage
- **Sync Wrappers**: Provide synchronous interface for RQ

### 5. Storage Layer (`app/storage/`)
**Role**: Abstracted data persistence
- **Multi-Storage**: Different storage types for different needs
- **Interface Pattern**: Consistent API across storage types
- **Connection Management**: Pooling and resource optimization
- **Error Handling**: Retry logic and failover

### 6. Data Models (`app/models/`)
**Role**: Type-safe data structures
- **Pydantic Models**: Validation and serialization
- **Schema Definition**: Clear data contracts
- **API Documentation**: Auto-generated from models
- **Type Safety**: Compile-time error checking

## Pipelines vs Tasks - The Key Distinction

This is one of the most important concepts to understand in the system.

### Pipelines: The Business Logic Engine

**What they are:**
- Object-oriented classes that contain the actual data processing logic
- Inherit from `BasePipeline` abstract class
- Handle external API calls, data transformation, and validation

**Why they exist:**
```python
class CompanyEnrichmentPipeline(BasePipeline):
    def __init__(self):
        self.clay_client = ClayAPIClient()
        self.storage = StorageService()
    
    async def _process_data(self, input_data):
        # 1. Call Clay API
        clay_data = await self.clay_client.enrich_company(input_data["company_name"])
        
        # 2. Transform data
        enriched_data = self._transform_clay_data(clay_data)
        
        # 3. Store raw data in S3
        await self.storage.store_raw_data(clay_data)
        
        # 4. Return processed result
        return ProcessingResult.success_result(enriched_data)
```

**Key characteristics:**
- **Stateful**: Maintain connections and configuration
- **Reusable**: Can be used in tests, direct calls, or job queue
- **Lifecycle**: Initialize resources, process data, cleanup
- **Error Handling**: Comprehensive logging and recovery

### Tasks: The Job Queue Interface

**What they are:**
- Simple functions that wrap pipeline execution for RQ compatibility
- Handle job-specific concerns like serialization and result formatting
- Provide synchronous interface for the async pipeline system

**Why they exist:**
```python
async def enrich_company_data(job_data: Dict[str, Any]) -> Dict[str, Any]:
    """Async task that wraps pipeline execution"""
    # 1. Extract job parameters
    company_id = job_data.get("company_id")
    
    # 2. Initialize pipeline
    pipeline = CompanyEnrichmentPipeline("company_enrichment")
    await pipeline.initialize()
    
    try:
        # 3. Execute pipeline
        result = await pipeline.process(job_data)
        
        # 4. Store results in database
        if result.success:
            await store_enrichment_result(company_id, result.data)
        
        # 5. Format for job queue
        return {
            "success": result.success,
            "job_id": job_data["job_id"],
            "company_id": company_id,
            "data": result.data
        }
    finally:
        # 6. Cleanup
        await pipeline.cleanup()

def enrich_company_data_sync(job_data: Dict[str, Any]) -> Dict[str, Any]:
    """Synchronous wrapper required by RQ"""
    return asyncio.run(enrich_company_data(job_data))
```

**Key characteristics:**
- **Stateless**: No persistent state between executions
- **Job-Focused**: Handle RQ serialization and metadata
- **Simple**: Minimal logic, mostly orchestration
- **Sync Interface**: Required for RQ compatibility

### Why Both Are Needed

1. **Separation of Concerns**
   - Pipelines: Focus on data processing logic
   - Tasks: Focus on job queue integration

2. **Testability**
   - Pipelines can be tested independently
   - Tasks can be tested with mock pipelines

3. **Reusability**
   - Pipelines work outside job queue context
   - Tasks provide standardized job interface

4. **Performance**
   - Pipelines optimize for data processing
   - Tasks handle job distribution

## Data Flow and Processing

### Complete Data Journey

```
1. Data Entry
   ├── Form Submission (Frontend → Backend)
   ├── API Call (External System → API)
   └── Webhook (External Service → Webhook Endpoint)
                    ↓
2. Job Creation
   ├── Validate Input Data
   ├── Create Job Metadata
   └── Queue Jobs in Redis
                    ↓
3. Parallel Processing
   ├── Company Enrichment (Clay API)
   ├── Founder Enrichment (PDL API)
   ├── News Aggregation (Bing News)
   └── Embedding Generation (OpenAI)
                    ↓
4. Data Storage
   ├── Raw Data → S3 (Archive)
   ├── Structured Data → PostgreSQL (Query)
   ├── Vectors → Qdrant (Search)
   └── Job Status → Redis (Cache)
                    ↓
5. ETL Merge
   ├── Collect All Enrichment Results
   ├── Apply Merge Rules (Priority-based)
   ├── Create Canonical Record
   └── Store Final Result
```

### Processing Patterns

#### Sequential Processing
```
Company Data → Company Enrichment → Founder Enrichment → Final Merge
```
Used when: Later stages depend on earlier results

#### Parallel Processing
```
Input Data
    ├── Company Enrichment
    ├── Founder Enrichment
    ├── News Aggregation
    └── Embedding Generation
         ↓
    Merge All Results
```
Used when: Stages are independent and can run simultaneously

## Storage Strategy

### Why Multiple Storage Types?

Each storage type is optimized for specific use cases:

#### PostgreSQL (RDS) - Structured Data
- **Purpose**: Queryable, relational data
- **Content**: Final merged records, metadata, relationships
- **Benefits**: ACID compliance, complex queries, joins
- **Use Cases**: Dashboard queries, reporting, analytics

#### S3 - Raw Data Archive
- **Purpose**: Long-term storage of original data
- **Content**: Raw API responses, documents, backups
- **Benefits**: Scalable, cost-effective, versioning
- **Use Cases**: Audit trails, data recovery, compliance

#### Qdrant - Vector Search
- **Purpose**: Semantic similarity search
- **Content**: Text embeddings, vector indices
- **Benefits**: Fast similarity search, ML integration
- **Use Cases**: Finding similar companies, semantic search

#### Redis - Cache and Queue
- **Purpose**: High-speed temporary storage
- **Content**: Job queue, session data, cache
- **Benefits**: Sub-millisecond access, pub/sub
- **Use Cases**: Job processing, real-time features

## External Integrations

### Clay API - Company Enrichment
- **Data**: Company profiles, financials, technology stack
- **Usage**: Primary company enrichment source
- **Integration**: REST API with authentication

### People Data Labs (PDL) - Founder Enrichment  
- **Data**: Professional backgrounds, education, experience
- **Usage**: Founder and team member profiles
- **Integration**: REST API with rate limiting

### Bing News API - Market Intelligence
- **Data**: News articles, press releases, market sentiment
- **Usage**: Company news and market analysis
- **Integration**: Search API with filtering

### OpenAI - AI Processing
- **Data**: Text embeddings, content generation
- **Usage**: Semantic search, similarity matching
- **Integration**: REST API with token-based billing

## Quick Start Guide

### 1. Prerequisites
```bash
# Required
- Docker and Docker Compose
- Redis server
- PostgreSQL database
- AWS S3 bucket

# Optional (for local development)
- Python 3.11+
- Poetry
```

### 2. Installation
```bash
# Clone repository
cd datapipelines

# Configure environment
cp .env.example .env
# Edit .env with your settings

# Start services
docker compose up --build
```

### 3. Verify Installation
```bash
# Check service health
curl http://localhost:8001/health

# View API documentation
open http://localhost:8001/docs
```

### 4. Trigger Your First Pipeline
```bash
curl -X POST http://localhost:8001/api/v1/pipelines/trigger \
  -H "Content-Type: application/json" \
  -d '{
    "company_id": "test_company",
    "org_id": "test_org",
    "company_name": "Test Company",
    "pipeline_types": ["company"]
  }'
```

## Documentation Index

### Core Documentation Files

1. **[datapipelines-architecture.md](./datapipelines-architecture.md)**
   - **Purpose**: Deep technical architecture
   - **Audience**: Developers, architects
   - **Content**: Component design, storage patterns, security

2. **[datapipelines-operations.md](./datapipelines-operations.md)**
   - **Purpose**: Deployment and operations
   - **Audience**: DevOps, system administrators
   - **Content**: Setup, configuration, monitoring, troubleshooting

3. **[datapipelines-integration.md](./datapipelines-integration.md)**
   - **Purpose**: Data flow and extensions
   - **Audience**: Developers, data engineers
   - **Content**: External APIs, ETL processes, adding new pipelines

4. **[datapipelines-overview.md](./datapipelines-overview.md)** (This file)
   - **Purpose**: Complete system overview
   - **Audience**: All stakeholders
   - **Content**: High-level concepts, quick start, navigation

### When to Use Each Document

- **New to the system?** Start with this overview
- **Setting up for the first time?** Use the operations guide
- **Need to understand the internals?** Read the architecture doc
- **Adding new features?** Check the integration guide
- **Troubleshooting issues?** Refer to operations guide

This comprehensive overview provides the foundation for understanding the TractionX Data Pipelines system. For detailed implementation information, refer to the specific documentation files linked above.
