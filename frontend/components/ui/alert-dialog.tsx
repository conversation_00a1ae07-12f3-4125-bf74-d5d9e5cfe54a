"use client"

import * as React from "react"
import {
  Root as AlertDialog,
  <PERSON><PERSON> as AlertDialogTrigger,
  Portal as AlertDialogPortal,
  Overlay as AlertDialogOverlay,
  Content as AlertDialogContent,
  Title as AlertDialogTitle,
  Description as AlertDialogDescription,
  Action as AlertDialogAction,
  Cancel as AlertDialogCancel,
} from "@radix-ui/react-alert-dialog"

import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"

const AlertDialogPortalComponent = ({
  children,
  ...props
}: React.ComponentPropsWithoutRef<typeof AlertDialogPortal>) => (
  <AlertDialogPortal {...props}>
    {children}
  </AlertDialogPortal>
)
AlertDialogPortalComponent.displayName = "AlertDialogPortal"

const AlertDialogOverlayComponent = React.forwardRef<
  React.ElementRef<typeof AlertDialogOverlay>,
  React.ComponentPropsWithoutRef<typeof AlertDialogOverlay>
>(({ className, children, ...props }, ref) => (
  <AlertDialogOverlay
    className={cn(
      "fixed inset-0 z-50 bg-black/50 backdrop-blur-sm transition-all duration-300 ease-out",
      "data-[state=closed]:animate-out data-[state=closed]:fade-out-0",
      "data-[state=open]:animate-in data-[state=open]:fade-in-0",
      className
    )}
    {...props}
    ref={ref}
  />
))
AlertDialogOverlayComponent.displayName = "AlertDialogOverlay"

const AlertDialogContentComponent = React.forwardRef<
  React.ElementRef<typeof AlertDialogContent>,
  React.ComponentPropsWithoutRef<typeof AlertDialogContent>
>(({ className, ...props }, ref) => (
  <AlertDialogPortalComponent>
    <AlertDialogOverlayComponent />
    <AlertDialogContent
      ref={ref}
      className={cn(
        // Always centered, compact alert modal
        "fixed left-1/2 top-1/2 z-50 -translate-x-1/2 -translate-y-1/2",
        "w-[90vw] min-w-[280px] max-w-xs",
        "max-h-[80vh]",
        "bg-white/90 backdrop-blur-lg",
        "rounded-2xl shadow-xl",
        "p-6",
        // Mobile adjustments
        "xs:p-5",
        // Animations
        "data-[state=open]:animate-in data-[state=open]:fade-in-0 data-[state=open]:zoom-in-95",
        "data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95",
        "transition-all duration-300 ease-out",
        "flex flex-col gap-4",
        className
      )}
      {...props}
    />
  </AlertDialogPortalComponent>
))
AlertDialogContentComponent.displayName = "AlertDialogContent"

const AlertDialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "mb-4 text-center",
      className
    )}
    {...props}
  />
)
AlertDialogHeader.displayName = "AlertDialogHeader"

const AlertDialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "mt-4 flex justify-end gap-3 pt-4",
      // Mobile: stack vertically below 480px
      "max-xs:flex-col max-xs:gap-2",
      className
    )}
    {...props}
  />
)
AlertDialogFooter.displayName = "AlertDialogFooter"

const AlertDialogTitleComponent = React.forwardRef<
  React.ElementRef<typeof AlertDialogTitle>,
  React.ComponentPropsWithoutRef<typeof AlertDialogTitle>
>(({ className, ...props }, ref) => (
  <AlertDialogTitle
    ref={ref}
    className={cn("text-center text-xl font-semibold text-gray-900", className)}
    {...props}
  />
))
AlertDialogTitleComponent.displayName = "AlertDialogTitle"

const AlertDialogDescriptionComponent = React.forwardRef<
  React.ElementRef<typeof AlertDialogDescription>,
  React.ComponentPropsWithoutRef<typeof AlertDialogDescription>
>(({ className, ...props }, ref) => (
  <AlertDialogDescription
    ref={ref}
    className={cn("mt-2 text-center text-base text-gray-600", className)}
    {...props}
  />
))
AlertDialogDescriptionComponent.displayName = "AlertDialogDescription"

const AlertDialogActionComponent = React.forwardRef<
  React.ElementRef<typeof AlertDialogAction>,
  React.ComponentPropsWithoutRef<typeof AlertDialogAction>
>(({ className, ...props }, ref) => (
  <AlertDialogAction
    ref={ref}
    className={cn(
      buttonVariants(),
      "min-h-touch px-8 py-3", // Big touch targets
      className
    )}
    {...props}
  />
))
AlertDialogActionComponent.displayName = "AlertDialogAction"

const AlertDialogCancelComponent = React.forwardRef<
  React.ElementRef<typeof AlertDialogCancel>,
  React.ComponentPropsWithoutRef<typeof AlertDialogCancel>
>(({ className, ...props }, ref) => (
  <AlertDialogCancel
    ref={ref}
    className={cn(
      buttonVariants({ variant: "outline" }),
      "min-h-touch px-8 py-3", // Big touch targets
      className
    )}
    {...props}
  />
))
AlertDialogCancelComponent.displayName = "AlertDialogCancel"

export {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContentComponent as AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitleComponent as AlertDialogTitle,
  AlertDialogDescriptionComponent as AlertDialogDescription,
  AlertDialogActionComponent as AlertDialogAction,
  AlertDialogCancelComponent as AlertDialogCancel,
}
