# Git
.git
.gitignore
.github

# Docker
.dockerignore
docker-compose*.yml
Dockerfile*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.mypy_cache/

# Virtual Environment
venv/
.venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# OS specific
.DS_Store
Thumbs.db

# Project specific
.env
.env.*
!.env.example
