from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import Depends, HTTPException, status
from pydantic import BaseModel, Field

from app.api.base import BaseAPIRouter
from app.core.logging import get_logger
from app.models.form import Submission
from app.models.public_submission import PublicSubmission
from app.services.factory import get_queue_service
from app.services.queue.interfaces import Job<PERSON><PERSON>rity, JobStatus, QueueType
from app.utils.common import PyObjectId
from app.utils.jobs.job_utils import create_job_chain
from app.utils.rbac.rbac import rbac_register

router = BaseAPIRouter(prefix="/queue", tags=["queue"])

logger = get_logger(__name__)


class EnqueueJobRequest(BaseModel):
    job_type: str = Field(..., description="Type of job to process")
    payload: Dict[str, Any] = Field(..., description="Job data")
    queue_type: QueueType = Field(
        default=QueueType.DEFAULT, description="Which queue to use"
    )
    priority: JobPriority = Field(
        default=JobPriority.NORMAL, description="Job priority"
    )
    delay_seconds: int = Field(
        default=0, description="Delay before job is available for processing"
    )
    retry_config: Optional[Dict[str, Any]] = Field(
        default=None, description="Configuration for retries"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional metadata for the job"
    )


class ScheduleJobRequest(BaseModel):
    job_type: str = Field(..., description="Type of job to process")
    payload: Dict[str, Any] = Field(..., description="Job data")
    scheduled_time: int = Field(..., description="When to run the job (unix timestamp)")
    retry_config: Optional[Dict[str, Any]] = Field(
        default=None, description="Configuration for retries"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional metadata for the job"
    )


@router.post("/jobs", status_code=status.HTTP_201_CREATED)
@rbac_register(
    resource="queue", action="create", group="Queue", description="Enqueue a job"
)
async def enqueue_job(
    request: EnqueueJobRequest, queue_service=Depends(get_queue_service)
) -> Dict[str, Any]:
    """
    Add a job to the queue.
    """
    try:
        job = await queue_service.enqueue_job(
            job_type=request.job_type,
            payload=request.payload,
            queue_type=request.queue_type,
            priority=request.priority,
            delay_seconds=request.delay_seconds,
            retry_config=request.retry_config,
            metadata=request.metadata,
        )
        return job
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to enqueue job: {str(e)}",
        )


@router.post("/jobs/schedule", status_code=status.HTTP_201_CREATED)
@rbac_register(
    resource="queue", action="create", group="Queue", description="Schedule a job"
)
async def schedule_job(
    request: ScheduleJobRequest, queue_service=Depends(get_queue_service)
) -> Dict[str, Any]:
    """
    Schedule a job to run at a specific time.
    """
    try:
        job = await queue_service.schedule_job(
            job_type=request.job_type,
            payload=request.payload,
            scheduled_time=request.scheduled_time,
            retry_config=request.retry_config,
            metadata=request.metadata,
        )
        return job
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to schedule job: {str(e)}",
        )


@router.get("/jobs/{job_id}")
@rbac_register(
    resource="queue", action="view", group="Queue", description="Get job details"
)
async def get_job(
    job_id: str, queue_service=Depends(get_queue_service)
) -> Dict[str, Any]:
    """
    Get job details by ID.
    """
    job = await queue_service.get_job(job_id)
    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Job not found"
        )
    return job


@router.post("/jobs/{job_id}/retry")
@rbac_register(
    resource="queue", action="update", group="Queue", description="Retry a failed job"
)
async def retry_job(
    job_id: str, delay_seconds: int = 0, queue_service=Depends(get_queue_service)
) -> Dict[str, str]:
    """
    Retry a failed job.
    """
    success = await queue_service.retry_job(job_id, delay_seconds)
    logger.info(f"Job {job_id} retried with delay {delay_seconds}: {success}")
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to retry job"
        )
    return {"status": "Job scheduled for retry"}


@router.delete("/jobs/{job_id}")
@rbac_register(
    resource="queue", action="delete", group="Queue", description="Cancel a job"
)
async def cancel_job(
    job_id: str, queue_service=Depends(get_queue_service)
) -> Dict[str, str]:
    """
    Cancel a pending or scheduled job.
    """
    success = await queue_service.cancel_job(job_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to cancel job"
        )
    return {"status": "Job cancelled"}


@router.get("/stats")
@rbac_register(
    resource="queue", action="view", group="Queue", description="Get queue statistics"
)
async def get_queue_stats(
    queue_type: Optional[QueueType] = None, queue_service=Depends(get_queue_service)
) -> Dict[str, Any]:
    """
    Get statistics about the queue(s).
    """
    return await queue_service.get_queue_stats(queue_type)


@router.get("/jobs")
@rbac_register(resource="queue", action="view", group="Queue", description="List jobs")
async def list_jobs(
    queue_type: QueueType = QueueType.DEFAULT,
    status: Optional[JobStatus] = None,
    job_type: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    queue_service=Depends(get_queue_service),
) -> List[Dict[str, Any]]:
    """
    List jobs in a queue.
    """
    return await queue_service.list_jobs(
        queue_type=queue_type,
        status=status,
        job_type=job_type,
        limit=limit,
        offset=offset,
    )


# Write a function to process submission
# jobs = await create_job_chain(
#             entity_type="form_submission",
#             entity_id=str(submission.id),
#             job_configs=[
#                 # Submission processing job (new comprehensive workflow)
#                 {
#                     "job_type": "submission_processing",
#                     "queue_job_type": "submission_processing",
#                     "payload": {
#                         "submission_id": str(submission.id),
#                         "form_id": str(public_submission.form_id),
#                         "org_id": str(public_submission.org_id),
#                         "answers": public_submission.answers,
#                         "metadata": {
#                             "submitted_via": "shared_form",
#                             "sharing_token": public_submission.sharing_token,
#                             "public_user_email": public_submission.public_user_email,
#                             "public_submission_id": str(public_submission.id),
#                             "submission_time": int(datetime.now().timestamp()),
#                         },
#                         "created_by": None,  # Public submission, no user context
#                     },
#                     "metadata": {
#                         "submission_time": int(datetime.now().timestamp()),
#                         "submission_source": "public_shared_form",
#                     },
#                 }
#             ],
#         )
@router.post("/jobs/submission_processing")
@rbac_register(
    resource="queue",
    action="create",
    group="Queue",
    description="Create a submission processing job",
)
async def create_submission_processing_job(
    submission_id: str, request: Dict[str, Any]
) -> List[Dict[str, Any]]:
    submission = await Submission.find_one({"_id": PyObjectId(submission_id)})
    if not submission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Submission not found"
        )
    public_submission = await PublicSubmission.find_one({
        "submission_id": PyObjectId(submission.id)
    })
    if not public_submission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Public submission not found"
        )
    if request.get("deal_id"):
        deal_id = request.get("deal_id")
    else:
        deal_id = None
    jobs = await create_job_chain(
        entity_type="form_submission",
        entity_id=str(submission.id),
        job_configs=[
            # Submission processing job (new comprehensive workflow)
            {
                "job_type": "submission_processing",
                "queue_job_type": "submission_processing",
                "payload": {
                    "submission_id": str(submission.id),
                    "form_id": str(public_submission.form_id),
                    "deal_id": deal_id,
                    "org_id": str(public_submission.org_id),
                    "answers": public_submission.answers,
                    "metadata": {
                        "submitted_via": "shared_form",
                        "sharing_token": public_submission.sharing_token,
                        "public_user_email": public_submission.public_user_email,
                        "public_submission_id": str(public_submission.id),
                        "submission_time": int(datetime.now().timestamp()),
                    },
                    "created_by": None,  # Public submission, no user context
                },
                "metadata": {
                    "submission_time": int(datetime.now().timestamp()),
                    "submission_source": "public_shared_form",
                },
            }
        ],
    )
    logger.info(
        f"Created submission processing job chain for public submission {submission.id}: {jobs}"
    )
    return [job.model_dump() for job in jobs]
