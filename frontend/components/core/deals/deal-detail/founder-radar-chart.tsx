"use client"

import { useMemo } from "react"
import { ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, Tooltip as RechartsTooltip } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { HelpCircle } from "lucide-react"
import clsx from "clsx"
import { motion } from "framer-motion"

interface SkillProfile {
  tech: number
  product: number
  business: number
  operations: number
  fundraising: number
  details?: {
    [domain: string]: {
      topSkills?: string[]
      interpretation?: string
    }
  }
}

interface FounderRadarChartProps {
  skillProfile: SkillProfile
  founderName: string
  className?: string
}

const skillDomains = [
  { key: 'tech', label: 'Tech' },
  { key: 'product', label: 'Product' },
  { key: 'business', label: 'Business' },
  { key: 'operations', label: 'Operations' },
  { key: 'fundraising', label: 'Fundraising' },
]

const getStrengthComment = (score: number) => {
  if (score >= 8) return "Based on 5+ experiences"
  if (score >= 6) return "Based on 3+ experiences"
  if (score >= 4) return "Based on 2+ experiences"
  return "Limited exposure"
}

export function FounderRadarChart({ skillProfile, founderName, className }: FounderRadarChartProps) {
  // Prepare chart data
  const chartData = useMemo(() => skillDomains.map(({ key, label }) => ({
    skill: label,
    value: (skillProfile[key as keyof SkillProfile] as number) ?? 0,
    fullMark: 10,
    interpretation: skillProfile.details?.[key]?.interpretation || undefined
  })), [skillProfile])

  // Calculate overall score
  const overallScore = useMemo(() => {
    const scores = skillDomains.map(({ key }) => (skillProfile[key as keyof SkillProfile] as number) ?? 0)
    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length)
  }, [skillProfile])

  const getConfidenceLevel = (score: number) => {
    if (score >= 8) return "High"
    if (score >= 6) return "Medium"
    if (score >= 4) return "Low"
    return "Limited"
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <Card className={clsx(
        "border border-slate-200 bg-white/90 backdrop-blur-sm shadow-none hover:shadow-md transition-shadow duration-200",
        className
      )}>
        <CardHeader className="border-b border-slate-100 pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="rounded-2xl border border-slate-200 bg-slate-50 p-2">
                <HelpCircle className="size-4 text-slate-600" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-slate-900">
                  Skill Profile
                </CardTitle>
                <p className="mt-0.5 text-xs text-slate-500">
                  AI-analyzed competency assessment
                </p>
              </div>
            </div>
            
            {/* Skill Confidence Badge */}
            <div className="text-right">
              <div className="text-sm font-medium text-slate-900">
                Skill Confidence: {getConfidenceLevel(overallScore)}
              </div>
              <div className="text-xs text-slate-500">
                {overallScore}/10 average
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          <div className="flex flex-col items-center gap-6 lg:flex-row lg:items-start">
            {/* Clean Spider Chart */}
            <div className="flex w-full flex-col items-center justify-center lg:w-1/2">
              <div className="relative h-80 w-full rounded-2xl bg-slate-50/50 p-4 lg:h-96">
                <ResponsiveContainer width="100%" height="100%">
                  <RadarChart 
                    data={chartData} 
                    cx="50%" 
                    cy="50%" 
                    outerRadius="80%" 
                    startAngle={120} 
                    endAngle={480}
                  >
                    <PolarGrid 
                      stroke="#e2e8f0" 
                      strokeWidth={1} 
                      radialLines={true}
                      strokeDasharray="2 2"
                    />
                    <PolarAngleAxis 
                      dataKey="skill" 
                      tick={{ 
                        fontSize: 12, 
                        fill: '#475569', 
                        fontWeight: 500,
                        textAnchor: 'middle'
                      }} 
                    />
                    <PolarRadiusAxis 
                      angle={90} 
                      domain={[0, 10]} 
                      tick={false} 
                      axisLine={false} 
                    />
                    <Radar
                      name={founderName}
                      dataKey="value"
                      stroke="#1e293b"
                      fill="#1e293b"
                      fillOpacity={0.1}
                      strokeWidth={2}
                      isAnimationActive={true}
                      animationBegin={0}
                      animationDuration={1200}
                      animationEasing="ease-out"
                    />
                    
                    <RechartsTooltip
                      content={({ active, payload }) => {
                        if (!active || !payload || !payload[0]) return null
                        const { skill, value, interpretation } = payload[0].payload
                        
                        return (
                          <div className="rounded-2xl border border-slate-200 bg-white/95 p-4 text-sm shadow-lg">
                            <div className="mb-1 font-semibold text-slate-900">
                              {skill}: {value}/10
                            </div>
                            <div className="text-xs leading-relaxed text-slate-600">
                              {interpretation || 'No detailed interpretation available.'}
                            </div>
                          </div>
                        )
                      }}
                    />
                  </RadarChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Clean Skill Bars */}
            <div className="flex w-full flex-col gap-4 lg:w-1/2">
              {skillDomains.map(({ key, label }, index) => {
                const score = (skillProfile[key as keyof SkillProfile] as number) ?? 0
                const topSkills = skillProfile.details?.[key]?.topSkills || []
                
                return (
                  <motion.div
                    key={key}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.5 }}
                    className="group"
                  >
                    <div className="mb-2 flex items-center justify-between">
                      <span className="text-sm font-medium text-slate-900">{label}</span>
                      <div className="flex items-center gap-2">
                        <span className="font-mono text-sm text-slate-600">{score}/10</span>
                        <span className="text-xs text-slate-500">{getStrengthComment(score)}</span>
                      </div>
                    </div>
                    
                    <div className="relative">
                      {/* Background bar */}
                      <div className="h-2 w-full overflow-hidden rounded-full bg-slate-100">
                        {/* Clean fill */}
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{ width: `${(score / 10) * 100}%` }}
                          transition={{ delay: index * 0.1 + 0.5, duration: 1, ease: "easeOut" }}
                          className="h-full rounded-full bg-slate-900 transition-all duration-300"
                        />
                      </div>
                      
                      {/* Skills micro-text */}
                      <div className="mt-2 text-xs text-slate-500">
                        {topSkills.length > 0 ? (
                          <div className="flex flex-wrap gap-1">
                            {topSkills.slice(0, 2).map((skill, i) => (
                              <span key={i} className="rounded-full border border-slate-200 bg-white px-2 py-1 text-slate-600">
                                {skill}
                              </span>
                            ))}
                            {topSkills.length > 2 && (
                              <span className="rounded-full border border-slate-200 bg-white px-2 py-1 text-slate-500">
                                +{topSkills.length - 2} more
                              </span>
                            )}
                          </div>
                        ) : (
                          <span className="italic text-slate-400">No specific skills identified</span>
                        )}
                      </div>
                    </div>
                  </motion.div>
                )
              })}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
