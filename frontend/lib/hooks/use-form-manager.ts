"use client"

import { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from '@/components/ui/use-toast';

import {
  Form,
  FormState,
  Section,
  Question,
  FormCreateRequest,
  SectionCreateRequest,
  QuestionCreateRequest
} from '@/lib/types/form';
import FormAPI from '@/lib/api/form-api';

interface UseFormManagerProps {
  initialForm?: Form;
  onSaveSuccess?: (form: Form) => void;
}

/**
 * Custom hook for managing form state and API operations
 *
 * This hook handles the form state and API operations for form creation and editing.
 * It maintains the parent-child relationships between form, sections, and questions.
 */
export function useFormManager({ initialForm, onSaveSuccess }: UseFormManagerProps = {}) {
  const router = useRouter();
  const [formState, setFormState] = useState<FormState>(() => {
    const defaultForm: FormState = {
      name: '',
      description: '',
      status: 'draft',
      sections: [],
    };

    // If initialForm is provided, convert it to FormState
    if (initialForm) {
      const convertedForm: FormState = {
        _id: initialForm._id || initialForm.id,
        id: initialForm.id || initialForm._id,
        name: initialForm.name,
        description: initialForm.description,
        status: initialForm.status,
        sections: initialForm.sections.map(section => ({
          _id: section._id || section.id,
          id: section.id || section._id,
          form_id: section.form_id || initialForm._id || initialForm.id,
          title: section.title,
          description: section.description,
          order: section.order,
          repeatable: section.repeatable,
          questions: section.questions.map(question => ({
            _id: question._id || question.id,
            id: question.id || question._id,
            section_id: question.section_id || section._id || section.id,
            label: question.label,
            type: question.type,
            help_text: question.help_text,
            required: question.required,
            options: question.options,
            validation: question.validation,
            visibility_condition: question.visibility_condition,
            repeat_section_id: question.repeat_section_id,
            max_repeats: question.max_repeats,
            order: question.order,
          })),
        })),
        default_section_ids: initialForm.default_section_ids,
        is_active: initialForm.is_active,
        version: initialForm.version,
        created_at: initialForm.created_at,
        updated_at: initialForm.updated_at,
      };

      console.log('Initialized form state from initialForm:', convertedForm);
      return convertedForm;
    }

    console.log('Initialized default form state');
    return defaultForm;
  });

  const [isSaving, setIsSaving] = useState(false);
  const [activeSection, setActiveSection] = useState<string | null>(null);

  // Set the first section as active when form loads or sections change
  useEffect(() => {
    if (formState.sections.length > 0 && !activeSection) {
      setActiveSection(`section-${0}`);
    }
  }, [formState.sections.length, activeSection]);

  // Update form metadata
  const updateFormMeta = useCallback((values: { name: string; description: string; status: 'draft' | 'active' }) => {
    console.log('Updating form metadata:', values);

    setFormState(prev => ({
      ...prev,
      name: values.name.trim(),
      description: values.description.trim(),
      status: values.status,
    }));
  }, []);

  // Add a new section
  const addSection = useCallback(async () => {
    console.log('Adding new section');
    const formId = formState._id || formState.id;
    if (!formId) {
      throw new Error('Form ID is required to create a section');
    }

    const newOrder = formState.sections.length;
    const sectionRequest: SectionCreateRequest = {
      title: `Section ${newOrder + 1}`,
      description: '',
      order: newOrder,
      repeatable: false as const,
    };

    try {
      const createdSection = await FormAPI.createSection(formId, sectionRequest);
      const sectionId = createdSection._id || createdSection.id;
      if (!sectionId) throw new Error('Failed to get section ID after creation');

      const newSection: Required<Pick<Section, '_id' | 'id' | 'form_id' | 'title' | 'description' | 'order' | 'repeatable' | 'questions'>> = {
        _id: sectionId,
        id: sectionId,
        form_id: formId,
        title: sectionRequest.title,
        description: sectionRequest.description,
        order: sectionRequest.order,
        repeatable: sectionRequest.repeatable,
        questions: [],
      };

      setFormState(prev => ({ ...prev, sections: [...prev.sections, newSection] }));
      setActiveSection(`section-${newOrder}`);
      toast({ title: "Section added", description: "The new section has been added successfully." });
    } catch (error) {
      console.error('Error adding section:', error);
      toast({ title: "Error adding section", description: "There was an error adding the section. Please try again.", variant: "destructive" });
      throw error;
    }
  }, [formState._id, formState.id, formState.sections.length]);

  // Update a section
  const updateSection = useCallback(async (index: number, updatedSection: Partial<Section>) => {
    console.log('Updating section', index, 'with', updatedSection);

    try {
      // Get the current section from formState
      const currentSection = formState.sections[index];

      if (!currentSection) {
        console.error(`No section found at index ${index}`);
        throw new Error(`No section found at index ${index}`);
      }

      // Get the section ID if it exists
      const sectionId = currentSection._id || currentSection.id;

      // Create the updated section object
      const sectionToUpdate = {
        ...currentSection,
        ...updatedSection,
        // Ensure these fields are preserved
        order: currentSection.order ?? index,
        form_id: currentSection.form_id,
        _id: currentSection._id,
        id: currentSection.id,
      };

      console.log('Section to update:', sectionToUpdate);

      // If we have a section ID, update it via API first
      if (sectionId) {
        console.log(`Calling API to update section ${sectionId}`);

        // Prepare the section data for the API
        const sectionRequest: SectionCreateRequest = {
          title: sectionToUpdate.title,
          description: sectionToUpdate.description,
          order: sectionToUpdate.order,
          repeatable: sectionToUpdate.repeatable,
        };

        console.log('Section update request:', sectionRequest);

        // Call the API to update the section
        try {
          const updatedSectionFromApi = await FormAPI.updateSection(sectionId, sectionRequest);
          console.log('Section updated successfully via API:', updatedSectionFromApi);
        } catch (error) {
          console.error('Error updating section via API:', error);
          throw error;
        }
      } else {
        console.log('Section has no ID, skipping API update');
      }

      // Now update the local state
      setFormState(prev => {
        // Make a deep copy of the sections array
        const updatedSections = [...prev.sections];

        // Make sure we have a valid section at this index
        if (!updatedSections[index]) {
          console.error(`No section found at index ${index} when updating state`);
          return prev;
        }

        // Update the section with the new values
        updatedSections[index] = sectionToUpdate;

        console.log('Updated section in local state:', updatedSections[index]);

        return {
          ...prev,
          sections: updatedSections,
        };
      });
    } catch (error) {
      console.error('Error in updateSection:', error);
      throw error;
    }
  }, [formState.sections]);

  // Delete a section
  const deleteSection = useCallback(async (index: number) => {
    console.log('Deleting section', index);

    try {
      // Get the section to delete
      const sectionToDelete = formState.sections[index];
      if (!sectionToDelete) {
        console.error(`No section found at index ${index}`);
        throw new Error(`No section found at index ${index}`);
      }

      // Get the section ID if it exists
      const sectionId = sectionToDelete._id || sectionToDelete.id;

      // If the section has an ID, delete it via API first
      if (sectionId) {
        console.log(`Calling API to delete section ${sectionId}`);
        try {
          await FormAPI.deleteSection(sectionId);
          console.log('Section deleted successfully via API');
        } catch (error) {
          console.error('Error deleting section via API:', error);
          throw error;
        }
      } else {
        console.log('Section has no ID, skipping API delete');
      }

      // Now update the local state
      setFormState(prev => {
        const updatedSections = prev.sections.filter((_, i) => i !== index);

        // Update order for remaining sections
        const reorderedSections = updatedSections.map((section, i) => ({
          ...section,
          order: i,
        }));

        return {
          ...prev,
          sections: reorderedSections,
        };
      });

      // If the active section was deleted, set the first section as active
      setFormState(prev => {
        if (activeSection === `section-${index}` && prev.sections.length > 0) {
          setActiveSection(`section-${0}`);
        } else if (prev.sections.length === 0) {
          setActiveSection(null);
        }
        return prev;
      });

      // Show success toast
      toast({
        title: "Section deleted",
        description: "The section has been deleted successfully.",
      });
    } catch (error) {
      console.error('Error in deleteSection:', error);
      toast({
        title: "Error deleting section",
        description: "There was an error deleting the section. Please try again.",
        variant: "destructive",
      });
      throw error;
    }
  }, [activeSection, formState.sections]);

  // Update questions for a section
  const updateQuestions = useCallback(async (sectionIndex: number, questions: Question[]) => {
    console.log('Updating questions for section', sectionIndex, 'with', questions);

    try {
      const section = formState.sections[sectionIndex];
      if (!section) {
        console.error(`No section found at index ${sectionIndex}`);
        throw new Error(`No section found at index ${sectionIndex}`);
      }

      const sectionId = section._id || section.id;
      if (!sectionId) {
        console.error('Section ID is required to update questions');
        throw new Error('Section ID is required to update questions');
      }

      // First update local state to ensure UI is responsive
      setFormState(prev => {
        const updatedSections = [...prev.sections];
        updatedSections[sectionIndex] = {
          ...updatedSections[sectionIndex],
          questions: questions.map(q => ({
            ...q,
            section_id: sectionId, // Ensure section_id is set
          })),
        };
        return {
          ...prev,
          sections: updatedSections,
        };
      });

      // Then process each question - create new ones or update existing ones
      const updatedQuestions: Question[] = [];
      for (const question of questions) {
        const questionId = question._id || question.id;

        // Create a base question object with required fields
        const baseQuestion = {
          section_id: sectionId,
          order: question.order ?? questions.indexOf(question),
          type: question.type || 'short_text',
          label: question.label || 'New Question',
          required: question.required ?? false,
          help_text: question.help_text || '',
          options: question.options || [],
          validation: question.validation || {},
        };

        // Add optional fields only if they exist in the original question
        const questionWithSectionId: Question = {
          ...baseQuestion,
          ...(question.visibility_condition && { visibility_condition: question.visibility_condition }),
          ...(question.repeat_section_id && { repeat_section_id: question.repeat_section_id }),
          ...(question.max_repeats !== undefined && { max_repeats: question.max_repeats }),
          ...(question._id && { _id: question._id }),
          ...(question.id && { id: question.id }),
        };

        try {
          if (!questionId) {
            // Create new question
            console.log('Creating new question:', questionWithSectionId);
            const questionRequest: QuestionCreateRequest = {
              ...baseQuestion,
              // Only include optional fields if they exist
              ...(question.visibility_condition && { visibility_condition: question.visibility_condition }),
              ...(question.repeat_section_id && { repeat_section_id: question.repeat_section_id }),
              ...(question.max_repeats !== undefined && { max_repeats: question.max_repeats }),
            };

            const updatedForm = await FormAPI.createQuestion(sectionId, questionRequest);
            console.log('Question created successfully, received full form:', updatedForm);

            // Since we now get the full form back, we should update our entire form state
            // But for now, let's extract the created question from the response
            const createdQuestion = updatedForm.sections
              .find(s => (s._id || s.id) === sectionId)?.questions
              .find(q => q.label === questionRequest.label);

            if (createdQuestion) {
            updatedQuestions.push({
              ...questionWithSectionId,
              _id: createdQuestion._id || createdQuestion.id,
              id: createdQuestion.id || createdQuestion._id,
            });
            } else {
              // Fallback: add the question with a temporary ID
              updatedQuestions.push(questionWithSectionId);
            }
          } else {
            // Update existing question
            console.log('Updating existing question:', questionId, questionWithSectionId);
            const questionRequest: QuestionCreateRequest = {
              ...baseQuestion,
              // Only include optional fields if they exist
              ...(question.visibility_condition && { visibility_condition: question.visibility_condition }),
              ...(question.repeat_section_id && { repeat_section_id: question.repeat_section_id }),
              ...(question.max_repeats !== undefined && { max_repeats: question.max_repeats }),
            };

            await FormAPI.updateQuestion(questionId, questionRequest);
            console.log('Question updated successfully');

            // Add the updated question to our array
            updatedQuestions.push(questionWithSectionId);
          }
        } catch (error) {
          console.error('Error processing question:', error);
          // If there's an error, keep the original question in the array
          updatedQuestions.push(questionWithSectionId);
          // Show error toast but continue processing other questions
          toast({
            title: "Error processing question",
            description: "There was an error processing one of the questions. Some changes may not have been saved.",
            variant: "destructive",
          });
        }
      }

      // Update local state with the processed questions
      setFormState(prev => {
        const updatedSections = [...prev.sections];
        updatedSections[sectionIndex] = {
          ...updatedSections[sectionIndex],
          questions: updatedQuestions,
        };
        return {
          ...prev,
          sections: updatedSections,
        };
      });

      // Show success toast
      toast({
        title: "Questions updated",
        description: "The questions have been updated successfully.",
      });
    } catch (error) {
      console.error('Error in updateQuestions:', error);
      toast({
        title: "Error updating questions",
        description: "There was an error updating the questions. Please try again.",
        variant: "destructive",
      });
      throw error;
    }
  }, [formState.sections]);

  // Save the form
  const saveForm = useCallback(async () => {
    try {
      // Validate form data
      if (!formState.name || !formState.description) {
        toast({
          title: "Missing information",
          description: "Please provide a name and description for your form.",
          variant: "destructive",
        });
        return;
      }

      console.log('Saving form:', formState);
      setIsSaving(true);

      // Check if form has an ID
      const formId = formState._id || formState.id;

      if (!formId) {
        // Create new form
        console.log('Creating new form');
        await createNewForm();
      } else {
        // Update existing form
        console.log('Updating existing form');
        await updateExistingForm(formId);
      }

      // Call onSaveSuccess callback if provided
      if (onSaveSuccess) {
        onSaveSuccess(formState as Form);
      }
    } catch (error) {
      console.error('Error saving form:', error);
      toast({
        title: "Error",
        description: "There was an error saving your form. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  }, [formState, onSaveSuccess]);

  // Create a new form
  const createNewForm = async () => {
    try {
      // Validate required fields
      if (!formState.name || !formState.description) {
        throw new Error('Form name and description are required');
      }

      // Step 1: Create the form with validated data
      const formRequest: FormCreateRequest = {
        name: formState.name,
        description: formState.description,
        is_active: formState.is_active ?? false,
      };

      console.log('Creating form with:', formRequest);
      const createdForm = await FormAPI.createForm(formRequest);

      // Validate the response
      const formId = createdForm._id || createdForm.id;
      if (!formId) {
        throw new Error('Form creation failed: No ID returned');
      }

      console.log('Form created with ID:', formId);

      // Update form state with the created form ID
      setFormState(prev => ({
        ...prev,
        _id: formId,
        id: formId,
      }));

      // Step 2: Create sections with proper error handling
      const createdSections = await Promise.all(
        formState.sections.map(async (section, index) => {
          const sectionRequest: SectionCreateRequest = {
            title: section.title || `Section ${index + 1}`,
            description: section.description || '',
            order: section.order ?? index,
            repeatable: section.repeatable ?? false,
          };

          console.log('Creating section:', sectionRequest);
          const createdSection = await FormAPI.createSection(formId, sectionRequest);

          // Validate section ID
          const sectionId = createdSection._id || createdSection.id;
          if (!sectionId) {
            throw new Error(`Section creation failed: No ID returned for section ${index}`);
          }

          console.log('Section created with ID:', sectionId);

          // Step 3: Create questions for this section
          const createdQuestions = await Promise.all(
            section.questions.map(async (question, qIndex) => {
              // Ensure we have a valid section ID
              if (!sectionId) {
                throw new Error(`Invalid section ID for question ${qIndex} in section ${index}`);
              }

              const questionRequest: QuestionCreateRequest = {
                section_id: sectionId, // Now we know this is a string
                type: question.type || 'short_text',
                label: question.label || `Question ${qIndex + 1}`,
                help_text: question.help_text || '',
                required: question.required ?? false,
                options: question.options || [],
                validation: question.validation || {},
                visibility_condition: question.visibility_condition,
                repeat_section_id: question.repeat_section_id,
                max_repeats: question.max_repeats,
                order: question.order ?? qIndex,
              };

              console.log('Creating question:', questionRequest);
              const updatedForm = await FormAPI.createQuestion(sectionId, questionRequest);

              // Extract the created question from the full form response
              const createdQuestion = updatedForm.sections
                .find(s => (s._id || s.id) === sectionId)?.questions
                .find(q => q.label === questionRequest.label);

              if (!createdQuestion) {
                throw new Error(`Question creation failed: Could not find created question ${qIndex} in section ${index}`);
              }

              // Validate question ID
              const questionId = createdQuestion._id || createdQuestion.id;
              if (!questionId) {
                throw new Error(`Question creation failed: No ID returned for question ${qIndex} in section ${index}`);
              }

              return {
                ...question,
                _id: questionId,
                id: questionId,
                section_id: sectionId,
              };
            })
          );

          return {
            ...section,
            _id: sectionId,
            id: sectionId,
            form_id: formId,
            questions: createdQuestions,
          };
        })
      );

      // Update the form state with all created entities
      setFormState(prev => ({
        ...prev,
        sections: createdSections,
      }));

      toast({
        title: "Form created",
        description: "Your form has been created successfully.",
      });

      // Redirect to the forms list page
      router.push('/forms');
    } catch (error) {
      console.error('Error creating form:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create form. Please try again.",
        variant: "destructive",
      });
      throw error;
    }
  };

  // Update an existing form
  const updateExistingForm = async (formId: string) => {
    try {
      console.log('Updating form using atomic APIs');

      // Step 1: Update form metadata
      const formRequest: FormCreateRequest = {
        name: formState.name,
        description: formState.description,
        is_active: formState.status === 'active',  // Convert status to boolean
      };

      console.log('Updating form metadata:', formRequest);
      await FormAPI.updateForm(formId, formRequest);

      // Step 2: Process sections
      for (let index = 0; index < formState.sections.length; index++) {
        const section = formState.sections[index];
        const sectionId = section._id || section.id;

        if (!sectionId) {
          // Create new section
          const sectionRequest: SectionCreateRequest = {
            title: section.title,
            description: section.description,
            order: section.order || index, // Ensure order is set correctly
            repeatable: section.repeatable,
          };

          console.log('Creating new section:', sectionRequest);
          const createdSection = await FormAPI.createSection(formId, sectionRequest);
          const newSectionId = createdSection._id || createdSection.id;

          if (!newSectionId) {
            console.error('Failed to get section ID after creation');
            throw new Error('Failed to create section: No section ID returned');
          }

          // Create questions for this new section
          for (let index = 0; index < section.questions.length; index++) {
            const question = section.questions[index];
            const questionRequest: QuestionCreateRequest = {
              section_id: newSectionId,
              type: question.type,
              label: question.label,
              help_text: question.help_text,
              required: question.required,
              options: question.options,
              validation: question.validation,
              visibility_condition: question.visibility_condition,
              repeat_section_id: question.repeat_section_id,
              max_repeats: question.max_repeats,
              order: question.order || index, // Ensure order is set correctly
            };

            console.log('Creating question for new section:', questionRequest);
            await FormAPI.createQuestion(newSectionId, questionRequest);
            // Note: We get the full form back but don't need to process it here since we're in a loop
          }
        } else {
          // Update existing section
          const sectionRequest: SectionCreateRequest = {
            title: section.title,
            description: section.description,
            order: section.order || index, // Ensure order is set correctly
            repeatable: section.repeatable,
          };

          console.log('Updating section in updateExistingForm:', sectionId, sectionRequest);
          try {
            const updatedSection = await FormAPI.updateSection(sectionId, sectionRequest);
            console.log('Section updated successfully in updateExistingForm:', updatedSection);

            // Process questions for this section
            for (let index = 0; index < section.questions.length; index++) {
              const question = section.questions[index];
              const questionId = question._id || question.id;

              if (!questionId) {
                // Create new question
                const questionRequest: QuestionCreateRequest = {
                  section_id: sectionId,
                  type: question.type,
                  label: question.label,
                  help_text: question.help_text,
                  required: question.required,
                  options: question.options,
                  validation: question.validation,
                  visibility_condition: question.visibility_condition,
                  repeat_section_id: question.repeat_section_id,
                  max_repeats: question.max_repeats,
                  order: question.order || index, // Ensure order is set correctly
                };

                console.log('Creating new question:', questionRequest);
                await FormAPI.createQuestion(sectionId, questionRequest);
                // Note: We get the full form back but don't need to process it here since we're in a loop
              } else {
                // Update existing question
                const questionRequest: QuestionCreateRequest = {
                  section_id: sectionId,
                  type: question.type,
                  label: question.label,
                  help_text: question.help_text,
                  required: question.required,
                  options: question.options,
                  validation: question.validation,
                  visibility_condition: question.visibility_condition,
                  repeat_section_id: question.repeat_section_id,
                  max_repeats: question.max_repeats,
                  order: question.order || index, // Ensure order is set correctly
                };

                console.log('Updating question:', questionId, questionRequest);
                try {
                  await FormAPI.updateQuestion(questionId, questionRequest);
                  console.log('Question updated successfully');
                } catch (error) {
                  console.error('Error updating question:', error);
                  throw error;
                }
              }
            }
          } catch (error) {
            console.error('Error updating section in updateExistingForm:', error);
            throw error;
          }
        }
      }

      toast({
        title: "Form updated",
        description: "Your form has been updated successfully.",
      });
    } catch (error) {
      console.error('Error updating form:', error);
      toast({
        title: "Error",
        description: "Failed to update form. Please try again.",
        variant: "destructive",
      });
      throw error; // Re-throw to be caught by the saveForm function
    }
  };

  // Update the form state directly
  const updateFormState = useCallback((newFormState: FormState) => {
    console.log('Setting form state directly:', newFormState);
    setFormState(newFormState);
  }, []);

  return {
    formState,
    isSaving,
    activeSection,
    setActiveSection,
    updateFormMeta,
    addSection,
    updateSection,
    deleteSection,
    updateQuestions,
    saveForm,
    setFormState: updateFormState,
  };
}

export default useFormManager;
