"""
Queue worker interfaces.

This module defines the interfaces for queue workers, providing
a clear contract for different worker implementations.
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Callable

from app.services.queue.interfaces import QueueServiceInterface, AsyncJobHandler
from app.models.queue import Job, QueueType


class JobHandlerInterface(ABC):
    """Interface for job handlers."""
    
    @abstractmethod
    async def handle(self, job: Job) -> Dict[str, Any]:
        """
        Handle a job.
        
        Args:
            job: The job to handle
            
        Returns:
            Result of job processing
        """
        pass


class WorkerInterface(ABC):
    """Interface for queue workers."""
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the worker."""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up resources used by the worker."""
        pass
    
    @abstractmethod
    def register_handler(
        self,
        job_type: str,
        handler: Union[JobHandlerInterface, Callable, AsyncJobHandler]
    ) -> None:
        """
        Register a handler for a job type.
        
        Args:
            job_type: Type of job to handle
            handler: Handler function or class
        """
        pass
    
    @abstractmethod
    def unregister_handler(self, job_type: str) -> bool:
        """
        Unregister a handler for a job type.
        
        Args:
            job_type: Type of job to unregister handler for
            
        Returns:
            True if successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def start(self) -> None:
        """Start the worker."""
        pass
    
    @abstractmethod
    async def stop(self) -> None:
        """Stop the worker."""
        pass
    
    @abstractmethod
    async def process_one(
        self,
        queue_type: QueueType = QueueType.DEFAULT,
        job_types: Optional[List[str]] = None,
        wait_timeout: int = 0
    ) -> Optional[Dict[str, Any]]:
        """
        Process a single job.
        
        Args:
            queue_type: Which queue to get jobs from
            job_types: Optional list of job types to filter by
            wait_timeout: How long to wait for a job (0 = no wait)
            
        Returns:
            Result of job processing or None if no jobs available
        """
        pass
    
    @abstractmethod
    def get_registered_handlers(self) -> Dict[str, Union[JobHandlerInterface, Callable, AsyncJobHandler]]:
        """
        Get all registered handlers.
        
        Returns:
            Dictionary of job types to handlers
        """
        pass
    
    @abstractmethod
    def get_queue_service(self) -> QueueServiceInterface:
        """
        Get the queue service.
        
        Returns:
            The queue service
        """
        pass


class WorkerPoolInterface(ABC):
    """Interface for worker pools."""
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the worker pool."""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up resources used by the worker pool."""
        pass
    
    @abstractmethod
    async def start(self, num_workers: int = 1) -> None:
        """
        Start the worker pool.
        
        Args:
            num_workers: Number of workers to start
        """
        pass
    
    @abstractmethod
    async def stop(self) -> None:
        """Stop the worker pool."""
        pass
    
    @abstractmethod
    def register_handler(
        self,
        job_type: str,
        handler: Union[JobHandlerInterface, Callable, AsyncJobHandler]
    ) -> None:
        """
        Register a handler for a job type.
        
        Args:
            job_type: Type of job to handle
            handler: Handler function or class
        """
        pass
