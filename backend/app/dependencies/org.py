from typing import <PERSON><PERSON>
from fastapi import Depends, Request
from app.models.user import User
from app.dependencies.auth import get_current_user
from app.core.org_context import OrgContext


async def get_org_context(
    request: Request,
    current_user: User = Depends(get_current_user)
) -> Tuple[str, bool]:
    """
    Get organization context for the request from the X-ORG-ID header.

    Returns:
        tuple[str, bool]: (organization_id, is_cross_org)
            - organization_id: The organization ID to use
            - is_cross_org: Whether this is a cross-organization request
    """
    return OrgContext.get_org_id(request, current_user)


