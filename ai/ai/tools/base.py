"""
Base tool classes for TractionX AI Agent system.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Type, Set
import logging
import inspect
from functools import wraps
from pydantic import BaseModel, Field
from ai.models import AgentContext

logger = logging.getLogger(__name__)

# Global tool registry for automatic discovery
_TOOL_REGISTRY: Dict[str, Type['BaseTool']] = {}
_TOOL_METADATA: Dict[str, Dict[str, Any]] = {}


def tool(
    name: Optional[str] = None,
    description: Optional[str] = None,
    category: Optional[str] = None,
    tags: Optional[List[str]] = None,
    enabled: bool = True,
    **metadata
):
    """
    Decorator for automatic tool registration.

    Usage:
        @tool(name="my_tool", description="My tool description", category="analysis")
        class MyTool(BaseTool):
            pass

    Args:
        name: Tool name (defaults to class name in snake_case)
        description: Tool description (defaults to class docstring)
        category: Tool category for grouping
        tags: List of tags for tool discovery
        enabled: Whether the tool is enabled
        **metadata: Additional metadata
    """
    def decorator(cls: Type['BaseTool']) -> Type['BaseTool']:
        # Generate name from class name if not provided
        tool_name = name or _class_name_to_snake_case(cls.__name__)

        # Use class docstring as description if not provided
        tool_description = description or (cls.__doc__ or "").strip()

        # Store tool metadata
        tool_metadata = {
            "name": tool_name,
            "description": tool_description,
            "category": category,
            "tags": tags or [],
            "enabled": enabled,
            "class": cls,
            **metadata
        }

        # Register the tool
        _TOOL_REGISTRY[tool_name] = cls
        _TOOL_METADATA[tool_name] = tool_metadata

        # Add metadata as class attributes
        cls._tool_name = tool_name
        cls._tool_description = tool_description
        cls._tool_category = category
        cls._tool_tags = tags or []
        cls._tool_enabled = enabled
        cls._tool_metadata = tool_metadata

        logger.debug(f"Registered tool: {tool_name} ({cls.__name__})")

        return cls

    return decorator


def _class_name_to_snake_case(name: str) -> str:
    """Convert CamelCase class name to snake_case."""
    import re
    # Remove 'Tool' suffix if present
    if name.endswith('Tool'):
        name = name[:-4]

    # Convert to snake_case
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()


class ToolRegistry:
    """Centralized tool registry with discovery and management capabilities."""

    def __init__(self):
        self._instances: Dict[str, 'BaseTool'] = {}
        self._initialized = False

    def discover_tools(self) -> None:
        """Discover and import all tools from the tools package."""
        if self._initialized:
            return

        import importlib
        import pkgutil
        from ai import tools

        logger.debug("Starting tool discovery...")

        # Import all modules in the tools package
        for importer, modname, ispkg in pkgutil.iter_modules(tools.__path__, tools.__name__ + "."):
            try:
                importlib.import_module(modname)
                logger.debug(f"Imported tool module: {modname}")
            except Exception as e:
                logger.warning(f"Failed to import tool module {modname}: {e}")

        self._initialized = True
        logger.info(f"Tool discovery complete. Found {len(_TOOL_REGISTRY)} tools.")

    def get_tool_instance(self, tool_name: str) -> Optional['BaseTool']:
        """Get or create a tool instance."""
        if tool_name not in self._instances:
            if tool_name not in _TOOL_REGISTRY:
                return None

            tool_class = _TOOL_REGISTRY[tool_name]
            metadata = _TOOL_METADATA[tool_name]

            if not metadata.get("enabled", True):
                logger.warning(f"Tool {tool_name} is disabled")
                return None

            try:
                # Create instance with metadata
                instance = tool_class()
                # Override name and description from metadata
                instance.name = metadata["name"]
                instance.description = metadata["description"]

                self._instances[tool_name] = instance
                logger.debug(f"Created tool instance: {tool_name}")
            except Exception as e:
                logger.error(f"Failed to create tool instance {tool_name}: {e}")
                return None

        return self._instances[tool_name]

    def get_all_tools(self) -> Dict[str, 'BaseTool']:
        """Get all available tool instances."""
        self.discover_tools()

        tools = {}
        for tool_name in _TOOL_REGISTRY:
            tool = self.get_tool_instance(tool_name)
            if tool:
                tools[tool_name] = tool

        return tools

    def get_tools_by_category(self, category: str) -> Dict[str, 'BaseTool']:
        """Get tools by category."""
        tools = {}
        for tool_name, metadata in _TOOL_METADATA.items():
            if metadata.get("category") == category:
                tool = self.get_tool_instance(tool_name)
                if tool:
                    tools[tool_name] = tool

        return tools

    def get_tools_by_tags(self, tags: List[str]) -> Dict[str, 'BaseTool']:
        """Get tools that have any of the specified tags."""
        tools = {}
        for tool_name, metadata in _TOOL_METADATA.items():
            tool_tags = metadata.get("tags", [])
            if any(tag in tool_tags for tag in tags):
                tool = self.get_tool_instance(tool_name)
                if tool:
                    tools[tool_name] = tool

        return tools

    def list_available_tools(self) -> List[Dict[str, Any]]:
        """List all available tools with their metadata."""
        self.discover_tools()

        tools = []
        for tool_name, metadata in _TOOL_METADATA.items():
            if metadata.get("enabled", True):
                tools.append({
                    "name": tool_name,
                    "description": metadata["description"],
                    "category": metadata.get("category"),
                    "tags": metadata.get("tags", []),
                    "class_name": metadata["class"].__name__
                })

        return tools


# Global tool registry instance
tool_registry = ToolRegistry()


class ToolInput(BaseModel):
    """Base tool input model."""
    pass


class ToolOutput(BaseModel):
    """Base tool output model."""
    success: bool = Field(default=True)
    data: Optional[Dict[str, Any]] = Field(default=None)
    error: Optional[str] = Field(default=None)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class BaseTool(ABC):
    """Base class for all AI agent tools."""

    def __init__(self, name: Optional[str] = None, description: Optional[str] = None):
        # Use metadata from decorator if available, otherwise use provided values
        self.name = name or getattr(self, '_tool_name', self.__class__.__name__.lower())
        self.description = description or getattr(self, '_tool_description', self.__doc__ or "").strip()
        self.category = getattr(self, '_tool_category', None)
        self.tags = getattr(self, '_tool_tags', [])
        self.enabled = getattr(self, '_tool_enabled', True)
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    async def execute(
        self,
        input_data: ToolInput,
        context: AgentContext
    ) -> ToolOutput:
        """Execute the tool with given input and context."""
        pass
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for LangChain integration."""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "object",
                "properties": {
                    "input": {
                        "type": "object",
                        "description": "Tool input parameters"
                    }
                },
                "required": ["input"]
            }
        }
    
    async def _handle_error(self, error: Exception, input_data: Any = None) -> ToolOutput:
        """Handle tool execution errors."""
        error_msg = str(error)
        self.logger.error(f"Tool {self.name} execution failed: {error_msg}")
        
        return ToolOutput(
            success=False,
            error=error_msg,
            metadata={
                "tool_name": self.name,
                "input_data": str(input_data) if input_data else None
            }
        )


class DataRetrievalTool(BaseTool):
    """Base class for data retrieval tools."""

    def __init__(self, name: Optional[str] = None, description: Optional[str] = None):
        super().__init__(name, description)
    
    async def _get_deal_data(self, deal_id: str, context: AgentContext) -> Optional[Dict[str, Any]]:
        """Get deal data from database."""
        # TODO: Implement deal data retrieval
        # This would connect to the main backend database
        self.logger.debug(f"Getting deal data for {deal_id}")
        return None
    
    async def _get_form_data(self, form_id: str, context: AgentContext) -> Optional[Dict[str, Any]]:
        """Get form data from database."""
        # TODO: Implement form data retrieval
        self.logger.debug(f"Getting form data for {form_id}")
        return None
    
    async def _get_submission_data(self, submission_id: str, context: AgentContext) -> Optional[Dict[str, Any]]:
        """Get submission data from database."""
        # TODO: Implement submission data retrieval
        self.logger.debug(f"Getting submission data for {submission_id}")
        return None


class AnalysisTool(BaseTool):
    """Base class for analysis tools."""

    def __init__(self, name: Optional[str] = None, description: Optional[str] = None):
        super().__init__(name, description)
    
    async def _calculate_score(
        self,
        data: Dict[str, Any],
        criteria: Dict[str, Any],
        weights: Optional[Dict[str, float]] = None
    ) -> float:
        """Calculate weighted score based on criteria."""
        # TODO: Implement scoring logic
        self.logger.debug("Calculating score")
        return 0.0
    
    async def _analyze_text(self, text: str, analysis_type: str = "sentiment") -> Dict[str, Any]:
        """Analyze text using various methods."""
        # TODO: Implement text analysis
        self.logger.debug(f"Analyzing text: {analysis_type}")
        return {}


class VectorTool(BaseTool):
    """Base class for vector-based tools."""
    
    def __init__(self, name: str, description: str):
        super().__init__(name, description)
    
    async def _search_vectors(
        self,
        query_vector: List[float],
        context: AgentContext,
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Search vectors in org-specific collection."""
        from ai.context import qdrant_helper
        
        return await qdrant_helper.search_vectors(
            org_id=str(context.org_id),
            query_vector=query_vector,
            limit=limit,
            filters=filters
        )
    
    async def _get_embedding(self, text: str) -> List[float]:
        """Get text embedding."""
        # TODO: Implement embedding generation using OpenAI
        self.logger.debug(f"Getting embedding for text: {text[:50]}...")
        return [0.0] * 1536  # Placeholder
