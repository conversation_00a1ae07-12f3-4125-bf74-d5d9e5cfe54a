export function TailwindIndicator() {
  if (process.env.NODE_ENV === "production") return null

  return (
    <div className="fixed bottom-1 left-1 z-50 flex size-6 items-center justify-center rounded-full bg-gray-800 p-3 font-mono text-xs text-white">
      <div className="block sm:hidden">xs</div>
      <div className="2xl:hidden hidden sm:block md:hidden lg:hidden xl:hidden">
        sm
      </div>
      <div className="2xl:hidden hidden md:block lg:hidden xl:hidden">md</div>
      <div className="2xl:hidden hidden lg:block xl:hidden">lg</div>
      <div className="2xl:hidden hidden xl:block">xl</div>
      <div className="2xl:block hidden">2xl</div>
    </div>
  )
}
