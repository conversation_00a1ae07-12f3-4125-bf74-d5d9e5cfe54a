import * as React from "react"
import { VariantProps, cva } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center border rounded-full px-5 py-2 text-base font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "bg-[linear-gradient(135deg,_#54FFBD_0%,_#00C2FF_100%)] text-[#27272A] shadow-[0_0_8px_rgba(84,255,189,0.15)] border-transparent",
        secondary:
          "bg-accent-secondary/10 text-accent-secondary border-transparent",
        destructive:
          "bg-warning text-white border-transparent",
        outline: "text-text border border-border",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
