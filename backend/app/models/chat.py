"""
Chat models for Orbit AI Investment Chat Assistant.

This module defines the database models for chat threads and messages,
supporting the Perplexity Sonar integration with async completions.
"""

from datetime import datetime, timezone
from enum import Enum
from typing import Annotated, Any, Dict, List, Optional

from pydantic import Field

from app.models.base import TractionXModel
from app.models.deal import Deal
from app.models.user import User
from app.utils.common import ObjectIdField, PyObjectId
from app.utils.model.helper import populate_reference


class MessageRole(str, Enum):
    """Message role types."""

    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class MessageStatus(str, Enum):
    """Message completion status."""

    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"


class ChatMode(str, Enum):
    """Chat mode types."""

    CHAT = "chat"  # ChatGPT - fast, context-aware, human-like
    RESEARCH = "research"  # Perplexity - sourced, analyst-grade
    AGENT = "agent"  # Future - autonomous, multi-step


class ChatSource(TractionXModel):
    """Represents a source/citation from Perplexity response."""

    title: str = Field(..., description="Source title")
    url: str = Field(..., description="Source URL")
    snippet: Optional[str] = Field(None, description="Text snippet from source")
    domain: Optional[str] = Field(None, description="Source domain")


class ChatThread(TractionXModel):
    """
    Represents a chat thread between a user and Orbit AI for a specific deal.
    Each deal/user/mode combination has its own persistent thread.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    deal_id: Annotated[ObjectIdField, populate_reference(Deal)] = Field(
        ..., description="Deal this thread is associated with"
    )
    user_id: Annotated[ObjectIdField, populate_reference(User)] = Field(
        ..., description="User who owns this thread"
    )
    org_id: ObjectIdField = Field(..., description="Organization ID for multi-tenancy")

    # Thread mode and metadata
    mode: ChatMode = Field(
        default=ChatMode.CHAT, description="Chat mode for this thread"
    )
    title: Optional[str] = Field(None, description="Optional thread title")
    last_message_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp()),
        description="Timestamp of last message",
    )
    message_count: int = Field(default=0, description="Total number of messages")

    # Mode-specific metadata
    last_model_used: Optional[str] = Field(None, description="Last AI model used")
    context_summary: Optional[str] = Field(
        None, description="Auto-generated context summary"
    )

    # Timestamps
    created_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
    updated_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )


class ChatMessage(TractionXModel):
    """
    Represents an individual message in a chat thread.
    Supports both user messages and AI responses with sources across different modes.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    thread_id: Annotated[ObjectIdField, populate_reference(ChatThread)] = Field(
        ..., description="Thread this message belongs to"
    )

    # Message content and mode
    role: MessageRole = Field(..., description="Message role (user/assistant/system)")
    content: str = Field(..., description="Message content")
    mode: ChatMode = Field(
        default=ChatMode.CHAT, description="Mode used for this message"
    )

    # AI response metadata
    status: MessageStatus = Field(
        default=MessageStatus.COMPLETED, description="Message completion status"
    )
    sources: List[ChatSource] = Field(
        default_factory=list,
        description="Sources/citations for AI responses (mainly for Research mode)",
    )

    # AI model metadata
    ai_model: Optional[str] = Field(
        None, description="AI model used (e.g., gpt-4, sonar-pro)"
    )
    ai_provider: Optional[str] = Field(
        None, description="AI provider (openai, perplexity, etc.)"
    )

    # Legacy Perplexity fields (for backward compatibility)
    perplexity_request_id: Optional[str] = Field(
        None, description="Perplexity request ID (deprecated)"
    )
    perplexity_model: Optional[str] = Field(
        None, description="Perplexity model (deprecated - use ai_model)"
    )

    # Error handling
    error_message: Optional[str] = Field(
        None, description="Error message if completion failed"
    )
    retry_count: int = Field(default=0, description="Number of retry attempts")

    # Context metadata
    deal_context: Optional[Dict[str, Any]] = Field(
        None, description="Deal context snapshot at time of message"
    )

    # Performance metadata
    response_time_ms: Optional[int] = Field(
        None, description="AI response time in milliseconds"
    )
    token_count: Optional[int] = Field(None, description="Token count for the response")

    # Timestamps
    created_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
    updated_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )


class ChatCompletionJob(TractionXModel):
    """
    Represents a background job for processing chat completions.
    Used for tracking async Perplexity API calls.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    message_id: Annotated[ObjectIdField, populate_reference(ChatMessage)] = Field(
        ..., description="Message being processed"
    )
    thread_id: Annotated[ObjectIdField, populate_reference(ChatThread)] = Field(
        ..., description="Thread the message belongs to"
    )

    # Job metadata
    job_id: Optional[str] = Field(None, description="RQ job ID")
    perplexity_request_id: str = Field(..., description="Perplexity async request ID")

    # Status tracking
    status: MessageStatus = Field(default=MessageStatus.PENDING)
    started_at: Optional[int] = Field(None, description="Job start timestamp")
    completed_at: Optional[int] = Field(None, description="Job completion timestamp")

    # Error handling
    error_message: Optional[str] = Field(
        None, description="Error message if job failed"
    )
    retry_count: int = Field(default=0, description="Number of retry attempts")
    max_retries: int = Field(default=3, description="Maximum retry attempts")

    # Timestamps
    created_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
    updated_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
