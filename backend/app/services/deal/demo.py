"""
Demo Deal Service

This module provides services for creating demo deals with realistic data
to help new users understand the platform's value proposition.

This service follows the exact same workflow as public submissions to ensure consistency.
"""

import random
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Union

from bson import ObjectId

from app.core.logging import get_logger
from app.models.deal import Deal
from app.models.form import Submission
from app.services.base import BaseService
from app.utils.common import PyObjectId
from app.utils.jobs.job_utils import create_job_chain

logger = get_logger(__name__)


class DemoDealService(BaseService):
    """Service for creating demo deals following the public submission workflow."""

    def __init__(self):
        super().__init__()

    async def initialize(self) -> None:
        """Initialize service dependencies."""
        pass

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        pass

    async def create_demo_deal(
        self,
        org_id: Union[str, ObjectId],
        form_id: Union[str, ObjectId],
        created_by: Union[str, ObjectId],
    ) -> Optional[Deal]:
        """
        Create a demo deal following the exact same workflow as public submissions.

        This creates a submission with realistic answers that will trigger
        the complete submission processing workflow to create a deal with scores.

        Args:
            org_id: Organization ID
            form_id: Form ID to create submission for
            created_by: User ID who is creating the demo

        Returns:
            Created deal with scoring data, or None if creation fails
        """
        try:
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(form_id, str):
                form_id = ObjectId(form_id)
            if isinstance(created_by, str):
                created_by = ObjectId(created_by)

            logger.info(f"Creating demo deal for org {org_id} with form {form_id}")

            # Step 1: Get form details to understand question structure
            from app.services.factory import get_form_service

            form_service = await get_form_service()
            form = await form_service.get_form_with_details(str(form_id))  # type: ignore

            if not form:
                logger.error(f"Form {form_id} not found")
                return None

            # Step 2: Generate realistic demo answers mapped to actual question IDs
            demo_answers = await self._generate_demo_answers_for_form(form)
            logger.info(f"Generated demo answers: {demo_answers}")

            # Step 3: Create the actual submission (same as public submission flow)
            submission = await Submission(
                org_id=PyObjectId(org_id),
                form_id=PyObjectId(form_id),
                answers=demo_answers,
                metadata={
                    "submitted_via": "demo_creation",
                    "is_demo": True,
                    "demo_created_by": str(created_by),
                    "demo_created_at": int(datetime.now(timezone.utc).timestamp()),
                },
            ).save()

            logger.info(f"Created demo submission {submission.id}")

            # Step 4: Create submission processing job chain (same as public submission flow)
            jobs = await create_job_chain(
                entity_type="form_submission",
                entity_id=str(submission.id),
                job_configs=[
                    # Submission processing job (comprehensive workflow)
                    {
                        "job_type": "submission_processing",
                        "queue_job_type": "submission_processing",
                        "payload": {
                            "submission_id": str(submission.id),
                            "form_id": str(form_id),
                            "org_id": str(org_id),
                            "answers": demo_answers,
                            "metadata": {
                                "submitted_via": "demo_creation",
                                "is_demo": True,
                                "demo_created_by": str(created_by),
                                "demo_created_at": int(
                                    datetime.now(timezone.utc).timestamp()
                                ),
                                "submission_time": int(datetime.now().timestamp()),
                            },
                            "created_by": str(created_by),  # Demo has user context
                        },
                        "metadata": {
                            "submission_time": int(datetime.now().timestamp()),
                            "submission_source": "demo_creation",
                        },
                    }
                ],
            )

            logger.info(
                f"Created submission processing job chain for demo submission {submission.id}: {jobs}"
            )

            # Step 5: Wait for processing to complete and return the deal
            # Note: In real implementation, this would be handled asynchronously
            # For demo purposes, we return the submission ID and let the async processing handle deal creation

            logger.info(
                f"Successfully created demo submission {submission.id} with processing jobs"
            )

            # Return a placeholder - the actual deal will be created by the async job
            # In practice, the frontend will poll for the deal or receive it via websocket
            return None  # Will be replaced by actual deal from async processing

        except Exception as e:
            logger.error(f"Error creating demo deal: {str(e)}", exc_info=True)
            await self.handle_error(
                e,
                {
                    "org_id": str(org_id),
                    "form_id": str(form_id),
                    "created_by": str(created_by),
                },
            )
            return None

    async def _generate_demo_answers_for_form(self, form: Any) -> Dict[str, Any]:
        """
        Generate realistic demo answers mapped to actual question IDs from the form.

        This properly maps to the core field extraction logic by:
        1. Finding questions with core fields
        2. Mapping demo values to the correct question IDs
        3. Handling repeatable sections for founders
        """

        # Get all questions from form sections
        from app.models.form import Question

        questions = []
        section_map = {}

        for section in form.sections:
            section_map[section.id] = section
            section_questions = await Question.find_many(
                query={"section_id": section.id},
                sort=[("order", 1)],
            )
            questions.extend(section_questions)

        # Generate demo company data
        demo_companies = [
            {
                "company_name": "TractionX",
                "company_website": "https://tractionx.ai",
                "stage": "seed",
                "sector": ["saas", "ai_ml"],
                "description": "We are building a decision support system for private market investors - including VCs, accelerators, PEs, angels, syndicates,  — to streamline the entire deal lifecycle and reduce time to decisions.",
            },
            {
                "company_name": "TractionX Labs",
                "company_website": "https://tractionx.ai",
                "stage": "series_a",
                "sector": ["fintech", "saas"],
                "description": "We are building a decision support system for private market investors - including VCs, accelerators, PEs, angels, syndicates,  — to streamline the entire deal lifecycle and reduce time to decisions.",
            },
            {
                "company_name": "TractionX Analytics",
                "company_website": "https://tractionx.ai",
                "stage": "pre_seed",
                "sector": ["ai_ml", "fintech"],
                "description": "We are building a decision support system for private market investors - including VCs, accelerators, PEs, angels, syndicates,  — to streamline the entire deal lifecycle and reduce time to decisions.",
            },
        ]

        selected_company = random.choice(demo_companies)

        # Founder data for repeatable sections
        founders_data = [
            {
                "founder_name": "Andy",
                "founder_role": ["ceo", "co_founder"],
                "founder_linkedin": "https://www.linkedin.com/in/andy-singcharoenchai/",
            },
            {
                "founder_name": "Prasanna",
                "founder_role": ["cto", "co_founder"],
                "founder_linkedin": "https://www.linkedin.com/in/prasanna-saravanan/",
            },
        ]

        # Map answers to actual question IDs
        answers = {}

        for question in questions:
            question_id = str(question.id)
            section = section_map.get(question.section_id)

            # Handle core field questions
            if question.core_field:
                if section and section.repeatable:
                    # Handle repeatable sections (founders)
                    if question.core_field == "founder_name":
                        for i, founder in enumerate(
                            founders_data[:2]
                        ):  # Limit to 2 founders
                            answers[f"{question_id}_{i}"] = founder["founder_name"]
                    elif question.core_field == "founder_role":
                        for i, founder in enumerate(founders_data[:2]):
                            answers[f"{question_id}_{i}"] = founder["founder_role"]
                    elif question.core_field == "founder_linkedin":
                        for i, founder in enumerate(founders_data[:2]):
                            answers[f"{question_id}_{i}"] = founder["founder_linkedin"]
                else:
                    # Handle regular core fields
                    if question.core_field == "company_name":
                        answers[question_id] = selected_company["company_name"]
                    elif question.core_field == "company_website":
                        answers[question_id] = selected_company["company_website"]
                    elif question.core_field == "stage":
                        answers[question_id] = selected_company["stage"]
                    elif question.core_field == "sector":
                        answers[question_id] = selected_company["sector"]

            # Handle non-core field questions based on label matching
            elif not question.core_field:
                demo_value = self._generate_demo_value_by_question(
                    question, selected_company
                )
                if demo_value is not None:
                    answers[question_id] = demo_value

        logger.info(
            f"Generated {len(answers)} demo answers mapped to actual question IDs for {selected_company['company_name']}"
        )
        return answers

    def _generate_demo_value_by_question(
        self, question: Any, company_data: Dict[str, Any]
    ) -> Any:
        """
        Generate demo values for non-core field questions based on their labels and types.
        """
        label = question.label.lower().strip()
        question_type = question.type

        # Map common question patterns to demo values
        if "technical co-founder" in label or "technical cofounder" in label:
            return True
        elif "product" in label and "building" in label:
            return "web_app" if question_type == "single_select" else "Web Application"
        elif "architecture" in label or "describe" in label and "web" in label:
            return company_data["description"]
        elif "paying customers" in label and "have" in label:
            return True
        elif "customers" in label and "many" in label:
            return random.randint(10, 50)
        elif "revenue" in label and "monthly" in label:
            return random.randint(10000, 100000)
        elif "team size" in label:
            return random.randint(5, 15)
        elif "runway" in label or "months" in label:
            return random.randint(12, 24)
        elif "acquisition cost" in label:
            return random.randint(100, 500)
        elif "lifetime value" in label:
            return random.randint(1000, 5000)
        elif "growth rate" in label:
            return random.randint(10, 30)
        elif "market size" in label:
            return "1b_10b" if question_type == "single_select" else "1-10 billion"
        elif "challenge" in label and "biggest" in label:
            return f"Our biggest challenge is scaling our {company_data['description'].lower().split('.')[0]} while maintaining quality and user experience."
        elif "start working" in label or "idea" in label:
            return (datetime.now() - timedelta(days=random.randint(180, 720))).strftime(
                "%Y-%m-%d"
            )
        elif "hear about" in label:
            return (
                random.choice(["referral", "google", "social_media", "event"])
                if question_type == "single_select"
                else "Referral"
            )
        elif "updates" in label and "receive" in label:
            return True
        elif question_type == "boolean":
            return random.choice([True, False])
        elif question_type == "number":
            return random.randint(1, 100)
        elif question_type == "date":
            return (datetime.now() - timedelta(days=random.randint(30, 365))).strftime(
                "%Y-%m-%d"
            )
        elif question_type == "single_select" and question.options:
            return random.choice(question.options)["value"]
        elif question_type == "multi_select" and question.options:
            num_selections = min(random.randint(1, 2), len(question.options))
            selected_options = random.sample(question.options, num_selections)
            return [opt["value"] for opt in selected_options]
        elif question_type in ["short_text", "long_text"]:
            return "Demo response for this question"

        return None

    def _generate_demo_answers(self) -> Dict[str, Any]:
        """
        Legacy method - kept for backward compatibility but not used.
        Use _generate_demo_answers_for_form instead.
        """
        return {}

    async def create_multiple_demo_deals(
        self,
        org_id: Union[str, ObjectId],
        form_id: Union[str, ObjectId],
        created_by: Union[str, ObjectId],
        count: int = 1,
    ) -> List[Optional[Deal]]:
        """
        Create multiple demo deals with different characteristics.

        Args:
            org_id: Organization ID
            form_id: Form ID to create submissions for
            created_by: User ID who is creating the demos
            count: Number of demo deals to create

        Returns:
            List of created demo deals (may contain None for failed creations)
        """
        deals = []

        for i in range(count):
            try:
                deal = await self.create_demo_deal(org_id, form_id, created_by)
                deals.append(deal)
                logger.info(f"Created demo deal {i + 1}/{count}")
            except Exception as e:
                logger.error(f"Error creating demo deal {i + 1}/{count}: {str(e)}")
                deals.append(None)
                continue

        success_count = len([d for d in deals if d is not None])
        logger.info(f"Successfully created {success_count}/{count} demo deals")
        return deals
