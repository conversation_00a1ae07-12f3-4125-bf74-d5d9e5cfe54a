"use client"

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import apiClient from '@/lib/api-client';
import { useAuth } from '@/lib/auth-context';

export function AuthTestPanel() {
  const { isAuthenticated, user, token, refreshToken } = useAuth();
  const [testResults, setTestResults] = useState<Array<{
    test: string;
    success: boolean;
    message: string;
    timestamp: string;
  }>>([]);

  const addResult = (test: string, success: boolean, message: string) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      message,
      timestamp: new Date().toISOString()
    }]);
  };

  const testProtectedEndpoint = async () => {
    try {
      console.log('🧪 Testing protected endpoint with apiClient...');
      const response = await apiClient.get('/users/me');
      addResult('Protected Endpoint', true, `Success: ${response.data.email}`);
    } catch (error: any) {
      addResult('Protected Endpoint', false, `Error: ${error.message}`);
    }
  };

  const testFormsList = async () => {
    try {
      console.log('🧪 Testing forms list endpoint...');
      const response = await apiClient.get('/forms');
      addResult('Forms List', true, `Success: ${response.data.length} forms`);
    } catch (error: any) {
      addResult('Forms List', false, `Error: ${error.message}`);
    }
  };

  const expireToken = () => {
    // Set an expired token to trigger refresh
    localStorage.setItem('token', 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0IiwiZXhwIjoxfQ.expired');
    addResult('Token Expiry', true, 'Access token manually expired');
  };

  const expireRefreshToken = () => {
    // Set an expired refresh token
    localStorage.setItem('refreshToken', 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0IiwiZXhwIjoxfQ.expired');
    addResult('Refresh Token Expiry', true, 'Refresh token manually expired');
  };

  const testMultipleConcurrentRequests = async () => {
    try {
      console.log('🧪 Testing multiple concurrent requests...');
      
      // First expire the token
      expireToken();
      
      // Make multiple concurrent requests
      const promises = [
        apiClient.get('/users/me'),
        apiClient.get('/forms'),
        apiClient.get('/users/me'),
        apiClient.get('/forms'),
        apiClient.get('/users/me')
      ];
      
      const results = await Promise.allSettled(promises);
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      
      addResult('Concurrent Requests', successful > 0, 
        `${successful} successful, ${failed} failed`);
    } catch (error: any) {
      addResult('Concurrent Requests', false, `Error: ${error.message}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <Card className="mx-auto w-full max-w-4xl">
      <CardHeader>
        <CardTitle>🔐 Authentication Flow Test Panel</CardTitle>
        <div className="flex gap-2">
          <Badge variant={isAuthenticated ? "default" : "destructive"}>
            {isAuthenticated ? "Authenticated" : "Not Authenticated"}
          </Badge>
          {user && <Badge variant="outline">{user.email}</Badge>}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Token Status */}
        <div className="grid grid-cols-2 gap-4">
          <Alert>
            <AlertDescription>
              <strong>Access Token:</strong><br />
              {token ? `${token.substring(0, 30)}...` : 'None'}
            </AlertDescription>
          </Alert>
          <Alert>
            <AlertDescription>
              <strong>Refresh Token:</strong><br />
              {refreshToken ? `${refreshToken.substring(0, 30)}...` : 'None'}
            </AlertDescription>
          </Alert>
        </div>

        {/* Test Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button onClick={testProtectedEndpoint} variant="outline">
            Test Protected Endpoint
          </Button>
          <Button onClick={testFormsList} variant="outline">
            Test Forms List
          </Button>
          <Button onClick={expireToken} variant="destructive">
            Expire Access Token
          </Button>
          <Button onClick={expireRefreshToken} variant="destructive">
            Expire Refresh Token
          </Button>
          <Button onClick={testMultipleConcurrentRequests} variant="secondary">
            Test Concurrent Requests
          </Button>
          <Button onClick={clearResults} variant="ghost">
            Clear Results
          </Button>
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-semibold">Test Results:</h4>
            <div className="max-h-60 space-y-1 overflow-y-auto">
              {testResults.map((result, index) => (
                <Alert key={index} className={result.success ? "border-green-200" : "border-red-200"}>
                  <AlertDescription>
                    <div className="flex items-start justify-between">
                      <div>
                        <span className={result.success ? "text-green-600" : "text-red-600"}>
                          {result.success ? "✅" : "❌"}
                        </span>
                        <strong className="ml-2">{result.test}:</strong>
                        <span className="ml-2">{result.message}</span>
                      </div>
                      <span className="text-xs text-gray-500">
                        {new Date(result.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          </div>
        )}

        {/* Instructions */}
        <Alert>
          <AlertDescription>
            <strong>Test Instructions:</strong>
            <ol className="mt-2 list-inside list-decimal space-y-1">
              <li>First, make sure you're logged in</li>
              <li>Test a protected endpoint to verify it works</li>
              <li>Click "Expire Access Token" to simulate token expiration</li>
              <li>Test the protected endpoint again - it should auto-refresh and succeed</li>
              <li>Click "Expire Refresh Token" to simulate refresh token expiration</li>
              <li>Test again - it should log you out and redirect to login</li>
              <li>Test concurrent requests to verify no race conditions</li>
            </ol>
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
}
