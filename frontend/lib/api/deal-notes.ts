/**
 * Deal Notes API Client
 * 
 * API functions for managing deal notes with structured content
 */

import apiClient from '../api-client';
import {
  DealNote,
  DealNoteCreate,
  DealNoteUpdate,
  DealNoteListResponse,
  DealNoteCreateResponse,
  DealNoteDeleteResponse,
} from '@/lib/types/deal-notes';

/**
 * List notes for a deal
 */
export async function listDealNotes(
  dealId: string,
  params: {
    skip?: number;
    limit?: number;
    include_deleted?: boolean;
  } = {}
): Promise<DealNoteListResponse> {
  const searchParams = new URLSearchParams();
  
  if (params.skip !== undefined) searchParams.append('skip', params.skip.toString());
  if (params.limit !== undefined) searchParams.append('limit', params.limit.toString());
  if (params.include_deleted !== undefined) searchParams.append('include_deleted', params.include_deleted.toString());

  const response = await apiClient.get(`/deals/${dealId}/notes?${searchParams}`);
  return response.data;
}

/**
 * Get a specific note
 */
export async function getDealNote(
  dealId: string,
  noteId: string
): Promise<DealNote> {
  const response = await apiClient.get(`/deals/${dealId}/notes/${noteId}`);
  return response.data;
}

/**
 * Create a new note
 */
export async function createDealNote(
  dealId: string,
  data: DealNoteCreate
): Promise<DealNoteCreateResponse> {
  const response = await apiClient.post(`/deals/${dealId}/notes`, data);
  return response.data;
}

/**
 * Update a note
 */
export async function updateDealNote(
  dealId: string,
  noteId: string,
  data: DealNoteUpdate
): Promise<DealNote> {
  const response = await apiClient.patch(`/deals/${dealId}/notes/${noteId}`, data);
  return response.data;
}

/**
 * Delete a note
 */
export async function deleteDealNote(
  dealId: string,
  noteId: string
): Promise<DealNoteDeleteResponse> {
  const response = await apiClient.delete(`/deals/${dealId}/notes/${noteId}`);
  return response.data;
}

/**
 * Toggle note pin status
 */
export async function toggleNotePin(
  dealId: string,
  noteId: string,
  pinned: boolean
): Promise<DealNote> {
  const response = await apiClient.patch(`/deals/${dealId}/notes/${noteId}/pin`, {
    pinned,
  });
  return response.data;
}

/**
 * Get notes where current user is tagged
 */
export async function getNotesByUser(
  params: {
    skip?: number;
    limit?: number;
  } = {}
): Promise<DealNoteListResponse> {
  const searchParams = new URLSearchParams();
  
  if (params.skip !== undefined) searchParams.append('skip', params.skip.toString());
  if (params.limit !== undefined) searchParams.append('limit', params.limit.toString());

  const response = await apiClient.get(`/deals/notes/tagged?${searchParams}`);
  return response.data;
}

/**
 * Get note count for a deal
 */
export async function getNoteCount(dealId: string): Promise<number> {
  const response = await apiClient.get(`/deals/${dealId}/notes/count`);
  return response.data.count;
} 