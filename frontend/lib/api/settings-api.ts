import apiClient from "../api-client";


// Types for Settings API
export interface UserProfile {
  id: string;
  name: string;
  email: string;
  profile_picture?: string;
  status: string;
  created_at: number;
  last_login?: number;
}

export interface UpdateProfileRequest {
  name?: string;
  profile_picture?: string;
}

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

export interface OrganizationInfo {
  id: string;
  name: string;
  subdomain?: string;
  logo_url?: string;
  website?: string;
  contact_email?: string;
  address?: string;
  description?: string;
  plan: string;
  created_at: number;
}

export interface UpdateOrganizationRequest {
  name?: string;
  logo_url?: string;
  website?: string;
  contact_email?: string;
  address?: string;
  description?: string;
}

export interface OrganizationMember {
  id: string;
  name: string;
  email: string;
  profile_picture?: string;
  status: string;
  role_name?: string;
  last_login?: number;
  created_at: number;
}

export interface InviteMemberRequest {
  emails: string[];
  role_id?: string;
  message?: string;
}

export interface InviteMemberResponse {
  total_sent: number;
  total_failed: number;
  results: Array<{
    email: string;
    status: 'sent' | 'failed';
    result?: any;
    error?: string;
  }>;
}

export interface UploadUrlRequest {
  file_type: string;
  filename: string;
  content_type: string;
  file_size: number;
}

export interface UploadUrlResponse {
  presigned_url: string;
  public_url: string;
  s3_key: string;
  expires_in: number;
}

export interface ThesisConfig {
  geography: string[];
  sector: string[];
  stage: string[];
  business_model: string[];
}

export interface ThesisConfigResponse {
  success: boolean;
  message: string;
}

// Settings API Client
export const SettingsAPI = {
  // Profile endpoints
  async getProfile(): Promise<UserProfile> {
    const response = await apiClient.get('/settings/profile');
    return response.data;
  },

  async updateProfile(data: UpdateProfileRequest): Promise<{ message: string }> {
    const response = await apiClient.patch('/settings/profile', data);
    return response.data;
  },

  async changePassword(data: ChangePasswordRequest): Promise<{ message: string }> {
    const response = await apiClient.post('/settings/profile/change-password', data);
    return response.data;
  },

  // Organization endpoints
  async getOrganization(): Promise<OrganizationInfo> {
    const response = await apiClient.get('/settings/organization');
    return response.data;
  },

  async updateOrganization(data: UpdateOrganizationRequest): Promise<{ message: string }> {
    const response = await apiClient.patch('/settings/organization', data);
    return response.data;
  },

  // Members endpoints
  async getMembers(): Promise<OrganizationMember[]> {
    const response = await apiClient.get('/settings/members');
    return response.data;
  },

  async inviteMembers(data: InviteMemberRequest): Promise<InviteMemberResponse> {
    const response = await apiClient.post('/settings/members/invite', data);
    return response.data;
  },

  async removeMember(memberId: string): Promise<{ message: string }> {
    const response = await apiClient.delete(`/settings/members/${memberId}`);
    return response.data;
  },

  // Upload endpoints
  async generateUploadUrl(data: UploadUrlRequest): Promise<UploadUrlResponse> {
    const response = await apiClient.post('/uploads/generate-presigned-url', data);
    return response.data;
  },

  async deleteAsset(s3Key: string): Promise<{ message: string }> {
    const response = await apiClient.delete(`/uploads/asset/${s3Key}`);
    return response.data;
  },

  // Thesis configuration endpoints
  async getThesisConfig(): Promise<ThesisConfig> {
    // Get current org ID from localStorage or auth context
    const orgId = localStorage.getItem('orgId');
    if (!orgId) {
      throw new Error('Organization ID not found');
    }
    const response = await apiClient.get(`/organizations/${orgId}/thesis-config`);
    return response.data;
  },

  async updateThesisConfig(data: ThesisConfig): Promise<ThesisConfigResponse> {
    // Get current org ID from localStorage or auth context
    const orgId = localStorage.getItem('orgId');
    if (!orgId) {
      throw new Error('Organization ID not found');
    }
    const response = await apiClient.post(`/organizations/${orgId}/thesis-config`, data);
    return response.data;
  },

  // Helper method for file upload
  async uploadFile(file: File, type: 'avatar' | 'logo'): Promise<string> {
    try {
      // Step 1: Generate presigned URL
      const uploadData = await this.generateUploadUrl({
        file_type: type,
        filename: file.name,
        content_type: file.type,
        file_size: file.size,
      });

      // Step 2: Upload file to S3
      const formData = new FormData();
      formData.append('Content-Type', file.type);
      formData.append('Content-Length', file.size.toString());
      formData.append('ACL', 'public-read');
      formData.append('file', file);

      const uploadResponse = await fetch(uploadData.presigned_url, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
          'Content-Length': file.size.toString(),
        },
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload file to S3');
      }

      // Step 3: Return public URL
      return uploadData.public_url;
    } catch (error) {
      console.error('File upload error:', error);
      throw new Error('Failed to upload file');
    }
  },
};

export default SettingsAPI;
