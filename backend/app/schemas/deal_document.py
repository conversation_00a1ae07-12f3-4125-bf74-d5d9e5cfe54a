"""
Pydantic schemas for deal document API operations.
"""

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field

from app.models.deal_document import DocumentSource, DocumentStatus, DocumentType


class DealDocumentResponse(BaseModel):
    """Response schema for deal document."""

    id: str = Field(..., description="Document ID")
    deal_id: str = Field(..., description="Deal ID")
    org_id: str = Field(..., description="Organization ID")
    source: DocumentSource = Field(..., description="Document source")
    filename: str = Field(..., description="Original filename")
    document_type: DocumentType = Field(..., description="Document type")
    file_size: int = Field(..., description="File size in bytes")
    status: DocumentStatus = Field(..., description="Document status")

    # User information
    uploaded_by_email: str = Field(..., description="Email of uploader")
    uploaded_by_name: Optional[str] = Field(None, description="Name of uploader")
    uploaded_by_role: Optional[str] = Field(None, description="Role of uploader")

    # URLs
    download_url: Optional[str] = Field(None, description="Download URL")
    preview_url: Optional[str] = Field(None, description="Preview URL")

    # Tags and metadata
    tags: List[str] = Field(default_factory=list, description="Document tags")

    # Access tracking
    download_count: int = Field(0, description="Download count")
    last_accessed_at: Optional[datetime] = Field(None, description="Last access time")
    last_accessed_by: Optional[str] = Field(None, description="Last accessor email")

    # Timestamps
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Update timestamp")

    # Permissions
    can_delete: bool = Field(False, description="Whether current user can delete")

    class Config:
        from_attributes = True


class DealDocumentListResponse(BaseModel):
    """Response schema for listing deal documents."""

    documents: List[DealDocumentResponse] = Field(..., description="List of documents")
    total_count: int = Field(..., description="Total number of documents")
    total_size: int = Field(..., description="Total size of all documents in bytes")


class DocumentUploadRequest(BaseModel):
    """Request schema for document upload."""

    tags: Optional[List[str]] = Field(
        None, description="Optional tags for the document"
    )


class DocumentTagRequest(BaseModel):
    """Request schema for adding/removing tags."""

    tags: List[str] = Field(..., description="Tags to add or remove")


class DocumentDeleteResponse(BaseModel):
    """Response schema for document deletion."""

    success: bool = Field(..., description="Whether deletion was successful")
    message: str = Field(..., description="Success or error message")


class DocumentDownloadResponse(BaseModel):
    """Response schema for document download URL."""

    download_url: str = Field(..., description="Presigned download URL")
    expires_in: int = Field(..., description="URL expiry time in seconds")
