"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { 
  Play, 
  CheckCircle, 
  Clock, 
  HelpCircle, 
  ChevronDown,
  BookOpen,
  Target,
  FileText,
  FolderOpen,
  Settings,
  BarChart3
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useTour } from "./tour-provider"
import { tourConfigurations, TourType } from "./tour-steps"
import { cn } from "@/lib/utils"

const tourIcons: Record<TourType, any> = {
  dashboard: BarChart3,
  deals: FolderOpen,
  forms: FileText,
  theses: Target,
  settings: Settings,
  workflow: BookOpen
}

interface TourMenuProps {
  variant?: 'button' | 'icon' | 'minimal'
  className?: string
}

export function TourMenu({ variant = 'button', className }: TourMenuProps) {
  const { startTour, hasCompletedTour } = useTour()
  const [isOpen, setIsOpen] = useState(false)

  const handleStartTour = (tourType: TourType) => {
    startTour(tourType)
    setIsOpen(false)
  }

  const availableTours = Object.entries(tourConfigurations).map(([key, config]) => ({
    type: key as TourType,
    ...config,
    completed: hasCompletedTour(key as TourType)
  }))

  const completedCount = availableTours.filter(tour => tour.completed).length
  const totalCount = availableTours.length

  if (variant === 'icon') {
    return (
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn("relative", className)}
          >
            <HelpCircle className="size-4" />
            {completedCount < totalCount && (
              <span className="absolute -top-1 -right-1 size-2 bg-purple-500 rounded-full" />
            )}
          </Button>
        </DropdownMenuTrigger>
        <TourMenuContent 
          tours={availableTours}
          onStartTour={handleStartTour}
          completedCount={completedCount}
          totalCount={totalCount}
        />
      </DropdownMenu>
    )
  }

  if (variant === 'minimal') {
    return (
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn("text-xs text-gray-500 hover:text-gray-700", className)}
          >
            Help & Tours
            <ChevronDown className="size-3 ml-1" />
          </Button>
        </DropdownMenuTrigger>
        <TourMenuContent 
          tours={availableTours}
          onStartTour={handleStartTour}
          completedCount={completedCount}
          totalCount={totalCount}
        />
      </DropdownMenu>
    )
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn("flex items-center gap-2", className)}
        >
          <Play className="size-4" />
          Product Tours
          {completedCount < totalCount && (
            <Badge variant="secondary" className="text-xs">
              {completedCount}/{totalCount}
            </Badge>
          )}
          <ChevronDown className="size-4" />
        </Button>
      </DropdownMenuTrigger>
      <TourMenuContent 
        tours={availableTours}
        onStartTour={handleStartTour}
        completedCount={completedCount}
        totalCount={totalCount}
      />
    </DropdownMenu>
  )
}

interface TourMenuContentProps {
  tours: Array<{
    type: TourType
    id: string
    name: string
    description: string
    completed: boolean
    steps: any[]
  }>
  onStartTour: (tourType: TourType) => void
  completedCount: number
  totalCount: number
}

function TourMenuContent({ tours, onStartTour, completedCount, totalCount }: TourMenuContentProps) {
  return (
    <DropdownMenuContent align="end" className="w-80">
      <DropdownMenuLabel className="flex items-center justify-between">
        <span>Product Tours</span>
        <Badge variant="secondary" className="text-xs">
          {completedCount}/{totalCount} completed
        </Badge>
      </DropdownMenuLabel>
      <DropdownMenuSeparator />
      
      {/* Progress indicator */}
      <div className="px-2 py-3">
        <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
          <span>Progress</span>
          <span>{Math.round((completedCount / totalCount) * 100)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            className="bg-purple-600 h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${(completedCount / totalCount) * 100}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>
      </div>

      <DropdownMenuSeparator />

      {/* Tour list */}
      <div className="max-h-96 overflow-y-auto">
        {tours.map((tour) => {
          const Icon = tourIcons[tour.type]
          
          return (
            <DropdownMenuItem
              key={tour.type}
              onClick={() => onStartTour(tour.type)}
              className="flex items-start gap-3 p-3 cursor-pointer hover:bg-gray-50"
            >
              <div className={cn(
                "flex size-8 items-center justify-center rounded-lg flex-shrink-0",
                tour.completed 
                  ? "bg-green-100 text-green-600" 
                  : "bg-purple-100 text-purple-600"
              )}>
                {tour.completed ? (
                  <CheckCircle className="size-4" />
                ) : (
                  <Icon className="size-4" />
                )}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-sm text-gray-900 truncate">
                    {tour.name}
                  </h4>
                  {tour.completed && (
                    <Badge variant="secondary" className="text-xs">
                      ✓
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-gray-500 line-clamp-2">
                  {tour.description}
                </p>
                <div className="flex items-center gap-2 mt-2">
                  <div className="flex items-center gap-1 text-xs text-gray-400">
                    <Clock className="size-3" />
                    {tour.steps.length} steps
                  </div>
                  <div className="flex items-center gap-1 text-xs text-purple-600">
                    <Play className="size-3" />
                    {tour.completed ? 'Replay tour' : 'Start tour'}
                  </div>
                </div>
              </div>
            </DropdownMenuItem>
          )
        })}
      </div>

      <DropdownMenuSeparator />
      
      {/* Footer */}
      <div className="p-2">
        <p className="text-xs text-gray-500 text-center">
          Tours help you learn TractionX features quickly
        </p>
      </div>
    </DropdownMenuContent>
  )
}

// Floating tour button for first-time users
interface FloatingTourButtonProps {
  className?: string
}

export function FloatingTourButton({ className }: FloatingTourButtonProps) {
  const { startTour, hasCompletedTour } = useTour()
  const [isVisible, setIsVisible] = useState(true)

  // Hide if user has completed dashboard tour
  if (hasCompletedTour('dashboard') || !isVisible) {
    return null
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.8, y: 20 }}
      className={cn(
        "fixed bottom-6 right-6 z-50",
        className
      )}
    >
      <Button
        onClick={() => startTour('dashboard')}
        className={cn(
          "bg-gradient-to-r from-purple-500 to-purple-600 text-white",
          "hover:from-purple-600 hover:to-purple-700",
          "shadow-lg hover:shadow-xl transition-all duration-300",
          "flex items-center gap-2 px-6 py-3 rounded-full",
          "animate-pulse"
        )}
      >
        <Play className="size-4" />
        Take a Tour
      </Button>
      
      {/* Dismiss button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsVisible(false)}
        className="absolute -top-2 -right-2 size-6 p-0 bg-white shadow-sm rounded-full text-gray-400 hover:text-gray-600"
      >
        ×
      </Button>
    </motion.div>
  )
}
