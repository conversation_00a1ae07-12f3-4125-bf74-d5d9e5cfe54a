// API client for deal notes operations
import apiClient from '@/lib/api-client';
import { NoteContent } from '@/lib/types/deal-notes';

export interface DealNote {
  _id: string;
  deal_id: string;
  org_id: string;
  created_by: {
    _id: string;
    name: string;
    email: string;
  };
  structured_content: NoteContent;
  tagged_user_ids: string[];
  pinned: boolean;
  deleted_at?: number;
  deleted_by?: string;
  created_at: number;
  updated_at: number;
}

export interface DealNoteCreate {
  structured_content: NoteContent;
  tagged_user_ids?: string[];
  pinned?: boolean;
}

export interface DealNoteUpdate {
  structured_content?: NoteContent;
  tagged_user_ids?: string[];
  pinned?: boolean;
}

export interface DealNoteListResponse {
  notes: DealNote[];
  total: number;
  skip: number;
  limit: number;
  has_more: boolean;
}

export interface DealNoteCreateResponse {
  success: boolean;
  note_id: string;
  message: string;
}

export interface DealNoteDeleteResponse {
  success: boolean;
  message: string;
}

export interface DealNotePinRequest {
  pinned: boolean;
}

export const DealNotesAPI = {
  /**
   * List notes for a deal
   */
  async listNotes(
    dealId: string,
    skip: number = 0,
    limit: number = 100,
    includeDeleted: boolean = false
  ): Promise<DealNoteListResponse> {
    console.log(`Fetching notes for deal ${dealId}`);
    
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
        include_deleted: includeDeleted.toString(),
      });
      
      const response = await apiClient.get(`/deals/${dealId}/notes?${params}`);
      console.log('Notes fetched:', response.data);
      console.log('First note structure:', response.data.notes?.[0]);
      
      // Map id to _id for compatibility
      const notes = (response.data.notes || []).map((note: any) => ({
        ...note,
        _id: note._id || note.id,
      }));
      
      return { ...response.data, notes };
    } catch (error: any) {
      console.error('Error fetching notes:', error);
      throw error;
    }
  },

  /**
   * Create a new note
   */
  async createNote(dealId: string, noteData: DealNoteCreate): Promise<DealNoteCreateResponse> {
    console.log(`Creating note for deal ${dealId}:`, noteData);
    
    try {
      const response = await apiClient.post(`/deals/${dealId}/notes`, noteData);
      console.log('Note created:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error creating note:', error);
      throw error;
    }
  },

  /**
   * Get a specific note
   */
  async getNote(dealId: string, noteId: string): Promise<DealNote> {
    console.log(`Fetching note ${noteId} for deal ${dealId}`);
    
    try {
      const response = await apiClient.get(`/deals/${dealId}/notes/${noteId}`);
      console.log('Note fetched:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching note:', error);
      throw error;
    }
  },

  /**
   * Update a note
   */
  async updateNote(dealId: string, noteId: string, noteData: DealNoteUpdate): Promise<DealNote> {
    console.log(`Updating note ${noteId} for deal ${dealId}:`, noteData);
    
    try {
      const response = await apiClient.patch(`/deals/${dealId}/notes/${noteId}`, noteData);
      console.log('Note updated:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error updating note:', error);
      throw error;
    }
  },

  /**
   * Delete a note (soft delete)
   */
  async deleteNote(dealId: string, noteId: string): Promise<DealNoteDeleteResponse> {
    console.log(`Deleting note ${noteId} for deal ${dealId}`);
    
    try {
      const response = await apiClient.delete(`/deals/${dealId}/notes/${noteId}`);
      console.log('Note deleted:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error deleting note:', error);
      throw error;
    }
  },

  /**
   * Toggle pin status of a note
   */
  async togglePin(dealId: string, noteId: string, pinned: boolean): Promise<DealNote> {
    console.log(`Toggling pin status for note ${noteId} to ${pinned}`);
    
    try {
      const response = await apiClient.patch(`/deals/${dealId}/notes/${noteId}/pin`, { pinned });
      console.log('Pin status updated:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error toggling pin status:', error);
      throw error;
    }
  },

  /**
   * Get notes where current user is tagged
   */
  async getNotesByUser(skip: number = 0, limit: number = 100): Promise<DealNoteListResponse> {
    console.log(`Fetching notes where user is tagged`);
    
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });
      
      const response = await apiClient.get(`/deals/notes/tagged?${params}`);
      console.log('User tagged notes fetched:', response.data);
      
      // Map id to _id for compatibility
      const notes = (response.data.notes || []).map((note: any) => ({
        ...note,
        _id: note._id || note.id,
      }));
      
      return { ...response.data, notes };
    } catch (error: any) {
      console.error('Error fetching user tagged notes:', error);
      throw error;
    }
  },
}; 