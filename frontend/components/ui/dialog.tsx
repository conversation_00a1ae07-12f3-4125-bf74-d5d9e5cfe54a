"use client"

import * as React from "react"
import {
  Root as Dialog,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON>rigger,
  Portal as DialogPortal,
  Overlay as DialogOverlay,
  Content as DialogContent,
  Title as DialogTitle,
  Description as DialogDescription,
  Close as DialogClose,
} from "@radix-ui/react-dialog"
import { X } from "lucide-react"

import { cn } from "@/lib/utils"
import { visualRetreat } from "@/lib/utils/responsive-safe"

const DialogPortalComponent = ({
  children,
  ...props
}: React.ComponentPropsWithoutRef<typeof DialogPortal>) => (
  <DialogPortal {...props}>
    {children}
  </DialogPortal>
)
DialogPortalComponent.displayName = "DialogPortal"

const DialogOverlayComponent = React.forwardRef<
  React.ElementRef<typeof DialogOverlay>,
  React.ComponentPropsWithoutRef<typeof DialogOverlay>
>(({ className, ...props }, ref) => (
  <DialogOverlay
    ref={ref}
    className={cn(
      visualRetreat.modal.overlay,
      className
    )}
    {...props}
  />
))
DialogOverlayComponent.displayName = "DialogOverlay"

const DialogContentComponent = React.forwardRef<
  React.ElementRef<typeof DialogContent>,
  React.ComponentPropsWithoutRef<typeof DialogContent> & {
    size?: 'small' | 'default' | 'large'
  }
>(({ className, children, size = 'default', ...props }, ref) => {
  const getContentClasses = () => {
    switch (size) {
      case 'small':
        return visualRetreat.modal.contentAlert
      case 'large':
        return visualRetreat.modal.contentLarge
      default:
        return visualRetreat.modal.content
    }
  }

  return (
    <DialogPortalComponent>
      <DialogOverlayComponent />
      <DialogContent
        ref={ref}
        className={cn(
          getContentClasses(),
          "flex flex-col",
          className
        )}
        {...props}
      >
        {children}
        <DialogClose className={cn(
          visualRetreat.modal.closeButton
        )}>
          <X className="size-5" />
          <span className="sr-only">Close</span>
        </DialogClose>
      </DialogContent>
    </DialogPortalComponent>
  )
})
DialogContentComponent.displayName = "DialogContent"

const DialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      visualRetreat.modal.header,
      className
    )}
    {...props}
  />
)
DialogHeader.displayName = "DialogHeader"

const DialogBody = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      visualRetreat.modal.body,
      className
    )}
    {...props}
  />
)
DialogBody.displayName = "DialogBody"

const DialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      visualRetreat.modal.actions,
      className
    )}
    {...props}
  />
)
DialogFooter.displayName = "DialogFooter"

const DialogTitleComponent = React.forwardRef<
  React.ElementRef<typeof DialogTitle>,
  React.ComponentPropsWithoutRef<typeof DialogTitle>
>(({ className, ...props }, ref) => (
  <DialogTitle
    ref={ref}
    className={cn(
      "text-center text-xl font-semibold text-gray-900",
      className
    )}
    {...props}
  />
))
DialogTitleComponent.displayName = "DialogTitle"

const DialogDescriptionComponent = React.forwardRef<
  React.ElementRef<typeof DialogDescription>,
  React.ComponentPropsWithoutRef<typeof DialogDescription>
>(({ className, ...props }, ref) => (
  <DialogDescription
    ref={ref}
    className={cn(
      "mt-2 text-center text-base text-gray-600",
      className
    )}
    {...props}
  />
))
DialogDescriptionComponent.displayName = "DialogDescription"

export {
  Dialog,
  DialogTrigger,
  DialogContentComponent as DialogContent,
  DialogHeader,
  DialogBody,
  DialogFooter,
  DialogTitleComponent as DialogTitle,
  DialogDescriptionComponent as DialogDescription,
}
