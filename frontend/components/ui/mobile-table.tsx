"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { MoreHorizontal, ChevronRight } from "lucide-react"

// Mobile-first table that transforms into cards on mobile
interface MobileTableProps {
  data: any[]
  columns: {
    key: string
    label: string
    render?: (value: any, row: any) => React.ReactNode
    className?: string
    mobileLabel?: string // Custom label for mobile view
    hideOnMobile?: boolean // Hide this column on mobile
  }[]
  onRowClick?: (row: any) => void
  className?: string
  emptyMessage?: string
}

export function MobileTable({ 
  data, 
  columns, 
  onRowClick, 
  className,
  emptyMessage = "No data available"
}: MobileTableProps) {
  if (data.length === 0) {
    return (
      <Card className={cn("py-12 text-center", className)}>
        <CardContent>
          <p className="text-muted-foreground">{emptyMessage}</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn("w-full", className)}>
      {/* Desktop Table View */}
      <div className="hidden overflow-x-auto md:block">
        <table className="w-full">
          <thead>
            <tr className="border-b">
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={cn(
                    "px-4 py-3 text-left text-sm font-medium text-muted-foreground",
                    column.className
                  )}
                >
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.map((row, index) => (
              <tr
                key={index}
                className={cn(
                  "border-b transition-colors hover:bg-muted/50",
                  onRowClick && "cursor-pointer"
                )}
                onClick={() => onRowClick?.(row)}
              >
                {columns.map((column) => (
                  <td
                    key={column.key}
                    className={cn(
                      "px-4 py-3 text-sm",
                      column.className
                    )}
                  >
                    {column.render 
                      ? column.render(row[column.key], row)
                      : row[column.key]
                    }
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View */}
      <div className="space-y-3 md:hidden">
        {data.map((row, index) => (
          <Card
            key={index}
            className={cn(
              "transition-all duration-200",
              onRowClick && "cursor-pointer touch-manipulation hover:shadow-md active:scale-[0.99]"
            )}
            onClick={() => onRowClick?.(row)}
          >
            <CardContent className="p-4">
              <div className="space-y-3">
                {columns
                  .filter(column => !column.hideOnMobile)
                  .map((column) => {
                    const value = row[column.key]
                    const displayValue = column.render 
                      ? column.render(value, row)
                      : value

                    // Skip empty values
                    if (!displayValue && displayValue !== 0) return null

                    return (
                      <div key={column.key} className="flex items-start justify-between gap-3">
                        <span className="shrink-0 text-sm font-medium text-muted-foreground">
                          {column.mobileLabel || column.label}:
                        </span>
                        <div className="min-w-0 flex-1 text-right text-sm">
                          {displayValue}
                        </div>
                      </div>
                    )
                  })}
                
                {/* Action indicator for clickable rows */}
                {onRowClick && (
                  <div className="flex justify-end border-t pt-2">
                    <ChevronRight className="size-4 text-muted-foreground" />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

// Specialized components for common table patterns
export function StatusBadge({ 
  status, 
  variant = "secondary" 
}: { 
  status: string
  variant?: "default" | "secondary" | "destructive" | "outline"
}) {
  return status === "Active"
    ? <span className="inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-[#e0e7ef] via-[#f3f4f6] to-[#cbd5e1] text-[#22292f] text-xs font-medium shadow-sm backdrop-blur-sm border border-gray-200">Active</span>
    : <Badge variant={variant} className="text-xs">{status}</Badge>
}

export function ActionButton({ 
  onClick, 
  children,
  variant = "ghost",
  size = "sm"
}: {
  onClick: (e: React.MouseEvent) => void
  children: React.ReactNode
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
}) {
  return (
    <Button
      variant={variant}
      size={size}
      onClick={(e) => {
        e.stopPropagation() // Prevent row click
        onClick(e)
      }}
      className="touch-target"
    >
      {children}
    </Button>
  )
}

// Example usage component
export function ExampleMobileTable() {
  const sampleData = [
    {
      id: 1,
      name: "Acme Corp",
      status: "Active",
      amount: "$50,000",
      date: "2024-01-15",
      type: "Series A"
    },
    {
      id: 2,
      name: "TechStart Inc",
      status: "Pending",
      amount: "$25,000",
      date: "2024-01-10",
      type: "Seed"
    }
  ]

  const columns = [
    {
      key: "name",
      label: "Company",
      render: (value: string) => (
        <div className="font-medium">{value}</div>
      )
    },
    {
      key: "status",
      label: "Status",
      render: (value: string) => (
        <StatusBadge 
          status={value} 
          variant={value === "Active" ? "default" : "secondary"}
        />
      )
    },
    {
      key: "amount",
      label: "Amount",
      className: "text-right",
      render: (value: string) => (
        <span className="font-mono">{value}</span>
      )
    },
    {
      key: "type",
      label: "Round Type",
      mobileLabel: "Round"
    },
    {
      key: "date",
      label: "Date",
      hideOnMobile: true // Hide on mobile to save space
    },
    {
      key: "actions",
      label: "",
      render: () => (
        <ActionButton onClick={(e) => console.log("Action clicked")}>
          <MoreHorizontal className="size-4" />
        </ActionButton>
      ),
      className: "w-12"
    }
  ]

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Deals</h2>
        <Button size="sm">Add Deal</Button>
      </div>
      
      <MobileTable
        data={sampleData}
        columns={columns}
        onRowClick={(row) => console.log("Row clicked:", row)}
        emptyMessage="No deals found"
      />
    </div>
  )
}

// Export the main component as default
export default MobileTable
