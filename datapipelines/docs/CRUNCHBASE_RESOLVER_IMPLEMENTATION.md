# Crunchbase URL Resolver + BrightData Scraper Trigger Implementation

## Overview

This implementation provides a complete pipeline for automatically resolving company Crunchbase URLs and triggering BrightData scraping to collect comprehensive company data. The system is designed to be standalone but callable from other enrichment jobs.

## Architecture

### Components

1. **CrunchbaseResolverService** (`app/services/crunchbase_resolver.py`)
   - Handles Serper API search for Crunchbase URLs
   - Uses LLM (Together AI) for intelligent link selection
   - Triggers BrightData scraping jobs
   - Manages S3 storage of raw data

2. **CrunchbaseResolverTask** (`app/tasks/crunchbase_resolver.py`)
   - Main task function for job queue integration
   - Handles input validation and result storage
   - Processes BrightData company data

3. **BrightDataCompanyProcessor** (`app/tasks/brightdata_company_processor.py`)
   - Processes and cleans BrightData company data
   - Stores normalized data in database
   - Handles data quality assessment

4. **Data Models** (`app/models/crunchbase.py`)
   - Input/output schemas for resolver
   - BrightData company data structures
   - Serper search response models

## API Integration

### Required API Keys

Add the following to your environment variables:

```bash
# Serper API for Google search
SERPAPI_KEY=your_serpapi_key

# Together AI for LLM-based link selection
TOGETHER_API_KEY=your_together_api_key

# BrightData for company scraping
BRIGHTDATA_API_KEY=your_brightdata_api_key

# S3 for data storage
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
S3_BUCKET=your_s3_bucket
```

### BrightData Dataset Configuration

The system uses BrightData dataset ID: `gd_l1vijqt9jfj7olije` for Crunchbase company scraping.

## Usage

### 1. Standalone Crunchbase URL Resolution

```python
from app.tasks.crunchbase_resolver import resolve_crunchbase_url_task

# Job payload
payload = {
    "company_domain": "landscape.vc",
    "company_description": "Landscape helps venture capital firms leverage AI to uncover early stage private market companies.",
    "org_id": "org_123",
    "job_id": "job_456"
}

# Execute task
result = await resolve_crunchbase_url_task(payload)
```

### 2. Process BrightData Company Data

```python
from app.tasks.crunchbase_resolver import process_brightdata_company_data_task

# Job payload
payload = {
    "snapshot_id": "s_md4gd4z9ifb6bdpf3",
    "company_domain": "landscape.vc",
    "org_id": "org_123"
}

# Execute task
result = await process_brightdata_company_data_task(payload)
```

### 3. Direct Service Usage

```python
from app.services.crunchbase_resolver import get_crunchbase_resolver_service
from app.models.crunchbase import CrunchbaseResolverInput

# Get service
resolver_service = await get_crunchbase_resolver_service()

# Create input
input_data = CrunchbaseResolverInput(
    company_domain="landscape.vc",
    company_description="AI-powered venture capital platform",
    org_id="org_123"
)

# Resolve URL
result = await resolver_service.resolve_crunchbase_url(input_data)
```

## Data Flow

### 1. URL Resolution Pipeline

```
Input: company_domain + description
    ↓
Serper API Search: site:crunchbase.com/organization {domain}
    ↓
LLM Selection: Choose best Crunchbase URL from results
    ↓
URL Validation: Regex pattern matching
    ↓
BrightData Trigger: POST to /datasets/v3/trigger
    ↓
S3 Storage: Store raw data for audit
    ↓
Output: status, crunchbase_url, snapshot_id
```

### 2. Data Processing Pipeline

```
Input: snapshot_id + metadata
    ↓
BrightData Download: GET /datasets/v3/snapshot/{id}
    ↓
Data Cleaning: Extract and normalize fields
    ↓
Database Storage: Store in normalized tables
    ↓
S3 Backup: Store raw data
    ↓
Output: processed company data
```

## Database Schema

### Company Profiles Table

```sql
CREATE TABLE company_profiles (
    id UUID PRIMARY KEY,
    company_id VARCHAR NOT NULL,
    org_id VARCHAR NOT NULL,
    name VARCHAR,
    description TEXT,
    website VARCHAR,
    domain VARCHAR,
    industry VARCHAR,
    sub_industry VARCHAR,
    stage VARCHAR,
    founded_year INTEGER,
    employee_count INTEGER,
    employee_count_range VARCHAR,
    headquarters VARCHAR,
    country VARCHAR,
    city VARCHAR,
    state VARCHAR,
    funding_total DECIMAL,
    funding_rounds INTEGER,
    last_funding_date DATE,
    last_funding_amount DECIMAL,
    valuation DECIMAL,
    revenue DECIMAL,
    linkedin_url VARCHAR,
    twitter_url VARCHAR,
    facebook_url VARCHAR,
    email VARCHAR,
    phone VARCHAR,
    crunchbase_url VARCHAR,
    brightdata_snapshot_id VARCHAR,
    s3_raw_data_key VARCHAR,
    source VARCHAR DEFAULT 'brightdata_crunchbase',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### Related Tables

- `company_founders`: Company founder information
- `company_executives`: Company executive information
- `company_technologies`: Technologies used by company
- `company_keywords`: Company keywords/tags
- `company_competitors`: Competitor information
- `processing_errors`: Error tracking for debugging

## Error Handling

### Error Types

1. **Serper Search Failures**
   - API rate limits
   - Network connectivity issues
   - Invalid search queries

2. **LLM Selection Failures**
   - Together AI API issues
   - Invalid response formats
   - Timeout errors

3. **BrightData Failures**
   - Dataset configuration issues
   - Snapshot processing failures
   - Data download errors

4. **Data Processing Failures**
   - Invalid data formats
   - Database connection issues
   - S3 storage failures

### Error Recovery

- Automatic retries with exponential backoff
- Fallback to "NO_MATCH" for uncertain cases
- Comprehensive error logging and S3 storage
- Error tracking in database for debugging

## Monitoring and Observability

### Logging

The system provides structured logging with the following tags:
- `resolver`: Crunchbase resolver operations
- `source=serper`: Serper API operations
- `agent=llama3`: LLM operations
- `validated=true|false`: URL validation status

### Metrics

Key metrics tracked:
- Processing time per stage
- Success/failure rates
- Data quality scores
- API response times

### S3 Storage

Raw data is stored in S3 with the following structure:
```
s3://bucket/crunchbase_resolver/{org_id}/{company_domain}/{timestamp}_{snapshot_id}.json
```

## Integration with Existing Pipelines

### Company Enrichment Integration

The Crunchbase resolver can be integrated into the existing company enrichment pipeline:

```python
# In company_enrichment.py
async def enrich_company_data(payload: Dict[str, Any]) -> Dict[str, Any]:
    # ... existing enrichment logic ...
    
    # Check if Crunchbase URL is needed
    if not company_data.get("crunchbase_url"):
        crunchbase_result = await resolve_crunchbase_url_task({
            "company_domain": company_data["domain"],
            "company_description": company_data.get("description"),
            "org_id": payload["org_id"],
            "job_id": payload["job_id"]
        })
        
        if crunchbase_result["success"]:
            # Process BrightData company data
            brightdata_result = await process_brightdata_company_data_task({
                "snapshot_id": crunchbase_result["data"]["brightdata_snapshot_id"],
                "company_domain": company_data["domain"],
                "org_id": payload["org_id"]
            })
            
            # Merge results
            if brightdata_result["success"]:
                company_data.update(brightdata_result["data"])
    
    # ... continue with existing logic ...
```

## Testing

### Unit Tests

```python
# Test URL validation
def test_valid_crunchbase_url():
    service = CrunchbaseResolverService()
    assert service._is_valid_crunchbase_url("https://www.crunchbase.com/organization/landscape-5806")
    assert service._is_valid_crunchbase_url("NO_MATCH")
    assert not service._is_valid_crunchbase_url("https://example.com")

# Test data cleaning
def test_clean_string():
    processor = BrightDataCompanyProcessor(None, None)
    assert processor._clean_string("  test  ") == "test"
    assert processor._clean_string(None) is None
    assert processor._clean_string("") is None
```

### Integration Tests

```python
# Test complete pipeline
async def test_crunchbase_resolution_pipeline():
    payload = {
        "company_domain": "test-company.com",
        "company_description": "Test company description",
        "org_id": "test_org"
    }
    
    result = await resolve_crunchbase_url_task(payload)
    assert result["success"] in [True, False]
    assert "status" in result
    assert "data" in result
```

## Performance Considerations

### Rate Limiting

- Serper API: 100 requests/minute
- Together AI: 3000 requests/minute
- BrightData: 100 requests/minute

### Caching

Consider implementing caching for:
- Resolved Crunchbase URLs
- Serper search results
- Processed company data

### Batch Processing

For high-volume scenarios, consider:
- Batch URL resolution
- Parallel processing
- Queue-based processing

## Security Considerations

### API Key Management

- Store API keys in environment variables
- Use AWS Secrets Manager for production
- Rotate keys regularly

### Data Privacy

- Sanitize company data before storage
- Implement data retention policies
- Audit data access

### Access Control

- Multi-tenant isolation via org_id
- Role-based access control
- API rate limiting per organization

## Future Enhancements

### Planned Features

1. **Crunchbase API Integration**
   - Direct API access for better accuracy
   - Hybrid resolver (API + Serper)

2. **Advanced LLM Prompts**
   - Domain-specific prompting
   - Confidence scoring
   - Multi-step reasoning

3. **Data Quality Improvements**
   - Automated data validation
   - Duplicate detection
   - Data enrichment from multiple sources

4. **Real-time Processing**
   - Webhook-based triggers
   - Real-time status updates
   - Streaming data processing

### Performance Optimizations

1. **Caching Layer**
   - Redis-based caching
   - CDN for static data
   - Database query optimization

2. **Parallel Processing**
   - Async batch processing
   - Worker pool management
   - Load balancing

3. **Monitoring Enhancements**
   - Real-time dashboards
   - Alert systems
   - Performance metrics

## Troubleshooting

### Common Issues

1. **"Serper client not initialized"**
   - Check SERPAPI_KEY environment variable
   - Verify API key validity
   - Check network connectivity

2. **"LLM selection failed"**
   - Verify TOGETHER_API_KEY
   - Check API quota limits
   - Review prompt format

3. **"BrightData trigger failed"**
   - Verify BRIGHTDATA_API_KEY
   - Check dataset configuration
   - Review URL format

4. **"S3 storage failed"**
   - Verify AWS credentials
   - Check S3 bucket permissions
   - Review bucket configuration

### Debug Mode

Enable debug logging by setting:
```bash
LOG_LEVEL=DEBUG
```

This will provide detailed logs for troubleshooting API calls and data processing steps.

## Support

For issues and questions:
1. Check the logs for error details
2. Review S3 stored raw data
3. Verify API key configurations
4. Contact the data engineering team 