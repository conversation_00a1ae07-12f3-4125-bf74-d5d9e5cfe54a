from typing import Annotated, Any

import tldextract  # type: ignore
from bson import ObjectId
from pydantic import GetJsonSchemaHandler
from pydantic_core import CoreSchema, core_schema


class PyObjectId(ObjectId):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)

    @classmethod
    def __get_pydantic_core_schema__(
        cls,
        _source_type: Any,
        _handler: GetJsonSchemaHandler,
    ) -> CoreSchema:
        return core_schema.json_or_python_schema(
            json_schema=core_schema.str_schema(),
            python_schema=core_schema.union_schema([
                core_schema.is_instance_schema(ObjectId),
                core_schema.chain_schema([
                    core_schema.str_schema(),
                    core_schema.no_info_plain_validator_function(cls.validate),
                ]),
            ]),
            serialization=core_schema.plain_serializer_function_ser_schema(
                lambda x: str(x),
                return_schema=core_schema.str_schema(),
                when_used="json",
            ),
        )

    def __str__(self):
        return super().__str__()

    def __repr__(self):
        return f"ObjectId('{str(self)}')"

    def __eq__(self, other):
        if isinstance(other, ObjectId):
            return str(self) == str(other)
        return False

    def __hash__(self):
        return hash(str(self))


ObjectIdField = Annotated[PyObjectId, PyObjectId]

# ObjectIdField = Annotated[PyObjectId, Field(default_factory=PyObjectId)]


def get_root_domain(url_or_host: str) -> str:
    """
    Extracts the root domain from a full URL or hostname.
    Examples:
      - https://www.test.com → test.com
      - www.test.com         → test.com
      - v1.staging.foo.dev   → foo.dev
    """
    if not url_or_host:
        return ""

    extracted = tldextract.extract(url_or_host.strip().lower())
    if not extracted.domain or not extracted.suffix:
        return ""

    return f"{extracted.domain}.{extracted.suffix}"
