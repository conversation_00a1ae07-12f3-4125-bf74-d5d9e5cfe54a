"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { PitchUploadAPI } from '@/lib/api/pitch-upload-api';

export function PitchUploadTest() {
  const [isUploading, setIsUploading] = React.useState(false);
  const [progress, setProgress] = React.useState(0);
  const [status, setStatus] = React.useState<string>('idle');
  const [error, setError] = React.useState<string | null>(null);

  const handleTestUpload = async () => {
    setIsUploading(true);
    setError(null);
    setProgress(0);
    setStatus('preparing');

    try {
      // Create a test PDF file (this is just for testing)
      const testFile = new File(['test pdf content'], 'test-pitch.pdf', {
        type: 'application/pdf',
      });

      const token = localStorage.getItem('token');
      const orgId = localStorage.getItem('orgId');

      if (!token || !orgId) {
        throw new Error('Please log in first');
      }

      console.log('Testing pitch upload with:', {
        filename: testFile.name,
        size: testFile.size,
        type: testFile.type,
        token: token ? 'present' : 'missing',
        orgId: orgId ? 'present' : 'missing'
      });

      const result = await PitchUploadAPI.uploadPitchComplete(
        testFile,
        token,
        orgId,
        (progress, status) => {
          console.log('Upload progress:', { progress, status });
          setProgress(progress);
          setStatus(status);
        }
      );

      console.log('Upload completed:', result);
      setStatus('completed');
      
    } catch (err: any) {
      console.error('Upload test failed:', err);
      setError(err.message || 'Upload failed');
      setStatus('error');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-4 rounded-lg border p-4">
      <h3 className="text-lg font-semibold">Pitch Upload Test</h3>
      
      <div className="space-y-2">
        <p className="text-sm text-gray-600">
          Status: <span className="font-medium">{status}</span>
        </p>
        {progress > 0 && (
          <div className="h-2 w-full rounded-full bg-gray-200">
            <div 
              className="h-2 rounded-full bg-blue-600 transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        )}
        {error && (
          <p className="text-sm text-red-600">{error}</p>
        )}
      </div>

      <Button 
        onClick={handleTestUpload} 
        disabled={isUploading}
        variant="outline"
      >
        {isUploading ? 'Testing Upload...' : 'Test Pitch Upload'}
      </Button>
    </div>
  );
} 