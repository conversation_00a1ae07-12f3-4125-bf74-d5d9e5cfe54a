from typing import Any, Dict, List, Optional

from pydantic import Field, field_validator

from app.models.base import TractionXModel
from app.schemas.sharing import EmbedType, SharingType
from app.utils.common import ObjectIdField, PyObjectId


class SharingConfig(TractionXModel):
    """
    Represents a sharing configuration for a resource.

    This model defines how a resource can be shared, including:
    - What types of sharing are enabled (link, embed, QR code)
    - Domain restrictions for embeds
    - Styling options
    - Expiration settings
    - Tracking options
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    resource_type: str = Field(
        ..., description="Type of resource (e.g., 'form', 'report')"
    )
    resource_id: ObjectIdField = Field(..., description="ID of the resource")
    org_id: ObjectIdField = Field(..., description="Organization ID")
    enabled: bool = Field(default=True, description="Whether sharing is enabled")
    sharing_types: List[str] = Field(
        default_factory=lambda: [SharingType.LINK.value],
        description="List of sharing types enabled",
    )
    access_token: str = Field(
        ..., description="Access token for the sharing configuration"
    )
    expires_at: Optional[int] = Field(
        default=None,
        description="When the sharing configuration expires (unix timestamp)",
    )
    allowed_domains: List[str] = Field(
        default_factory=list, description="List of domains allowed for embedding"
    )
    embed_type: str = Field(
        default=EmbedType.INLINE.value, description="Default type of embed"
    )
    custom_styles: Dict[str, Any] = Field(
        default_factory=dict, description="Custom styles for embedding"
    )
    tracking_enabled: bool = Field(
        default=True, description="Whether to track views/usage"
    )
    created_at: int = Field(..., description="Creation timestamp (ms)")
    updated_at: int = Field(..., description="Last update timestamp (ms)")

    @field_validator("sharing_types")
    @classmethod
    def validate_sharing_types(cls, v: List[str]) -> List[str]:
        """Validate sharing types are valid."""
        valid_types = {t.value for t in SharingType}
        for sharing_type in v:
            if sharing_type not in valid_types:
                raise ValueError(f"Invalid sharing type: {sharing_type}")
        return v

    @field_validator("embed_type")
    @classmethod
    def validate_embed_type(cls, v: str) -> str:
        """Validate embed type is valid."""
        valid_types = {t.value for t in EmbedType}
        if v not in valid_types:
            raise ValueError(f"Invalid embed type: {v}")
        return v


class SharingLink(TractionXModel):
    """
    Represents a sharing link for a resource.

    This model defines a link that can be used to access a shared resource,
    including tracking information and expiration settings.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    sharing_config_id: Optional[ObjectIdField] = Field(
        default=None, description="ID of the sharing configuration"
    )
    resource_type: str = Field(..., description="Type of resource")
    resource_id: ObjectIdField = Field(..., description="ID of the resource")
    org_id: ObjectIdField = Field(..., description="Organization ID")
    type: str = Field(..., description="Type of sharing link")
    token: str = Field(..., description="Unique token for the sharing link")
    short_code: str = Field(..., description="Short code for the sharing link")
    expires_at: Optional[int] = Field(
        default=None, description="When the link expires (unix timestamp)"
    )
    is_active: bool = Field(default=True, description="Whether the link is active")
    created_by: ObjectIdField = Field(
        ..., description="ID of the user who created the link"
    )
    views: int = Field(default=0, description="Number of views")
    last_viewed_at: Optional[int] = Field(
        default=None, description="When the link was last viewed (unix timestamp)"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata for the link"
    )
    created_at: int = Field(..., description="Creation timestamp (ms)")
    updated_at: int = Field(..., description="Last update timestamp (ms)")

    # These fields are computed at runtime and not stored in the database
    url: Optional[str] = None
    short_url: Optional[str] = None


class EmbedCode(TractionXModel):
    """
    Represents an embed code for a resource.

    This model defines an embed code that can be used to embed a shared resource
    in a website, including styling options and domain restrictions.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    sharing_config_id: ObjectIdField = Field(
        ..., description="ID of the sharing configuration"
    )
    resource_type: str = Field(..., description="Type of resource")
    resource_id: ObjectIdField = Field(..., description="ID of the resource")
    org_id: ObjectIdField = Field(..., description="Organization ID")
    token: str = Field(..., description="Unique token for the embed code")
    embed_type: str = Field(..., description="Type of embed")
    allowed_domains: List[str] = Field(
        default_factory=list, description="List of domains allowed for embedding"
    )
    custom_styles: Dict[str, Any] = Field(
        default_factory=dict, description="Custom styles for embedding"
    )
    is_active: bool = Field(
        default=True, description="Whether the embed code is active"
    )
    created_by: ObjectIdField = Field(
        ..., description="ID of the user who created the embed code"
    )
    views: int = Field(default=0, description="Number of views")
    last_viewed_at: Optional[int] = Field(
        default=None, description="When the embed code was last viewed (unix timestamp)"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata for the embed code"
    )
    created_at: int = Field(..., description="Creation timestamp (ms)")
    updated_at: int = Field(..., description="Last update timestamp (ms)")

    # This field is computed at runtime and not stored in the database
    html: Optional[str] = None

    @field_validator("embed_type")
    @classmethod
    def validate_embed_type(cls, v: str) -> str:
        """Validate embed type is valid."""
        valid_types = {t.value for t in EmbedType}
        if v not in valid_types:
            raise ValueError(f"Invalid embed type: {v}")
        return v


class QRCode(TractionXModel):
    """
    Represents a QR code for a resource.

    This model defines a QR code that can be used to access a shared resource,
    including styling options and tracking information.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    sharing_config_id: ObjectIdField = Field(
        ..., description="ID of the sharing configuration"
    )
    resource_type: str = Field(..., description="Type of resource")
    resource_id: ObjectIdField = Field(..., description="ID of the resource")
    org_id: ObjectIdField = Field(..., description="Organization ID")
    token: str = Field(..., description="Unique token for the QR code")
    size: int = Field(default=300, description="Size of the QR code in pixels")
    error_correction_level: str = Field(
        default="M", description="Error correction level (L, M, Q, H)"
    )
    is_active: bool = Field(default=True, description="Whether the QR code is active")
    created_by: ObjectIdField = Field(
        ..., description="ID of the user who created the QR code"
    )
    scans: int = Field(default=0, description="Number of scans")
    last_scanned_at: Optional[int] = Field(
        default=None, description="When the QR code was last scanned (unix timestamp)"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata for the QR code"
    )
    created_at: int = Field(..., description="Creation timestamp (ms)")
    updated_at: int = Field(..., description="Last update timestamp (ms)")

    # These fields are computed at runtime and not stored in the database
    image_data: Optional[str] = None
    url: Optional[str] = None

    @field_validator("error_correction_level")
    @classmethod
    def validate_error_correction_level(cls, v: str) -> str:
        """Validate error correction level is valid."""
        valid_levels = {"L", "M", "Q", "H"}
        if v not in valid_levels:
            raise ValueError(f"Invalid error correction level: {v}")
        return v


class SharingView(TractionXModel):
    """
    Represents a view of a shared resource.

    This model tracks when a shared resource was viewed, including
    metadata about the view such as referrer and user agent.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    sharing_id: ObjectIdField = Field(
        ..., description="ID of the sharing item (link, embed, QR code)"
    )
    sharing_type: str = Field(..., description="Type of sharing (link, embed, qr_code)")
    timestamp: int = Field(..., description="When the view occurred (unix timestamp)")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata for the view"
    )
    updated_at: int = Field(..., description="Last update timestamp (ms)")
    created_at: int = Field(..., description="Creation timestamp (ms)")

    @field_validator("sharing_type")
    @classmethod
    def validate_sharing_type(cls, v: str) -> str:
        """Validate sharing type is valid."""
        valid_types = {t.value for t in SharingType}
        if v not in valid_types:
            raise ValueError(f"Invalid sharing type: {v}")
        return v
