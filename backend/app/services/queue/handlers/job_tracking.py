"""
Job tracking handlers.

This module contains handlers for job tracking, including AI processing jobs.
"""

from typing import Any, Dict, Optional

from app.core.logging import get_logger
from app.models.job import JobStatus
from app.models.queue import Job
from app.services.factory import get_job_service
from app.services.queue.handlers.base import BaseJobHandler

logger = get_logger(__name__)


class AIProcessingHandler(BaseJobHandler):
    """Handler for AI processing jobs."""

    def __init__(self):
        """Initialize the handler."""
        super().__init__()
        # self.job_service = JobService()

    async def _initialize_services(self) -> None:
        """Initialize services for the handler."""
        from app.services.job.mongo import JobService

        self.job_service: JobService = await get_job_service()  # type: ignore

    async def process(self, job: Job) -> Optional[Dict[str, Any]]:
        """
        Process an AI processing job.

        Args:
            job: The job to process

        Returns:
            Processing result
        """
        payload = job.payload
        self.logger.info(f"Processing AI job: {job.id}")

        # Extract data from payload
        tracked_job_id = payload.get("tracked_job_id")
        if not tracked_job_id:
            self.logger.error("Missing tracked_job_id in payload")
            raise ValueError("Missing tracked_job_id in payload")

        # Initialize job service
        await self.job_service.initialize()

        # Update job status to in progress
        await self.job_service.update_job_status(
            job_id=tracked_job_id, status=JobStatus.IN_PROGRESS, progress=0.1
        )

        try:
            # Extract job parameters
            entity_type = payload.get("entity_type")
            entity_id = payload.get("entity_id")
            job_type = payload.get("job_type")
            # input_data = payload.get("input_data", {})

            self.logger.info(f"Processing {job_type} for {entity_type} {entity_id}")

            # Simulate AI processing
            # In a real implementation, this would call an AI service
            # For now, we'll just simulate progress updates and a result
            await self.job_service.update_job_status(
                job_id=tracked_job_id, status=JobStatus.IN_PROGRESS, progress=0.5
            )

            # Simulate AI result
            ai_result = {
                "summary": f"This is a simulated AI summary for {entity_type} {entity_id}",
                "analysis": "The analysis shows positive sentiment and high relevance.",
                "score": 0.85,
            }

            # Update job with result
            await self.job_service.update_job_status(
                job_id=tracked_job_id,
                status=JobStatus.COMPLETED,
                progress=1.0,
                output=ai_result,
                metadata={"model": "gpt-4o", "tokens_used": 1234},
            )

            self.logger.info(f"Completed AI processing for job {tracked_job_id}")
            return {"success": True, "result": ai_result}

        except Exception as e:
            self.logger.error(f"Error processing AI job: {str(e)}", exc_info=True)

            # Update job status to failed
            await self.job_service.update_job_status(
                job_id=tracked_job_id, status=JobStatus.FAILED, error=str(e)
            )

            raise


# Legacy function-based handler for backward compatibility
async def process_ai_summary(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process an AI summary job.

    Args:
        payload: Job payload containing job data

    Returns:
        Processing result
    """
    handler = AIProcessingHandler()
    job = Job(id="legacy", type="ai_summary_job", payload=payload)

    try:
        result = await handler.process(job)
        return result or {"success": True}
    except Exception as e:
        logger.error(f"Error processing AI summary: {str(e)}")
        return {"success": False, "error": str(e)}


# Register handlers
HANDLERS = {
    "ai_summary_job": process_ai_summary,
    "ai_analysis_job": process_ai_summary,  # Reuse the same handler for now
}
