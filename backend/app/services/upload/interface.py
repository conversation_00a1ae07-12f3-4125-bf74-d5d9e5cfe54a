from abc import ABC, abstractmethod
from typing import Dict, Any


class IUploadService(ABC):
    """Interface for upload service operations."""

    @abstractmethod
    async def generate_presigned_url(
        self,
        filename: str,
        file_type: str,
        file_size: int,
        upload_type: str = "general"
    ) -> Dict[str, Any]:
        """Generate a presigned URL for S3 upload."""
        pass

    @abstractmethod
    async def delete_file(self, file_url: str) -> bool:
        """Delete a file from S3."""
        pass

    @abstractmethod
    async def validate_file(
        self,
        filename: str,
        file_type: str,
        file_size: int,
        upload_type: str = "general"
    ) -> bool:
        """Validate file before upload."""
        pass
