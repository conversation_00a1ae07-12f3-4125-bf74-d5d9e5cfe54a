import * as React from "react"

import { cn } from "@/lib/utils"

interface DashboardShellProps extends React.HTMLAttributes<HTMLDivElement> {}

export function DashboardShell({
  children,
  className,
  ...props
}: DashboardShellProps) {
  return (
    <div className={cn("w-full space-y-8", className)} {...props}>
      {children}
    </div>
  )
}

// Export Shell as an alias for DashboardShell for backward compatibility
export const Shell = DashboardShell;
