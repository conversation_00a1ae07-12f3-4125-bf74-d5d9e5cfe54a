"""
MongoDB database connection and utilities for TractionX AI Agent system.
Follows the same patterns as the backend database module.
"""

from typing import AsyncGenerator
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from ai.config import settings
import logging

logger = logging.getLogger(__name__)


class AIDatabase:
    """AI Agent database connection manager."""

    client: AsyncIOMotorClient = None
    db: AsyncIOMotorDatabase = None

    async def connect_to_database(self) -> None:
        """Create database connection."""
        try:
            self.client = AsyncIOMotorClient(settings.mongodb_connection_string)
            self.db = self.client[settings.MONGODB_DB_NAME]

            # Test connection
            await self.client.admin.command('ping')
            logger.info(f"Connected to MongoDB: {settings.MONGODB_DB_NAME}")

        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise

    async def close_database_connection(self) -> None:
        """Close database connection."""
        if self.client is not None:
            self.client.close()
            logger.info("MongoDB connection closed")

    async def get_database(self) -> AsyncGenerator[AsyncIOMotorDatabase, None]:
        """Get database instance."""
        if self.db is None:
            await self.connect_to_database()
        yield self.db

    async def health_check(self) -> bool:
        """Check database health."""
        try:
            if self.db is None:
                return False
            await self.client.admin.command('ping')
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False


# Global database instance
ai_db = AIDatabase()


async def get_ai_database() -> AsyncGenerator[AsyncIOMotorDatabase, None]:
    """FastAPI dependency that provides an AI database session."""
    async for database in ai_db.get_database():
        yield database


async def ensure_indexes() -> None:
    """Ensure required database indexes exist."""
    try:
        db = None
        async for database in get_ai_database():
            db = database
            break

        if db is None:
            logger.error("Could not get database connection for index creation")
            return

        # Agent indexes
        await db.agents.create_index([("org_id", 1), ("user_id", 1)])
        await db.agents.create_index([("org_id", 1), ("type", 1)])
        await db.agents.create_index([("org_id", 1), ("visibility", 1)])
        await db.agents.create_index("created_at")

        # Agent logs indexes
        await db.agent_logs.create_index([("org_id", 1), ("user_id", 1)])
        await db.agent_logs.create_index([("agent_id", 1), ("timestamp", -1)])
        await db.agent_logs.create_index([("org_id", 1), ("timestamp", -1)])
        await db.agent_logs.create_index("timestamp")

        logger.info("Database indexes created successfully")

    except Exception as e:
        logger.error(f"Failed to create database indexes: {e}")
        raise
