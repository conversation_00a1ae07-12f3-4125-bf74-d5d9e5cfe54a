# Retry Merge and Process Utility

This utility allows you to retry a `merge_comprehensive_enrichment_results` job and automatically trigger the `process_comprehensive_enrichment_data_task` without re-running all the scraping tasks.

## Usage

### 1. CLI Script

Run the standalone CLI script:

```bash
# From the datapipelines directory
cd datapipelines

# Run the script with a job ID
python scripts/retry_merge_and_process.py merge_comprehensive_enrichment_results:1234567890

# Or with custom Redis URL
REDIS_URL=redis://your-redis-host:6379/0 python scripts/retry_merge_and_process.py merge_comprehensive_enrichment_results:1234567890
```

### 2. Python Function

Import and use the utility function directly:

```python
import asyncio
from app.util.retry_merge_and_process import retry_merge_and_process

async def main():
    job_id = "merge_comprehensive_enrichment_results:1234567890"
    redis_url = "redis://localhost:6379/0"
    
    result = await retry_merge_and_process(job_id, redis_url)
    
    if result.get("success"):
        print("✅ Successfully retried merge and process")
        print(f"Job ID: {result['job_id']}")
        print(f"Merge success: {result['merge_result'].get('success', False)}")
        print(f"Process success: {result['process_result'].get('success', False)}")
    else:
        print("❌ Failed to retry merge and process")
        print(f"Error: {result.get('error', 'Unknown error')}")

# Run the function
asyncio.run(main())
```

### 3. Integration with retry_job Endpoint

To integrate this with your retry_job endpoint, you can modify the retry logic to check for merge jobs:

```python
async def retry_job_endpoint(job_id: str):
    # Get job details
    job_status = await queue_service.get_job_status(job_id)
    
    if job_status and isinstance(queue_service.backend, RedisQueueBackend):
        try:
            job_data = await queue_service.backend._get_job_data(job_id)
            job_func = job_data.get("job_func") if job_data else None
            
            # Special handling for merge jobs
            if job_func == "merge_comprehensive_enrichment_results":
                from app.util.retry_merge_and_process import retry_merge_and_process
                
                redis_url = getattr(queue_service.backend, 'redis_url', 'redis://localhost:6379/0')
                result = await retry_merge_and_process(job_id, redis_url)
                
                if result.get("success"):
                    return {"status": "success", "message": "Merge and process retry successful"}
                else:
                    return {"status": "error", "message": f"Merge and process retry failed: {result.get('error')}"}
        except Exception as e:
            logger.warning(f"Error in special retry handling: {e}")
    
    # Fall back to normal retry
    success = await queue_service.retry_job(job_id)
    return {"status": "success" if success else "error", "message": "Job retried"}
```

## What it does

1. **Fetches original job data**: Retrieves the original job arguments from Redis
2. **Re-runs merge operation**: Calls `merge_comprehensive_enrichment_results` with the original arguments
3. **Triggers processing**: Automatically calls `process_comprehensive_enrichment_data_task` with the merged data
4. **Returns results**: Provides detailed success/failure information for both operations

## Benefits

- **No re-scraping**: Avoids re-running LinkedIn, Crunchbase, Apollo, and other scraping tasks
- **Faster recovery**: Only re-processes the merge and downstream tasks
- **Data preservation**: Uses the same scraped data that was already collected
- **Error isolation**: Can retry just the merge/process steps when they fail

## Error Handling

The utility includes comprehensive error handling:

- Validates job existence
- Checks merge operation success
- Verifies enrichment data availability
- Provides detailed error messages
- Cleans up resources properly

## Example Output

```
🔄 Retrying merge and process for job ID: merge_comprehensive_enrichment_results:1234567890
📡 Using Redis URL: redis://localhost:6379/0
--------------------------------------------------
✅ Successfully retried merge and process
📋 Job ID: merge_comprehensive_enrichment_results:1234567890
🔗 Merge success: True
⚙️  Process success: True
📊 Data summary: LinkedIn=True, Crunchbase=False, Apollo=True, Website Insights=1
💬 Process message: Comprehensive enrichment data processed successfully
``` 