/**
 * Form validation utilities for question answers
 */

export interface ValidationRule {
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: string;
  minLength?: number;
  maxLength?: number;
  step?: number;
}

export interface Question {
  _id: string;
  type: string;
  label: string;
  help_text?: string;
  required: boolean;
  validation?: ValidationRule;
  options?: Array<{ value: string; label: string }>;
}

export function validateAnswer(question: Question, value: any): string | null {
  // Check if required field is empty
  if (question.required && (value === null || value === undefined || value === '')) {
    return `${question.label} is required`;
  }

  // If value is empty and not required, it's valid
  if (!value && !question.required) {
    return null;
  }

  const validation = question.validation;
  if (!validation) return null;

  const questionType = question.type.toLowerCase();

  switch (questionType) {
    case 'short_text':
    case 'long_text':
      if (typeof value !== 'string') return null;
      
      if (validation.minLength && value.length < validation.minLength) {
        return `${question.label} must be at least ${validation.minLength} characters`;
      }
      
      if (validation.maxLength && value.length > validation.maxLength) {
        return `${question.label} must be no more than ${validation.maxLength} characters`;
      }
      
      if (validation.pattern) {
        const regex = new RegExp(validation.pattern);
        if (!regex.test(value)) {
          return `${question.label} format is invalid`;
        }
      }
      break;

    case 'number':
    case 'range':
      const numValue = Number(value);
      
      if (isNaN(numValue)) {
        return `${question.label} must be a valid number`;
      }
      
      if (validation.min !== undefined && numValue < validation.min) {
        return `${question.label} must be at least ${validation.min}`;
      }
      
      if (validation.max !== undefined && numValue > validation.max) {
        return `${question.label} must be no more than ${validation.max}`;
      }
      
      if (validation.step && numValue % validation.step !== 0) {
        return `${question.label} must be a multiple of ${validation.step}`;
      }
      break;

    case 'single_select':
      if (!question.options?.some(option => option.value === value)) {
        return `${question.label} must be one of the available options`;
      }
      break;

    case 'multi_select':
      if (!Array.isArray(value)) {
        return `${question.label} must be an array of values`;
      }
      
      const invalidOptions = value.filter(v => 
        !question.options?.some(option => option.value === v)
      );
      
      if (invalidOptions.length > 0) {
        return `${question.label} contains invalid options`;
      }
      
      if (validation.min && value.length < validation.min) {
        return `${question.label} must have at least ${validation.min} selections`;
      }
      
      if (validation.max && value.length > validation.max) {
        return `${question.label} must have no more than ${validation.max} selections`;
      }
      break;

    case 'boolean':
      if (typeof value !== 'boolean') {
        return `${question.label} must be true or false`;
      }
      break;

    case 'date':
      if (value && !isValidDate(value)) {
        return `${question.label} must be a valid date`;
      }
      break;

    case 'file':
      // File validation would typically be handled during upload
      // This is just a placeholder for the filename
      break;

    default:
      // Unknown question type, skip validation
      break;
  }

  return null;
}

function isValidDate(dateString: string): boolean {
  try {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime());
  } catch {
    return false;
  }
}

export function validateAllAnswers(questions: Question[], answers: Record<string, any>): Record<string, string> {
  const errors: Record<string, string> = {};

  questions.forEach(question => {
    const value = answers[question._id];
    const error = validateAnswer(question, value);
    if (error) {
      errors[question._id] = error;
    }
  });

  return errors;
}
