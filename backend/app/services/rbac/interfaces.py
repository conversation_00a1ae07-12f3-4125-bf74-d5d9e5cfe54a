from typing import Optional, List, Dict, Tuple
from abc import ABC, abstractmethod

from app.models.role import Role, Permission


class RBACServiceInterface(ABC):
    """Interface for RBAC (Role-Based Access Control) services."""

    @abstractmethod
    async def check_permission(self, user_id: str, resource: str, action: str, request=None) -> bool:
        """Check if user has permission for a specific resource and action."""
        pass

    @abstractmethod
    async def get_user_role(self, user_id: str) -> Optional[Tuple[Role, bool]]:
        """Get user's role with permissions."""
        pass

    @abstractmethod
    async def create_role(
        self,
        name: str,
        description: str,
        permissions: List[Permission],
        is_system: bool = False
    ) -> Role:
        """Create a new role."""
        pass

    @abstractmethod
    async def update_role(
        self,
        role_id: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        permissions: Optional[List[Permission]] = None
    ) -> Optional[Role]:
        """Update an existing role."""
        pass

    @abstractmethod
    async def assign_role(self, user_id: str, role_id: str) -> bool:
        """Assign a role to a user."""
        pass

    @abstractmethod
    async def get_user_permissions(self, user_id: str) -> List[Dict]:
        """Get all permissions for a user."""
        pass

