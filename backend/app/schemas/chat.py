"""
Chat API Schemas

Pydantic models for Chat API requests and responses.
"""

from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field

from app.models.chat import MessageRole, MessageStatus, ChatMode


class ChatSourceResponse(BaseModel):
    """Schema for chat message sources/citations."""
    
    title: str = Field(..., description="Source title")
    url: str = Field(..., description="Source URL")
    snippet: Optional[str] = Field(None, description="Text snippet from source")
    domain: Optional[str] = Field(None, description="Source domain")


class ChatMessageResponse(BaseModel):
    """Schema for chat message responses."""

    id: str = Field(..., description="Message ID")
    thread_id: str = Field(..., description="Thread ID")
    role: MessageRole = Field(..., description="Message role")
    content: str = Field(..., description="Message content")
    mode: ChatMode = Field(..., description="Chat mode used")
    status: MessageStatus = Field(..., description="Message status")
    sources: List[ChatSourceResponse] = Field(
        default_factory=list,
        description="Sources/citations (mainly for Research mode)"
    )
    ai_model: Optional[str] = Field(None, description="AI model used")
    ai_provider: Optional[str] = Field(None, description="AI provider")
    response_time_ms: Optional[int] = Field(None, description="Response time in milliseconds")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    created_at: int = Field(..., description="Creation timestamp")
    updated_at: int = Field(..., description="Last update timestamp")


class ChatThreadResponse(BaseModel):
    """Schema for chat thread responses."""

    id: str = Field(..., description="Thread ID")
    deal_id: str = Field(..., description="Deal ID")
    user_id: str = Field(..., description="User ID")
    mode: ChatMode = Field(..., description="Thread mode")
    title: Optional[str] = Field(None, description="Thread title")
    message_count: int = Field(..., description="Total message count")
    last_message_at: int = Field(..., description="Last message timestamp")
    last_model_used: Optional[str] = Field(None, description="Last AI model used")
    messages: List[ChatMessageResponse] = Field(
        default_factory=list,
        description="Thread messages"
    )
    created_at: int = Field(..., description="Creation timestamp")
    updated_at: int = Field(..., description="Last update timestamp")


class SendMessageRequest(BaseModel):
    """Schema for sending a new chat message."""

    message: str = Field(..., min_length=1, max_length=4000, description="Message content")
    mode: ChatMode = Field(
        default=ChatMode.CHAT,
        description="Chat mode to use (chat, research, agent)"
    )
    agent_type: Optional[str] = Field(
        default="investment_analysis",
        description="Type of AI agent to use (legacy field)"
    )
    include_deal_context: bool = Field(
        default=True,
        description="Whether to include deal context in the prompt"
    )


class SendMessageResponse(BaseModel):
    """Schema for send message response."""
    
    message_id: str = Field(..., description="Created message ID")
    thread_id: str = Field(..., description="Thread ID")
    status: MessageStatus = Field(..., description="Message status")
    estimated_completion_time: Optional[int] = Field(
        None, description="Estimated completion time in seconds"
    )


class MessageStatusResponse(BaseModel):
    """Schema for message status polling."""
    
    message_id: str = Field(..., description="Message ID")
    status: MessageStatus = Field(..., description="Current status")
    content: Optional[str] = Field(None, description="Message content if completed")
    sources: List[ChatSourceResponse] = Field(
        default_factory=list, 
        description="Sources if completed"
    )
    error_message: Optional[str] = Field(None, description="Error message if failed")
    retry_count: int = Field(default=0, description="Number of retries attempted")
    updated_at: int = Field(..., description="Last update timestamp")


class ChatHistoryResponse(BaseModel):
    """Schema for chat history response."""
    
    thread: ChatThreadResponse = Field(..., description="Chat thread with messages")
    has_more: bool = Field(default=False, description="Whether more messages exist")
    total_messages: int = Field(..., description="Total message count")


class ChatContextRequest(BaseModel):
    """Schema for updating chat context."""
    
    deal_context: Dict[str, Any] = Field(..., description="Deal context data")


class ChatStatsResponse(BaseModel):
    """Schema for chat statistics."""
    
    total_threads: int = Field(..., description="Total threads for user")
    total_messages: int = Field(..., description="Total messages for user")
    active_threads: int = Field(..., description="Threads with recent activity")
    avg_response_time: Optional[float] = Field(
        None, description="Average AI response time in seconds"
    )
