from fastapi import Depends, HTTPException, status
from pydantic import BaseModel, Field
from typing import Optional, Any

from app.dependencies.auth import get_current_user
from app.models.user import User
from app.services.cache.interfaces import CacheServiceInterface
from app.services.factory import get_redis_service
from app.api.base import BaseAPIRouter

router = BaseAPIRouter(prefix="/cache", tags=["cache"])


class CacheSetRequest(BaseModel):
    key: str = Field(..., min_length=1, max_length=255)
    value: Any
    namespace: Optional[str] = Field(None, min_length=1, max_length=50)
    ttl: Optional[int] = Field(None, ge=0)


class CacheResponse(BaseModel):
    key: str
    value: Any
    namespace: Optional[str] = None
    ttl: Optional[int] = None


@router.get("/{key}")
async def get_cache(
    key: str,
    namespace: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    cache_service: CacheServiceInterface = Depends(get_redis_service)
):
    """Get value by key."""
    org_key = f"{current_user.org_id}:{key}"
    value = await cache_service.get(org_key, namespace)
    if value is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Key not found"
        )

    ttl = await cache_service.ttl(org_key, namespace)
    return CacheResponse(
        key=key,
        value=value,
        namespace=namespace,
        ttl=ttl
    )


@router.post("")
async def set_cache(
    request: CacheSetRequest,
    current_user: User = Depends(get_current_user),
    cache_service: CacheServiceInterface = Depends(get_redis_service)
):
    """Set value for key."""
    org_key = f"{current_user.org_id}:{request.key}"
    success = await cache_service.set(
        org_key,
        request.value,
        request.namespace,
        request.ttl
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to set cache value"
        )

    return CacheResponse(
        key=request.key,
        value=request.value,
        namespace=request.namespace,
        ttl=request.ttl
    )


@router.delete("/{key}")
async def delete_cache(
    key: str,
    namespace: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    cache_service: CacheServiceInterface = Depends(get_redis_service)
):
    """Delete key from cache."""
    org_key = f"{current_user.org_id}:{key}"
    success = await cache_service.delete(org_key, namespace)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Key not found"
        )

    return {"message": "Key deleted successfully"}


@router.get("")
async def list_cache(
    pattern: str = "*",
    namespace: Optional[str] = None,
    count: int = 100,
    current_user: User = Depends(get_current_user),
    cache_service: CacheServiceInterface = Depends(get_redis_service)
):
    """List cache keys."""
    org_pattern = f"{current_user.org_id}:{pattern}"
    keys = await cache_service.list_keys(org_pattern, namespace, count)
    clean_keys = [k.split(":", 1)[1] for k in keys]
    return {"keys": clean_keys}
