"use client"

import { useState, useRef, useCallback } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Upload, X, User, Camera, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { toast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"

interface AvatarUploaderProps {
  currentAvatar?: string
  userName?: string
  onUpload: (file: File) => Promise<string>
  onRemove?: () => Promise<void>
  className?: string
  size?: "sm" | "md" | "lg"
}

export function AvatarUploader({
  currentAvatar,
  userName = "",
  onUpload,
  onRemove,
  className,
  size = "lg"
}: AvatarUploaderProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const sizeClasses = {
    sm: "w-16 h-16",
    md: "w-24 h-24", 
    lg: "w-32 h-32"
  }

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map(n => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2)
  }

  const validateFile = (file: File) => {
    const maxSize = 5 * 1024 * 1024 // 5MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']

    if (!allowedTypes.includes(file.type)) {
      throw new Error('Please upload a JPEG, PNG, or WebP image')
    }

    if (file.size > maxSize) {
      throw new Error('File size must be less than 5MB')
    }
  }

  const handleFileSelect = useCallback(async (file: File) => {
    try {
      validateFile(file)
      setIsUploading(true)

      // Create preview
      const preview = URL.createObjectURL(file)
      setPreviewUrl(preview)

      // Upload file
      const avatarUrl = await onUpload(file)
      
      toast({
        title: "Avatar updated",
        description: "Your profile picture has been updated successfully.",
      })

      // Clean up preview
      URL.revokeObjectURL(preview)
      setPreviewUrl(null)

    } catch (error) {
      console.error('Avatar upload error:', error)
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Failed to upload avatar",
        variant: "destructive",
      })
      
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl)
        setPreviewUrl(null)
      }
    } finally {
      setIsUploading(false)
    }
  }, [onUpload, previewUrl])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }, [handleFileSelect])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const handleRemove = async () => {
    if (onRemove) {
      try {
        await onRemove()
        toast({
          title: "Avatar removed",
          description: "Your profile picture has been removed.",
        })
      } catch (error) {
        toast({
          title: "Remove failed",
          description: "Failed to remove avatar",
          variant: "destructive",
        })
      }
    }
  }

  const displayAvatar = previewUrl || currentAvatar

  return (
    <div className={cn("flex flex-col items-center space-y-4", className)}>
      {/* Avatar Display */}
      <div className="group relative">
        <Avatar className={cn(sizeClasses[size], "border-4 border-white shadow-lg dark:border-gray-800")}>
          <AvatarImage src={displayAvatar} alt={userName} />
          <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-lg font-semibold text-white">
            {getInitials(userName)}
          </AvatarFallback>
        </Avatar>

        {/* Loading Overlay */}
        <AnimatePresence>
          {isUploading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 flex items-center justify-center rounded-full bg-black/50"
            >
              <Loader2 className="size-6 animate-spin text-white" />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Hover Overlay */}
        <motion.div
          className="absolute inset-0 flex cursor-pointer items-center justify-center rounded-full bg-black/50 opacity-0 transition-opacity group-hover:opacity-100"
          onClick={() => fileInputRef.current?.click()}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Camera className="size-6 text-white" />
        </motion.div>
      </div>

      {/* Upload Area */}
      <div
        className={cn(
          "relative rounded-xl border-2 border-dashed p-6 text-center transition-all duration-200",
          isDragging 
            ? "border-blue-500 bg-blue-50 dark:bg-blue-950/20" 
            : "border-gray-300 hover:border-gray-400 dark:border-gray-600 dark:hover:border-gray-500"
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/png,image/webp"
          onChange={(e) => {
            const file = e.target.files?.[0]
            if (file) handleFileSelect(file)
          }}
          className="hidden"
          disabled={isUploading}
        />

        <div className="space-y-3">
          <Upload className="mx-auto size-8 text-gray-400" />
          <div>
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
              Drop your avatar here, or{" "}
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="font-semibold text-blue-600 hover:text-blue-500"
                disabled={isUploading}
              >
                browse
              </button>
            </p>
            <p className="mt-1 text-xs text-gray-500">
              JPEG, PNG, or WebP up to 5MB
            </p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
          className="bg-white/70 backdrop-blur-sm dark:bg-black/70"
        >
          <Upload className="mr-2 size-4" />
          Change
        </Button>
        
        {(currentAvatar || previewUrl) && onRemove && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleRemove}
            disabled={isUploading}
            className="bg-white/70 text-red-600 backdrop-blur-sm hover:text-red-700 dark:bg-black/70"
          >
            <X className="mr-2 size-4" />
            Remove
          </Button>
        )}
      </div>
    </div>
  )
}
