"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export function AIInsightCard() {
  return (
    <Card className="border-0 bg-gradient-to-br from-purple-50 to-blue-50 shadow-xl backdrop-blur-md border border-purple-100">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            {/* <div className="h-5 w-5 text-purple-600">🧠</div> */}
            <CardTitle className="text-lg font-semibold text-gray-900">
              Orbit Ai - Generated Company Insights
            </CardTitle>
          </div>
          <Badge variant="secondary" className="text-xs bg-purple-100 text-purple-700">
            Coming Soon
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="flex items-start gap-3">
          {/* <div className="h-5 w-5 text-purple-500 mt-0.5">✨</div> */}
          <div className="space-y-2">
            <p className="text-sm text-gray-700">
              We're working on surfacing strengths, risks, and investor fit analysis based on your thesis.
            </p>
            <p className="text-sm text-gray-600">
              Stay tuned for AI-powered insights that will help you make better investment decisions.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 