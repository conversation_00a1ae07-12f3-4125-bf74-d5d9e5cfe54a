"""
Pipeline API endpoints for TractionX Data Pipeline Service.
"""

import time
from typing import Any, Dict, Optional

from fastapi import APIRouter, BackgroundTasks, HTTPException
from pydantic import Field

from app.configs import get_logger, settings
from app.models.base import TractionXModel
from app.queueing.dispatcher import cancel_job as queue_cancel_job
from app.queueing.dispatcher import dispatch_job, get_queue_stats
from app.queueing.dispatcher import get_job_status as queue_get_job_status
from app.queueing.dispatcher import retry_job as queue_retry_job
from app.utils.common import get_root_domain

logger = get_logger(__name__)
router = APIRouter()


class PipelineTriggerRequest(TractionXModel):
    """Request model for triggering a pipeline."""

    company_id: str = Field(..., description="Company identifier")
    org_id: str = Field(..., description="Organization identifier")
    company_name: str = Field(..., description="Company name")
    domain: Optional[str] = Field(None, description="Company domain")
    form_data: Optional[Dict[str, Any]] = Field(
        None, description="Form submission data"
    )
    pipeline_types: list[str] = Field(
        default=["company", "founder", "news", "embedding"],
        description="Types of pipelines to run",
    )
    priority: str = Field(
        default="normal", description="Job priority (low, normal, high)"
    )
    use_comprehensive_enrichment: bool = Field(
        default=False,
        description="Use comprehensive enrichment with barrier orchestration",
    )


class PipelineStatusResponse(TractionXModel):
    """Response model for pipeline status."""

    job_id: str
    status: str
    pipeline_type: str
    created_at: Optional[str] = None
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    progress: Optional[float] = None
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None


class JobRetryRequest(TractionXModel):
    """Request model for retrying a job."""

    delay_seconds: int = Field(
        default=0, description="Delay in seconds before retrying the job"
    )


@router.post("/trigger")
async def trigger_pipeline(
    request: PipelineTriggerRequest, background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """
    Trigger a complete data pipeline for a company.

    This endpoint triggers all specified pipeline types for enriching
    company data from various sources. If use_comprehensive_enrichment is True,
    it uses barrier orchestration for coordinated enrichment.
    """
    try:
        logger.info(
            "Triggering pipeline",
            company_id=request.company_id,
            company_name=request.company_name,
            pipeline_types=request.pipeline_types,
            use_comprehensive_enrichment=request.use_comprehensive_enrichment,
        )

        # If comprehensive enrichment is requested, use the barrier-based approach
        if request.use_comprehensive_enrichment:
            return await _trigger_comprehensive_enrichment(request)

        # Otherwise, use the traditional pipeline approach
        return await _trigger_traditional_pipeline(request)

    except Exception as e:
        logger.error(f"Failed to trigger pipeline: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


async def _trigger_comprehensive_enrichment(
    request: PipelineTriggerRequest,
) -> Dict[str, Any]:
    """Trigger comprehensive enrichment using barrier orchestration."""

    # Use get_root_domain to extract clean domain
    company_domain = get_root_domain(request.domain or request.company_id)
    if not company_domain:
        raise ValueError("Could not extract valid domain from company_id or domain")

    # Prepare job data for comprehensive enrichment
    job_data = {
        "company_domain": company_domain,
        "org_id": request.org_id,
        "company_description": request.company_name,
        "job_id": f"comprehensive_{int(time.time())}",
    }

    # Dispatch the comprehensive enrichment job
    job_id = await dispatch_job(
        job_func="enrich_company_data_comprehensive",
        job_args=job_data,
        priority=request.priority,
    )

    logger.info(
        "Queued comprehensive enrichment job",
        job_id=job_id,
        company_domain=company_domain,
    )

    return {
        "success": True,
        "message": "Comprehensive enrichment pipeline triggered successfully",
        "company_id": request.company_id,
        "job_id": job_id,
        "company_domain": company_domain,
        "org_id": request.org_id,
        "pipeline_type": "comprehensive",
        "barrier_group_id": f"company:{request.org_id}:{company_domain}:{int(time.time())}",
    }


async def _trigger_traditional_pipeline(
    request: PipelineTriggerRequest,
) -> Dict[str, Any]:
    """Trigger traditional pipeline with individual job types."""

        # Queue jobs for each pipeline type
    job_ids = {}

    for pipeline_type in request.pipeline_types:
        job_data = {
            "company_id": get_root_domain(request.company_id),
            "org_id": request.org_id,
            "company_name": request.company_name,
            "domain": request.domain,
            "form_data": request.form_data,
            "pipeline_type": pipeline_type,
        }

        # Queue appropriate job based on pipeline type
        job_func = None
        if pipeline_type == "company":
            job_func = "enrich_company_data"
        elif pipeline_type == "founder":
            job_func = "enrich_founder_data"
        elif pipeline_type == "news":
            job_func = "aggregate_news_data"
        elif pipeline_type == "embedding":
            job_func = "generate_embeddings"
        else:
            logger.warning(f"Unknown pipeline type: {pipeline_type}")
            continue

        # Dispatch the job using the new queue system
        job_id = await dispatch_job(
            job_func=job_func,
            job_args=job_data,
            priority=request.priority,
        )

        job_ids[pipeline_type] = job_id

        logger.info(
            "Queued pipeline job",
            pipeline_type=pipeline_type,
            job_id=job_id,
            company_id=request.company_id,
        )

    return {
        "success": True,
    "message": "Traditional pipeline jobs queued successfully",
        "company_id": request.company_id,
        "job_ids": job_ids,
        "total_jobs": len(job_ids),
    "pipeline_type": "traditional",
    }


@router.get("/status/{job_id}")
async def get_pipeline_status(job_id: str) -> PipelineStatusResponse:
    """
    Get the status of a specific pipeline job.
    """
    try:
        # Get job status
        job_result = await queue_get_job_status(job_id)

        if job_result is None:
            raise HTTPException(status_code=404, detail="Job not found")

        # Extract pipeline type from job args (stored in result data)
        pipeline_type = "unknown"
        if job_result.result and isinstance(job_result.result, dict):
            pipeline_type = job_result.result.get("pipeline_type", "unknown")

        # Convert timestamps to ISO format
        created_at = None
        started_at = None
        if job_result.started_at:
            started_at = job_result.started_at.isoformat()

        completed_at = None
        if job_result.completed_at:
            completed_at = job_result.completed_at.isoformat()

        response = PipelineStatusResponse(
            job_id=job_id,
            status=job_result.status.value,
            pipeline_type=pipeline_type,
            created_at=created_at,
            started_at=started_at,
            completed_at=completed_at,
            error_message=job_result.error_message,
            result=job_result.result,
        )

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get pipeline status: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats")
async def get_pipeline_stats() -> Dict[str, Any]:
    """
    Get pipeline statistics and queue information.
    """
    try:
        # Get queue statistics
        queues = ["high", "normal", "low"]
        queue_stats = {}

        for queue_name in queues:
            stats = await get_queue_stats(queue_name)
            queue_stats[queue_name] = stats

        # Worker stats would need to be implemented separately
        # For now, return basic info
        worker_stats = {
            "total_workers": 1,  # This would need to be tracked separately
            "active_workers": 0,  # This would need to be tracked separately
            "idle_workers": 1,  # This would need to be tracked separately
        }

        return {
            "queues": queue_stats,
            "workers": worker_stats,
            "service_info": {
                "version": settings.VERSION,
                "environment": settings.ENVIRONMENT,
            },
        }

    except Exception as e:
        logger.error(f"Failed to get pipeline stats: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/jobs/{job_id}")
async def cancel_pipeline_job(job_id: str) -> Dict[str, Any]:
    """
    Cancel a pipeline job.
    """
    try:
        # Cancel the job
        success = await queue_cancel_job(job_id)

        if not success:
            raise HTTPException(
                status_code=404, detail="Job not found or could not be cancelled"
            )

        return {
            "success": True,
            "message": f"Job {job_id} has been cancelled",
            "job_id": job_id,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/jobs/{job_id}/retry")
async def retry_pipeline_job(job_id: str, request: JobRetryRequest) -> Dict[str, Any]:
    """
    Retry a failed or cancelled pipeline job.

    Args:
        job_id: The ID of the job to retry
        request: Retry request containing delay configuration
    """
    try:
        # Retry the job
        success = await queue_retry_job(job_id, request.delay_seconds)

        if not success:
            raise HTTPException(
                status_code=400, detail="Job not found or cannot be retried"
            )

        return {
            "success": True,
            "message": f"Job {job_id} has been queued for retry",
            "job_id": job_id,
            "delay_seconds": request.delay_seconds,
        }

    except ValueError as e:
        # Handle validation errors (job not found, cannot retry, etc.)
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to retry job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
