import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/lib/auth-context';

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

// Helper function to check if we're on the client side
function isClient() {
  return typeof window !== 'undefined';
}

// Helper function to safely access localStorage
function safeLocalStorage() {
  if (!isClient()) return null;
  try {
    return window.localStorage;
  } catch (error) {
    console.warn('localStorage not available:', error);
    return null;
  }
}

// Helper function to check if a question is visible based on visibility conditions
function isQuestionVisible(question: any, answers: Record<string, any>): boolean {
  if (!question?.visibility_condition) {
    return true; // No condition means always visible
  }

  try {
    const condition = question.visibility_condition;
    const targetQuestionId = condition.question_id;
    const operator = condition.operator;
    const expectedValue = condition.value;

    // Get the answer for the target question
    const actualValue = answers[targetQuestionId];

    // If no answer exists, question is not visible (unless operator is 'empty')
    if (actualValue === undefined || actualValue === null || actualValue === '') {
      return operator === 'empty';
    }

    // Apply the visibility condition
    switch (operator) {
      case 'eq':
        return actualValue === expectedValue;
      case 'ne':
        return actualValue !== expectedValue;
      case 'gt':
        return Number(actualValue) > Number(expectedValue);
      case 'gte':
        return Number(actualValue) >= Number(expectedValue);
      case 'lt':
        return Number(actualValue) < Number(expectedValue);
      case 'lte':
        return Number(actualValue) <= Number(expectedValue);
      case 'in':
        return Array.isArray(expectedValue) && expectedValue.includes(actualValue);
      case 'not_in':
        return Array.isArray(expectedValue) && !expectedValue.includes(actualValue);
      case 'contains':
        return String(actualValue).toLowerCase().includes(String(expectedValue).toLowerCase());
      case 'not_contains':
        return !String(actualValue).toLowerCase().includes(String(expectedValue).toLowerCase());
      case 'empty':
        return actualValue === undefined || actualValue === null || actualValue === '';
      case 'not_empty':
        return actualValue !== undefined && actualValue !== null && actualValue !== '';
      default:
        return true; // Unknown operator, default to visible
    }
  } catch (error) {
    console.warn('Error evaluating visibility condition:', error);
    return true; // Default to visible on error
  }
}

// Helper function to get the number of required instances for a repeatable section
function getRequiredInstances(controllerQuestion: any, answers: Record<string, any>): number {
  if (!controllerQuestion || !controllerQuestion.repeat_section_id) {
    return 0;
  }

  try {
    const controllerAnswer = answers[controllerQuestion._id || controllerQuestion.id];
    
    // If no answer, no instances required
    if (controllerAnswer === undefined || controllerAnswer === null || controllerAnswer === '') {
      return 0;
    }

    // Convert to number and ensure it's within bounds
    const numInstances = Math.max(0, Math.min(Number(controllerAnswer) || 0, controllerQuestion.max_repeats || 10));
    return numInstances;
  } catch (error) {
    console.warn('Error calculating required instances:', error);
    return 0;
  }
}

export function useFormProgress(token: string, form: any) {
  const { isAuthenticated, user } = useAuth();
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [progress, setProgress] = useState(0);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(false);
  const [canSubmit, setCanSubmit] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);

  // Mark as hydrated after first render
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Load progress from localStorage or backend
  useEffect(() => {
    if (!form || !isHydrated) return;

    const loadProgress = async () => {
      try {
        if (isAuthenticated && user) {
          // Load from backend if authenticated
          try {
            const response = await fetch(`${API_BASE}/forms/share/${token}/progress/${user.id}`);
            if (response.ok) {
              const data = await response.json();
              setAnswers(data.answers || {});
              setAutoSaveEnabled(true);
            }
          } catch (error) {
            console.error('Failed to load progress from backend:', error);
          }
        } else {
          // Load from localStorage
          const storage = safeLocalStorage();
          if (storage) {
            try {
              const saved = storage.getItem(`form_progress_${token}`);
              if (saved) {
                const data = JSON.parse(saved);
                setAnswers(data.answers || {});
              }
            } catch (error) {
              console.error('Failed to parse saved progress:', error);
            }
          }
        }
      } catch (error) {
        console.error('Error loading progress:', error);
      }
    };

    loadProgress();
  }, [token, form, isAuthenticated, user, isHydrated]);

  // Calculate progress with proper validation logic
  useEffect(() => {
    if (!form?.sections || !isHydrated) {
      return; // Don't update state during SSR
    }

    try {
      let totalRequired = 0;
      let totalAnswered = 0;

      // Build a map of sections for quick lookup
      const sectionMap: Record<string, any> = {};
      form.sections.forEach((section: any) => {
        if (section && (section._id || section.id)) {
          sectionMap[section._id || section.id] = section;
        }
      });

      // Process each section
      form.sections.forEach((section: any) => {
        // Skip repeatable sections - they'll be handled by their controller questions
        if (!section || section.repeatable) {
          return;
        }

        section.questions?.forEach((question: any) => {
          if (!question) return;
          
          const questionId = question._id || question.id;
          if (!questionId) return;

          // Check if this question controls a repeatable section
          if (question.repeat_section_id) {
            const repeatSection = sectionMap[question.repeat_section_id];
            if (repeatSection) {
              // First, check if the controller question itself is visible and required
              if (isQuestionVisible(question, answers)) {
                if (question.required) {
                  totalRequired++;
                  const answer = answers[questionId];
                  if (answer !== undefined && answer !== null && answer !== '') {
                    totalAnswered++;
                  }
                }

                // Then handle the repeatable section instances
                const numInstances = getRequiredInstances(question, answers);
                
                for (let instanceIdx = 0; instanceIdx < numInstances; instanceIdx++) {
                  repeatSection.questions?.forEach((subQuestion: any) => {
                    if (!subQuestion) return;
                    
                    const subQuestionId = subQuestion._id || subQuestion.id;
                    if (!subQuestionId) return;
                    
                    const scopedKey = `${subQuestionId}_${instanceIdx}`;

                    // Check visibility for this instance
                    if (isQuestionVisible(subQuestion, answers)) {
                      if (subQuestion.required) {
                        totalRequired++;
                        const answer = answers[scopedKey];
                        if (answer !== undefined && answer !== null && answer !== '') {
                          totalAnswered++;
                        }
                      }
                    }
                  });
                }
              }
            }
          } else {
            // Regular question (not controlling a repeatable section)
            if (isQuestionVisible(question, answers)) {
              if (question.required) {
                totalRequired++;
                const answer = answers[questionId];
                if (answer !== undefined && answer !== null && answer !== '') {
                  totalAnswered++;
                }
              }
            }
          }
        });
      });

      const progressPercent = totalRequired > 0 ? (totalAnswered / totalRequired) * 100 : 100;
      const canSubmitForm = totalRequired === 0 || totalAnswered === totalRequired;
      
      setProgress(Math.round(progressPercent));
      setCanSubmit(canSubmitForm);

      // Debug logging (only in development)
      if (process.env.NODE_ENV === 'development') {
        console.log('Progress calculation:', {
          totalRequired,
          totalAnswered,
          progressPercent: Math.round(progressPercent),
          canSubmit: canSubmitForm,
          answers: Object.keys(answers).length,
          answersDetail: answers,
          formSections: form?.sections?.length || 0
        });
      }
    } catch (error) {
      console.error('Error calculating progress:', error);
      // Don't update state on error to prevent hydration mismatches
    }
  }, [answers, form, isHydrated]);

  const updateAnswer = useCallback((questionId: string, value: any, repeatIndex?: number) => {
    if (!isHydrated) return; // Don't update during SSR
    
    const key = repeatIndex !== undefined ? `${questionId}_${repeatIndex}` : questionId;
    setAnswers(prev => ({ ...prev, [key]: value }));
  }, [isHydrated]);

  const removeRepeatAnswers = useCallback((sectionId: string, repeatIndex: number) => {
    if (!form || !isHydrated) return;

    setAnswers(prev => {
      const newAnswers = { ...prev };

      // Find the section and remove answers for all its questions at the given repeat index
      const section = form.sections.find((s: any) => (s._id || s.id) === sectionId);
      if (section) {
        section.questions?.forEach((question: any) => {
          const key = `${question._id || question.id}_${repeatIndex}`;
          delete newAnswers[key];
        });
      }

      return newAnswers;
    });
  }, [form, isHydrated]);

  const saveProgress = useCallback(async () => {
    if (!isHydrated) return;

    const progressData = {
      answers,
      updated_at: new Date().toISOString(),
    };

    try {
      if (isAuthenticated && user) {
        // Save to backend
        try {
          await fetch(`${API_BASE}/forms/share/${token}/progress/${user.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
            },
            body: JSON.stringify(progressData),
          });
        } catch (error) {
          console.error('Failed to save progress to backend:', error);
        }
      } else {
        // Save to localStorage
        const storage = safeLocalStorage();
        if (storage) {
          try {
            storage.setItem(`form_progress_${token}`, JSON.stringify(progressData));
          } catch (error) {
            console.error('Failed to save progress to localStorage:', error);
          }
        }
      }
    } catch (error) {
      console.error('Error saving progress:', error);
    }
  }, [answers, token, isAuthenticated, user, isHydrated]);

  return {
    answers,
    updateAnswer,
    removeRepeatAnswers,
    progress,
    saveProgress,
    autoSaveEnabled,
    canSubmit,
  };
}
