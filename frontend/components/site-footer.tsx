// import * as React from "react"

// import { siteConfig } from "@/config/site"
// import { cn } from "@/lib/utils"
// import { Icons } from "@/components/icons"
// import { ModeToggle } from "@/components/mode-toggle"

// export function SiteFooter({ className }: React.HTMLAttributes<HTMLElement>) {
//   return (
//     <footer className={cn(className)}>
//       <div className="container flex flex-col items-center justify-between gap-4 py-10 md:h-24 md:flex-row md:py-0">
//         <div className="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0">
//           <Icons.logo />
//           <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
//             © {new Date().getFullYear()} TractionX. All rights reserved.
//             Feel free to reach out to us at <a href="mailto:<EMAIL>"><EMAIL></a> for feedback
//           </p>
//         </div>
//         <ModeToggle />
//       </div>
//     </footer>
//   )
// }
import * as React from "react"

import { siteConfig } from "@/config/site"
import { cn } from "@/lib/utils"
import { Icons } from "@/components/icons"
// import { ModeToggle } from "@/components/mode-toggle"


export function SiteFooter({ className }: React.HTMLAttributes<HTMLElement>) {
  return (
    <footer className={cn(className)}>
      <div className="container flex flex-col items-center justify-between gap-2 py-4 md:h-16 md:flex-row md:py-0">
        <div className="flex flex-col items-center gap-2 px-4 md:flex-row md:gap-2 md:px-0">
          {/* 
            Use a larger Tailwind scale: 
            • h-8 w-8  => 32px × 32px
            • md:h-10 md:w-10 => 40px × 40px on medium+ screens 
          */}

          <p className="text-center text-xs leading-snug text-muted-foreground md:text-left">
            © {new Date().getFullYear()} TractionX. All rights reserved. Feel free to reach out at{" "}
            <a className="underline" href="mailto:<EMAIL>">
              <EMAIL>
            </a>
          </p>
        </div>
        {/* <ModeToggle /> */}
      </div>
    </footer>
  )
}
