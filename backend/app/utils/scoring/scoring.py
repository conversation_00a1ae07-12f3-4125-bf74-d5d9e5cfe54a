"""
Scoring Utility Functions

This module provides utility functions for scoring matches between expected values
and actual values for different data types, as well as evaluating conditions.
"""

import re
from difflib import SequenceMatcher
from typing import Any, Dict, List, Union


def normalize_bool(val):
    if isinstance(val, bool):
        return val
    if isinstance(val, str):
        if val.lower() in ("true", "yes", "1"):
            return True
        if val.lower() in ("false", "no", "0"):
            return False
    if isinstance(val, int):
        return bool(val)
    return val


def score_number(
    expected: Union[int, float, bool, str, list[Any]],
    actual: Union[int, float, bool, str, list[Any]],
) -> float:
    """
    Score a numeric match between expected and actual values.

    The score is calculated based on the relative difference between the values.
    A perfect match (expected == actual) returns 1.0.
    As the difference increases, the score approaches 0.0.

    Args:
        expected: Expected numeric value
        actual: Actual numeric value

    Returns:
        Score between 0.0 and 1.0
    """
    # Convert to float for numeric operations
    try:
        expected_val = float(expected) if expected is not None else 0.0  # type: ignore
        actual_val = float(actual) if actual is not None else 0.0  # type: ignore
    except (ValueError, TypeError):
        return 0.0

    if expected_val == actual_val:
        return 1.0

    # Calculate relative difference
    max_val = max(abs(expected_val), abs(actual_val))
    if max_val == 0:
        return 1.0  # Both are zero

    diff = abs(expected_val - actual_val)
    relative_diff = diff / max_val

    # Convert to score (1.0 for perfect match, approaching 0.0 for large differences)
    score = 1.0 / (1.0 + relative_diff)

    return score


def score_boolean(
    expected: Union[bool, str, Any], actual: Union[bool, str, Any]
) -> float:
    """
    Score a boolean match between expected and actual values.

    Args:
        expected: Expected boolean value
        actual: Actual boolean value

    Returns:
        1.0 if values match, 0.0 otherwise
    """
    return 1.0 if expected == actual else 0.0


def score_short_text(
    expected: Union[str, bool, int, float, list[Any], Any],
    actual: Union[str, bool, int, float, list[Any], Any],
) -> float:
    """
    Score a text match between expected and actual values.

    Uses SequenceMatcher to calculate similarity between strings.

    Args:
        expected: Expected text value
        actual: Actual text value

    Returns:
        Score between 0.0 and 1.0
    """
    # Convert to strings and normalize
    expected_str = str(expected) if expected is not None else ""
    actual_str = str(actual) if actual is not None else ""

    expected_norm = expected_str.lower().strip()
    actual_norm = actual_str.lower().strip()

    if expected_norm == actual_norm:
        return 1.0

    # Calculate similarity ratio
    similarity = SequenceMatcher(None, expected_norm, actual_norm).ratio()

    return similarity


def score_long_text(
    expected: Union[str, bool, int, float, list[Any], Any],
    actual: Union[str, bool, int, float, list[Any], Any],
) -> float:
    """
    Score a long text match between expected and actual values.

    For long text, we focus on key terms and semantic similarity.

    Args:
        expected: Expected text value
        actual: Actual text value

    Returns:
        Score between 0.0 and 1.0
    """
    # Convert to strings and normalize
    expected_str = str(expected) if expected is not None else ""
    actual_str = str(actual) if actual is not None else ""

    expected_norm = expected_str.lower().strip()
    actual_norm = actual_str.lower().strip()

    if expected_norm == actual_norm:
        return 1.0

    # Extract key terms from expected text
    expected_terms = set(re.findall(r"\b\w+\b", expected_norm))
    if not expected_terms:
        return 0.0

    # Count how many expected terms appear in actual text
    actual_terms = set(re.findall(r"\b\w+\b", actual_norm))
    matching_terms = expected_terms.intersection(actual_terms)

    # Calculate score based on term overlap
    score = len(matching_terms) / len(expected_terms)

    return score


def score_multi_select(
    expected: List[Union[int, float, bool, str, list[Any], Any]],
    actual: List[Union[int, float, bool, str, list[Any], Any]],
) -> float:
    """
    Score a multi-select match between expected and actual values.

    The score is based on the overlap between the two lists.

    Args:
        expected: Expected list of values
        actual: Actual list of values

    Returns:
        Score between 0.0 and 1.0
    """
    if not expected:
        return 1.0 if not actual else 0.0

    if not actual:
        return 0.0

    # Convert to sets for easier intersection
    expected_set = set(expected)
    actual_set = set(actual)

    # Calculate Jaccard similarity: |A ∩ B| / |A ∪ B|
    intersection = len(expected_set.intersection(actual_set))
    union = len(expected_set.union(actual_set))

    if union == 0:
        return 1.0  # Both sets are empty

    return intersection / union


def score_single_select(expected: Any, actual: Any) -> float:
    """
    Score a single-select match between expected and actual values.

    Args:
        expected: Expected value
        actual: Actual value

    Returns:
        1.0 if values match, 0.0 otherwise
    """
    return 1.0 if expected == actual else 0.0


def score_range(
    expected: List[Union[int, float, bool, str, list[Any], Any]],
    actual: Union[int, float, bool, str, list[Any], Any],
) -> float:
    """
    Score a range match between expected range and actual value.

    Args:
        expected: Expected range as [min, max]
        actual: Actual value

    Returns:
        Score between 0.0 and 1.0
    """
    if len(expected) != 2:
        return 0.0

    # Convert expected values to numbers
    try:
        min_val = float(expected[0]) if expected[0] is not None else 0.0  # type: ignore
        max_val = float(expected[1]) if expected[1] is not None else 0.0  # type: ignore
    except (ValueError, TypeError):
        return 0.0

    # Convert actual value to number
    try:
        actual_val = float(actual) if actual is not None else 0.0  # type: ignore
    except (ValueError, TypeError):
        return 0.0

    # Check if actual is within range
    if min_val <= actual_val <= max_val:
        return 1.0

    # Calculate distance to range
    if actual_val < min_val:
        distance = min_val - actual_val
        reference = max(abs(min_val), abs(max_val))
    else:  # actual_val > max_val
        distance = actual_val - max_val
        reference = max(abs(min_val), abs(max_val))

    if reference == 0:
        return 0.0

    # Convert to score (approaching 0.0 as distance increases)
    relative_distance = distance / reference
    score = 1.0 / (1.0 + relative_distance)

    return score


def evaluate_condition(condition: Dict[str, Any], value: Any) -> bool:
    """
    Evaluate a condition against a value.

    Args:
        condition: Dictionary with operator and value, e.g., {"eq": 10}
        value: Value to evaluate against

    Returns:
        True if condition is met, False otherwise
    """
    if not condition:
        return True

    operator = next(iter(condition))
    condition_value = condition[operator]

    if operator == "eq":
        # Special handling for booleans
        if isinstance(value, (bool, str, int)) and isinstance(
            condition_value, (bool, str, int)
        ):
            return normalize_bool(value) == normalize_bool(condition_value)
        return value == condition_value
    elif operator == "ne":
        if isinstance(value, (bool, str, int)) and isinstance(
            condition_value, (bool, str, int)
        ):
            return normalize_bool(value) != normalize_bool(condition_value)
        return value != condition_value
    elif operator == "gt":
        if isinstance(value, (int, float)) and isinstance(
            condition_value, (int, float)
        ):
            return value > condition_value
        return False
    elif operator == "lt":
        if isinstance(value, (int, float)) and isinstance(
            condition_value, (int, float)
        ):
            return value < condition_value
        return False
    elif operator == "gte":
        if isinstance(value, (int, float)) and isinstance(
            condition_value, (int, float)
        ):
            return value >= condition_value
        return False
    elif operator == "lte":
        if isinstance(value, (int, float)) and isinstance(
            condition_value, (int, float)
        ):
            return value <= condition_value
        return False
    elif operator == "contains":
        if isinstance(value, str) and isinstance(condition_value, str):
            return condition_value in value
        elif isinstance(value, list):
            return condition_value in value
        return False
    elif operator == "not_contains":
        if isinstance(value, str) and isinstance(condition_value, str):
            return condition_value not in value
        elif isinstance(value, list):
            return condition_value not in value
        return True
    elif operator == "starts_with":
        if isinstance(value, str) and isinstance(condition_value, str):
            return value.startswith(condition_value)
        return False
    elif operator == "ends_with":
        if isinstance(value, str) and isinstance(condition_value, str):
            return value.endswith(condition_value)
        return False
    elif operator == "in":
        if isinstance(condition_value, list):
            return value in condition_value
        return False
    elif operator == "not_in":
        if isinstance(condition_value, list):
            return value not in condition_value
        return True
    elif operator == "between":
        if (
            isinstance(condition_value, list)
            and len(condition_value) == 2
            and isinstance(value, (int, float))
        ):
            return condition_value[0] <= value <= condition_value[1]
        return False
    elif operator == "not_between":
        if (
            isinstance(condition_value, list)
            and len(condition_value) == 2
            and isinstance(value, (int, float))
        ):
            return value < condition_value[0] or value > condition_value[1]
        return True
    else:
        return False


def score_match(expected: Any, actual: Any, match_type: str = "auto") -> float:
    """
    Score a match between expected and actual values based on their types.

    Args:
        expected: Expected value
        actual: Actual value
        match_type: Type of match to perform (auto, number, boolean, text, multi_select, single_select, range)

    Returns:
        Score between 0.0 and 1.0
    """
    # Auto-detect match type if not specified
    if match_type == "auto":
        if isinstance(expected, (int, float)) and isinstance(actual, (int, float)):
            match_type = "number"
        elif isinstance(expected, bool) and isinstance(actual, bool):
            match_type = "boolean"
        elif isinstance(expected, str) and isinstance(actual, str):
            if len(expected) > 100 or len(actual) > 100:
                match_type = "long_text"
            else:
                match_type = "short_text"
        elif isinstance(expected, list) and isinstance(actual, list):
            match_type = "multi_select"
        elif (
            isinstance(expected, list)
            and len(expected) == 2
            and isinstance(actual, (int, float))
        ):
            match_type = "range"
        else:
            match_type = "single_select"

    # Score based on match type
    if match_type == "number":
        return score_number(expected, actual)
    elif match_type == "boolean":
        return score_boolean(expected, actual)
    elif match_type == "short_text":
        return score_short_text(expected, actual)
    elif match_type == "long_text":
        return score_long_text(expected, actual)
    elif match_type == "multi_select":
        return score_multi_select(expected, actual)  # type: ignore
    elif match_type == "single_select":
        return score_single_select(expected, actual)
    elif match_type == "range":
        return score_range(expected, actual)  # type: ignore
    else:
        # Default to exact match
        return 1.0 if expected == actual else 0.0
