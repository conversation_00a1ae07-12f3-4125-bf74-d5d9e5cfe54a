"""
Exclusion Filter Service Implementation

This module implements the exclusion filter service interface.
"""
from typing import Dict, List, Optional, Any, Union
from bson import ObjectId
from datetime import datetime, timezone

from app.services.base import BaseService
from app.services.exclusion_filter.interfaces import ExclusionFilterServiceInterface
from app.models.exclusion_filter import ExclusionFilter
from app.utils.scoring.scoring import evaluate_condition


class ExclusionFilterService(BaseService, ExclusionFilterServiceInterface):
    """Implementation of the exclusion filter service."""

    def __init__(self, db=None):
        super().__init__()
        self.db = db

    async def initialize(self) -> None:
        """Initialize service resources."""
        if self.db is None:
            from app.core.database import get_database
            async for db in get_database():
                self.db = db
                break
        self.logger.info("Exclusion filter service initialized")

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        pass

    async def create_exclusion_filter(
        self,
        **kwargs
    ) -> ExclusionFilter:
        """Create a new exclusion filter."""
        try:
            # Convert string IDs to ObjectId
            for id_field in ["org_id", "form_id", "created_by"]:
                if id_field in kwargs and isinstance(kwargs[id_field], str) and kwargs[id_field]:
                    kwargs[id_field] = ObjectId(kwargs[id_field])

            # Set default created_by if not provided
            if "created_by" not in kwargs or not kwargs["created_by"]:
                kwargs["created_by"] = ObjectId("000000000000000000000000")  # Default to system user

            # Normalize operator to lowercase if provided
            if "operator" in kwargs and kwargs["operator"]:
                kwargs["operator"] = kwargs["operator"].lower()

            # Ensure conditions is a list
            if "conditions" not in kwargs or not kwargs["conditions"]:
                kwargs["conditions"] = []

            # Create filter
            exclusion_filter = ExclusionFilter(**kwargs)

            # Save to database
            await exclusion_filter.save()

            return exclusion_filter
        except Exception as e:
            await self.handle_error(e, {
                "kwargs": kwargs
            })
            raise

    async def get_exclusion_filter(
        self,
        filter_id: Union[str, ObjectId]
    ) -> Optional[ExclusionFilter]:
        """Get an exclusion filter by ID."""
        try:
            if isinstance(filter_id, str):
                filter_id = ObjectId(filter_id)

            return await ExclusionFilter.find_one({"_id": filter_id, "is_deleted": False})
        except Exception as e:
            await self.handle_error(e, {"filter_id": str(filter_id)})
            return None

    async def update_exclusion_filter(
        self,
        filter_id: Union[str, ObjectId],
        update_data: Dict[str, Any]
    ) -> Optional[ExclusionFilter]:
        """Update an exclusion filter."""
        try:
            if isinstance(filter_id, str):
                filter_id = ObjectId(filter_id)

            # Get existing filter
            exclusion_filter = await self.get_exclusion_filter(filter_id)
            if not exclusion_filter:
                return None

            # Update fields
            for key, value in update_data.items():
                if key in ["name", "description", "operator", "conditions", "is_active"]:
                    setattr(exclusion_filter, key, value)

            # Update timestamp
            exclusion_filter.updated_at = int(datetime.now(timezone.utc).timestamp())

            # Save changes
            await exclusion_filter.save(is_update=True)

            return exclusion_filter
        except Exception as e:
            await self.handle_error(e, {
                "filter_id": str(filter_id),
                "update_data": update_data
            })
            return None

    async def delete_exclusion_filter(
        self,
        filter_id: Union[str, ObjectId]
    ) -> bool:
        """Delete an exclusion filter."""
        try:
            if isinstance(filter_id, str):
                filter_id = ObjectId(filter_id)

            # Get existing filter
            exclusion_filter = await self.get_exclusion_filter(filter_id)
            if not exclusion_filter:
                return False

            # Soft delete
            exclusion_filter.is_deleted = True
            exclusion_filter.updated_at = int(datetime.now(timezone.utc).timestamp())

            # Save changes
            await exclusion_filter.save(is_update=True)

            return True
        except Exception as e:
            await self.handle_error(e, {"filter_id": str(filter_id)})
            return False

    async def list_exclusion_filters(
        self,
        org_id: Union[str, ObjectId],
        form_id: Optional[Union[str, ObjectId]] = None,
        include_deleted: bool = False
    ) -> List[ExclusionFilter]:
        """List exclusion filters for an organization."""
        try:
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            # Build query
            query = {"org_id": org_id}

            if form_id:
                if isinstance(form_id, str):
                    form_id = ObjectId(form_id)
                query["form_id"] = form_id

            if not include_deleted:
                query["is_deleted"] = False

            # Get filters
            return await ExclusionFilter.find_many(query=query)
        except Exception as e:
            await self.handle_error(e, {
                "org_id": str(org_id),
                "form_id": str(form_id) if form_id else None
            })
            return []

    async def check_exclusion(
        self,
        org_id: Union[str, ObjectId],
        form_id: Union[str, ObjectId],
        answers: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Check if a submission should be excluded based on exclusion filters."""
        try:
            # Convert IDs
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(form_id, str):
                form_id = ObjectId(form_id)

            # Get active filters for this org and form
            filters = await self.list_exclusion_filters(org_id, form_id)

            # Default response (not excluded)
            result = {
                "excluded": False,
                "filter_id": None,
                "filter_name": None,
                "reason": None
            }

            # Check each filter
            for exclusion_filter in filters:
                if not exclusion_filter.is_active:
                    continue

                # Evaluate filter conditions
                if await self._evaluate_filter(exclusion_filter, answers):
                    # Filter matched, submission should be excluded
                    result["excluded"] = True
                    result["filter_id"] = str(exclusion_filter.id)
                    result["filter_name"] = exclusion_filter.name
                    result["reason"] = exclusion_filter.description or f"Matched exclusion filter: {exclusion_filter.name}"
                    break

            return result
        except Exception as e:
            await self.handle_error(e, {
                "org_id": str(org_id),
                "form_id": str(form_id)
            })
            # Default to not excluded on error
            return {
                "excluded": False,
                "filter_id": None,
                "filter_name": None,
                "reason": f"Error evaluating exclusion filters: {str(e)}"
            }

    async def _evaluate_filter(
        self,
        exclusion_filter: ExclusionFilter,
        answers: Dict[str, Any]
    ) -> bool:
        """
        Evaluate if a filter matches the given answers.

        Args:
            exclusion_filter: The filter to evaluate
            answers: Form answers

        Returns:
            True if filter matches (should exclude), False otherwise
        """
        # Handle structured answers format
        if isinstance(answers, dict) and "answers" in answers and isinstance(answers["answers"], dict):
            flat_answers = answers["answers"]

            # Also handle repeatable answers if present
            if "repeatable_answers" in answers and isinstance(answers["repeatable_answers"], dict):
                # We only check the first instance of each repeatable section for simplicity
                for section_id, instances in answers["repeatable_answers"].items():
                    if instances and "0" in instances:
                        flat_answers.update(instances["0"])
        else:
            flat_answers = answers

        # No conditions means no exclusion
        if not exclusion_filter.conditions:
            return False

        # Evaluate each condition
        results = []
        for condition in exclusion_filter.conditions:
            question_id = condition.get("question_id")
            if not question_id:
                continue

            # Skip if answer not provided
            if question_id not in flat_answers:
                results.append(False)
                continue

            # Get answer value
            answer_value = flat_answers[question_id]

            # Evaluate condition
            condition_result = self._evaluate_condition(
                condition.get("operator", "eq"),
                answer_value,
                condition.get("value")
            )
            results.append(condition_result)

        # Apply logical operator
        if exclusion_filter.operator.lower() == "and":
            return all(results)
        else:  # "or"
            return any(results)

    def _evaluate_condition(
        self,
        operator: str,
        answer_value: Any,
        expected_value: Any
    ) -> bool:
        """
        Evaluate a single condition.

        Args:
            operator: Comparison operator
            answer_value: Actual answer value
            expected_value: Expected value to compare against

        Returns:
            True if condition matches, False otherwise
        """
        # Map operator to condition format expected by evaluate_condition
        operator_map = {
            "eq": "eq",
            "ne": "ne",
            "gt": "gt",
            "lt": "lt",
            "gte": "gte",
            "lte": "lte",
            "in": "in",
            "not_in": "not_in",
            "contains": "contains",
            "not_contains": "not_contains"
        }

        # Create condition in format expected by evaluate_condition
        condition = {operator_map.get(operator, "eq"): expected_value}

        # Use existing utility function to evaluate
        return evaluate_condition(condition, answer_value)
