/**
 * API client for handling file uploads (avatars, logos, etc.)
 */

import { API_BASE_URL } from "./auth-api"

interface GenerateUploadUrlRequest {
  file_type: "avatar" | "logo"
  filename: string
  content_type: string
  file_size: number
}

interface GenerateUploadUrlResponse {
  presigned_url: string
  public_url: string
  s3_key: string
  expires_in: number
}

export class UploadsAPI {
  /**
   * Generate presigned URL for uploading assets
   */
  static async generateUploadUrl(
    request: GenerateUploadUrlRequest,
    token: string
  ): Promise<GenerateUploadUrlResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/uploads/generate-presigned-url`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || 'Failed to generate upload URL')
      }

      return response.json()
    } catch (error) {
      console.error('Generate upload URL error:', error)
      throw error
    }
  }

  /**
   * Upload file directly to S3 using presigned URL
   */
  static async uploadToS3(
    file: File,
    presignedUrl: string,
    contentType: string
  ): Promise<void> {
    try {
      const response = await fetch(presignedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': contentType,
          'Content-Length': file.size.toString(),
        },
        body: file,
      })

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.status} ${response.statusText}`)
      }
    } catch (error) {
      console.error('S3 upload error:', error)
      throw error
    }
  }

  /**
   * Complete upload process: generate URL, upload to S3, return public URL
   */
  static async uploadFile(
    file: File,
    fileType: "avatar" | "logo",
    token: string
  ): Promise<string> {
    try {
      // Step 1: Generate presigned URL
      const uploadData = await this.generateUploadUrl(
        {
          file_type: fileType,
          filename: file.name,
          content_type: file.type,
          file_size: file.size,
        },
        token
      )

      // Step 2: Upload to S3
      await this.uploadToS3(file, uploadData.presigned_url, file.type)

      // Step 3: Return public URL
      return uploadData.public_url
    } catch (error) {
      console.error('Complete upload error:', error)
      throw error
    }
  }

  /**
   * Delete an uploaded asset
   */
  static async deleteAsset(s3Key: string, token: string): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/uploads/asset/${encodeURIComponent(s3Key)}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || 'Failed to delete asset')
      }
    } catch (error) {
      console.error('Delete asset error:', error)
      throw error
    }
  }

  /**
   * Upload avatar for current user
   */
  static async uploadAvatar(file: File, token: string): Promise<string> {
    return this.uploadFile(file, "avatar", token)
  }

  /**
   * Upload logo for organization
   */
  static async uploadLogo(file: File, token: string): Promise<string> {
    return this.uploadFile(file, "logo", token)
  }
}
