"use client"

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Icons } from '@/components/icons';
import { useToast } from '@/components/ui/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

interface FormSharingPanelProps {
  formId: string;
  formName: string;
}

interface SharingConfig {
  _id?: string;
  enabled: boolean;
  sharing_types: string[];
  expires_at?: number;
  access_token?: string;
}

interface SharingLink {
  _id: string;
  token: string;
  url: string;
  expires_at?: number;
  view_count: number;
  created_at: number;
}

export function FormSharingPanel({ formId, formName }: FormSharingPanelProps) {
  const { toast } = useToast();
  const [sharingConfig, setSharingConfig] = useState<SharingConfig | null>(null);
  const [sharingLinks, setSharingLinks] = useState<SharingLink[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [expirationDays, setExpirationDays] = useState(30);

  useEffect(() => {
    loadSharingData();
  }, [formId]);

  const loadSharingData = async () => {
    try {
      setLoading(true);

      // Load sharing config
      const configResponse = await fetch(`${API_BASE}/sharing/configs?resource_type=form&resource_id=${formId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'X-ORG-ID': localStorage.getItem('orgId') || '',
        },
      });

      if (configResponse.ok) {
        const configs = await configResponse.json();
        if (configs.length > 0) {
          setSharingConfig(configs[0]);

          // Load sharing links for this config
          const linksResponse = await fetch(`${API_BASE}/sharing/configs/${configs[0]._id}/links`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'X-ORG-ID': localStorage.getItem('orgId') || '',
            },
          });

          if (linksResponse.ok) {
            const links = await linksResponse.json();
            setSharingLinks(links);
          } else {
            console.error('Failed to load sharing links:', linksResponse.status, linksResponse.statusText);
          }
        } else {
          // No sharing config found, reset links
          setSharingLinks([]);
        }
      } else {
        console.error('Failed to load sharing configs:', configResponse.status, configResponse.statusText);
      }
    } catch (error) {
      console.error('Error loading sharing data:', error);
    } finally {
      setLoading(false);
    }
  };

  const createSharingConfig = async () => {
    try {
      setCreating(true);

      const response = await fetch(`${API_BASE}/sharing/configs`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'X-ORG-ID': localStorage.getItem('orgId') || '',
        },
        body: JSON.stringify({
          resource_type: 'form',
          resource_id: formId,
          enabled: true,
          sharing_types: ['link'],
          tracking_enabled: true,
        }),
      });

      if (response.ok) {
        const config = await response.json();
        setSharingConfig(config);
        await loadSharingData(); // Reload to get links
        toast({
          title: 'Success',
          description: 'Sharing enabled for this form',
        });
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to create sharing config');
      }
    } catch (error) {
      console.error('Error creating sharing config:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to enable sharing',
        variant: 'destructive',
      });
    } finally {
      setCreating(false);
    }
  };

  const generateSharingLink = async () => {
    if (!sharingConfig) return;

    try {
      setCreating(true);

      const expiresAt = expirationDays > 0
        ? Math.floor((Date.now() + expirationDays * 24 * 60 * 60 * 1000) / 1000)
        : undefined;

      const response = await fetch(`${API_BASE}/sharing/configs/${sharingConfig._id}/links`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'X-ORG-ID': localStorage.getItem('orgId') || '',
        },
        body: JSON.stringify({
          expires_at: expiresAt,
          metadata: { source: 'form_builder' },
        }),
      });

      if (response.ok) {
        const link = await response.json();
        setSharingLinks(prev => [link, ...prev]);
        toast({
          title: 'Success',
          description: 'Sharing link created successfully',
        });
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to create sharing link');
      }
    } catch (error) {
      console.error('Error creating sharing link:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create sharing link',
        variant: 'destructive',
      });
    } finally {
      setCreating(false);
    }
  };

  const copyToClipboard = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      toast({
        title: 'Copied!',
        description: 'Sharing link copied to clipboard',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to copy link',
        variant: 'destructive',
      });
    }
  };

  const toggleSharing = async (enabled: boolean) => {
    if (!sharingConfig) return;

    try {
      const response = await fetch(`${API_BASE}/sharing/configs/${sharingConfig._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'X-ORG-ID': localStorage.getItem('orgId') || '',
        },
        body: JSON.stringify({
          enabled,
        }),
      });

      if (response.ok) {
        setSharingConfig(prev => prev ? { ...prev, enabled } : null);
        toast({
          title: 'Success',
          description: `Sharing ${enabled ? 'enabled' : 'disabled'}`,
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update sharing settings',
        variant: 'destructive',
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Icons.spinner className="size-6 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Share Form</h3>
        <p className="text-sm text-muted-foreground">
          Create public links to share this form with external users
        </p>
      </div>

      {!sharingConfig ? (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4 text-center">
              <Icons.share className="mx-auto size-12 text-muted-foreground" />
              <div>
                <h4 className="font-medium">Enable Form Sharing</h4>
                <p className="text-sm text-muted-foreground">
                  Allow external users to access and submit this form via public links
                </p>
              </div>
              <Button onClick={createSharingConfig} disabled={creating}>
                {creating ? (
                  <>
                    <Icons.spinner className="mr-2 size-4 animate-spin" />
                    Enabling...
                  </>
                ) : (
                  <>
                    <Icons.share className="mr-2 size-4" />
                    Enable Sharing
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {/* Sharing Status */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">Sharing Status</CardTitle>
                <Badge variant={sharingConfig.enabled ? "default" : "secondary"}>
                  {sharingConfig.enabled ? "Enabled" : "Disabled"}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="sharing-enabled">Enable public sharing</Label>
                  <p className="text-xs text-muted-foreground">
                    Allow external users to access this form
                  </p>
                </div>
                <Switch
                  id="sharing-enabled"
                  checked={sharingConfig.enabled}
                  onCheckedChange={toggleSharing}
                />
              </div>
            </CardContent>
          </Card>

          {/* Create New Link */}
          {sharingConfig.enabled && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Create Sharing Link</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* <div className="space-y-2">
                  <Label htmlFor="expiration">Link Expiration</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="expiration"
                      type="number"
                      value={expirationDays}
                      onChange={(e) => setExpirationDays(Number(e.target.value))}
                      placeholder="Days"
                      min="0"
                      max="365"
                    />
                    <span className="flex items-center text-sm text-muted-foreground">days</span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Set to 0 for no expiration
                  </p>
                </div> */}
                <div className="flex items-center gap-2">
                  <Input
                    id="expiration"
                    type="number"
                    value={expirationDays}
                    onChange={(e) => setExpirationDays(Number(e.target.value))}
                    placeholder="Days"
                    min="0"
                    max="365"
                    className="w-20"  // or "w-16" for a tighter box
                  />
                  <span className="text-sm text-muted-foreground">days</span>
                </div>

                <Button onClick={generateSharingLink} disabled={creating} className="w-full">
                  {creating ? (
                    <>
                      <Icons.spinner className="mr-2 size-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Icons.link className="mr-2 size-4" />
                      Generate Link
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Existing Links */}
          {sharingLinks.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Active Links</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {sharingLinks.map((link) => (
                  <div key={link._id} className="space-y-2 rounded-lg border p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Icons.link className="size-4 text-muted-foreground" />
                        <span className="text-sm font-medium">
                          {link.view_count} views
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(link.url)}
                        >
                          <Icons.copy className="size-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(link.url, '_blank')}
                        >
                          <Icons.externalLink className="size-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="rounded bg-muted p-2">
                      <code className="break-all text-xs">{link.url}</code>
                    </div>

                    {link.expires_at && (
                      <p className="text-xs text-muted-foreground">
                        Expires: {new Date(link.expires_at * 1000).toLocaleDateString()}
                      </p>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
