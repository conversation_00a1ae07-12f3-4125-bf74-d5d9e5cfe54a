"use client"

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Icons } from '@/components/icons';
import Confetti from 'react-confetti';
import { useWindowSize } from 'react-use';

interface SubmissionSuccessProps {
  organization?: {
    name: string;
    logo_url?: string;
  };
  submissionId: string;
}

export function SubmissionSuccess({ organization, submissionId }: SubmissionSuccessProps) {
  const { width, height } = useWindowSize();

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-green-50 to-blue-50 p-4">
      <Confetti
        width={width}
        height={height}
        recycle={false}
        numberOfPieces={200}
        gravity={0.3}
      />
      
      <motion.div
        initial={{ opacity: 0, scale: 0.8, y: 50 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="w-full max-w-md"
      >
        <Card className="border-0 bg-white/90 shadow-2xl backdrop-blur-sm">
          <CardContent className="p-12 text-center">
            {/* Success Icon */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
              className="mx-auto mb-6 flex size-20 items-center justify-center rounded-full bg-green-100"
            >
              <Icons.check className="size-10 text-green-600" />
            </motion.div>

            {/* Success Message */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <h1 className="mb-4 text-3xl font-bold text-gray-900">
                Thank You!
              </h1>
              
              <p className="mb-2 text-lg text-gray-600">
                Your submission has been received successfully.
              </p>
              
              {organization && (
                <p className="mb-6 text-gray-500">
                  {organization.name} will review your information and be in touch soon.
                </p>
              )}
              
              <div className="mb-6 rounded-lg bg-gray-50 p-4">
                <p className="mb-1 text-sm text-gray-600">Submission ID</p>
                <p className="break-all font-mono text-sm text-gray-900">
                  {submissionId}
                </p>
              </div>
            </motion.div>

            {/* Actions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
              className="space-y-3"
            >
              <Button
                onClick={() => window.print()}
                variant="outline"
                className="w-full"
              >
                <Icons.printer className="mr-2 size-4" />
                Print Confirmation
              </Button>
              
              <Button
                onClick={() => window.close()}
                className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
              >
                Close
              </Button>
            </motion.div>

            {/* Footer */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}
              className="mt-8 border-t border-gray-200 pt-6"
            >
              <p className="font-mono text-xs tracking-wider text-gray-400">
                POWERED BY{' '}
                <a
                  href="https://tractionx.ai"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="transition-colors hover:text-gray-600"
                >
                  TRACTIONX
                </a>
              </p>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
