"""
News Intelligence Tool for TractionX AI Agent system.
Orchestrates multiple search APIs to provide comprehensive, real-time news intelligence.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from pydantic import BaseModel, Field
import re
from difflib import SequenceMatcher
from bson import ObjectId
from ai.tools.base import BaseTool, ToolInput, ToolOutput, tool
from ai.tools.websearch import WebSearchTool, WebSearchInput, SearchResult
from ai.models import AgentContext
from ai.config import settings

# OpenAI imports for LLM functionality
from openai import AsyncOpenAI
import json

logger = logging.getLogger(__name__)


class NewsIntelligenceInput(ToolInput):
    """Input for news intelligence tool."""
    query: str = Field(..., description="Search query for news intelligence")
    max_sources: int = Field(default=3, description="Maximum number of sources to query")
    time_range: str = Field(default="7d", description="Time range for news search")
    include_analysis: bool = Field(default=True, description="Include AI analysis and summarization")
    priority_sources: Optional[List[str]] = Field(None, description="Priority sources to use first")


class NewsSection(BaseModel):
    """A section of categorized news results."""
    title: str = Field(..., description="Section title")
    articles: List[SearchResult] = Field(default_factory=list, description="Articles in this section")
    summary: Optional[str] = Field(None, description="AI-generated summary of the section")


class NewsIntelligenceOutput(ToolOutput):
    """Output from news intelligence tool."""
    query: str = Field(..., description="Original search query")
    total_articles: int = Field(default=0, description="Total number of articles found")
    sources_used: List[str] = Field(default_factory=list, description="Sources that were queried")
    sections: List[NewsSection] = Field(default_factory=list, description="Categorized news sections")
    executive_summary: Optional[str] = Field(None, description="Overall executive summary")
    search_time: float = Field(default=0.0, description="Time taken for search in seconds")
    last_updated: datetime = Field(default_factory=datetime.now, description="When the search was performed")


@tool(
    name="news_intelligence",
    description="Advanced news intelligence with multi-source search, deduplication, and AI analysis",
    category="intelligence",
    tags=["news", "analysis", "sentiment", "trends", "ai", "intelligence"]
)
class NewsIntelligenceTool(BaseTool):
    """
    Advanced news intelligence tool that orchestrates multiple search APIs
    to provide comprehensive, real-time news analysis.

    Features:
    - Parallel search across multiple APIs
    - Intelligent deduplication
    - AI-powered categorization and summarization
    - Source credibility scoring
    - Real-time news intent detection
    """
    
    # Keywords that trigger news intelligence mode
    NEWS_INTENT_KEYWORDS = [
        "news", "latest", "recent", "breaking", "announced", "funding", "raised",
        "launched", "acquired", "merger", "ipo", "investment", "round", "series",
        "development", "update", "report", "press", "release", "statement"
    ]
    
    # High-credibility news sources
    CREDIBLE_SOURCES = {
        "Reuters", "Associated Press", "Bloomberg", "Wall Street Journal", 
        "Financial Times", "TechCrunch", "VentureBeat", "Forbes", "Fortune",
        "Business Insider", "The Information", "Axios", "Crunchbase News"
    }
    
    def __init__(self):
        super().__init__()
        self.web_search_tool = WebSearchTool()
        
        # Initialize OpenAI client for summarization
        self.openai_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
    
    async def execute(
        self,
        input_data: NewsIntelligenceInput,
        context: AgentContext
    ) -> NewsIntelligenceOutput:
        """Execute comprehensive news intelligence search."""
        
        start_time = datetime.now()
        logger.info(f"Starting news intelligence search for: {input_data.query}")
        
        try:
            # Step 1: Detect if this is a news-related query
            is_news_query = self._detect_news_intent(input_data.query)
            logger.debug(f"News intent detected: {is_news_query}")
            
            # Step 2: Get available sources and prioritize them
            available_sources = self._get_prioritized_sources(input_data.priority_sources)
            sources_to_use = available_sources[:input_data.max_sources]
            
            if not sources_to_use:
                return NewsIntelligenceOutput(
                    query=input_data.query,
                    success=False,
                    error="No available news sources configured"
                )
            
            logger.info(f"Using sources: {sources_to_use}")
            
            # Step 3: Execute parallel searches
            all_results = await self._parallel_search(
                input_data.query,
                sources_to_use,
                input_data.time_range
            )
            
            # Step 4: Deduplicate results
            deduplicated_results = self._deduplicate_articles(all_results)
            logger.info(f"Found {len(deduplicated_results)} unique articles after deduplication")
            
            # Step 5: Score and sort by credibility and relevance
            scored_results = self._score_articles(deduplicated_results, input_data.query)
            
            # Step 6: Categorize articles into sections
            sections = self._categorize_articles(scored_results)
            
            # Step 7: Generate AI summaries if requested
            if input_data.include_analysis:
                sections = await self._generate_summaries(sections, context)
                executive_summary = await self._generate_executive_summary(
                    input_data.query, sections, context
                )
            else:
                executive_summary = None
            
            search_time = (datetime.now() - start_time).total_seconds()
            
            return NewsIntelligenceOutput(
                query=input_data.query,
                total_articles=len(deduplicated_results),
                sources_used=sources_to_use,
                sections=sections,
                executive_summary=executive_summary,
                search_time=search_time,
                success=True
            )
            
        except Exception as e:
            logger.error(f"News intelligence search failed: {e}")
            return NewsIntelligenceOutput(
                query=input_data.query,
                success=False,
                error=str(e),
                search_time=(datetime.now() - start_time).total_seconds()
            )
    
    def _detect_news_intent(self, query: str) -> bool:
        """Detect if the query is asking for news/recent information."""
        query_lower = query.lower()
        return any(keyword in query_lower for keyword in self.NEWS_INTENT_KEYWORDS)
    
    def _get_prioritized_sources(self, priority_sources: Optional[List[str]] = None) -> List[str]:
        """Get available sources in priority order."""
        all_sources = ["newsapi", "bing_news", "google_news_rss", "serpapi", "gnews"]
        available_sources = [
            source for source in all_sources 
            if self.web_search_tool._has_api_key(source)
        ]
        
        if priority_sources:
            # Put priority sources first, then others
            prioritized = [s for s in priority_sources if s in available_sources]
            remaining = [s for s in available_sources if s not in prioritized]
            return prioritized + remaining
        
        return available_sources
    
    async def _parallel_search(
        self, 
        query: str, 
        sources: List[str], 
        time_range: str
    ) -> List[SearchResult]:
        """Execute searches across multiple sources in parallel."""
        
        search_tasks = []
        for source in sources:
            search_input = WebSearchInput(
                query=query,
                source=source,
                limit=10,  # Get more results per source for better coverage
                date_range=time_range,
                sort_by="publishedAt"
            )
            
            # Create task for each source
            task = self._safe_search(search_input)
            search_tasks.append(task)
        
        # Execute all searches in parallel with timeout
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*search_tasks, return_exceptions=True),
                timeout=settings.NEWS_SEARCH_TIMEOUT
            )
        except asyncio.TimeoutError:
            logger.warning(f"News search timed out after {settings.NEWS_SEARCH_TIMEOUT}s")
            results = []
        
        # Flatten results and filter out exceptions
        all_articles = []
        for result in results:
            if isinstance(result, list):
                all_articles.extend(result)
            elif isinstance(result, Exception):
                logger.warning(f"Search failed: {result}")
        
        return all_articles
    
    async def _safe_search(self, search_input: WebSearchInput) -> List[SearchResult]:
        """Execute a single search with error handling."""
        try:
            # Create a dummy context for the web search
            context = AgentContext(
                org_id=ObjectId("683c4dca84d99b9ddd7ac39a"),
                user_id=ObjectId("683c4dca84d99b9ddd7ac39a"),
                session_id="temp"
            )
            
            result = await self.web_search_tool.execute(search_input, context)
            if result.success and result.data:
                return result.data.get("results", [])
            return []
        except Exception as e:
            logger.warning(f"Search failed for source {search_input.source}: {e}")
            return []

    def _deduplicate_articles(self, articles: List[SearchResult]) -> List[SearchResult]:
        """Remove duplicate articles based on title similarity."""
        if not articles:
            return []

        unique_articles = []
        seen_titles = set()

        for article in articles:
            # Skip articles without titles
            if not article.title:
                continue

            # Normalize title for comparison
            normalized_title = self._normalize_title(article.title)

            # Check for duplicates
            is_duplicate = False
            for seen_title in seen_titles:
                similarity = SequenceMatcher(None, normalized_title, seen_title).ratio()
                if similarity > settings.NEWS_DEDUP_THRESHOLD:
                    is_duplicate = True
                    break

            if not is_duplicate:
                unique_articles.append(article)
                seen_titles.add(normalized_title)

        return unique_articles

    def _normalize_title(self, title: str) -> str:
        """Normalize title for deduplication comparison."""
        # Remove common prefixes/suffixes and normalize
        title = re.sub(r'^(BREAKING|UPDATE|EXCLUSIVE):\s*', '', title, flags=re.IGNORECASE)
        title = re.sub(r'\s*-\s*[^-]+$', '', title)  # Remove source suffix
        title = title.lower().strip()
        return title

    def _score_articles(self, articles: List[SearchResult], query: str) -> List[SearchResult]:
        """Score articles by credibility and relevance."""
        query_terms = set(query.lower().split())

        for article in articles:
            score = 0.0

            # Credibility score (0-40 points)
            if article.source in self.CREDIBLE_SOURCES:
                score += 40
            elif article.source:
                score += 20  # Some source is better than no source

            # Recency score (0-30 points)
            if article.published_at:
                days_ago = (datetime.now() - article.published_at).days
                if days_ago <= 1:
                    score += 30
                elif days_ago <= 7:
                    score += 20
                elif days_ago <= 30:
                    score += 10

            # Relevance score (0-30 points)
            title_lower = article.title.lower() if article.title else ""
            desc_lower = article.description.lower() if article.description else ""

            title_matches = sum(1 for term in query_terms if term in title_lower)
            desc_matches = sum(1 for term in query_terms if term in desc_lower)

            relevance = (title_matches * 2 + desc_matches) / len(query_terms) if query_terms else 0
            score += min(relevance * 30, 30)

            # Store score in metadata
            if not hasattr(article, 'metadata'):
                article.metadata = {}
            article.metadata['score'] = score

        # Sort by score (highest first)
        return sorted(articles, key=lambda x: x.metadata.get('score', 0), reverse=True)

    def _categorize_articles(self, articles: List[SearchResult]) -> List[NewsSection]:
        """Categorize articles into logical sections."""

        # Define categories with keywords
        categories = {
            "Funding & Investment": [
                "funding", "raised", "investment", "round", "series", "venture",
                "capital", "investor", "valuation", "ipo", "acquisition"
            ],
            "Product & Technology": [
                "launched", "release", "product", "feature", "technology", "platform",
                "update", "version", "beta", "ai", "software"
            ],
            "Business & Strategy": [
                "partnership", "merger", "acquisition", "expansion", "growth",
                "strategy", "market", "revenue", "profit", "loss"
            ],
            "Leadership & People": [
                "ceo", "founder", "executive", "hired", "appointed", "resignation",
                "team", "leadership", "management"
            ],
            "Market & Industry": [
                "market", "industry", "sector", "trend", "analysis", "report",
                "competition", "competitor"
            ]
        }

        sections = {}
        uncategorized = []

        for article in articles:
            categorized = False
            text_to_analyze = f"{article.title} {article.description}".lower()

            for category, keywords in categories.items():
                if any(keyword in text_to_analyze for keyword in keywords):
                    if category not in sections:
                        sections[category] = NewsSection(title=category)
                    sections[category].articles.append(article)
                    categorized = True
                    break

            if not categorized:
                uncategorized.append(article)

        # Add uncategorized articles to "Other News" section
        if uncategorized:
            sections["Other News"] = NewsSection(title="Other News", articles=uncategorized)

        return list(sections.values())

    async def _generate_summaries(
        self,
        sections: List[NewsSection],
        context: AgentContext
    ) -> List[NewsSection]:
        """Generate AI summaries for each section."""
        logger.debug(f"Generating AI summaries for {len(sections)} sections")
        
        try:
            # Process each section
            for section in sections:
                if not section.articles:
                    continue
                
                logger.debug(f"Generating summary for section: {section.title} ({len(section.articles)} articles)")
                
                # Prepare article data for summarization
                articles_text = []
                for i, article in enumerate(section.articles[:5]):  # Limit to top 5 articles per section
                    article_text = f"Article {i+1}:\n"
                    article_text += f"Title: {article.title}\n"
                    if article.description:
                        article_text += f"Description: {article.description}\n"
                    if article.source:
                        article_text += f"Source: {article.source}\n"
                    if article.published_at:
                        article_text += f"Published: {article.published_at.strftime('%Y-%m-%d %H:%M')}\n"
                    articles_text.append(article_text)
                
                combined_text = "\n\n".join(articles_text)
                
                # Create summarization prompt
                prompt = f"""You are an expert news analyst. Analyze the following articles from the "{section.title}" category and provide a concise, informative summary.

Focus on:
- Key developments and trends
- Important companies, people, or events mentioned
- Significant financial figures (funding amounts, valuations, etc.)
- Market implications or business impact

Articles to analyze:
{combined_text}

Please provide a clear, professional summary in 2-3 sentences that captures the most important information from these articles."""

                try:
                    # Call OpenAI API
                    response = await self.openai_client.chat.completions.create(
                        model=settings.OPENAI_MODEL,
                        messages=[
                            {"role": "system", "content": "You are an expert business and technology news analyst."},
                            {"role": "user", "content": prompt}
                        ],
                        max_tokens=200,
                        temperature=0.3,  # Lower temperature for more factual summaries
                        timeout=30
                    )
                    
                    if response.choices and response.choices[0].message:
                        section.summary = response.choices[0].message.content.strip()
                        logger.debug(f"Generated summary for {section.title}: {len(section.summary)} characters")
                    else:
                        logger.warning(f"Empty response from OpenAI for section {section.title}")
                        section.summary = self._generate_fallback_summary(section)
                        
                except Exception as api_error:
                    logger.warning(f"OpenAI API error for section {section.title}: {api_error}")
                    section.summary = self._generate_fallback_summary(section)
                
                # Small delay to avoid rate limiting
                await asyncio.sleep(0.1)
        
        except Exception as e:
            logger.error(f"Error generating summaries: {e}")
            # Provide fallback summaries for all sections
            for section in sections:
                if not section.summary and section.articles:
                    section.summary = self._generate_fallback_summary(section)
        
        return sections
    
    def _generate_fallback_summary(self, section: NewsSection) -> str:
        """Generate a basic summary when AI summarization fails."""
        article_count = len(section.articles)
        
        if article_count == 0:
            return f"No articles found in {section.title}."
        
        # Extract key information from articles
        sources = set()
        recent_count = 0
        
        for article in section.articles:
            if article.source:
                sources.add(article.source)
            if article.published_at and (datetime.now() - article.published_at).days <= 1:
                recent_count += 1
        
        summary_parts = [f"Found {article_count} articles in {section.title}"]
        
        if recent_count > 0:
            summary_parts.append(f"including {recent_count} from the last 24 hours")
        
        if sources:
            top_sources = list(sources)[:3]
            summary_parts.append(f"from sources including {', '.join(top_sources)}")
        
        return ". ".join(summary_parts) + "."

    async def _generate_executive_summary(
        self,
        query: str,
        sections: List[NewsSection],
        context: AgentContext
    ) -> str:
        """Generate an executive summary of all findings."""
        logger.debug(f"Generating executive summary for query: '{query}' with {len(sections)} sections")
        
        try:
            if not sections:
                return f"No news articles found for '{query}'."
            
            total_articles = sum(len(section.articles) for section in sections)
            
            # Prepare section summaries for analysis
            section_data = []
            for section in sections:
                if section.articles:
                    section_info = {
                        "category": section.title,
                        "article_count": len(section.articles),
                        "summary": section.summary or "No summary available",
                        "top_sources": list(set([
                            article.source for article in section.articles[:3] 
                            if article.source
                        ]))
                    }
                    section_data.append(section_info)
            
            # Create comprehensive analysis prompt
            sections_text = ""
            for i, section_info in enumerate(section_data, 1):
                sections_text += f"{i}. {section_info['category']} ({section_info['article_count']} articles)\n"
                sections_text += f"   Summary: {section_info['summary']}\n"
                if section_info['top_sources']:
                    sections_text += f"   Sources: {', '.join(section_info['top_sources'])}\n"
                sections_text += "\n"
            
            prompt = f"""You are an expert business intelligence analyst. Create a comprehensive executive summary for the search query: "{query}"

Based on the following news analysis across {len(sections)} categories with {total_articles} total articles:

{sections_text}

Please provide an executive summary that includes:

1. **Key Highlights**: The most important developments or trends
2. **Market Impact**: Business implications and significance 
3. **Notable Details**: Important companies, people, funding amounts, or strategic moves
4. **Summary**: Overall assessment and what this means for stakeholders

Keep the summary professional, concise (4-6 sentences), and focus on actionable intelligence. Prioritize the most significant and recent developments."""

            try:
                # Call OpenAI API for executive summary
                response = await self.openai_client.chat.completions.create(
                    model=settings.OPENAI_MODEL,
                    messages=[
                        {"role": "system", "content": "You are an expert business intelligence analyst specializing in market research and strategic analysis."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=400,
                    temperature=0.2,  # Very low temperature for factual analysis
                    timeout=30
                )
                
                if response.choices and response.choices[0].message:
                    executive_summary = response.choices[0].message.content.strip()
                    logger.debug(f"Generated executive summary: {len(executive_summary)} characters")
                    return executive_summary
                else:
                    logger.warning("Empty response from OpenAI for executive summary")
                    return self._generate_fallback_executive_summary(query, sections)
                    
            except Exception as api_error:
                logger.warning(f"OpenAI API error for executive summary: {api_error}")
                return self._generate_fallback_executive_summary(query, sections)
        
        except Exception as e:
            logger.error(f"Error generating executive summary: {e}")
            return self._generate_fallback_executive_summary(query, sections)
    
    def _generate_fallback_executive_summary(
        self, 
        query: str, 
        sections: List[NewsSection]
    ) -> str:
        """Generate a basic executive summary when AI generation fails."""
        if not sections:
            return f"No news articles found for '{query}'."
        
        total_articles = sum(len(section.articles) for section in sections)
        section_names = [section.title for section in sections if section.articles]
        
        # Find most recent articles
        recent_articles = []
        for section in sections:
            for article in section.articles:
                if article.published_at and (datetime.now() - article.published_at).days <= 1:
                    recent_articles.append(article)
        
        # Extract unique sources
        all_sources = set()
        for section in sections:
            for article in section.articles:
                if article.source:
                    all_sources.add(article.source)
        
        summary_parts = [
            f"News intelligence analysis for '{query}' found {total_articles} articles across {len(section_names)} categories: {', '.join(section_names[:3])}{'...' if len(section_names) > 3 else ''}."
        ]
        
        if recent_articles:
            summary_parts.append(f"Analysis includes {len(recent_articles)} recent articles from the last 24 hours, indicating active developments.")
        
        if all_sources:
            credible_sources = [s for s in all_sources if s in self.CREDIBLE_SOURCES]
            if credible_sources:
                summary_parts.append(f"Coverage includes reports from credible sources such as {', '.join(list(credible_sources)[:3])}.")
        
        summary_parts.append("This analysis provides a comprehensive view of recent developments and market activity in the specified area.")
        
        return " ".join(summary_parts)
