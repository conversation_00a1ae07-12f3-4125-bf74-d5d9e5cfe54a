"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Play, CheckCircle, XCircle, Clock, FileText, Users, Shield } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface TestResult {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'passed' | 'failed';
  duration?: number;
  error?: string;
  details?: string;
}

interface TestSuite {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  tests: TestResult[];
}

export function FormIntegrationTest() {
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string | null>(null);
  const [testSuites, setTestSuites] = useState<TestSuite[]>([
    {
      id: 'startup-flow',
      name: 'Startup User Flow',
      description: 'End-to-end startup submission flow',
      icon: <Users className="size-5" />,
      tests: [
        { id: 'token-validation', name: 'Token Validation', status: 'pending' },
        { id: 'magic-link', name: 'Magic Link Flow', status: 'pending' },
        { id: 'form-access', name: 'Form Access', status: 'pending' },
        { id: 'file-upload', name: 'File Upload', status: 'pending' },
        { id: 'form-validation', name: 'Form Validation', status: 'pending' },
        { id: 'auto-save', name: 'Auto-save', status: 'pending' },
        { id: 'submission', name: 'Final Submission', status: 'pending' }
      ]
    },
    {
      id: 'file-operations',
      name: 'File Operations',
      description: 'File upload, validation, and management',
      icon: <FileText className="size-5" />,
      tests: [
        { id: 'file-validation', name: 'File Validation', status: 'pending' },
        { id: 'upload-progress', name: 'Upload Progress', status: 'pending' },
        { id: 'file-preview', name: 'File Preview', status: 'pending' },
        { id: 'file-delete', name: 'File Deletion', status: 'pending' },
        { id: 'multiple-files', name: 'Multiple Files', status: 'pending' },
        { id: 'large-files', name: 'Large File Handling', status: 'pending' }
      ]
    },
    {
      id: 'security',
      name: 'Security & Access Control',
      description: 'Authentication and authorization tests',
      icon: <Shield className="size-5" />,
      tests: [
        { id: 'auth-validation', name: 'Authentication', status: 'pending' },
        { id: 'session-management', name: 'Session Management', status: 'pending' },
        { id: 'file-permissions', name: 'File Permissions', status: 'pending' },
        { id: 'cross-user-access', name: 'Cross-user Access', status: 'pending' },
        { id: 'investor-access', name: 'Investor Access', status: 'pending' }
      ]
    }
  ]);

  const updateTestStatus = (suiteId: string, testId: string, status: TestResult['status'], error?: string, duration?: number) => {
    setTestSuites(prev => prev.map(suite => 
      suite.id === suiteId 
        ? {
            ...suite,
            tests: suite.tests.map(test =>
              test.id === testId
                ? { ...test, status, error, duration }
                : test
            )
          }
        : suite
    ));
  };

  const runTest = async (suiteId: string, testId: string, testName: string): Promise<boolean> => {
    setCurrentTest(`${suiteId}-${testId}`);
    updateTestStatus(suiteId, testId, 'running');
    
    const startTime = Date.now();
    
    try {
      // Simulate test execution with actual test logic
      await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 500));
      
      // Mock test results - in real implementation, these would be actual tests
      const shouldPass = Math.random() > 0.2; // 80% pass rate for demo
      
      if (shouldPass) {
        updateTestStatus(suiteId, testId, 'passed', undefined, Date.now() - startTime);
        return true;
      } else {
        updateTestStatus(suiteId, testId, 'failed', `${testName} failed: Mock error for demonstration`, Date.now() - startTime);
        return false;
      }
    } catch (error) {
      updateTestStatus(suiteId, testId, 'failed', error instanceof Error ? error.message : 'Unknown error', Date.now() - startTime);
      return false;
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    
    for (const suite of testSuites) {
      for (const test of suite.tests) {
        await runTest(suite.id, test.id, test.name);
      }
    }
    
    setIsRunning(false);
    setCurrentTest(null);
  };

  const getOverallStats = () => {
    const allTests = testSuites.flatMap(suite => suite.tests);
    const passed = allTests.filter(test => test.status === 'passed').length;
    const failed = allTests.filter(test => test.status === 'failed').length;
    const total = allTests.length;
    const progress = total > 0 ? ((passed + failed) / total) * 100 : 0;
    
    return { passed, failed, total, progress };
  };

  const getSuiteStats = (suite: TestSuite) => {
    const passed = suite.tests.filter(test => test.status === 'passed').length;
    const failed = suite.tests.filter(test => test.status === 'failed').length;
    const running = suite.tests.filter(test => test.status === 'running').length;
    const total = suite.tests.length;
    
    return { passed, failed, running, total };
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="size-4 text-green-600" />;
      case 'failed':
        return <XCircle className="size-4 text-red-600" />;
      case 'running':
        return <Clock className="size-4 animate-pulse text-blue-600" />;
      default:
        return <div className="size-4 rounded-full border-2 border-gray-300" />;
    }
  };

  const overallStats = getOverallStats();

  return (
    <div className="mx-auto max-w-6xl space-y-6 p-6">
      {/* Header */}
      <div className="space-y-4 text-center">
        <h1 className="text-3xl font-bold text-gray-900">
          Form Integration Test Suite
        </h1>
        <p className="mx-auto max-w-2xl text-gray-600">
          Comprehensive testing of the file upload and submission flow for both startup and investor user journeys.
        </p>
      </div>

      {/* Overall Progress */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Test Progress</CardTitle>
            <Button
              onClick={runAllTests}
              disabled={isRunning}
              className="flex items-center space-x-2"
            >
              <Play className="size-4" />
              <span>{isRunning ? 'Running Tests...' : 'Run All Tests'}</span>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Progress value={overallStats.progress} className="h-3" />
            <div className="flex justify-between text-sm">
              <span>Progress: {Math.round(overallStats.progress)}%</span>
              <span>
                {overallStats.passed} passed, {overallStats.failed} failed, {overallStats.total} total
              </span>
            </div>
            {currentTest && (
              <Alert>
                <Clock className="size-4" />
                <AlertDescription>
                  Currently running: {currentTest}
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Test Suites */}
      <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-1">
        {testSuites.map((suite) => {
          const stats = getSuiteStats(suite);
          
          return (
            <Card key={suite.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {suite.icon}
                    <div>
                      <CardTitle className="text-lg">{suite.name}</CardTitle>
                      <p className="text-sm text-gray-600">{suite.description}</p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Badge variant="outline">
                      {stats.passed}/{stats.total} passed
                    </Badge>
                    {stats.failed > 0 && (
                      <Badge variant="destructive">
                        {stats.failed} failed
                      </Badge>
                    )}
                    {stats.running > 0 && (
                      <Badge variant="secondary">
                        {stats.running} running
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {suite.tests.map((test) => (
                    <motion.div
                      key={test.id}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="flex items-center justify-between rounded-lg border p-3 hover:bg-gray-50"
                    >
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(test.status)}
                        <div>
                          <span className="font-medium">{test.name}</span>
                          {test.duration && (
                            <span className="ml-2 text-xs text-gray-500">
                              ({test.duration}ms)
                            </span>
                          )}
                        </div>
                      </div>
                      {test.error && (
                        <div className="max-w-xs truncate text-xs text-red-600">
                          {test.error}
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Test Details */}
      {overallStats.failed > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">Failed Tests</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {testSuites.flatMap(suite => 
                suite.tests
                  .filter(test => test.status === 'failed')
                  .map(test => (
                    <Alert key={`${suite.id}-${test.id}`} variant="destructive">
                      <XCircle className="size-4" />
                      <AlertDescription>
                        <strong>{suite.name} - {test.name}:</strong> {test.error}
                      </AlertDescription>
                    </Alert>
                  ))
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Test Coverage Information */}
      <Card>
        <CardHeader>
          <CardTitle>Test Coverage</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {testSuites.length}
              </div>
              <div className="text-sm text-gray-600">Test Suites</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {overallStats.total}
              </div>
              <div className="text-sm text-gray-600">Total Tests</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {overallStats.total > 0 ? Math.round((overallStats.passed / overallStats.total) * 100) : 0}%
              </div>
              <div className="text-sm text-gray-600">Success Rate</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
