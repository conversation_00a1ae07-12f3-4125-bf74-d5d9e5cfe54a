"use client"

import { useState } from "react"
import { useRouter, useSearch<PERSON>ara<PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { motion } from "framer-motion"
import * as z from "zod"
import Link from "next/link"

import { cn } from "@/lib/utils"
import { visualRetreat } from "@/lib/utils/responsive"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { Icons } from "@/components/icons"
import { useAuth } from "@/lib/auth-context"
import { AuthCard } from "./auth-card"
import { AuthAPI } from "@/lib/api/auth-api"

const acceptInvitationSchema = z.object({
  password: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string().min(8, "Please confirm your password"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type AcceptInvitationFormValues = z.infer<typeof acceptInvitationSchema>

export function RegisterForm() {
  const auth = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)
  
  // Get invitation token from URL params
  const token = searchParams?.get('token')

  const form = useForm<AcceptInvitationFormValues>({
    resolver: zodResolver(acceptInvitationSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  })

  async function onSubmit(data: AcceptInvitationFormValues) {
    if (!token) {
      toast({
        title: "Invalid invitation",
        description: "No invitation token found. Please check your invitation link.",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)

    try {
      // Use the AuthAPI for consistent API handling
      await AuthAPI.acceptInvitation(token, data.password, data.confirmPassword)
      
      toast({
        title: "Invitation accepted!",
        description: "Your account has been successfully activated. You can now sign in.",
      })
      
      // Redirect to login page
      router.push("/login")
    } catch (error) {
      console.error("Invitation acceptance error:", error)
      toast({
        title: "Failed to accept invitation",
        description: error instanceof Error ? error.message : "Please check your invitation and try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Show error if no token is provided
  if (!token) {
    return (
      <AuthCard>
        <div className="space-y-4 text-center">
          <div className="text-red-500">
            <Icons.alertCircle className="mx-auto mb-4 size-12" />
            <h3 className="text-lg font-semibold">Invalid Invitation Link</h3>
            <p className="text-sm text-gray-600">
              No invitation token found. Please check your invitation email and use the correct link.
            </p>
          </div>
          <Link
            href="/login"
            className="inline-flex h-10 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground ring-offset-background transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
          >
            Go to Login
          </Link>
        </div>
      </AuthCard>
    )
  }

  return (
    <AuthCard>
      <form onSubmit={form.handleSubmit(onSubmit)} className={visualRetreat.form.container}>
        <div className="space-y-6">
          {/* Password Field */}
          <div className={visualRetreat.form.field}>
            <Label htmlFor="password" className="text-sm font-medium text-gray-700">
              Create Password
            </Label>
            <Input
              id="password"
              type="password"
              placeholder="Create a strong password"
              autoComplete="new-password"
              disabled={isLoading}
              className={cn(
                visualRetreat.form.input,
                "border-white/20 bg-white/50 focus:border-blue-500/50 focus:ring-blue-500/20"
              )}
              {...form.register("password")}
            />
            {form.formState.errors.password && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.password.message}
              </motion.p>
            )}
          </div>

          {/* Confirm Password Field */}
          <div className={visualRetreat.form.field}>
            <Label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
              Confirm Password
            </Label>
            <Input
              id="confirmPassword"
              type="password"
              placeholder="Confirm your password"
              autoComplete="new-password"
              disabled={isLoading}
              className={cn(
                visualRetreat.form.input,
                "border-white/20 bg-white/50 focus:border-blue-500/50 focus:ring-blue-500/20"
              )}
              {...form.register("confirmPassword")}
            />
            {form.formState.errors.confirmPassword && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.confirmPassword.message}
              </motion.p>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          disabled={isLoading}
          className={cn(
            visualRetreat.form.button,
            "bg-gradient-to-r from-blue-600 to-purple-600 font-medium text-white shadow-lg shadow-blue-500/25 hover:from-blue-700 hover:to-purple-700"
          )}
        >
          {isLoading ? (
            <>
              <Icons.spinner className="mr-2 size-4 animate-spin" />
              Accepting invitation...
            </>
          ) : (
            "Accept Invitation"
          )}
        </Button>

        {/* Sign In Link */}
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            Already have an account?{" "}
            <Link
              href="/login"
              className="font-medium text-blue-600 transition-colors hover:text-blue-500"
            >
              Sign in
            </Link>
          </p>
        </div>
      </form>
    </AuthCard>
  )
}
