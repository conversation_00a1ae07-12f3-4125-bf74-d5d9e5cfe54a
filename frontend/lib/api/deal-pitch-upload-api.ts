/**
 * API client for handling pitch deck uploads to existing deals
 */

import { API_BASE_URL, createAuthHeaders } from "./auth-api";

export interface DealPitchUploadRequest {
  type: "deck" | "one_pager";
  filename: string;
  content_type: string;
  file_size: number;
}

export interface DealPitchUploadResponse {
  temp_id: string;
  presigned_url: string;
  s3_key: string;
  status: string;
  expires_in: number;
}

export interface ConfirmDealPitchUploadRequest {
  temp_id: string;
}

export interface ConfirmDealPitchUploadResponse {
  temp_id: string;
  status: string;
  message: string;
  job: {
    id: string;
    type: string;
    payload: Record<string, any>;
    status: string;
    priority: string;
    queue: string;
    created_at: number;
    updated_at: number;
    scheduled_for: number | null;
    started_at: number | null;
    completed_at: number | null;
    failed_at: number | null;
    attempts: number;
    max_attempts: number;
    retry_strategy: string;
    base_delay: number;
    max_delay: number;
    next_retry_at: number | null;
    result: any;
    error: string | null;
    metadata: {
      source: string | null;
      correlation_id: string | null;
      tags: string[];
      custom: Record<string, any>;
    };
  };
}

export class DealPitchUploadAPI {
  /**
   * Generate presigned URL for pitch upload to existing deal
   */
  static async generatePitchUploadUrl(
    dealId: string,
    request: DealPitchUploadRequest,
    token: string,
    orgId: string
  ): Promise<DealPitchUploadResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/uploads/deals/${dealId}/pitch`, {
        method: 'POST',
        headers: createAuthHeaders(token, orgId),
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to generate pitch upload URL');
      }

      return response.json();
    } catch (error) {
      console.error('Generate deal pitch upload URL error:', error);
      throw error;
    }
  }

  /**
   * Upload file to S3 using presigned URL
   */
  static async uploadPitchFile(
    file: File,
    presignedUrl: string,
    onProgress?: (progress: number) => void
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      // Track upload progress
      if (onProgress) {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = (event.loaded / event.total) * 100;
            onProgress(progress);
          }
        });
      }

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve();
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed'));
      });

      xhr.addEventListener('abort', () => {
        reject(new Error('Upload aborted'));
      });

      xhr.open('PUT', presignedUrl);
      xhr.setRequestHeader('Content-Type', file.type);
      xhr.setRequestHeader('Content-Length', file.size.toString());
      xhr.send(file);
    });
  }

  /**
   * Confirm pitch upload and trigger processing for existing deal
   */
  static async confirmPitchUpload(
    dealId: string,
    request: ConfirmDealPitchUploadRequest,
    token: string,
    orgId: string
  ): Promise<ConfirmDealPitchUploadResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/uploads/deals/${dealId}/pitch/confirm`, {
        method: 'POST',
        headers: createAuthHeaders(token, orgId),
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to confirm pitch upload');
      }

      return response.json();
    } catch (error) {
      console.error('Confirm deal pitch upload error:', error);
      throw error;
    }
  }

  /**
   * Complete pitch upload process for existing deal (generate URL, upload, confirm)
   */
  static async uploadPitchComplete(
    dealId: string,
    file: File,
    token: string,
    orgId: string,
    onProgress?: (progress: number, status: string) => void
  ): Promise<ConfirmDealPitchUploadResponse> {
    try {
      // Step 1: Generate presigned upload URL
      onProgress?.(0, 'preparing');
      
      const uploadRequest: DealPitchUploadRequest = {
        type: 'deck', // Default to deck, could be made configurable
        filename: file.name,
        content_type: file.type,
        file_size: file.size
      };

      const uploadResponse = await this.generatePitchUploadUrl(dealId, uploadRequest, token, orgId);

      // Step 2: Upload file to S3
      onProgress?.(0, 'uploading');
      await this.uploadPitchFile(
        file,
        uploadResponse.presigned_url,
        (progress) => {
          onProgress?.(progress, 'uploading');
        }
      );

      // Step 3: Confirm upload
      onProgress?.(100, 'processing');
      const confirmResponse = await this.confirmPitchUpload(
        dealId,
        { temp_id: uploadResponse.temp_id },
        token,
        orgId
      );

      // Return both the confirm response and the job info for polling
      onProgress?.(100, 'completed');
      return {
        ...confirmResponse,
        temp_id: uploadResponse.temp_id
      };

    } catch (error) {
      onProgress?.(0, 'error');
      throw error;
    }
  }
} 