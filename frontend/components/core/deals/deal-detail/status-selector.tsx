"use client"

import React, { useState } from 'react'
import { Check, ChevronsUpDown } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/use-toast'
import { Deal, DealStatus } from '@/lib/types/deal'
import { DealAPI } from '@/lib/api/deal-api'

interface StatusSelectorProps {
  deal: Deal
  onDealUpdate: (updatedDeal: Deal) => void
  className?: string
}

// Status configuration with colors and labels
const STATUS_CONFIG = {
  [DealStatus.NEW]: {
    label: 'New',
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    dotColor: 'bg-blue-500'
  },
  [DealStatus.TRIAGE]: {
    label: 'Triage',
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    dotColor: 'bg-yellow-500'
  },
  [DealStatus.REVIEWED]: {
    label: 'Reviewed',
    color: 'bg-green-100 text-green-800 border-green-200',
    dotColor: 'bg-green-500'
  },
  [DealStatus.EXCLUDED]: {
    label: 'Excluded',
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    dotColor: 'bg-gray-400'
  },
  [DealStatus.REJECTED]: {
    label: 'Rejected',
    color: 'bg-red-100 text-red-800 border-red-200',
    dotColor: 'bg-red-500'
  },
  [DealStatus.APPROVED]: {
    label: 'Approved',
    color: 'bg-emerald-100 text-emerald-800 border-emerald-200',
    dotColor: 'bg-emerald-500'
  },
  [DealStatus.NEGOTIATING]: {
    label: 'Negotiating',
    color: 'bg-indigo-100 text-indigo-800 border-indigo-200',
    dotColor: 'bg-indigo-500'
  },
  [DealStatus.CLOSED]: {
    label: 'Closed',
    color: 'bg-zinc-100 text-zinc-800 border-zinc-200',
    dotColor: 'bg-zinc-500'
  },
}

export function StatusSelector({ deal, onDealUpdate, className }: StatusSelectorProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [note, setNote] = useState('')
  const [showNoteInput, setShowNoteInput] = useState(false)
  const [pendingStatus, setPendingStatus] = useState<DealStatus | null>(null)
  const { toast } = useToast()

  const currentStatus = deal.status
  const currentConfig = STATUS_CONFIG[currentStatus]

  const handleStatusSelect = (status: DealStatus) => {
    if (status === currentStatus) {
      setOpen(false)
      return
    }

    setPendingStatus(status)
    setShowNoteInput(true)
    setNote('')
  }

  const handleStatusUpdate = async () => {
    if (!pendingStatus || loading) return

    setLoading(true)
    try {
      const updatedDeal = await DealAPI.updateDealStatus(
        deal.id, 
        pendingStatus, 
        note.trim() || undefined
      )
      
      onDealUpdate(updatedDeal)
      setOpen(false)
      setShowNoteInput(false)
      setPendingStatus(null)
      setNote('')
      
      const statusLabel = STATUS_CONFIG[pendingStatus].label
      toast({
        title: "Status Updated",
        description: `Deal status changed to ${statusLabel}`,
      })
    } catch (error) {
      console.error('Failed to update status:', error)
      toast({
        title: "Error",
        description: "Failed to update deal status",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setShowNoteInput(false)
    setPendingStatus(null)
    setNote('')
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <span className="hidden text-sm font-medium text-muted-foreground sm:block">
        Status
      </span>
      
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="h-9 min-w-[120px] justify-between transition-colors hover:bg-muted/50 sm:min-w-[140px]"
            disabled={loading}
          >
            <div className="flex items-center gap-2">
              <div className={cn("size-2 rounded-full", currentConfig.dotColor)} />
              <Badge 
                variant="secondary" 
                className={cn("border text-xs font-medium", currentConfig.color)}
              >
                {currentConfig.label}
              </Badge>
            </div>
            <ChevronsUpDown className="ml-2 size-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        
        <PopoverContent className="w-[320px] p-0" align="start">
          {!showNoteInput ? (
            <Command>
              <CommandInput placeholder="Search status..." />
              <CommandEmpty>No status found.</CommandEmpty>
              <CommandGroup>
                {Object.entries(STATUS_CONFIG).map(([status, config]) => (
                  <CommandItem
                    key={status}
                    value={config.label}
                    onSelect={() => handleStatusSelect(status as DealStatus)}
                    className="flex cursor-pointer items-center gap-2"
                  >
                    <div className={cn("size-2 rounded-full", config.dotColor)} />
                    <Badge 
                      variant="secondary" 
                      className={cn("border text-xs font-medium", config.color)}
                    >
                      {config.label}
                    </Badge>
                    <Check
                      className={cn(
                        "ml-auto size-4",
                        currentStatus === status ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            </Command>
          ) : (
            <div className="space-y-4 p-4">
              <div className="flex items-center gap-2">
                <div className={cn("size-2 rounded-full", STATUS_CONFIG[pendingStatus!].dotColor)} />
                <span className="font-medium">
                  Change status to {STATUS_CONFIG[pendingStatus!].label}
                </span>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="status-note" className="text-sm">
                  Add a note (optional)
                </Label>
                <Textarea
                  id="status-note"
                  placeholder="Explain the status change..."
                  value={note}
                  onChange={(e) => setNote(e.target.value)}
                  className="min-h-[80px] text-sm"
                  maxLength={1000}
                />
                <div className="text-right text-xs text-muted-foreground">
                  {note.length}/1000
                </div>
              </div>
              
              <div className="flex gap-2 pt-2">
                <Button
                  onClick={handleStatusUpdate}
                  disabled={loading}
                  className="flex-1"
                  size="sm"
                >
                  {loading ? "Updating..." : "Update Status"}
                </Button>
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={loading}
                  size="sm"
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </PopoverContent>
      </Popover>
    </div>
  )
}
