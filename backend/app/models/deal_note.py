"""
Deal Note models for TractionX deal note management.

This module defines models for tracking notes associated with deals,
including team mentions, versioning, and audit trails.
"""

from datetime import datetime, timezone
from typing import Annotated, Any, Dict, List, Optional

from pydantic import Field

from app.models.base import TractionXModel
from app.models.organization import Organization
from app.models.user import User
from app.utils.common import ObjectIdField, PyObjectId
from app.utils.model.helper import populate_reference


class DealNote(TractionXModel):
    """
    Represents a note associated with a deal.

    This model tracks notes from IC/internal meetings with:
    - Structured rich text content (Slate.js-style JSON)
    - Team mentions/tags
    - Pinning functionality
    - Versioned, attributed, and timestamped content
    - Soft deletion support
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")

    # Core relationships
    deal_id: Annotated[ObjectIdField, populate_reference("Deal")] = Field(
        ..., description="Linked Deal ID"
    )
    org_id: Annotated[ObjectIdField, populate_reference(Organization)] = Field(
        ..., description="Org who owns this note"
    )
    created_by: Annotated[ObjectIdField, populate_reference(User)] = Field(
        ..., description="User who created this note"
    )

    # Structured content
    structured_content: List[Dict[str, Any]] = Field(
        ...,
        description="Structured rich text content supporting mentions and formatting",
    )
    tagged_user_ids: List[Annotated[ObjectIdField, populate_reference(User)]] = Field(
        default_factory=list, description="Users mentioned/tagged in this note"
    )
    pinned: bool = Field(
        default=False, description="If note is pinned to top of notes list"
    )

    # Soft deletion
    deleted_at: Optional[int] = Field(
        default=None, description="Timestamp when note was soft-deleted"
    )
    deleted_by: Optional[Annotated[ObjectIdField, populate_reference(User)]] = Field(
        default=None, description="User who deleted this note"
    )

    # Timestamps
    created_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
    updated_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
    is_deleted: bool = Field(default=False, description="If note is soft-deleted")

    def soft_delete(self, deleted_by: ObjectIdField) -> None:
        """Soft delete the note."""
        self.deleted_at = int(datetime.now(timezone.utc).timestamp())
        self.deleted_by = deleted_by
        self.updated_at = int(datetime.now(timezone.utc).timestamp())

    def restore(self) -> None:
        """Restore a soft-deleted note."""
        self.deleted_at = None
        self.deleted_by = None
        self.updated_at = int(datetime.now(timezone.utc).timestamp())

    def update_structured_content(
        self,
        structured_content: List[Dict[str, Any]],
        tagged_user_ids: Optional[List[ObjectIdField]] = None,
    ) -> None:
        """Update the note structured content and tagged users."""
        self.structured_content = structured_content
        if tagged_user_ids is not None:
            self.tagged_user_ids = tagged_user_ids
        self.updated_at = int(datetime.now(timezone.utc).timestamp())

    def toggle_pinned(self) -> None:
        """Toggle the pinned status of the note."""
        self.pinned = not self.pinned
        self.updated_at = int(datetime.now(timezone.utc).timestamp())
