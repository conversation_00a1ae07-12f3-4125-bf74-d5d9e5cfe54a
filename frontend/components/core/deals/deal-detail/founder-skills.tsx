"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ChevronDown, ChevronRight, Zap, Target, TrendingUp, Users, Rocket, Plus } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { useState } from "react"
import clsx from "clsx"

interface Skill {
  name: string
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  years?: number
  category: string
}

interface SkillCategory {
  name: string
  icon: any
  skills: Skill[]
  color: string
}

interface FounderSkillsProps {
  skills: Skill[]
  className?: string
}

const skillCategories: { [key: string]: { name: string; icon: any; color: string } } = {
  tech: { name: 'Tech', icon: Zap, color: 'bg-slate-100 border-slate-200 text-slate-700' },
  product: { name: 'Product', icon: Target, color: 'bg-slate-100 border-slate-200 text-slate-700' },
  business: { name: 'Business', icon: TrendingUp, color: 'bg-slate-100 border-slate-200 text-slate-700' },
  operations: { name: 'Operations', icon: Users, color: 'bg-slate-100 border-slate-200 text-slate-700' },
  fundraising: { name: 'Fundraising', icon: Rocket, color: 'bg-slate-100 border-slate-200 text-slate-700' },
}

const getLevelColor = (level: string) => {
  switch (level) {
    case 'expert':
      return 'bg-slate-900 text-white'
    case 'advanced':
      return 'bg-slate-700 text-white'
    case 'intermediate':
      return 'bg-slate-500 text-white'
    case 'beginner':
      return 'bg-slate-300 text-slate-700'
    default:
      return 'bg-slate-100 text-slate-600'
  }
}

const getLevelText = (level: string) => {
  switch (level) {
    case 'expert':
      return 'Expert'
    case 'advanced':
      return 'Advanced'
    case 'intermediate':
      return 'Intermediate'
    case 'beginner':
      return 'Beginner'
    default:
      return 'Unknown'
  }
}

export function FounderSkills({ skills, className }: FounderSkillsProps) {
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['tech']))

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(category)) {
      newExpanded.delete(category)
    } else {
      newExpanded.add(category)
    }
    setExpandedCategories(newExpanded)
  }

  // Group skills by category
  const groupedSkills = skills.reduce((acc, skill) => {
    const category = skill.category.toLowerCase()
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(skill)
    return acc
  }, {} as { [key: string]: Skill[] })

  // Create category objects
  const categories: SkillCategory[] = Object.entries(groupedSkills).map(([key, skills]) => {
    const categoryInfo = skillCategories[key] || { name: key, icon: Plus, color: 'bg-slate-100 border-slate-200 text-slate-700' }
    return {
      name: categoryInfo.name,
      icon: categoryInfo.icon,
      skills: skills.sort((a, b) => {
        const levelOrder = { expert: 4, advanced: 3, intermediate: 2, beginner: 1 }
        return (levelOrder[b.level as keyof typeof levelOrder] || 0) - (levelOrder[a.level as keyof typeof levelOrder] || 0)
      }),
      color: categoryInfo.color
    }
  })

  if (!skills || skills.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Card className={clsx(
          "border border-slate-200 bg-white/90 backdrop-blur-sm shadow-none hover:shadow-md transition-shadow duration-200",
          className
        )}>
          <CardHeader className="border-b border-slate-100 pb-4">
            <div className="flex items-center gap-3">
              <div className="rounded-2xl border border-slate-200 bg-slate-50 p-2">
                <Zap className="size-4 text-slate-600" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-slate-900">
                  Skills & Expertise
                </CardTitle>
                <p className="mt-0.5 text-xs text-slate-500">
                  Technical and professional skills
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="py-8 text-center">
              <div className="mx-auto mb-4 flex size-16 items-center justify-center rounded-2xl border border-slate-200 bg-slate-50">
                <Zap className="size-8 text-slate-400" />
              </div>
              <p className="text-sm text-slate-500">No skills data available</p>
              <p className="mt-1 text-xs text-slate-400">Add founder skills to see expertise breakdown</p>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.4 }}
    >
      <Card className={clsx(
        "border border-slate-200 bg-white/90 backdrop-blur-sm shadow-none hover:shadow-md transition-shadow duration-200",
        className
      )}>
        <CardHeader className="border-b border-slate-100 pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="rounded-2xl border border-slate-200 bg-slate-50 p-2">
                <Zap className="size-4 text-slate-600" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-slate-900">
                  Skills & Expertise
                </CardTitle>
                <p className="mt-0.5 text-xs text-slate-500">
                  Technical and professional skills
                </p>
              </div>
            </div>
            
            <Badge variant="outline" className="border-slate-200 text-xs text-slate-600">
              {skills.length} skills
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent className="p-6">
          <div className="space-y-4">
            {categories.map((category, index) => {
              const isExpanded = expandedCategories.has(category.name.toLowerCase())
              const Icon = category.icon
              
              return (
                <motion.div
                  key={category.name}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                  className="overflow-hidden rounded-2xl border border-slate-200"
                >
                  {/* Category Header */}
                  <button
                    onClick={() => toggleCategory(category.name.toLowerCase())}
                    className="flex w-full items-center justify-between p-4 transition-colors duration-200 hover:bg-slate-50"
                  >
                    <div className="flex items-center gap-3">
                      <div className={clsx(
                        "p-2 rounded-2xl border",
                        category.color
                      )}>
                        <Icon className="size-4" />
                      </div>
                      <div className="text-left">
                        <h3 className="text-sm font-semibold text-slate-900">
                          {category.name}
                        </h3>
                        <p className="text-xs text-slate-500">
                          {category.skills.length} skill{category.skills.length !== 1 ? 's' : ''}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Badge 
                        variant="outline" 
                        className="rounded-full border-slate-200 bg-white px-2 py-1 text-xs text-slate-600"
                      >
                        {category.skills.filter(s => s.level === 'expert' || s.level === 'advanced').length} advanced
                      </Badge>
                      
                      {isExpanded ? (
                        <ChevronDown className="size-4 text-slate-500" />
                      ) : (
                        <ChevronRight className="size-4 text-slate-500" />
                      )}
                    </div>
                  </button>
                  
                  {/* Skills List */}
                  <AnimatePresence>
                    {isExpanded && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3, ease: "easeInOut" }}
                        className="border-t border-slate-200"
                      >
                        <div className="space-y-3 p-4">
                          {category.skills.map((skill, skillIndex) => (
                            <motion.div
                              key={skill.name}
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: skillIndex * 0.05, duration: 0.3 }}
                              className="flex items-center justify-between rounded-2xl border border-slate-200 bg-slate-50 p-3 transition-colors duration-200 hover:bg-slate-100"
                            >
                              <div className="min-w-0 flex-1">
                                <h4 className="truncate text-sm font-medium text-slate-900">
                                  {skill.name}
                                </h4>
                                {skill.years && (
                                  <p className="mt-0.5 text-xs text-slate-500">
                                    {skill.years} year{skill.years !== 1 ? 's' : ''} experience
                                  </p>
                                )}
                              </div>
                              
                              <div className="flex items-center gap-2">
                                <Badge 
                                  className={clsx(
                                    "text-xs px-2 py-1 rounded-full",
                                    getLevelColor(skill.level)
                                  )}
                                >
                                  {getLevelText(skill.level)}
                                </Badge>
                              </div>
                            </motion.div>
                          ))}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

export default FounderSkills
