"""
Qdrant vector storage implementation for TractionX Data Pipeline Service.
"""

from typing import Any, Dict, List, Optional

from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.exceptions import ResponseHandlingException

from app.configs import get_logger, settings
from app.storage.interfaces import VectorStorageInterface


class QdrantStorage(VectorStorageInterface):
    """
    Qdrant vector storage implementation that provides access to Qdrant vector database.

    This class implements the VectorStorageInterface for vector database operations.
    It provides methods for creating collections, inserting vectors, searching vectors,
    and other vector database operations.
    """

    def __init__(self):
        """Initialize Qdrant storage with logger."""
        self.logger = get_logger(__name__)
        self.client = None
        self.vector_size = settings.QDRANT_VECTOR_SIZE
        self._collections_cache = set()
        self.initialized = False

    async def initialize(self) -> None:
        """
        Initialize Qdrant storage by creating a QdrantClient.

        This method sets up the connection to Qdrant using credentials
        from environment variables.
        """
        try:
            self.logger.info("Initializing Qdrant client connection...")
            self.client = QdrantClient(
                url=settings.QDRANT_URL,
                api_key=settings.QDRANT_API_KEY,
                timeout=30,  # Default timeout of 30 seconds
            )

            # Test connection
            collections = self.client.get_collections()
            self.logger.info(
                f"Connected to Qdrant: {len(collections.collections)} collections"
            )
            self.logger.debug(
                f"Available collections: {[c.name for c in collections.collections]}"
            )

            self.initialized = True
            self.logger.info("Qdrant storage initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize Qdrant storage: {str(e)}")
            self.initialized = False
            raise

    async def cleanup(self) -> None:
        """
        Clean up Qdrant storage resources.

        This method releases any resources used by the Qdrant client.
        """
        self.client = None
        self.initialized = False
        self._collections_cache.clear()
        self.logger.info("Qdrant storage resources cleaned up")

    async def health_check(self) -> bool:
        """
        Check Qdrant storage health by verifying connection to the server.

        Returns:
            bool: True if the connection is healthy, False otherwise.
        """
        if not self.initialized or not self.client:
            return False

        try:
            self.client.get_collections()
            return True
        except Exception as e:
            self.logger.error(f"Qdrant health check failed: {str(e)}")
            return False

    async def create_collection(
        self, collection_name: str, vector_size: int, distance_metric: str = "cosine"
    ) -> bool:
        """
        Create a vector collection in Qdrant.

        Args:
            collection_name: The name of the collection to create
            vector_size: The size of vectors to store in the collection
            distance_metric: The distance metric to use (default: "cosine")

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.initialized or not self.client:
            self.logger.error("Qdrant client not initialized")
            return False

        try:
            # Check if collection already exists
            try:
                self.client.get_collection(collection_name)
                self.logger.info(f"Collection already exists: {collection_name}")
                self._collections_cache.add(collection_name)
                return True
            except ResponseHandlingException:
                # Collection doesn't exist, create it
                pass

            # Map string distance metric to Qdrant Distance enum
            distance = models.Distance.COSINE
            if (
                distance_metric.lower() == "euclid"
                or distance_metric.lower() == "euclidean"
            ):
                distance = models.Distance.EUCLID
            elif distance_metric.lower() == "dot":
                distance = models.Distance.DOT

            # Create collection
            self.client.create_collection(
                collection_name=collection_name,
                vectors_config=models.VectorParams(size=vector_size, distance=distance),
            )

            self._collections_cache.add(collection_name)
            self.logger.info(
                f"Created collection: {collection_name} with vector size {vector_size}"
            )
            return True
        except Exception as e:
            self.logger.error(
                f"Failed to create collection {collection_name}: {str(e)}"
            )
            return False

    async def insert_vectors(
        self, collection_name: str, vectors: List[Dict[str, Any]]
    ) -> bool:
        """
        Insert vectors with metadata into a collection.

        Args:
            collection_name: The name of the collection
            vectors: List of dictionaries containing vector data
                Each dictionary should have:
                - "id": Vector ID (string)
                - "vector": The vector data (list of floats)
                - "payload": Optional metadata (dictionary)

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.initialized or not self.client:
            self.logger.error("Qdrant client not initialized")
            return False

        try:
            # Ensure collection exists
            if collection_name not in self._collections_cache:
                await self.create_collection(collection_name, self.vector_size)

            # Convert vector data to Qdrant points
            points = []
            for vector_data in vectors:
                vector_id = vector_data.get("id")
                vector = vector_data.get("vector")
                payload = vector_data.get("payload", {})

                if not vector_id or not vector:
                    self.logger.warning(
                        f"Skipping vector with missing id or vector data: {vector_data}"
                    )
                    continue

                point = models.PointStruct(id=vector_id, vector=vector, payload=payload)
                points.append(point)

            if not points:
                self.logger.warning("No valid points to insert")
                return False

            # Insert vectors
            self.client.upsert(collection_name=collection_name, points=points)

            self.logger.info(
                f"Successfully inserted {len(points)} vectors into {collection_name}"
            )
            return True
        except Exception as e:
            self.logger.error(
                f"Failed to insert vectors into {collection_name}: {str(e)}"
            )
            return False

    async def update_vector(
        self,
        collection_name: str,
        vector_id: str,
        vector: List[float],
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Update a vector by ID.

        Args:
            collection_name: The name of the collection
            vector_id: The ID of the vector to update
            vector: The new vector data
            metadata: Optional new metadata

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.initialized or not self.client:
            self.logger.error("Qdrant client not initialized")
            return False

        try:
            # Ensure collection exists
            if collection_name not in self._collections_cache:
                await self.create_collection(collection_name, self.vector_size)

            # Create point with updated data
            point = models.PointStruct(
                id=vector_id, vector=vector, payload=metadata or {}
            )

            # Update vector
            self.client.upsert(collection_name=collection_name, points=[point])

            self.logger.info(
                f"Successfully updated vector {vector_id} in {collection_name}"
            )
            return True
        except Exception as e:
            self.logger.error(
                f"Failed to update vector {vector_id} in {collection_name}: {str(e)}"
            )
            return False

    async def search_vectors(
        self,
        collection_name: str,
        query_vector: List[float],
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Search for similar vectors in a collection.

        Args:
            collection_name: The name of the collection
            query_vector: The query vector to search for
            limit: Maximum number of results to return
            filters: Optional filters to apply to the search

        Returns:
            List[Dict[str, Any]]: List of search results
        """
        if not self.initialized or not self.client:
            self.logger.error("Qdrant client not initialized")
            return []

        try:
            # Ensure collection exists
            if collection_name not in self._collections_cache:
                try:
                    self.client.get_collection(collection_name)
                    self._collections_cache.add(collection_name)
                except ResponseHandlingException:
                    self.logger.warning(f"Collection {collection_name} does not exist")
                    return []

            # Build filter
            query_filter = None
            if filters:
                filter_conditions = []
                for key, value in filters.items():
                    filter_conditions.append(
                        models.FieldCondition(
                            key=key, match=models.MatchValue(value=value)
                        )
                    )

                if filter_conditions:
                    query_filter = models.Filter(must=filter_conditions)

            # Search vectors
            results = self.client.search(
                collection_name=collection_name,
                query_vector=query_vector,
                query_filter=query_filter,
                limit=limit,
                with_payload=True,
                with_vectors=False,
            )

            # Convert to dict format
            search_results = []
            for result in results:
                result_dict = {
                    "id": result.id,
                    "score": result.score,
                    "payload": result.payload,
                }
                search_results.append(result_dict)

            self.logger.info(
                f"Found {len(search_results)} vectors in {collection_name}"
            )
            return search_results
        except Exception as e:
            self.logger.error(
                f"Failed to search vectors in {collection_name}: {str(e)}"
            )
            return []

    async def get_vector(
        self, collection_name: str, vector_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get a vector by ID.

        Args:
            collection_name: The name of the collection
            vector_id: The ID of the vector to get

        Returns:
            Optional[Dict[str, Any]]: The vector data or None if not found
        """
        if not self.initialized or not self.client:
            self.logger.error("Qdrant client not initialized")
            return None

        try:
            # Ensure collection exists
            if collection_name not in self._collections_cache:
                try:
                    self.client.get_collection(collection_name)
                    self._collections_cache.add(collection_name)
                except ResponseHandlingException:
                    self.logger.warning(f"Collection {collection_name} does not exist")
                    return None

            # Get vector
            results = self.client.retrieve(
                collection_name=collection_name,
                ids=[vector_id],
                with_payload=True,
                with_vectors=True,
            )

            if not results:
                self.logger.warning(
                    f"Vector {vector_id} not found in {collection_name}"
                )
                return None

            # Convert to dict format
            result = results[0]
            vector_data = {
                "id": result.id,
                "vector": result.vector,
                "payload": result.payload,
            }

            self.logger.info(f"Retrieved vector {vector_id} from {collection_name}")
            return vector_data
        except Exception as e:
            self.logger.error(
                f"Failed to get vector {vector_id} from {collection_name}: {str(e)}"
            )
            return None

    async def delete_vector(self, collection_name: str, vector_id: str) -> bool:
        """
        Delete a vector by ID.

        Args:
            collection_name: The name of the collection
            vector_id: The ID of the vector to delete

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.initialized or not self.client:
            self.logger.error("Qdrant client not initialized")
            return False

        try:
            # Ensure collection exists
            if collection_name not in self._collections_cache:
                try:
                    self.client.get_collection(collection_name)
                    self._collections_cache.add(collection_name)
                except ResponseHandlingException:
                    self.logger.warning(f"Collection {collection_name} does not exist")
                    return False

            # Delete vector
            self.client.delete(
                collection_name=collection_name,
                points_selector=models.PointIdsList(points=[vector_id]),
            )

            self.logger.info(f"Deleted vector {vector_id} from {collection_name}")
            return True
        except Exception as e:
            self.logger.error(
                f"Failed to delete vector {vector_id} from {collection_name}: {str(e)}"
            )
            return False

    async def delete_vectors(
        self, collection_name: str, filters: Dict[str, Any]
    ) -> int:
        """
        Delete vectors matching filters.

        Args:
            collection_name: The name of the collection
            filters: Filters to match vectors to delete

        Returns:
            int: Number of vectors deleted
        """
        if not self.initialized or not self.client:
            self.logger.error("Qdrant client not initialized")
            return 0

        try:
            # Ensure collection exists
            if collection_name not in self._collections_cache:
                try:
                    self.client.get_collection(collection_name)
                    self._collections_cache.add(collection_name)
                except ResponseHandlingException:
                    self.logger.warning(f"Collection {collection_name} does not exist")
                    return 0

            # Build filter
            filter_conditions = []
            for key, value in filters.items():
                filter_conditions.append(
                    models.FieldCondition(key=key, match=models.MatchValue(value=value))
                )

            if not filter_conditions:
                self.logger.warning("No filters provided for delete_vectors")
                return 0

            query_filter = models.Filter(must=filter_conditions)

            # Delete vectors
            result = self.client.delete(
                collection_name=collection_name,
                points_selector=models.FilterSelector(filter=query_filter),
            )

            deleted_count = 0
            if hasattr(result, "deleted") and result.deleted is not None:  # type: ignore
                deleted_count = result.deleted  # type: ignore

            self.logger.info(f"Deleted {deleted_count} vectors from {collection_name}")
            return deleted_count
        except Exception as e:
            self.logger.error(
                f"Failed to delete vectors from {collection_name}: {str(e)}"
            )
            return 0

    async def get_collection_info(
        self, collection_name: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get collection information.

        Args:
            collection_name: The name of the collection

        Returns:
            Optional[Dict[str, Any]]: Collection information or None if not found
        """
        if not self.initialized or not self.client:
            self.logger.error("Qdrant client not initialized")
            return None

        try:
            # Get collection info
            collection_info = self.client.get_collection(collection_name)

            # Convert to dict format
            info = {
                "name": collection_name,
                "vector_size": collection_info.config.params.vectors.size,  # type: ignore
                "distance": str(collection_info.config.params.vectors.distance),  # type: ignore
                "points_count": collection_info.vectors_count,
                "status": str(collection_info.status),
            }

            self._collections_cache.add(collection_name)
            self.logger.info(f"Retrieved collection info for {collection_name}")
            return info
        except ResponseHandlingException:
            self.logger.warning(f"Collection {collection_name} does not exist")
            return None
        except Exception as e:
            self.logger.error(
                f"Failed to get collection info for {collection_name}: {str(e)}"
            )
            return None
