/**
 * API client for queue job management and status polling
 */

import { API_BASE_URL, createAuthHeaders } from "./auth-api";

export interface JobStatus {
  id: string;
  type: string;
  payload: Record<string, any>;
  status: 'pending' | 'queued' | 'processing' | 'completed' | 'failed';
  priority: 'low' | 'normal' | 'high';
  queue: string;
  created_at: number;
  updated_at: number;
  scheduled_for: number | null;
  started_at: number | null;
  completed_at: number | null;
  failed_at: number | null;
  attempts: number;
  max_attempts: number;
  retry_strategy: string;
  base_delay: number;
  max_delay: number;
  next_retry_at: number | null;
  result: any;
  error: string | null;
  metadata: {
    source: string | null;
    correlation_id: string | null;
    tags: string[];
    custom: Record<string, any>;
  };
}

export class QueueAPI {
  /**
   * Get job status by ID
   */
  static async getJobStatus(
    jobId: string,
    token: string,
    orgId: string
  ): Promise<JobStatus> {
    try {
      const response = await fetch(`${API_BASE_URL}/queue/jobs/${jobId}`, {
        method: 'GET',
        headers: createAuthHeaders(token, orgId),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to get job status');
      }

      return response.json();
    } catch (error) {
      console.error('Get job status error:', error);
      throw error;
    }
  }

  /**
   * List jobs with optional filters
   */
  static async listJobs(
    token: string,
    orgId: string,
    params: {
      queue_type?: string;
      status?: string;
      job_type?: string;
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<JobStatus[]> {
    try {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`${API_BASE_URL}/queue/jobs?${searchParams}`, {
        method: 'GET',
        headers: createAuthHeaders(token, orgId),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to list jobs');
      }

      return response.json();
    } catch (error) {
      console.error('List jobs error:', error);
      throw error;
    }
  }

  /**
   * Retry a failed job
   */
  static async retryJob(
    jobId: string,
    delaySeconds: number = 0,
    token: string,
    orgId: string
  ): Promise<{ status: string }> {
    try {
      const response = await fetch(`${API_BASE_URL}/queue/jobs/${jobId}/retry`, {
        method: 'POST',
        headers: {
          ...createAuthHeaders(token, orgId),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ delay_seconds: delaySeconds }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to retry job');
      }

      return response.json();
    } catch (error) {
      console.error('Retry job error:', error);
      throw error;
    }
  }

  /**
   * Cancel a job
   */
  static async cancelJob(
    jobId: string,
    token: string,
    orgId: string
  ): Promise<{ status: string }> {
    try {
      const response = await fetch(`${API_BASE_URL}/queue/jobs/${jobId}`, {
        method: 'DELETE',
        headers: createAuthHeaders(token, orgId),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to cancel job');
      }

      return response.json();
    } catch (error) {
      console.error('Cancel job error:', error);
      throw error;
    }
  }
} 