# Service Settings
SERVICE_NAME=tractionx-datapipelines
VERSION=0.1.0
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# API Settings
API_HOST=0.0.0.0
API_PORT=8001
API_PREFIX=/api/v1

# Redis Configuration (for job queue)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_USERNAME=
REDIS_PASSWORD=
REDIS_ENDPOINT=
REDIS_KEY_PREFIX=txpipeline

# Database Configuration (PostgreSQL)
DB_HOST=postgres
DB_PORT=5432
DB_NAME=tractionx_pipelines
DB_USER=tractionx
DB_PASSWORD=tractionx123
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# MongoDB Configuration (for barrier tracking)
MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_DB=tractionx_datapipelines
MONGO_USER=
MONGO_PASSWORD=

# S3 Storage
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1
S3_BUCKET=tractionx-pipeline-data
S3_PREFIX=enrichment-data

# Qdrant Vector Database
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=
QDRANT_VECTOR_SIZE=1536

# External API Keys
CLAY_API_KEY=your_clay_api_key
CLAY_BASE_URL=https://api.clay.com/v1
PDL_API_KEY=your_pdl_api_key
PDL_BASE_URL=https://api.peopledatalabs.com/v5
BRIGHTDATA_API_KEY=your_brightdata_api_key
BRIGHTDATA_BASE_URL=https://api.brightdata.com
BRIGHTDATA_WEB_UNLOCKER_ZONE=your_web_unlocker_zone
NEWSAPI_KEY=your_newsapi_key
GNEWS_KEY=your_gnews_key
SERPAPI_KEY=your_serpapi_key
TOGETHER_API_KEY=your_together_api_key
OPENAI_API_KEY=your_openai_api_key
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_MODEL=gpt-4

# Worker Settings
WORKER_CONCURRENCY=4
WORKER_POLL_INTERVAL=5
WORKER_TIMEOUT=1800
MAX_RETRIES=3
RETRY_BACKOFF_FACTOR=2

# Pipeline Settings
PIPELINE_TIMEOUT=3600
ENABLE_COMPANY_ENRICHMENT=true
ENABLE_FOUNDER_ENRICHMENT=true
ENABLE_NEWS_AGGREGATION=true
ENABLE_EMBEDDING_GENERATION=true

# Webhook Settings
WEBHOOK_SECRET=your_webhook_secret
CLAY_WEBHOOK_SECRET=your_clay_webhook_secret

# Backend Integration
BACKEND_API_URL=http://localhost:8000/api/v1
BACKEND_API_KEY=your_backend_api_key

# Monitoring
SENTRY_DSN=
PROMETHEUS_PORT=9090
ENABLE_METRICS=true

# Rate Limiting
CLAY_RATE_LIMIT=100
PDL_RATE_LIMIT=1000
BING_RATE_LIMIT=1000
OPENAI_RATE_LIMIT=3000

# Data Retention
RAW_DATA_RETENTION_DAYS=90
PROCESSED_DATA_RETENTION_DAYS=365
LOG_RETENTION_DAYS=30

# Development Settings
WATCHDOG_ENABLED=true
RELOAD_DIRS=app/ 