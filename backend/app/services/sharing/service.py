from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from bson import ObjectId

from app.core.logging import get_logger
from app.models.sharing import (
    EmbedCode,
    QRCode,
    SharingConfig,
    SharingLink,
    SharingView,
)
from app.services.base import BaseService
from app.services.cache.redis import RedisService
from app.services.factory import ServiceFactory, get_form_service
from app.services.sharing.interfaces import (
    EmbedType,
    SharingServiceInterface,
    SharingType,
)
from app.utils.sharing import (
    datetime_to_timestamp,
    generate_embed_html,
    generate_qr_image,
    generate_qr_url,
    generate_sharing_url,
    generate_short_code,
    generate_short_url,
    generate_token,
    timestamp_ms,
    validate_object_id,
    validate_sharing_config,
)

logger = get_logger(__name__)


class SharingService(BaseService, SharingServiceInterface):
    """Implementation of the sharing service."""

    def __init__(self, db=None):
        super().__init__()
        self.db = db
        self.cache = None
        self.token_ttl = 60 * 60 * 24 * 30  # 30 days

    async def initialize(self) -> None:
        """Initialize service resources."""
        if self.db is None:
            from app.core.database import get_database

            self.db = await get_database()

        self.cache = await ServiceFactory.get_service(RedisService)
        self.logger.info("Sharing service initialized")

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        pass

    async def create_sharing_config(
        self,
        resource_type: str,
        resource_id: str,
        org_id: str,
        enabled: bool = True,
        sharing_types: Optional[List[SharingType]] = None,
        allowed_domains: Optional[List[str]] = None,
        embed_type: EmbedType = EmbedType.INLINE,
        custom_styles: Optional[Dict[str, Any]] = None,
        tracking_enabled: bool = True,
        expires_at: Optional[Union[datetime, int]] = None,
    ) -> Optional[Dict[str, Any]]:
        """Create a sharing configuration for a resource."""
        try:
            # Validate parameters
            valid, error = validate_sharing_config(
                resource_type=resource_type,
                resource_id=resource_id,
                org_id=org_id,
                sharing_types=sharing_types,
                embed_type=embed_type,
                expires_at=expires_at,
            )
            if not valid:
                raise ValueError(error)

            # Convert datetime to timestamp if needed
            if expires_at and isinstance(expires_at, datetime):
                expires_at = datetime_to_timestamp(expires_at)

            # Create sharing config
            now = timestamp_ms()
            sharing_config_data = {
                "resource_type": resource_type,
                "resource_id": ObjectId(resource_id),
                "org_id": ObjectId(org_id),
                "enabled": enabled,
                "sharing_types": [
                    st.value for st in (sharing_types or [SharingType.LINK])
                ],
                "access_token": generate_token(),
                "expires_at": expires_at,
                "allowed_domains": allowed_domains or [],
                "embed_type": embed_type.value,
                "custom_styles": custom_styles or {},
                "tracking_enabled": tracking_enabled,
                "created_at": now,
                "updated_at": now,
            }

            # Create and save sharing config using model
            sharing_config = SharingConfig(**sharing_config_data)
            await sharing_config.save()

            # Convert to response format
            response = sharing_config.model_dump(by_alias=True)
            response["_id"] = str(response["_id"])
            response["resource_id"] = str(response["resource_id"])
            response["org_id"] = str(response["org_id"])

            self.logger.info(
                f"Created sharing config for {resource_type} {resource_id}"
            )
            return response
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "resource_type": resource_type,
                    "resource_id": resource_id,
                    "error": "Failed to create sharing config",
                },
            )

    async def get_sharing_config(self, config_id: str) -> Optional[Dict[str, Any]]:
        """Get sharing configuration by ID."""
        try:
            # Validate config_id
            valid, error = validate_object_id(config_id)
            if not valid:
                raise ValueError(error)

            # Get from database using model
            config = await SharingConfig.find_one({"_id": ObjectId(config_id)})
            if config:
                # Convert to response format
                response = config.model_dump(by_alias=True)
                response["_id"] = str(response["_id"])
                response["resource_id"] = str(response["resource_id"])
                response["org_id"] = str(response["org_id"])

                return response

            return None
        except Exception as e:
            await self.handle_error(
                e, {"config_id": config_id, "error": "Failed to get sharing config"}
            )

    async def update_sharing_config(
        self, config_id: str, updates: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Update a sharing configuration."""
        try:
            # Validate config_id
            valid, error = validate_object_id(config_id)
            if not valid:
                raise ValueError(error)

            # Validate updates
            if "sharing_types" in updates:
                sharing_types = updates["sharing_types"]
                if isinstance(sharing_types, list):
                    valid_types = {t.value for t in SharingType}
                    for sharing_type in sharing_types:
                        if sharing_type not in valid_types:
                            raise ValueError(f"Invalid sharing type: {sharing_type}")

            if "embed_type" in updates:
                embed_type = updates["embed_type"]
                valid_types = {t.value for t in EmbedType}
                if embed_type not in valid_types:
                    raise ValueError(f"Invalid embed type: {embed_type}")

            # Add updated_at timestamp
            updates["updated_at"] = timestamp_ms()

            # Update in database using raw MongoDB operation (model doesn't have find_one_and_update)
            result = await self.db.sharing_configs.find_one_and_update(  # type: ignore
                {"_id": ObjectId(config_id)}, {"$set": updates}, return_document=True
            )

            if result:
                # Convert ObjectId to string for serialization
                response = {
                    **result,
                    "_id": str(result["_id"]),
                    "resource_id": str(result["resource_id"]),
                    "org_id": str(result["org_id"]),
                }

                self.logger.info(f"Updated sharing config {config_id}")
                return response

            return None
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "config_id": config_id,
                    "updates": updates,
                    "error": "Failed to update sharing config",
                },
            )

    async def delete_sharing_config(self, config_id: str) -> Optional[bool]:
        """Delete a sharing configuration."""
        try:
            # Validate config_id
            valid, error = validate_object_id(config_id)
            if not valid:
                raise ValueError(error)

            # Delete from database using raw MongoDB operation (model doesn't have delete_one)
            result = await self.db.sharingconfigs.delete_one({  # type: ignore
                "_id": ObjectId(config_id)
            })

            if result.deleted_count > 0:
                # Delete from cache

                # Delete all related sharing items using raw MongoDB operations
                await self.db.sharinglinks.delete_many({  # type: ignore
                    "sharing_config_id": ObjectId(config_id)
                })
                await self.db.embedcodes.delete_many({  # type: ignore
                    "sharing_config_id": ObjectId(config_id)
                })
                await self.db.qrcodes.delete_many({  # type: ignore
                    "sharing_config_id": ObjectId(config_id)
                })

                self.logger.info(f"Deleted sharing config {config_id}")
                return True

            return False
        except Exception as e:
            await self.handle_error(
                e, {"config_id": config_id, "error": "Failed to delete sharing config"}
            )

    async def generate_sharing_link(
        self,
        config_id: str,
        created_by: str,
        expires_at: Optional[Union[datetime, int]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Optional[Dict[str, Any]]:
        """Generate a sharing link."""
        try:
            # Validate parameters
            valid, error = validate_object_id(config_id)
            if not valid:
                raise ValueError(error)

            valid, error = validate_object_id(created_by)
            if not valid:
                raise ValueError(f"Invalid created_by: {error}")

            # Get sharing config
            config = await self.get_sharing_config(config_id)
            if not config:
                raise ValueError(f"Sharing config {config_id} not found")

            # Check if link sharing is enabled
            if SharingType.LINK.value not in config["sharing_types"]:
                raise ValueError(f"Link sharing is not enabled for config {config_id}")

            # Convert datetime to timestamp if needed
            if expires_at and isinstance(expires_at, datetime):
                expires_at = datetime_to_timestamp(expires_at)

            # Create sharing link
            now = timestamp_ms()
            token = generate_token()
            short_code = generate_short_code()

            sharing_link_data = {
                "sharing_config_id": ObjectId(config_id),
                "resource_type": config["resource_type"],
                "resource_id": config["resource_id"],
                "org_id": config["org_id"],
                "token": token,
                "short_code": short_code,
                "type": "form_sharing_link",
                "expires_at": expires_at,
                "is_active": True,
                "created_by": ObjectId(created_by),
                "views": 0,
                "last_viewed_at": None,
                "metadata": metadata or {},
                "created_at": now,
                "updated_at": now,
            }
            logger.info(f"sharing_link_data: {sharing_link_data}")
            # Create and save sharing link using model
            sharing_link = SharingLink(**sharing_link_data)
            await sharing_link.save()
            logger.info(f"sharing_link: {sharing_link}")

            # Generate full URL
            sharing_link.url = generate_sharing_url(token)
            sharing_link.short_url = generate_short_url(short_code)

            # Convert to response format
            response = sharing_link.model_dump(by_alias=True)
            # response["_id"] = str(response["_id"])
            # response["sharing_config_id"] = str(response["sharing_config_id"])
            # response["resource_id"] = str(response["resource_id"])
            # response["org_id"] = str(response["org_id"])
            # response["created_by"] = str(response["created_by"])

            self.logger.info(
                f"Generated sharing link for {config['resource_type']} {config['resource_id']}"
            )
            return response
        except Exception as e:
            await self.handle_error(
                e, {"config_id": config_id, "error": "Failed to generate sharing link"}
            )

    async def list_sharing_configs(
        self, org_id: str, resource_type: str, resource_id: str
    ) -> Optional[List[Dict[str, Any]]]:
        """List sharing configs for a resource."""
        try:
            # Validate parameters
            valid, error = validate_object_id(org_id)
            if not valid:
                raise ValueError(error)
            logger.info(
                f"listing sharing configs for {resource_type} {resource_id} {org_id}"
            )
            query = {
                "org_id": ObjectId(org_id),
                "resource_type": resource_type,
                "resource_id": ObjectId(resource_id),
            }
            logger.info(f"query: {query}")
            configs = await SharingConfig.find_many(query=query)
            logger.info(f"configs: {configs}")
            return [config.model_dump(by_alias=True) for config in configs]

        except Exception as e:
            await self.handle_error(
                e,
                {
                    "org_id": org_id,
                    "resource_type": resource_type,
                    "resource_id": resource_id,
                    "error": "Failed to list sharing links",
                },
            )

    async def generate_embed_code(
        self,
        config_id: str,
        created_by: str,
        embed_type: Optional[EmbedType] = None,
        custom_styles: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Optional[Dict[str, Any]]:
        """Generate an embed code."""
        try:
            # Validate parameters
            valid, error = validate_object_id(config_id)
            if not valid:
                raise ValueError(error)

            valid, error = validate_object_id(created_by)
            if not valid:
                raise ValueError(f"Invalid created_by: {error}")

            # Get sharing config
            config = await self.get_sharing_config(config_id)
            if not config:
                raise ValueError(f"Sharing config {config_id} not found")

            # Check if embed sharing is enabled
            if SharingType.EMBED.value not in config["sharing_types"]:
                raise ValueError(f"Embed sharing is not enabled for config {config_id}")

            # Create embed code
            now = timestamp_ms()
            token = generate_token()

            embed_code_data = {
                "sharing_config_id": ObjectId(config_id),
                "resource_type": config["resource_type"],
                "resource_id": config["resource_id"],
                "org_id": config["org_id"],
                "token": token,
                "embed_type": embed_type.value if embed_type else config["embed_type"],
                "allowed_domains": config["allowed_domains"],
                "custom_styles": custom_styles or config["custom_styles"],
                "is_active": True,
                "created_by": ObjectId(created_by),
                "views": 0,
                "last_viewed_at": None,
                "metadata": metadata or {},
                "created_at": now,
                "updated_at": now,
            }

            # Create and save embed code using model
            embed_code = EmbedCode(**embed_code_data)
            await embed_code.save()

            # Store token mapping in Redis for quick validation

            # Generate embed code HTML
            embed_code.html = generate_embed_html(
                token=token,
                embed_type=embed_code_data["embed_type"],
                custom_styles=embed_code_data["custom_styles"],
            )

            # Convert to response format
            response = embed_code.model_dump(by_alias=True)
            response["_id"] = str(response["_id"])
            response["sharing_config_id"] = str(response["sharing_config_id"])
            response["resource_id"] = str(response["resource_id"])
            response["org_id"] = str(response["org_id"])
            response["created_by"] = str(response["created_by"])

            self.logger.info(
                f"Generated embed code for {config['resource_type']} {config['resource_id']}"
            )
            return response
        except Exception as e:
            await self.handle_error(
                e, {"config_id": config_id, "error": "Failed to generate embed code"}
            )

    async def generate_qr_code(
        self,
        config_id: str,
        created_by: str,
        size: int = 300,
        error_correction_level: str = "M",
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Optional[Dict[str, Any]]:
        """Generate a QR code."""
        try:
            # Validate parameters
            valid, error = validate_object_id(config_id)
            if not valid:
                raise ValueError(error)

            valid, error = validate_object_id(created_by)
            if not valid:
                raise ValueError(f"Invalid created_by: {error}")

            if error_correction_level not in {"L", "M", "Q", "H"}:
                raise ValueError(
                    f"Invalid error correction level: {error_correction_level}"
                )

            # Get sharing config
            config = await self.get_sharing_config(config_id)
            if not config:
                raise ValueError(f"Sharing config {config_id} not found")

            # Check if QR code sharing is enabled
            if SharingType.QR_CODE.value not in config["sharing_types"]:
                raise ValueError(
                    f"QR code sharing is not enabled for config {config_id}"
                )

            # Create QR code
            now = timestamp_ms()
            token = generate_token()

            qr_code_data = {
                "sharing_config_id": ObjectId(config_id),
                "resource_type": config["resource_type"],
                "resource_id": config["resource_id"],
                "org_id": config["org_id"],
                "token": token,
                "size": size,
                "error_correction_level": error_correction_level,
                "is_active": True,
                "created_by": ObjectId(created_by),
                "scans": 0,
                "last_scanned_at": None,
                "metadata": metadata or {},
                "created_at": now,
                "updated_at": now,
            }

            # Create and save QR code using model
            qr_code = QRCode(**qr_code_data)
            await qr_code.save()

            # Generate QR code image
            qr_url = generate_qr_url(token)
            try:
                qr_code.image_data = generate_qr_image(
                    url=qr_url, size=size, error_correction_level=error_correction_level
                )
            except Exception as qr_error:
                self.logger.error(f"Failed to generate QR image: {str(qr_error)}")
                # Provide a placeholder image data
                qr_code.image_data = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII="

            qr_code.url = qr_url

            # Convert to response format
            response = qr_code.model_dump(by_alias=True)
            response["_id"] = str(response["_id"])
            response["sharing_config_id"] = str(response["sharing_config_id"])
            response["resource_id"] = str(response["resource_id"])
            response["org_id"] = str(response["org_id"])
            response["created_by"] = str(response["created_by"])

            self.logger.info(
                f"Generated QR code for {config['resource_type']} {config['resource_id']}"
            )
            return response
        except Exception as e:
            await self.handle_error(
                e, {"config_id": config_id, "error": "Failed to generate QR code"}
            )

    async def list_sharing_links(
        self, config_id: str
    ) -> Optional[List[Dict[str, Any]]]:
        """List sharing links for a configuration."""
        try:
            # Validate config_id
            valid, error = validate_object_id(config_id)
            if not valid:
                raise ValueError(error)

            # Get sharing links for this config
            links = await SharingLink.find_many({
                "sharing_config_id": ObjectId(config_id),
                "is_active": True,
            })

            # Convert to response format
            response_links = []
            for link in links:
                # Add URLs
                link.url = generate_sharing_url(link.token)
                link.short_url = generate_short_url(link.short_code)

                # Convert to response format
                response = link.model_dump(by_alias=True)
                response["_id"] = str(response["_id"])
                response["sharing_config_id"] = str(response["sharing_config_id"])
                response["resource_id"] = str(response["resource_id"])
                response["org_id"] = str(response["org_id"])
                response["created_by"] = str(response["created_by"])

                # Map views to view_count for frontend compatibility
                response["view_count"] = response.get("views", 0)

                response_links.append(response)

            self.logger.info(
                f"Listed {len(response_links)} sharing links for config {config_id}"
            )
            return response_links
        except Exception as e:
            await self.handle_error(
                e, {"config_id": config_id, "error": "Failed to list sharing links"}
            )

    async def get_sharing_link(self, link_id: str) -> Optional[Dict[str, Any]]:
        """Get sharing link by ID."""
        try:
            # Validate link_id
            valid, error = validate_object_id(link_id)
            if not valid:
                raise ValueError(error)

            link = await SharingLink.find_one({"_id": ObjectId(link_id)})

            if link:
                # Add URLs
                link.url = generate_sharing_url(link.token)
                link.short_url = generate_short_url(link.short_code)

                # Convert to response format
                response = link.model_dump(by_alias=True)
                response["_id"] = str(response["_id"])
                response["sharing_config_id"] = str(response["sharing_config_id"])
                response["resource_id"] = str(response["resource_id"])
                response["org_id"] = str(response["org_id"])
                response["created_by"] = str(response["created_by"])
                return response

            return None
        except Exception as e:
            await self.handle_error(
                e, {"link_id": link_id, "error": "Failed to get sharing link"}
            )

    async def get_embed_code(self, embed_id: str) -> Optional[Dict[str, Any]]:
        """Get embed code by ID."""
        try:
            # Validate embed_id
            valid, error = validate_object_id(embed_id)
            if not valid:
                raise ValueError(error)

            embed = await EmbedCode.find_one({"_id": ObjectId(embed_id)})

            if embed:
                # Generate embed code HTML
                embed.html = generate_embed_html(
                    token=embed.token,
                    embed_type=embed.embed_type,
                    custom_styles=embed.custom_styles,
                )

                # Convert to response format
                response = embed.model_dump(by_alias=True)
                response["_id"] = str(response["_id"])
                response["sharing_config_id"] = str(response["sharing_config_id"])
                response["resource_id"] = str(response["resource_id"])
                response["org_id"] = str(response["org_id"])
                response["created_by"] = str(response["created_by"])
                return response

            return None
        except Exception as e:
            await self.handle_error(
                e, {"embed_id": embed_id, "error": "Failed to get embed code"}
            )

    async def get_qr_code(self, qr_id: str) -> Optional[Dict[str, Any]]:
        """Get QR code by ID."""
        try:
            # Validate qr_id
            valid, error = validate_object_id(qr_id)
            if not valid:
                raise ValueError(error)

            qr = await QRCode.find_one({"_id": ObjectId(qr_id)})

            if qr:
                # Add URL
                qr_url = generate_qr_url(qr.token)
                qr.url = qr_url

                # Regenerate QR code image if needed
                if not qr.image_data:
                    try:
                        qr.image_data = generate_qr_image(
                            url=qr_url,
                            size=qr.size,
                            error_correction_level=qr.error_correction_level,
                        )
                    except Exception as qr_error:
                        self.logger.error(
                            f"Failed to regenerate QR image: {str(qr_error)}"
                        )
                        # Provide a placeholder image data
                        qr.image_data = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII="

                # Convert to response format
                response = qr.model_dump(by_alias=True)
                response["_id"] = str(response["_id"])
                response["sharing_config_id"] = str(response["sharing_config_id"])
                response["resource_id"] = str(response["resource_id"])
                response["org_id"] = str(response["org_id"])
                response["created_by"] = str(response["created_by"])
                return response

            return None
        except Exception as e:
            await self.handle_error(
                e, {"qr_id": qr_id, "error": "Failed to get QR code"}
            )

    async def track_view(
        self,
        sharing_id: str,
        sharing_type: SharingType,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Optional[bool]:
        """Track a view of a shared resource."""
        try:
            # Validate parameters
            valid, error = validate_object_id(sharing_id)
            if not valid:
                raise ValueError(error)

            if not isinstance(sharing_type, SharingType):
                raise ValueError(f"Invalid sharing type: {sharing_type}")

            now = timestamp_ms()
            collection = None

            # Determine which collection to update
            if sharing_type == SharingType.LINK:
                collection = self.db.sharinglinks  # type: ignore
                field_name = "views"
                timestamp_field = "last_viewed_at"
            elif sharing_type == SharingType.EMBED:
                collection = self.db.embedcodes  # type: ignore
                field_name = "views"
                timestamp_field = "last_viewed_at"
            elif sharing_type == SharingType.QR_CODE:
                collection = self.db.qrcodes  # type: ignore
                field_name = "scans"
                timestamp_field = "last_scanned_at"
            else:
                raise ValueError(f"Invalid sharing type: {sharing_type}")

            # Update view count and last viewed timestamp using raw MongoDB operation
            result = await collection.update_one(
                {"_id": ObjectId(sharing_id)},
                {
                    "$inc": {field_name: 1},
                    "$set": {timestamp_field: now, "updated_at": now},
                },
            )

            # Log view using model
            view_log_data = {
                "sharing_id": ObjectId(sharing_id),
                "sharing_type": sharing_type.value,
                "timestamp": now,
                "metadata": metadata or {},
                "created_at": now,
                "updated_at": now,
            }
            view_log = SharingView(**view_log_data)
            await view_log.save()

            return result.modified_count > 0
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "sharing_id": sharing_id,
                    "sharing_type": (
                        sharing_type.value
                        if isinstance(sharing_type, SharingType)
                        else str(sharing_type)
                    ),
                    "error": "Failed to track view",
                },
            )

    async def get_sharing_stats(
        self, resource_type: str, resource_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get sharing statistics for a resource."""
        try:
            # Validate parameters
            if not resource_type:
                raise ValueError("Resource type cannot be empty")

            valid, error = validate_object_id(resource_id)
            if not valid:
                raise ValueError(error)

            # Get sharing configs for this resource using model
            configs = await SharingConfig.find_many({
                "resource_type": resource_type,
                "resource_id": ObjectId(resource_id),
            })

            if not configs:
                return {
                    "resource_type": resource_type,
                    "resource_id": resource_id,
                    "total_views": 0,
                    "recent_views": 0,
                    "sharing_types": {},
                }

            config_ids = [config.id for config in configs]

            # Get sharing links using model
            links = await SharingLink.find_many({
                "sharing_config_id": {"$in": config_ids}
            })

            # Get embed codes using model
            embeds = await EmbedCode.find_many({
                "sharing_config_id": {"$in": config_ids}
            })

            # Get QR codes using model
            qr_codes = await QRCode.find_many({
                "sharing_config_id": {"$in": config_ids}
            })

            # Calculate statistics
            link_views = sum(link.views for link in links)
            embed_views = sum(embed.views for embed in embeds)
            qr_scans = sum(qr.scans for qr in qr_codes)

            # Get recent views using model
            sharing_ids = (
                [link.id for link in links]
                + [embed.id for embed in embeds]
                + [qr.id for qr in qr_codes]
            )
            recent_views = await SharingView.find_many({
                "sharing_id": {"$in": sharing_ids},
                "timestamp": {
                    "$gt": timestamp_ms() - (7 * 24 * 60 * 60 * 1000)
                },  # Last 7 days
            })

            # Convert config_ids to strings for response
            config_ids_str = [str(config_id) for config_id in config_ids]

            # Prepare sharing links data with string IDs
            links_data = []
            for link in links:
                links_data.append({
                    "id": str(link.id),
                    "views": link.views,
                    "last_viewed_at": link.last_viewed_at,
                })

            # Prepare embed codes data with string IDs
            embeds_data = []
            for embed in embeds:
                embeds_data.append({
                    "id": str(embed.id),
                    "views": embed.views,
                    "last_viewed_at": embed.last_viewed_at,
                })

            # Prepare QR codes data with string IDs
            qr_codes_data = []
            for qr in qr_codes:
                qr_codes_data.append({
                    "id": str(qr.id),
                    "scans": qr.scans,
                    "last_scanned_at": qr.last_scanned_at,
                })

            return {
                "resource_type": resource_type,
                "resource_id": resource_id,
                "total_views": link_views + embed_views + qr_scans,
                "recent_views": len(recent_views),
                "config_ids": config_ids_str,
                "sharing_types": {
                    SharingType.LINK.value: {
                        "count": len(links),
                        "views": link_views,
                        "items": links_data,
                    },
                    SharingType.EMBED.value: {
                        "count": len(embeds),
                        "views": embed_views,
                        "items": embeds_data,
                    },
                    SharingType.QR_CODE.value: {
                        "count": len(qr_codes),
                        "scans": qr_scans,
                        "items": qr_codes_data,
                    },
                },
            }
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "resource_type": resource_type,
                    "resource_id": resource_id,
                    "error": "Failed to get sharing stats",
                },
            )

    async def get_shared_resource(
        self, resource_type: str, resource_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get the shared resource data based on its type.

        Args:
            resource_type: Type of resource (e.g., 'form', 'report')
            resource_id: ID of the resource

        Returns:
            Resource data or None if not found
        """
        try:
            if resource_type == "form":
                # Get form service
                form_service = await get_form_service()
                # Get form with all details
                form_data = await form_service.get_form_with_details(resource_id)  # type: ignore
                if form_data:
                    # Convert to dict if it's a model
                    if not isinstance(form_data, dict):
                        form_data = form_data.model_dump(by_alias=True)
                    return form_data
            # Add other resource types here as needed
            return None
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "resource_type": resource_type,
                    "resource_id": resource_id,
                    "error": "Failed to get shared resource",
                },
            )

    async def validate_sharing_token(
        self, token: str, config: Any = None
    ) -> Optional[Dict[str, Any]]:
        """
        Validate a sharing token and return the sharing configuration and resource data.

        Args:
            token: The sharing token to validate

        Returns:
            Dictionary containing sharing config and resource data, or None if invalid
        """
        try:
            if token:
                # First try the new format (sharing_configs collection)
                sharing_link = await SharingLink.find_one({"token": token})
                if sharing_link and sharing_link.type == "deal_form_collection":
                    # Get resource data
                    logger.info(f"Sharing link while validating token: {sharing_link}")
                    resource_data = await self.get_shared_resource(
                        sharing_link.resource_type, str(sharing_link.resource_id)
                    )

                    # Get organization data
                    from app.models.organization import Organization

                    org = await Organization.find_one({"_id": sharing_link.org_id})
                    org_data = None
                    if org:
                        org_data = org.model_dump(by_alias=True)
                        org_data["_id"] = str(org_data["_id"])

                    # Convert to response format
                    response = {
                        "sharing_config": {
                            **sharing_link.model_dump(by_alias=True),
                            "_id": str(sharing_link.id),
                            "resource_id": str(sharing_link.resource_id),
                            "org_id": str(sharing_link.org_id),
                        },
                        "resource_data": resource_data,
                        "organization": org_data,
                    }

                    return response

                config = await SharingConfig.find_one({
                    "_id": sharing_link.sharing_config_id  # type: ignore
                })

            if config:
                # Check if sharing is enabled
                if not config.enabled:
                    return None

                # Check if expired
                expires_at = config.expires_at
                if expires_at and expires_at < timestamp_ms():
                    return None

                # Get resource data
                resource_data = await self.get_shared_resource(
                    config.resource_type, str(config.resource_id)
                )

                if not resource_data:
                    return None

                # Get organization data
                from app.models.organization import Organization

                org = await Organization.find_one({"_id": config.org_id})
                org_data = None
                if org:
                    org_data = org.model_dump(by_alias=True)
                    org_data["_id"] = str(org_data["_id"])

                # Convert to response format
                response = {
                    "sharing_config": {
                        **config.model_dump(by_alias=True),
                        "_id": str(config.id),
                        "resource_id": str(config.resource_id),
                        "org_id": str(config.org_id),
                    },
                    "resource_data": resource_data,
                    "organization": org_data,
                }

                return response

            # If not found in sharing_configs, try the old format
            # Check in sharing_links
            link = await SharingLink.find_one({"token": token})
            if link:
                # Check if link is active and not expired
                if not link.is_active:
                    return None

                if link.expires_at and link.expires_at < timestamp_ms():
                    return None

                # Get sharing config
                sharing_config = await SharingConfig.find_one({
                    "_id": link.sharing_config_id
                })

                if not sharing_config or not sharing_config.enabled:
                    return None

                # Get resource data
                resource_data = await self.get_shared_resource(
                    link.resource_type, str(link.resource_id)
                )

                if not resource_data:
                    return None

                # Get organization data
                from app.models.organization import Organization

                org = await Organization.find_one({"_id": link.org_id})
                org_data = None
                if org:
                    org_data = org.model_dump(by_alias=True)
                    org_data["_id"] = str(org_data["_id"])

                # Return in old format for backward compatibility
                return {
                    "id": str(link.id),
                    "type": "link",
                    "resource_type": link.resource_type,
                    "resource_id": str(link.resource_id),
                    "org_id": str(link.org_id),
                    "sharing_config": {
                        **sharing_config.model_dump(by_alias=True),
                        "_id": str(sharing_config.id),
                        "resource_id": str(sharing_config.resource_id),
                        "org_id": str(sharing_config.org_id),
                    },
                    "resource_data": resource_data,
                    "organization": org_data,
                }

            # Check in embed_codes
            embed = await EmbedCode.find_one({"token": token})
            if embed:
                # Check if embed is active
                if not embed.is_active:
                    return None

                # Get sharing config
                sharing_config = await SharingConfig.find_one({
                    "_id": embed.sharing_config_id
                })

                if not sharing_config or not sharing_config.enabled:
                    return None

                # Get resource data
                resource_data = await self.get_shared_resource(
                    embed.resource_type, str(embed.resource_id)
                )

                if not resource_data:
                    return None

                # Get organization data
                from app.models.organization import Organization

                org = await Organization.find_one({"_id": embed.org_id})
                org_data = None
                if org:
                    org_data = org.model_dump(by_alias=True)
                    org_data["_id"] = str(org_data["_id"])

                # Return in old format for backward compatibility
                return {
                    "id": str(embed.id),
                    "type": "embed",
                    "resource_type": embed.resource_type,
                    "resource_id": str(embed.resource_id),
                    "org_id": str(embed.org_id),
                    "sharing_config": {
                        **sharing_config.model_dump(by_alias=True),
                        "_id": str(sharing_config.id),
                        "resource_id": str(sharing_config.resource_id),
                        "org_id": str(sharing_config.org_id),
                    },
                    "resource_data": resource_data,
                    "organization": org_data,
                }

            # Check in qr_codes
            qr = await QRCode.find_one({"token": token})
            if qr:
                # Check if QR code is active
                if not qr.is_active:
                    return None

                # Get sharing config
                sharing_config = await SharingConfig.find_one({
                    "_id": qr.sharing_config_id
                })

                if not sharing_config or not sharing_config.enabled:
                    return None

                # Get resource data
                resource_data = await self.get_shared_resource(
                    qr.resource_type, str(qr.resource_id)
                )

                if not resource_data:
                    return None

                # Get organization data
                from app.models.organization import Organization

                org = await Organization.find_one({"_id": qr.org_id})
                org_data = None
                if org:
                    org_data = org.model_dump(by_alias=True)
                    org_data["_id"] = str(org_data["_id"])

                # Return in old format for backward compatibility
                return {
                    "id": str(qr.id),
                    "type": "qr_code",
                    "resource_type": qr.resource_type,
                    "resource_id": str(qr.resource_id),
                    "org_id": str(qr.org_id),
                    "sharing_config": {
                        **sharing_config.model_dump(by_alias=True),
                        "_id": str(sharing_config.id),
                        "resource_id": str(sharing_config.resource_id),
                        "org_id": str(sharing_config.org_id),
                    },
                    "resource_data": resource_data,
                    "organization": org_data,
                }

            return None
        except Exception as e:
            await self.handle_error(
                e, {"token": token, "error": "Failed to validate sharing token"}
            )

    async def create_deal_form_sharing_link(
        self,
        deal_id: str,
        form_id: str,
        org_id: str,
        created_by: str,
        expires_at: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Create a sharing link for a deal/form without a sharing_config."""
        now = timestamp_ms()
        token = generate_token()
        short_code = generate_short_code()
        sharing_link_data = {
            "sharing_config_id": None,
            "resource_type": "form",
            "resource_id": ObjectId(form_id),
            "org_id": ObjectId(org_id),
            "token": token,
            "short_code": short_code,
            "expires_at": expires_at,
            "type": "deal_form_collection",
            "is_active": True,
            "created_by": ObjectId(created_by),
            "views": 0,
            "last_viewed_at": None,
            "metadata": {
                "deal_id": deal_id,
                "form_id": form_id,
                "type": "deal_form_collection",
                **(metadata or {}),
            },
            "created_at": now,
            "updated_at": now,
        }
        sharing_link = SharingLink(**sharing_link_data)
        await sharing_link.save()

        # Generate URLs
        sharing_link.url = generate_sharing_url(token)
        sharing_link.short_url = generate_short_url(short_code)

        return sharing_link.model_dump(by_alias=True)
