"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { toast } from "sonner"

import { SettingsLayout } from "@/components/core/settings/settings-layout"
import { MembersTable } from "@/components/core/settings/members-table"
import { InviteUserForm } from "@/components/core/settings/invite-user-form"
import { Separator } from "@/components/ui/separator"
import { SettingsAPI, OrganizationMember } from "@/lib/api/settings-api"
import { useAuth } from "@/lib/auth-context"

export default function MembersSettingsPage() {
  const router = useRouter()
  const { user } = useAuth()
  const [members, setMembers] = useState<OrganizationMember[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadMembers()
  }, [])

  const loadMembers = async () => {
    try {
      setIsLoading(true)
      const membersData = await SettingsAPI.getMembers()
      setMembers(membersData)
    } catch (error: any) {
      console.error("Failed to load members:", error)
      toast.error("Failed to load members")
    } finally {
      setIsLoading(false)
    }
  }

  const handleInviteMembers = async (data: {
    emails: string[]
    role_id?: string
    message?: string
  }) => {
    try {
      const result = await SettingsAPI.inviteMembers(data)
      
      if (result.total_sent > 0) {
        toast.success(`Invitations sent to ${result.total_sent} people!`)
      }
      
      if (result.total_failed > 0) {
        toast.warning(`${result.total_failed} invitations failed to send`)
      }

      // Reload members list
      await loadMembers()
      
      return result
    } catch (error: any) {
      console.error("Failed to invite members:", error)
      toast.error(error.message || "Failed to send invitations")
      throw error
    }
  }

  const handleRemoveMember = async (memberId: string) => {
    try {
      await SettingsAPI.removeMember(memberId)
      toast.success("Member removed successfully!")
      
      // Reload members list
      await loadMembers()
    } catch (error: any) {
      console.error("Failed to remove member:", error)
      toast.error(error.message || "Failed to remove member")
    }
  }

  const handleResendInvite = async (email: string) => {
    try {
      await handleInviteMembers({ emails: [email] })
      toast.success("Invitation resent successfully!")
    } catch (error: any) {
      console.error("Failed to resend invite:", error)
      toast.error("Failed to resend invitation")
    }
  }

  if (isLoading) {
    return (
      <SettingsLayout activeTab="members">
        <div className="flex items-center justify-center py-12">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="size-8 rounded-full border-2 border-primary border-t-transparent"
          />
        </div>
      </SettingsLayout>
    )
  }

  return (
    <SettingsLayout activeTab="members">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="space-y-8"
      >
        {/* Invite Form */}
        <InviteUserForm onInvite={handleInviteMembers} />
        
        <Separator />
        
        {/* Members Table */}
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">Team Members</h3>
            <p className="mt-1 text-sm text-muted-foreground">
              Manage your organization's team members and their permissions
            </p>
          </div>
          
          <MembersTable
            members={members}
            currentUserId={user?.id || ""}
            onResendInvite={handleResendInvite}
            onRemoveMember={handleRemoveMember}
          />
        </div>
      </motion.div>
    </SettingsLayout>
  )
}
