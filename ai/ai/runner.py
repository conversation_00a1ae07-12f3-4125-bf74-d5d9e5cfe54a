"""
LangChain agent execution logic for TractionX AI Agent system.
"""

import time
import uuid
from typing import Dict, Any, List, Optional
import logging
from langchain.agents import AgentExecutor, create_openai_functions_agent
from langchain.tools import BaseTool as LangChainBaseTool
from langchain_openai import Chat<PERSON>penAI
from langchain.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain.schema import BaseMessage
from langchain.memory import ConversationBufferWindowMemory
import json

from ai.models import Agent, AgentContext
from ai.tools.base import BaseTool, tool_registry
from ai.audit import audit_logger
from ai.config import settings

logger = logging.getLogger(__name__)


class LangChainToolWrapper(LangChainBaseTool):
    """Wrapper to make TractionX tools compatible with LangChain."""

    name: str
    description: str
    
    def __init__(self, tool: BaseTool, context: AgentContext, **kwargs):
        logger.debug(f"Creating Lang<PERSON>hain wrapper for tool: {tool.name}")
        logger.debug(f"Tool details: description='{tool.description[:50]}...', category={getattr(tool, 'category', 'unknown')}")
        
        # Initialize with proper Pydantic model first
        super().__init__(
            name=tool.name,
            description=tool.description,
            **kwargs
        )
        
        # Then set our custom attributes after Pydantic initialization
        object.__setattr__(self, 'tool', tool)
        object.__setattr__(self, 'context', context)
        
        logger.debug(f"LangChain tool wrapper created for: {tool.name}")

    def _run(self, input_data: str) -> str:
        """Run the tool synchronously (not used in async context)."""
        logger.warning(f"Synchronous _run called for tool {self.tool.name} - this should not happen in async context")
        raise NotImplementedError("Use async version")

    async def _arun(self, input_data: str) -> str:
        """Run the tool asynchronously."""
        logger.debug(f"Running tool {self.tool.name} with input: {input_data[:100]}...")
        logger.debug(f"Tool execution context: user_id={self.context.user_id}, org_id={self.context.org_id}")
        
        try:
            # Parse input data
            if isinstance(input_data, str):
                logger.debug(f"Input data is string, attempting JSON parse for tool {self.tool.name}")
                try:
                    logger.debug(f"Parsing JSON input for tool {self.tool.name}")
                    parsed_input = json.loads(input_data)
                    logger.debug(f"Successfully parsed JSON input for tool {self.tool.name}: {list(parsed_input.keys()) if isinstance(parsed_input, dict) else type(parsed_input).__name__}")
                except json.JSONDecodeError as e:
                    logger.debug(f"Input for tool {self.tool.name} is not valid JSON ({e}), using as raw input")
                    parsed_input = {"input": input_data}
            else:
                logger.debug(f"Input for tool {self.tool.name} is already parsed: {type(input_data).__name__}")
                parsed_input = input_data

            # Create tool input - detect the proper input class for this tool
            logger.debug(f"Creating tool input object for {self.tool.name}")
            
            # Get the tool's execute method signature to find the input type
            import inspect
            
            # Get the execute method and its type annotations
            execute_method = getattr(self.tool, 'execute', None)
            if execute_method and hasattr(execute_method, '__annotations__'):
                type_hints = execute_method.__annotations__
                input_param_type = type_hints.get('input_data')
                
                if input_param_type and input_param_type != type(None):
                    logger.debug(f"Found specific input class for tool {self.tool.name}: {input_param_type.__name__}")
                    try:
                        # Handle LangChain's default input format mapping
                        if isinstance(parsed_input, dict) and 'input' in parsed_input and len(parsed_input) == 1:
                            # LangChain passes single argument as {'input': 'value'}
                            # Map to the expected field name for this tool
                            if input_param_type.__name__ == 'WebSearchInput':
                                logger.debug(f"Mapping LangChain 'input' to 'query' for WebSearchInput")
                                parsed_input = {'query': parsed_input['input']}
                            elif hasattr(input_param_type, '__fields__') and 'query' in input_param_type.__fields__:
                                logger.debug(f"Mapping LangChain 'input' to 'query' for {input_param_type.__name__}")
                                parsed_input = {'query': parsed_input['input']}
                            elif hasattr(input_param_type, '__fields__') and 'query_text' in input_param_type.__fields__:
                                logger.debug(f"Mapping LangChain 'input' to 'query_text' for {input_param_type.__name__}")
                                parsed_input = {'query_text': parsed_input['input']}
                            elif input_param_type.__name__ == 'NewsIntelligenceInput':
                                logger.debug(f"Mapping LangChain 'input' to 'query' for NewsIntelligenceInput")
                                parsed_input = {'query': parsed_input['input']}
                            # Add more mappings as needed for other tools
                            logger.debug(f"Mapped input for {self.tool.name}: {parsed_input}")
                        
                        tool_input = input_param_type(**parsed_input)
                        logger.debug(f"Tool input created successfully using {input_param_type.__name__} for {self.tool.name}")
                    except Exception as e:
                        logger.error(f"Failed to create {input_param_type.__name__} for tool {self.tool.name}: {e}")
                        logger.debug(f"Input data was: {parsed_input}")
                        logger.debug(f"Expected input type: {input_param_type}")
                        logger.debug(f"Available input keys: {list(parsed_input.keys()) if isinstance(parsed_input, dict) else 'Not a dict'}")
                        
                        # Try to see what fields are required vs provided (Pydantic v2 compatible)
                        if hasattr(input_param_type, '__fields__'):
                            try:
                                # Pydantic v2 compatibility
                                fields_info = input_param_type.__fields__
                                required_fields = []
                                optional_fields = []
                                
                                for name, field in fields_info.items():
                                    # Handle both Pydantic v1 and v2 field inspection
                                    is_required = False
                                    try:
                                        # Try Pydantic v2 style
                                        is_required = field.is_required()
                                    except AttributeError:
                                        try:
                                            # Try Pydantic v1 style
                                            is_required = field.required
                                        except AttributeError:
                                            # Default to checking if default is available
                                            is_required = not hasattr(field, 'default') or field.default is None
                                    
                                    if is_required:
                                        required_fields.append(name)
                                    else:
                                        optional_fields.append(name)
                                
                                logger.debug(f"Required fields for {input_param_type.__name__}: {required_fields}")
                                logger.debug(f"Optional fields for {input_param_type.__name__}: {optional_fields}")
                            except Exception as field_error:
                                logger.debug(f"Could not inspect fields for {input_param_type.__name__}: {field_error}")
                        
                        raise
                else:
                    logger.debug(f"No specific input type found for tool {self.tool.name}, using default ToolInput")
                    from ai.tools.base import ToolInput
                    try:
                        tool_input = ToolInput(**parsed_input)
                        logger.debug(f"Default tool input created successfully for {self.tool.name}")
                    except Exception as e:
                        logger.error(f"Failed to create default tool input for {self.tool.name}: {e}")
                        raise
            else:
                logger.debug(f"Tool {self.tool.name} has no execute method annotations, using default ToolInput")
                from ai.tools.base import ToolInput
                try:
                    tool_input = ToolInput(**parsed_input)
                    logger.debug(f"Default tool input created successfully for {self.tool.name}")
                except Exception as e:
                    logger.error(f"Failed to create default tool input for {self.tool.name}: {e}")
                    raise

            # Execute tool
            logger.debug(f"Executing tool {self.tool.name}")
            start_time = time.time()
            result = await self.tool.execute(tool_input, self.context)
            execution_time = time.time() - start_time
            logger.debug(f"Tool {self.tool.name} executed in {execution_time:.2f}s with success={result.success if result else 'None'}")
            
            if result and result.success:
                logger.debug(f"Tool {self.tool.name} result data keys: {list(result.data.keys()) if hasattr(result, 'data') and result.data else 'None'}")
            elif result:
                logger.debug(f"Tool {self.tool.name} failed with error: {result.error}")

            # Log tool usage
            logger.debug(f"Logging usage for tool {self.tool.name}")
            try:
                await audit_logger.log_tool_usage(
                    agent_id=str(self.context.metadata.get("agent_id", "")),
                    user_id=str(self.context.user_id),
                    org_id=str(self.context.org_id),
                    tool_name=self.tool.name,
                    tool_input=parsed_input,
                    tool_output=result.dict() if result else None,
                    execution_time=execution_time,
                    session_id=self.context.session_id,
                    deal_id=str(self.context.deal_id) if self.context.deal_id else None
                )
                logger.debug(f"Tool usage logged successfully for {self.tool.name}")
            except Exception as e:
                logger.error(f"Failed to log tool usage for {self.tool.name}: {e}")

            # Return result as string
            if result and result.success:
                logger.debug(f"Tool {self.tool.name} succeeded, returning result")
                
                # Custom JSON encoder to handle datetime and other non-serializable objects
                class CustomJSONEncoder(json.JSONEncoder):
                    def default(self, obj):
                        if hasattr(obj, 'isoformat'):  # datetime objects
                            return obj.isoformat()
                        elif hasattr(obj, '__dict__'):  # Pydantic objects or other classes
                            return obj.__dict__
                        elif hasattr(obj, '_asdict'):  # namedtuples
                            return obj._asdict()
                        return str(obj)  # fallback to string representation
                
                try:
                    result_json = json.dumps(result.dict(), cls=CustomJSONEncoder)
                    logger.debug(f"Tool {self.tool.name} result size: {len(result_json)} characters")
                    return result_json
                except Exception as json_error:
                    logger.warning(f"Failed to serialize result for tool {self.tool.name}: {json_error}")
                    # Return a simplified version
                    return json.dumps({
                        "success": True,
                        "data": str(result.data) if result.data else None,
                        "message": "Tool executed successfully but result contains non-serializable data"
                    })
            else:
                error_msg = result.error if result else "No result returned"
                logger.warning(f"Tool {self.tool.name} failed: {error_msg}")
                return f"Tool execution failed: {error_msg}"

        except Exception as e:
            logger.error(f"Tool {self.tool.name} execution failed: {e}")
            logger.debug(f"Tool {self.tool.name} error type: {type(e).__name__}")
            return f"Tool execution error: {str(e)}"


class AgentRunner:
    """LangChain agent execution runner."""

    def __init__(self):
        logger.debug("Initializing AgentRunner")
        self.llm_cache: Dict[str, ChatOpenAI] = {}
        self.memory_cache: Dict[str, ConversationBufferWindowMemory] = {}

        # Initialize unified tool registry
        tool_registry.discover_tools()
        logger.info(f"AgentRunner initialized with {len(tool_registry.list_available_tools())} tools")

    @property
    def tool_registry(self):
        """Get all available tools from the unified registry."""
        return tool_registry.get_all_tools()

    async def run_agent(
        self,
        agent: Agent,
        context: AgentContext,
        input_message: str,
        conversation_history: Optional[List[BaseMessage]] = None
    ) -> Dict[str, Any]:
        """Run an agent with the given input."""

        logger.info(f"Running agent: {agent.name} (ID: {agent.id})")
        logger.debug(f"Agent input: {input_message[:100]}...")
        logger.debug(f"Agent type: {agent.type}, model: {agent.model}")
        logger.debug(f"Agent configuration: max_iterations={agent.max_iterations}, temperature={agent.temperature}")

        session_id = context.session_id or str(uuid.uuid4())
        context.session_id = session_id
        context.metadata["agent_id"] = str(agent.id)

        logger.debug(f"Agent session ID: {session_id}")
        logger.debug(f"Agent context: user_id={context.user_id}, org_id={context.org_id}, deal_id={context.deal_id}")
        logger.debug(f"Context metadata: {list(context.metadata.keys())}")

        start_time = time.time()

        try:
            # Create LLM
            logger.debug(f"Creating LLM with model={agent.model}, temperature={agent.temperature}, max_tokens={agent.max_tokens}")
            try:
                llm = ChatOpenAI(
                    model=agent.model,
                    temperature=agent.temperature,
                    max_tokens=agent.max_tokens,
                    openai_api_key=settings.OPENAI_API_KEY
                )
                logger.debug("LLM created successfully")
            except Exception as e:
                logger.error(f"Failed to create LLM: {e}")
                raise

            # Prepare tools
            logger.debug(f"Preparing tools for agent: {agent.tools}")
            langchain_tools = []
            for tool_name in agent.tools:
                logger.debug(f"Processing tool: {tool_name}")
                if tool_name in self.tool_registry:
                    tool = self.tool_registry[tool_name]
                    logger.debug(f"Adding tool to agent: {tool_name}")
                    try:
                        wrapped_tool = LangChainToolWrapper(tool, context)
                        langchain_tools.append(wrapped_tool)
                        logger.debug(f"Tool {tool_name} wrapped and added successfully")
                    except Exception as e:
                        logger.error(f"Failed to wrap tool {tool_name}: {e}")
                        raise
                else:
                    logger.warning(f"Tool not found: {tool_name}")

            logger.debug(f"Agent has {len(langchain_tools)} tools available")
            logger.debug(f"Available tool names: {[tool.name for tool in langchain_tools]}")

            # Create prompt template
            logger.debug("Creating prompt template")
            try:
                prompt = self._create_prompt_template(agent)
                logger.debug("Prompt template created successfully")
            except Exception as e:
                logger.error(f"Failed to create prompt template: {e}")
                raise

            # Create agent
            logger.debug("Creating LangChain agent")
            try:
                langchain_agent = create_openai_functions_agent(
                    llm=llm,
                    tools=langchain_tools,
                    prompt=prompt
                )
                logger.debug("LangChain agent created successfully")
            except Exception as e:
                logger.error(f"Failed to create LangChain agent: {e}")
                raise

            # Create memory
            memory_size = agent.config.get("memory_size", settings.AGENT_MEMORY_SIZE)
            logger.debug(f"Creating conversation memory with size={memory_size}")
            try:
                memory = ConversationBufferWindowMemory(
                    k=memory_size,
                    memory_key="chat_history",
                    return_messages=True
                )
                logger.debug("Conversation memory created successfully")
            except Exception as e:
                logger.error(f"Failed to create conversation memory: {e}")
                raise

            # Add conversation history to memory
            if conversation_history:
                logger.debug(f"Adding {len(conversation_history)} messages to conversation history")
                try:
                    for i, message in enumerate(conversation_history):
                        logger.debug(f"Adding message {i+1}/{len(conversation_history)}: {type(message).__name__}")
                        memory.chat_memory.add_message(message)
                    logger.debug("All conversation history messages added successfully")
                except Exception as e:
                    logger.error(f"Failed to add conversation history: {e}")
                    raise
            else:
                logger.debug("No conversation history provided")

            # Create agent executor
            logger.debug(f"Creating agent executor with max_iterations={agent.max_iterations}")
            try:
                agent_executor = AgentExecutor(
                    agent=langchain_agent,
                    tools=langchain_tools,
                    memory=memory,
                    verbose=settings.DEBUG,
                    max_iterations=agent.max_iterations,
                    handle_parsing_errors=True
                )
                logger.debug("Agent executor created successfully")
            except Exception as e:
                logger.error(f"Failed to create agent executor: {e}")
                raise

            # Execute agent
            logger.info(f"Executing agent {agent.name}")
            logger.debug("Starting agent execution...")
            try:
                result = await agent_executor.ainvoke({
                    "input": input_message,
                    "chat_history": memory.chat_memory.messages
                })
                logger.debug("Agent execution completed")
            except Exception as e:
                logger.error(f"Agent execution failed: {e}")
                raise

            execution_time = time.time() - start_time
            logger.debug(f"Agent execution completed in {execution_time:.2f}s")

            # Extract output
            logger.debug("Extracting agent execution results...")
            output = result.get("output", "")
            intermediate_steps = result.get("intermediate_steps", [])
            logger.debug(f"Agent used {len(intermediate_steps)} steps to generate response")
            logger.debug(f"Agent output length: {len(output)} characters")

            # Extract tools used - fix the attribute access issue
            tools_used = []
            logger.debug("Extracting tools used from intermediate steps...")
            for i, step in enumerate(intermediate_steps):
                logger.debug(f"Processing step {i+1}/{len(intermediate_steps)}")
                if len(step) >= 1 and hasattr(step[0], 'tool'):
                    tool_name = step[0].tool
                    tools_used.append(tool_name)
                    logger.debug(f"Step {i+1} used tool: {tool_name}")
                elif len(step) >= 1:
                    # Try alternative attribute names for tool identification
                    step_action = step[0]
                    if hasattr(step_action, 'tool_name'):
                        tool_name = step_action.tool_name
                        tools_used.append(tool_name)
                        logger.debug(f"Step {i+1} used tool (via tool_name): {tool_name}")
                    elif hasattr(step_action, 'name'):
                        tool_name = step_action.name
                        tools_used.append(tool_name)
                        logger.debug(f"Step {i+1} used tool (via name): {tool_name}")
                    else:
                        logger.debug(f"Step {i+1} could not extract tool name, available attributes: {dir(step_action)}")
                else:
                    logger.debug(f"Step {i+1} has unexpected structure: {len(step)} elements")

            logger.debug(f"Total tools used: {len(tools_used)} - {tools_used}")

            # Log execution
            logger.debug("Logging agent execution to audit log")
            try:
                await audit_logger.log_agent_execution(
                    agent_id=str(agent.id),
                    user_id=str(context.user_id),
                    org_id=str(context.org_id),
                    input_data={"message": input_message},
                    output_data={"response": output, "steps": len(intermediate_steps)},
                    tools_used=tools_used,
                    execution_time=execution_time,
                    session_id=session_id,
                    deal_id=str(context.deal_id) if context.deal_id else None,
                    message="Agent execution completed successfully"
                )
                logger.debug("Agent execution logged successfully")
            except Exception as e:
                logger.error(f"Failed to log agent execution: {e}")

            logger.info(f"Agent {agent.name} execution successful")
            return {
                "success": True,
                "response": output,
                "session_id": session_id,
                "execution_time": execution_time,
                "tools_used": tools_used,
                "intermediate_steps": len(intermediate_steps)
            }

        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = str(e)

            logger.error(f"Agent {agent.name} execution failed after {execution_time:.2f}s: {error_msg}")
            logger.debug(f"Agent execution error type: {type(e).__name__}")

            # Log error
            logger.debug("Logging agent error to audit log")
            try:
                await audit_logger.log_error(
                    agent_id=str(agent.id),
                    user_id=str(context.user_id),
                    org_id=str(context.org_id),
                    error=error_msg,
                    input_data={"message": input_message},
                    session_id=session_id,
                    deal_id=str(context.deal_id) if context.deal_id else None
                )
                logger.debug("Agent error logged successfully")
            except Exception as log_error:
                logger.error(f"Failed to log agent error: {log_error}")

            return {
                "success": False,
                "error": error_msg,
                "session_id": session_id,
                "execution_time": execution_time
            }

    def _create_prompt_template(self, agent: Agent) -> ChatPromptTemplate:
        """Create prompt template for the agent."""

        logger.debug(f"Creating prompt template for agent: {agent.name}")

        system_prompt = agent.system_prompt or "You are a helpful AI assistant."
        logger.debug(f"Using system prompt: {system_prompt[:100]}...")

        if agent.instructions:
            logger.debug(f"Adding instructions to prompt: {agent.instructions[:100]}...")
            system_prompt += f"\n\nInstructions:\n{agent.instructions}"
        else:
            logger.debug("No additional instructions provided for agent")

        # Add context about available tools
        if agent.tools:
            logger.debug(f"Adding tool descriptions for {len(agent.tools)} tools")
            tools_description_parts = []
            for tool_name in agent.tools:
                if tool_name in self.tool_registry:
                    tool = self.tool_registry[tool_name]
                    description = tool.description or 'Tool description not available'
                    tools_description_parts.append(f"- {tool_name}: {description}")
                    logger.debug(f"Added description for tool: {tool_name}")
                else:
                    logger.warning(f"Tool {tool_name} not found in registry when building prompt")
            
            tools_description = "\n".join(tools_description_parts)

            if tools_description:
                logger.debug("Tool descriptions added to prompt")
                system_prompt += f"\n\nAvailable tools:\n{tools_description}"
        else:
            logger.debug("No tools configured for agent")

        # Create prompt template
        logger.debug("Building final prompt template with placeholders")
        try:
            prompt = ChatPromptTemplate.from_messages([
                ("system", system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{input}"),
                MessagesPlaceholder(variable_name="agent_scratchpad")
            ])
            logger.debug("Prompt template created successfully")
            logger.debug(f"Prompt template has {len(prompt.messages)} message components")
        except Exception as e:
            logger.error(f"Failed to create prompt template: {e}")
            raise

        return prompt

    async def get_agent_capabilities(self, agent: Agent) -> Dict[str, Any]:
        """Get agent capabilities and available tools."""

        logger.debug(f"Getting capabilities for agent: {agent.name} (ID: {agent.id})")

        capabilities = {
            "agent_id": str(agent.id),
            "name": agent.name,
            "description": agent.description,
            "type": agent.type,
            "model": agent.model,
            "tools": [],
            "categories": set()
        }

        logger.debug(f"Agent base capabilities: model={agent.model}, type={agent.type}")

        # Get tool information
        logger.debug(f"Processing {len(agent.tools)} tools for agent capabilities")
        for tool_name in agent.tools:
            logger.debug(f"Processing capabilities for tool: {tool_name}")
            if tool_name in self.tool_registry:
                tool = self.tool_registry[tool_name]
                logger.debug(f"Adding tool to capabilities: {tool_name}")
                try:
                    tool_info = {
                        "name": tool.name,
                        "description": tool.description,
                        "schema": tool.get_schema()
                    }
                    capabilities["tools"].append(tool_info)
                    logger.debug(f"Tool {tool_name} capabilities added successfully")

                    # Add category if available
                    if hasattr(tool, 'category'):
                        logger.debug(f"Adding category from tool {tool_name}: {tool.category}")
                        capabilities["categories"].add(tool.category)
                    else:
                        logger.debug(f"Tool {tool_name} has no category attribute")
                except Exception as e:
                    logger.error(f"Failed to get capabilities for tool {tool_name}: {e}")
            else:
                logger.warning(f"Tool {tool_name} not found in registry when building capabilities")

        capabilities["categories"] = list(capabilities["categories"])
        logger.debug(f"Agent capabilities complete: {len(capabilities['tools'])} tools, {len(capabilities['categories'])} categories")

        return capabilities


# Global agent runner instance
logger.debug("Creating global agent runner instance")
agent_runner = AgentRunner()
logger.debug("Global agent runner instance created")
