import { NoteContent, MentionElement } from '@/lib/types/deal-notes';

/**
 * Convert plain text with @mentions to Slate.js structured content
 */
export function convertTextToStructuredContent(text: string, mentions: {[key: string]: string}): NoteContent {
  if (!text.trim()) {
    return [
      {
        type: 'paragraph',
        children: [{ text: '' }]
      }
    ];
  }

  // Improved regex: match @ followed by word chars, dashes, dots, or numbers
  const parts = text.split(/(@[\w.-]+)/g);
  const children: any[] = [];

  for (const part of parts) {
    if (part.startsWith('@')) {
      const displayName = part.substring(1); // Remove @
      const userId = mentions[displayName];
      if (userId) {
        // Create mention element
        const mentionElement: MentionElement = {
          type: 'mention',
          user_id: userId,
          name: displayName,
          children: [{ text: '' }]
        };
        children.push(mentionElement);
      } else {
        // If no user ID found, treat as plain text
        children.push({ text: part });
      }
    } else {
      // Plain text
      children.push({ text: part });
    }
  }

  return [
    {
      type: 'paragraph',
      children
    }
  ];
}

function isMentionElement(child: any): child is MentionElement {
  return child && child.type === 'mention';
}

/**
 * Convert Slate.js structured content back to plain text
 */
export function convertStructuredContentToText(content: NoteContent): string {
  let text = '';
  
  for (const element of content) {
    if (element.type === 'paragraph') {
      for (const child of element.children) {
        if (isMentionElement(child)) {
          text += `@${child.name}`;
        } else if (typeof child.text === 'string') {
          text += child.text;
        }
      }
    }
  }
  
  return text;
} 