"""
RDS (PostgreSQL) storage implementation for TractionX Data Pipeline Service.
"""

import json
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union
from uuid import UUID, uuid4

import asyncpg  # type: ignore
from asyncpg import Pool  # type: ignore

from app.configs import get_logger, settings
from app.models.base import BaseModel
from app.storage.interfaces import RelationalStorageInterface


class RDSStorage(RelationalStorageInterface):
    """PostgreSQL storage implementation."""

    def __init__(self):
        self.logger = get_logger(__name__)
        self.pool: Optional[Pool] = None
        self.connection_string = settings.database_connection_string

    async def initialize(self) -> None:
        """Initialize the database connection pool."""
        try:
            self.pool = await asyncpg.create_pool(
                self.connection_string,
                min_size=1,
                max_size=settings.DB_POOL_SIZE,
                max_inactive_connection_lifetime=300,
            )
            self.logger.info("RDS storage initialized successfully")

            # Create core tables if they don't exist
            # await self._create_core_tables()

        except Exception as e:
            self.logger.error(f"Failed to initialize RDS storage: {e}")
            raise

    async def cleanup(self) -> None:
        """Clean up database resources."""
        if self.pool:
            await self.pool.close()
            self.logger.info("RDS storage cleaned up")

    async def health_check(self) -> bool:
        """Check if database is healthy."""
        try:
            if not self.pool:
                return False

            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            return True

        except Exception as e:
            self.logger.error(f"RDS health check failed: {e}")
            return False

    async def create_table(self, table_name: str, schema: Dict[str, Any]) -> bool:
        """Create a table with the given schema."""
        try:
            # Convert schema to SQL DDL
            columns = []
            for column_name, column_def in schema.items():
                columns.append(f"{column_name} {column_def}")

            ddl = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns)})"

            if not self.pool:
                raise ValueError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                await conn.execute(ddl)

            self.logger.info(f"Created table {table_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to create table {table_name}: {e}")
            return False

    async def insert(
        self, table_name: str, data: Union[Dict[str, Any], BaseModel]
    ) -> str:
        """Insert data and return the ID."""
        try:
            # Convert BaseModel to dict if needed
            if isinstance(data, BaseModel):
                data_dict = data.model_dump()
            else:
                data_dict = data.copy()

            # Add timestamp if not present
            if "created_at" not in data_dict:
                data_dict["created_at"] = datetime.now(timezone.utc)

            # Prepare SQL
            columns = list(data_dict.keys())
            placeholders = [f"${i + 1}" for i in range(len(columns))]
            values = [self._serialize_value(data_dict[col]) for col in columns]

            sql = f"""
                INSERT INTO {table_name} ({", ".join(columns)})
                VALUES ({", ".join(placeholders)})
                RETURNING id
            """

            if not self.pool:
                raise ValueError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                record_id = await conn.fetchval(sql, *values)

            self.logger.debug(f"Inserted record into {table_name} with ID {record_id}")
            return str(record_id)

        except Exception as e:
            self.logger.error(f"Failed to insert into {table_name}: {e}")
            raise

    async def update(
        self, table_name: str, id: str, data: Union[Dict[str, Any], BaseModel]
    ) -> bool:
        """Update data by ID."""
        try:
            # Convert BaseModel to dict if needed
            if isinstance(data, BaseModel):
                data_dict = data.model_dump()
            else:
                data_dict = data.copy()

            # Add updated timestamp
            data_dict["updated_at"] = datetime.now(timezone.utc)

            # Prepare SQL
            set_clauses = []
            values = []
            for i, (column, value) in enumerate(data_dict.items()):
                set_clauses.append(f"{column} = ${i + 1}")
                values.append(self._serialize_value(value))

            values.append(id)  # For WHERE clause

            sql = f"""
                UPDATE {table_name}
                SET {", ".join(set_clauses)}
                WHERE id = ${len(values)}
            """

            if not self.pool:
                raise ValueError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                result = await conn.execute(sql, *values)

            # Check if any rows were updated
            rows_updated = int(result.split()[-1])
            success = rows_updated > 0

            if success:
                self.logger.debug(f"Updated record in {table_name} with ID {id}")
            else:
                self.logger.warning(
                    f"No record found to update in {table_name} with ID {id}"
                )

            return success

        except Exception as e:
            self.logger.error(f"Failed to update {table_name} record {id}: {e}")
            return False

    async def upsert(
        self,
        table_name: str,
        data: Union[Dict[str, Any], BaseModel],
        key_fields: List[str],
    ) -> str:
        """Insert or update data based on key fields."""
        try:
            # Convert BaseModel to dict if needed
            if isinstance(data, BaseModel):
                data_dict = data.model_dump()
            else:
                data_dict = data.copy()

            # Add timestamps
            now = datetime.now(timezone.utc)
            if "created_at" not in data_dict:
                data_dict["created_at"] = now
            data_dict["updated_at"] = now

            # Build conflict resolution
            conflict_columns = ", ".join(key_fields)
            update_clauses = []
            for column in data_dict.keys():
                if column not in key_fields:
                    update_clauses.append(f"{column} = EXCLUDED.{column}")

            # Prepare SQL
            columns = list(data_dict.keys())
            placeholders = [f"${i + 1}" for i in range(len(columns))]
            values = [self._serialize_value(data_dict[col]) for col in columns]

            sql = f"""
                INSERT INTO {table_name} ({", ".join(columns)})
                VALUES ({", ".join(placeholders)})
                ON CONFLICT ({conflict_columns})
                DO UPDATE SET {", ".join(update_clauses)}
                RETURNING id
            """

            if not self.pool:
                raise ValueError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                record_id = await conn.fetchval(sql, *values)

            self.logger.debug(f"Upserted record in {table_name} with ID {record_id}")
            return str(record_id)

        except Exception as e:
            self.logger.error(f"Failed to upsert into {table_name}: {e}")
            raise

    async def get_by_id(self, table_name: str, id: str) -> Optional[Dict[str, Any]]:
        """Get data by ID."""
        try:
            sql = f"SELECT * FROM {table_name} WHERE id = $1"

            if not self.pool:
                raise ValueError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                record = await conn.fetchrow(sql, id)

            if record:
                return dict(record)
            return None

        except Exception as e:
            self.logger.error(
                f"Failed to get record from {table_name} with ID {id}: {e}"
            )
            return None

    async def get_by_fields(
        self, table_name: str, filters: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Get data by field filters."""
        try:
            # Build WHERE clause
            where_clauses = []
            values = []
            for i, (column, value) in enumerate(filters.items()):
                where_clauses.append(f"{column} = ${i + 1}")
                values.append(self._serialize_value(value))

            where_clause = " AND ".join(where_clauses) if where_clauses else "TRUE"
            sql = f"SELECT * FROM {table_name} WHERE {where_clause}"

            if not self.pool:
                raise ValueError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                records = await conn.fetch(sql, *values)

            return [dict(record) for record in records]

        except Exception as e:
            self.logger.error(f"Failed to get records from {table_name}: {e}")
            return []

    async def delete(self, table_name: str, id: str) -> bool:
        """Delete data by ID."""
        try:
            sql = f"DELETE FROM {table_name} WHERE id = $1"

            if not self.pool:
                raise ValueError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                result = await conn.execute(sql, id)

            rows_deleted = int(result.split()[-1])
            success = rows_deleted > 0

            if success:
                self.logger.debug(f"Deleted record from {table_name} with ID {id}")
            else:
                self.logger.warning(
                    f"No record found to delete from {table_name} with ID {id}"
                )

            return success

        except Exception as e:
            self.logger.error(f"Failed to delete from {table_name} record {id}: {e}")
            return False

    async def execute_query(
        self, query: str, params: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Execute a custom query."""
        try:
            if not self.pool:
                raise ValueError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                if params:
                    records = await conn.fetch(query, *params.values())
                else:
                    records = await conn.fetch(query)

            return [dict(record) for record in records]

        except Exception as e:
            self.logger.error(f"Failed to execute query: {e}")
            return []

    def _serialize_value(self, value: Any) -> Any:
        """Serialize value for database storage."""
        from uuid import UUID

        if isinstance(value, UUID):
            return str(value)
        elif isinstance(value, dict):
            return json.dumps(value, default=self._json_serializer)
        elif isinstance(value, list):
            # For PostgreSQL arrays, return the list as-is
            # For JSON fields, serialize to JSON string
            return value
        elif isinstance(value, datetime):
            return value
        elif isinstance(value, str):
            # Try to parse ISO datetime strings back to datetime objects
            try:
                return datetime.fromisoformat(value.replace("Z", "+00:00"))
            except (ValueError, AttributeError):
                # Not a datetime string, return as-is
                return value
        else:
            return value

    def _json_serializer(self, obj: Any) -> Any:
        """Custom JSON serializer for complex objects."""
        from uuid import UUID

        from pydantic import HttpUrl

        if isinstance(obj, UUID):
            return str(obj)
        elif isinstance(obj, HttpUrl):
            return str(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, "model_dump"):  # Pydantic models
            return obj.model_dump()
        elif hasattr(obj, "__dict__"):
            return obj.__dict__
        else:
            raise TypeError(
                f"Object of type {type(obj).__name__} is not JSON serializable"
            )

    async def _create_core_tables(self) -> None:
        """Create core tables for the pipeline."""

        # Enable UUID extension first
        await self._enable_uuid_extension()

        # Legacy tables (keeping for backward compatibility during migration)
        legacy_tables = {
            "companies": {
                "id": "SERIAL PRIMARY KEY",
                "company_id": "VARCHAR(255) UNIQUE NOT NULL",
                "org_id": "VARCHAR(255) NOT NULL",
                "name": "VARCHAR(500)",
                "data": "JSONB",
                "source": "VARCHAR(100)",
                "created_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
                "updated_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
            },
            "founders_legacy": {
                "id": "SERIAL PRIMARY KEY",
                "founder_id": "VARCHAR(255) UNIQUE NOT NULL",
                "company_id": "VARCHAR(255) NOT NULL",
                "org_id": "VARCHAR(255) NOT NULL",
                "name": "VARCHAR(500)",
                "data": "JSONB",
                "source": "VARCHAR(100)",
                "s3_raw_data_key": "VARCHAR(1000)",
                "error_message": "TEXT",
                "created_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
                "updated_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
            },
            "news": {
                "id": "SERIAL PRIMARY KEY",
                "company_id": "VARCHAR(255) NOT NULL",
                "org_id": "VARCHAR(255) NOT NULL",
                "data": "JSONB",
                "created_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
                "updated_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
            },
            "pipeline_jobs": {
                "id": "SERIAL PRIMARY KEY",
                "job_id": "VARCHAR(255) UNIQUE NOT NULL",
                "pipeline_name": "VARCHAR(255) NOT NULL",
                "status": "VARCHAR(50) NOT NULL",
                "metadata": "JSONB",
                "created_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
                "updated_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
            },
        }

        # Create legacy tables first
        for table_name, schema in legacy_tables.items():
            await self.create_table(table_name, schema)

        # Create founders table first (parent table)
        founders_schema = {
            "id": "UUID PRIMARY KEY DEFAULT gen_random_uuid()",
            "founder_id": "VARCHAR(255) UNIQUE NOT NULL",
            "full_name": "VARCHAR(500)",
            "first_name": "VARCHAR(255)",
            "last_name": "VARCHAR(255)",
            "current_job_title": "VARCHAR(255)",
            "current_job_company": "VARCHAR(255)",
            "linkedin_url": "TEXT",
            "github_url": "TEXT",
            "location_country": "VARCHAR(100)",
            "org_id": "VARCHAR(255) NOT NULL",
            "company_id": "VARCHAR(255) NOT NULL",
            "source": "VARCHAR(100)",
            "confidence_score": "FLOAT",
            "enrichment_date": "TIMESTAMPTZ",
            "s3_raw_data_key": "TEXT",
            "created_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
            "updated_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
        }
        await self.create_table("founders", founders_schema)

        # Add unique constraint on id and company_id combination
        await self._add_founder_unique_constraint()

        # Create child tables that reference founders
        child_tables = {
            "founder_experiences": {
                "id": "UUID PRIMARY KEY DEFAULT gen_random_uuid()",
                "founder_id": "UUID REFERENCES founders(id) ON DELETE CASCADE",
                "company_name": "VARCHAR(255)",
                "title": "VARCHAR(255)",
                "industry": "VARCHAR(255)",
                "company_size": "VARCHAR(50)",
                "start_date": "DATE",
                "end_date": "DATE",
                "is_primary": "BOOLEAN",
                "location": "TEXT",
            },
            "founder_education": {
                "id": "UUID PRIMARY KEY DEFAULT gen_random_uuid()",
                "founder_id": "UUID REFERENCES founders(id) ON DELETE CASCADE",
                "school_name": "VARCHAR(255)",
                "degrees": "TEXT[]",
                "majors": "TEXT[]",
                "start_date": "DATE",
                "end_date": "DATE",
                "location": "TEXT",
            },
            "founder_skills": {
                "id": "UUID PRIMARY KEY DEFAULT gen_random_uuid()",
                "founder_id": "UUID REFERENCES founders(id) ON DELETE CASCADE",
                "skill": "TEXT",
            },
            "founder_profiles": {
                "id": "UUID PRIMARY KEY DEFAULT gen_random_uuid()",
                "founder_id": "UUID REFERENCES founders(id) ON DELETE CASCADE",
                "network": "VARCHAR(50)",
                "url": "TEXT",
            },
            "founder_signals": {
                "id": "UUID PRIMARY KEY DEFAULT gen_random_uuid()",
                "founder_id": "UUID REFERENCES founders(id) ON DELETE CASCADE",
                "score": "INTEGER CHECK (score >= 0 AND score <= 100)",
                "tags": "TEXT[]",
                "strengths": "JSONB",
                "risks": "JSONB",
                "generated_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
            },
            "team_signals": {
                "id": "UUID PRIMARY KEY DEFAULT gen_random_uuid()",
                "company_id": "VARCHAR(255)",
                "org_id": "VARCHAR(255)",
                "complementarity_score": "INTEGER CHECK (complementarity_score >= 0 AND complementarity_score <= 100)",
                "coverage": "JSONB",
                "narrative": "TEXT",
                "generated_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
            },
        }

        # Create child tables
        for table_name, schema in child_tables.items():
            await self.create_table(table_name, schema)

        # Create indexes for better performance
        await self._create_founder_indexes()

        # Create company enrichment tables
        await self._create_company_tables()

    async def _enable_uuid_extension(self) -> None:
        """Enable the UUID extension in PostgreSQL."""
        try:
            if not self.pool:
                raise ValueError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                await conn.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')
                self.logger.info("UUID extension enabled")
        except Exception as e:
            self.logger.error(f"Failed to enable UUID extension: {e}")
            raise

    async def _create_founder_indexes(self) -> None:
        """Create indexes for founder tables for optimal query performance."""
        indexes = [
            # Founders table indexes
            "CREATE INDEX IF NOT EXISTS idx_founders_founder_id ON founders(founder_id)",
            "CREATE INDEX IF NOT EXISTS idx_founders_org_id ON founders(org_id)",
            "CREATE INDEX IF NOT EXISTS idx_founders_company_id ON founders(company_id)",
            "CREATE INDEX IF NOT EXISTS idx_founders_org_company ON founders(org_id, company_id)",
            # Founder experiences indexes
            "CREATE INDEX IF NOT EXISTS idx_founder_experiences_founder_id ON founder_experiences(founder_id)",
            "CREATE INDEX IF NOT EXISTS idx_founder_experiences_company ON founder_experiences(company_name)",
            "CREATE INDEX IF NOT EXISTS idx_founder_experiences_primary ON founder_experiences(founder_id, is_primary)",
            # Founder education indexes
            "CREATE INDEX IF NOT EXISTS idx_founder_education_founder_id ON founder_education(founder_id)",
            "CREATE INDEX IF NOT EXISTS idx_founder_education_school ON founder_education(school_name)",
            # Founder skills indexes
            "CREATE INDEX IF NOT EXISTS idx_founder_skills_founder_id ON founder_skills(founder_id)",
            "CREATE INDEX IF NOT EXISTS idx_founder_skills_skill ON founder_skills(skill)",
            # Founder profiles indexes
            "CREATE INDEX IF NOT EXISTS idx_founder_profiles_founder_id ON founder_profiles(founder_id)",
            "CREATE INDEX IF NOT EXISTS idx_founder_profiles_network ON founder_profiles(network)",
            # Founder signals indexes
            "CREATE INDEX IF NOT EXISTS idx_founder_signals_founder_id ON founder_signals(founder_id)",
            "CREATE INDEX IF NOT EXISTS idx_founder_signals_score ON founder_signals(score)",
            # Team signals indexes
            "CREATE INDEX IF NOT EXISTS idx_team_signals_company_id ON team_signals(company_id)",
            "CREATE INDEX IF NOT EXISTS idx_team_signals_org_id ON team_signals(org_id)",
            "CREATE INDEX IF NOT EXISTS idx_team_signals_org_company ON team_signals(org_id, company_id)",
        ]

        if not self.pool:
            raise ValueError("Database pool not initialized")

        async with self.pool.acquire() as conn:
            for index_sql in indexes:
                try:
                    await conn.execute(index_sql)
                    self.logger.debug(f"Created index: {index_sql}")
                except Exception as e:
                    self.logger.warning(
                        f"Failed to create index: {index_sql}, error: {e}"
                    )

    async def insert_founder_with_relations(
        self,
        founder_data: Dict[str, Any],
        experiences: Optional[List[Dict[str, Any]]] = None,
        education: Optional[List[Dict[str, Any]]] = None,
        skills: Optional[List[str]] = None,
        profiles: Optional[List[Dict[str, Any]]] = None,
    ) -> str:
        """
        Insert a founder record with all related data in a transaction.

        Args:
            founder_data: Main founder record data
            experiences: List of experience records
            education: List of education records
            skills: List of skill strings
            profiles: List of profile records

        Returns:
            The founder UUID
        """
        if not self.pool:
            raise ValueError("Database pool not initialized")

        async with self.pool.acquire() as conn:
            async with conn.transaction():
                # Insert main founder record
                founder_id = await self._insert_single_record(
                    conn, "founders", founder_data
                )

                # Insert related records
                if experiences:
                    for exp in experiences:
                        exp["founder_id"] = founder_id
                        await self._insert_single_record(
                            conn, "founder_experiences", exp
                        )

                if education:
                    for edu in education:
                        edu["founder_id"] = founder_id
                        await self._insert_single_record(conn, "founder_education", edu)

                if skills:
                    for skill in skills:
                        skill_data = {"founder_id": founder_id, "skill": skill}
                        await self._insert_single_record(
                            conn, "founder_skills", skill_data
                        )

                if profiles:
                    for profile in profiles:
                        profile["founder_id"] = founder_id
                        await self._insert_single_record(
                            conn, "founder_profiles", profile
                        )

                self.logger.info(f"Inserted founder with relations: {founder_id}")
                return founder_id

    async def upsert_founder_with_relations(
        self,
        founder_data: Dict[str, Any],
        experiences: Optional[List[Dict[str, Any]]] = None,
        education: Optional[List[Dict[str, Any]]] = None,
        skills: Optional[List[str]] = None,
        profiles: Optional[List[Dict[str, Any]]] = None,
    ) -> str:
        """
        Upsert a founder record with all related data in a transaction.
        This method handles duplicates by updating existing records.

        Args:
            founder_data: Main founder record data
            experiences: List of experience records
            education: List of education records
            skills: List of skill strings
            profiles: List of profile records

        Returns:
            The founder UUID
        """
        if not self.pool:
            raise ValueError("Database pool not initialized")

        async with self.pool.acquire() as conn:
            async with conn.transaction():
                # Upsert main founder record using id and company_id as key fields
                founder_id = await self._upsert_single_record(
                    conn, "founders", founder_data, ["id", "company_id"]
                )

                # For related records, we need to handle them differently:
                # 1. Delete existing related records for this founder
                # 2. Insert new related records

                # Delete existing related records
                await conn.execute(
                    "DELETE FROM founder_experiences WHERE founder_id = $1", founder_id
                )
                await conn.execute(
                    "DELETE FROM founder_education WHERE founder_id = $1", founder_id
                )
                await conn.execute(
                    "DELETE FROM founder_skills WHERE founder_id = $1", founder_id
                )
                await conn.execute(
                    "DELETE FROM founder_profiles WHERE founder_id = $1", founder_id
                )

                # Insert new related records
                if experiences:
                    for exp in experiences:
                        exp["founder_id"] = founder_id
                        await self._insert_single_record(
                            conn, "founder_experiences", exp
                        )

                if education:
                    for edu in education:
                        edu["founder_id"] = founder_id
                        await self._insert_single_record(conn, "founder_education", edu)

                if skills:
                    for skill in skills:
                        skill_data = {"founder_id": founder_id, "skill": skill}
                        await self._insert_single_record(
                            conn, "founder_skills", skill_data
                        )

                if profiles:
                    for profile in profiles:
                        profile["founder_id"] = founder_id
                        await self._insert_single_record(
                            conn, "founder_profiles", profile
                        )

                self.logger.info(f"Upserted founder with relations: {founder_id}")
                return founder_id

    async def _insert_single_record(
        self, conn, table_name: str, data: Dict[str, Any]
    ) -> str:
        """Helper method to insert a single record within a transaction."""
        # Add timestamp if not present
        if "created_at" not in data and table_name == "founders":
            data["created_at"] = datetime.now(timezone.utc)

        # Prepare SQL
        columns = list(data.keys())
        placeholders = [f"${i + 1}" for i in range(len(columns))]
        values = [self._serialize_value(data[col]) for col in columns]

        sql = f"""
            INSERT INTO {table_name} ({", ".join(columns)})
            VALUES ({", ".join(placeholders)})
            RETURNING id
        """

        record_id = await conn.fetchval(sql, *values)
        return str(record_id)

    async def _upsert_single_record(
        self, conn, table_name: str, data: Dict[str, Any], key_fields: List[str]
    ) -> str:
        """Helper method to upsert a single record within a transaction."""
        # Add timestamps
        now = datetime.now(timezone.utc)
        if "created_at" not in data and table_name == "founders":
            data["created_at"] = now
        data["updated_at"] = now

        # Build conflict resolution
        conflict_columns = ", ".join(key_fields)
        update_clauses = []
        for column in data.keys():
            if column not in key_fields:
                update_clauses.append(f"{column} = EXCLUDED.{column}")

        # Prepare SQL
        columns = list(data.keys())
        placeholders = [f"${i + 1}" for i in range(len(columns))]
        values = [self._serialize_value(data[col]) for col in columns]

        sql = f"""
            INSERT INTO {table_name} ({", ".join(columns)})
            VALUES ({", ".join(placeholders)})
            ON CONFLICT ({conflict_columns})
            DO UPDATE SET {", ".join(update_clauses)}
            RETURNING id
        """

        record_id = await conn.fetchval(sql, *values)
        return str(record_id)

    async def get_founder_with_relations(
        self, founder_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get a founder record with all related data.

        Args:
            founder_id: The founder UUID or founder_id

        Returns:
            Dictionary with founder data and all relations
        """
        if not self.pool:
            raise ValueError("Database pool not initialized")

        async with self.pool.acquire() as conn:
            # Get main founder record
            founder_id = UUID(founder_id)  # type: ignore
            founder_sql = "SELECT * FROM founders WHERE id = $1"
            founder_record = await conn.fetchrow(founder_sql, founder_id)

            if not founder_record:
                return None

            founder_uuid = founder_record["id"]
            result = dict(founder_record)

            # Get experiences
            exp_sql = "SELECT * FROM founder_experiences WHERE founder_id = $1 ORDER BY start_date DESC"
            experiences = await conn.fetch(exp_sql, founder_uuid)
            result["experiences"] = [dict(exp) for exp in experiences]

            # Get education
            edu_sql = "SELECT * FROM founder_education WHERE founder_id = $1 ORDER BY start_date DESC"
            education = await conn.fetch(edu_sql, founder_uuid)
            result["education"] = [dict(edu) for edu in education]

            # Get skills
            skills_sql = (
                "SELECT skill FROM founder_skills WHERE founder_id = $1 ORDER BY skill"
            )
            skills = await conn.fetch(skills_sql, founder_uuid)
            result["skills"] = [skill["skill"] for skill in skills]

            # Get profiles
            profiles_sql = "SELECT * FROM founder_profiles WHERE founder_id = $1"
            profiles = await conn.fetch(profiles_sql, founder_uuid)
            result["profiles"] = [dict(profile) for profile in profiles]

            # Get signals
            signals_sql = "SELECT * FROM founder_signals WHERE founder_id = $1 ORDER BY generated_at DESC LIMIT 1"
            signal = await conn.fetchrow(signals_sql, founder_uuid)
            result["signals"] = dict(signal) if signal else None

            return result

    async def _add_founder_unique_constraint(self) -> None:
        """Add a unique constraint on the combination of id and org_id for the founders table."""
        try:
            if not self.pool:
                raise ValueError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                # Check if constraint already exists
                constraint_exists = await conn.fetchval(
                    """
                    SELECT COUNT(*) FROM information_schema.table_constraints 
                    WHERE table_name = 'founders' 
                    AND constraint_name = 'founders_id_company_id_unique'
                    """
                )

                if not constraint_exists:
                    await conn.execute(
                        """
                        ALTER TABLE founders ADD CONSTRAINT founders_id_company_id_unique UNIQUE (id, company_id);
                        """
                    )
                    self.logger.info(
                        "Added unique constraint on founders(id, company_id)"
                    )
                else:
                    self.logger.info(
                        "Unique constraint on founders(id, company_id) already exists"
                    )
        except Exception as e:
            self.logger.error(
                f"Failed to add unique constraint on founders(id, company_id): {e}"
            )
            raise

    async def insert_company_with_relations(
        self,
        company_data: Dict[str, Any],
        keywords: Optional[List[str]] = None,
        technologies: Optional[List[Dict[str, Any]]] = None,
        departments: Optional[List[Dict[str, Any]]] = None,
    ) -> str:
        """
        Insert a company record with all related data in a transaction.

        Args:
            company_data: Main company record data
            keywords: List of keyword strings
            technologies: List of technology records with category
            departments: List of department count records

        Returns:
            The company ID
        """
        if not self.pool:
            raise ValueError("Database pool not initialized")

        async with self.pool.acquire() as conn:
            async with conn.transaction():
                # Insert main company record
                company_id = await self._insert_single_record(
                    conn, "companies_enrichment", company_data
                )

                # Insert related records
                if keywords:
                    for keyword in keywords:
                        keyword_data = {
                            "id": str(uuid4()),
                            "company_id": company_id,
                            "keyword": keyword,
                        }
                        await self._insert_single_record(
                            conn, "company_keywords", keyword_data
                        )

                if technologies:
                    for tech in technologies:
                        tech_data = {
                            "id": str(uuid4()),
                            "company_id": company_id,
                            "technology": tech["technology"],
                            "category": tech.get("category"),
                        }
                        await self._insert_single_record(
                            conn, "company_technologies", tech_data
                        )

                if departments:
                    for dept in departments:
                        dept_data = {
                            "id": str(uuid4()),
                            "company_id": company_id,
                            "department": dept["department"],
                            "head_count": dept["head_count"],
                        }
                        await self._insert_single_record(
                            conn, "company_department_counts", dept_data
                        )

                self.logger.info(f"Inserted company with relations: {company_id}")
                return company_id

    async def upsert_company_with_relations(
        self,
        company_data: Dict[str, Any],
        keywords: Optional[List[str]] = None,
        technologies: Optional[List[Dict[str, Any]]] = None,
        departments: Optional[List[Dict[str, Any]]] = None,
    ) -> str:
        """
        Upsert a company record with all related data in a transaction.
        This method handles duplicates by updating existing records.

        Args:
            company_data: Main company record data
            keywords: List of keyword strings
            technologies: List of technology records with category
            departments: List of department count records

        Returns:
            The company ID
        """
        if not self.pool:
            raise ValueError("Database pool not initialized")

        async with self.pool.acquire() as conn:
            async with conn.transaction():
                # Upsert main company record using company_id as key field
                # Note: We need the business company_id for foreign keys, not the database id
                await self._upsert_single_record(
                    conn, "companies_enrichment", company_data, ["company_id"]
                )
                company_id = company_data["company_id"]  # Use the business identifier

                # For related records, we need to handle them differently:
                # 1. Delete existing related records for this company
                # 2. Insert new related records

                # Delete existing related records
                await conn.execute(
                    "DELETE FROM company_keywords WHERE company_id = $1", company_id
                )
                await conn.execute(
                    "DELETE FROM company_technologies WHERE company_id = $1", company_id
                )
                await conn.execute(
                    "DELETE FROM company_department_counts WHERE company_id = $1",
                    company_id,
                )

                # Insert new related records
                if keywords:
                    for keyword in keywords:
                        keyword_data = {
                            "id": str(uuid4()),
                            "company_id": company_id,
                            "keyword": keyword,
                        }
                        await self._insert_single_record(
                            conn, "company_keywords", keyword_data
                        )

                if technologies:
                    for tech in technologies:
                        tech_data = {
                            "id": str(uuid4()),
                            "company_id": company_id,
                            "technology": tech["technology"],
                            "category": tech.get("category"),
                        }
                        await self._insert_single_record(
                            conn, "company_technologies", tech_data
                        )

                if departments:
                    for dept in departments:
                        dept_data = {
                            "id": str(uuid4()),
                            "company_id": company_id,
                            "department": dept["department"],
                            "head_count": dept["head_count"],
                        }
                        await self._insert_single_record(
                            conn, "company_department_counts", dept_data
                        )

                self.logger.info(f"Upserted company with relations: {company_id}")
                return company_id

    async def upsert_company_with_relations_company_schema(
        self,
        company_data: Dict[str, Any],
        keywords: Optional[List[str]] = None,
        technologies: Optional[List[Dict[str, Any]]] = None,
        departments: Optional[List[Dict[str, Any]]] = None,
    ) -> str:
        """
        Upsert a company record with all related data in company schema.
        This method handles duplicates by updating existing records.

        Args:
            company_data: Main company record data
            keywords: List of keyword strings
            technologies: List of technology records with category
            departments: List of department count records

        Returns:
            The company ID
        """
        if not self.pool:
            raise ValueError("Database pool not initialized")

        async with self.pool.acquire() as conn:
            async with conn.transaction():
                # Upsert main company record using company_id as key field
                # Note: We need the business company_id for foreign keys, not the database id
                await self._upsert_single_record(
                    conn, "company.companies", company_data, ["company_id"]
                )
                company_id = company_data["company_id"]  # Use the business identifier

                # For related records, we need to handle them differently:
                # 1. Delete existing related records for this company
                # 2. Insert new related records

                # Delete existing related records
                await conn.execute(
                    "DELETE FROM company_keywords WHERE company_id = $1", company_id
                )
                await conn.execute(
                    "DELETE FROM company_technologies WHERE company_id = $1", company_id
                )
                await conn.execute(
                    "DELETE FROM company_department_counts WHERE company_id = $1",
                    company_id,
                )

                # Insert new related records
                if keywords:
                    for keyword in keywords:
                        keyword_data = {
                            "id": str(uuid4()),
                            "company_id": company_id,
                            "keyword": keyword,
                        }
                        await self._insert_single_record(
                            conn, "company_keywords", keyword_data
                        )

                if technologies:
                    for tech in technologies:
                        tech_data = {
                            "id": str(uuid4()),
                            "company_id": company_id,
                            "technology": tech["technology"],
                            "category": tech.get("category"),
                        }
                        await self._insert_single_record(
                            conn, "company_technologies", tech_data
                        )

                if departments:
                    for dept in departments:
                        dept_data = {
                            "id": str(uuid4()),
                            "company_id": company_id,
                            "department": dept["department"],
                            "head_count": dept["head_count"],
                        }
                        await self._insert_single_record(
                            conn, "company_department_counts", dept_data
                        )

                self.logger.info(
                    f"Upserted company with relations in company schema: {company_id}"
                )
                return company_id

    async def get_or_create_company_by_domain(
        self, domain: str, org_id: str, source: str = "comprehensive_enrichment"
    ) -> str:
        """
        Get existing company UUID or create new one in company schema.

        Args:
            domain: Company domain
            org_id: Organization ID
            source: Data source (default: comprehensive_enrichment)

        Returns:
            Company UUID
        """
        try:
            # Check if company already exists in company schema
            query = """
                SELECT id FROM company.companies 
                WHERE domain = $1 AND org_id = $2
            """
            result = await self.execute_query(
                query, {"domain": domain, "org_id": org_id}
            )

            if result and result[0]:
                return str(result[0]["id"])

            # Create new company record in company schema
            company_uuid = str(uuid4())
            insert_query = """
                INSERT INTO company.companies (id, company_id, org_id, domain, source, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
            """
            await self.execute_query(
                insert_query,
                {
                    "company_uuid": company_uuid,
                    "company_id": domain,  # Use domain as company_id
                    "org_id": org_id,
                    "domain": domain,
                    "source": source,
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc),
                },
            )

            self.logger.info(
                f"Created new company record in company schema: {company_uuid}"
            )
            return company_uuid

        except Exception as e:
            self.logger.error(f"Error in get_or_create_company_by_domain: {e}")
            raise

    async def store_enrichment_source(
        self,
        company_uuid: str,
        source_name: str,
        raw_data: Dict[str, Any],
        extracted_fields: Dict[str, Any],
        s3_raw_data_url: Optional[str] = None,
    ) -> str:
        """
        Store enrichment source data in company.enrichment_sources.
        Uses upsert to handle duplicate records.

        Args:
            company_uuid: Company UUID
            source_name: Name of the enrichment source
            raw_data: Raw data from the source
            extracted_fields: Extracted/processed fields
            s3_raw_data_url: Optional S3 URL for raw data

        Returns:
            Source UUID
        """
        try:
            source_uuid = str(uuid4())
            upsert_query = """
                INSERT INTO company.enrichment_sources 
                (id, company_id, source_name, raw_data, extracted_fields, s3_raw_data_url, status, scraped_at, created_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                ON CONFLICT (company_id, source_name) 
                DO UPDATE SET
                    raw_data = EXCLUDED.raw_data,
                    extracted_fields = EXCLUDED.extracted_fields,
                    s3_raw_data_url = EXCLUDED.s3_raw_data_url,
                    status = EXCLUDED.status,
                    scraped_at = EXCLUDED.scraped_at,
                    updated_at = CURRENT_TIMESTAMP
                RETURNING id
            """

            result = await self.execute_query(
                upsert_query,
                {
                    "source_uuid": source_uuid,
                    "company_id": company_uuid,
                    "source_name": source_name,
                    "raw_data": json.dumps(raw_data, default=self._json_serializer),
                    "extracted_fields": json.dumps(
                        extracted_fields, default=self._json_serializer
                    ),
                    "s3_raw_data_url": s3_raw_data_url,
                    "status": "success",
                    "scraped_at": datetime.now(timezone.utc),
                    "created_at": datetime.now(timezone.utc),
                },
            )

            # Get the actual UUID from the result (might be existing or new)
            actual_uuid = result[0]["id"] if result else source_uuid

            self.logger.info(
                f"Stored/updated enrichment source {source_name} for company {company_uuid}"
            )
            return actual_uuid

        except Exception as e:
            self.logger.error(f"Error storing enrichment source: {e}")
            raise

    async def store_company_insight(
        self, company_uuid: str, insight: Dict[str, Any]
    ) -> str:
        """
        Store a single company insight in company.insights.
        Uses upsert to handle duplicate records.

        Args:
            company_uuid: Company UUID
            insight: Insight data with source_url, label, value, type, confidence, source

        Returns:
            Insight UUID
        """
        try:
            insight_uuid = str(uuid4())
            upsert_query = """
                INSERT INTO company.insights 
                (id, company_id, source_url, label, value, type, confidence, source, created_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                ON CONFLICT (company_id, label, type) 
                DO UPDATE SET
                    value = EXCLUDED.value,
                    source_url = EXCLUDED.source_url,
                    confidence = EXCLUDED.confidence,
                    source = EXCLUDED.source
                RETURNING id
            """

            result = await self.execute_query(
                upsert_query,
                {
                    "insight_uuid": insight_uuid,
                    "company_id": company_uuid,
                    "source_url": insight.get("source_url", ""),
                    "label": insight.get("label", ""),
                    "value": insight.get("value", ""),
                    "type": insight.get("type", "general"),
                    "confidence": insight.get("confidence", 0.5),
                    "source": insight.get("source", "unknown"),
                    "created_at": datetime.now(timezone.utc),
                },
            )

            # Get the actual UUID from the result (might be existing or new)
            actual_uuid = result[0]["id"] if result else insight_uuid

            self.logger.debug(
                f"Stored/updated insight {insight.get('label')} for company {company_uuid}"
            )
            return actual_uuid

        except Exception as e:
            self.logger.error(f"Error storing company insight: {e}")
            raise

    async def store_company_link(
        self,
        company_uuid: str,
        discovered_from: str,
        page_url: str,
        page_type: str = "general",
        title: Optional[str] = None,
        is_spa: bool = False,
    ) -> str:
        """
        Store a discovered company link in company.links.

        Args:
            company_uuid: Company UUID
            discovered_from: Source of discovery (e.g., sitemap, ai_extraction)
            page_url: URL of the page
            page_type: Type of page (e.g., about, blog, careers)
            title: Optional page title
            is_spa: Whether this is a Single Page Application

        Returns:
            Link UUID
        """
        try:
            link_uuid = str(uuid4())
            insert_query = """
                INSERT INTO company.links 
                (id, company_id, discovered_from, page_url, page_type, title, is_spa, created_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            """

            await self.execute_query(
                insert_query,
                {
                    "link_uuid": link_uuid,
                    "company_id": company_uuid,
                    "discovered_from": discovered_from,
                    "page_url": page_url,
                    "page_type": page_type,
                    "title": title,
                    "is_spa": is_spa,
                    "created_at": datetime.now(timezone.utc),
                },
            )

            self.logger.debug(f"Stored link {page_url} for company {company_uuid}")
            return link_uuid

        except Exception as e:
            self.logger.error(f"Error storing company link: {e}")
            raise

    async def store_company_departments(
        self,
        company_uuid: str,
        departments: List[Dict[str, Any]],
        source: str = "apollo_data",
    ) -> List[str]:
        """
        Store company departments in company.departments table.
        Uses upsert to handle duplicate records.

        Args:
            company_uuid: Company UUID
            departments: List of department data with department_name and head_count
            source: Data source (default: apollo_data)

        Returns:
            List of department UUIDs
        """
        try:
            department_uuids = []

            for dept in departments:
                dept_uuid = str(uuid4())
                upsert_query = """
                    INSERT INTO company.departments 
                    (id, company_id, department_name, head_count, source, confidence_score, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                    ON CONFLICT (company_id, department_name) 
                    DO UPDATE SET
                        head_count = EXCLUDED.head_count,
                        source = EXCLUDED.source,
                        confidence_score = EXCLUDED.confidence_score,
                        updated_at = CURRENT_TIMESTAMP
                    RETURNING id
                """

                result = await self.execute_query(
                    upsert_query,
                    {
                        "dept_uuid": dept_uuid,
                        "company_id": company_uuid,
                        "department_name": dept.get("department_name", ""),
                        "head_count": dept.get("head_count", 0),
                        "source": source,
                        "confidence_score": dept.get("confidence_score", 0.8),
                        "created_at": datetime.now(timezone.utc),
                    },
                )

                # Get the actual UUID from the result (might be existing or new)
                actual_uuid = result[0]["id"] if result else dept_uuid
                department_uuids.append(actual_uuid)

            self.logger.debug(
                f"Stored/updated {len(departments)} departments for company {company_uuid}"
            )
            return department_uuids

        except Exception as e:
            self.logger.error(f"Error storing company departments: {e}")
            raise

    async def update_company_record_company_schema(
        self, company_uuid: str, company_data: Dict[str, Any]
    ) -> bool:
        """
        Update company record in company.companies schema.

        Args:
            company_uuid: Company UUID
            company_data: Company data to update

        Returns:
            Success status
        """
        try:
            # Add updated timestamp
            company_data["updated_at"] = datetime.now(timezone.utc)

            # Prepare SQL with positional parameters
            set_clauses = []
            values = []
            for i, (column, value) in enumerate(company_data.items()):
                set_clauses.append(f"{column} = ${i + 1}")
                values.append(self._serialize_value(value))

            # Add company UUID parameter
            values.append(company_uuid)

            sql = f"""
                UPDATE company.companies
                SET {", ".join(set_clauses)}
                WHERE id = ${len(values)}
            """

            if not self.pool:
                raise ValueError("Database pool not initialized")

            async with self.pool.acquire() as conn:
                result = await conn.execute(sql, *values)

            # Check if any rows were updated
            rows_updated = int(result.split()[-1])
            success = rows_updated > 0

            if success:
                self.logger.debug(
                    f"Updated company record in company schema: {company_uuid}"
                )
            else:
                self.logger.warning(
                    f"No company record found to update in company schema: {company_uuid}"
                )

            return success

        except Exception as e:
            self.logger.error(
                f"Failed to update company record in company schema {company_uuid}: {e}"
            )
            return False

    async def _create_company_tables(self) -> None:
        """Create company enrichment tables for the pipeline."""

        # Company enrichment tables
        company_tables = {
            "companies": {
                "id": "UUID PRIMARY KEY DEFAULT gen_random_uuid()",
                "company_id": "VARCHAR(255) NOT NULL",
                "org_id": "VARCHAR(255) NOT NULL",
                "name": "VARCHAR(500)",
                "legal_entity_name": "VARCHAR(500)",
                "description": "TEXT",
                "industry": "VARCHAR(255)",
                "sub_industry": "VARCHAR(255)",
                "business_model": "VARCHAR(255)",
                "company_type": "VARCHAR(100)",
                "incorporation_country": "VARCHAR(100)",
                "hq_location": "VARCHAR(500)",
                "revenue": "DECIMAL(15,2)",
                "employee_count": "INTEGER",
                "employee_count_range": "VARCHAR(50)",
                "founded_year": "INTEGER",
                "traction_level": "VARCHAR(100)",
                "funding_status": "VARCHAR(100)",
                "funding_total": "DECIMAL(15,2)",
                "funding_rounds": "INTEGER",
                "last_funding_date": "DATE",
                "last_funding_amount": "DECIMAL(15,2)",
                "valuation": "DECIMAL(15,2)",
                "deal_type": "VARCHAR(100)",
                "round_size": "VARCHAR(100)",
                "stage": "VARCHAR(100)",
                "sector": "VARCHAR(255)",
                "geo_tags": "TEXT",
                "competitors": "JSONB",
                "website": "TEXT",
                "linkedin_url": "TEXT",
                "twitter_url": "TEXT",
                "facebook_url": "TEXT",
                "email": "VARCHAR(255)",
                "phone": "VARCHAR(50)",
                "funding_data": "JSONB",
                "cb_data": "JSONB",
                "pb_data": "JSONB",
                "apollo_data": "JSONB",
                "linkedin_data": "JSONB",
                "confidence_score": "FLOAT CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0)",
                "enrichment_date": "TIMESTAMPTZ",
                "s3_raw_data_key": "TEXT",
                "domain": "VARCHAR(255)",
                "source": "VARCHAR(100) NOT NULL DEFAULT 'comprehensive_enrichment'",
                "created_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
                "updated_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
            },
            "companies_enrichment": {
                "id": "UUID PRIMARY KEY DEFAULT gen_random_uuid()",
                "company_id": "VARCHAR(255) UNIQUE NOT NULL",
                "org_id": "VARCHAR(255) NOT NULL",
                "apollo_id": "VARCHAR(255)",
                "name": "VARCHAR(500)",
                "domain": "VARCHAR(255)",
                "website": "TEXT",
                "description": "TEXT",
                "industry": "VARCHAR(255)",
                "sub_industry": "VARCHAR(255)",
                "employee_count": "INTEGER",
                "employee_count_range": "VARCHAR(50)",
                "founded_year": "INTEGER",
                "headquarters": "VARCHAR(500)",
                "country": "VARCHAR(100)",
                "city": "VARCHAR(100)",
                "state": "VARCHAR(100)",
                "funding_total": "DECIMAL(15,2)",
                "funding_rounds": "INTEGER",
                "last_funding_date": "DATE",
                "last_funding_amount": "DECIMAL(15,2)",
                "valuation": "DECIMAL(15,2)",
                "revenue": "DECIMAL(15,2)",
                "linkedin_url": "TEXT",
                "twitter_url": "TEXT",
                "facebook_url": "TEXT",
                "email": "VARCHAR(255)",
                "phone": "VARCHAR(50)",
                "source": "VARCHAR(100) NOT NULL DEFAULT 'apollo'",
                "confidence_score": "FLOAT CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0)",
                "enrichment_date": "TIMESTAMPTZ",
                "s3_raw_data_key": "TEXT",
                "apollo_metadata": "JSONB",
                "created_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
                "updated_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
            },
            "company_enrichment_sources": {
                "id": "UUID PRIMARY KEY DEFAULT gen_random_uuid()",
                "company_id": "UUID NOT NULL",
                "source_name": "VARCHAR(100) NOT NULL",
                "raw_data": "JSONB",
                "extracted_fields": "JSONB",
                "s3_raw_data_url": "TEXT",
                "status": "VARCHAR(50) NOT NULL DEFAULT 'pending'",
                "scraped_at": "TIMESTAMPTZ",
                "created_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
                "updated_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
            },
            "company_insights": {
                "id": "UUID PRIMARY KEY DEFAULT gen_random_uuid()",
                "company_id": "UUID NOT NULL",
                "source_url": "TEXT",
                "label": "VARCHAR(255) NOT NULL",
                "value": "TEXT",
                "type": "VARCHAR(100) NOT NULL DEFAULT 'general'",
                "confidence": "FLOAT CHECK (confidence >= 0.0 AND confidence <= 1.0) DEFAULT 0.5",
                "source": "VARCHAR(100) NOT NULL DEFAULT 'unknown'",
                "created_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
                "updated_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
            },
            "company_links": {
                "id": "UUID PRIMARY KEY DEFAULT gen_random_uuid()",
                "company_id": "UUID NOT NULL",
                "discovered_from": "VARCHAR(100) NOT NULL",
                "page_url": "TEXT NOT NULL",
                "page_type": "VARCHAR(100) NOT NULL DEFAULT 'general'",
                "created_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
            },
            "company_keywords": {
                "id": "UUID PRIMARY KEY DEFAULT gen_random_uuid()",
                "company_id": "VARCHAR(255) NOT NULL",
                "keyword": "VARCHAR(255) NOT NULL",
            },
            "company_technologies": {
                "id": "UUID PRIMARY KEY DEFAULT gen_random_uuid()",
                "company_id": "VARCHAR(255) NOT NULL",
                "technology": "VARCHAR(255) NOT NULL",
                "category": "VARCHAR(100)",
            },
            "company_department_counts": {
                "id": "UUID PRIMARY KEY DEFAULT gen_random_uuid()",
                "company_id": "VARCHAR(255) NOT NULL",
                "department": "VARCHAR(255) NOT NULL",
                "head_count": "INTEGER NOT NULL CHECK (head_count > 0)",
            },
            "departments": {
                "id": "UUID PRIMARY KEY DEFAULT gen_random_uuid()",
                "company_id": "UUID NOT NULL",
                "department_name": "VARCHAR(255) NOT NULL",
                "head_count": "INTEGER NOT NULL CHECK (head_count > 0)",
                "source": "VARCHAR(100) NOT NULL DEFAULT 'apollo_data'",
                "confidence_score": "FLOAT CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0) DEFAULT 0.8",
                "created_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
                "updated_at": "TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP",
            },
        }

        # Create company tables
        for table_name, schema in company_tables.items():
            await self.create_table(table_name, schema)

        # Add unique constraints and foreign keys
        await self._add_company_constraints()

        # Create indexes for better performance
        await self._create_company_indexes()

    async def _add_company_constraints(self) -> None:
        """Add constraints to company tables."""
        constraints = [
            # Unique constraints to prevent duplicates
            "ALTER TABLE company_keywords ADD CONSTRAINT IF NOT EXISTS company_keywords_unique UNIQUE (company_id, keyword)",
            "ALTER TABLE company_technologies ADD CONSTRAINT IF NOT EXISTS company_technologies_unique UNIQUE (company_id, technology)",
            "ALTER TABLE company_department_counts ADD CONSTRAINT IF NOT EXISTS company_department_counts_unique UNIQUE (company_id, department)",
            "ALTER TABLE departments ADD CONSTRAINT IF NOT EXISTS departments_unique UNIQUE (company_id, department_name)",
            "ALTER TABLE company_insights ADD CONSTRAINT IF NOT EXISTS insights_company_label_type_unique UNIQUE (company_id, label, type)",
            # Foreign key constraints (referencing the enrichment table)
            "ALTER TABLE company_keywords ADD CONSTRAINT IF NOT EXISTS fk_company_keywords_company FOREIGN KEY (company_id) REFERENCES companies_enrichment(company_id) ON DELETE CASCADE",
            "ALTER TABLE company_technologies ADD CONSTRAINT IF NOT EXISTS fk_company_technologies_company FOREIGN KEY (company_id) REFERENCES companies_enrichment(company_id) ON DELETE CASCADE",
            "ALTER TABLE company_department_counts ADD CONSTRAINT IF NOT EXISTS fk_company_department_counts_company FOREIGN KEY (company_id) REFERENCES companies_enrichment(company_id) ON DELETE CASCADE",
            # Foreign key constraints for new comprehensive enrichment tables
            "ALTER TABLE company_enrichment_sources ADD CONSTRAINT IF NOT EXISTS fk_company_enrichment_sources_company FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE",
            "ALTER TABLE company_insights ADD CONSTRAINT IF NOT EXISTS fk_company_insights_company FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE",
            "ALTER TABLE company_links ADD CONSTRAINT IF NOT EXISTS fk_company_links_company FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE",
            "ALTER TABLE departments ADD CONSTRAINT IF NOT EXISTS fk_departments_company FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE",
        ]

        if not self.pool:
            raise ValueError("Database pool not initialized")

        async with self.pool.acquire() as conn:
            for constraint_sql in constraints:
                try:
                    await conn.execute(constraint_sql)
                    self.logger.debug(f"Added constraint: {constraint_sql}")
                except Exception as e:
                    self.logger.warning(
                        f"Failed to add constraint: {constraint_sql}, error: {e}"
                    )

    async def _create_company_indexes(self) -> None:
        """Create indexes for company tables for optimal query performance."""
        indexes = [
            # Companies table indexes (comprehensive enrichment)
            "CREATE INDEX IF NOT EXISTS idx_companies_company_id ON companies(company_id)",
            "CREATE INDEX IF NOT EXISTS idx_companies_org_id ON companies(org_id)",
            "CREATE INDEX IF NOT EXISTS idx_companies_org_company ON companies(org_id, company_id)",
            "CREATE INDEX IF NOT EXISTS idx_companies_industry ON companies(industry)",
            "CREATE INDEX IF NOT EXISTS idx_companies_employee_count ON companies(employee_count)",
            "CREATE INDEX IF NOT EXISTS idx_companies_founded_year ON companies(founded_year)",
            "CREATE INDEX IF NOT EXISTS idx_companies_source ON companies(source)",
            # Company enrichment sources indexes
            "CREATE INDEX IF NOT EXISTS idx_company_enrichment_sources_company_id ON company_enrichment_sources(company_id)",
            "CREATE INDEX IF NOT EXISTS idx_company_enrichment_sources_source_name ON company_enrichment_sources(source_name)",
            "CREATE INDEX IF NOT EXISTS idx_company_enrichment_sources_status ON company_enrichment_sources(status)",
            # Company insights indexes
            "CREATE INDEX IF NOT EXISTS idx_company_insights_company_id ON company_insights(company_id)",
            "CREATE INDEX IF NOT EXISTS idx_company_insights_type ON company_insights(type)",
            "CREATE INDEX IF NOT EXISTS idx_company_insights_source ON company_insights(source)",
            "CREATE INDEX IF NOT EXISTS idx_company_insights_confidence ON company_insights(confidence)",
            # Company links indexes
            "CREATE INDEX IF NOT EXISTS idx_company_links_company_id ON company_links(company_id)",
            "CREATE INDEX IF NOT EXISTS idx_company_links_discovered_from ON company_links(discovered_from)",
            "CREATE INDEX IF NOT EXISTS idx_company_links_page_type ON company_links(page_type)",
            # Companies enrichment table indexes
            "CREATE INDEX IF NOT EXISTS idx_companies_enrichment_company_id ON companies_enrichment(company_id)",
            "CREATE INDEX IF NOT EXISTS idx_companies_enrichment_org_id ON companies_enrichment(org_id)",
            "CREATE INDEX IF NOT EXISTS idx_companies_enrichment_org_company ON companies_enrichment(org_id, company_id)",
            "CREATE INDEX IF NOT EXISTS idx_companies_enrichment_domain ON companies_enrichment(domain)",
            "CREATE INDEX IF NOT EXISTS idx_companies_enrichment_industry ON companies_enrichment(industry)",
            "CREATE INDEX IF NOT EXISTS idx_companies_enrichment_employee_count ON companies_enrichment(employee_count)",
            "CREATE INDEX IF NOT EXISTS idx_companies_enrichment_founded_year ON companies_enrichment(founded_year)",
            "CREATE INDEX IF NOT EXISTS idx_companies_enrichment_source ON companies_enrichment(source)",
            # Company keywords indexes
            "CREATE INDEX IF NOT EXISTS idx_company_keywords_company_id ON company_keywords(company_id)",
            "CREATE INDEX IF NOT EXISTS idx_company_keywords_keyword ON company_keywords(keyword)",
            # Company technologies indexes
            "CREATE INDEX IF NOT EXISTS idx_company_technologies_company_id ON company_technologies(company_id)",
            "CREATE INDEX IF NOT EXISTS idx_company_technologies_technology ON company_technologies(technology)",
            "CREATE INDEX IF NOT EXISTS idx_company_technologies_category ON company_technologies(category)",
            # Company department counts indexes
            "CREATE INDEX IF NOT EXISTS idx_company_department_counts_company_id ON company_department_counts(company_id)",
            "CREATE INDEX IF NOT EXISTS idx_company_department_counts_department ON company_department_counts(department)",
            "CREATE INDEX IF NOT EXISTS idx_company_department_counts_head_count ON company_department_counts(head_count)",
            # Departments indexes (new table)
            "CREATE INDEX IF NOT EXISTS idx_departments_company_id ON departments(company_id)",
            "CREATE INDEX IF NOT EXISTS idx_departments_department_name ON departments(department_name)",
            "CREATE INDEX IF NOT EXISTS idx_departments_source ON departments(source)",
            "CREATE INDEX IF NOT EXISTS idx_departments_head_count ON departments(head_count)",
        ]

        if not self.pool:
            raise ValueError("Database pool not initialized")

        async with self.pool.acquire() as conn:
            for index_sql in indexes:
                try:
                    await conn.execute(index_sql)
                    self.logger.debug(f"Created index: {index_sql}")
                except Exception as e:
                    self.logger.warning(
                        f"Failed to create index: {index_sql}, error: {e}"
                    )
