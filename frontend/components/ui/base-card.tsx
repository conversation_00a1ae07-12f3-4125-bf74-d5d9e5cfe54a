import { cn } from "@/lib/utils"
import { <PERSON>, CardContent, CardHeader } from "./card"

interface BaseCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  className?: string
  contentClassName?: string
  noPadding?: boolean
  isInteractive?: boolean
}

export function BaseCard({
  children,
  className,
  contentClassName,
  noPadding = false,
  isInteractive = false,
  ...props
}: BaseCardProps) {
  return (
    <Card
      className={cn(
        "relative flex flex-col items-center justify-center",
        "min-h-[200px] md:min-h-[140px]",
        "border-0 shadow-lg transition-all duration-200",
        "rounded-2xl bg-white/95 backdrop-blur-sm",
        isInteractive && "hover:border hover:border-gray-100 hover:shadow-2xl",
        className
      )}
      {...props}
    >
      <CardContent
        className={cn(
          "flex size-full flex-col items-center justify-center",
          "p-0",
          contentClassName
        )}
      >
        {children}
      </CardContent>
    </Card>
  )
} 