import { createEnv } from "@t3-oss/env-nextjs"
import { z } from "zod"

export const env = createEnv({
  server: {
    // This is optional because it's only used in development.
    // See https://next-auth.js.org/deployment.
    NEXTAUTH_URL: z.string().url().optional(),
    NEXTAUTH_SECRET: z.string().min(1).optional().default("supersecretkey"),
    GITHUB_CLIENT_ID: z.string().optional().default("dummy"),
    GITHUB_CLIENT_SECRET: z.string().optional().default("dummy"),
    GITHUB_ACCESS_TOKEN: z.string().optional().default("dummy"),
    DATABASE_URL: z.string().optional().default("dummy"),
    SMTP_FROM: z.string().optional().default("<EMAIL>"),
    POSTMARK_API_TOKEN: z.string().optional().default("dummy"),
    POSTMARK_SIGN_IN_TEMPLATE: z.string().optional().default("dummy"),
    POSTMARK_ACTIVATION_TEMPLATE: z.string().optional().default("dummy"),
    STRIPE_API_KEY: z.string().optional().default("dummy"),
    STRIPE_WEBHOOK_SECRET: z.string().optional().default("dummy"),
    STRIPE_PRO_MONTHLY_PLAN_ID: z.string().optional().default("dummy"),
  },
  client: {
    NEXT_PUBLIC_APP_URL: z.string().optional().default("http://localhost:3000"),
    NEXT_PUBLIC_API_URL: z.string().optional().default("http://localhost:8000/api/v1"),
  },
  runtimeEnv: {
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    GITHUB_CLIENT_ID: process.env.GITHUB_CLIENT_ID,
    GITHUB_CLIENT_SECRET: process.env.GITHUB_CLIENT_SECRET,
    GITHUB_ACCESS_TOKEN: process.env.GITHUB_ACCESS_TOKEN,
    DATABASE_URL: process.env.DATABASE_URL,
    SMTP_FROM: process.env.SMTP_FROM,
    POSTMARK_API_TOKEN: process.env.POSTMARK_API_TOKEN,
    POSTMARK_SIGN_IN_TEMPLATE: process.env.POSTMARK_SIGN_IN_TEMPLATE,
    POSTMARK_ACTIVATION_TEMPLATE: process.env.POSTMARK_ACTIVATION_TEMPLATE,
    STRIPE_API_KEY: process.env.STRIPE_API_KEY,
    STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,
    STRIPE_PRO_MONTHLY_PLAN_ID: process.env.STRIPE_PRO_MONTHLY_PLAN_ID,
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
  },
})
