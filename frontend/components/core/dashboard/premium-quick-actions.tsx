"use client"

import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { 
  Plus, 
  Upload, 
  Search, 
  Settings, 
  Users, 
  BarChart3,
  ArrowRight,
  Zap
} from 'lucide-react';
import { useRouter } from 'next/navigation';

interface PremiumQuickActionsProps {
  className?: string;
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const actionVariants = {
  hidden: { 
    opacity: 0, 
    y: 10,
    scale: 0.98
  },
  visible: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.4,
      ease: [0.25, 0.46, 0.45, 0.94] as const
    }
  }
};

const actions = [
  {
    title: "Add Deal",
    description: "Create new deal entry",
    icon: Plus,
    color: "primary" as const,
    modal: "new-deal",
    // shortcut: "⌘N"
  },
  {
    title: "Upload Documents",
    description: "Batch import deal files",
    icon: Upload,
    color: "secondary" as const,
    modal: "excel-upload",
    // shortcut: "⌘U"
  },
  {
    title: "Team Management",
    description: "Manage team members",
    icon: Users,
    color: "secondary" as const,
    href: "/settings/members",
    // shortcut: "⌘T"
  },
];

export function PremiumQuickActions({
  className
}: PremiumQuickActionsProps) {
  const router = useRouter();

  const handleAction = (action: typeof actions[number]) => {
    if (action.modal === "new-deal") {
      router.push("/deals?modal=new-deal");
    } else if (action.modal === "excel-upload") {
      router.push("/deals?modal=excel-upload");
    } else if (action.href) {
      router.push(action.href);
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn(
        "grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3",
        className
      )}
    >
      {actions.map((action, index) => (
        <motion.div
          key={action.title}
          variants={actionVariants}
          className="group"
          onClick={() => handleAction(action)}
          tabIndex={0}
          role="button"
          onKeyDown={e => { if (e.key === 'Enter' || e.key === ' ') handleAction(action); }}
        >
          <Card className={cn(
            "border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-[#0B0F19] rounded-xl shadow-sm relative overflow-hidden group transition-all duration-300 hover:shadow-xl hover:scale-[1.01] cursor-pointer",
            className
          )}>
            {/* Accent gradient top bar */}
            <div className="h-[3px] w-full bg-gradient-to-r from-[#6366F1] via-[#4F46E5] to-[#06B6D4] transition-all duration-300 group-hover:from-[#4F46E5] group-hover:to-[#0EA5E9]" />
            <CardContent className="p-5">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-4 mb-4">
                    <div className={cn(
                      "p-4 rounded-xl bg-gradient-to-tr from-[#6366F1] via-[#4F46E5] to-[#06B6D4] text-white"
                    )}>
                      <action.icon className="w-7 h-7" />
                    </div>
                    <div>
                      <h3 className="text-base font-semibold text-black dark:text-white">
                        {action.title}
                      </h3>
                      <p className="text-sm text-muted-foreground mt-2">
                        {action.description}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-8 px-3 text-xs font-medium opacity-0 group-hover:opacity-100 transition-opacity text-[#6366F1] hover:bg-[#6366F1]/10"
                    >
                      Open
                      <ArrowRight className="w-3 h-3 ml-1" />
                    </Button>
                    <div className="flex items-center gap-2">
                      {/* <kbd className="px-2 py-1 text-xs font-mono rounded border border-[#6366F1]/20 text-[#6366F1] bg-[#6366F1]/5">
                        {action.shortcut}
                      </kbd> */}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}

      {/* AI Status Card */}
      {/* <motion.div
        variants={actionVariants}
        className="group md:col-span-2 lg:col-span-3"
      >
        <Card className={cn(
          "border-0 bg-gradient-to-r from-primary/5 to-secondary/5 shadow-institutional hover:shadow-institutional-lg transition-all duration-300",
          "rounded-xl overflow-hidden"
        )}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-lg bg-primary/10 text-primary">
                  <Zap className="w-6 h-6" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-text">
                    AI Processing Active
                  </h3>
                  <p className="text-sm text-muted mt-1">
                    Analyzing 3 deals in background
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-primary animate-live-pulse" />
                  <span className="text-xs text-muted">Live</span>
                </div>
                <Button 
                  variant="outline" 
                  size="sm"
                  className="border-primary/20 text-primary hover:bg-primary/10"
                >
                  View Queue
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div> */}
    </motion.div>
  );
} 