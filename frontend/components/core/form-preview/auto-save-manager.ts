/**
 * Auto-Save Manager
 * 
 * Handles automatic saving and restoration of form state to/from localStorage
 * Provides seamless state persistence across page refreshes
 */

import { FormAnswers, RepeatableAnswers } from './visibility-engine';

export interface FormState {
  answers: FormAnswers;
  repeatableAnswers: RepeatableAnswers;
  lastSaved: number;
  formId: string;
  version: number;
}

export class AutoSaveManager {
  private formId: string;
  private storageKey: string;
  private saveTimeout: NodeJS.Timeout | null = null;
  private readonly SAVE_DELAY = 500; // 500ms debounce
  private readonly VERSION = 1;

  constructor(formId: string) {
    this.formId = formId;
    this.storageKey = `form_preview_${formId}`;
  }

  /**
   * Save form state to localStorage with debouncing
   */
  saveState(answers: FormAnswers, repeatableAnswers: RepeatableAnswers): void {
    // Clear existing timeout
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }

    // Set new timeout for debounced save
    this.saveTimeout = setTimeout(() => {
      try {
        const state: FormState = {
          answers,
          repeatableAnswers,
          lastSaved: Date.now(),
          formId: this.formId,
          version: this.VERSION,
        };

        localStorage.setItem(this.storageKey, JSON.stringify(state));
        console.log('Form state auto-saved:', { 
          answersCount: Object.keys(answers).length,
          repeatableSections: Object.keys(repeatableAnswers).length,
          timestamp: new Date(state.lastSaved).toISOString()
        });
      } catch (error) {
        console.error('Failed to save form state:', error);
      }
    }, this.SAVE_DELAY);
  }

  /**
   * Load form state from localStorage
   */
  loadState(): { answers: FormAnswers; repeatableAnswers: RepeatableAnswers } | null {
    try {
      const savedData = localStorage.getItem(this.storageKey);
      if (!savedData) {
        return null;
      }

      const state: FormState = JSON.parse(savedData);

      // Validate the saved state
      if (
        state.formId !== this.formId ||
        state.version !== this.VERSION ||
        !state.answers ||
        !state.repeatableAnswers
      ) {
        console.warn('Invalid or outdated saved state, clearing...');
        this.clearState();
        return null;
      }

      console.log('Form state loaded:', {
        answersCount: Object.keys(state.answers).length,
        repeatableSections: Object.keys(state.repeatableAnswers).length,
        lastSaved: new Date(state.lastSaved).toISOString()
      });

      return {
        answers: state.answers,
        repeatableAnswers: state.repeatableAnswers,
      };
    } catch (error) {
      console.error('Failed to load form state:', error);
      this.clearState();
      return null;
    }
  }

  /**
   * Clear saved form state
   */
  clearState(): void {
    try {
      localStorage.removeItem(this.storageKey);
      console.log('Form state cleared');
    } catch (error) {
      console.error('Failed to clear form state:', error);
    }
  }

  /**
   * Get last saved timestamp
   */
  getLastSavedTime(): Date | null {
    try {
      const savedData = localStorage.getItem(this.storageKey);
      if (!savedData) {
        return null;
      }

      const state: FormState = JSON.parse(savedData);
      return new Date(state.lastSaved);
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if there's any saved state
   */
  hasSavedState(): boolean {
    try {
      const savedData = localStorage.getItem(this.storageKey);
      if (!savedData) {
        return false;
      }

      const state: FormState = JSON.parse(savedData);
      return (
        state.formId === this.formId &&
        state.version === this.VERSION &&
        (Object.keys(state.answers).length > 0 || Object.keys(state.repeatableAnswers).length > 0)
      );
    } catch (error) {
      return false;
    }
  }

  /**
   * Get storage usage information
   */
  getStorageInfo(): { size: number; sizeFormatted: string } {
    try {
      const savedData = localStorage.getItem(this.storageKey);
      if (!savedData) {
        return { size: 0, sizeFormatted: '0 B' };
      }

      const size = new Blob([savedData]).size;
      const sizeFormatted = this.formatBytes(size);

      return { size, sizeFormatted };
    } catch (error) {
      return { size: 0, sizeFormatted: '0 B' };
    }
  }

  /**
   * Format bytes to human readable string
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Cleanup method to call when component unmounts
   */
  cleanup(): void {
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
      this.saveTimeout = null;
    }
  }

  /**
   * Export form state as JSON for debugging or backup
   */
  exportState(): string | null {
    const state = this.loadState();
    if (!state) {
      return null;
    }

    return JSON.stringify({
      ...state,
      exportedAt: new Date().toISOString(),
      formId: this.formId,
    }, null, 2);
  }

  /**
   * Import form state from JSON string
   */
  importState(jsonString: string): boolean {
    try {
      const importedData = JSON.parse(jsonString);
      
      if (importedData.formId !== this.formId) {
        console.error('Form ID mismatch during import');
        return false;
      }

      if (!importedData.answers || !importedData.repeatableAnswers) {
        console.error('Invalid import data structure');
        return false;
      }

      this.saveState(importedData.answers, importedData.repeatableAnswers);
      return true;
    } catch (error) {
      console.error('Failed to import form state:', error);
      return false;
    }
  }
}
