"""Company service interfaces."""

from typing import Any, Dict, Optional, Protocol


class CompanyServiceInterface(Protocol):
    """Interface for company services."""

    async def initialize(self) -> None:
        """Initialize service dependencies."""
        ...

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        ...

    async def get_company_with_all_relations(self, domain: str) -> Optional[Dict[str, Any]]:
        """
        Get a company with all related data by domain.
        
        Args:
            domain: The company domain
            
        Returns:
            Dictionary with company data and all relations, or None if not found
        """
        ...
