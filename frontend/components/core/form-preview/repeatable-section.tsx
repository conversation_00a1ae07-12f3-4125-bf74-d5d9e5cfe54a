"use client"

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Trash2, GripVertical } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { Section, Question } from '@/lib/types/form';
import { QuestionRenderer } from './question-renderer';
import { ValidationResult } from './validation-engine';
import { VisibilityContext } from './visibility-engine';

interface RepeatableSectionProps {
  section: Section;
  instances: Array<{
    instanceIndex: number;
    answers: { [questionId: string]: any };
  }>;
  onAddInstance: () => void;
  onRemoveInstance: (instanceIndex: number) => void;
  onAnswerChange: (instanceIndex: number, questionId: string, value: any) => void;
  visibilityContext: VisibilityContext;
  validationResults: { [instanceIndex: number]: ValidationResult };
  maxInstances?: number;
  minInstances?: number;
  disabled?: boolean;
  className?: string;
}

export function RepeatableSection({
  section,
  instances,
  onAddInstance,
  onRemoveInstance,
  onAnswerChange,
  visibilityContext,
  validationResults,
  maxInstances = 10,
  minInstances = 1,
  disabled = false,
  className
}: RepeatableSectionProps) {
  const canAddMore = instances.length < maxInstances;
  const canRemove = instances.length > minInstances;

  const getFieldError = (instanceIndex: number, questionId: string): string | undefined => {
    const validation = validationResults[instanceIndex];
    if (!validation) return undefined;

    const error = validation.errors.find(err => err.field === questionId);
    return error?.message;
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Section Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-semibold text-slate-900">
              {section.title}
            </h3>
            <Badge variant="outline" className="text-xs">
              Repeatable
            </Badge>
          </div>
          {section.description && (
            <p className="text-sm text-slate-600">
              {section.description}
            </p>
          )}
        </div>

        {/* Add Instance Button */}
        {canAddMore && !disabled && (
          <Button
            onClick={onAddInstance}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="size-4" />
            <span>Add Another</span>
          </Button>
        )}
      </div>

      {/* Instance List */}
      <div className="space-y-4">
        <AnimatePresence mode="popLayout">
          {instances.map((instance, index) => (
            <motion.div
              key={instance.instanceIndex}
              layout
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{
                duration: 0.3,
                ease: "easeOut"
              }}
            >
              <RepeatableInstance
                section={section}
                instance={instance}
                instanceNumber={index + 1}
                onAnswerChange={(questionId, value) =>
                  onAnswerChange(instance.instanceIndex, questionId, value)
                }
                onRemove={canRemove && !disabled ? () => onRemoveInstance(instance.instanceIndex) : undefined}
                visibilityContext={{
                  ...visibilityContext,
                  currentSectionInstance: {
                    sectionId: section._id || section.id!,
                    instanceIndex: instance.instanceIndex
                  }
                }}
                validationResult={validationResults[instance.instanceIndex]}
                disabled={disabled}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Empty State */}
      {instances.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="py-8 text-center text-slate-500"
        >
          <p className="text-sm">No instances yet.</p>
          {canAddMore && !disabled && (
            <Button
              onClick={onAddInstance}
              variant="outline"
              size="sm"
              className="mt-2 flex items-center gap-2"
            >
              <Plus className="size-4" />
              <span>Add First Instance</span>
            </Button>
          )}
        </motion.div>
      )}

      {/* Instance Limits Info */}
      {(instances.length >= maxInstances || instances.length <= minInstances) && (
        <div className="text-center text-xs text-slate-500">
          {instances.length >= maxInstances && (
            <p>Maximum of {maxInstances} instances allowed</p>
          )}
          {instances.length <= minInstances && minInstances > 1 && (
            <p>Minimum of {minInstances} instances required</p>
          )}
        </div>
      )}
    </div>
  );
}

interface RepeatableInstanceProps {
  section: Section;
  instance: {
    instanceIndex: number;
    answers: { [questionId: string]: any };
  };
  instanceNumber: number;
  onAnswerChange: (questionId: string, value: any) => void;
  onRemove?: () => void;
  visibilityContext: VisibilityContext;
  validationResult?: ValidationResult;
  disabled?: boolean;
}

function RepeatableInstance({
  section,
  instance,
  instanceNumber,
  onAnswerChange,
  onRemove,
  visibilityContext,
  validationResult,
  disabled = false
}: RepeatableInstanceProps) {
  const getFieldError = (questionId: string): string | undefined => {
    if (!validationResult) return undefined;

    const error = validationResult.errors.find(err => err.field === questionId);
    return error?.message;
  };

  return (
    <Card className="relative">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base font-medium text-slate-900">
            {section.title} #{instanceNumber}
          </CardTitle>

          <div className="flex items-center gap-2">
            {/* Drag Handle (for future drag-and-drop) */}
            <div className="cursor-grab text-slate-400 hover:text-slate-600">
              <GripVertical className="size-4" />
            </div>

            {/* Remove Button */}
            {onRemove && (
              <Button
                onClick={onRemove}
                variant="ghost"
                size="sm"
                className="flex items-center justify-center text-red-600 hover:bg-red-50 hover:text-red-700"
              >
                <Trash2 className="size-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {section.questions
          .sort((a, b) => a.order - b.order)
          .map((question, questionIndex) => (
            <div key={question._id || question.id}>
              <QuestionRenderer
                question={question}
                value={instance.answers[question._id || question.id!]}
                onChange={(value) => onAnswerChange(question._id || question.id!, value)}
                error={getFieldError(question._id || question.id!)}
                disabled={disabled}
              />

              {questionIndex < section.questions.length - 1 && (
                <Separator className="mt-6" />
              )}
            </div>
          ))}
      </CardContent>
    </Card>
  );
}

// Utility component for managing repeatable section state
export function useRepeatableSection(sectionId: string, initialInstances: number = 1) {
  const [instances, setInstances] = useState<Array<{
    instanceIndex: number;
    answers: { [questionId: string]: any };
  }>>(() => {
    // Initialize with the specified number of instances
    return Array.from({ length: initialInstances }, (_, index) => ({
      instanceIndex: index,
      answers: {}
    }));
  });

  const addInstance = () => {
    const newInstanceIndex = Math.max(...instances.map(i => i.instanceIndex), -1) + 1;
    setInstances(prev => [...prev, {
      instanceIndex: newInstanceIndex,
      answers: {}
    }]);
  };

  const removeInstance = (instanceIndex: number) => {
    setInstances(prev => prev.filter(i => i.instanceIndex !== instanceIndex));
  };

  const updateAnswer = (instanceIndex: number, questionId: string, value: any) => {
    setInstances(prev => prev.map(instance =>
      instance.instanceIndex === instanceIndex
        ? {
            ...instance,
            answers: {
              ...instance.answers,
              [questionId]: value
            }
          }
        : instance
    ));
  };

  const getInstanceAnswers = (instanceIndex: number) => {
    const instance = instances.find(i => i.instanceIndex === instanceIndex);
    return instance?.answers || {};
  };

  const getAllAnswers = () => {
    const result: { [instanceIndex: number]: { [questionId: string]: any } } = {};
    instances.forEach(instance => {
      result[instance.instanceIndex] = instance.answers;
    });
    return result;
  };

  return {
    instances,
    addInstance,
    removeInstance,
    updateAnswer,
    getInstanceAnswers,
    getAllAnswers
  };
}
