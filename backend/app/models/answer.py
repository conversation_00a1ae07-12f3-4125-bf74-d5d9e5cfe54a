"""
Answer schema models for form submissions.

This module defines the data structures for form answers, including support for:
- Regular question answers
- Repeatable section answers
- Complex nested structures
- Answer validation
"""
from typing import Dict, Any, Optional
from pydantic import Field, model_validator
from datetime import datetime, timezone
from app.models.base import TractionXModel


class AnswerSchema(TractionXModel):
    """
    Schema for form answers, supporting both regular questions and repeatable sections.

    This schema uses a clean, intuitive structure:
    1. answers: Dict mapping question_id to answer value or dict
    2. repeatable_answers: Dict with the following structure:
       {
           section_id: {
               instance_id: {
                   question_id: answer_value,
                   ...
               },
               ...
           },
           ...
       }

    Where:
    - section_id: Identifier for the repeatable section
    - instance_id: String identifier for the instance (typically "0", "1", "2", etc.)
    - question_id: Identifier for the question within the instance
    - answer_value: The answer value for that question in that instance
    """

    class Config:
        """Configuration for the AnswerSchema model."""
        json_encoders = {
            # Add custom encoders for any types that need special handling
        }

    def model_dump(self, **kwargs):
        """Override model_dump to ensure JSON serialization works properly."""
        # Get the default model dump
        data = super().model_dump(**kwargs)
        return data
    # Regular answers (question_id -> answer)
    answers: Dict[str, Any] = Field(
        default_factory=dict,
        description="Answers to regular questions (question_id -> answer value or dict)"
    )

    # Repeatable section answers
    repeatable_answers: Dict[str, Dict[str, Dict[str, Any]]] = Field(
        default_factory=dict,
        description="Answers to repeatable sections: {section_id: {instance_id: {question_id2: answer_value, question_id2: answer_value}}}"
    )

    # Optional metadata
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=lambda: {
            "submitted_at": int(datetime.now(timezone.utc).timestamp())
        },
        description="Optional metadata for the submission"
    )

    @model_validator(mode='after')
    def validate_structure(self) -> 'AnswerSchema':
        """
        Validate the answer structure and ensure it's consistent.

        This validator checks that:
        1. Question IDs are not duplicated between regular answers and repeatable sections
        2. All required fields are present
        3. Instance IDs are valid strings
        """
        # If no repeatable answers, nothing to validate
        if not self.repeatable_answers:
            return self

        # Check for duplicates between regular answers and repeatable sections
        repeatable_question_ids = set()
        for section_id, instances in self.repeatable_answers.items():
            for instance_id, instance_answers in instances.items():
                # Validate instance_id is a string
                if not isinstance(instance_id, str):
                    raise ValueError(f"Instance ID must be a string, got {type(instance_id)} for {instance_id}")

                # Collect question IDs from this instance
                repeatable_question_ids.update(instance_answers.keys())

        # Find duplicates
        duplicates = repeatable_question_ids.intersection(self.answers.keys())
        if duplicates:
            raise ValueError(
                f"Question IDs cannot appear in both regular answers and repeatable sections: {duplicates}"
            )

        return self

    def to_flat_dict(self) -> Dict[str, Any]:
        """
        Convert the structured answer format to a flat dictionary.

        This is useful for backward compatibility with existing code that expects
        a flat structure of question_id -> answer.

        For repeatable sections, the keys will be in the format:
        {section_id}_{instance_id}_{question_id} and also
        {question_id}__{instance_index} for compatibility with validation
        """
        result = self.answers.copy()

        # Add repeatable section answers
        for section_id, instances in self.repeatable_answers.items():
            # Add each instance's answers
            for instance_id, instance_answers in instances.items():
                # Convert instance_id to int for indexing
                try:
                    instance_idx = int(instance_id)
                except ValueError:
                    instance_idx = int(instance_id.split('_')[-1]) if '_' in instance_id else 0

                for question_id, answer in instance_answers.items():
                    # Format 1: section_id_instance_id_question_id (for form engine)
                    key1 = f"{section_id}_{instance_id}_{question_id}"
                    result[key1] = answer

                    # Format 2: question_id__instance_idx (for validation)
                    key2 = f"{question_id}__{instance_idx}"
                    result[key2] = answer

        return result

    @classmethod
    def from_flat_dict(
        cls,
        answers: Dict[str, Any],
        repeatable_section_map: Dict[str, Dict[str, Any]]
    ) -> 'AnswerSchema':
        """
        Convert a flat dictionary of answers to a structured AnswerSchema.

        Args:
            answers: Flat dictionary of question_id -> answer
            repeatable_section_map: Map of section_id -> section info
                Each section info should have:
                - section_id: ID of the repeatable section
                - max_repeats: Maximum number of repeats allowed

        Returns:
            AnswerSchema with structured repeatable sections
        """
        # Start with regular answers
        regular_answers = {}
        repeatable_answers = {}

        # Create a set of section_ids for quick lookup
        section_ids = set(repeatable_section_map.keys())

        # Track processed keys to avoid duplicates
        processed_keys = set()

        # First pass: identify repeatable section answers in format section_id_instance_id_question_id
        for key, value in answers.items():
            # Skip already processed keys
            if key in processed_keys:
                continue

            # Check if this is a repeatable section answer in format section_id_instance_id_question_id
            if "_" in key:
                parts = key.split("_")
                if len(parts) >= 3:
                    section_id = parts[0]
                    instance_id = parts[1]  # Keep as string
                    question_id = "_".join(parts[2:])  # Handle question IDs that contain underscores

                    # Check if this section is repeatable
                    if section_id in section_ids:
                        # Initialize section instances if needed
                        if section_id not in repeatable_answers:
                            repeatable_answers[section_id] = {}

                        # Initialize instance if needed
                        if instance_id not in repeatable_answers[section_id]:
                            repeatable_answers[section_id][instance_id] = {}

                        # Add answer to instance
                        repeatable_answers[section_id][instance_id][question_id] = value
                        processed_keys.add(key)
                    else:
                        # Not a repeatable section, treat as regular answer
                        regular_answers[key] = value
                        processed_keys.add(key)
                else:
                    # Regular answer
                    regular_answers[key] = value
                    processed_keys.add(key)
            else:
                # Regular answer
                regular_answers[key] = value
                processed_keys.add(key)

        # Second pass: identify repeatable section answers in format question_id__instance_idx
        for key, value in answers.items():
            # Skip already processed keys
            if key in processed_keys:
                continue

            # Check if this is a repeatable section answer in format question_id__instance_idx
            if "__" in key:
                parts = key.split("__")
                if len(parts) == 2:
                    question_id = parts[0]
                    try:
                        instance_idx = int(parts[1])
                        instance_id = str(instance_idx)
                    except ValueError:
                        # Skip invalid instance indices
                        continue

                    # Find the section this question belongs to
                    section_id = None
                    # We would need additional information to map question_id to section_id
                    # For now, we'll skip this format and rely on the section_id_instance_id_question_id format

                    if section_id:
                        # Initialize section instances if needed
                        if section_id not in repeatable_answers:
                            repeatable_answers[section_id] = {}

                        # Initialize instance if needed
                        if instance_id not in repeatable_answers[section_id]:
                            repeatable_answers[section_id][instance_id] = {}

                        # Add answer to instance
                        repeatable_answers[section_id][instance_id][question_id] = value
                        processed_keys.add(key)

        # Create and return the schema
        return cls(
            answers=regular_answers,
            repeatable_answers=repeatable_answers
        )
