"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { Key, ArrowRight } from "lucide-react"
import { toast } from "sonner"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AuthCard } from "@/components/auth/auth-card"

interface InviteCodeEntryProps {
  onCodeSubmit?: (code: string) => void
}

export function InviteCodeEntry({ onCodeSubmit }: InviteCodeEntryProps) {
  const [code, setCode] = useState("")
  const [isValidating, setIsValidating] = useState(false)
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!code.trim()) {
      toast.error("Please enter an invite code")
      return
    }

    if (code.length < 6) {
      toast.error("Invite code must be at least 6 characters")
      return
    }

    setIsValidating(true)

    try {
      // If callback provided, use it; otherwise navigate directly
      if (onCodeSubmit) {
        onCodeSubmit(code.toUpperCase())
      } else {
        router.push(`/onboard/${code.toUpperCase()}`)
      }
    } catch (error) {
      toast.error("Invalid invite code")
    } finally {
      setIsValidating(false)
    }
  }

  const formatCode = (value: string) => {
    // Remove non-alphanumeric characters and convert to uppercase
    const cleaned = value.replace(/[^A-Za-z0-9]/g, '').toUpperCase()
    return cleaned.substring(0, 10) // Limit to 10 characters
  }

  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatCode(e.target.value)
    setCode(formatted)
  }

  return (
    <AuthCard
      title="Enter Invite Code"
      description="Enter your unique invite code to get started"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="invite-code" className="flex items-center gap-2">
            <Key className="size-4" />
            Invite Code
          </Label>
          <Input
            id="invite-code"
            type="text"
            placeholder="ABC123XY"
            value={code}
            onChange={handleCodeChange}
            className="h-12 rounded-xl border-2 text-center font-mono text-lg tracking-wider transition-colors focus:border-primary"
            autoComplete="off"
            autoFocus
          />
          <p className="text-center text-xs text-muted-foreground">
            Enter the 8-character code from your invitation email
          </p>
        </div>

        <Button
          type="submit"
          disabled={isValidating || code.length < 6}
          className="h-12 w-full rounded-xl font-semibold"
        >
          {isValidating ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              className="mr-2 size-4 rounded-full border-2 border-white border-t-transparent"
            />
          ) : (
            <ArrowRight className="mr-2 size-4" />
          )}
          {isValidating ? "Validating..." : "Continue"}
        </Button>

        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            Don't have an invite code?{" "}
            <button
              type="button"
              onClick={() => router.push("/login")}
              className="font-medium text-primary hover:underline"
            >
              Sign in instead
            </button>
          </p>
        </div>
      </form>
    </AuthCard>
  )
}
