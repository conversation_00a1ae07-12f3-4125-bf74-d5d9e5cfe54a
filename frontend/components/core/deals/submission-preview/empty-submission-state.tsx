"use client"

import { motion } from "framer-motion"
import { FileText } from "lucide-react"

export function EmptySubmissionState() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="mx-auto max-w-md text-center"
    >
      <div className="mx-auto max-w-lg rounded-3xl border border-gray-200/40 bg-white/95 p-12 shadow-xl backdrop-blur-sm">
        {/* Icon */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.4 }}
          className="mx-auto mb-8 flex size-20 items-center justify-center rounded-3xl bg-gradient-to-br from-gray-100 to-gray-200"
        >
          <FileText className="size-10 text-gray-500" />
        </motion.div>

        {/* Content */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.4 }}
          className="text-center"
        >
          <h3 className="mb-4 text-2xl font-bold text-gray-900">
            No submissions received yet
          </h3>
          <p className="mb-6 text-base leading-relaxed text-gray-600">
            No submissions have been received for this deal yet.
            Submissions will appear here once they are submitted through the deal form.
          </p>
          <div className="text-sm text-gray-500">
            Check back later or share the deal link to receive submissions.
          </div>
        </motion.div>

        {/* Decorative elements */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.6 }}
          className="mt-8 flex justify-center space-x-3"
        >
          <div className="size-3 rounded-full bg-gradient-to-r from-blue-400 to-blue-500 opacity-60" />
          <div className="size-3 rounded-full bg-gradient-to-r from-blue-500 to-blue-600" />
          <div className="size-3 rounded-full bg-gradient-to-r from-blue-400 to-blue-500 opacity-60" />
        </motion.div>
      </div>
    </motion.div>
  )
}
