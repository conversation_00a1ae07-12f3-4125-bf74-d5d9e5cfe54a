# Form and Thesis Technical Implementation Guide

This technical guide provides detailed information about the Form and Thesis systems' architecture, data models, and implementation details for developers integrating with the frontend.

## System Architecture

### Form System

The Form system consists of several key components:

1. **Base Models**:
   - `Form`: The top-level container for a form
   - `Section`: A grouping of related questions
   - `Question`: Individual form fields with various types and properties
   - `Submission`: Recorded answers to a form

2. **Form Engine**:
   - Handles conditional visibility logic
   - Manages repeatable sections
   - Provides different rendering modes (sequential, full, next)
   - Validates submissions against rules

3. **Form Service**:
   - Creates and manages forms, sections, and questions
   - Processes form submissions
   - Implements the form engine logic

### Thesis System

The Thesis system builds on top of the Form system:

1. **Base Models**:
   - `InvestmentThesis`: Defines investment criteria and preferences
   - `ScoringRule`: Rules for scoring form responses
   - `MatchRule`: Rules for filtering startups

2. **Scoring Engine**:
   - Calculates scores based on form responses
   - Applies different scoring strategies based on question types
   - Normalizes scores to a 0-1 range

3. **Thesis Service**:
   - Creates and manages theses
   - Processes scoring rules and match rules
   - Finds matching theses for form responses

## Data Models

### Form Models

```typescript
// Form
interface Form {
  _id: string;
  org_id: string;
  name: string;
  description: string;
  version: number;
  is_active: boolean;
  sections: string[];  // Section IDs
  default_section_ids: string[];  // Default sections to show
  created_at: number;  // Unix timestamp
  updated_at: number;  // Unix timestamp
}

// Section
interface Section {
  _id: string;
  form_id: string;
  title: string;
  description: string;
  order: number;
  questions: string[];  // Question IDs
  repeatable: boolean;
  created_at: number;
  updated_at: number;
}

// Question
interface Question {
  _id: string;
  section_id: string;
  type: QuestionType;  // Enum of question types
  label: string;
  help_text?: string;
  required: boolean;
  options?: Array<{value: string, label: string}>;  // For select questions
  validation?: ValidationRule;
  visibility_condition?: VisibilityCondition;
  repeat_section_id?: string;  // For repeatable section control
  max_repeats?: number;  // Maximum number of repeats
  order: number;
  created_at: number;
  updated_at: number;
}

// Question Types
enum QuestionType {
  SHORT_TEXT = "short_text",
  LONG_TEXT = "long_text",
  NUMBER = "number",
  DATE = "date",
  SINGLE_SELECT = "single_select",
  MULTI_SELECT = "multi_select",
  BOOLEAN = "boolean",
  EMAIL = "email",
  PHONE = "phone",
  URL = "url",
  FILE = "file",
  RANGE = "range"
}

// Visibility Condition
interface VisibilityCondition {
  operator: "==" | "!=" | ">" | ">=" | "<" | "<=" | "contains" | "not_contains";
  conditions: Array<{
    question_id: string;
    value: any;
  }>;
  logic?: "and" | "or";  // Default is "and"
}
```

### Thesis Models

```typescript
// Investment Thesis
interface InvestmentThesis {
  _id: string;
  org_id: string;
  name: string;
  description: string;
  form_id: string;
  status: ThesisStatus;  // Enum: DRAFT, ACTIVE, ARCHIVED
  is_active: boolean;
  is_deleted: boolean;
  scoring_rules: string[];  // ScoringRule IDs
  match_rules: string[];  // MatchRule IDs
  created_by: string;  // User ID
  created_at: number;
  updated_at: number;
  submitted_at?: number;
  reviewed_at?: number;
  reviewed_by?: string;  // User ID
}

// Scoring Rule
interface ScoringRule {
  _id: string;
  thesis_id: string;
  question_id: string;
  weight: number;  // Importance weight (1-10)
  config: {
    target_value?: any;  // Target value to compare against
    min_score?: number;  // Minimum score (0-1)
    strategy?: string;  // Scoring strategy
    [key: string]: any;  // Additional configuration
  };
  created_at: number;
  updated_at: number;
}

// Match Rule
interface MatchRule {
  _id: string;
  thesis_id: string;
  question_id: string;
  operator: "==" | "!=" | ">" | ">=" | "<" | "<=" | "contains" | "not_contains";
  value: any;
  required: boolean;  // If true, this rule must match
  created_at: number;
  updated_at: number;
}
```

## API Response Structures

### Form Engine Response

The Form Engine API returns different structures based on the requested mode:

#### Sequential Mode

```json
{
  "form": {
    "_id": "6123456789abcdef01234567",
    "name": "Startup Qualification Form",
    "description": "Form to collect startup information and qualify for investment"
  },
  "current_section": {
    "_id": "6223456789abcdef01234567",
    "title": "Company Information",
    "description": "Basic information about your company"
  },
  "visible_questions": [
    {
      "_id": "6323456789abcdef01234567",
      "type": "short_text",
      "label": "Company Name",
      "required": true,
      "order": 0
    },
    {
      "_id": "6323456789abcdef01234568",
      "type": "single_select",
      "label": "Industry",
      "required": true,
      "options": [
        {"value": "technology", "label": "Technology"},
        {"value": "healthcare", "label": "Healthcare"},
        {"value": "finance", "label": "Finance"}
      ],
      "order": 1
    }
  ],
  "progress": {
    "current_section_index": 0,
    "total_sections": 3,
    "answered_questions": 0,
    "total_questions": 15
  },
  "navigation": {
    "next_section_id": "6223456789abcdef01234568",
    "prev_section_id": null
  }
}
```

#### Full Mode

```json
{
  "form": {
    "_id": "6123456789abcdef01234567",
    "name": "Startup Qualification Form",
    "description": "Form to collect startup information and qualify for investment"
  },
  "sections": [
    {
      "_id": "6223456789abcdef01234567",
      "title": "Company Information",
      "description": "Basic information about your company",
      "order": 0,
      "questions": [
        {
          "_id": "6323456789abcdef01234567",
          "type": "short_text",
          "label": "Company Name",
          "required": true,
          "order": 0,
          "visible": true
        },
        {
          "_id": "6323456789abcdef01234568",
          "type": "single_select",
          "label": "Industry",
          "required": true,
          "options": [
            {"value": "technology", "label": "Technology"},
            {"value": "healthcare", "label": "Healthcare"},
            {"value": "finance", "label": "Finance"}
          ],
          "order": 1,
          "visible": true
        }
      ],
      "visible": true
    },
    {
      "_id": "6223456789abcdef01234568",
      "title": "Founder Information",
      "description": "Details about each founder",
      "order": 1,
      "repeatable": true,
      "repeat_count": 3,  // Based on the answer to the control question
      "questions": [
        {
          "_id": "6323456789abcdef01234570",
          "type": "short_text",
          "label": "Founder Name",
          "required": true,
          "order": 0,
          "visible": true,
          "repeat_indices": [0, 1, 2]  // This question appears 3 times
        },
        {
          "_id": "6323456789abcdef01234571",
          "type": "short_text",
          "label": "Founder Role",
          "required": true,
          "order": 1,
          "visible": true,
          "repeat_indices": [0, 1, 2]  // This question appears 3 times
        }
      ],
      "visible": true
    }
  ],
  "dependencies": {
    "6323456789abcdef01234569": {  // Control question ID
      "controls_visibility": ["6223456789abcdef01234568"],  // Section ID
      "type": "repeat_section"
    }
  }
}
```

### Thesis Preview Response

```json
{
  "form": {
    "_id": "6123456789abcdef01234567",
    "name": "Startup Qualification Form"
  },
  "sections": [
    {
      "_id": "6223456789abcdef01234567",
      "title": "Company Information",
      "questions": [
        {
          "_id": "6323456789abcdef01234567",
          "type": "short_text",
          "label": "Company Name",
          "scoring_options": {
            "strategies": ["exact_match", "fuzzy_match", "contains"],
            "suggested_config": {
              "strategy": "fuzzy_match",
              "min_score": 0.5
            }
          },
          "match_options": {
            "operators": ["==", "!=", "contains", "not_contains"],
            "suggested_config": {
              "operator": "contains",
              "required": false
            }
          },
          "existing_scoring_rule": null,
          "existing_match_rule": null
        },
        {
          "_id": "6323456789abcdef01234568",
          "type": "single_select",
          "label": "Industry",
          "options": [
            {"value": "technology", "label": "Technology"},
            {"value": "healthcare", "label": "Healthcare"},
            {"value": "finance", "label": "Finance"}
          ],
          "scoring_options": {
            "strategies": ["exact_match", "category_match"],
            "suggested_config": {
              "strategy": "exact_match",
              "target_value": "technology",
              "min_score": 0.2
            }
          },
          "match_options": {
            "operators": ["==", "!="],
            "suggested_config": {
              "operator": "==",
              "value": "technology",
              "required": true
            }
          },
          "existing_scoring_rule": {
            "_id": "6623456789abcdef01234567",
            "weight": 5,
            "config": {
              "target_value": "technology",
              "min_score": 0.2,
              "strategy": "exact_match"
            }
          },
          "existing_match_rule": null
        }
      ]
    }
  ],
  "templates": {
    "scoring_templates": [
      {
        "name": "Industry Focus",
        "description": "Score based on specific industry preferences",
        "rules": [
          {
            "question_id": "6323456789abcdef01234568",
            "weight": 8,
            "config": {
              "target_value": "technology",
              "min_score": 0.2,
              "strategy": "exact_match"
            }
          }
        ]
      }
    ],
    "match_templates": [
      {
        "name": "Technology Startups Only",
        "description": "Only match technology startups",
        "rules": [
          {
            "question_id": "6323456789abcdef01234568",
            "operator": "==",
            "value": "technology",
            "required": true
          }
        ]
      }
    ]
  }
}
```

## Implementation Considerations

### Handling Repeatable Sections

Repeatable sections require special handling in both the backend and frontend:

1. **Backend**:
   - Questions in repeatable sections are identified by their original ID plus an index suffix
   - Example: `question_id_0`, `question_id_1`, etc.
   - The number of repeats is determined by the control question's answer

2. **Frontend**:
   - Dynamically create UI components based on the control question's answer
   - Use consistent naming convention for form field names
   - Update the UI when the control question's answer changes

### Scoring Logic

The scoring system uses different strategies based on question types:

1. **Text Questions**:
   - Exact match: 1.0 if exact, 0.0 otherwise
   - Fuzzy match: Similarity score based on text comparison
   - Contains: 1.0 if target is contained, 0.0 otherwise

2. **Numeric Questions**:
   - Exact match: 1.0 if exact, 0.0 otherwise
   - Range match: Score based on proximity to target range
   - Threshold match: 1.0 if above/below threshold, 0.0 otherwise

3. **Select Questions**:
   - Exact match: 1.0 if exact, 0.0 otherwise
   - Partial match: Proportion of matching options
   - Weighted match: Weighted score based on option importance

### Match Rule Evaluation

Match rules are evaluated as follows:

1. Required rules must all pass for a thesis to match
2. Non-required rules contribute to the overall score but don't disqualify
3. Different operators (==, !=, >, etc.) are applied based on question type
4. For repeatable sections, at least one repeat must match the rule

## Frontend Integration Patterns

### Form Rendering

1. **Progressive Form**:
   - Use the sequential mode API
   - Show one section at a time
   - Validate each section before proceeding
   - Handle repeatable sections dynamically

2. **Full Form**:
   - Use the full mode API
   - Show/hide questions based on visibility rules
   - Update visibility when answers change
   - Group repeatable sections visually

### Thesis Configuration

1. **Rule Builder**:
   - Use the preview API to get configuration options
   - Implement a drag-and-drop interface for rule creation
   - Provide templates for common scenarios
   - Show real-time score preview

2. **Scoring Visualization**:
   - Use charts to show score distribution
   - Highlight strong and weak areas
   - Show which rules passed or failed
   - Provide recommendations for improvement
