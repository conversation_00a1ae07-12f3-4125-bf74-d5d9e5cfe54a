"use client"

import { useState, useEffect } from "react"
import { DashboardHeader } from "@/components/header"
import { DashboardShell } from "@/components/shell"
import {
  PremiumSummaryTiles,
  PremiumInsightsCharts,
  PremiumQuickActions,
  OnboardingFlow
} from "@/components/core/dashboard"
import { useDashboard } from "@/lib/hooks/use-dashboard"
import { useDashboardInsights } from "@/lib/hooks/use-dashboard-insights"
import { useAuth } from "@/lib/auth-context"
import { useAutoTour } from "@/components/core/tour/tour-provider"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Zap, TrendingUp } from "lucide-react"
import { Badge } from "@/components/ui/badge"

export default function PremiumDashboardPage() {
  const { user } = useAuth()
  const { data: dashboardData, loading: summaryLoading, error: summaryError, refetch: refetchSummary } = useDashboard()
  const { data: insightsData, loading: insightsLoading, error: insightsError, refetch: refetchInsights } = useDashboardInsights()
  const { triggerDashboardTour, shouldShowFirstTimeTour } = useAutoTour()
  const [showOnboarding, setShowOnboarding] = useState(false)

  // Determine if we should show onboarding
  const shouldShowOnboarding = dashboardData &&
    (!dashboardData.onboarding.has_form || !dashboardData.onboarding.has_thesis)

  // Auto-trigger dashboard tour for first-time users
  useEffect(() => {
    if (!summaryLoading && !insightsLoading && dashboardData && insightsData) {
      // Small delay to ensure DOM elements are rendered
      const timer = setTimeout(() => {
        if (shouldShowFirstTimeTour()) {
          triggerDashboardTour()
        }
      }, 1000)

      return () => clearTimeout(timer)
    }
  }, [summaryLoading, insightsLoading, dashboardData, insightsData, shouldShowFirstTimeTour, triggerDashboardTour])

  // Loading state
  if (summaryLoading || insightsLoading) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Dashboard"
          text="Your decision support system is live"
          showOrgName={true}
        />
        
        {/* Premium header badge */}
        <div className="px-4 md:px-6 lg:px-8 mb-6">
          <div className="flex items-center gap-3">
            {/* <Badge variant="secondary" className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 text-blue-400 border-blue-500/20">
              <Zap className="w-3 h-3 mr-1" />
              Premium Insights
            </Badge> */}
          </div>
        </div>

        <div className="space-y-6 md:space-y-8">
          {/* Summary tiles skeleton */}
          <div className="grid grid-cols-1 gap-4 px-4 sm:grid-cols-2 md:grid-cols-4 md:gap-6 md:px-6 lg:gap-8 xl:gap-8 xl:px-8">
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} className="h-32 rounded-2xl" />
            ))}
          </div>
          
          {/* Charts skeleton */}
          <div className="grid grid-cols-1 gap-4 px-4 md:grid-cols-2 md:gap-6 md:px-6 lg:grid-cols-6 lg:gap-8 xl:grid-cols-12 xl:gap-8 xl:px-8">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-80 rounded-2xl lg:col-span-6 xl:col-span-4" />
            ))}
          </div>
        </div>
      </DashboardShell>
    )
  }

  // Error state
  if (summaryError || insightsError) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Dashboard"
          text="Your decision support system is live"
        />
        <Alert variant="destructive" className="mx-4 md:mx-6 lg:mx-8">
          <AlertCircle className="size-4" />
          <AlertDescription>
            {summaryError || insightsError}
          </AlertDescription>
        </Alert>
      </DashboardShell>
    )
  }

  // No data state
  if (!dashboardData || !insightsData) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Dashboard"
          text="Your decision support system is live"
        />
        <div className="py-12 text-center">
          <p className="text-muted-foreground">No dashboard data available.</p>
        </div>
      </DashboardShell>
    )
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Dashboard"
        text="Your decision support system is live"
        showOrgName={true}
      />

      {/* Premium header badge */}
      <div className="px-4 md:px-6 lg:px-8 mb-6">
        <div className="flex items-center gap-3">
          {/* <Badge variant="secondary" className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 text-blue-400 border-blue-500/20">
            <Zap className="w-3 h-3 mr-1" />
            Premium Insights
          </Badge> */}

          <div className="ml-auto flex items-center gap-2">
            {/* <TrendingUp className="w-4 h-4 text-emerald-400" />
            <span className="text-xs text-muted-foreground">Live Data</span> */}
          </div>
        </div>
      </div>

      <div className="space-y-6 md:space-y-8 pb-24">
        {/* Show onboarding for incomplete setup */}
        {shouldShowOnboarding && (
          <OnboardingFlow
            userName={user?.name}
            onboardingStatus={dashboardData.onboarding}
            onDismiss={() => setShowOnboarding(false)}
            onRefresh={refetchSummary}
          />
        )}

        {/* Premium Summary Tiles */}
        <div data-tour="quick-stats">
          <PremiumSummaryTiles dashboardData={{ insights: insightsData }} />
        </div>

        {/* Premium Insights Charts */}
        <div data-tour="deals-section">
          <PremiumInsightsCharts insightsData={insightsData} />
        </div>

        {/* Premium Quick Actions - Sticky Footer */}
        <div data-tour="recent-activity">
          <PremiumQuickActions />
        </div>
      </div>
    </DashboardShell>
  )
} 