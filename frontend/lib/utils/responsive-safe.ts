/**
 * Webpack-Safe Responsive Utilities
 * 
 * Minimal responsive utilities designed to avoid webpack module resolution issues
 * in development mode while maintaining all functionality.
 */

// Core responsive classes for mobile-first design
export const mobileRetreat = {
  page: {
    container: "min-h-screen bg-gray-50 flex flex-col",
    content: "flex-1 px-4 py-6 md:px-6 md:py-8 lg:px-8 lg:py-10",
    maxWidth: "max-w-7xl mx-auto w-full"
  },
  loading: {
    container: "flex items-center justify-center p-8 md:p-12",
    spinner: "animate-spin rounded-full h-8 w-8 md:h-12 md:w-12 border-2 border-gray-300 border-t-gray-900",
    text: "text-sm md:text-base text-gray-600 mt-4"
  },
  empty: {
    container: "text-center py-12 md:py-16 lg:py-20",
    icon: "w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 text-gray-400",
    title: "text-lg md:text-xl font-semibold text-gray-900 mb-2",
    description: "text-sm md:text-base text-gray-600 mb-6",
    action: "inline-flex items-center justify-center"
  },
  error: {
    container: "text-center py-8 md:py-12",
    icon: "w-12 h-12 md:w-16 md:h-16 mx-auto mb-4 text-red-500",
    title: "text-lg md:text-xl font-semibold text-gray-900 mb-2",
    description: "text-sm md:text-base text-gray-600 mb-4"
  },
  header: {
    sticky: "sticky top-0 z-40 bg-white/95 backdrop-blur-sm border-b border-gray-200/50",
    content: "flex items-center justify-between h-16 px-4 md:px-6 lg:px-8",
    title: "text-2xl md:text-3xl font-bold text-gray-900",
    subtitle: "text-sm md:text-base text-gray-600 mt-1"
  },
  grid: {
    responsive: "grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6 lg:gap-8",
    cards: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8",
    stats: "grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6"
  }
}

// Core visual retreat classes for premium UI
export const visualRetreat = {
  card: {
    base: "bg-white/98 backdrop-blur-sm border border-gray-200/50 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 ease-out",
    interactive: "cursor-pointer hover:scale-[1.02] active:scale-[0.98] hover:bg-white hover:shadow-xl hover:border-gray-300/50",
    floating: "shadow-lg hover:shadow-xl border-gray-100 bg-white/95"
  },
  tabs: {
    container: "flex overflow-x-auto scrollbar-hide gap-2 p-1 bg-gray-50/50 rounded-xl",
    item: "flex-shrink-0 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 min-w-[80px] hover:bg-white/80 active:scale-95",
    active: "bg-white shadow-sm text-gray-900 border border-gray-200/50",
    inactive: "text-gray-600 hover:text-gray-900"
  },
  form: {
    container: "space-y-6 p-4 md:p-6 lg:p-8",
    field: "space-y-2",
    input: "w-full h-12 px-4 rounded-xl border border-gray-200 focus:border-gray-400 focus:ring-2 focus:ring-gray-100 transition-all duration-200 text-base",
    button: "w-full h-12 px-6 rounded-xl font-semibold transition-all duration-200 active:scale-[0.98]",
    label: "text-sm md:text-base font-medium mb-1 block",
    error: "text-sm text-destructive mt-1",
    fieldset: "space-y-4"
  },
  modal: {
    overlay: "fixed inset-0 z-50 bg-black/50 backdrop-blur-sm transition-all duration-300 ease-out",
    content: "fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50 w-[90vw] max-w-md min-w-[320px] max-h-[80vh] bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl p-8 transition-all duration-300 ease-out",
    contentAlert: "fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50 w-[90vw] max-w-sm min-w-[280px] bg-white/95 backdrop-blur-lg rounded-2xl shadow-xl p-6 transition-all duration-300 ease-out",
    contentLarge: "fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50 w-[95vw] max-w-4xl min-w-[320px] max-h-[90vh] bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl p-8 transition-all duration-300 ease-out",
    closeButton: "absolute right-4 top-4 rounded-lg p-2 opacity-70 transition-opacity hover:opacity-100 text-gray-500 hover:text-gray-700 hover:bg-gray-100 active:scale-95",
    header: "mb-6",
    body: "space-y-4 max-h-[80vh] overflow-y-auto",
    actions: "flex gap-3 pt-6 mt-6 justify-end"
  }
}

// Helper function for card variants
export const getVisualRetreatCard = (variant: 'base' | 'interactive' | 'floating' = 'base') => {
  const base = visualRetreat.card.base
  switch (variant) {
    case 'interactive':
      return `${base} ${visualRetreat.card.interactive}`
    case 'floating':
      return `${base} ${visualRetreat.card.floating}`
    default:
      return base
  }
} 