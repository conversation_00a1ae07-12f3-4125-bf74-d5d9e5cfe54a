"""
Exclusion Filter Models

This module defines the data models for exclusion filters, which allow organizations
to define criteria for excluding form submissions based on answers to specific questions.
"""

from datetime import datetime, timezone
from typing import Annotated, Any, Dict, List, Literal, Optional

from pydantic import Field, model_validator

from app.models.base import TractionXModel
from app.utils.common import ObjectIdField, PyObjectId
from app.utils.model.helper import populate_reference


class ExclusionFilter(TractionXModel):
    """
    Represents an exclusion filter for a form.

    Exclusion filters determine which form submissions should be excluded from processing
    based on specific criteria. They are structured as a condition tree with logical operators,
    similar to match rules.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    org_id: ObjectIdField  # Organization that owns this filter
    form_id: Annotated[
        ObjectIdField, populate_reference("Form")
    ]  # Form this filter applies to
    name: str  # Name of the filter
    description: Optional[str] = None  # Description of the filter

    # Condition tree structure
    operator: Literal["and", "or"] = "and"  # Logical operator for conditions
    conditions: List[Dict[str, Any]] = Field(default_factory=list)  # List of conditions

    # Status
    is_active: bool = True  # Whether this filter is active
    is_deleted: bool = False  # Whether this filter is deleted

    # Timestamps
    created_by: ObjectIdField  # User who created this filter
    created_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
    updated_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )

    @model_validator(mode="after")  # type: ignore
    def validate_conditions(self) -> "ExclusionFilter":
        """Validate that conditions are properly structured."""
        if not self.conditions:
            return self

        for condition in self.conditions:
            if not isinstance(condition, dict):
                raise ValueError("Each condition must be a dictionary")

            if "question_id" not in condition:
                raise ValueError("Each condition must have a question_id")

            if "operator" not in condition:
                raise ValueError("Each condition must have an operator")

            if "value" not in condition:
                raise ValueError("Each condition must have a value")

            # Validate operator
            valid_operators = [
                "eq",
                "ne",
                "gt",
                "lt",
                "gte",
                "lte",
                "in",
                "not_in",
                "contains",
                "not_contains",
            ]
            if condition["operator"] not in valid_operators:
                raise ValueError(
                    f"Invalid operator: {condition['operator']}. Must be one of: {', '.join(valid_operators)}"
                )

        return self
