"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { DealNote } from "@/lib/api/deal-notes-api"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { 
  MoreHorizontal, 
  Edit3, 
  Trash2, 
  Pi<PERSON>, 
  <PERSON>n<PERSON><PERSON>,
  <PERSON>ly,
  Clock,
  User
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"
import { formatDistanceToNow } from "date-fns"
import { OrgUser } from '@/lib/types/deal-notes';
import { structuredContentToText, renderStructuredContent } from '@/lib/utils/structured-content-display'

interface NoteCardProps {
  note: DealNote
  currentUserId?: string
  onUpdate: (noteId: string, content: string, taggedUserIds: string[]) => void
  onDelete: (noteId: string) => void
  onTogglePin: (noteId: string, pinned: boolean) => void
  orgUsers: OrgUser[];
}

export function NoteCard({ 
  note, 
  currentUserId, 
  onUpdate, 
  onDelete, 
  onTogglePin, 
  orgUsers
}: NoteCardProps) {
  // Get initial content from either structured_content or legacy content field
  const getInitialContent = () => {
    if (note.structured_content && note.structured_content.length > 0) {
      return structuredContentToText(note.structured_content)
    } else if ((note as any).content) {
      return (note as any).content
    }
    return ''
  }

  const [isEditing, setIsEditing] = useState(false)
  const [editContent, setEditContent] = useState(getInitialContent())
  const [showActions, setShowActions] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  // Remove: const { members } = useOrgMembers()

  const isAuthor = currentUserId === note.created_by._id
  const canEdit = isAuthor // Only author can edit for now
  const canDelete = isAuthor // Only author can delete for now
  const canPin = isAuthor // Only author can pin for now

  // Format timestamps
  const createdTime = formatDistanceToNow(new Date(note.created_at * 1000), { 
    addSuffix: true 
  })
  const updatedTime = note.updated_at !== note.created_at 
    ? formatDistanceToNow(new Date(note.updated_at * 1000), { addSuffix: true })
    : null

  // Handle edit submission
  const handleEditSubmit = async () => {
    const originalText = structuredContentToText(note.structured_content || [])
    if (!editContent.trim() || editContent === originalText) {
      setIsEditing(false)
      setEditContent(originalText)
      return
    }

    try {
      setIsSubmitting(true)
      await onUpdate(note._id, editContent, note.tagged_user_ids)
      setIsEditing(false)
    } catch (error) {
      // Error is handled by parent component
      setEditContent(originalText) // Reset to original content
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle edit cancellation
  const handleEditCancel = () => {
    setIsEditing(false)
    setEditContent(structuredContentToText(note.structured_content || []))
  }

  // Handle delete confirmation
  const handleDelete = () => {
    if (confirm("Are you sure you want to delete this note? This action cannot be undone.")) {
      onDelete(note._id)
    }
  }

  // Handle pin toggle
  const handleTogglePin = () => {
    onTogglePin(note._id, !note.pinned)
  }

  // Render structured content with proper mention styling
  const renderContent = () => {
    if (note.structured_content && note.structured_content.length > 0) {
      return note.structured_content.map((block, blockIdx) => {
        if (block.type === 'paragraph' && Array.isArray(block.children)) {
          return (
            <span key={blockIdx}>
              {block.children.map((child, childIdx) => {
                if (typeof child === 'object' && 'type' in child && child.type === 'mention') {
                  return (
                    <span key={childIdx} className="inline-flex items-center gap-1 rounded-md bg-blue-100 px-1.5 py-0.5 text-sm font-medium text-blue-700">
                      @{child.name}
                    </span>
                  );
                } else if (typeof child === 'object' && 'text' in child) {
                  return <span key={childIdx}>{child.text}</span>;
                }
                return null;
              })}
            </span>
          );
        }
        return null;
      });
    } else if ((note as any).content) {
      // Fallback to old content format - simple mention highlighting
      const content = (note as any).content;
      const parts = content.split(/(@\w+)/g);
      const elements = parts.map((part: string, index: number) => {
        if (part.startsWith('@')) {
          return (
            <span key={index} className="inline-flex items-center gap-1 rounded-md bg-blue-100 px-1.5 py-0.5 text-sm font-medium text-blue-700">
              {part}
            </span>
          );
        }
        return part;
      });
      return <div className="whitespace-pre-wrap">{elements}</div>
    } else {
      return <div className="text-gray-500 italic">No content</div>
    }
  }

  return (
    <motion.div
      className={cn(
        "group relative rounded-2xl border border-gray-200/60 bg-white/40 p-4 backdrop-blur-sm transition-all duration-300",
        "hover:border-gray-300/80 hover:bg-white/60 hover:shadow-md",
        note.pinned && "border-amber-200/60 bg-amber-50/40 hover:border-amber-300/80 hover:bg-amber-50/60"
      )}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
      whileHover={{ scale: 1.01 }}
      layout
    >
      {/* Pin indicator */}
      {note.pinned && (
        <div className="absolute -top-2 left-4 z-10">
          <div className="flex items-center gap-1 rounded-full bg-amber-100 px-2 py-1 text-xs font-medium text-amber-700 shadow-sm">
            <Pin className="size-3" />
            Pinned
          </div>
        </div>
      )}

      {/* Header */}
      <div className="flex items-start justify-between gap-3">
        <div className="flex items-start gap-3 flex-1 min-w-0">
          {/* Avatar */}
          <Avatar className="size-8 flex-shrink-0">
            <AvatarImage src={`https://avatar.vercel.sh/${note.created_by._id}`} />
            <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xs font-medium">
              {note.created_by.name?.charAt(0)?.toUpperCase() || 'U'}
            </AvatarFallback>
          </Avatar>

          {/* Content */}
          <div className="flex-1 min-w-0 space-y-2">
            {/* Author and timestamp */}
            <div className="flex items-center gap-2 text-sm">
              <span className="font-medium text-gray-900 truncate">
                {note.created_by.name || 'Unknown User'}
              </span>
              <span className="text-gray-500">•</span>
              <span className="text-gray-500 flex items-center gap-1">
                <Clock className="size-3" />
                {createdTime}
              </span>
              {updatedTime && (
                <>
                  <span className="text-gray-500">•</span>
                  <span className="text-gray-500 text-xs">edited {updatedTime}</span>
                </>
              )}
            </div>

            {/* Note content */}
            <div className="text-gray-700 leading-relaxed">
              {isEditing ? (
                <div className="space-y-3">
                  <textarea
                    value={editContent}
                    onChange={(e) => setEditContent(e.target.value)}
                    className="w-full min-h-[80px] rounded-lg border border-gray-300 p-3 text-sm resize-none focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="Edit your note..."
                    autoFocus
                  />
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      onClick={handleEditSubmit}
                      disabled={isSubmitting || !editContent.trim()}
                      className="gap-2"
                    >
                      {isSubmitting ? "Saving..." : "Save"}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleEditCancel}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="whitespace-pre-wrap">
                  {renderContent()}
                </div>
              )}
            </div>

            {/* Tagged users */}
            {note.tagged_user_ids.filter(userId => userId !== note.created_by._id).length > 0 && (
              <div className="flex items-center gap-2">
                {/* <span className="text-xs text-gray-500">Tagged:</span> */}
                {/* <div className="flex items-center gap-1">
                  {note.tagged_user_ids
                    .filter(userId => userId !== note.created_by._id)
                    .map((userId) => {
                      const member = orgUsers.find(m => m._id === userId)
                      return (
                        <Badge
                          key={userId}
                          variant="secondary"
                          className="text-xs bg-blue-50 text-blue-700 border-blue-200"
                        >
                          @{member?.name || member?.email || 'user'}
                        </Badge>
                      )
                    })}
                </div> */}
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <AnimatePresence>
          {showActions && (canEdit || canDelete || canPin) && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="flex items-center gap-1"
            >
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="size-8 p-0 opacity-60 hover:opacity-100"
                  >
                    <MoreHorizontal className="size-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  {canPin && (
                    <DropdownMenuItem onClick={handleTogglePin}>
                      {note.pinned ? (
                        <>
                          <PinOff className="mr-2 size-4" />
                          Unpin
                        </>
                      ) : (
                        <>
                          <Pin className="mr-2 size-4" />
                          Pin
                        </>
                      )}
                    </DropdownMenuItem>
                  )}
                  
                  {canEdit && !isEditing && (
                    <DropdownMenuItem onClick={() => setIsEditing(true)}>
                      <Edit3 className="mr-2 size-4" />
                      Edit
                    </DropdownMenuItem>
                  )}
                  
                  <DropdownMenuItem disabled>
                    <Reply className="mr-2 size-4" />
                    Reply
                  </DropdownMenuItem>
                  
                  {canDelete && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={handleDelete}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="mr-2 size-4" />
                        Delete
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  )
} 