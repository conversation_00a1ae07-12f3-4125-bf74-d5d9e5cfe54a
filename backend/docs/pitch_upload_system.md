# Universal Pitch Upload & Parsing Pipeline

This document describes the comprehensive pitch upload and parsing system that enables users to upload pitch decks or one-pagers and automatically extract structured deal data using AI.

## Overview

The system provides a complete pipeline for:
- **PDF Upload**: Secure upload to S3 with temporary storage
- **Text Extraction**: PyMuPDF-based PDF text extraction with OCR fallback
- **AI Parsing**: OpenAI GPT-4 powered content analysis and data extraction
- **Deal Creation**: Automatic deal creation with structured data
- **File Management**: Moving files to permanent storage with proper organization

## Architecture

```mermaid
graph TD
    A[User uploads PDF] --> B[POST /uploads/pitch]
    B --> C[Generate presigned URL]
    C --> D[Upload to S3 temp/]
    D --> E[Queue parse_pitch_upload job]
    E --> F[Extract text with PyMuPDF]
    F --> G[Parse with GPT-4]
    G --> H[Generate markdown summary]
    H --> I[Create Deal object]
    I --> J[Move PDF to permanent location]
    J --> K[Update deal with final URLs]
```

## API Endpoints

### POST /uploads/pitch

Upload a pitch deck or one-pager for AI parsing.

**Request Body:**
```json
{
  "type": "deck",  // "deck" or "one_pager"
  "filename": "startup_pitch.pdf",
  "content_type": "application/pdf",
  "file_size": 2048576
}
```

**Response:**
```json
{
  "temp_id": "43a7b2c1-...",
  "presigned_url": "https://s3.amazonaws.com/...",
  "s3_key": "decks/org123/temp/43a7b2c1-.../pitch_upload.pdf",
  "status": "queued",
  "expires_in": 3600
}
```

**Features:**
- ✅ PDF validation (content type and size limits)
- ✅ Temporary S3 storage with unique IDs
- ✅ Automatic queue job creation
- ✅ Presigned URL for secure upload

## Services

### PDFParserService

Handles PDF text extraction using PyMuPDF.

**Key Methods:**
- `extract_text_from_pdf()`: Extract text from PDF BytesIO
- `extract_text_from_s3()`: Extract text from S3-stored PDF
- `detect_pitch_type()`: Auto-detect deck vs one-pager

**Features:**
- ✅ Multi-page text extraction
- ✅ PDF metadata extraction
- ✅ OCR fallback detection (logs warning for image-based content)
- ✅ Automatic pitch type detection

### PitchParserService

LLM-based parsing using OpenAI GPT-4.

**Key Methods:**
- `parse_pitch_content()`: Extract structured data from text
- `generate_markdown_summary()`: Create investor-ready summary

**Extracted Fields:**
- `company_name`: Company name
- `tagline`: Company description/tagline
- `ask_amount`: Funding ask (e.g., "$1.5M", "Series A $5M")
- `team_summary`: Key founders and backgrounds
- `traction`: Metrics, revenue, customers, growth
- `market_size`: TAM, SAM, market opportunity
- `sector`: Industry categories (array)
- `source_page_map`: Page numbers where info was found

**Features:**
- ✅ Dynamic prompts based on pitch type
- ✅ Confidence scoring
- ✅ Fallback handling for parsing failures
- ✅ Professional markdown generation

### PitchProcessingHandler

Queue handler for background processing.

**Processing Steps:**
1. **PDF Text Extraction**: Extract text and metadata
2. **Auto-Detection**: Detect pitch type if not specified
3. **AI Parsing**: Extract structured data with GPT-4
4. **Markdown Generation**: Create investor summary
5. **Deal Creation**: Create deal with parsed data
6. **File Management**: Move PDF to permanent location
7. **URL Updates**: Update deal with final file URLs

**Error Handling:**
- ✅ Comprehensive error logging
- ✅ Fallback deal creation on parsing failure
- ✅ Transaction rollback on file operation failures

## Data Models

### Deal Integration

The system integrates with existing Deal models:

```python
deal_data = {
    "company_name": parsed_data.get("company_name"),
    "description": parsed_data.get("tagline"),
    "sector": parsed_data.get("sector", []),
    "status": DealStatus.TRIAGE,
    "enriched_data": {
        "pitch_parsing": parsed_data,
        "pdf_metadata": pdf_metadata,
        "processing_metadata": {...}
    },
    "tags": ["one_pager"] if pitch_type == "one_pager" else [],
    "timeline": [
        {
            "event": "Deal created from pitch upload",
            "timestamp": "2024-01-01T00:00:00Z",
            "details": {...}
        }
    ]
}
```

### S3 File Organization

```
decks/
├── {org_id}/
│   ├── temp/
│   │   └── {temp_id}/
│   │       └── pitch_upload.pdf
│   └── {deal_id}/
│       ├── pitch_upload.pdf
│       └── pitch_upload_summary.md
```

## Configuration

### Environment Variables

```bash
# S3 Configuration
S3_BUCKET_ASSETS=your-assets-bucket
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1

# OpenAI Configuration
OPENAI_API_KEY=your-openai-key

# File Upload Limits
MAX_PITCH_FILE_SIZE=52428800  # 50MB
S3_PRESIGNED_URL_EXPIRY=3600  # 1 hour
```

### Dependencies

```toml
# pyproject.toml
pymupdf = "^1.23.0"  # PDF text extraction
openai = "^1.84.0"   # AI parsing
boto3 = "^1.38.30"   # S3 operations
```

## Usage Examples

### Frontend Integration

```javascript
// Upload pitch deck
const uploadPitch = async (file) => {
  // 1. Request upload URL
  const response = await fetch('/uploads/pitch', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      type: 'deck',
      filename: file.name,
      content_type: file.type,
      file_size: file.size
    })
  });
  
  const { presigned_url, temp_id } = await response.json();
  
  // 2. Upload file to S3
  await fetch(presigned_url, {
    method: 'PUT',
    body: file,
    headers: { 'Content-Type': file.type }
  });
  
  // 3. Poll for completion or use webhooks
  return { temp_id, status: 'processing' };
};
```

### Backend Queue Processing

```python
# Queue job payload
job_data = {
    "temp_id": "43a7b2c1-...",
    "org_id": "org123",
    "user_id": "user456",
    "user_email": "<EMAIL>",
    "pitch_type": "deck",
    "filename": "startup_pitch.pdf",
    "file_size": 2048576,
    "s3_key": "decks/org123/temp/43a7b2c1-.../pitch_upload.pdf"
}

# Processing result
result = {
    "success": True,
    "deal_id": "deal789",
    "temp_id": "43a7b2c1-...",
    "pitch_type": "deck",
    "confidence_score": 0.85,
    "pages_processed": 12
}
```

## Testing

### Unit Tests

```bash
# Run pitch upload tests
pytest tests/test_pitch_upload.py -v

# Run with coverage
pytest tests/test_pitch_upload.py --cov=app.services.pdf_parser --cov=app.services.pitch_parser
```

### Integration Testing

```python
# Test complete flow
async def test_pitch_upload_flow():
    # 1. Upload PDF
    # 2. Process through queue
    # 3. Verify deal creation
    # 4. Check file movement
    # 5. Validate markdown generation
```

## Monitoring & Observability

### Metrics to Track

- Upload success/failure rates
- Processing time per pitch type
- AI parsing confidence scores
- Deal creation success rates
- File operation success rates

### Logging

```python
# Key log events
logger.info("Pitch upload queued", temp_id=temp_id, org_id=org_id)
logger.info("PDF text extracted", pages=page_count, confidence=confidence)
logger.info("Deal created from pitch", deal_id=deal_id, pitch_type=pitch_type)
logger.error("Pitch processing failed", error=str(e), temp_id=temp_id)
```

## Security Considerations

- ✅ **File Validation**: PDF content type and size limits
- ✅ **Temporary Storage**: Files stored in temp/ with unique IDs
- ✅ **Access Control**: Organization-based file isolation
- ✅ **Presigned URLs**: Time-limited upload URLs
- ✅ **Error Handling**: No sensitive data in error messages

## Performance Optimization

- ✅ **Async Processing**: Background queue processing
- ✅ **Efficient PDF Parsing**: PyMuPDF for fast text extraction
- ✅ **Smart Prompting**: Optimized prompts for different pitch types
- ✅ **File Management**: Efficient S3 operations with proper cleanup

## Future Enhancements

- **OCR Integration**: Full OCR support for image-based PDFs
- **Multi-language Support**: Support for non-English pitches
- **Advanced Analytics**: Pitch quality scoring and recommendations
- **Webhook Integration**: Real-time notifications for processing completion
- **Batch Processing**: Support for multiple file uploads
