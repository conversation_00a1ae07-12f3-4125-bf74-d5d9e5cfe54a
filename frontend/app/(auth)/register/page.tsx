import { Metada<PERSON> } from "next"
import { Suspense } from "react"
import Link from "next/link"

import { AuthLayout } from "@/components/auth/auth-layout"
import { RegisterForm } from "@/components/auth/register-form"
import { mobileRetreat } from "@/lib/utils/responsive"

export const metadata: Metadata = {
  title: "Create your TractionX account",
  description: "Join TractionX to access AI-powered investment intelligence and deal management.",
}

export default function RegisterPage() {
  return (
    <AuthLayout
      title="Create your account"
      subtitle="Join TractionX to access AI-powered investment intelligence"
    >
      <Suspense fallback={
        <div className={mobileRetreat.loading.container}>
          <div className={mobileRetreat.loading.spinner}></div>
          <p className={mobileRetreat.loading.text}>Loading...</p>
        </div>
      }>
        <RegisterForm />
      </Suspense>

      {/* Terms and Privacy */}
      <div className="text-center">
        <p className="text-xs text-muted-foreground">
          By creating an account, you agree to our{" "}
          <Link
            href="/terms"
            className="text-blue-600 underline underline-offset-4 hover:text-blue-500"
          >
            Terms of Service
          </Link>{" "}
          and{" "}
          <Link
            href="/privacy"
            className="text-blue-600 underline underline-offset-4 hover:text-blue-500"
          >
            Privacy Policy
          </Link>
          .
        </p>
      </div>
    </AuthLayout>
  )
}
