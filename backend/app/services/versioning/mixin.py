"""
Versioning Mixin

This module provides a reusable mixin for implementing versioning functionality
across different services.
"""

from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Type, TypeVar, Union
from bson import ObjectId

from app.core.logging import get_logger
from app.models.base import TractionXModel
from app.models.versioning import ResourceVersion

logger = get_logger(__name__)

T = TypeVar('T', bound=TractionXModel)


class VersioningMixin:
    """
    A mixin that provides versioning functionality for services.

    This mixin can be used by any service that needs to maintain version history
    for its resources. It provides methods for saving snapshots, bumping versions,
    and rolling back to previous versions.
    """

    async def save_snapshot(self, resource_instance: TractionXModel, actor_id: Optional[ObjectId] = None) -> ResourceVersion:
        """
        Save a snapshot of the current resource version before updating.

        Args:
            resource_instance: The resource instance to snapshot
            actor_id: Optional ID of the user performing the operation

        Returns:
            The created ResourceVersion instance
        """
        try:
            # Get the resource type from the class name
            resource_type = resource_instance.__class__.__name__

            # Get the current version from the resource if it exists
            current_version = getattr(resource_instance, 'version', 1)

            # Create a snapshot of the current state
            snapshot = resource_instance.model_dump(by_alias=True)

            # Create a new ResourceVersion instance
            resource_version = ResourceVersion(
                resource_type=resource_type,
                resource_id=resource_instance.id,
                version=current_version,
                snapshot=snapshot,
                created_by=actor_id
            )

            # Save the snapshot to the database
            await resource_version.save()

            logger.info(f"Saved snapshot of {resource_type} {resource_instance.id} (version {current_version})")

            return resource_version
        except Exception as e:
            logger.error(f"Error saving snapshot: {str(e)}")
            raise

    async def bump_version(
        self,
        resource_instance: TractionXModel,
        updates: Dict[str, Any],
        actor_id: Optional[ObjectId] = None
    ) -> TractionXModel:
        """
        Save snapshot, apply updates, increment version, update timestamps, and save.

        This method handles the complete versioning workflow:
        1. Save a snapshot of the current state
        2. Apply the updates to the resource
        3. Increment the version number
        4. Update timestamps
        5. Save the updated resource

        Args:
            resource_instance: The resource instance to update
            updates: Dictionary of updates to apply
            actor_id: Optional ID of the user performing the operation

        Returns:
            The updated resource instance
        """
        try:
            # Save a snapshot of the current state
            await self.save_snapshot(resource_instance, actor_id)

            # Apply updates to the resource
            for key, value in updates.items():
                setattr(resource_instance, key, value)

            # Increment version
            current_version = getattr(resource_instance, 'version', 1)
            setattr(resource_instance, 'version', current_version + 1)

            # Update timestamps
            now = int(datetime.now(timezone.utc).timestamp())
            setattr(resource_instance, 'updated_at', now)

            # # Set updated_by if actor_id is provided
            # if actor_id and hasattr(resource_instance, 'updated_by'):
            #     setattr(resource_instance, 'updated_by', actor_id)

            # Save the updated resource
            await resource_instance.save(actor_id=actor_id, is_update=True)

            logger.info(f"Bumped version of {resource_instance.__class__.__name__} {resource_instance.id} to {current_version + 1}")

            return resource_instance
        except Exception as e:
            logger.error(f"Error bumping version: {str(e)}")
            raise

    async def get_version_history(
        self,
        resource_type: str,
        resource_id: Union[str, ObjectId],
        limit: int = 10
    ) -> List[ResourceVersion]:
        """
        Get version history for a resource.

        Args:
            resource_type: Type of resource (e.g., 'Form', 'Thesis')
            resource_id: ID of the resource
            limit: Maximum number of versions to return (default: 10)

        Returns:
            List of ResourceVersion instances, ordered by version (descending)
        """
        try:
            # Convert string ID to ObjectId if needed
            if isinstance(resource_id, str):
                resource_id = ObjectId(resource_id)

            # Find all versions for this resource
            versions = await ResourceVersion.find_many(
                {
                    'resource_type': resource_type,
                    'resource_id': resource_id
                },
                sort=[('version', -1)],
                limit=limit
            )

            return versions
        except Exception as e:
            logger.error(f"Error getting version history: {str(e)}")
            raise

    async def rollback_to_version(
        self,
        resource_type: str,
        resource_id: Union[str, ObjectId],
        target_version: int,
        actor_id: Optional[ObjectId] = None,
        resource_class: Type[T] = None
    ) -> Optional[T]:
        """
        Roll back a resource to a specific version.

        Args:
            resource_type: Type of resource (e.g., 'Form', 'Thesis')
            resource_id: ID of the resource
            target_version: Version to roll back to
            actor_id: Optional ID of the user performing the operation
            resource_class: Class of the resource (for instantiation)

        Returns:
            The rolled-back resource instance, or None if the version doesn't exist
        """
        try:
            # Convert string ID to ObjectId if needed
            if isinstance(resource_id, str):
                resource_id = ObjectId(resource_id)

            # Find the target version
            target = await ResourceVersion.find_one({
                'resource_type': resource_type,
                'resource_id': resource_id,
                'version': target_version
            })

            if not target:
                logger.warning(f"Version {target_version} not found for {resource_type} {resource_id}")
                return None

            # Get the current resource
            if resource_class:
                current = await resource_class.find_one({'_id': resource_id})
                if not current:
                    logger.warning(f"{resource_type} {resource_id} not found")
                    return None

                # Save a snapshot of the current state before rollback
                snapshot = await self.save_snapshot(current, actor_id)

                # Apply the snapshot data to the current resource
                snapshot_data = target.snapshot

                # Keep the original ID
                snapshot_data['_id'] = resource_id

                # Increment the version
                current_version = getattr(current, 'version', 1)
                snapshot_data['version'] = current_version + 1

                # Update timestamps
                now = int(datetime.now(timezone.utc).timestamp())
                snapshot_data['updated_at'] = now

                # Set updated_by if actor_id is provided
                if actor_id and hasattr(current, 'updated_by'):
                    snapshot_data['updated_by'] = actor_id

                # Create a new instance with the snapshot data
                rolled_back = resource_class(**snapshot_data)

                # Save the rolled-back resource
                await rolled_back.save(actor_id=actor_id, is_update=True)

                # Update the snapshot with the new resource ID
                snapshot.new_resource_id = rolled_back.id
                await snapshot.save(is_update=True)

                logger.info(f"Rolled back {resource_type} {resource_id} to version {target_version}")

                return rolled_back
            else:
                logger.warning("Resource class not provided for rollback")
                return None
        except Exception as e:
            logger.error(f"Error rolling back to version: {str(e)}")
            raise
