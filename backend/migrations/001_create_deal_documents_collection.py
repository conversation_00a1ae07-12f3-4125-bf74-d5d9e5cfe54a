"""
Migration: Create deal_documents collection with indexes

This migration creates the deal_documents collection and sets up
the necessary indexes for optimal query performance.
"""

import asyncio

from app.core.config import settings
from motor.motor_asyncio import AsyncIOMotorClient


async def up():
    """Apply the migration."""
    client = AsyncIOMotorClient(settings.MONGODB_URL)
    db = client[settings.MONGODB_DB_NAME]

    # Create deal_documents collection if it doesn't exist
    collection_name = "deal_documents"
    if collection_name not in await db.list_collection_names():
        await db.create_collection(collection_name)
        print(f"Created collection: {collection_name}")

    collection = db[collection_name]

    # Create indexes for optimal query performance
    indexes = [
        # Compound index for listing documents by deal and org
        [("deal_id", 1), ("org_id", 1), ("is_active", 1)],
        # Index for finding documents by deal
        [("deal_id", 1)],
        # Index for finding documents by organization
        [("org_id", 1)],
        # Index for finding documents by source
        [("source", 1)],
        # Index for finding documents by uploader
        [("uploaded_by_user_id", 1)],
        # Index for finding documents by submission
        [("submission_id", 1)],
        # Index for sorting by creation date
        [("created_at", -1)],
        # Index for active documents
        [("is_active", 1)],
        # Index for document status
        [("status", 1)],
    ]

    for index_spec in indexes:
        try:
            await collection.create_index(index_spec)
            print(f"Created index: {index_spec}")
        except Exception as e:
            print(f"Index {index_spec} may already exist: {e}")

    print("Migration completed successfully!")
    client.close()


async def down():
    """Rollback the migration."""
    client = AsyncIOMotorClient(settings.MONGODB_URL)
    db = client[settings.MONGODB_DB_NAME]

    # Drop the collection
    collection_name = "deal_documents"
    if collection_name in await db.list_collection_names():
        await db.drop_collection(collection_name)
        print(f"Dropped collection: {collection_name}")

    print("Migration rollback completed!")
    client.close()


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "down":
        asyncio.run(down())
    else:
        asyncio.run(up())
