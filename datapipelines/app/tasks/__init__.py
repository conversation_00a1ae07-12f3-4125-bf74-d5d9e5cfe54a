"""
RQ Tasks for TractionX Data Pipeline Service.
"""

from typing import Callable, Dict

from app.tasks.apollo_company_processor import process_apollo_company_data_task
from app.tasks.company_enrichment import enrich_company_data
from app.tasks.comprehensive_company_enrichment import (
    enrich_company_data_comprehensive,
    merge_comprehensive_enrichment_results,
)
from app.tasks.comprehensive_data_processor import (
    process_comprehensive_enrichment_data_task,
)
from app.tasks.crunchbase_resolver import (
    process_brightdata_company_data_task,
    resolve_crunchbase_url_task,
)
from app.tasks.embedding_generation import generate_embeddings
from app.tasks.etl_merge import merge_enrichment_data
from app.tasks.founder_enrichment import enrich_founder_data
from app.tasks.linkedin_resolver import (
    process_brightdata_linkedin_data_task,
    resolve_linkedin_url_task,
)
from app.tasks.news_aggregation import aggregate_news_data
from app.tasks.pitchbook_resolver import (
    process_brightdata_pitchbook_data_task,
    resolve_pitchbook_url_task,
)
from app.tasks.sitemap_generator import generate_sitemap_task
from app.tasks.website_insights import generate_website_insights_task


def get_job_handlers() -> Dict[str, Callable]:
    """Get all available job handlers."""
    return {
        "enrich_company_data": enrich_company_data,
        "enrich_company_data_comprehensive": enrich_company_data_comprehensive,
        "enrich_founder_data": enrich_founder_data,
        "aggregate_news_data": aggregate_news_data,
        "generate_embeddings": generate_embeddings,
        "merge_enrichment_data": merge_enrichment_data,
        "resolve_crunchbase_url_task": resolve_crunchbase_url_task,
        "process_brightdata_company_data_task": process_brightdata_company_data_task,
        "process_apollo_company_data_task": process_apollo_company_data_task,
        "resolve_linkedin_url_task": resolve_linkedin_url_task,
        "process_brightdata_linkedin_data_task": process_brightdata_linkedin_data_task,
        "resolve_pitchbook_url_task": resolve_pitchbook_url_task,
        "process_brightdata_pitchbook_data_task": process_brightdata_pitchbook_data_task,
        "generate_sitemap_task": generate_sitemap_task,
        "generate_website_insights_task": generate_website_insights_task,
        "merge_comprehensive_enrichment_results": merge_comprehensive_enrichment_results,
        "process_comprehensive_enrichment_data_task": process_comprehensive_enrichment_data_task,
    }


# Export task functions for direct import
__all__ = [
    "enrich_company_data",
    "enrich_company_data_comprehensive",
    "enrich_founder_data",
    "aggregate_news_data",
    "generate_embeddings",
    "merge_enrichment_data",
    "resolve_crunchbase_url_task",
    "process_brightdata_company_data_task",
    "process_apollo_company_data_task",
    "resolve_linkedin_url_task",
    "process_brightdata_linkedin_data_task",
    "resolve_pitchbook_url_task",
    "process_brightdata_pitchbook_data_task",
    "generate_sitemap_task",
    "generate_website_insights_task",
    "merge_comprehensive_enrichment_results",
    "get_job_handlers",
]
