"use client"

import { createContext, useContext, useState, ReactNode } from "react"
import { PageLoading } from "@/components/ui/loading"

interface LoadingContextType {
  isLoading: boolean
  loadingText: string
  showLoading: (text?: string) => void
  hideLoading: () => void
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined)

export function LoadingProvider({ children }: { children: ReactNode }) {
  const [isLoading, setIsLoading] = useState(false)
  const [loadingText, setLoadingText] = useState("Loading...")

  const showLoading = (text: string = "Loading...") => {
    setLoadingText(text)
    setIsLoading(true)
  }

  const hideLoading = () => {
    setIsLoading(false)
  }

  return (
    <LoadingContext.Provider value={{ 
      isLoading, 
      loadingText, 
      showLoading, 
      hideLoading 
    }}>
      {children}
      {isLoading && <PageLoading text={loadingText} />}
    </LoadingContext.Provider>
  )
}

export function useLoading() {
  const context = useContext(LoadingContext)
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider')
  }
  return context
}

// HOC for pages that need loading states
export function withLoading<T extends object>(
  Component: React.ComponentType<T>,
  loadingText?: string
) {
  return function LoadingComponent(props: T) {
    const { showLoading, hideLoading } = useLoading()
    
    return <Component {...props} />
  }
} 