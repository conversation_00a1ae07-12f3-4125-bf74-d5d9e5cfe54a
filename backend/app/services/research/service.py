"""
Research service for external signals generation.
"""

import json
from datetime import datetime, timezone
from typing import Any, Dict, Optional

from app.core.logging import get_logger
from app.services.base import BaseService
from app.services.perplexity.client import PerplexityClient
from app.services.research.interface import IResearchService
from app.services.research.prompts import (
    format_competitors_prompt,
    format_market_prompt,
    format_news_prompt,
    format_summary_prompt,
)
from app.services.research.schemas import (
    CompetitorsResponse,
    ExecutiveSummaryResponse,
    ExternalSignalsResponse,
    MarketResponse,
    NewsResponse,
)
from app.utils.aws.s3 import S3Storage

logger = get_logger(__name__)


class ResearchService(BaseService, IResearchService):
    """Service for generating external signals research using Perplexity API."""

    def __init__(self):
        """Initialize the research service."""
        self.logger = logger
        self.perplexity_client: Optional[PerplexityClient] = None
        self.s3_storage: Optional[S3Storage] = None
        self.initialized = False

    async def initialize(self) -> None:
        """Initialize service dependencies."""
        if not self.initialized:
            self.logger.info("Initializing ResearchService")

            # Initialize Perplexity client
            self.perplexity_client = PerplexityClient()

            # Initialize S3 storage
            self.s3_storage = S3Storage()
            await self.s3_storage.initialize()

            self.initialized = True
            self.logger.info("ResearchService initialized successfully")

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        if self.perplexity_client:
            await self.perplexity_client.cleanup()
        if self.s3_storage:
            await self.s3_storage.cleanup()
        self.initialized = False

    async def generate_external_signals(
        self, deal_id: str, context_block: Dict[str, Any], force_refresh: bool = True
    ) -> ExternalSignalsResponse:
        """
        Generate complete external signals research for a deal.

        Args:
            deal_id: The deal ID
            context_block: Deal context block data
            force_refresh: Whether to force refresh existing research

        Returns:
            Complete external signals response
        """
        if not self.initialized:
            await self.initialize()

        self.logger.info(f"Generating external signals for deal {deal_id}")

        # Check if research already exists (unless force refresh)
        if not force_refresh:
            existing_research = await self._load_existing_research(deal_id)
            if existing_research:
                self.logger.info(f"Returning existing research for deal {deal_id}")
                return existing_research

        # Generate each research component
        competitors_data = await self._generate_competitors_research(
            deal_id, context_block
        )
        market_data = await self._generate_market_research(deal_id, context_block)
        news_data = await self._generate_news_research(deal_id, context_block)

        # Generate executive summary using all research data
        summary_data = await self._generate_executive_summary(
            deal_id, context_block, competitors_data, market_data, news_data
        )

        # Create complete response
        external_signals = ExternalSignalsResponse(
            deal_id=deal_id,
            competitors=competitors_data,
            market=market_data,
            news=news_data,
            summary=summary_data,
            generated_at=int(datetime.now(timezone.utc).timestamp()),
            version="v1.0",
        )

        self.logger.info(f"Successfully generated external signals for deal {deal_id}")
        return external_signals

    async def _generate_competitors_research(
        self, deal_id: str, context_block: Dict[str, Any]
    ) -> Optional[CompetitorsResponse]:
        """Generate competitors research."""
        try:
            # Check if already exists
            existing_data = await self._load_research_component(deal_id, "competitors")
            if existing_data:
                return CompetitorsResponse(**existing_data)

            self.logger.info(f"Generating competitors research for deal {deal_id}")

            # Format prompt
            prompt = format_competitors_prompt(context_block)

            # Call Perplexity API
            messages = [{"role": "user", "content": prompt}]
            response = await self.perplexity_client.create_chat_completion(
                messages=messages,
                model="sonar-reasoning",
                max_tokens=2000,
                temperature=0.2,
            )

            # Extract and parse response
            content = response["choices"][0]["message"]["content"]
            research_data = self._parse_json_response(content)

            if not research_data or "competitors" not in research_data:
                raise ValueError("Invalid competitors response format")

            # Validate and create response
            competitors_response = CompetitorsResponse(**research_data)

            # Store in S3
            await self._store_research_component(deal_id, "competitors", research_data)

            self.logger.info(
                f"Successfully generated competitors research for deal {deal_id}"
            )
            return competitors_response

        except Exception as e:
            self.logger.error(
                f"Error generating competitors research for deal {deal_id}: {str(e)}"
            )
            return None

    async def _generate_market_research(
        self, deal_id: str, context_block: Dict[str, Any]
    ) -> Optional[MarketResponse]:
        """Generate market research."""
        try:
            # Check if already exists
            existing_data = await self._load_research_component(deal_id, "market")
            if existing_data:
                return MarketResponse(**existing_data)

            self.logger.info(f"Generating market research for deal {deal_id}")

            # Format prompt
            prompt = format_market_prompt(context_block)

            # Call Perplexity API
            messages = [{"role": "user", "content": prompt}]
            response = await self.perplexity_client.create_chat_completion(
                messages=messages,
                model="sonar-reasoning",
                max_tokens=2000,
                temperature=0.2,
            )

            # Extract and parse response
            content = response["choices"][0]["message"]["content"]
            research_data = self._parse_json_response(content)

            if not research_data or "market_trends" not in research_data:
                raise ValueError("Invalid market response format")

            # Validate and create response
            market_response = MarketResponse(**research_data)

            # Store in S3
            await self._store_research_component(deal_id, "market", research_data)

            self.logger.info(
                f"Successfully generated market research for deal {deal_id}"
            )
            return market_response

        except Exception as e:
            self.logger.error(
                f"Error generating market research for deal {deal_id}: {str(e)}"
            )
            return None

    async def _generate_news_research(
        self, deal_id: str, context_block: Dict[str, Any]
    ) -> Optional[NewsResponse]:
        """Generate news research."""
        try:
            # Check if already exists
            existing_data = await self._load_research_component(deal_id, "news")
            if existing_data:
                return NewsResponse(**existing_data)

            self.logger.info(f"Generating news research for deal {deal_id}")

            # Format prompt
            prompt = format_news_prompt(context_block)

            # Call Perplexity API
            messages = [{"role": "user", "content": prompt}]
            response = await self.perplexity_client.create_chat_completion(
                messages=messages,
                model="sonar-reasoning",
                max_tokens=2000,
                temperature=0.2,
            )

            # Extract and parse response
            content = response["choices"][0]["message"]["content"]
            research_data = self._parse_json_response(content)

            if not research_data or "news_signals" not in research_data:
                raise ValueError("Invalid news response format")

            # Validate and create response
            news_response = NewsResponse(**research_data)

            # Store in S3
            await self._store_research_component(deal_id, "news", research_data)

            self.logger.info(f"Successfully generated news research for deal {deal_id}")
            return news_response

        except Exception as e:
            self.logger.error(
                f"Error generating news research for deal {deal_id}: {str(e)}"
            )
            return None

    async def _generate_executive_summary(
        self,
        deal_id: str,
        context_block: Dict[str, Any],
        competitors_data: Optional[CompetitorsResponse],
        market_data: Optional[MarketResponse],
        news_data: Optional[NewsResponse],
    ) -> Optional[ExecutiveSummaryResponse]:
        """Generate executive summary using all research data."""
        try:
            # Check if already exists
            existing_data = await self._load_research_component(deal_id, "summary")
            if existing_data:
                return ExecutiveSummaryResponse(**existing_data)

            self.logger.info(f"Generating executive summary for deal {deal_id}")

            # Convert response objects to dict for prompt formatting
            competitors_dict = (
                competitors_data.model_dump() if competitors_data else None
            )
            market_dict = market_data.model_dump() if market_data else None
            news_dict = news_data.model_dump() if news_data else None

            # Format prompt with research data
            prompt = format_summary_prompt(
                context_block, competitors_dict, market_dict, news_dict
            )

            # Call Perplexity API
            messages = [{"role": "user", "content": prompt}]
            response = await self.perplexity_client.create_chat_completion(
                messages=messages,
                model="sonar-reasoning",
                max_tokens=2000,
                temperature=0.2,
            )

            # Extract and parse response
            content = response["choices"][0]["message"]["content"]
            research_data = self._parse_json_response(content)

            if not research_data or "executive_summary" not in research_data:
                raise ValueError("Invalid summary response format")

            # Validate and create response
            summary_response = ExecutiveSummaryResponse(**research_data)

            # Store in S3
            await self._store_research_component(deal_id, "summary", research_data)

            self.logger.info(
                f"Successfully generated executive summary for deal {deal_id}"
            )
            return summary_response

        except Exception as e:
            self.logger.error(
                f"Error generating executive summary for deal {deal_id}: {str(e)}"
            )
            return None

    async def _load_existing_research(
        self, deal_id: str
    ) -> Optional[ExternalSignalsResponse]:
        """Load existing complete research from S3."""
        try:
            # Try to load all components
            competitors_data = await self._load_research_component(
                deal_id, "competitors"
            )
            market_data = await self._load_research_component(deal_id, "market")
            news_data = await self._load_research_component(deal_id, "news")
            summary_data = await self._load_research_component(deal_id, "summary")
            self.logger.info(f"Market Data: {market_data}")
            self.logger.info(f"News Data: {news_data}")
            self.logger.info(f"Competitors Data: {competitors_data}")
            self.logger.info(f"Summary Data: {summary_data}")
            # If any component is missing, return None to trigger full regeneration
            # if not all([competitors_data, market_data, news_data, summary_data]):
            #     return None

            # Create response objects
            competitors_response = (
                CompetitorsResponse(**competitors_data) if competitors_data else None
            )
            market_response = MarketResponse(**market_data) if market_data else None
            news_response = NewsResponse(**news_data) if news_data else None
            summary_response = (
                ExecutiveSummaryResponse(**summary_data) if summary_data else None
            )

            return ExternalSignalsResponse(
                deal_id=deal_id,
                competitors=competitors_response,
                market=market_response,
                news=news_response,
                summary=summary_response,
                generated_at=int(datetime.now(timezone.utc).timestamp()),
                version="v1.0",
            )

        except Exception as e:
            self.logger.error(
                f"Error loading existing research for deal {deal_id}: {str(e)}"
            )
            return None

    async def _load_research_component(
        self, deal_id: str, component: str
    ) -> Optional[Dict[str, Any]]:
        """Load a specific research component from S3."""
        try:
            key = f"deals/{deal_id}/research/{component}.json"
            data = await self.s3_storage.get_object(key)
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            self.logger.debug(
                f"Research component {component} not found for deal {deal_id}: {str(e)}"
            )
            return None

    async def _store_research_component(
        self, deal_id: str, component: str, data: Dict[str, Any]
    ) -> None:
        """Store a research component to S3."""
        try:
            key = f"deals/{deal_id}/research/{component}.json"
            await self.s3_storage.put_object(key, json.dumps(data, indent=2))
            self.logger.info(f"Stored {component} research for deal {deal_id}")
        except Exception as e:
            self.logger.error(
                f"Error storing {component} research for deal {deal_id}: {str(e)}"
            )
            raise

    def _parse_json_response(self, content: str) -> Optional[Dict[str, Any]]:
        """Parse JSON response from Perplexity, handling potential formatting issues."""
        try:
            # Try direct JSON parsing first
            return json.loads(content)
        except json.JSONDecodeError:
            # Try to extract JSON from markdown code blocks
            import re

            json_match = re.search(
                r"```(?:json)?\s*(\{.*?\})\s*```", content, re.DOTALL
            )
            if json_match:
                try:
                    return json.loads(json_match.group(1))
                except json.JSONDecodeError:
                    pass

            # Try to find JSON object in the content
            json_match = re.search(r"\{.*\}", content, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group(0))
                except json.JSONDecodeError:
                    pass

            self.logger.error(
                f"Failed to parse JSON from Perplexity response: {content}"
            )
            return None

    async def get_external_signals(
        self, deal_id: str
    ) -> Optional[ExternalSignalsResponse]:
        """Get existing external signals for a deal."""
        if not self.initialized:
            await self.initialize()

        return await self._load_existing_research(deal_id)

    async def refresh_external_signals(
        self, deal_id: str, context_block: Dict[str, Any]
    ) -> ExternalSignalsResponse:
        """Force refresh external signals for a deal."""
        return await self.generate_external_signals(
            deal_id, context_block, force_refresh=True
        )
