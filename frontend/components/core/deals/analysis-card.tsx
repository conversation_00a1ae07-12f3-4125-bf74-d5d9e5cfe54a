import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { BaseCard } from "@/components/ui/base-card"
import { Progress } from "@/components/ui/progress"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import { ExternalLink, ChevronRight, AlertTriangle } from "lucide-react"

interface AnalysisCardProps {
  title: string
  description: string
  icon: React.ReactNode
  status?: "pending" | "complete" | "coming-soon"
  score?: number
  bonusPoints?: number
  penaltyPoints?: number
  onViewDetails?: () => void
  className?: string
}

export function AnalysisCard({
  title,
  description,
  icon,
  status = "pending",
  score,
  bonusPoints,
  penaltyPoints,
  onViewDetails,
  className
}: AnalysisCardProps) {
  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-700 bg-green-50 border-green-200"
    if (score >= 60) return "text-blue-700 bg-blue-50 border-blue-200"
    if (score >= 40) return "text-yellow-700 bg-yellow-50 border-yellow-200"
    return "text-red-700 bg-red-50 border-red-200"
  }

  const renderContent = () => {
    if (status === "coming-soon") {
      return (
        <div className="flex flex-col items-center text-center">
          <Badge variant="outline" className="mb-4">Coming Soon</Badge>
          <div className="mb-3 text-gray-400">{icon}</div>
          <p className="text-sm text-gray-500">{description}</p>
        </div>
      )
    }

    if (status === "pending") {
      return (
        <div className="flex flex-col items-center text-center">
          <Badge variant="outline" className="mb-4">Analysis Pending</Badge>
          <div className="mb-3 text-gray-400">{icon}</div>
          <p className="text-sm text-gray-500">Analysis will be available soon</p>
        </div>
      )
    }

    return (
      <div className="flex w-full flex-col items-center">
        <div className="mb-3 flex w-full items-center justify-between">
          <div className="text-blue-600">{icon}</div>
          {score !== undefined && (
            <div className="text-right">
              <div className="mb-2 text-4xl font-bold">{score.toFixed(2)}%</div>
              <Progress value={score} className="h-2 w-32" />
            </div>
          )}
        </div>

        {(bonusPoints || penaltyPoints) && (
          <div className="mt-3 flex items-center gap-3">
            {bonusPoints && bonusPoints > 0 && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Badge className="border-green-200 bg-green-50 text-green-700">
                      +{bonusPoints} bonus
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Additional points awarded based on specific criteria</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            {penaltyPoints && penaltyPoints > 0 && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Badge className="border-red-200 bg-red-50 text-red-700">
                      -{penaltyPoints} penalty
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Points deducted based on specific criteria</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        )}

        {onViewDetails && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onViewDetails}
            className="mt-4"
          >
            View Details
            <ChevronRight className="ml-2 size-4" />
          </Button>
        )}
      </div>
    )
  }

  return (
    <BaseCard
      className={cn(
        status === "coming-soon" && "bg-gray-50/80",
        className
      )}
      isInteractive={status === "complete"}
    >
      <div className="mb-3 text-center">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>
      {renderContent()}
    </BaseCard>
  )
} 