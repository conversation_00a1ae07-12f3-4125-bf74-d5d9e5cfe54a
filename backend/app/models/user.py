import secrets
import string
from datetime import datetime
from enum import Enum
from typing import Annotated, Any, Dict, List, Optional

from pydantic import EmailStr, Field, model_validator

from app.models.base import TractionXModel
from app.models.organization import Organization
from app.models.role import Role
from app.utils.common import ObjectIdField, PyObjectId
from app.utils.model.helper import populate_reference


class UserStatus(str, Enum):
    ACTIVE = "active"
    SUSPENDED = "suspended"
    INVITED = "invited"


class User(TractionXModel):
    """
    Represents a user in the system, including authentication, organization memberships, roles, and status.
    """

    id: ObjectIdField = Field(default_factory=ObjectIdField, alias="_id")  # type: ignore
    name: str
    email: EmailStr
    password_hash: str = Field(default="")
    provider: str = "email"  # email, google, linkedin
    org_id: Annotated[ObjectIdField, populate_reference(Organization)] = Field(
        ..., description="Default organization ID"
    )
    org_ids: List[ObjectIdField] = Field(
        default_factory=list, description="List of organization IDs user has access to"
    )
    role_id: Annotated[Optional[ObjectIdField], populate_reference(Role)] = None
    status: UserStatus = Field(default=UserStatus.INVITED)
    is_superuser: bool = False
    is_active: bool = True
    created_at: int = Field(default_factory=lambda: int(datetime.utcnow().timestamp()))
    updated_at: int = Field(default_factory=lambda: int(datetime.utcnow().timestamp()))
    last_login: Optional[int] = None

    # User preferences for UI customization and behavior
    preferences: Dict[str, Any] = Field(
        default_factory=dict,
        description="User preferences for UI customization, filters, and behavior",
    )

    @model_validator(mode="after")
    def activate_user(self):
        if self.last_login and self.status == UserStatus.INVITED:
            # return a copy with status flipped to ACTIVE
            return self.model_copy(update={"status": UserStatus.ACTIVE})
        return self


class PublicUserStatus(str, Enum):
    ACTIVE = "active"
    SUSPENDED = "suspended"


class PublicUser(TractionXModel):
    """
    Represents a public user who accesses shared forms without full system registration.
    These users are separate from the main User model and have limited access.
    """

    id: ObjectIdField = Field(default_factory=ObjectIdField, alias="_id")  # type: ignore
    email: EmailStr
    name: Optional[str] = None
    status: PublicUserStatus = Field(default=PublicUserStatus.ACTIVE)
    is_active: bool = True
    org_id: Optional[Annotated[ObjectIdField, populate_reference(Organization)]] = (
        Field(..., description="Organization ID (from sharing config)")
    )
    created_at: int = Field(default_factory=lambda: int(datetime.utcnow().timestamp()))
    updated_at: int = Field(default_factory=lambda: int(datetime.utcnow().timestamp()))
    last_access: Optional[int] = None
    # Track which shared forms this public user has accessed
    accessed_forms: List[ObjectIdField] = Field(
        default_factory=list,
        description="List of form IDs this public user has accessed",
    )
    # Optional metadata for tracking
    metadata: dict = Field(
        default_factory=dict, description="Additional metadata for the public user"
    )


class InviteCodeStatus(str, Enum):
    PENDING = "pending"
    REDEEMED = "redeemed"
    EXPIRED = "expired"


class InviteCode(TractionXModel):
    """
    Represents an invite code for organization onboarding.
    Single-use codes that allow new users to create organizations.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    code: str = Field(..., description="Unique invite code (8-10 characters)")
    email: EmailStr = Field(..., description="Email address reserved for this invite")
    org_name: Optional[str] = Field(
        None, description="Optional pre-filled organization name"
    )
    status: InviteCodeStatus = Field(default=InviteCodeStatus.PENDING)
    created_at: int = Field(default_factory=lambda: int(datetime.utcnow().timestamp()))
    redeemed_at: Optional[int] = None
    redeemed_by: Optional[ObjectIdField] = None  # User ID who redeemed the code
    updated_at: int = Field(default_factory=lambda: int(datetime.utcnow().timestamp()))
    expires_at: int = Field(
        default_factory=lambda: int(
            (datetime.utcnow().timestamp() + (30 * 24 * 60 * 60))
        )  # 30 days
    )
    metadata: dict = Field(
        default_factory=dict, description="Additional metadata for tracking"
    )

    @classmethod
    def generate_code(cls) -> str:
        """Generate a unique 8-character invite code."""
        # Use uppercase letters and numbers, excluding confusing characters
        chars = string.ascii_uppercase + string.digits
        chars = (
            chars.replace("0", "").replace("O", "").replace("1", "").replace("I", "")
        )
        return "".join(secrets.choice(chars) for _ in range(8))

    def is_expired(self) -> bool:
        """Check if the invite code is expired."""
        return int(datetime.utcnow().timestamp()) > self.expires_at

    def is_redeemable(self) -> bool:
        """Check if the invite code can be redeemed."""
        return self.status == InviteCodeStatus.PENDING and not self.is_expired()

    def redeem(self, user_id: ObjectIdField) -> None:
        """Mark the invite code as redeemed."""
        self.status = InviteCodeStatus.REDEEMED
        self.redeemed_at = int(datetime.utcnow().timestamp())
        self.redeemed_by = user_id
