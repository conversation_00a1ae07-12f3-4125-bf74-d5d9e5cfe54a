"""
Tool management API endpoints for TractionX AI Agent system.
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field

from ai.tools.base import tool_registry
from ai.models import AgentContext
from ai.auth import get_current_context

router = APIRouter(prefix="/tools", tags=["tools"])


class ToolInfo(BaseModel):
    """Tool information model."""
    name: str = Field(..., description="Tool name")
    description: str = Field(..., description="Tool description")
    category: Optional[str] = Field(None, description="Tool category")
    tags: List[str] = Field(default_factory=list, description="Tool tags")
    class_name: str = Field(..., description="Tool class name")
    enabled: bool = Field(default=True, description="Whether tool is enabled")


class ToolListResponse(BaseModel):
    """Response for tool list endpoint."""
    tools: List[ToolInfo] = Field(..., description="List of available tools")
    total_count: int = Field(..., description="Total number of tools")
    categories: List[str] = Field(..., description="Available categories")


class ToolExecutionRequest(BaseModel):
    """Request for tool execution."""
    tool_name: str = Field(..., description="Name of tool to execute")
    input_data: Dict[str, Any] = Field(..., description="Input data for tool")


class ToolExecutionResponse(BaseModel):
    """Response for tool execution."""
    success: bool = Field(..., description="Whether execution was successful")
    data: Optional[Dict[str, Any]] = Field(None, description="Tool output data")
    error: Optional[str] = Field(None, description="Error message if failed")
    execution_time: Optional[float] = Field(None, description="Execution time in seconds")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


@router.get("/", response_model=ToolListResponse)
async def list_tools(
    category: Optional[str] = None,
    tags: Optional[str] = None,
    context: AgentContext = Depends(get_current_context)
):
    """
    List all available tools with optional filtering.
    
    Args:
        category: Filter by tool category
        tags: Filter by tags (comma-separated)
        context: Current agent context
    
    Returns:
        List of available tools with metadata
    """
    try:
        # Ensure tools are discovered
        tool_registry.discover_tools()
        
        # Get tools based on filters
        if category:
            tools = tool_registry.get_tools_by_category(category)
        elif tags:
            tag_list = [tag.strip() for tag in tags.split(",")]
            tools = tool_registry.get_tools_by_tags(tag_list)
        else:
            tools = tool_registry.get_all_tools()
        
        # Get tool metadata
        tool_list = tool_registry.list_available_tools()
        
        # Filter tool list based on available tools
        if category or tags:
            available_names = set(tools.keys())
            tool_list = [tool for tool in tool_list if tool["name"] in available_names]
        
        # Convert to response format
        tool_infos = [
            ToolInfo(
                name=tool["name"],
                description=tool["description"],
                category=tool.get("category"),
                tags=tool.get("tags", []),
                class_name=tool["class_name"],
                enabled=True
            )
            for tool in tool_list
        ]
        
        # Get unique categories
        categories = list(set(
            tool.get("category") for tool in tool_list 
            if tool.get("category")
        ))
        
        return ToolListResponse(
            tools=tool_infos,
            total_count=len(tool_infos),
            categories=sorted(categories)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list tools: {str(e)}")


@router.get("/{tool_name}")
async def get_tool_info(
    tool_name: str,
    context: AgentContext = Depends(get_current_context)
):
    """
    Get detailed information about a specific tool.
    
    Args:
        tool_name: Name of the tool
        context: Current agent context
    
    Returns:
        Detailed tool information including schema
    """
    try:
        # Get tool instance
        tool = tool_registry.get_tool_instance(tool_name)
        if not tool:
            raise HTTPException(status_code=404, detail=f"Tool '{tool_name}' not found")
        
        # Get tool metadata
        tool_list = tool_registry.list_available_tools()
        tool_metadata = next((t for t in tool_list if t["name"] == tool_name), None)
        
        if not tool_metadata:
            raise HTTPException(status_code=404, detail=f"Tool metadata for '{tool_name}' not found")
        
        # Get tool schema
        try:
            schema = tool.get_schema()
        except Exception as e:
            schema = {"error": f"Failed to get schema: {str(e)}"}
        
        return {
            "name": tool.name,
            "description": tool.description,
            "category": tool_metadata.get("category"),
            "tags": tool_metadata.get("tags", []),
            "class_name": tool_metadata["class_name"],
            "enabled": True,
            "schema": schema
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get tool info: {str(e)}")


@router.post("/{tool_name}/execute", response_model=ToolExecutionResponse)
async def execute_tool(
    tool_name: str,
    request: ToolExecutionRequest,
    context: AgentContext = Depends(get_current_context)
):
    """
    Execute a tool with provided input data.
    
    Args:
        tool_name: Name of the tool to execute
        request: Tool execution request with input data
        context: Current agent context
    
    Returns:
        Tool execution result
    """
    try:
        # Validate tool name matches request
        if tool_name != request.tool_name:
            raise HTTPException(
                status_code=400, 
                detail=f"Tool name mismatch: URL has '{tool_name}', body has '{request.tool_name}'"
            )
        
        # Get tool instance
        tool = tool_registry.get_tool_instance(tool_name)
        if not tool:
            raise HTTPException(status_code=404, detail=f"Tool '{tool_name}' not found")
        
        # Create tool input (this is simplified - in practice you'd need proper input validation)
        from ai.tools.base import ToolInput
        
        # For now, create a generic ToolInput with the provided data
        # In a real implementation, you'd need to validate against the tool's specific input schema
        tool_input = ToolInput(**request.input_data)
        
        # Execute tool
        import time
        start_time = time.time()
        
        result = await tool.execute(tool_input, context)
        
        execution_time = time.time() - start_time
        
        return ToolExecutionResponse(
            success=result.success,
            data=result.data,
            error=result.error,
            execution_time=execution_time,
            metadata=result.metadata
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")


@router.get("/categories/{category}")
async def get_tools_by_category(
    category: str,
    context: AgentContext = Depends(get_current_context)
):
    """
    Get all tools in a specific category.
    
    Args:
        category: Tool category
        context: Current agent context
    
    Returns:
        List of tools in the specified category
    """
    try:
        tools = tool_registry.get_tools_by_category(category)
        
        if not tools:
            raise HTTPException(status_code=404, detail=f"No tools found in category '{category}'")
        
        # Get metadata for these tools
        tool_list = tool_registry.list_available_tools()
        category_tools = [
            tool for tool in tool_list 
            if tool.get("category") == category
        ]
        
        return {
            "category": category,
            "tools": category_tools,
            "count": len(category_tools)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get tools by category: {str(e)}")


@router.post("/discover")
async def discover_tools(
    context: AgentContext = Depends(get_current_context)
):
    """
    Trigger tool discovery to refresh the registry.
    
    Args:
        context: Current agent context
    
    Returns:
        Discovery result with tool count
    """
    try:
        # Force rediscovery
        tool_registry._initialized = False
        tool_registry.discover_tools()
        
        # Get updated tool list
        tools = tool_registry.list_available_tools()
        
        return {
            "success": True,
            "message": "Tool discovery completed",
            "tools_found": len(tools),
            "categories": list(set(
                tool.get("category") for tool in tools 
                if tool.get("category")
            ))
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Tool discovery failed: {str(e)}")
