"use client"

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Plus, Target, Edit, MoreHorizontal, Trash2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EmptyPlaceholder } from '@/components/empty-placeholder';
import { DashboardHeader } from '@/components/header';
import { toast } from '@/components/ui/use-toast';
import { ThesisAPI } from '@/lib/api/thesis-api';
import { InvestmentThesis, ThesisStatus } from '@/lib/types/thesis';
import { useAuth } from '@/lib/auth-context';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import { FormAPI } from '@/lib/api/form-api';

export function ThesesList() {
  const [theses, setTheses] = useState<InvestmentThesis[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [thesisToDelete, setThesisToDelete] = useState<InvestmentThesis | null>(null);
  const [deleting, setDeleting] = useState(false);

  console.log('📋 ThesesList component loaded');

  const handleCreateThesis = async () => {
    console.log('🚀 Creating thesis with placeholder data');
    try {
      // First, get available forms to use as default
      const forms = await FormAPI.listForms();
      const defaultForm = forms.find(f => f.is_active) || forms[0];
      
      if (!defaultForm) {
        toast({
          title: "No forms available",
          description: "Please create a form first before creating a thesis.",
          variant: "destructive",
        });
        return;
      }

      // Create thesis with placeholder data
      const placeholderThesis = await ThesisAPI.createThesis({
        name: "New Investment Thesis",
        description: "Describe your investment thesis, target criteria, and evaluation approach...",
        form_id: defaultForm._id || defaultForm.id || '',
        status: ThesisStatus.DRAFT,
        is_active: true,
      });

      console.log('✅ Thesis created:', placeholderThesis);
      
      // Navigate to the thesis builder with the new thesis
      router.push(`/theses/${placeholderThesis._id || placeholderThesis.id}`);
      
      toast({
        title: "Thesis created",
        description: "Your new thesis has been created. You can now start adding matching and scoring rules.",
      });
      
    } catch (error) {
      console.error('❌ Failed to create thesis:', error);
      toast({
        title: "Error creating thesis",
        description: "Failed to create thesis. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Fetch theses from the API
  useEffect(() => {
    const fetchTheses = async () => {
      try {
        console.log("Fetching theses from API");
        setIsLoading(true);
        const thesesList = await ThesisAPI.listTheses();
        console.log("Theses fetched:", thesesList);

        // Ensure we always have an array
        if (Array.isArray(thesesList)) {
          setTheses(thesesList);
        } else {
          console.warn("API returned non-array response:", thesesList);
          setTheses([]);
        }
      } catch (error) {
        console.error("Error fetching theses:", error);
        setTheses([]); // Reset to empty array on error
        toast({
          title: "Error",
          description: "Failed to load theses. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (isAuthenticated) {
      fetchTheses();
    }
  }, [isAuthenticated]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <span className="inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-[#e0e7ef] via-[#f3f4f6] to-[#cbd5e1] text-[#22292f] text-xs font-medium shadow-sm backdrop-blur-sm border border-gray-200">Active</span>;
      case 'draft':
        return <Badge variant="secondary">Draft</Badge>;
      case 'archived':
        return <Badge variant="destructive">Archived</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div>
        <DashboardHeader
          heading="Investment Theses"
          text="Create and manage your investment theses."
        >
          <Button onClick={handleCreateThesis}>
            <Plus className="size-4" />
          </Button>
        </DashboardHeader>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 w-3/4 rounded bg-muted"></div>
                <div className="h-3 w-1/2 rounded bg-muted"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 rounded bg-muted"></div>
                  <div className="h-3 w-2/3 rounded bg-muted"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (theses.length === 0) {
    return (
      <div>
        <DashboardHeader
          heading="Investment Theses"
          text="Create and manage your investment theses."
        >
          <Button onClick={handleCreateThesis}>
            <Plus className="size-4" />
          </Button>
        </DashboardHeader>

        <EmptyPlaceholder>
          <EmptyPlaceholder.Icon name="page" />
          <EmptyPlaceholder.Title>No theses created</EmptyPlaceholder.Title>
          <EmptyPlaceholder.Description>
            You don&apos;t have any investment theses yet. Create a thesis to define your investment strategy.
          </EmptyPlaceholder.Description>
          <Button variant="outline" onClick={handleCreateThesis}>
            <Plus className="size-4" />
          </Button>
        </EmptyPlaceholder>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <DashboardHeader
        heading="Investment Theses"
        text="Create and manage your investment theses."
      >
        <Button onClick={handleCreateThesis}>
          <Plus className="size-4" />
        </Button>
      </DashboardHeader>

      <div className="grid auto-rows-fr gap-8 md:grid-cols-2 lg:grid-cols-3">
        {theses.map((thesis) => (
          <Card key={thesis._id || thesis.id} className="flex h-full flex-col transition-shadow hover:shadow-lg">
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="min-w-0 flex-1 space-y-2">
                  <CardTitle className="text-xl">{thesis.name}</CardTitle>
                  <div className="flex items-center gap-3">
                    {getStatusBadge(thesis.status)}
                    <span className="text-sm text-muted-foreground">
                      Updated {thesis.updated_at ? new Date(thesis.updated_at * 1000).toLocaleDateString() : 'N/A'}
                    </span>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="shrink-0">
                      <MoreHorizontal className="size-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem asChild>
                      <Link href={`/theses/${thesis._id || thesis.id}`}>
                        <Edit className="mr-2 size-4 text-muted-foreground" />
                        Edit
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => {
                        setThesisToDelete(thesis);
                        setDeleteDialogOpen(true);
                      }}
                      className="flex w-full text-destructive"
                    >
                      <Trash2 className="mr-2 size-4 text-destructive" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>

            <CardContent className="flex grow flex-col space-y-4">
              <CardDescription className="line-clamp-2">
                {thesis.description}
              </CardDescription>

              <div className="space-y-2">
                {/*<div className="flex items-center justify-between text-sm">*/}
                {/*  <span className="text-muted-foreground">Form Name:</span>*/}
                {/*  <span className="font-medium text-xs">{thesis.form_id}</span>*/}
                {/*</div>*/}

                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Rules:</span>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      <Target className="mr-1 size-3" />
                      {thesis.match_rules?.length || 0} match
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {thesis.scoring_rules?.length || 0} scoring
                    </Badge>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Status:</span>
                  <span className={`font-medium ${thesis.status === 'active' ? 'text-gray-900' : thesis.status === 'archived' ? 'text-gray-500' : 'text-gray-500'}`}>
                    {thesis.status === 'active' ? 'Active' : thesis.status === 'archived' ? 'Archived' : 'Inactive'}
                  </span>
                </div>
              </div>

              <div className="mt-auto pt-2">
                <Link href={`/theses/${thesis._id || thesis.id}`}>
                  <Button variant="outline" size="sm" className="w-full">
                    <Edit className="size-4" />
                        Edit Thesis
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <ConfirmDialog
        open={deleteDialogOpen}
        title="Delete Thesis"
        description={`Are you sure you want to delete the thesis "${thesisToDelete?.name || ''}"? This action cannot be undone.`}
        onCancel={() => { setDeleteDialogOpen(false); setThesisToDelete(null); }}
        onConfirm={async () => {
          if (!thesisToDelete) return;
          setDeleting(true);
          try {
            const id = String(thesisToDelete._id || thesisToDelete.id);
            await ThesisAPI.deleteThesis(id);
            setTheses(theses => theses.filter(t => String(t._id || t.id) !== id));
            toast({
              title: 'Thesis deleted',
              description: 'The thesis has been deleted successfully.'
            });
          } catch (error) {
            toast({
              title: 'Error',
              description: 'Failed to delete thesis. Please try again.',
              variant: 'destructive'
            });
          } finally {
            setDeleting(false);
            setDeleteDialogOpen(false);
            setThesisToDelete(null);
          }
        }}
        loading={deleting}
      />
    </div>
  );
}
