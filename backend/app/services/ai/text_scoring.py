"""
AI Text Scoring Service

This module provides AI-powered text scoring capabilities for thesis evaluation.
Uses OpenAI GPT models to evaluate text responses against good/bad references.
"""

import json
import re
from typing import Any, Dict, List, Optional

from openai import AsyncOpenAI  # type: ignore

from app.core.config import settings
from app.core.logging import get_logger
from app.models.form import QuestionType

logger = get_logger(__name__)


class AITextScoringService:
    """Service for AI-powered text scoring using OpenAI models."""

    def __init__(self):
        """Initialize the AI text scoring service."""
        self.client = (
            AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
            if settings.OPENAI_API_KEY
            else None
        )
        self.model = "gpt-4o-mini"  # Cost-effective model for scoring
        self.max_tokens = 500
        self.temperature = 0.1  # Low temperature for consistent scoring

    async def score_text_response(
        self,
        question_label: str,
        user_answer: str,
        good_reference: str,
        bad_reference: str,
        question_type: QuestionType = QuestionType.LONG_TEXT,
        context: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Score a text response using AI evaluation.

        Args:
            question_label: The question being answered
            user_answer: The user's text response
            good_reference: Example of a good answer
            bad_reference: Example of a bad answer
            question_type: Type of question (SHORT_TEXT or LONG_TEXT)
            context: Additional context for scoring

        Returns:
            Dict containing score, explanation, and sources
        """
        if not self.client:
            logger.warning(
                "OpenAI API key not configured, falling back to basic scoring"
            )
            return await self._fallback_text_scoring(
                user_answer, good_reference, bad_reference
            )

        try:
            # Create the scoring prompt
            prompt = self._create_scoring_prompt(
                question_label=question_label,
                user_answer=user_answer,
                good_reference=good_reference,
                bad_reference=bad_reference,
                question_type=question_type,
                context=context,
            )

            # Call OpenAI API
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert investment analyst evaluating startup responses. Provide accurate, consistent scoring with clear explanations.",
                    },
                    {"role": "user", "content": prompt},
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                response_format={"type": "json_object"},
            )

            # Parse the response
            result = json.loads(response.choices[0].message.content)

            # Validate and normalize the result
            return self._validate_ai_response(result)

        except Exception as e:
            logger.error(f"Error in AI text scoring: {str(e)}", exc_info=True)
            return await self._fallback_text_scoring(
                user_answer, good_reference, bad_reference
            )

    def _create_scoring_prompt(
        self,
        question_label: str,
        user_answer: str,
        good_reference: str,
        bad_reference: str,
        question_type: QuestionType,
        context: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Create a structured prompt for AI scoring."""

        # Determine if this is a market/founder signal question
        is_market_signal = any(
            keyword in question_label.lower()
            for keyword in [
                "market",
                "competition",
                "industry",
                "sector",
                "tam",
                "addressable",
            ]
        )
        is_founder_signal = any(
            keyword in question_label.lower()
            for keyword in [
                "founder",
                "team",
                "experience",
                "background",
                "expertise",
                "leadership",
            ]
        )

        prompt = f"""
You are evaluating a startup's response to the question: "{question_label}"

**User's Answer:**
{user_answer}

**Good Answer Reference:**
{good_reference}

**Bad Answer Reference:**
{bad_reference}

**Evaluation Criteria:**
1. Relevance to the question
2. Quality and depth of information
3. Alignment with good practices vs. red flags
4. Specificity and evidence provided
5. Overall investment attractiveness

"""

        if is_market_signal:
            prompt += """
**Market Signal Focus:**
- Market size and growth potential
- Competitive landscape understanding
- Market timing and opportunity
- Customer validation and demand
- Industry trends and dynamics

Please include relevant sources or links if the answer mentions specific companies, reports, or data.
"""

        elif is_founder_signal:
            prompt += """
**Founder Signal Focus:**
- Relevant experience and expertise
- Track record and achievements
- Team composition and roles
- Leadership capabilities
- Domain knowledge

Please include relevant sources or links if the answer mentions specific companies, roles, or achievements.
"""

        prompt += """
**Response Format (JSON):**
{
  "score": 0.85,
  "explanation": "Clear explanation of the scoring rationale",
  "sources": [
    {
      "title": "Source title",
      "url": "https://example.com",
      "type": "linkedin|crunchbase|news|report|other"
    }
  ],
  "key_strengths": ["strength1", "strength2"],
  "key_weaknesses": ["weakness1", "weakness2"],
  "confidence": 0.9
}

**Scoring Guidelines:**
- 0.9-1.0: Exceptional answer, clearly demonstrates strong capability
- 0.7-0.89: Good answer with solid evidence and reasoning
- 0.5-0.69: Average answer, some good points but lacks depth
- 0.3-0.49: Below average, concerning gaps or red flags
- 0.0-0.29: Poor answer, significant concerns or irrelevant

Score between 0.0 and 1.0. Be consistent and objective.
"""

        return prompt

    def _validate_ai_response(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and normalize AI response."""
        # Ensure required fields
        score = float(result.get("score", 0.0))
        score = max(0.0, min(1.0, score))  # Clamp to [0, 1]

        explanation = result.get("explanation", "No explanation provided")
        sources = result.get("sources", [])
        key_strengths = result.get("key_strengths", [])
        key_weaknesses = result.get("key_weaknesses", [])
        confidence = float(result.get("confidence", 0.8))

        # Validate sources format
        validated_sources = []
        for source in sources:
            if isinstance(source, dict) and "title" in source:
                validated_sources.append({
                    "title": source.get("title", ""),
                    "url": source.get("url", ""),
                    "type": source.get("type", "other"),
                })

        return {
            "score": score,
            "explanation": explanation,
            "sources": validated_sources,
            "key_strengths": key_strengths,
            "key_weaknesses": key_weaknesses,
            "confidence": confidence,
            "ai_generated": True,
        }

    async def _fallback_text_scoring(
        self, user_answer: str, good_reference: str, bad_reference: str
    ) -> Dict[str, Any]:
        """Fallback scoring when AI is not available."""
        # Simple keyword-based scoring
        user_lower = user_answer.lower()
        good_lower = good_reference.lower()
        bad_lower = bad_reference.lower()

        # Extract keywords
        good_keywords = set(re.findall(r"\b\w+\b", good_lower))
        bad_keywords = set(re.findall(r"\b\w+\b", bad_lower))
        user_keywords = set(re.findall(r"\b\w+\b", user_lower))

        # Calculate overlap
        good_overlap = len(user_keywords.intersection(good_keywords))
        bad_overlap = len(user_keywords.intersection(bad_keywords))

        # Simple scoring logic
        if good_overlap > bad_overlap:
            score = min(0.8, good_overlap / max(len(good_keywords), 1))
        else:
            score = max(0.2, 1.0 - (bad_overlap / max(len(bad_keywords), 1)))

        return {
            "score": score,
            "explanation": f"Fallback scoring based on keyword analysis. Good overlap: {good_overlap}, Bad overlap: {bad_overlap}",
            "sources": [],
            "key_strengths": [],
            "key_weaknesses": [],
            "confidence": 0.5,
            "ai_generated": False,
        }

    async def batch_score_texts(
        self, scoring_requests: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Score multiple text responses in batch."""
        results = []
        for request in scoring_requests:
            result = await self.score_text_response(**request)
            results.append(result)
        return results


# Global service instance
_text_scoring_service: Optional[AITextScoringService] = None


async def get_text_scoring_service() -> AITextScoringService:
    """Get the global text scoring service instance."""
    global _text_scoring_service
    if _text_scoring_service is None:
        _text_scoring_service = AITextScoringService()
    return _text_scoring_service
