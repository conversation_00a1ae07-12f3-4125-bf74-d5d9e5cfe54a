"use client"

import { useEffect, useState } from "react";
import Link from "next/link";
import { Plus, MoreHorizontal, Edit, Eye, Filter, Trash2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import { DashboardHeader } from "@/components/header";
import { DashboardShell } from "@/components/shell";
import { EmptyPlaceholder } from "@/components/empty-placeholder";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "@/components/ui/use-toast";
import { FormAPI } from "@/lib/api/form-api";
import { Form } from "@/lib/types/form";
import { useAuth } from "@/lib/auth-context";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
// import { FormActionDropdown } from "@/components/core/form-builder/form-action-dropdown";

export default function FormsPage() {
  const [forms, setForms] = useState<Form[]>([]);
  const [loading, setLoading] = useState(true);
  const { isAuthenticated } = useAuth();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [formToDelete, setFormToDelete] = useState<Form | null>(null);
  const [deleting, setDeleting] = useState(false);
  const [isNavigatingToCreate, setIsNavigatingToCreate] = useState(false);

  // Fetch forms from the API
  useEffect(() => {
    const fetchForms = async () => {
      try {
        console.log("Fetching forms from API");
        setLoading(true);
        const formsList = await FormAPI.listForms();
        console.log("Forms fetched:", formsList);

        // Ensure we always have an array
        if (Array.isArray(formsList)) {
          setForms(formsList);
        } else {
          console.warn("API returned non-array response:", formsList);
          setForms([]);
        }
      } catch (error) {
        console.error("Error fetching forms:", error);
        setForms([]); // Reset to empty array on error
        toast({
          title: "Error",
          description: "Failed to load forms. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated) {
      fetchForms();
    }
  }, [isAuthenticated]);

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Forms"
        text="Create and manage your forms"
      >
        <Link href="/forms/new">
          <Button 
            disabled={isNavigatingToCreate}
            onClick={() => {
              if (!isNavigatingToCreate) {
                setIsNavigatingToCreate(true);
                // Reset after a delay to allow navigation
                setTimeout(() => setIsNavigatingToCreate(false), 2000);
              }
            }}
          >
            <Plus className="size-4" />
            {isNavigatingToCreate && <span className="ml-2">Creating...</span>}
          </Button>
        </Link>
      </DashboardHeader>

      <ConfirmDialog
        open={deleteDialogOpen}
        title="Delete Form"
        description={`Are you sure you want to delete the form "${formToDelete?.name || ''}"? This action cannot be undone.`}
        onCancel={() => { setDeleteDialogOpen(false); setFormToDelete(null); }}
        onConfirm={async () => {
          if (!formToDelete) return;
          setDeleting(true);
          try {
            const id = String(formToDelete._id || formToDelete.id);
            await FormAPI.deleteForm(id);
            // Refetch forms from API to ensure UI is in sync and not stuck
            const formsList = await FormAPI.listForms();
            setForms(Array.isArray(formsList) ? formsList : []);
            toast({
              title: 'Form deleted',
              description: 'The form has been deleted successfully.'
            });
          } catch (error) {
            toast({
              title: 'Error',
              description: 'Failed to delete form. Please try again.',
              variant: 'destructive'
            });
          } finally {
            setDeleting(false);
            setDeleteDialogOpen(false);
            setFormToDelete(null);
          }
        }}
        loading={deleting}
      />

      <div>
        {loading ? (
          // Loading skeleton
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="overflow-hidden">
                <CardHeader className="gap-2">
                  <Skeleton className="h-5 w-1/2" />
                  <Skeleton className="h-4 w-full" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-full" />
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Skeleton className="h-10 w-24" />
                  <Skeleton className="h-10 w-24" />
                </CardFooter>
              </Card>
            ))}
          </div>
        ) : !forms || forms.length === 0 ? (
          <EmptyPlaceholder>
            <EmptyPlaceholder.Icon name="post" />
            <EmptyPlaceholder.Title>No forms created</EmptyPlaceholder.Title>
            <EmptyPlaceholder.Description>
              You don&apos;t have any forms yet. Start creating forms to collect data from your users.
            </EmptyPlaceholder.Description>
            <Link href="/forms/new">
              <Button 
                variant="outline"
                disabled={isNavigatingToCreate}
                onClick={() => {
                  if (!isNavigatingToCreate) {
                    setIsNavigatingToCreate(true);
                    setTimeout(() => setIsNavigatingToCreate(false), 2000);
                  }
                }}
              >
                Create your first form
                {isNavigatingToCreate && <span className="ml-2">...</span>}
              </Button>
            </Link>
          </EmptyPlaceholder>
        ) : (
          <div className="grid auto-rows-fr gap-8 md:grid-cols-2 lg:grid-cols-3">
            {forms && Array.isArray(forms) && forms.map((form) => {
              // Ensure we have a valid ID for the form
              const formId = form._id || form.id;

              // Skip forms without valid IDs
              if (!formId) {
                console.warn('Form without valid ID found:', form);
                return null;
              }

              return (
                <Card
                  key={formId}
                  className="flex min-h-[280px] h-full flex-col transition-all duration-200 hover:shadow-md"
                >
                  <div className="flex flex-col justify-between h-full p-6">
                    {/* Header Section */}
                    <div className="space-y-4">
                      {/* Title and Menu */}
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-xl leading-tight pr-4">
                          {form.name || "Untitled Form"}
                        </CardTitle>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="shrink-0">
                              <MoreHorizontal className="size-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-48">
                            <DropdownMenuItem asChild>
                              <Link href={`/forms/${formId}`} className="flex w-full">
                                <Edit className="mr-2 size-4 text-muted-foreground" />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/preview/${formId}`} className="flex w-full">
                                <Eye className="mr-2 size-4 text-muted-foreground" />
                                Preview
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => {
                                setFormToDelete(form);
                                setDeleteDialogOpen(true);
                              }}
                              className="flex w-full text-destructive"
                            >
                              <Trash2 className="mr-2 size-4 text-destructive" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      {/* Status and Updated Row */}
                                              <div className="flex items-center justify-between">
                          <span className={form.is_active
                            ? 'inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-blue-50 to-cyan-50 text-blue-700 text-xs font-medium shadow-sm border border-blue-200'
                            : 'inline-flex items-center px-3 py-1 rounded-full bg-gray-100 text-gray-500 text-xs font-medium border border-gray-200'}>
                            {form.is_active ? 'Active' : 'Draft'}
                          </span>
                        <span className="text-xs text-muted-foreground">
                          Updated {form.updated_at ? new Date(form.updated_at * 1000).toLocaleDateString() : 'N/A'}
                        </span>
                      </div>

                      {/* Description */}
                      <CardDescription className="line-clamp-2 text-sm leading-relaxed">
                        {form.description || "No description provided"}
                      </CardDescription>

                      {/* Metadata Section */}
                      <div className="space-y-3">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Sections:</span>
                          <span className="inline-flex items-center justify-center px-2 py-1 text-xs bg-muted rounded-full font-medium">
                            {form.sections?.length || 0} sections
                          </span>
                        </div>

                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Status:</span>
                          <span className={`font-medium ${form.is_active ? 'text-gray-900' : 'text-gray-500'}`}>
                            {form.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Bottom Section - Edit Button */}
                    <div className="pt-4">
                      <Link href={`/forms/${formId}`}>
                        <Button variant="outline" size="sm" className="w-full">
                          <Edit className="mr-2 size-4" />
                          Edit Form
                        </Button>
                      </Link>
                    </div>
                  </div>
                </Card>
              );
            }).filter(Boolean)}
          </div>
        )}
      </div>
    </DashboardShell>
  );
}
