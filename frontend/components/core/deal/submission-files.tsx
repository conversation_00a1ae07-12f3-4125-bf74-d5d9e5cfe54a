"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { FileDisplay, FileListDisplay } from '@/components/core/form-share/file-display';
import { Badge } from '@/components/ui/badge';

interface SubmissionFilesProps {
  submission: {
    id: string;
    answers: Record<string, any>;
    status: 'draft' | 'submitted';
  };
  form: {
    sections: Array<{
      _id: string;
      title: string;
      questions: Array<{
        _id: string;
        label: string;
        type: string;
        required?: boolean;
      }>;
    }>;
  };
  accessToken?: string;
  mode?: 'edit' | 'view';
}

export function SubmissionFiles({
  submission,
  form,
  accessToken,
  mode = 'view'
}: SubmissionFilesProps) {
  // Extract all file questions and their answers
  const fileQuestions = form.sections.flatMap(section =>
    section.questions
      .filter(question => question.type === 'file')
      .map(question => ({
        ...question,
        sectionTitle: section.title,
        answer: submission.answers[question._id]
      }))
  );

  // Filter to only questions that have file answers
  const questionsWithFiles = fileQuestions.filter(q => 
    q.answer && typeof q.answer === 'object' && q.answer.file
  );

  if (questionsWithFiles.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span>Documents</span>
            <Badge variant="secondary">0 files</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm italic text-gray-500">
            No documents have been uploaded for this submission.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <span>Documents</span>
          <Badge variant="secondary">{questionsWithFiles.length} files</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {questionsWithFiles.map((question) => (
          <div key={question._id} className="space-y-3">
            {/* Question Label */}
            <div className="border-b pb-2">
              <h4 className="font-medium text-gray-900">
                {question.label}
                {question.required && (
                  <Badge variant="destructive" className="ml-2 text-xs">
                    Required
                  </Badge>
                )}
              </h4>
              <p className="text-sm text-gray-500">
                {question.sectionTitle}
              </p>
            </div>
            
            {/* File Display */}
            <FileDisplay
              fileAnswer={question.answer}
              accessToken={accessToken}
              mode={mode}
            />
          </div>
        ))}
      </CardContent>
    </Card>
  );
}

interface SubmissionFilesGridProps {
  submissions: Array<{
    id: string;
    answers: Record<string, any>;
    status: 'draft' | 'submitted';
    company_name?: string;
    created_at?: string;
  }>;
  form: {
    sections: Array<{
      _id: string;
      title: string;
      questions: Array<{
        _id: string;
        label: string;
        type: string;
        required?: boolean;
      }>;
    }>;
  };
  accessToken?: string;
}

export function SubmissionFilesGrid({
  submissions,
  form,
  accessToken
}: SubmissionFilesGridProps) {
  // Get all file questions
  const fileQuestions = form.sections.flatMap(section =>
    section.questions.filter(question => question.type === 'file')
  );

  if (fileQuestions.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-center text-sm italic text-gray-500">
            This form does not contain any file upload questions.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {submissions.map((submission) => {
        // Get files for this submission
        const submissionFiles = fileQuestions
          .map(question => ({
            question,
            answer: submission.answers[question._id]
          }))
          .filter(item => 
            item.answer && typeof item.answer === 'object' && item.answer.file
          );

        if (submissionFiles.length === 0) {
          return null; // Skip submissions with no files
        }

        return (
          <Card key={submission.id}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div>
                  <span>{submission.company_name || `Submission ${submission.id.slice(-6)}`}</span>
                  <Badge 
                    variant={submission.status === 'submitted' ? 'default' : 'secondary'}
                    className="ml-2"
                  >
                    {submission.status}
                  </Badge>
                </div>
                <Badge variant="outline">
                  {submissionFiles.length} files
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {submissionFiles.map(({ question, answer }) => (
                  <div key={question._id} className="space-y-2">
                    <h5 className="truncate text-sm font-medium text-gray-700">
                      {question.label}
                    </h5>
                    <FileDisplay
                      fileAnswer={answer}
                      accessToken={accessToken}
                      mode="view"
                      className="text-xs"
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}

// Helper component for displaying file statistics
export function FileStatistics({
  submissions,
  form
}: {
  submissions: Array<{ answers: Record<string, any> }>;
  form: { sections: Array<{ questions: Array<{ _id: string; type: string }> }> };
}) {
  const fileQuestions = form.sections.flatMap(section =>
    section.questions.filter(question => question.type === 'file')
  );

  const totalFiles = submissions.reduce((count, submission) => {
    return count + fileQuestions.filter(question => {
      const answer = submission.answers[question._id];
      return answer && typeof answer === 'object' && answer.file;
    }).length;
  }, 0);

  const submissionsWithFiles = submissions.filter(submission =>
    fileQuestions.some(question => {
      const answer = submission.answers[question._id];
      return answer && typeof answer === 'object' && answer.file;
    })
  ).length;

  return (
    <div className="grid grid-cols-3 gap-4">
      <Card>
        <CardContent className="p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{totalFiles}</div>
          <div className="text-sm text-gray-500">Total Files</div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-4 text-center">
          <div className="text-2xl font-bold text-green-600">{submissionsWithFiles}</div>
          <div className="text-sm text-gray-500">Submissions with Files</div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-4 text-center">
          <div className="text-2xl font-bold text-purple-600">{fileQuestions.length}</div>
          <div className="text-sm text-gray-500">File Questions</div>
        </CardContent>
      </Card>
    </div>
  );
}
