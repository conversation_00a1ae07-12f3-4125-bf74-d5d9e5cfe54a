import { TourStep } from "./product-tour"

// Dashboard Tour - Main overview of the platform
export const dashboardTourSteps: TourStep[] = [
  {
    id: "welcome",
    title: "Welcome to TractionX",
    content: "Let's take a quick tour of your investment decision support system. This will help you understand how everything works together.",
    target: "[data-tour='dashboard-header']",
    placement: "center",
    waitForElement: true
  },
  {
    id: "sidebar-navigation",
    title: "Navigation Sidebar",
    content: "Your main navigation hub. Access Deals, Forms, Theses, and Settings from here. The sidebar adapts to your screen size.",
    target: "[data-tour='sidebar']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "deals-section",
    title: "Deals Overview",
    content: "This is where all your investment opportunities live. You can view, filter, and analyze deals based on your investment thesis.",
    target: "[data-tour='deals-section']",
    placement: "top",
    waitForElement: true
  },
  {
    id: "quick-stats",
    title: "Quick Statistics",
    content: "Get an instant overview of your deal pipeline, thesis matches, and recent activity. These metrics update in real-time.",
    target: "[data-tour='quick-stats']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "recent-activity",
    title: "Recent Activity",
    content: "Stay updated with the latest submissions, scoring updates, and team actions. Never miss important deal developments.",
    target: "[data-tour='recent-activity']",
    placement: "left",
    waitForElement: true
  }
]

// Deals Tour - Deep dive into deal management
export const dealsTourSteps: TourStep[] = [
  {
    id: "deals-header",
    title: "Deal Management Hub",
    content: "This is your central command for managing investment opportunities. Filter, sort, and analyze deals with AI-powered insights.",
    target: "[data-tour='deals-header']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "deal-filters",
    title: "Smart Filters",
    content: "Filter deals by stage, sector, thesis match score, or any custom criteria. Save your favorite filter combinations.",
    target: "[data-tour='deal-filters']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "deal-card",
    title: "Deal Cards",
    content: "Each deal shows key metrics, thesis match percentage, and AI-generated insights. Click to dive deeper into analysis.",
    target: "[data-tour='deal-card']:first-child",
    placement: "right",
    waitForElement: true
  },
  {
    id: "thesis-score",
    title: "Thesis Match Score",
    content: "See how well each deal aligns with your investment thesis. Scores are calculated using AI based on your configured preferences.",
    target: "[data-tour='thesis-score']",
    placement: "top",
    waitForElement: true
  },
  {
    id: "deal-actions",
    title: "Quick Actions",
    content: "Take immediate action on deals - schedule meetings, add notes, change status, or share with team members.",
    target: "[data-tour='deal-actions']",
    placement: "left",
    waitForElement: true
  }
]

// Forms Tour - Understanding form creation and management
export const formsTourSteps: TourStep[] = [
  {
    id: "forms-overview",
    title: "Form Builder",
    content: "Create custom forms to collect structured data from startups. Standardize your deal intake process with intelligent form builders.",
    target: "[data-tour='forms-header']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "create-form",
    title: "Create New Form",
    content: "Start building a new form to collect specific information from startups. Choose from templates or build from scratch.",
    target: "[data-tour='create-form-btn']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "form-templates",
    title: "Form Templates",
    content: "Use pre-built templates for common scenarios like pitch decks, due diligence, or initial screening. Save time with proven formats.",
    target: "[data-tour='form-templates']",
    placement: "top",
    waitForElement: true
  },
  {
    id: "form-sections",
    title: "Form Sections",
    content: "Organize your forms into logical sections. Add conditional logic to show/hide sections based on previous answers.",
    target: "[data-tour='form-sections']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "form-sharing",
    title: "Share Forms",
    content: "Generate secure links to share forms with startups. Track submission status and get notified when forms are completed.",
    target: "[data-tour='form-sharing']",
    placement: "left",
    waitForElement: true
  }
]

// Theses Tour - Investment thesis and scoring
export const thesesTourSteps: TourStep[] = [
  {
    id: "theses-overview",
    title: "Investment Theses",
    content: "Define your investment criteria and scoring rules. Let AI evaluate deals against your thesis automatically with detailed analysis.",
    target: "[data-tour='theses-header']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "create-thesis",
    title: "Create Thesis",
    content: "Build a new investment thesis with specific criteria, scoring rules, and matching logic. Connect it to forms for automatic evaluation.",
    target: "[data-tour='create-thesis-btn']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "thesis-criteria",
    title: "Scoring Criteria",
    content: "Set up detailed scoring rules based on geography, sector, stage, business model, and custom criteria from your forms.",
    target: "[data-tour='thesis-criteria']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "thesis-weights",
    title: "Criteria Weights",
    content: "Assign importance weights to different criteria. The AI will use these to calculate overall thesis match scores for deals.",
    target: "[data-tour='thesis-weights']",
    placement: "left",
    waitForElement: true
  },
  {
    id: "thesis-preview",
    title: "Live Preview",
    content: "See how your thesis would score existing deals in real-time. Adjust criteria and weights to fine-tune your scoring model.",
    target: "[data-tour='thesis-preview']",
    placement: "top",
    waitForElement: true
  }
]

// Settings Tour - Organization and user management
export const settingsTourSteps: TourStep[] = [
  {
    id: "settings-overview",
    title: "Organization Settings",
    content: "Manage your organization profile, team members, integrations, and platform preferences from this central hub.",
    target: "[data-tour='settings-header']",
    placement: "bottom",
    waitForElement: true
  },
  {
    id: "org-profile",
    title: "Organization Profile",
    content: "Update your firm's information, logo, and public profile. This information appears on shared forms and public pages.",
    target: "[data-tour='org-profile']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "team-members",
    title: "Team Management",
    content: "Invite team members, assign roles, and manage permissions. Control who can access different parts of the platform.",
    target: "[data-tour='team-members']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "org-thesis-settings",
    title: "Organization Thesis",
    content: "Configure your organization-wide investment preferences. These serve as defaults for new theses and deal filtering.",
    target: "[data-tour='org-thesis']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "integrations",
    title: "Integrations",
    content: "Connect TractionX with your existing tools like CRM, email, calendar, and data providers for seamless workflow.",
    target: "[data-tour='integrations']",
    placement: "left",
    waitForElement: true
  }
]

// Complete workflow tour - End-to-end process
export const workflowTourSteps: TourStep[] = [
  {
    id: "workflow-intro",
    title: "Complete Workflow",
    content: "Let's walk through the complete TractionX workflow: from creating forms to evaluating deals with your investment thesis.",
    target: "[data-tour='dashboard-header']",
    placement: "center",
    waitForElement: true
  },
  {
    id: "step1-forms",
    title: "Step 1: Create Forms",
    content: "Start by creating forms to collect structured data from startups. This standardizes your deal intake process.",
    target: "[data-tour='nav-forms']",
    placement: "right",
    waitForElement: true,
    onBeforeStep: () => {
      // Could navigate to forms page
      console.log("Navigating to forms section")
    }
  },
  {
    id: "step2-theses",
    title: "Step 2: Define Theses",
    content: "Create investment theses that define your criteria and scoring rules. Connect them to your forms for automatic evaluation.",
    target: "[data-tour='nav-theses']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "step3-deals",
    title: "Step 3: Evaluate Deals",
    content: "As startups submit forms, deals are automatically created and scored against your theses. Review and take action on opportunities.",
    target: "[data-tour='nav-deals']",
    placement: "right",
    waitForElement: true
  },
  {
    id: "workflow-complete",
    title: "Workflow Complete",
    content: "That's the complete TractionX workflow! Forms collect data, theses provide scoring, and deals give you actionable insights.",
    target: "[data-tour='dashboard-header']",
    placement: "center",
    waitForElement: true
  }
]

// Export all tour configurations
export const tourConfigurations = {
  dashboard: {
    id: "dashboard-tour",
    name: "Dashboard Overview",
    description: "Get familiar with your TractionX dashboard",
    steps: dashboardTourSteps
  },
  deals: {
    id: "deals-tour", 
    name: "Deal Management",
    description: "Learn how to manage and analyze investment opportunities",
    steps: dealsTourSteps
  },
  forms: {
    id: "forms-tour",
    name: "Form Builder",
    description: "Create and manage data collection forms",
    steps: formsTourSteps
  },
  theses: {
    id: "theses-tour",
    name: "Investment Theses", 
    description: "Define scoring criteria and investment logic",
    steps: thesesTourSteps
  },
  settings: {
    id: "settings-tour",
    name: "Organization Settings",
    description: "Manage your organization and team",
    steps: settingsTourSteps
  },
  workflow: {
    id: "workflow-tour",
    name: "Complete Workflow",
    description: "End-to-end TractionX process walkthrough",
    steps: workflowTourSteps
  }
}

export type TourType = keyof typeof tourConfigurations
