"""
Job Tracking API Routes

This module defines the API routes for the job tracking system.
"""

from typing import List, Optional

from fastapi import Depends, HTTPException, Path, Query, status

from app.api.base import BaseAPIRouter
from app.core.logging import get_logger
from app.models.job import EntityType, JobStatus, JobType, TrackedJob
from app.services.factory import get_job_service
from app.services.job.interfaces import JobServiceInterface

router = BaseAPIRouter(
    prefix="/jobs",
    tags=["jobs"],
    responses={
        404: {"description": "Not found"},
        403: {"description": "Forbidden"},
        400: {"description": "Bad request"},
        500: {"description": "Internal server error"},
    },
)

logger = get_logger(__name__)


@router.get("/{job_id}", response_model=TrackedJob)
async def get_job(
    job_id: str = Path(..., description="Job ID"),
    job_service: JobServiceInterface = Depends(get_job_service),
) -> TrackedJob:
    """
    Get a job by ID.

    Args:
        job_id: Job ID
        job_service: Job service instance

    Returns:
        The job if found
    """
    try:
        job = await job_service.get_job(job_id)
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Job not found"
            )

        return job
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error retrieving job: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve job: {str(e)}",
        )


@router.get("/entity/{entity_type}/{entity_id}", response_model=List[TrackedJob])
async def get_entity_jobs(
    entity_type: str = Path(..., description="Entity type"),
    entity_id: str = Path(..., description="Entity ID"),
    status: Optional[str] = Query(None, description="Filter by job status"),
    job_type: Optional[str] = Query(None, description="Filter by job type"),
    job_service: JobServiceInterface = Depends(get_job_service),
) -> List[TrackedJob]:
    """
    Get all jobs for an entity.

    Args:
        entity_type: Entity type
        entity_id: Entity ID
        status: Optional filter by job status
        job_type: Optional filter by job type
        job_service: Job service instance

    Returns:
        List of jobs for the entity
    """
    try:
        # Validate entity type
        try:
            entity_type_enum = EntityType(entity_type)
            entity_type = entity_type_enum
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,  # type: ignore
                detail=f"Invalid entity type: {entity_type}",
            )

        # Validate status if provided
        if status:
            try:
                status_enum = JobStatus(status)
                status = status_enum
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,  # type: ignore
                    detail=f"Invalid job status: {status}",
                )

        # Validate job type if provided
        if job_type:
            try:
                job_type_enum = JobType(job_type)
                job_type = job_type_enum
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,  # type: ignore
                    detail=f"Invalid job type: {job_type}",
                )

        jobs = await job_service.get_entity_jobs(
            entity_type=entity_type,
            entity_id=entity_id,
            status=status,
            job_type=job_type,
        )

        return jobs
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error retrieving entity jobs: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,  # type: ignore
            detail=f"Failed to retrieve entity jobs: {str(e)}",
        )
