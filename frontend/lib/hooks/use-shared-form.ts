import { useState, useEffect } from 'react';
import { SharedFormsAPI, SharedFormData } from '@/lib/api/shared-forms';

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

export function useSharedForm(token: string) {
  const [formData, setFormData] = useState<SharedFormData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (!token) return;

    const fetchFormData = async () => {
      try {
        const data = await SharedFormsAPI.getFormDetails(token);
        setFormData(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchFormData();
  }, [token]);

  const submitForm = async (answers: Record<string, any>) => {
    if (!formData) throw new Error('Form data not loaded');

    setSubmitting(true);
    try {
      return await SharedFormsAPI.submitForm(token, answers);
    } finally {
      setSubmitting(false);
    }
  };

  return {
    formData,
    loading,
    error,
    submitForm,
    submitting,
  };
}
