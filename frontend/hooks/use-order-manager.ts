import { useState, useCallback, useRef } from 'react';
import { toast } from '@/components/ui/use-toast';
import FormAPI from '@/lib/api/form-api';
import { Section, Question } from '@/lib/types/form';

interface OrderManagerOptions<T> {
  items: T[];
  onSave: (items: T[]) => Promise<void>;
  getItemId: (item: T) => string;
  updateOrder: (item: T, order: number) => T;
  onError?: (error: any) => void;
  resourceType: 'section' | 'question';
  parentId?: string; // section_id for questions, form_id for sections
}

interface OrderManagerState<T> {
  items: T[];
  hasUnsavedChanges: boolean;
  isSaving: boolean;
  saveOrder: () => Promise<void>;
  undoLastChange: () => void;
  updateItems: (newItems: T[]) => void;
}

export function useOrderManager<T>({
  items: initialItems,
  onSave,
  getItemId,
  updateOrder,
  onError,
  resourceType,
  parentId,
}: OrderManagerOptions<T>): OrderManagerState<T> {
  // State
  const [items, setItems] = useState(initialItems);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // History stack for undo functionality
  const historyStack = useRef<T[][]>([]);
  const maxHistorySize = 10;

  // Update items with order
  const updateItems = useCallback((newItems: T[]) => {
    // Add current state to history before updating
    historyStack.current = [
      [...items],
      ...historyStack.current.slice(0, maxHistorySize - 1),
    ];

    // Update items with sequential order
    const orderedItems = newItems.map((item, index) => updateOrder(item, index));
    setItems(orderedItems);
    setHasUnsavedChanges(true);
  }, [items, updateOrder]);

  // Save order changes
  const saveOrder = useCallback(async () => {
    if (!hasUnsavedChanges || isSaving) return;

    setIsSaving(true);
    try {
      // Use the onSave callback which should handle the reorder API call
      await onSave(items);

      setHasUnsavedChanges(false);
      // Clear history after successful save
      historyStack.current = [];
      toast({
        title: "Order saved",
        description: "The new order has been saved successfully.",
      });
    } catch (error) {
      console.error('Error saving order:', error);
      onError?.(error);
      toast({
        title: "Error saving order",
        description: "There was an error saving the new order. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  }, [items, hasUnsavedChanges, isSaving, onSave, onError]);

  // Undo last change
  const undoLastChange = useCallback(() => {
    if (historyStack.current.length === 0 || isSaving) return;

    const previousState = historyStack.current[0];
    historyStack.current = historyStack.current.slice(1);
    setItems(previousState);
    setHasUnsavedChanges(true);
  }, [isSaving]);

  return {
    items,
    hasUnsavedChanges,
    isSaving,
    saveOrder,
    undoLastChange,
    updateItems,
  };
}