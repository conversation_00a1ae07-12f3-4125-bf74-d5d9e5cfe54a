"use client"

import * as React from "react"
import {
  Root as Tabs,
  List as TabsList,
  Trigger as TabsTrigger,
  Content as TabsContent,
} from "@radix-ui/react-tabs"
import { cn } from "@/lib/utils"

const TabsListComponent = React.forwardRef<
  React.ElementRef<typeof TabsList>,
  React.ComponentPropsWithoutRef<typeof TabsList>
>(({ className, ...props }, ref) => (
  <TabsList
    ref={ref}
    className={cn(
      // Container styling
      "inline-flex h-10 items-center justify-center",
      "rounded-lg bg-gray-100/30 p-1",
      "border border-gray-100",
      // Mobile responsiveness
      "w-full sm:w-auto",
      // Remove any backdrop effects
      "backdrop-blur-none",
      className
    )}
    {...props}
  />
))
TabsListComponent.displayName = "TabsList"

const TabsTriggerComponent = React.forwardRef<
  React.ElementRef<typeof TabsTrigger>,
  React.ComponentPropsWithoutRef<typeof TabsTrigger>
>(({ className, ...props }, ref) => (
  <TabsTrigger
    ref={ref}
    className={cn(
      // Base styles
      "inline-flex items-center justify-center whitespace-nowrap",
      "rounded-md px-3.5 py-2 text-sm font-medium",
      "transition-all duration-150 ease-in-out",
      // Remove focus ring styles
      "focus:outline-none focus:ring-0",
      "disabled:pointer-events-none disabled:opacity-50",
      // Active state - clean and minimal
      "data-[state=active]:bg-white",
      "data-[state=active]:text-gray-900",
      "data-[state=active]:shadow-[0_1px_2px_rgba(0,0,0,0.04)]",
      // Inactive state - subtle and clean
      "data-[state=inactive]:text-gray-600",
      "data-[state=inactive]:hover:bg-gray-50",
      "data-[state=inactive]:hover:text-gray-900",
      className
    )}
    {...props}
  />
))
TabsTriggerComponent.displayName = "TabsTrigger"

const TabsContentComponent = React.forwardRef<
  React.ElementRef<typeof TabsContent>,
  React.ComponentPropsWithoutRef<typeof TabsContent>
>(({ className, ...props }, ref) => (
  <TabsContent
    ref={ref}
    className={cn(
      // Content spacing
      "mt-4",
      // Remove focus styles
      "focus:outline-none",
      // Smooth fade transition
      "transition-opacity duration-200",
      "data-[state=inactive]:opacity-0",
      "data-[state=active]:opacity-100",
      className
    )}
    {...props}
  />
))
TabsContentComponent.displayName = "TabsContent"

export { 
  Tabs, 
  TabsListComponent as TabsList, 
  TabsTriggerComponent as TabsTrigger, 
  TabsContentComponent as TabsContent 
}
