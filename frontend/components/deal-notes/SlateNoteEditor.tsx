"use client"

/**
 * SlateNoteEditor
 *
 * Rich text editor for deal notes with Slack-style mentions, using Slate.js.
 * Handles mention autocomplete, insertion, and non-editable mention tokens.
 */

import React, { useCallback, useMemo, useState } from 'react';
import {
  createEditor,
  Descendant,
  Editor,
  Transforms,
  Range,
  Element as SlateElement,
  Text,
} from 'slate';
import {
  Slate,
  Editable,
  withReact,
  ReactEditor,
  RenderElementProps,
} from 'slate-react';
import { withHistory } from 'slate-history';
import { Button } from '@/components/ui/button';
import { OrgUser, NoteContent, CustomElement } from '@/lib/types/deal-notes';
import { useMentionPlugin } from './utils/useMentionPlugin';
import { renderNode } from './renderers/renderNode';
// @ts-ignore // Suppress module not found error for MentionDropdown
import { MentionDropdown } from './MentionDropdown';
import { Skeleton } from '@/components/ui/skeleton';
import { Editor as SlateEditor, BaseEditor } from 'slate';
import { HistoryEditor } from 'slate-history';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Pin, List, ListOrdered } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

interface SlateNoteEditorProps {
  orgUsers: OrgUser[];
  onSubmit: (content: NoteContent, pinned: boolean) => Promise<void>;
  className?: string;
  currentUser?: { id: string; name: string; avatar_url?: string };
}

// Ensure initialValue is Descendant[]
const initialValue: NoteContent = [
  {
    type: 'paragraph',
    children: [{ text: '' }],
  },
];

// Utility to deeply ensure a valid Slate value
function ensureValidSlateValue(val: any): NoteContent {
  // Only valid empty state is a single empty paragraph
  const EMPTY: NoteContent = [{ type: 'paragraph', children: [{ text: '' }] }];
  if (!Array.isArray(val) || val.length === 0) return EMPTY;
  // Map and repair each block
  const validBlocks = val.map((block: any) => {
    if (!block || typeof block !== 'object' || !('type' in block)) {
      return { type: 'paragraph', children: [{ text: '' }] };
    }
    // List blocks
    if (block.type === 'bulleted-list' || block.type === 'numbered-list') {
      let children = Array.isArray(block.children)
        ? block.children.map((item: any) => {
            if (item && typeof item === 'object' && item.type === 'list-item') {
              let liChildren = Array.isArray(item.children)
                ? item.children.map((child: any) => {
                    if (typeof child === 'object' && 'text' in child) {
                      return { text: child.text || '' };
                    }
                    if (typeof child === 'object' && 'type' in child && child.type === 'mention') {
                      return child;
                    }
                    return { text: '' };
                  })
                : [{ text: '' }];
              if (liChildren.length === 0) liChildren = [{ text: '' }];
              return { type: 'list-item', children: liChildren };
            }
            // Coerce any non-list-item to a valid list-item
            return { type: 'list-item', children: [{ text: '' }] };
          })
        : [{ type: 'list-item', children: [{ text: '' }] }];
      if (children.length === 0) children = [{ type: 'list-item', children: [{ text: '' }] }];
      return { type: block.type, children };
    }
    // List-item
    if (block.type === 'list-item') {
      let children = Array.isArray(block.children)
        ? block.children.map((child: any) => {
            if (typeof child === 'object' && 'text' in child) {
              return { text: child.text || '' };
            }
            if (typeof child === 'object' && 'type' in child && child.type === 'mention') {
              return child;
            }
            return { text: '' };
          })
        : [{ text: '' }];
      if (children.length === 0) children = [{ text: '' }];
      return { type: 'list-item', children };
    }
    // Paragraph
    if (block.type === 'paragraph') {
      let children = Array.isArray(block.children)
        ? block.children.map((child: any) => {
            if (typeof child === 'object' && 'text' in child) {
              return { text: child.text || '' };
            }
            if (typeof child === 'object' && 'type' in child && child.type === 'mention') {
              return child;
            }
            return { text: '' };
          })
        : [{ text: '' }];
      if (children.length === 0) children = [{ text: '' }];
      return { type: 'paragraph', children };
    }
    // Fallback
    return { type: 'paragraph', children: [{ text: '' }] };
  });
  // Remove any blocks with no children
  const filtered = validBlocks.filter(
    block => block && Array.isArray(block.children) && block.children.length > 0
  );
  // If all blocks are empty, return EMPTY
  const allEmpty =
    filtered.length === 0 ||
    filtered.every(
      block =>
        !block.children ||
        block.children.length === 0 ||
        block.children.every(
          (child: any) =>
            (typeof child === 'object' && 'text' in child && child.text.trim() === '')
        )
    );
  if (allEmpty) return EMPTY;
  return filtered as NoteContent;
}

export function SlateNoteEditor({ orgUsers, onSubmit, className, currentUser }: SlateNoteEditorProps) {
  const [value, setValue] = useState<NoteContent>(initialValue);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [pinned, setPinned] = useState(false);
  const [listType, setListType] = useState<'paragraph' | 'bulleted-list' | 'numbered-list'>('paragraph');
  // Track focus for bullet visibility
  const [isFocused, setIsFocused] = useState(false);
  // Add a local state key for force-resetting the Slate editor
  const [editorKey, setEditorKey] = useState(0);

  const editor = useMemo(
    () => withMentionSupport(withHistory(withReact(createEditor()))),
    []
  );

  // Mention plugin state/logic
  const {
    target,
    search,
    index,
    setTarget,
    setSearch,
    setIndex,
    filteredUsers,
    onMentionSelect,
    onKeyDown: baseOnKeyDown,
  } = useMentionPlugin(editor, orgUsers);

  // Recursively sanitize Slate value
  function sanitizeBlock(block: any): any {
    if (!block || typeof block !== 'object' || !('type' in block)) {
      return { type: 'paragraph', children: [{ text: '' }] };
    }
    if (block.type === 'paragraph') {
      let children = Array.isArray(block.children)
        ? block.children.map(child => {
            if (typeof child === 'object' && 'text' in child) {
              return { text: child.text || '' };
            }
            if (typeof child === 'object' && 'type' in child && child.type === 'mention') {
              return child;
            }
            return { text: '' };
          })
        : [];
      if (children.length === 0) children = [{ text: '' }];
      return { type: 'paragraph', children };
    }
    if (block.type === 'bulleted-list' || block.type === 'numbered-list') {
      let children = Array.isArray(block.children)
        ? block.children.map(item => {
            if (item && typeof item === 'object' && item.type === 'list-item') {
              let liChildren = Array.isArray(item.children)
                ? item.children.map(child => {
                    if (typeof child === 'object' && 'text' in child) {
                      return { text: child.text || '' };
                    }
                    if (typeof child === 'object' && 'type' in child && child.type === 'mention') {
                      return child;
                    }
                    return { text: '' };
                  })
                : [];
              if (liChildren.length === 0) liChildren = [{ text: '' }];
              return { type: 'list-item', children: liChildren };
            }
            // Coerce any non-list-item to a valid list-item
            return { type: 'list-item', children: [{ text: '' }] };
          })
        : [];
      if (children.length === 0) children = [{ type: 'list-item', children: [{ text: '' }] }];
      return { type: block.type, children };
    }
    if (block.type === 'list-item') {
      let children = Array.isArray(block.children)
        ? block.children.map(child => {
            if (typeof child === 'object' && 'text' in child) {
              return { text: child.text || '' };
            }
            if (typeof child === 'object' && 'type' in child && child.type === 'mention') {
              return child;
            }
            return { text: '' };
          })
        : [];
      if (children.length === 0) children = [{ text: '' }];
      return { type: 'list-item', children };
    }
    // Fallback: coerce to paragraph
    return { type: 'paragraph', children: [{ text: '' }] };
  }

  const isEditorEmpty = useMemo(() => {
    if (!Array.isArray(value) || value.length === 0) return true;
    function blockHasContent(block: any): boolean {
      if (!block || typeof block !== 'object' || !('children' in block) || !Array.isArray(block.children)) return false;
      if (block.type === 'bulleted-list' || block.type === 'numbered-list') {
        return block.children.some((item: any) => blockHasContent(item));
      }
      return block.children.some((child: any) => {
        if (typeof child === 'object' && 'type' in child && child.type === 'mention') return true;
        if (typeof child === 'object' && 'text' in child && typeof child.text === 'string' && child.text.trim() !== '') return true;
        return false;
      });
    }
    return !value.some(blockHasContent);
  }, [value]);

  const handleChange = (newValue: Descendant[]) => {
    setValue(ensureValidSlateValue(newValue));
  };

  const handleSubmit = async () => {
    if (isSubmitting) return;
    // Validate: at least one paragraph with text or mention
    const hasContent = value.some(
      (block) =>
        block.type === 'paragraph' &&
        block.children.some((child) => {
          if ('type' in child && child.type === 'mention') return true;
          if ('text' in child && typeof child.text === 'string' && child.text.trim() !== '') return true;
          return false;
        })
    );
    if (!hasContent) return;
    setIsSubmitting(true);
    try {
      await onSubmit(value, pinned);
      setValue(ensureValidSlateValue(initialValue));
      setPinned(false);
      setEditorKey(k => k + 1); // Force-reset Slate
    } finally {
      setIsSubmitting(false);
    }
  };

  // Enhanced backspace: remove mention node if cursor is after it
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (
      event.key === 'Backspace' &&
      editor.selection &&
      Range.isCollapsed(editor.selection)
    ) {
      const mentionEntry = Editor.above(editor, {
        match: n => typeof n === 'object' && 'type' in n && n.type === 'mention',
        mode: 'lowest',
      });
      if (mentionEntry) {
        event.preventDefault();
        Transforms.removeNodes(editor, { at: mentionEntry[1] });
        return;
      }
    }
    baseOnKeyDown(event);
  };

  // Enhanced Post button enabled logic
  const hasContent = useMemo(() => {
    return value.some(block => {
      if (!block || typeof block !== 'object' || !('children' in block) || !Array.isArray(block.children)) return false;
      return block.children.some(child => {
        if (typeof child === 'object' && 'type' in child && child.type === 'mention') return true;
        if (typeof child === 'object' && 'text' in child && typeof child.text === 'string' && child.text.trim() !== '') return true;
        return false;
      });
    });
  }, [value]);

  // Improved list toggle logic: wrap in list-item
  const handleListToggle = (type: 'bulleted-list' | 'numbered-list') => {
    const { selection } = editor;
    if (!selection) return;
    const [blockEntry] = Array.from(
      Editor.nodes(editor, {
        match: n => typeof n === 'object' && 'type' in n && ['paragraph', 'bulleted-list', 'numbered-list', 'list-item'].includes((n as any).type),
        mode: 'lowest',
      })
    );
    if (blockEntry) {
      const [block, path] = blockEntry as [CustomElement, any];
      if ('type' in block && block.type === type) {
        // Unwrap from list
        Transforms.setNodes(editor, { type: 'paragraph' }, { at: path });
        Transforms.unwrapNodes(editor, {
          match: n => typeof n === 'object' && 'type' in n && (n.type === 'list-item'),
          split: true,
        });
        Transforms.unwrapNodes(editor, {
          match: n => typeof n === 'object' && 'type' in n && (n.type === 'bulleted-list' || n.type === 'numbered-list'),
          split: true,
        });
        setListType('paragraph');
      } else {
        // Wrap in list-item and list
        Transforms.setNodes(editor, { type: 'list-item' }, { at: path });
        Transforms.wrapNodes(editor, { type, children: [] } as any, { at: path });
        setListType(type);
      }
    }
  };

  // In render, always pass a valid value to <Slate>
  const safeValue = useMemo(() => ensureValidSlateValue(value), [value]);

  return (
    <div className={cn('relative flex items-start gap-2 min-h-[64px] p-0 bg-transparent rounded-xl', className)}>
      {/* Avatar restored */}
      <Avatar className="w-9 h-9 mt-1">
        {currentUser?.avatar_url ? (
          <AvatarImage src={currentUser.avatar_url} alt={currentUser.name} />
        ) : (
          <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xs font-medium">
            {currentUser?.name ? currentUser.name.charAt(0).toUpperCase() : ''}
          </AvatarFallback>
        )}
      </Avatar>
      <div className="flex-1 relative">
        <Slate key={editorKey} editor={editor} initialValue={safeValue} onChange={handleChange}>
          <div className="flex items-center min-h-[48px] rounded-xl bg-background/80 px-3 py-2">
            {/* Bullet icon removed as requested */}
            <Editable
              renderElement={renderNode}
              placeholder="Add a note... Use @ to mention teammates"
              spellCheck
              autoFocus
              onKeyDown={handleKeyDown}
              className="relative z-10 flex-1 min-h-[48px] px-0 py-0 text-sm bg-transparent outline-none focus:ring-0 focus:outline-none border-none appearance-none"
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
            />
            {target && filteredUsers.length > 0 && (
              <MentionDropdown
                users={filteredUsers}
                index={index}
                target={target}
                onSelect={onMentionSelect}
                onClose={() => setTarget(null)}
              />
            )}
            {/* Pin toggle button and list toggles remain unchanged */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    type="button"
                    className={cn('ml-2 p-1 rounded hover:bg-muted/30', pinned ? 'text-primary' : 'text-muted-foreground')}
                    aria-label={pinned ? 'Unpin note' : 'Pin note'}
                    onClick={() => setPinned(p => !p)}
                  >
                    <Pin fill={pinned ? 'currentColor' : 'none'} className="w-5 h-5" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>Pin this note</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            {/* <div className="flex items-center gap-1 ml-2">
              <button
                type="button"
                className={cn('p-1 rounded hover:bg-muted/30', listType === 'bulleted-list' && 'bg-muted/40 text-primary')}
                aria-label="Bulleted list"
                onClick={() => handleListToggle('bulleted-list')}
              >
                <List className="w-5 h-5" />
              </button>
              <button
                type="button"
                className={cn('p-1 rounded hover:bg-muted/30', listType === 'numbered-list' && 'bg-muted/40 text-primary')}
                aria-label="Numbered list"
                onClick={() => handleListToggle('numbered-list')}
              >
                <ListOrdered className="w-5 h-5" />
              </button>
            </div> */}
          </div>
          <div className="flex justify-end mt-2">
            <Button
              type="button"
              onClick={handleSubmit}
              disabled={isSubmitting || !hasContent}
              className="px-4"
            >
              {isSubmitting ? 'Posting...' : 'Post'}
            </Button>
          </div>
        </Slate>
      </div>
    </div>
  );
}

// Add mention support to Slate editor
function withMentionSupport(editor: BaseEditor & ReactEditor & HistoryEditor) {
  const { isInline, isVoid } = editor;
  editor.isInline = (element) =>
    element.type === 'mention' ? true : isInline(element);
  editor.isVoid = (element) =>
    element.type === 'mention' ? true : isVoid(element);
  return editor;
} 