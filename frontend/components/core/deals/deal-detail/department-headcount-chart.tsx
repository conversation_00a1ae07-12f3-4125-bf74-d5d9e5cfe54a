"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Users } from "lucide-react"

interface Department {
  department: string
  headCount: number
}

interface DepartmentHeadcountChartProps {
  departments: Department[]
}

export function DepartmentHeadcountChart({ departments }: DepartmentHeadcountChartProps) {
  // Sort departments by headcount (descending)
  const sortedDepartments = [...departments].sort((a, b) => b.headCount - a.headCount)
  
  // Calculate total headcount for percentage
  const totalHeadcount = departments.reduce((sum, dept) => sum + dept.headCount, 0)
  
  // Get max headcount for scaling
  const maxHeadcount = Math.max(...departments.map(d => d.headCount))

  if (!departments || departments.length === 0) {
    return null
  }

  return (
    <div className="animate-in fade-in duration-500">
      <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-md">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5 text-gray-600" />
            <CardTitle className="text-lg font-semibold text-gray-900">
              Team Structure
            </CardTitle>
            <span className="text-sm text-gray-500">
              {totalHeadcount} Total Employees
            </span>
          </div>
        </CardHeader>

        <CardContent className="space-y-3">
          {sortedDepartments.map((dept, index) => {
            const percentage = totalHeadcount > 0 ? (dept.headCount / totalHeadcount) * 100 : 0
            const barWidth = maxHeadcount > 0 ? (dept.headCount / maxHeadcount) * 100 : 0
            
            return (
              <div
                key={dept.department}
                className="animate-in fade-in slide-in-from-left duration-300"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium text-gray-700 capitalize">
                    {dept.department.toLowerCase().replace(/\s+/g, ' ')}
                  </span>
                  <div className="flex items-center gap-2">
                    <span className="text-gray-600">{dept.headCount}</span>
                    <span className="text-xs text-gray-500">
                      ({percentage.toFixed(1)}%)
                    </span>
                  </div>
                </div>
                
                <div className="relative h-2 bg-gray-100 rounded-full overflow-hidden mt-2">
                  <div
                    className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-800"
                    style={{ 
                      width: `${barWidth}%`,
                      animationDelay: `${index * 100}ms`
                    }}
                  />
                </div>
              </div>
            )
          })}
        </CardContent>
      </Card>
    </div>
  )
} 