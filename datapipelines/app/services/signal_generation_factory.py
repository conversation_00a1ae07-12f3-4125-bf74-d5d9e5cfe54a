"""
Factory for creating configured signal generation services.
"""

from typing import Optional

from app.clients.openai_client import OpenAIClient
from app.configs.logging import get_logger
from app.services.signal_generation import SignalGenerationService

logger = get_logger(__name__)


def create_signal_generation_service(
    api_key: Optional[str] = None, model: str = "gpt-4o"
) -> SignalGenerationService:
    """
    Create a configured signal generation service.

    Args:
        api_key: OpenAI API key (optional, will use settings if not provided)
        model: OpenAI model to use for signal generation

    Returns:
        Configured SignalGenerationService instance

    Raises:
        ValueError: If OpenAI API key is not available
    """
    try:
        # Create OpenAI client
        openai_client = OpenAIClient(api_key=api_key)

        # Create signal generation service
        service = SignalGenerationService(llm_client=openai_client)
        service.model = model

        logger.info(
            "Created signal generation service",
            model=model,
            has_api_key=bool(api_key or openai_client.api_key),
        )

        return service

    except ValueError as e:
        logger.error(f"Failed to create signal generation service: {e}")
        raise ValueError(f"Signal generation service creation failed: {e}")
    except Exception as e:
        logger.error(f"Unexpected error creating signal generation service: {e}")
        raise


def create_signal_generation_service_with_fallback(
    api_key: Optional[str] = None, model: str = "gpt-4o"
) -> Optional[SignalGenerationService]:
    """
    Create a signal generation service with graceful fallback.

    Args:
        api_key: OpenAI API key (optional, will use settings if not provided)
        model: OpenAI model to use for signal generation

    Returns:
        SignalGenerationService instance or None if creation fails
    """
    try:
        return create_signal_generation_service(api_key=api_key, model=model)
    except Exception as e:
        logger.warning(
            f"Failed to create signal generation service, falling back to None: {e}"
        )
        return None
