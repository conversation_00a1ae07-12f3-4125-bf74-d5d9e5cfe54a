"use client"

import { useState, useRef, useCallback } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Upload, X, Building2, Image, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { toast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"

interface LogoUploaderProps {
  currentLogo?: string
  organizationName?: string
  onUpload: (file: File) => Promise<string>
  onRemove?: () => Promise<void>
  className?: string
}

export function LogoUploader({
  currentLogo,
  organizationName = "",
  onUpload,
  onRemove,
  className
}: LogoUploaderProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const validateFile = (file: File) => {
    const maxSize = 10 * 1024 * 1024 // 10MB for logos
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml']

    if (!allowedTypes.includes(file.type)) {
      throw new Error('Please upload a JPEG, PNG, WebP, or SVG image')
    }

    if (file.size > maxSize) {
      throw new Error('File size must be less than 10MB')
    }
  }

  const handleFileSelect = useCallback(async (file: File) => {
    try {
      validateFile(file)
      setIsUploading(true)

      // Create preview
      const preview = URL.createObjectURL(file)
      setPreviewUrl(preview)

      // Upload file
      const logoUrl = await onUpload(file)
      
      toast({
        title: "Logo updated",
        description: "Your organization logo has been updated successfully.",
      })

      // Clean up preview
      URL.revokeObjectURL(preview)
      setPreviewUrl(null)

    } catch (error) {
      console.error('Logo upload error:', error)
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Failed to upload logo",
        variant: "destructive",
      })
      
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl)
        setPreviewUrl(null)
      }
    } finally {
      setIsUploading(false)
    }
  }, [onUpload, previewUrl])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }, [handleFileSelect])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const handleRemove = async () => {
    if (onRemove) {
      try {
        await onRemove()
        toast({
          title: "Logo removed",
          description: "Your organization logo has been removed.",
        })
      } catch (error) {
        toast({
          title: "Remove failed",
          description: "Failed to remove logo",
          variant: "destructive",
        })
      }
    }
  }

  const displayLogo = previewUrl || currentLogo

  return (
    <div className={cn("space-y-6", className)}>
      {/* Current Logo Display */}
      <div className="space-y-4">
        <label className="text-sm font-medium text-gray-900 dark:text-gray-100">
          Organization Logo
        </label>
        
        <div className="group relative">
          <div className="flex h-32 w-full max-w-md items-center justify-center rounded-xl border-2 border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
            {displayLogo ? (
              <div className="relative size-full">
                <img
                  src={displayLogo}
                  alt={`${organizationName} logo`}
                  className="size-full object-contain"
                />
                
                {/* Loading Overlay */}
                <AnimatePresence>
                  {isUploading && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="absolute inset-0 flex items-center justify-center rounded-lg bg-black/50"
                    >
                      <Loader2 className="size-6 animate-spin text-white" />
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Hover Overlay */}
                <motion.div
                  className="absolute inset-0 flex cursor-pointer items-center justify-center rounded-lg bg-black/50 opacity-0 transition-opacity group-hover:opacity-100"
                  onClick={() => fileInputRef.current?.click()}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Image className="size-6 text-white" />
                </motion.div>
              </div>
            ) : (
              <div className="text-center text-gray-400">
                <Building2 className="mx-auto mb-2 size-12" />
                <p className="text-sm">No logo uploaded</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Upload Area */}
      <div
        className={cn(
          "relative rounded-xl border-2 border-dashed p-8 text-center transition-all duration-200",
          isDragging 
            ? "border-blue-500 bg-blue-50 dark:bg-blue-950/20" 
            : "border-gray-300 hover:border-gray-400 dark:border-gray-600 dark:hover:border-gray-500"
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/png,image/webp,image/svg+xml"
          onChange={(e) => {
            const file = e.target.files?.[0]
            if (file) handleFileSelect(file)
          }}
          className="hidden"
          disabled={isUploading}
        />

        <div className="space-y-4">
          <Upload className="mx-auto size-12 text-gray-400" />
          <div>
            <p className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Drop your logo here, or{" "}
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="font-semibold text-blue-600 hover:text-blue-500"
                disabled={isUploading}
              >
                browse
              </button>
            </p>
            <p className="mt-2 text-sm text-gray-500">
              JPEG, PNG, WebP, or SVG up to 10MB
            </p>
            <p className="mt-1 text-xs text-gray-400">
              Recommended: Square format, minimum 200x200px
            </p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-3">
        <Button
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
          className="bg-white/70 backdrop-blur-sm dark:bg-black/70"
        >
          <Upload className="mr-2 size-4" />
          {displayLogo ? 'Change Logo' : 'Upload Logo'}
        </Button>
        
        {(currentLogo || previewUrl) && onRemove && (
          <Button
            variant="outline"
            onClick={handleRemove}
            disabled={isUploading}
            className="bg-white/70 text-red-600 backdrop-blur-sm hover:text-red-700 dark:bg-black/70"
          >
            <X className="mr-2 size-4" />
            Remove Logo
          </Button>
        )}
      </div>

      {/* Logo Preview in Context */}
      {displayLogo && (
        <div className="space-y-3">
          <label className="text-sm font-medium text-gray-900 dark:text-gray-100">
            Preview in shared forms
          </label>
          <div className="rounded-xl border bg-gradient-to-br from-blue-50 to-purple-50 p-6 dark:from-blue-950/20 dark:to-purple-950/20">
            <div className="flex items-center space-x-3">
              <img
                src={displayLogo}
                alt="Logo preview"
                className="size-8 object-contain"
              />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {organizationName || "Your Organization"}
                </p>
                <p className="text-xs text-gray-500">Powered by TractionX</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
