"""
BrightData Data Processor for TractionX Data Pipeline Service.

This module provides data processing for BrightData LinkedIn scraping results,
transforming them into normalized database records compatible with the existing
founder enrichment pipeline.
"""

from datetime import date, datetime, timezone
from typing import Any, Dict, List, Optional
from uuid import uuid4

from app.configs import get_logger
from app.models.founder import (
    BrightDataFounderData,
    BrightDataSnapshotRecord,
    FounderEducation,
    FounderExperience,
    FounderProfile,
    FounderRecord,
)
from app.services.skills_extraction import get_skills_extraction_service
from app.storage.rds_storage import RDSStorage
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class BrightDataProcessor:
    """
    Comprehensive processor for BrightData enrichment data.
    Handles cleaning, validation, and storage of LinkedIn scraping results.
    """

    def __init__(self, rds_storage: RDSStorage, s3_storage: S3Storage):
        self.rds = rds_storage
        self.s3 = s3_storage
        self.logger = get_logger(f"{__name__}.BrightDataProcessor")

    async def process_brightdata_payload(
        self, payload: Dict[str, Any], s3_raw_data_key: str
    ) -> Dict[str, Any]:
        """
        Process a complete BrightData payload and store in normalized schema.

        Args:
            payload: Complete BrightData payload with brightdata_data and metadata
            s3_raw_data_key: S3 key for raw data backup

        Returns:
            Processing result with success status and details
        """
        try:
            brightdata_data = payload.get("brightdata_data")
            metadata = payload.get("metadata", {})

            # Validate required fields
            validation_result = self._validate_brightdata_data(
                brightdata_data, metadata
            )
            self.logger.info(f"Validation result: {validation_result}")
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": f"Validation failed: {validation_result['errors']}",
                    "founder_id": metadata.get("founder_id"),
                }

            # At this point, brightdata_data is validated and not None
            if not isinstance(brightdata_data, BrightDataFounderData):
                return {
                    "success": False,
                    "error": "Invalid BrightData data type",
                    "founder_id": metadata.get("founder_id"),
                }

            # Generate deterministic UUID (same as PDL for consistency)
            founder_uuid = self._generate_founder_uuid(brightdata_data, metadata)

            # Clean and extract data
            cleaned_data = await self._clean_enrichment_data(
                brightdata_data, metadata, founder_uuid, s3_raw_data_key
            )

            # Store in database with transaction
            await self._store_founder_data(cleaned_data)

            self.logger.info(
                f"Successfully processed BrightData for founder {founder_uuid}"
            )

            return {
                "success": True,
                "founder_uuid": founder_uuid,
                "founder_id": metadata.get("founder_id"),
                "records_created": {
                    "experiences": len(cleaned_data["experiences"]),
                    "education": len(cleaned_data["education"]),
                    "skills": len(cleaned_data["skills"]),
                    "profiles": len(cleaned_data["profiles"]),
                },
            }

        except Exception as e:
            error_msg = f"BrightData processing failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            # Store error for debugging
            await self._store_processing_error(payload, error_msg, s3_raw_data_key)

            return {
                "success": False,
                "error": error_msg,
                "founder_id": metadata.get("founder_id"),
            }

    async def process_brightdata_snapshot(
        self,
        snapshot_data: List[Dict[str, Any]],
        metadata: Dict[str, Any],
        s3_raw_data_key: str,
    ) -> Dict[str, Any]:
        """
        Process BrightData snapshot data (real format) and store in normalized schema.

        Args:
            snapshot_data: Raw BrightData snapshot array
            metadata: Processing metadata
            s3_raw_data_key: S3 key for raw data backup

        Returns:
            Processing result with success status and details
        """
        try:
            if not snapshot_data or not isinstance(snapshot_data, list):
                return {
                    "success": False,
                    "error": "Invalid snapshot data format",
                    "founder_id": metadata.get("founder_id"),
                }

            # Take the first profile from the snapshot
            profile_data = snapshot_data[0]

            # Ensure required list fields have default values
            profile_data = {
                **profile_data,
                "experience": profile_data.get("experience") or [],
                "education": profile_data.get("education") or [],
                "skills": profile_data.get("skills") or [],
                "profiles": profile_data.get("profiles") or [],
                "languages": profile_data.get("languages") or [],
                "certifications": profile_data.get("certifications") or [],
                "recommendations": profile_data.get("recommendations") or [],
                "volunteer_experience": profile_data.get("volunteer_experience") or [],
                "projects": profile_data.get("projects") or [],
                "activity": profile_data.get("activity") or [],
            }

            # Convert to BrightDataSnapshotRecord
            snapshot_record = BrightDataSnapshotRecord(**profile_data)

            # Extract skills using LLM
            skills_service = await get_skills_extraction_service()
            extracted_skills = await skills_service.extract_skills_from_profile(
                snapshot_record
            )

            # Update the record with extracted skills
            snapshot_record.skills = extracted_skills

            # Generate deterministic UUID
            founder_uuid = self._generate_founder_uuid_from_snapshot(
                snapshot_record, metadata
            )

            # Clean and extract data
            cleaned_data = await self._clean_snapshot_data(
                snapshot_record, metadata, founder_uuid, s3_raw_data_key
            )

            # Store in database with transaction
            await self._store_founder_data(cleaned_data)

            # Determine data quality status
            data_quality = self._assess_data_quality(snapshot_record)

            self.logger.info(
                f"Successfully processed BrightData snapshot for founder {founder_uuid}",
                skills_count=len(extracted_skills),
                data_quality=data_quality,
            )

            return {
                "success": True,
                "founder_uuid": founder_uuid,
                "founder_id": metadata.get("founder_id"),
                "records_created": {
                    "experiences": len(cleaned_data["experiences"]),
                    "education": len(cleaned_data["education"]),
                    "skills": len(cleaned_data["skills"]),
                    "profiles": len(cleaned_data["profiles"]),
                },
                "extracted_skills": extracted_skills,
                "data_quality": data_quality,
                "data_source": "brightdata",
            }

        except Exception as e:
            error_msg = f"BrightData snapshot processing failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            # Store error for debugging
            await self._store_processing_error(
                {"snapshot_data": snapshot_data, "metadata": metadata},
                error_msg,
                s3_raw_data_key,
            )

            return {
                "success": False,
                "error": error_msg,
                "founder_id": metadata.get("founder_id"),
            }

    def _validate_brightdata_data(
        self, brightdata_data: Optional[BrightDataFounderData], metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate required fields in BrightData data."""
        errors = []

        # Check metadata
        required_metadata = ["founder_id", "company_id", "org_id"]
        for field in required_metadata:
            if not metadata.get(field):
                errors.append(f"Missing required metadata field: {field}")

        # Check BrightData data
        if not brightdata_data:
            errors.append("Missing BrightData data")
            return {"valid": False, "errors": errors}

        if not brightdata_data.url:
            errors.append("Missing LinkedIn URL")

        if not brightdata_data.full_name:
            errors.append("Missing full name")

        return {"valid": len(errors) == 0, "errors": errors}

    def _generate_founder_uuid(
        self, brightdata_data: BrightDataFounderData, metadata: Dict[str, Any]
    ) -> str:
        """Generate deterministic UUIDv5 based on name + LinkedIn URL."""
        from uuid import NAMESPACE_DNS, uuid5

        full_name = (brightdata_data.full_name or "").strip().lower()
        linkedin_url = (brightdata_data.url or "").strip().lower()

        # Create deterministic string for UUID generation
        uuid_string = f"{full_name}|{linkedin_url}"

        # Generate UUIDv5 using DNS namespace (same as PDL for consistency)
        founder_uuid = uuid5(NAMESPACE_DNS, uuid_string)

        return str(founder_uuid)

    async def _clean_enrichment_data(
        self,
        brightdata_data: BrightDataFounderData,
        metadata: Dict[str, Any],
        founder_uuid: str,
        s3_raw_data_key: str,
    ) -> Dict[str, Any]:
        """Clean and structure BrightData for database storage."""

        # Clean basic founder info
        founder_record = self._create_founder_record(
            brightdata_data, metadata, founder_uuid, s3_raw_data_key
        )

        # Extract and clean related data
        experiences = self._extract_experiences(brightdata_data)
        education = self._extract_education(brightdata_data)
        skills = self._extract_skills(brightdata_data)
        profiles = self._extract_profiles(brightdata_data)

        return {
            "founder": founder_record,
            "experiences": experiences,
            "education": education,
            "skills": skills,
            "profiles": profiles,
        }

    def _create_founder_record(
        self,
        brightdata_data: BrightDataFounderData,
        metadata: Dict[str, Any],
        founder_uuid: str,
        s3_raw_data_key: str,
    ) -> FounderRecord:
        """Create a clean FounderRecord from BrightData."""

        # Clean name fields
        full_name = self._clean_string(brightdata_data.full_name)
        first_name = self._clean_string(brightdata_data.first_name)
        last_name = self._clean_string(brightdata_data.last_name)

        # Clean job info from headline
        current_job_title = self._clean_string(brightdata_data.headline)
        current_job_company = None  # Extract from headline if possible

        # Clean URLs
        linkedin_url = self._clean_url(brightdata_data.url)
        github_url = None  # Extract from profiles if available

        # Clean location
        location_country = self._clean_string(brightdata_data.location)

        # Use scraped_at as enrichment date
        enrichment_date = brightdata_data.scraped_at

        # Use confidence score from BrightData
        confidence_score = brightdata_data.confidence_score or 0.8

        now = datetime.now(timezone.utc)

        return FounderRecord(
            id=founder_uuid,
            founder_id=metadata["founder_id"],
            full_name=full_name,
            first_name=first_name,
            last_name=last_name,
            current_job_title=current_job_title,
            current_job_company=current_job_company,
            linkedin_url=linkedin_url,
            github_url=github_url,
            location_country=location_country,
            org_id=metadata["org_id"],
            company_id=metadata["company_id"],
            source="brightdata",
            confidence_score=confidence_score,
            enrichment_date=enrichment_date,
            s3_raw_data_key=s3_raw_data_key,
            created_at=now,
            updated_at=now,
        )

    def _extract_experiences(
        self, brightdata_data: BrightDataFounderData
    ) -> List[FounderExperience]:
        """Extract and clean experience records from BrightData."""
        experiences = []
        experience_list = brightdata_data.experience or []

        for exp_data in experience_list:
            if not isinstance(exp_data, dict):
                continue

            experience = FounderExperience(
                id=str(uuid4()),
                founder_id="",  # Will be set during storage
                company_name=self._clean_string(exp_data.get("company")),
                title=self._clean_string(exp_data.get("title")),
                industry=self._clean_string(exp_data.get("industry")),
                company_size=self._clean_string(exp_data.get("company_size")),
                start_date=self._parse_date(exp_data.get("start_date")),
                end_date=self._parse_date(exp_data.get("end_date")),
                is_primary=exp_data.get("is_current", False),
                location=self._clean_string(exp_data.get("location")),
            )
            experiences.append(experience)

        return experiences

    def _extract_education(
        self, brightdata_data: BrightDataFounderData
    ) -> List[FounderEducation]:
        """Extract and clean education records from BrightData."""
        education_records = []
        education_list = brightdata_data.education or []

        for edu_data in education_list:
            if not isinstance(edu_data, dict):
                continue

            school_name = self._clean_string(edu_data.get("school"))

            # Skip entries without school name
            if not school_name:
                continue

            # Extract degree and major
            degree = self._clean_string(edu_data.get("degree"))
            major = self._clean_string(edu_data.get("major"))

            degrees = [degree] if degree else []
            majors = [major] if major else []

            education = FounderEducation(
                id=str(uuid4()),
                founder_id="",  # Will be set during storage
                school_name=school_name,
                degrees=degrees,
                majors=majors,
                start_date=self._parse_date(edu_data.get("start_date")),
                end_date=self._parse_date(edu_data.get("end_date")),
                location=self._clean_string(edu_data.get("location")),
            )
            education_records.append(education)

        return education_records

    def _extract_skills(self, brightdata_data: BrightDataFounderData) -> List[str]:
        """Extract and clean skills list from BrightData."""
        skills = brightdata_data.skills or []
        if not isinstance(skills, list):
            return []

        # Clean and deduplicate skills
        cleaned_skills = set()
        for skill in skills:
            if isinstance(skill, dict):
                skill_name = skill.get("name") or skill.get("skill")
            elif isinstance(skill, str):
                skill_name = skill
            else:
                continue

            cleaned_skill = self._clean_string(skill_name)
            if cleaned_skill and len(cleaned_skill) > 1:
                cleaned_skills.add(cleaned_skill.lower())

        return sorted(list(cleaned_skills))

    def _extract_profiles(
        self, brightdata_data: BrightDataFounderData
    ) -> List[FounderProfile]:
        """Extract and clean social profiles from BrightData."""
        profiles = []
        profiles_list = brightdata_data.profiles or []

        # Track URLs to avoid duplicates
        seen_urls = set()

        for profile_data in profiles_list:
            if not isinstance(profile_data, dict):
                continue

            network = self._clean_string(profile_data.get("network"))
            url = self._clean_url(profile_data.get("url"))

            if network and url and url not in seen_urls:
                profile = FounderProfile(
                    id=str(uuid4()),
                    founder_id="",  # Will be set during storage
                    network=network,
                    url=url,
                )
                profiles.append(profile)
                seen_urls.add(url)

        return profiles

    async def _store_founder_data(self, cleaned_data: Dict[str, Any]) -> None:
        """Store founder data in database with transaction."""
        founder_record = cleaned_data["founder"]
        experiences = cleaned_data["experiences"]
        education = cleaned_data["education"]
        skills = cleaned_data["skills"]
        profiles = cleaned_data["profiles"]

        # Use the same RDS method as PDL for consistency
        await self.rds.upsert_founder_with_relations(
            founder_data=founder_record.model_dump(),
            experiences=[exp.model_dump() for exp in experiences],
            education=[
                {
                    "id": edu.id,
                    "founder_id": "",  # Will be set during storage
                    "school_name": edu.school_name,
                    "degrees": edu.degrees,
                    "majors": edu.majors,
                    "start_date": edu.start_date,
                    "end_date": edu.end_date,
                    "location": edu.location,
                }
                for edu in education
            ],
            skills=skills,
            profiles=[
                {
                    "id": prof.id,
                    "founder_id": "",  # Will be set during storage
                    "network": prof.network,
                    "url": prof.url,
                }
                for prof in profiles
            ],
        )

    async def _store_processing_error(
        self, payload: Dict[str, Any], error_msg: str, s3_key: str
    ) -> None:
        """Store processing error for debugging."""
        try:
            error_record = {
                "founder_id": payload.get("metadata", {}).get("founder_id"),
                "company_id": payload.get("metadata", {}).get("company_id"),
                "org_id": payload.get("metadata", {}).get("org_id"),
                "error_message": error_msg,
                "s3_raw_data_key": s3_key,
                "payload_preview": str(payload)[:1000],
                "created_at": datetime.now(timezone.utc),
            }

            # Store in the same error table as PDL
            await self._ensure_error_table_exists()
            await self.rds.insert("founder_processing_errors", error_record)

        except Exception as e:
            self.logger.error(f"Failed to store processing error: {str(e)}")

    async def _ensure_error_table_exists(self) -> None:
        """Ensure the error tracking table exists."""
        # This table should already exist from PDL processing
        # Just ensure it's available
        pass

    def _clean_string(self, value: Any) -> Optional[str]:
        """Clean and normalize string values."""
        if not value or not isinstance(value, str):
            return None

        cleaned = value.strip()
        if not cleaned:
            return None

        # Remove excessive whitespace
        cleaned = " ".join(cleaned.split())

        return cleaned

    def _generate_founder_uuid_from_snapshot(
        self, snapshot_record: BrightDataSnapshotRecord, metadata: Dict[str, Any]
    ) -> str:
        """Generate deterministic UUIDv5 based on name + LinkedIn URL from snapshot."""
        from uuid import NAMESPACE_DNS, uuid5

        full_name = (snapshot_record.name or "").strip().lower()
        linkedin_url = (
            (snapshot_record.url or snapshot_record.input_url or "").strip().lower()
        )

        # Create deterministic string for UUID generation
        uuid_string = f"{full_name}|{linkedin_url}"

        # Generate UUIDv5 using DNS namespace (same as PDL for consistency)
        founder_uuid = uuid5(NAMESPACE_DNS, uuid_string)

        return str(founder_uuid)

    async def _clean_snapshot_data(
        self,
        snapshot_record: BrightDataSnapshotRecord,
        metadata: Dict[str, Any],
        founder_uuid: str,
        s3_raw_data_key: str,
    ) -> Dict[str, Any]:
        """Clean and structure BrightData snapshot for database storage."""

        # Clean basic founder info
        founder_record = self._create_founder_record_from_snapshot(
            snapshot_record, metadata, founder_uuid, s3_raw_data_key
        )

        # Extract and clean related data
        experiences = self._extract_experiences_from_snapshot(snapshot_record)
        education = self._extract_education_from_snapshot(snapshot_record)
        skills = snapshot_record.skills or []
        profiles = self._extract_profiles_from_snapshot(snapshot_record)

        return {
            "founder": founder_record,
            "experiences": experiences,
            "education": education,
            "skills": skills,
            "profiles": profiles,
        }

    def _create_founder_record_from_snapshot(
        self,
        snapshot_record: BrightDataSnapshotRecord,
        metadata: Dict[str, Any],
        founder_uuid: str,
        s3_raw_data_key: str,
    ) -> FounderRecord:
        """Create a clean FounderRecord from BrightData snapshot."""

        # Clean name fields
        full_name = self._clean_string(snapshot_record.name)
        first_name = self._clean_string(snapshot_record.first_name)
        last_name = self._clean_string(snapshot_record.last_name)

        # Clean job info from position
        current_job_title = self._clean_string(snapshot_record.position)
        current_job_company = None

        # Extract company from current_company if available
        if snapshot_record.current_company:
            current_job_company = self._clean_string(
                snapshot_record.current_company.get("name")
            )

        # Clean URLs
        linkedin_url = self._clean_url(snapshot_record.url or snapshot_record.input_url)
        github_url = None  # Extract from profiles if available

        # Clean location
        location_country = self._clean_string(
            snapshot_record.location or snapshot_record.city
        )

        # Parse timestamp
        enrichment_date = None
        if snapshot_record.timestamp:
            try:
                enrichment_date = datetime.fromisoformat(
                    snapshot_record.timestamp.replace("Z", "+00:00")
                )
            except (ValueError, AttributeError):
                pass

        # Use default confidence score for BrightData
        confidence_score = 0.8

        now = datetime.now(timezone.utc)

        return FounderRecord(
            id=founder_uuid,
            founder_id=metadata["founder_id"],
            full_name=full_name,
            first_name=first_name,
            last_name=last_name,
            current_job_title=current_job_title,
            current_job_company=current_job_company,
            linkedin_url=linkedin_url,
            github_url=github_url,
            location_country=location_country,
            org_id=metadata["org_id"],
            company_id=metadata["company_id"],
            source="brightdata",
            confidence_score=confidence_score,
            enrichment_date=enrichment_date,
            s3_raw_data_key=s3_raw_data_key,
            created_at=now,
            updated_at=now,
        )

    def _extract_experiences_from_snapshot(
        self, snapshot_record: BrightDataSnapshotRecord
    ) -> List[FounderExperience]:
        """Extract and clean experience records from BrightData snapshot."""
        experiences = []
        experience_list = snapshot_record.experience or []

        # Track companies to avoid duplicates
        seen_companies = set()

        # First, process explicit experience entries
        for exp_data in experience_list:
            if not isinstance(exp_data, dict):
                continue

            company_name = self._clean_string(exp_data.get("company"))
            if not company_name:
                continue

            # Track this company to avoid duplicates
            seen_companies.add(company_name.lower())

            experience = FounderExperience(
                id=str(uuid4()),
                founder_id="",  # Will be set during storage
                company_name=company_name,
                title=self._clean_string(exp_data.get("title")),
                industry=None,  # Not available in BrightData snapshot
                company_size=None,  # Not available in BrightData snapshot
                start_date=self._parse_date(exp_data.get("start_date")),
                end_date=self._parse_date(exp_data.get("end_date")),
                is_primary=exp_data.get("end_date") == "Present",
                location=self._clean_string(exp_data.get("location")),
            )
            experiences.append(experience)

        # Handle case where experience is null but current_company exists
        if not experiences and snapshot_record.current_company:
            current_company = snapshot_record.current_company
            if isinstance(current_company, dict):
                company_name = self._clean_string(current_company.get("name"))
                if company_name:
                    # Extract title from position or current_company
                    title = self._clean_string(snapshot_record.position)
                    if not title and current_company.get("title"):
                        title = self._clean_string(current_company.get("title"))

                    experience = FounderExperience(
                        id=str(uuid4()),
                        founder_id="",  # Will be set during storage
                        company_name=company_name,
                        title=title,
                        industry=None,
                        company_size=None,
                        start_date=None,  # Not available in current_company
                        end_date=None,
                        is_primary=True,  # Current company is always primary
                        location=self._clean_string(current_company.get("location")),
                    )
                    experiences.append(experience)
                    seen_companies.add(company_name.lower())

        # Handle case where current_company exists but wasn't in experience list
        elif snapshot_record.current_company and isinstance(
            snapshot_record.current_company, dict
        ):
            current_company = snapshot_record.current_company
            company_name = self._clean_string(current_company.get("name"))

            if company_name and company_name.lower() not in seen_companies:
                # Extract title from position or current_company
                title = self._clean_string(snapshot_record.position)
                if not title and current_company.get("title"):
                    title = self._clean_string(current_company.get("title"))

                experience = FounderExperience(
                    id=str(uuid4()),
                    founder_id="",  # Will be set during storage
                    company_name=company_name,
                    title=title,
                    industry=None,
                    company_size=None,
                    start_date=None,  # Not available in current_company
                    end_date=None,
                    is_primary=True,  # Current company is always primary
                    location=self._clean_string(current_company.get("location")),
                )
                experiences.append(experience)

        return experiences

    def _assess_data_quality(self, snapshot_record: BrightDataSnapshotRecord) -> str:
        """Assess the quality of BrightData snapshot data."""
        has_name = bool(snapshot_record.name)
        has_position = bool(snapshot_record.position)
        has_experience = bool(
            snapshot_record.experience and len(snapshot_record.experience) > 0
        )
        has_education = bool(
            snapshot_record.education and len(snapshot_record.education) > 0
        )
        has_about = bool(snapshot_record.about)
        has_location = bool(snapshot_record.location)

        # Count available fields
        available_fields = sum([
            has_name,
            has_position,
            has_experience,
            has_education,
            has_about,
            has_location,
        ])

        if available_fields >= 5:
            return "high"
        elif available_fields >= 3:
            return "medium"
        elif available_fields >= 2:
            return "low"
        else:
            return "minimal"

    def _extract_education_from_snapshot(
        self, snapshot_record: BrightDataSnapshotRecord
    ) -> List[FounderEducation]:
        """Extract and clean education records from BrightData snapshot."""
        education_records = []
        education_list = snapshot_record.education or []

        # Track schools to avoid duplicates
        seen_schools = set()

        # First, process explicit education entries
        for edu_data in education_list:
            if not isinstance(edu_data, dict):
                continue

            school_name = self._clean_string(edu_data.get("title"))

            # Skip entries without school name
            if not school_name:
                continue

            # Track this school to avoid duplicates
            seen_schools.add(school_name.lower())

            # Extract degree and field
            degree = self._clean_string(edu_data.get("degree"))
            field = self._clean_string(edu_data.get("field"))

            degrees = [degree] if degree else []
            majors = [field] if field else []

            education = FounderEducation(
                id=str(uuid4()),
                founder_id="",  # Will be set during storage
                school_name=school_name,
                degrees=degrees,
                majors=majors,
                start_date=self._parse_date(edu_data.get("start_year")),
                end_date=self._parse_date(edu_data.get("end_year")),
                location=None,  # Not available in BrightData snapshot
            )
            education_records.append(education)

        # Handle case where education is null but educations_details exists
        if not education_records and snapshot_record.educations_details:
            school_name = self._clean_string(snapshot_record.educations_details)
            if school_name:
                education = FounderEducation(
                    id=str(uuid4()),
                    founder_id="",  # Will be set during storage
                    school_name=school_name,
                    degrees=[],  # Not available in educations_details
                    majors=[],
                    start_date=None,
                    end_date=None,
                    location=None,
                )
                education_records.append(education)
                seen_schools.add(school_name.lower())

        # Handle case where educations_details exists but wasn't in education list
        elif snapshot_record.educations_details:
            school_name = self._clean_string(snapshot_record.educations_details)
            if school_name and school_name.lower() not in seen_schools:
                education = FounderEducation(
                    id=str(uuid4()),
                    founder_id="",  # Will be set during storage
                    school_name=school_name,
                    degrees=[],
                    majors=[],
                    start_date=None,
                    end_date=None,
                    location=None,
                )
                education_records.append(education)

        return education_records

    def _extract_profiles_from_snapshot(
        self, snapshot_record: BrightDataSnapshotRecord
    ) -> List[FounderProfile]:
        """Extract and clean social profiles from BrightData snapshot."""
        profiles = []

        # Add LinkedIn profile
        if snapshot_record.url or snapshot_record.input_url:
            linkedin_url = self._clean_url(
                snapshot_record.url or snapshot_record.input_url
            )
            if linkedin_url:
                profile = FounderProfile(
                    id=str(uuid4()),
                    founder_id="",  # Will be set during storage
                    network="linkedin",
                    url=linkedin_url,
                )
                profiles.append(profile)

        return profiles

    def _clean_url(self, value: Any) -> Optional[str]:
        """Clean and normalize URL values."""
        if not value or not isinstance(value, str):
            return None

        url = value.strip().lower()
        if not url:
            return None

        # Add protocol if missing
        if not url.startswith(("http://", "https://")):
            url = f"https://{url}"

        return url

    def _parse_date(self, date_str: Any) -> Optional[date]:
        """Parse date string to date object."""
        if not date_str:
            return None

        if isinstance(date_str, str):
            # Try different date formats
            for fmt in ["%Y-%m-%d", "%Y-%m", "%Y"]:
                try:
                    parsed = datetime.strptime(date_str, fmt)
                    return parsed.date()
                except ValueError:
                    continue

        return None
