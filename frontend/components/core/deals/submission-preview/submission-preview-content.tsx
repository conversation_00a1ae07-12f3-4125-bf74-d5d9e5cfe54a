"use client"

import { motion } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { FormattedSubmission, FormattedSection, FormattedAnswer } from "@/lib/types/submission-preview"
import { SubmissionPreviewAPI } from "@/lib/api/submission-preview-api"

interface SubmissionPreviewContentProps {
  submission: FormattedSubmission
}

interface AnswerDisplayProps {
  answer: FormattedAnswer
}

interface SectionCardProps {
  section: FormattedSection
  index: number
}

// Component to display individual answers
function AnswerDisplay({ answer }: AnswerDisplayProps) {
  const displayValue = answer.isEmpty
    ? SubmissionPreviewAPI.getEmptyDisplayValue()
    : Array.isArray(answer.value)
      ? answer.value.join(", ")
      : String(answer.value)

  return (
    <div className="-mx-3 grid grid-cols-1 gap-3 rounded-lg border-b border-gray-100/60 px-3 py-5 transition-colors duration-200 last:border-b-0 hover:bg-gray-50/30 lg:grid-cols-5">
      <div className="text-base font-semibold leading-relaxed text-gray-900 lg:col-span-2">
        {answer.label}
      </div>
      <div className={cn(
        "select-text text-base leading-relaxed lg:col-span-3",
        answer.isEmpty ? "italic text-gray-400" : "text-gray-700"
      )}>
        {answer.type === 'boolean' && !answer.isEmpty ? (
          <Badge
            variant={answer.value === 'Yes' ? 'default' : 'secondary'}
            className={cn(
              "px-4 py-1.5 text-sm font-medium",
              answer.value === 'Yes'
                ? "border-green-200 bg-green-100 text-green-800"
                : "border-gray-200 bg-gray-100 text-gray-700"
            )}
          >
            {displayValue}
          </Badge>
        ) : answer.type === 'multi_select' && Array.isArray(answer.value) && answer.value.length > 0 ? (
          <div className="flex flex-wrap gap-2">
            {answer.value.map((item, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="border-blue-200 bg-blue-50 px-3 py-1.5 text-sm font-medium text-blue-800"
              >
                {item}
              </Badge>
            ))}
          </div>
        ) : answer.type === 'number' && !answer.isEmpty ? (
          <span className="font-mono text-lg font-semibold text-gray-900">{displayValue}</span>
        ) : (
          <span className="break-words font-medium">{displayValue}</span>
        )}
      </div>
    </div>
  )
}

// Component to display a section card
function SectionCard({ section, index }: SectionCardProps) {
  const hasContent = SubmissionPreviewAPI.sectionHasContent(section)

  if (!hasContent) {
    return null // Don't render empty sections
  }

  // Get section icon based on title
  const getSectionIcon = (title: string) => {
    const lowerTitle = title.toLowerCase()
    if (lowerTitle.includes('company') || lowerTitle.includes('overview')) return ''
    if (lowerTitle.includes('founder') || lowerTitle.includes('team')) return ''
    if (lowerTitle.includes('business') || lowerTitle.includes('model')) return ''
    if (lowerTitle.includes('funding') || lowerTitle.includes('financial')) return ''
    if (lowerTitle.includes('product') || lowerTitle.includes('traction')) return ''
    if (lowerTitle.includes('market') || lowerTitle.includes('competition')) return ''
    return ''
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="h-fit"
    >
      <Card className="overflow-hidden rounded-2xl border-gray-200/40 bg-white/95 shadow-lg backdrop-blur-sm transition-all duration-300 hover:border-gray-300/60 hover:shadow-xl">
        <CardHeader className="rounded-t-2xl border-b border-gray-100/50 bg-white pb-2">
          <CardTitle className="flex items-center gap-3 text-xl font-bold text-gray-900">
            <span className="text-2xl">{getSectionIcon(section.title)}</span>
            <div className="flex-1">
              <div className="flex items-center gap-3">
                {section.title}
                {section.isRepeatable && (
                  <Badge variant="outline" className="border-blue-200 bg-blue-50 text-xs font-medium text-blue-700">
                    Repeatable
                  </Badge>
                )}
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="pb-8 pt-6">
          {section.isRepeatable && section.instances ? (
            // Render repeatable section instances
            <div className="space-y-8">
              {section.instances.map((instance, instanceIndex) => (
                <div key={instance.index} className="space-y-4">
                  <div className="flex items-center gap-3 border-b border-gray-200/60 pb-3">
                    <div className="flex size-8 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-blue-600">
                      <span className="text-sm font-bold text-white">#{instance.index + 1}</span>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-800">
                      {instance.title}
                    </h4>
                  </div>
                  <div className="space-y-1 pl-2">
                    {instance.answers.map((answer, answerIndex) => (
                      <AnswerDisplay key={answerIndex} answer={answer} />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            // Render regular section questions
            <div className="space-y-1">
              {section.questions?.map((question, questionIndex) => (
                <AnswerDisplay key={questionIndex} answer={question} />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}

export function SubmissionPreviewContent({ submission }: SubmissionPreviewContentProps) {
  const summary = SubmissionPreviewAPI.getSubmissionSummary(submission)
  const sectionsWithContent = submission.sections.filter(section =>
    SubmissionPreviewAPI.sectionHasContent(section)
  )

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-8"
    >
      {/* Section cards in a beautiful grid layout */}
      <div className="grid grid-cols-1 gap-8 xl:grid-cols-2">
        {sectionsWithContent.map((section, index) => (
          <SectionCard
            key={section.id}
            section={section}
            index={index}
          />
        ))}
      </div>

      {/* Empty state if no content */}
      {sectionsWithContent.length === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="py-12 text-center"
        >
          <div className="rounded-2xl border border-gray-200/60 bg-white/80 p-8 backdrop-blur-sm">
            <div className="mb-4 text-gray-400">
              <svg className="mx-auto size-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="mb-2 text-lg font-medium text-gray-900">No content to display</h3>
            <p className="text-sm text-gray-600">This submission appears to be empty or contains no answered questions.</p>
          </div>
        </motion.div>
      )}
    </motion.div>
  )
}
