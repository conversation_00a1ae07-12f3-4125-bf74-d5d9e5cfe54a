<svg width="1200" height="630" viewBox="0 0 1200 630" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1200" height="630" fill="#ffffff"/>
  
  <!-- Subtle gradient background -->
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="1200" height="630" fill="url(#bg)"/>
  
  <!-- Logo placeholder (you'll replace this with your actual logo) -->
  <rect x="100" y="200" width="80" height="80" rx="12" fill="#1e293b"/>
  <text x="140" y="250" font-family="system-ui, -apple-system, sans-serif" font-size="16" font-weight="bold" fill="#ffffff" text-anchor="middle">TX</text>
  
  <!-- Main heading -->
  <text x="220" y="230" font-family="system-ui, -apple-system, sans-serif" font-size="48" font-weight="bold" fill="#1e293b">TractionX</text>
  <text x="220" y="280" font-family="system-ui, -apple-system, sans-serif" font-size="24" font-weight="normal" fill="#64748b">Private Market Investment OS</text>
  
  <!-- Tagline -->
  <text x="100" y="350" font-family="system-ui, -apple-system, sans-serif" font-size="32" font-weight="600" fill="#334155">From first signal to exit</text>
  <text x="100" y="390" font-family="system-ui, -apple-system, sans-serif" font-size="24" font-weight="normal" fill="#64748b">Discover, evaluate, and act on high-potential startups</text>
  <text x="100" y="430" font-family="system-ui, -apple-system, sans-serif" font-size="24" font-weight="normal" fill="#64748b">with AI-powered insights</text>
  
  <!-- URL -->
  <text x="100" y="520" font-family="system-ui, -apple-system, sans-serif" font-size="20" font-weight="normal" fill="#94a3b8">tractionx.ai</text>
  
  <!-- Decorative elements -->
  <circle cx="1000" cy="150" r="60" fill="#e2e8f0" opacity="0.5"/>
  <circle cx="1100" cy="250" r="40" fill="#cbd5e1" opacity="0.3"/>
  <circle cx="950" cy="300" r="30" fill="#e2e8f0" opacity="0.4"/>
</svg> 