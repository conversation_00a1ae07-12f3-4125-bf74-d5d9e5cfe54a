"use client"

import { Shell } from "@/components/shell"
import { ThesesList } from "@/components/core/thesis-builder/theses-list"
import { useAuth } from "@/lib/auth-context"
import { mobileRetreat, visualRetreat } from "@/lib/utils/responsive"

export default function ThesesPage() {
  const { isAuthenticated } = useAuth()

  if (!isAuthenticated) {
    return null // This is handled by middleware
  }

  return (
    <Shell>
      <ThesesList />
    </Shell>
  )
}
