# TractionX AI Agent Foundation

A secure, modular, and extensible LangChain-based AI Agent system for TractionX with multi-tenant data isolation, MongoDB storage, and Qdrant vector database integration.

## Features

- **Multi-tenant Architecture**: Complete data and vector isolation per organization
- **LangChain Integration**: Built on LangChain for flexible agent orchestration
- **MongoDB Storage**: Agent metadata and audit logs stored in MongoDB
- **Qdrant Vector Database**: Org-scoped vector collections for semantic search
- **Proprietary Tools**: Pre-built tools for thesis matching, founder analysis, and market momentum
- **Full Auditability**: Comprehensive logging of all agent interactions
- **FastAPI REST API**: Complete CRUD operations for agents and deal chat
- **Security**: JWT-based authentication with row-level access control

## Architecture

```
ai/
├── __init__.py
├── config.py              # Configuration management
├── db.py                  # MongoDB connection utilities
├── models.py              # Pydantic models for agents, logs, context
├── registry.py            # Proprietary agent registration
├── runner.py              # LangChain agent execution logic
├── context.py             # Multi-tenant context builder & Qdrant helper
├── audit.py               # Audit logging helper
├── main.py                # FastAPI application
├── tools/                 # Proprietary tools
│   ├── __init__.py
│   ├── base.py           # Base tool classes
│   ├── thesis_tools.py   # Thesis matching & scoring tools
│   ├── founder_tools.py  # Founder analysis tools
│   ├── market_tools.py   # Market analysis tools
│   └── vector_tools.py   # Vector search tools
├── api/                   # FastAPI routes
│   ├── __init__.py
│   ├── auth.py           # Authentication dependencies
│   ├── agents.py         # Agent CRUD endpoints
│   └── deals.py          # Deal chat endpoints
└── tests/                 # Unit tests
```

## Quick Start

### 1. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit configuration
vim .env
```

Required environment variables:
```bash
# MongoDB (hosted)
MONGODB_URL=mongodb+srv://your-mongodb-atlas-url
MONGODB_DB_NAME=tx_ai

# Qdrant (hosted)
QDRANT_URL=https://your-qdrant-cloud-instance.cloud.qdrant.io
QDRANT_API_KEY=your_qdrant_api_key

# Redis (hosted)
REDIS_URL=redis://username:password@your-redis-cloud-instance:port

# OpenAI
OPENAI_API_KEY=your_openai_api_key

# Security
SECRET_KEY=your_secret_key
```

### 2. Installation

```bash
# Install dependencies
poetry install

# Or using pip
pip install -r requirements.txt
```

### 3. Database Setup

The system is configured to use hosted database services:

- **MongoDB**: Uses a hosted MongoDB Atlas instance with database name "tx_ai"
- **Qdrant**: Uses a hosted Qdrant Cloud instance
- **Redis**: Uses a hosted Redis Cloud instance

Make sure to update the connection URLs in your `.env` file:

```bash
# Initialize database indexes
poetry run python -c "
import asyncio
from ai.db import ensure_indexes
asyncio.run(ensure_indexes())
"
```

### 4. Run the Application

```bash
# Development server
poetry run uvicorn ai.main:app --reload --port 8002

# Or using Docker (only builds and runs the AI service, using hosted databases)
docker-compose up --build
```

### 5. API Documentation

Visit `http://localhost:8002/docs` for interactive API documentation.

## Core Components

### Agent Types

1. **Proprietary Agents**: Pre-built agents for common investment tasks
   - Thesis Matching Agent
   - Founder Signals Agent
   - Market Momentum Agent

2. **Custom Agents**: User-created agents with configurable tools and prompts

3. **System Agents**: Internal system agents for automation

### Multi-Tenant Security

- All data is isolated by `org_id`
- Vector collections are namespaced as `org_{org_id}_vectors`
- Row-level security for all database operations
- JWT-based authentication with org context validation

### Available Tools

- **Thesis Matcher**: Analyzes deals against investment thesis criteria
- **Scoring Engine**: Applies configurable scoring rules
- **Founder Analyzer**: Evaluates founder profiles and team dynamics
- **Network Analyzer**: Analyzes professional networks
- **News Analyzer**: Processes news and market sentiment
- **Vector Search**: Semantic search across org-specific content

## API Endpoints

### Agents

- `POST /api/ai/agents/` - Create agent
- `GET /api/ai/agents/` - List agents
- `GET /api/ai/agents/{id}` - Get agent details
- `PUT /api/ai/agents/{id}` - Update agent
- `DELETE /api/ai/agents/{id}` - Delete agent
- `POST /api/ai/agents/{id}/run` - Run agent
- `GET /api/ai/agents/{id}/logs` - Get execution logs
- `GET /api/ai/agents/{id}/capabilities` - Get agent capabilities

### Deal Chat

- `POST /api/ai/deals/{deal_id}/chat` - Chat about a deal
- `POST /api/ai/deals/{deal_id}/analyze` - Analyze a deal
- `GET /api/ai/deals/{deal_id}/chat/history` - Get chat history

## Usage Examples

### Create a Custom Agent

```python
import httpx

agent_data = {
    "name": "Investment Analyst",
    "description": "Analyzes investment opportunities",
    "tools": ["thesis_matcher", "founder_analyzer"],
    "system_prompt": "You are an expert investment analyst...",
    "visibility": "org"
}

response = httpx.post(
    "http://localhost:8002/api/ai/agents/",
    json=agent_data,
    headers={"Authorization": "Bearer <token>", "X-ORG-ID": "<org_id>"}
)
```

### Run an Agent

```python
run_request = {
    "message": "Analyze this startup's investment potential",
    "deal_id": "deal_123",
    "context_data": {"focus": "team_analysis"}
}

response = httpx.post(
    f"http://localhost:8002/api/ai/agents/{agent_id}/run",
    json=run_request,
    headers={"Authorization": "Bearer <token>", "X-ORG-ID": "<org_id>"}
)
```

### Deal Chat

```python
chat_request = {
    "message": "What are the key risks for this investment?",
    "agent_type": "thesis_matching"
}

response = httpx.post(
    f"http://localhost:8002/api/ai/deals/{deal_id}/chat",
    json=chat_request,
    headers={"Authorization": "Bearer <token>", "X-ORG-ID": "<org_id>"}
)
```

## Development

### Running Tests

```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=ai

# Run specific test file
poetry run pytest tests/test_auth.py
```

### Code Quality

```bash
# Format code
poetry run black ai/
poetry run isort ai/

# Lint code
poetry run flake8 ai/

# Type checking
poetry run mypy ai/
```

### Adding New Tools

1. Create tool class inheriting from `BaseTool`
2. Implement `execute` method
3. Register in `runner.py`
4. Add to agent configurations

Example:
```python
from ai.tools.base import BaseTool, ToolInput, ToolOutput

class CustomTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="custom_tool",
            description="Does custom analysis"
        )

    async def execute(self, input_data: ToolInput, context: AgentContext) -> ToolOutput:
        # Implementation here
        return ToolOutput(success=True, data={"result": "analysis"})
```

## Deployment

### Docker

```bash
# Build image
docker build -t tractionx-ai-agents .

# Run container
docker run -p 8002:8002 --env-file .env tractionx-ai-agents
```

### Production Considerations

- Set `DEBUG=false` in production
- Use proper secrets management for API keys
- Configure proper CORS origins
- Set up monitoring and logging
- Use a production WSGI server
- Configure database connection pooling

## Monitoring

### Health Check

```bash
curl http://localhost:8002/health
```

### Metrics

- Agent execution times
- Tool usage statistics
- Error rates
- Token consumption

## Security

- All API endpoints require authentication
- Org-level data isolation
- Input validation and sanitization
- Rate limiting (recommended for production)
- Audit logging for compliance

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Run quality checks
5. Submit a pull request

## License

Copyright (c) 2024 TractionX. All rights reserved.

## Environment Setup

### Docker Environment Variables

The application uses environment variables that are baked into the Docker image at build time. These are configured via:

1. **Build Arguments**: Defined in `Dockerfile` as `ARG` instructions
2. **Environment Variables**: Set in the container using the build args
3. **Docker Compose**: Passes values from `.env` file or uses defaults

#### Required Environment Variables

Create a `.env` file in the `ai/` directory with:

```bash
# Database Connection URLs
MONGODB_URL=mongodb+srv://prasanna:<EMAIL>
MONGODB_DB_NAME=tx_ai
QDRANT_URL=https://b423b15b-97b6-4fea-a932-592403dd8b92.us-west-1-0.aws.cloud.qdrant.io
REDIS_URL=redis://default:<EMAIL>:14410
```

#### How It Works

1. The `Dockerfile` declares `ARG` instructions for each environment variable
2. These are converted to `ENV` variables inside the container
3. `docker-compose.yml` passes these values as build arguments
4. If no `.env` file exists, fallback defaults are used

This approach ensures:
- Secrets are not hardcoded in the compose file
- Environment variables are baked into the image at build time
- Easy configuration management across environments
