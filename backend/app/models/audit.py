from datetime import datetime
from typing import Optional, Dict

from pydantic import Field

from app.models.base import TractionXModel
from app.utils.common import ObjectIdField


class AuditLog(TractionXModel):
    """
    Represents an audit log entry for tracking user actions and changes to entities in the system.
    """
    id: ObjectIdField = Field(default_factory=ObjectIdField, alias="_id")
    user_id: ObjectIdField
    action: str  # login, logout, create, update, delete, etc.
    entity_type: str  # user, role, form, deal, etc.
    entity_id: ObjectIdField
    timestamp: int = Field(default_factory=lambda: int(
        datetime.utcnow().timestamp()))
    metadata: Optional[Dict] = None  # additional context
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
