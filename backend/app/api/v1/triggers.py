from typing import Dict, Any, List, Optional
from fastapi import Depends, HTTPException, status, Query
from pydantic import BaseModel, Field

from app.services.trigger.interfaces import TriggerType, TriggerStatus
from app.services.factory import get_trigger_service
from app.dependencies.auth import get_current_user
from app.dependencies.org import get_org_context
from app.api.base import BaseAPIRouter
from app.utils.rbac.rbac import rbac_register
from app.models.user import User

router = BaseAPIRouter(prefix="/triggers", tags=["triggers"])


class CreateTriggerConfigRequest(BaseModel):
    resource_type: str = Field(..., description="Type of resource (e.g., 'form', 'report')")
    resource_id: str = Field(..., description="ID of the resource")
    type: TriggerType = Field(..., description="Type of trigger")
    name: str = Field(..., description="Name of the trigger")
    description: Optional[str] = Field(None, description="Description of the trigger")
    enabled: bool = Field(True, description="Whether the trigger is enabled")
    config: Dict[str, Any] = Field(default_factory=dict, description="Type-specific configuration")
    conditions: Dict[str, Any] = Field(default_factory=dict, description="Conditions for when to execute")
    retry_config: Optional[Dict[str, Any]] = Field(None, description="Retry configuration")


class UpdateTriggerConfigRequest(BaseModel):
    name: Optional[str] = Field(None, description="Name of the trigger")
    description: Optional[str] = Field(None, description="Description of the trigger")
    enabled: Optional[bool] = Field(None, description="Whether the trigger is enabled")
    config: Optional[Dict[str, Any]] = Field(None, description="Type-specific configuration")
    conditions: Optional[Dict[str, Any]] = Field(None, description="Conditions for when to execute")
    retry_config: Optional[Dict[str, Any]] = Field(None, description="Retry configuration")


class ExecuteTriggerRequest(BaseModel):
    event_data: Dict[str, Any] = Field(..., description="Data for the trigger execution")


class ProcessResourceEventRequest(BaseModel):
    event_type: str = Field(..., description="Type of event")
    event_data: Dict[str, Any] = Field(..., description="Data for the event")


@router.post("/configs", status_code=status.HTTP_201_CREATED)
@rbac_register(resource="triggers", action="create", group="Triggers", description="Create trigger configuration")
async def create_trigger_config(
    request: CreateTriggerConfigRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    trigger_service = Depends(get_trigger_service)
) -> Dict[str, Any]:
    """
    Create a trigger configuration.
    """
    org_id, is_cross_org = org_context
    
    try:
        config = await trigger_service.create_trigger_config(
            resource_type=request.resource_type,
            resource_id=request.resource_id,
            org_id=org_id,
            type=request.type,
            name=request.name,
            description=request.description,
            enabled=request.enabled,
            config=request.config,
            conditions=request.conditions,
            retry_config=request.retry_config
        )
        return config
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create trigger configuration: {str(e)}"
        )


@router.get("/configs/{trigger_id}")
@rbac_register(resource="triggers", action="view", group="Triggers", description="Get trigger configuration")
async def get_trigger_config(
    trigger_id: str,
    trigger_service = Depends(get_trigger_service)
) -> Dict[str, Any]:
    """
    Get trigger configuration by ID.
    """
    config = await trigger_service.get_trigger_config(trigger_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Trigger configuration not found"
        )
    return config


@router.put("/configs/{trigger_id}")
@rbac_register(resource="triggers", action="update", group="Triggers", description="Update trigger configuration")
async def update_trigger_config(
    trigger_id: str,
    request: UpdateTriggerConfigRequest,
    trigger_service = Depends(get_trigger_service)
) -> Dict[str, Any]:
    """
    Update a trigger configuration.
    """
    # Filter out None values
    updates = {k: v for k, v in request.dict().items() if v is not None}
    
    if not updates:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No updates provided"
        )
    
    config = await trigger_service.update_trigger_config(trigger_id, updates)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Trigger configuration not found"
        )
    return config


@router.delete("/configs/{trigger_id}")
@rbac_register(resource="triggers", action="delete", group="Triggers", description="Delete trigger configuration")
async def delete_trigger_config(
    trigger_id: str,
    trigger_service = Depends(get_trigger_service)
) -> Dict[str, str]:
    """
    Delete a trigger configuration.
    """
    success = await trigger_service.delete_trigger_config(trigger_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Trigger configuration not found"
        )
    return {"status": "Trigger configuration deleted"}


@router.get("/resources/{resource_type}/{resource_id}")
@rbac_register(resource="triggers", action="view", group="Triggers", description="Get resource triggers")
async def get_resource_triggers(
    resource_type: str,
    resource_id: str,
    trigger_service = Depends(get_trigger_service)
) -> List[Dict[str, Any]]:
    """
    Get all trigger configurations for a resource.
    """
    return await trigger_service.get_resource_triggers(resource_type, resource_id)


@router.post("/configs/{trigger_id}/execute")
@rbac_register(resource="triggers", action="execute", group="Triggers", description="Execute trigger")
async def execute_trigger(
    trigger_id: str,
    request: ExecuteTriggerRequest,
    trigger_service = Depends(get_trigger_service)
) -> Dict[str, Any]:
    """
    Execute a trigger.
    """
    try:
        execution = await trigger_service.execute_trigger(trigger_id, request.event_data)
        return execution
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to execute trigger: {str(e)}"
        )


@router.post("/resources/{resource_type}/{resource_id}/events")
@rbac_register(resource="triggers", action="execute", group="Triggers", description="Process resource event")
async def process_resource_event(
    resource_type: str,
    resource_id: str,
    request: ProcessResourceEventRequest,
    trigger_service = Depends(get_trigger_service)
) -> List[Dict[str, Any]]:
    """
    Process an event for a resource and execute matching triggers.
    """
    try:
        executions = await trigger_service.process_resource_event(
            resource_type=resource_type,
            resource_id=resource_id,
            event_type=request.event_type,
            event_data=request.event_data
        )
        return executions
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process resource event: {str(e)}"
        )


@router.get("/executions")
@rbac_register(resource="triggers", action="view", group="Triggers", description="Get execution history")
async def get_execution_history(
    trigger_id: Optional[str] = None,
    resource_type: Optional[str] = None,
    resource_id: Optional[str] = None,
    status: Optional[TriggerStatus] = None,
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    trigger_service = Depends(get_trigger_service)
) -> List[Dict[str, Any]]:
    """
    Get execution history.
    """
    return await trigger_service.get_execution_history(
        trigger_id=trigger_id,
        resource_type=resource_type,
        resource_id=resource_id,
        status=status,
        limit=limit,
        offset=offset
    )


@router.post("/executions/{execution_id}/retry")
@rbac_register(resource="triggers", action="execute", group="Triggers", description="Retry execution")
async def retry_execution(
    execution_id: str,
    trigger_service = Depends(get_trigger_service)
) -> Dict[str, Any]:
    """
    Retry a failed execution.
    """
    try:
        execution = await trigger_service.retry_execution(execution_id)
        return execution
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retry execution: {str(e)}"
        )
