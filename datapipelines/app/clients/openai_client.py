"""
OpenAI API Client for TractionX Data Pipeline Service.

Handles chat completions with OpenAI GPT models for founder signal generation.
Provides robust error handling, retries, and performance monitoring.
"""

import time
from typing import Any, Dict, List, Optional

from app.clients.base_client import BaseClient
from app.configs.logging import get_logger
from app.configs.settings import settings

logger = get_logger(__name__)


class OpenAIError(Exception):
    """Base exception for OpenAI API errors."""

    pass


class OpenAIRateLimitError(OpenAIError):
    """Raised when rate limit is exceeded."""

    pass


class OpenAITimeoutError(OpenAIError):
    """Raised when request times out."""

    pass


class OpenAIClient(BaseClient):
    """
    Client for OpenAI Chat Completions API.
    Handles founder signal generation with robust error handling and retries.
    """

    def __init__(self, api_key: Optional[str] = None):
        """Initialize the OpenAI client."""
        api_key = api_key or settings.OPENAI_API_KEY
        if not api_key:
            raise ValueError("OpenAI API key is required")

        super().__init__(
            base_url="https://api.openai.com/v1",
            api_key=api_key,
            timeout=60.0,  # Longer timeout for signal generation
            max_retries=3,
            retry_delay=1.0,
            headers={
                "Content-Type": "application/json",
            },
        )

    async def initialize(self) -> None:
        """Initialize the OpenAI client."""
        await super().initialize()
        self.logger.info("OpenAI client initialized")
        return None

    async def cleanup(self) -> None:
        """Clean up client resources."""
        await super().cleanup()
        self.logger.info("OpenAI client cleaned up")

    async def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "gpt-4o",
        max_tokens: int = 2000,
        temperature: float = 0.3,
        stream: bool = False,
    ) -> Dict[str, Any]:
        """
        Create a chat completion request with retries and error handling.

        Args:
            messages: List of message dictionaries with role and content
            model: OpenAI model to use
            max_tokens: Maximum tokens in response
            temperature: Response randomness (0.0-2.0)
            stream: Whether to stream the response

        Returns:
            completion_data: Full completion response with performance metadata

        Raises:
            OpenAIError: For API errors
            OpenAIRateLimitError: For rate limit issues
            OpenAITimeoutError: For timeout issues
        """
        start_time = time.time()

        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream,
            "presence_penalty": 0,
            "frequency_penalty": 0,
        }

        try:
            # Use base client's post method
            result = await self.post("/chat/completions", data=payload)

            if not result["success"]:
                # Handle specific error cases
                error = result.get("error", "")
                status_code = result.get("status_code", 0)

                if status_code == 429:
                    raise OpenAIRateLimitError("Rate limit exceeded")
                elif status_code == 400:
                    raise OpenAIError(f"Bad request: {error}")
                elif status_code == 401:
                    raise OpenAIError("Invalid API key")
                elif status_code == 403:
                    raise OpenAIError("Access forbidden. Check your API permissions.")
                elif status_code >= 500:
                    raise OpenAIError(f"OpenAI server error ({status_code})")
                else:
                    raise OpenAIError(f"API error {status_code}: {error}")

            # Add performance metadata
            response_data = result["data"]
            response_time_ms = int((time.time() - start_time) * 1000)
            response_data["_performance"] = {
                "response_time_ms": response_time_ms,
                "model": model,
                "attempts": result.get("attempts", 1),
            }

            if not response_data.get("choices"):
                raise OpenAIError("No response choices returned from OpenAI API")

            self.logger.info(
                "Completed OpenAI chat completion",
                model=model,
                response_time_ms=response_time_ms,
                attempts=result.get("attempts", 1),
                tokens_used=response_data.get("usage", {}).get("total_tokens", 0),
            )
            return response_data

        except (OpenAIError, OpenAIRateLimitError, OpenAITimeoutError):
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error during completion: {e}")
            raise OpenAIError(f"Failed to create completion: {str(e)}")

    def extract_content_from_completion(self, completion_data: Dict[str, Any]) -> str:
        """
        Extract content from OpenAI completion response.

        Args:
            completion_data: Response from create_chat_completion

        Returns:
            Extracted content string
        """
        try:
            choices = completion_data.get("choices", [])
            if choices:
                message = choices[0].get("message", {})
                content = message.get("content", "")
                return content

            return "No response generated"

        except Exception as e:
            self.logger.error(f"Error extracting content from completion: {e}")
            return "Error processing response"

    def extract_performance_metadata(
        self, completion_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Extract performance metadata from completion response.

        Args:
            completion_data: Response from create_chat_completion

        Returns:
            Performance metadata dictionary
        """
        try:
            performance = completion_data.get("_performance", {})
            usage = completion_data.get("usage", {})

            return {
                "response_time_ms": performance.get("response_time_ms"),
                "token_count": usage.get("total_tokens"),
                "model": performance.get("model"),
                "attempts": performance.get("attempts", 1),
            }

        except Exception as e:
            self.logger.error(f"Error extracting performance metadata: {e}")
            return {}

    async def generate_founder_signals(
        self,
        system_prompt: str,
        user_prompt: str,
        model: str = "gpt-4o",
        temperature: float = 0.3,
    ) -> Optional[str]:
        """
        Generate founder signals using OpenAI.

        Args:
            system_prompt: System prompt for the model
            user_prompt: User prompt with founder data
            model: OpenAI model to use
            temperature: Response randomness

        Returns:
            Generated signals text or None if failed
        """
        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ]

            completion = await self.create_chat_completion(
                messages=messages,
                model=model,
                max_tokens=2000,
                temperature=temperature,
            )

            return self.extract_content_from_completion(completion)

        except Exception as e:
            self.logger.error(f"Failed to generate founder signals: {e}")
            return None

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the OpenAI service.

        Returns:
            Dictionary with health status
        """
        try:
            # Try a simple request to check if the service is accessible
            result = await self.get("/models")

            return {
                "success": result["success"],
                "service": "openai",
                "status": "healthy" if result["success"] else "unhealthy",
                "error": result.get("error") if not result["success"] else None,
            }
        except Exception as e:
            return {
                "success": False,
                "service": "openai",
                "status": "unhealthy",
                "error": str(e),
            }
