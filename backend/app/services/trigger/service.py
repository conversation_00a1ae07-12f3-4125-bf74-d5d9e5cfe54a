import time
import json
import hmac
import hashlib
import asyncio
from typing import Any, Dict, List, Optional, Type, Callable
import importlib

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.config import settings
from app.services.base import BaseService
from app.services.trigger.interfaces import (
    TriggerServiceInterface,
    TriggerType,
    TriggerStatus
)
from app.core.logging import get_logger
from app.services.queue.interfaces import JobPriority, QueueType
from app.services.factory import get_queue_service

logger = get_logger(__name__)


class TriggerService(BaseService, TriggerServiceInterface):
    """Implementation of the trigger service."""

    def __init__(self, db=None):
        super().__init__()
        self.db = db
        self.queue_service = None
        self.processors = {}
        self._register_default_processors()

    async def initialize(self) -> None:
        """Initialize service resources."""
        if self.db is None:
            from app.core.database import get_database
            async for db in get_database():
                self.db = db
                break

        self.queue_service = await get_queue_service()
        self.logger.info("Trigger service initialized")

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        pass

    def _timestamp_ms(self) -> int:
        """Get current timestamp in milliseconds."""
        return int(time.time() * 1000)

    def _register_default_processors(self) -> None:
        """Register default trigger processors."""
        self.register_processor(TriggerType.EMAIL_NOTIFICATION, self._process_email_notification)
        self.register_processor(TriggerType.IN_APP_NOTIFICATION, self._process_in_app_notification)
        self.register_processor(TriggerType.THESIS_FORM, self._process_thesis_form)
        self.register_processor(TriggerType.WEBHOOK, self._process_webhook)

    def register_processor(
        self,
        trigger_type: TriggerType,
        processor: Callable[[Dict[str, Any], Dict[str, Any]], Dict[str, Any]]
    ) -> None:
        """
        Register a processor for a trigger type.

        Args:
            trigger_type: Type of trigger
            processor: Function to process the trigger
        """
        self.processors[trigger_type.value] = processor
        self.logger.info(f"Registered processor for trigger type: {trigger_type.value}")

    def register_module(self, module_path: str) -> None:
        """
        Register all processors from a module.

        The module should have a PROCESSORS dictionary mapping trigger types to processor functions.

        Args:
            module_path: Import path to the module
        """
        try:
            module = importlib.import_module(module_path)
            if hasattr(module, 'PROCESSORS') and isinstance(module.PROCESSORS, dict):
                for trigger_type, processor in module.PROCESSORS.items():
                    if isinstance(trigger_type, str):
                        trigger_type = TriggerType(trigger_type)
                    self.register_processor(trigger_type, processor)
                self.logger.info(f"Registered processors from module: {module_path}")
            else:
                self.logger.warning(f"Module {module_path} does not have a PROCESSORS dictionary")
        except ImportError as e:
            self.logger.error(f"Failed to import module {module_path}: {str(e)}")

    async def create_trigger_config(
        self,
        resource_type: str,
        resource_id: str,
        org_id: str,
        type: TriggerType,
        name: str,
        description: Optional[str] = None,
        enabled: bool = True,
        config: Optional[Dict[str, Any]] = None,
        conditions: Optional[Dict[str, Any]] = None,
        retry_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a trigger configuration."""
        try:
            # Create trigger config
            now = self._timestamp_ms()
            trigger_config = {
                "resource_type": resource_type,
                "resource_id": ObjectId(resource_id),
                "org_id": ObjectId(org_id),
                "type": type.value,
                "name": name,
                "description": description,
                "enabled": enabled,
                "config": config or {},
                "conditions": conditions or {},
                "retry_config": retry_config or {
                    "max_attempts": 3,
                    "backoff_factor": 2,
                    "max_backoff": 3600  # 1 hour in seconds
                },
                "created_at": now,
                "updated_at": now
            }

            # Insert into database
            result = await self.db.trigger_configs.insert_one(trigger_config)
            trigger_config["_id"] = result.inserted_id

            self.logger.info(f"Created trigger config for {resource_type} {resource_id}")
            return trigger_config
        except Exception as e:
            await self.handle_error(e, {
                "resource_type": resource_type,
                "resource_id": resource_id,
                "type": type.value,
                "error": "Failed to create trigger config"
            })

    async def get_trigger_config(
        self,
        trigger_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get trigger configuration by ID."""
        try:
            return await self.db.trigger_configs.find_one({"_id": ObjectId(trigger_id)})
        except Exception as e:
            await self.handle_error(e, {
                "trigger_id": trigger_id,
                "error": "Failed to get trigger config"
            })

    async def update_trigger_config(
        self,
        trigger_id: str,
        updates: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Update a trigger configuration."""
        try:
            # Add updated_at timestamp
            updates["updated_at"] = self._timestamp_ms()

            # Update in database
            result = await self.db.trigger_configs.find_one_and_update(
                {"_id": ObjectId(trigger_id)},
                {"$set": updates},
                return_document=True
            )

            if result:
                self.logger.info(f"Updated trigger config {trigger_id}")

            return result
        except Exception as e:
            await self.handle_error(e, {
                "trigger_id": trigger_id,
                "updates": updates,
                "error": "Failed to update trigger config"
            })

    async def delete_trigger_config(
        self,
        trigger_id: str
    ) -> bool:
        """Delete a trigger configuration."""
        try:
            # Delete from database
            result = await self.db.trigger_configs.delete_one({"_id": ObjectId(trigger_id)})

            if result.deleted_count > 0:
                self.logger.info(f"Deleted trigger config {trigger_id}")
                return True

            return False
        except Exception as e:
            await self.handle_error(e, {
                "trigger_id": trigger_id,
                "error": "Failed to delete trigger config"
            })

    async def get_resource_triggers(
        self,
        resource_type: str,
        resource_id: str
    ) -> List[Dict[str, Any]]:
        """Get all trigger configurations for a resource."""
        try:
            cursor = self.db.trigger_configs.find({
                "resource_type": resource_type,
                "resource_id": ObjectId(resource_id)
            })
            return await cursor.to_list(None)
        except Exception as e:
            await self.handle_error(e, {
                "resource_type": resource_type,
                "resource_id": resource_id,
                "error": "Failed to get resource triggers"
            })

    async def execute_trigger(
        self,
        trigger_id: str,
        event_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute a trigger."""
        try:
            # Get trigger config
            trigger_config = await self.get_trigger_config(trigger_id)
            if not trigger_config:
                raise ValueError(f"Trigger config {trigger_id} not found")

            # Check if trigger is enabled
            if not trigger_config.get("enabled", True):
                self.logger.info(f"Trigger {trigger_id} is disabled, skipping execution")
                return {
                    "trigger_id": trigger_id,
                    "status": TriggerStatus.FAILED.value,
                    "error": "Trigger is disabled",
                    "execution_time": 0
                }

            # Check conditions
            if not self._check_conditions(trigger_config.get("conditions", {}), event_data):
                self.logger.info(f"Trigger {trigger_id} conditions not met, skipping execution")
                return {
                    "trigger_id": trigger_id,
                    "status": TriggerStatus.FAILED.value,
                    "error": "Conditions not met",
                    "execution_time": 0
                }

            # Create execution record
            now = self._timestamp_ms()
            execution = {
                "trigger_id": ObjectId(trigger_id),
                "resource_type": trigger_config["resource_type"],
                "resource_id": trigger_config["resource_id"],
                "org_id": trigger_config["org_id"],
                "status": TriggerStatus.PENDING.value,
                "event_data": event_data,
                "execution_time": 0,
                "result": None,
                "error": None,
                "retry_count": 0,
                "next_retry_at": None,
                "created_at": now,
                "updated_at": now
            }

            # Insert execution record
            result = await self.db.trigger_executions.insert_one(execution)
            execution["_id"] = result.inserted_id

            # Queue the execution
            await self.queue_service.enqueue_job(
                job_type="execute_trigger",
                payload={
                    "execution_id": str(execution["_id"]),
                    "trigger_id": trigger_id,
                    "trigger_type": trigger_config["type"],
                    "event_data": event_data,
                    "config": trigger_config["config"]
                },
                priority=JobPriority.HIGH,
                retry_config=trigger_config.get("retry_config")
            )

            self.logger.info(f"Queued execution of trigger {trigger_id}")
            return execution
        except Exception as e:
            await self.handle_error(e, {
                "trigger_id": trigger_id,
                "error": "Failed to execute trigger"
            })

    async def process_resource_event(
        self,
        resource_type: str,
        resource_id: str,
        event_type: str,
        event_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Process an event for a resource and execute matching triggers."""
        try:
            # Get all triggers for this resource
            triggers = await self.get_resource_triggers(resource_type, resource_id)

            # Add event type to event data
            event_data["event_type"] = event_type

            # Execute each trigger
            executions = []
            for trigger in triggers:
                # Check if trigger should be executed for this event type
                if event_type in trigger.get("conditions", {}).get("event_types", [event_type]):
                    execution = await self.execute_trigger(str(trigger["_id"]), event_data)
                    executions.append(execution)

            return executions
        except Exception as e:
            await self.handle_error(e, {
                "resource_type": resource_type,
                "resource_id": resource_id,
                "event_type": event_type,
                "error": "Failed to process resource event"
            })

    async def get_execution_history(
        self,
        trigger_id: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        status: Optional[TriggerStatus] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get execution history."""
        try:
            # Build query
            query = {}
            if trigger_id:
                query["trigger_id"] = ObjectId(trigger_id)
            if resource_type:
                query["resource_type"] = resource_type
            if resource_id:
                query["resource_id"] = ObjectId(resource_id)
            if status:
                query["status"] = status.value

            # Get executions
            cursor = self.db.trigger_executions.find(query).sort(
                "created_at", -1
            ).skip(offset).limit(limit)

            return await cursor.to_list(None)
        except Exception as e:
            await self.handle_error(e, {
                "trigger_id": trigger_id,
                "resource_type": resource_type,
                "resource_id": resource_id,
                "status": status.value if status else None,
                "error": "Failed to get execution history"
            })

    async def retry_execution(
        self,
        execution_id: str
    ) -> Dict[str, Any]:
        """Retry a failed execution."""
        try:
            # Get execution
            execution = await self.db.trigger_executions.find_one({"_id": ObjectId(execution_id)})
            if not execution:
                raise ValueError(f"Execution {execution_id} not found")

            # Check if execution can be retried
            if execution["status"] != TriggerStatus.FAILED.value:
                raise ValueError(f"Execution {execution_id} is not in failed state")

            # Get trigger config
            trigger_id = str(execution["trigger_id"])
            trigger_config = await self.get_trigger_config(trigger_id)
            if not trigger_config:
                raise ValueError(f"Trigger config {trigger_id} not found")

            # Update execution
            now = self._timestamp_ms()
            retry_count = execution.get("retry_count", 0) + 1

            updates = {
                "status": TriggerStatus.RETRYING.value,
                "retry_count": retry_count,
                "updated_at": now
            }

            result = await self.db.trigger_executions.find_one_and_update(
                {"_id": ObjectId(execution_id)},
                {"$set": updates},
                return_document=True
            )

            # Queue the execution
            await self.queue_service.enqueue_job(
                job_type="execute_trigger",
                payload={
                    "execution_id": execution_id,
                    "trigger_id": trigger_id,
                    "trigger_type": trigger_config["type"],
                    "event_data": execution["event_data"],
                    "config": trigger_config["config"]
                },
                priority=JobPriority.HIGH,
                retry_config=trigger_config.get("retry_config")
            )

            self.logger.info(f"Queued retry of execution {execution_id}")
            return result
        except Exception as e:
            await self.handle_error(e, {
                "execution_id": execution_id,
                "error": "Failed to retry execution"
            })

    def _check_conditions(
        self,
        conditions: Dict[str, Any],
        event_data: Dict[str, Any]
    ) -> bool:
        """
        Check if conditions are met for a trigger.

        Args:
            conditions: Conditions to check
            event_data: Event data to check against

        Returns:
            True if conditions are met, False otherwise
        """
        # If no conditions, always execute
        if not conditions:
            return True

        # Check event type
        if "event_types" in conditions and event_data.get("event_type") not in conditions["event_types"]:
            return False

        # Check field conditions
        if "fields" in conditions:
            for field, condition in conditions["fields"].items():
                # Get field value from event data
                field_value = event_data.get(field)

                # Check condition
                if "eq" in condition and field_value != condition["eq"]:
                    return False
                if "neq" in condition and field_value == condition["neq"]:
                    return False
                if "gt" in condition and (field_value is None or field_value <= condition["gt"]):
                    return False
                if "gte" in condition and (field_value is None or field_value < condition["gte"]):
                    return False
                if "lt" in condition and (field_value is None or field_value >= condition["lt"]):
                    return False
                if "lte" in condition and (field_value is None or field_value > condition["lte"]):
                    return False
                if "in" in condition and field_value not in condition["in"]:
                    return False
                if "nin" in condition and field_value in condition["nin"]:
                    return False
                if "exists" in condition and (condition["exists"] and field_value is None or not condition["exists"] and field_value is not None):
                    return False

        # All conditions met
        return True

    async def _process_email_notification(
        self,
        config: Dict[str, Any],
        event_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process an email notification trigger.

        Args:
            config: Trigger configuration
            event_data: Event data

        Returns:
            Result of the email notification
        """
        try:
            # Get email service
            from app.services.factory import get_email_service
            email_service = await get_email_service()

            # Get template and recipient
            template_id = config.get("template_id")
            recipient_field = config.get("recipient_field", "email")
            recipient = event_data.get(recipient_field)

            if not template_id:
                raise ValueError("Template ID not specified in trigger config")

            if not recipient:
                raise ValueError(f"Recipient not found in event data field: {recipient_field}")

            # Send email
            result = await email_service.send_template_email(
                recipient,
                template_id,
                event_data
            )

            return {
                "success": True,
                "message_id": result.get("message_id"),
                "recipient": recipient
            }
        except Exception as e:
            self.logger.error(f"Error processing email notification: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _process_in_app_notification(
        self,
        config: Dict[str, Any],
        event_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process an in-app notification trigger.

        Args:
            config: Trigger configuration
            event_data: Event data

        Returns:
            Result of the in-app notification
        """
        try:
            # Get notification service
            # This would be implemented in a real application
            # For now, we'll just simulate it

            # Get recipient and message
            recipient_field = config.get("recipient_field", "user_id")
            recipient = event_data.get(recipient_field)

            if not recipient:
                raise ValueError(f"Recipient not found in event data field: {recipient_field}")

            # Create notification
            notification = {
                "user_id": recipient,
                "title": config.get("title", "Notification"),
                "message": config.get("message", "You have a new notification"),
                "data": event_data,
                "created_at": self._timestamp_ms()
            }

            # In a real implementation, this would be saved to the database
            # and potentially sent to the user in real-time

            return {
                "success": True,
                "notification_id": "simulated-notification-id",
                "recipient": recipient
            }
        except Exception as e:
            self.logger.error(f"Error processing in-app notification: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _process_thesis_form(
        self,
        config: Dict[str, Any],
        event_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process a thesis form trigger.

        Args:
            config: Trigger configuration
            event_data: Event data

        Returns:
            Result of the thesis form processing
        """
        try:
            # Get thesis service
            from app.services.factory import get_thesis_service
            thesis_service = await get_thesis_service()

            # Get thesis ID and form data
            thesis_id = config.get("thesis_id")
            form_data = event_data.get("answers", {})

            if not thesis_id:
                raise ValueError("Thesis ID not specified in trigger config")

            # Process thesis form
            # In a real implementation, this would update the thesis with the form data
            result = await thesis_service.update_thesis(
                thesis_id,
                {"form_data": form_data}
            )

            return {
                "success": True,
                "thesis_id": thesis_id,
                "updated": True
            }
        except Exception as e:
            self.logger.error(f"Error processing thesis form: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _process_webhook(
        self,
        config: Dict[str, Any],
        event_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process a webhook trigger.

        Args:
            config: Trigger configuration
            event_data: Event data

        Returns:
            Result of the webhook processing
        """
        try:
            import aiohttp

            # Get webhook URL and method
            url = config.get("url")
            method = config.get("method", "POST").upper()
            headers = config.get("headers", {})

            if not url:
                raise ValueError("Webhook URL not specified in trigger config")

            # Add signature if enabled
            if config.get("sign_payload", False):
                payload_str = json.dumps(event_data)
                signature = self._generate_webhook_signature(payload_str, config.get("secret", ""))
                headers["X-Webhook-Signature"] = signature

            # Send webhook request
            async with aiohttp.ClientSession() as session:
                if method == "GET":
                    async with session.get(url, params=event_data, headers=headers) as response:
                        status = response.status
                        response_text = await response.text()
                elif method == "POST":
                    async with session.post(url, json=event_data, headers=headers) as response:
                        status = response.status
                        response_text = await response.text()
                else:
                    raise ValueError(f"Unsupported webhook method: {method}")

            return {
                "success": status >= 200 and status < 300,
                "status_code": status,
                "response": response_text
            }
        except Exception as e:
            self.logger.error(f"Error processing webhook: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    def _generate_webhook_signature(self, payload: str, secret: str) -> str:
        """
        Generate a signature for a webhook payload.

        Args:
            payload: Payload to sign
            secret: Secret to use for signing

        Returns:
            Signature
        """
        return hmac.new(
            secret.encode(),
            payload.encode(),
            hashlib.sha256
        ).hexdigest()
