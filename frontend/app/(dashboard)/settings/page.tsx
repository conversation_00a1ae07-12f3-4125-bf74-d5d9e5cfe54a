"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function SettingsPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to profile tab by default
    router.replace('/settings/profile')
  }, [router])

  return (
    <div className="safe-top safe-bottom flex min-h-screen items-center justify-center">
      <div className="text-center">
        <p className="text-muted-foreground">Redirecting to settings...</p>
      </div>
    </div>
  )
}


