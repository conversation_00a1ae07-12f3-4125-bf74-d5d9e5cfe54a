"""
Base pipeline classes for TractionX Data Pipeline Service.
"""

import time
from abc import ABC, abstractmethod
from typing import Any, Dict

from app.configs.logging import get_logger
from app.models.base import ProcessingResult


class BasePipeline(ABC):
    """Base class for all data pipelines."""

    def __init__(self, pipeline_name: str):
        self.pipeline_name = pipeline_name
        self.logger = get_logger(f"datapipelines.pipeline.{pipeline_name}")
        self.initialized = False

    async def initialize(self) -> None:
        """Initialize the pipeline."""
        if self.initialized:
            return

        self.logger.info("Initializing pipeline")
        await self._initialize_pipeline()
        self.initialized = True
        self.logger.info("Pipeline initialized successfully")

    async def cleanup(self) -> None:
        """Clean up pipeline resources."""
        if not self.initialized:
            return

        self.logger.info("Cleaning up pipeline")
        await self._cleanup_pipeline()
        self.initialized = False
        self.logger.info("Pipeline cleaned up")

    async def process(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """
        Process input data through the pipeline.

        Args:
            input_data: Input data to process

        Returns:
            ProcessingResult with success status and output data
        """
        if not self.initialized:
            await self.initialize()

        start_time = time.time()

        # Log pipeline start
        self.logger.info(
            "Pipeline started",
            input_data_keys=list(input_data.keys()),
            input_size=len(str(input_data)),
        )

        try:
            # Validate input
            validation_result = await self._validate_input(input_data)
            if not validation_result.success:
                return validation_result

            # Process data
            result = await self._process_data(input_data)

            # Calculate processing time
            processing_time = time.time() - start_time
            result.processing_time = processing_time

            if result.success:
                self.logger.info(
                    "Pipeline completed successfully",
                    output_data_keys=list(result.data.keys()) if result.data else [],
                    output_size=len(str(result.data)) if result.data else 0,
                    duration_seconds=processing_time,
                )
            else:
                self.logger.error(
                    "Pipeline failed",
                    error_type=result.error_type,
                    error_message=result.error_message,
                    duration_seconds=processing_time,
                )

            return result

        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(
                "Pipeline failed",
                error_type=type(e).__name__,
                error_message=str(e),
                duration_seconds=processing_time,
                exc_info=True,
            )

            return ProcessingResult.error_result(
                error_message=str(e),
                error_type=type(e).__name__,
                processing_time=processing_time,
            )

    @abstractmethod
    async def _initialize_pipeline(self) -> None:
        """Initialize pipeline-specific resources."""
        pass

    @abstractmethod
    async def _cleanup_pipeline(self) -> None:
        """Clean up pipeline-specific resources."""
        pass

    @abstractmethod
    async def _validate_input(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """
        Validate input data.

        Args:
            input_data: Input data to validate

        Returns:
            ProcessingResult indicating validation success/failure
        """
        pass

    @abstractmethod
    async def _process_data(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """
        Process the input data.

        Args:
            input_data: Validated input data

        Returns:
            ProcessingResult with processed data
        """
        pass

    def _get_required_fields(self) -> list[str]:
        """Get list of required input fields."""
        return []

    def _validate_required_fields(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """Validate that all required fields are present."""
        required_fields = self._get_required_fields()
        missing_fields = []

        for field in required_fields:
            if field not in input_data or input_data[field] is None:
                missing_fields.append(field)

        if missing_fields:
            error_msg = f"Missing required fields: {', '.join(missing_fields)}"
            return ProcessingResult.error_result(
                error_message=error_msg, error_type="ValidationError"
            )

        return ProcessingResult.success_result({})

    async def _make_api_call(
        self, service_name: str, endpoint: str, method: str = "GET", **kwargs
    ) -> Dict[str, Any]:
        """
        Make an API call with logging and error handling.

        Args:
            service_name: Name of the external service
            endpoint: API endpoint
            method: HTTP method
            **kwargs: Additional arguments for the HTTP client

        Returns:
            API response data
        """
        import httpx

        start_time = time.time()

        try:
            async with httpx.AsyncClient() as client:
                response = await client.request(method, endpoint, **kwargs)

            duration = time.time() - start_time

            # Log API call
            self.logger.info(
                "External API call",
                service=service_name,
                endpoint=endpoint,
                status_code=response.status_code,
                duration_seconds=duration,
            )

            # Check for HTTP errors
            response.raise_for_status()

            # Return JSON response
            return response.json()

        except httpx.HTTPStatusError as e:
            duration = time.time() - start_time
            self.logger.error(
                "API call failed",
                service=service_name,
                endpoint=endpoint,
                status_code=e.response.status_code,
                duration_seconds=duration,
            )
            raise
        except Exception as e:
            duration = time.time() - start_time
            self.logger.error(
                "API call failed",
                service=service_name,
                endpoint=endpoint,
                error=str(e),
                duration_seconds=duration,
            )
            raise

    def _store_raw_data(self, data: Dict[str, Any], key: str) -> None:
        """Store raw data for audit/debugging purposes."""
        # This would typically store to S3 or similar
        # For now, just log the operation
        self.logger.info(
            "Storage operation",
            operation="store",
            storage_type="raw_data",
            key=key,
            success=True,
        )

    def _calculate_confidence_score(self, data: Dict[str, Any]) -> float:
        """Calculate confidence score for the processed data."""
        # Basic confidence calculation - can be overridden by subclasses
        if not data:
            return 0.0

        # Count non-null fields
        non_null_fields = sum(1 for value in data.values() if value is not None)
        total_fields = len(data)

        if total_fields == 0:
            return 0.0

        return non_null_fields / total_fields
