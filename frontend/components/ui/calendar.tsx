"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import {
  DayPicker,
  useNavigation,
  useDayPicker,
  type CaptionProps,
} from "react-day-picker"

import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export type CalendarProps = React.ComponentProps<typeof DayPicker>

function CalendarCaption({ displayMonth }: CaptionProps) {
  const { goToMonth, nextMonth, previousMonth } = useNavigation()
  const { fromYear, toYear } = useDayPicker()

  const years: number[] = []
  const start = fromYear || new Date().getFullYear() - 100
  const end = toYear || new Date().getFullYear()
  for (let i = start; i <= end; i++) {
    years.push(i)
  }

  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ]

  const handleMonthChange = (value: string) => {
    const month = parseInt(value, 10)
    const newDate = new Date(displayMonth)
    newDate.setMonth(month)
    goToMonth(newDate)
  }

  const handleYearChange = (value: string) => {
    const year = parseInt(value, 10)
    const newDate = new Date(displayMonth)
    newDate.setFullYear(year)
    goToMonth(newDate)
  }

  return (
    <div className="flex items-center justify-between px-1 py-2">
      <div className="flex items-center gap-1">
        <Select
          onValueChange={handleMonthChange}
          value={String(displayMonth.getMonth())}
        >
          <SelectTrigger className="w-[120px] focus:ring-0">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {months.map((month, index) => (
              <SelectItem key={month} value={String(index)}>
                {month}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select
          onValueChange={handleYearChange}
          value={String(displayMonth.getFullYear())}
        >
          <SelectTrigger className="w-[80px] focus:ring-0">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {years.map(year => (
              <SelectItem key={year} value={String(year)}>
                {year}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className="flex items-center gap-1">
        <button
          type="button"
          disabled={!previousMonth}
          onClick={() => previousMonth && goToMonth(previousMonth)}
          className={cn(buttonVariants({ variant: "outline" }), "size-7 p-0")}
        >
          <ChevronLeft className="size-4" />
        </button>
        <button
          type="button"
          disabled={!nextMonth}
          onClick={() => nextMonth && goToMonth(nextMonth)}
          className={cn(buttonVariants({ variant: "outline" }), "size-7 p-0")}
        >
          <ChevronRight className="size-4" />
        </button>
      </div>
    </div>
  )
}

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn("p-3", className)}
      classNames={{
        months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
        month: "space-y-4",
        table: "w-full border-collapse space-y-1 mt-4",
        head_row: "flex",
        head_cell:
          "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",
        row: "flex w-full mt-2",
        cell: "text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
        day: cn(
          buttonVariants({ variant: "ghost" }),
          "size-9 p-0 font-normal aria-selected:opacity-100"
        ),
        day_selected:
          "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
        day_today: "bg-accent text-accent-foreground",
        day_outside: "text-muted-foreground opacity-50",
        day_disabled: "text-muted-foreground opacity-50",
        day_range_middle:
          "aria-selected:bg-accent aria-selected:text-accent-foreground",
        day_hidden: "invisible",
        ...classNames,
      }}
      components={{
        Caption: CalendarCaption,
      }}
      fromYear={new Date().getFullYear() - 100}
      toYear={new Date().getFullYear()}
      {...props}
    />
  )
}
Calendar.displayName = "Calendar"

export { Calendar }
