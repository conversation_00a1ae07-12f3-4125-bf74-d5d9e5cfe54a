import { useCallback, useState } from 'react';
import { Editor, Range, Transforms, BaseEditor } from 'slate';
import { ReactEditor } from 'slate-react';
import { HistoryEditor } from 'slate-history';
import { OrgUser, MentionElement } from '@/lib/types/deal-notes';
import { Text } from 'slate';

export function useMentionPlugin(editor: BaseEditor & ReactEditor & HistoryEditor, orgUsers: OrgUser[]) {
  const [target, setTarget] = useState<Range | null>(null);
  const [search, setSearch] = useState('');
  const [index, setIndex] = useState(0);

  // Filter users by search string
  const filteredUsers = search
    ? orgUsers.filter(
        (user) =>
          user.name.toLowerCase().includes(search.toLowerCase()) ||
          user.email.toLowerCase().includes(search.toLowerCase())
      )
    : orgUsers;

  // Insert mention node at current selection
  const onMentionSelect = useCallback(
    (user: OrgU<PERSON>) => {
      if (!target) return;
      try {
        // Find the last '@' in the current text node before the cursor and delete it (and any search text) before inserting the mention
        const { anchor } = editor.selection || {};
        if (!anchor) return;
        const [node, path] = Editor.node(editor, anchor.path);
        if (Text.isText(node)) {
          const text = node.text.slice(0, anchor.offset);
          const atIndex = text.lastIndexOf('@');
          if (atIndex !== -1) {
            const from = { path: anchor.path, offset: atIndex };
            const to = anchor;
            const range = { anchor: from, focus: to };
            Transforms.select(editor, range);
            Transforms.delete(editor);
          }
        }
        // Insert the mention node
        const mention: MentionElement = {
          type: 'mention',
          user_id: user._id,
          name: user.name,
          children: [{ text: '' }],
        };
        Transforms.insertNodes(editor, mention);
        Transforms.insertText(editor, ' ');
        Transforms.move(editor);
      } catch (error) {
        console.error('Error inserting mention:', error);
      }
      setTarget(null);
      setSearch('');
      setIndex(0);
    },
    [editor, target]
  );

  // Handle keydown for mention dropdown and search
  const onKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      try {
        // Check for @ to start mention mode
        if (event.key === '@' && !target) {
          const { selection } = editor;
          if (selection && Range.isCollapsed(selection)) {
            setTarget(selection);
            setSearch('');
            setIndex(0);
          }
          return;
        }

        if (target && filteredUsers.length > 0) {
          switch (event.key) {
            case 'ArrowDown':
              event.preventDefault();
              setIndex((i) => (i + 1) % filteredUsers.length);
              break;
            case 'ArrowUp':
              event.preventDefault();
              setIndex((i) => (i - 1 + filteredUsers.length) % filteredUsers.length);
              break;
            case 'Tab':
            case 'Enter':
              event.preventDefault();
              if (filteredUsers[index]) {
                onMentionSelect(filteredUsers[index]);
              }
              break;
            case 'Escape':
              event.preventDefault();
              setTarget(null);
              setSearch('');
              setIndex(0);
              break;
            case 'Backspace':
              if (search.length <= 1) {
                setTarget(null);
                setSearch('');
                setIndex(0);
              } else {
                setSearch((prev) => prev.slice(0, -1));
              }
              break;
            default:
              if (event.key.length === 1 && !event.metaKey && !event.ctrlKey && !event.altKey) {
                setSearch((prev) => prev + event.key);
              }
              break;
          }
        }
      } catch (error) {
        console.error('Error in mention keydown:', error);
        // Reset mention state on error
        setTarget(null);
        setSearch('');
        setIndex(0);
      }
    },
    [target, filteredUsers, index, onMentionSelect, search, editor]
  );

  return {
    target,
    search,
    index,
    setTarget,
    setSearch,
    setIndex,
    filteredUsers,
    onMentionSelect,
    onKeyDown,
  };
} 