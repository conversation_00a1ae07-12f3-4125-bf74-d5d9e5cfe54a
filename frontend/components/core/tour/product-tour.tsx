"use client"

import { useState, useEffect, use<PERSON>allback, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { X, ArrowR<PERSON>, ArrowLeft, Play, SkipForward } from "lucide-react"
import { createPortal } from "react-dom"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

export interface TourStep {
  id: string
  title: string
  content: string
  target: string // CSS selector for the element to highlight
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'center'
  action?: 'click' | 'hover' | 'none'
  waitForElement?: boolean
  onBeforeStep?: () => void
  onAfterStep?: () => void
}

interface ProductTourProps {
  steps: TourStep[]
  isActive: boolean
  onComplete: () => void
  onSkip: () => void
  onClose: () => void
  autoStart?: boolean
}

interface SpotlightProps {
  target: Element
  children: React.ReactNode
}

// Spotlight component that creates a cutout effect
function Spotlight({ target, children }: SpotlightProps) {
  const [position, setPosition] = useState({ x: 0, y: 0, width: 0, height: 0 })

  useEffect(() => {
    const updatePosition = () => {
      const rect = target.getBoundingClientRect()
      setPosition({
        x: rect.left,
        y: rect.top,
        width: rect.width,
        height: rect.height
      })
    }

    updatePosition()
    window.addEventListener('resize', updatePosition)
    window.addEventListener('scroll', updatePosition)

    return () => {
      window.removeEventListener('resize', updatePosition)
      window.removeEventListener('scroll', updatePosition)
    }
  }, [target])

  return createPortal(
    <div className="fixed inset-0 z-[9999] pointer-events-none">
      {/* Dark overlay with cutout */}
      <div 
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        style={{
          clipPath: `polygon(
            0% 0%, 
            0% 100%, 
            ${position.x}px 100%, 
            ${position.x}px ${position.y}px, 
            ${position.x + position.width}px ${position.y}px, 
            ${position.x + position.width}px ${position.y + position.height}px, 
            ${position.x}px ${position.y + position.height}px, 
            ${position.x}px 100%, 
            100% 100%, 
            100% 0%
          )`
        }}
      />
      
      {/* Highlighted border around target */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="absolute border-2 border-purple-400 rounded-lg shadow-lg shadow-purple-400/50"
        style={{
          left: position.x - 4,
          top: position.y - 4,
          width: position.width + 8,
          height: position.height + 8
        }}
      >
        {/* Animated pulse ring */}
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.5, 0.8, 0.5]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute inset-0 border-2 border-purple-300 rounded-lg"
        />
      </motion.div>

      {children}
    </div>,
    document.body
  )
}

// Tooltip component for tour content
interface TooltipProps {
  step: TourStep
  currentStep: number
  totalSteps: number
  onNext: () => void
  onPrev: () => void
  onSkip: () => void
  onClose: () => void
  target: Element
}

function TourTooltip({ 
  step, 
  currentStep, 
  totalSteps, 
  onNext, 
  onPrev, 
  onSkip, 
  onClose,
  target 
}: TooltipProps) {
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const tooltipRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const updatePosition = () => {
      const targetRect = target.getBoundingClientRect()
      const tooltipRect = tooltipRef.current?.getBoundingClientRect()
      
      if (!tooltipRect) return

      let x = 0
      let y = 0

      switch (step.placement || 'bottom') {
        case 'top':
          x = targetRect.left + targetRect.width / 2 - tooltipRect.width / 2
          y = targetRect.top - tooltipRect.height - 16
          break
        case 'bottom':
          x = targetRect.left + targetRect.width / 2 - tooltipRect.width / 2
          y = targetRect.bottom + 16
          break
        case 'left':
          x = targetRect.left - tooltipRect.width - 16
          y = targetRect.top + targetRect.height / 2 - tooltipRect.height / 2
          break
        case 'right':
          x = targetRect.right + 16
          y = targetRect.top + targetRect.height / 2 - tooltipRect.height / 2
          break
        case 'center':
          x = window.innerWidth / 2 - tooltipRect.width / 2
          y = window.innerHeight / 2 - tooltipRect.height / 2
          break
      }

      // Keep tooltip within viewport
      x = Math.max(16, Math.min(x, window.innerWidth - tooltipRect.width - 16))
      y = Math.max(16, Math.min(y, window.innerHeight - tooltipRect.height - 16))

      setPosition({ x, y })
    }

    updatePosition()
    window.addEventListener('resize', updatePosition)
    window.addEventListener('scroll', updatePosition)

    return () => {
      window.removeEventListener('resize', updatePosition)
      window.removeEventListener('scroll', updatePosition)
    }
  }, [step.placement, target])

  return (
    <motion.div
      ref={tooltipRef}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className={cn(
        "fixed z-[10000] pointer-events-auto",
        "bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200",
        "p-6 max-w-sm"
      )}
      style={{
        left: position.x,
        top: position.y
      }}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <Badge variant="secondary" className="text-xs">
            {currentStep} of {totalSteps}
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
          >
            <X className="size-4" />
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="space-y-4">
        <div>
          <h3 className="font-semibold text-gray-900 mb-2">{step.title}</h3>
          <p className="text-sm text-gray-600 leading-relaxed">{step.content}</p>
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center gap-2">
            {currentStep > 1 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onPrev}
                className="flex items-center gap-1 text-xs"
              >
                <ArrowLeft className="size-3" />
                Back
              </Button>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onSkip}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              <SkipForward className="size-3 mr-1" />
              Skip Tour
            </Button>
            
            <Button
              onClick={onNext}
              size="sm"
              className="bg-purple-600 hover:bg-purple-700 text-white text-xs px-4"
            >
              {currentStep === totalSteps ? 'Finish' : 'Next'}
              <ArrowRight className="size-3 ml-1" />
            </Button>
          </div>
        </div>
      </div>

      {/* Progress dots */}
      <div className="flex items-center justify-center gap-1 mt-4 pt-4 border-t border-gray-100">
        {Array.from({ length: totalSteps }, (_, i) => (
          <div
            key={i}
            className={cn(
              "size-2 rounded-full transition-all duration-300",
              i + 1 === currentStep
                ? "bg-purple-600 w-6"
                : i + 1 < currentStep
                  ? "bg-purple-400"
                  : "bg-gray-200"
            )}
          />
        ))}
      </div>
    </motion.div>
  )
}

export function ProductTour({ 
  steps, 
  isActive, 
  onComplete, 
  onSkip, 
  onClose,
  autoStart = false 
}: ProductTourProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  const [targetElement, setTargetElement] = useState<Element | null>(null)
  const [isReady, setIsReady] = useState(false)

  const currentStep = steps[currentStepIndex]

  // Find target element for current step
  useEffect(() => {
    if (!isActive || !currentStep) return

    const findTarget = () => {
      const element = document.querySelector(currentStep.target)
      if (element) {
        setTargetElement(element)
        setIsReady(true)
        
        // Scroll element into view
        element.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center',
          inline: 'center'
        })

        // Execute before step callback
        currentStep.onBeforeStep?.()
      } else if (currentStep.waitForElement) {
        // Wait for element to appear
        const observer = new MutationObserver(() => {
          const element = document.querySelector(currentStep.target)
          if (element) {
            observer.disconnect()
            setTargetElement(element)
            setIsReady(true)
            element.scrollIntoView({ 
              behavior: 'smooth', 
              block: 'center',
              inline: 'center'
            })
            currentStep.onBeforeStep?.()
          }
        })

        observer.observe(document.body, {
          childList: true,
          subtree: true
        })

        return () => observer.disconnect()
      }
    }

    const timer = setTimeout(findTarget, 100)
    return () => clearTimeout(timer)
  }, [currentStep, isActive, currentStepIndex])

  const handleNext = useCallback(() => {
    currentStep?.onAfterStep?.()
    
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(prev => prev + 1)
      setIsReady(false)
    } else {
      onComplete()
    }
  }, [currentStepIndex, steps.length, onComplete, currentStep])

  const handlePrev = useCallback(() => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1)
      setIsReady(false)
    }
  }, [currentStepIndex])

  const handleSkip = useCallback(() => {
    onSkip()
  }, [onSkip])

  const handleClose = useCallback(() => {
    onClose()
  }, [onClose])

  // Auto-start tour
  useEffect(() => {
    if (autoStart && isActive && steps.length > 0) {
      setCurrentStepIndex(0)
    }
  }, [autoStart, isActive, steps.length])

  if (!isActive || !currentStep || !targetElement || !isReady) {
    return null
  }

  return (
    <AnimatePresence>
      <Spotlight target={targetElement}>
        <TourTooltip
          step={currentStep}
          currentStep={currentStepIndex + 1}
          totalSteps={steps.length}
          onNext={handleNext}
          onPrev={handlePrev}
          onSkip={handleSkip}
          onClose={handleClose}
          target={targetElement}
        />
      </Spotlight>
    </AnimatePresence>
  )
}
