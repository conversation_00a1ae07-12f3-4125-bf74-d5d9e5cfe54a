# PDL Data Processing Guide

This document describes the comprehensive PDL (People Data Labs) data processing system that converts enriched JSON into normalized RDS tables for founder intelligence, LLM scoring, and team analysis.

## Overview

The `PDLDataProcessor` class provides a robust, production-ready solution for:

- **Data Validation**: Comprehensive validation of PDL payloads
- **UUID Generation**: Deterministic UUIDv5 generation based on name + LinkedIn URL
- **Data Cleaning**: String normalization, deduplication, and validation
- **Transactional Storage**: Atomic database operations with rollback on failure
- **Error Handling**: Comprehensive error tracking and debugging support
- **S3 Integration**: Raw data backup for audit trails

## Input Format

The processor expects a complete PDL payload structure:

```json
{
  "enrichment_data": {
    "full_name": "trairat singcharoenchai",
    "first_name": "trairat",
    "last_name": "singcharoenchai",
    "job_title": "co-founder & chief operations officer",
    "job_company_name": "tag capital",
    "linkedin_url": "linkedin.com/in/andy-singcharoenchai",
    "location_country": "germany",
    "skills": ["accounting", "ai", "business strategy", ...],
    "experience": [...],
    "education": [...],
    "profiles": [...]
  },
  "metadata": {
    "founder_id": "72631b39ff5e",
    "company_id": "www.tractionx.ai",
    "org_id": "68515ce0e95f5962d46d3be3",
    "founder_name": "<PERSON>",
    "confidence_score": 1.0,
    "enrichment_sources": ["pdl_data"]
  },
  "job_id": "unique_job_id",
  "timestamp": "2025-06-27T14:38:40.879492"
}
```

## ID Strategy

| Field | Generation Method |
|-------|------------------|
| `founder_uuid` | `UUIDv5(NAMESPACE_DNS, "full_name\|linkedin_url")` |
| `founder_id` | Passed from metadata (legacy compatibility) |
| `org_id` | Passed from metadata |
| `company_id` | Passed from metadata |

**UUID Generation Logic:**
1. Use `linkedin_url` from direct field or first LinkedIn profile
2. Combine with `full_name` (cleaned and lowercased)
3. Generate deterministic UUIDv5 for consistent identification

## Data Processing Pipeline

### 1. Validation Phase

```python
validation_result = processor._validate_pdl_data(enrichment_data, metadata)
```

**Required Fields:**
- `metadata.founder_id`, `metadata.company_id`, `metadata.org_id`
- `enrichment_data.full_name`
- `enrichment_data.linkedin_url` OR LinkedIn profile in profiles array

### 2. UUID Generation

```python
founder_uuid = processor._generate_founder_uuid(enrichment_data)
```

**Deterministic Generation:**
- Same input always produces same UUID
- Different names/LinkedIn URLs produce different UUIDs
- Uses DNS namespace for consistency

### 3. Data Cleaning

```python
cleaned_data = processor._clean_enrichment_data(enrichment_data, metadata, founder_uuid, s3_key)
```

**Cleaning Operations:**
- **Strings**: Strip whitespace, normalize spacing, handle empty values
- **URLs**: Add HTTPS protocol, lowercase normalization
- **Skills**: Deduplicate, lowercase, filter single characters
- **Dates**: Parse multiple formats (`YYYY-MM-DD`, `YYYY-MM`, `YYYY`)
- **Locations**: Extract from various nested structures

### 4. Database Storage

```python
await processor._store_founder_data(cleaned_data)
```

**Transactional Insert:**
- All related records inserted in single transaction
- Rollback on any failure to prevent partial data
- Foreign key relationships automatically maintained

## Table Mapping

### founders
| Database Field | Source Path | Cleaning |
|---------------|-------------|----------|
| `id` | Generated UUID | UUIDv5 deterministic |
| `founder_id` | `metadata.founder_id` | Direct mapping |
| `full_name` | `enrichment_data.full_name` | String cleaning |
| `first_name` | `enrichment_data.first_name` | String cleaning |
| `last_name` | `enrichment_data.last_name` | String cleaning |
| `current_job_title` | `enrichment_data.job_title` | String cleaning |
| `current_job_company` | `enrichment_data.job_company_name` | String cleaning |
| `linkedin_url` | `enrichment_data.linkedin_url` | URL normalization |
| `github_url` | `enrichment_data.github_url` | URL normalization |
| `location_country` | `enrichment_data.location_country` | String cleaning |
| `confidence_score` | `metadata.confidence_score` OR `likelihood/10` | Float conversion |
| `enrichment_date` | `enrichment_data.enrichment_date` | ISO date parsing |

### founder_experiences
| Database Field | Source Path | Cleaning |
|---------------|-------------|----------|
| `company_name` | `experience[].company.name` | String cleaning |
| `title` | `experience[].title` OR `experience[].title.name` | String cleaning |
| `industry` | `experience[].company.industry` | String cleaning |
| `company_size` | `experience[].company.size` | String cleaning |
| `start_date` | `experience[].start_date` | Date parsing |
| `end_date` | `experience[].end_date` | Date parsing |
| `is_primary` | `experience[].is_primary` | Boolean direct |
| `location` | `experience[].location` | Location extraction |

### founder_education
| Database Field | Source Path | Cleaning |
|---------------|-------------|----------|
| `school_name` | `education[].school.name` | String cleaning |
| `degrees` | `education[].degrees[]` | Array cleaning |
| `majors` | `education[].majors[]` | Array cleaning |
| `start_date` | `education[].start_date` | Date parsing |
| `end_date` | `education[].end_date` | Date parsing |
| `location` | `education[].school.location` | Location extraction |

**Note**: Records without `school_name` are skipped.

### founder_skills
| Database Field | Source Path | Cleaning |
|---------------|-------------|----------|
| `skill` | `skills[]` | Lowercase, deduplicate, filter length > 1 |

### founder_profiles
| Database Field | Source Path | Cleaning |
|---------------|-------------|----------|
| `network` | `profiles[].network` | String cleaning |
| `url` | `profiles[].url` | URL normalization, deduplicate |

## Error Handling

### Validation Errors
```python
{
  "success": False,
  "error": "Validation failed: ['Missing required metadata field: org_id']",
  "founder_id": "72631b39ff5e"
}
```

### Processing Errors
```python
{
  "success": False,
  "error": "Database connection failed",
  "founder_id": "72631b39ff5e"
}
```

### Error Storage
- Errors stored in `founder_processing_errors` table
- Includes payload preview for debugging
- S3 raw data key for full payload access

## Usage Examples

### Basic Processing
```python
from app.tasks.founder_enrichment import PDLDataProcessor

# Initialize
processor = PDLDataProcessor(rds_storage, s3_storage)

# Process PDL payload
result = await processor.process_pdl_payload(pdl_payload, s3_key)

if result['success']:
    print(f"Processed founder: {result['founder_uuid']}")
else:
    print(f"Error: {result['error']}")
```

### Integration with Enrichment Task
```python
# In founder enrichment task
if result.data and result.data.get("pdl_data"):
    pdl_processor = PDLDataProcessor(rds_storage, s3_storage)
    
    pdl_payload = {
        "enrichment_data": result.data["pdl_data"].model_dump(for_db=True),
        "metadata": {
            "founder_id": founder_id,
            "company_id": company_id,
            "org_id": org_id,
            "founder_name": founder_name,
            "confidence_score": 1.0,
            "enrichment_sources": ["pdl_data"]
        },
        "job_id": job_id,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    
    processing_result = await pdl_processor.process_pdl_payload(pdl_payload, raw_data_key)
```

## Testing

### Run Test Suite
```bash
python scripts/test_pdl_processing.py
```

### Test Components
1. **UUID Generation**: Consistency and uniqueness testing
2. **Data Processing**: End-to-end processing with sample data
3. **Database Verification**: Confirm data stored correctly
4. **Error Handling**: Validation and error storage testing

## Performance Considerations

### Optimization Features
- **Batch Processing**: Single transaction for all related records
- **Deduplication**: Skills and profiles deduplicated before storage
- **Indexing**: Optimized indexes on founder_id, org_id, company_id
- **Connection Pooling**: Efficient database connection management

### Monitoring
- Processing time tracking
- Error rate monitoring
- Data quality metrics
- Storage utilization

## Validation Checklist

Before deploying:

- [ ] UUID generation is deterministic and consistent
- [ ] All string fields properly cleaned and normalized
- [ ] Skills and profiles deduplicated
- [ ] Dates parsed correctly (handles `YYYY-MM` format)
- [ ] Full raw payload backed up to S3
- [ ] Foreign key constraints enforced
- [ ] Transaction rollback on failure
- [ ] Error tracking functional
- [ ] Test suite passes with sample data

## What This Unlocks

✅ **Structured Data**: Founder profiles are searchable and relational
✅ **LLM Ready**: Clean data for scoring and analysis
✅ **Team Analysis**: Composable founder profiles for team insights
✅ **Audit Trail**: Full traceability through S3 backup
✅ **Data Quality**: Comprehensive validation and cleaning
✅ **Scalability**: Efficient storage and query patterns
