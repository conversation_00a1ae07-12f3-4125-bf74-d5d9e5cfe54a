#!/usr/bin/env python3
"""
Worker Runner for TractionX Data Pipeline Service

This script starts the new round-robin queue worker with proper configuration,
monitoring, and graceful shutdown handling.
"""

import argparse
import asyncio
import os
import sys
from typing import Any, Dict, Optional

# Add the app directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.configs import get_logger, settings
from app.queueing.backends.redis_backend import RedisQueueBackend
from app.queueing.service import TractionXQueueService

logger = get_logger(__name__)


class SimpleRoundRobinWorker:
    """
    Simplified round-robin worker that works with the current codebase.
    """

    def __init__(
        self,
        worker_id: str = "worker-1",
        concurrency: int = 4,
        poll_interval: float = 1.0,
    ):
        self.worker_id = worker_id
        self.concurrency = concurrency
        self.poll_interval = poll_interval
        self.running = False
        self.queue_service: Optional[TractionXQueueService] = None

        # Priority queues in order (high to low)
        self.queue_priorities = ["high", "normal", "low"]

        # Load job handlers
        from app.tasks import get_job_handlers

        self.handlers = get_job_handlers()

        logger.info(
            f"Worker {self.worker_id} initialized",
        )

    async def initialize(self) -> None:
        """Initialize the worker."""
        # Create Redis backend
        backend = RedisQueueBackend(
            redis_url=settings.redis_connection_string,
            key_prefix=settings.REDIS_KEY_PREFIX,
        )

        # Create queue service
        self.queue_service = TractionXQueueService(backend)  # type: ignore
        await self.queue_service.initialize()

        # Initialize barrier sync with task completion handlers
        from app.queueing.barrier_sync import BarrierSync
        from app.queueing.task_completion_handlers import (
            register_all_completion_handlers,
        )

        self._barrier_sync = BarrierSync(
            redis_url=settings.redis_connection_string,
            mongo_url=settings.mongo_connection_string,
        )
        await self._barrier_sync.initialize()

        # Register task completion handlers
        register_all_completion_handlers(self._barrier_sync, self.queue_service)

        logger.info(f"Worker {self.worker_id} queue service initialized")

    async def start(self) -> None:
        """Start the worker."""
        if not self.queue_service:
            await self.initialize()

        self.running = True
        logger.info(f"Starting worker {self.worker_id}")

        # Start worker tasks
        tasks = []
        for i in range(self.concurrency):
            task = asyncio.create_task(self._worker_loop(f"{self.worker_id}-{i}"))
            tasks.append(task)

        try:
            await asyncio.gather(*tasks)
        except KeyboardInterrupt:
            logger.info("Received interrupt signal, shutting down...")
            self.running = False
            for task in tasks:
                task.cancel()
            await asyncio.gather(*tasks, return_exceptions=True)

    async def _worker_loop(self, task_id: str) -> None:
        """Main worker loop."""
        logger.info(f"Worker task {task_id} started")

        while self.running:
            try:
                # Try to get a job using round-robin priority
                if self.queue_service and hasattr(self.queue_service, "backend"):
                    job_data = await self.queue_service.backend.dequeue(
                        queue_names=self.queue_priorities,
                        timeout=int(self.poll_interval),
                    )

                    if job_data:
                        await self._process_job(job_data, task_id)
                    else:
                        # No jobs available, brief sleep
                        await asyncio.sleep(0.1)
                else:
                    await asyncio.sleep(1)

            except asyncio.CancelledError:
                logger.info(f"Worker task {task_id} cancelled")
                break
            except Exception as e:
                logger.error(f"Worker task {task_id} error: {e}", exc_info=True)
                await asyncio.sleep(1)

        logger.info(f"Worker task {task_id} stopped")

    async def _process_job(self, job_data: dict, task_id: str) -> None:
        """Process a single job."""
        job_id = job_data.get("job_id", "unknown")
        job_func = job_data.get("job_func")
        job_args = job_data.get("job_args", {})

        if not job_func:
            logger.error(f"Job {job_id} has no job_func specified")
            return

        logger.info(f"Processing job {job_id}: {job_func}")

        # Handle barrier-aware job processing
        meta = None
        logger.info("Job args structure: ", job_args=job_args)
        if isinstance(job_args, dict) and "meta" in job_args:
            meta = job_args.get("meta")
            job_args = job_args.get("job_args", {})
            logger.info("Extracted meta: ", meta=meta)
            logger.info("Extracted job_args: ", job_args=job_args)
        else:
            logger.info("No meta found in job_args, checking job_data structure")
            logger.info("Full job_data: ", job_data=job_data)

        try:
            # Find handler
            handler = self.handlers.get(job_func)
            if not handler:
                raise ValueError(f"No handler found for: {job_func}")

            # Execute handler (now async)
            result = await handler(job_args)
            logger.info("Result: ", result=result)
            logger.info("job_data : ", job_data=job_data)

            # Handle barrier-aware job completion
            if meta and "barrier_group_id" in meta and "task_name" in meta:
                from app.configs import settings
                from app.queueing.barrier_sync import BarrierSync

                logger.info(
                    f"Processing barrier-aware job completion for {meta['task_name']}"
                )

                # Initialize barrier sync if not already done
                if not hasattr(self, "_barrier_sync"):
                    logger.info("Initializing barrier sync for worker")
                    self._barrier_sync = BarrierSync(
                        redis_url=settings.redis_connection_string,
                        mongo_url=settings.mongo_connection_string,
                    )
                    await self._barrier_sync.initialize()

                # Mark task as complete in barrier
                barrier_group_id = meta["barrier_group_id"]
                task_name = meta["task_name"]

                logger.info(
                    f"Marking task {task_name} as complete for barrier {barrier_group_id}"
                )

                try:
                    merge_ready = await self._barrier_sync.mark_task_complete(
                        group_id=barrier_group_id,
                        task_name=task_name,
                        metadata={"job_id": job_id, "result": result},
                    )

                    logger.info(
                        f"Task {task_name} marked complete, merge_ready: {merge_ready}"
                    )

                    # Only trigger merge if the barrier sync actually indicates merge is ready
                    if merge_ready:
                        logger.info(
                            f"Barrier {barrier_group_id} ready for merge, triggering merge job"
                        )
                        # Trigger merge job
                        await self._trigger_merge_job(barrier_group_id, meta)
                    else:
                        logger.debug(
                            f"Barrier {barrier_group_id} not ready for merge yet, waiting for more tasks to complete"
                        )
                except Exception as e:
                    logger.error(f"Failed to mark task {task_name} complete: {e}")
            else:
                logger.debug(f"Job {job_id} is not barrier-aware (meta: {meta})")

            # Mark as completed
            if self.queue_service:
                await self.queue_service.backend.complete_job(job_id, result)

            logger.info(f"Job {job_id} completed successfully")

        except Exception as e:
            error_msg = f"Job {job_id} failed: {str(e)}"
            logger.error(error_msg, exc_info=True)

            # Handle barrier-aware job failure
            if meta and "barrier_group_id" in meta and "task_name" in meta:
                from app.configs import settings
                from app.queueing.barrier_sync import BarrierSync

                # Initialize barrier sync if not already done
                if not hasattr(self, "_barrier_sync"):
                    self._barrier_sync = BarrierSync(
                        redis_url=settings.redis_connection_string,
                        mongo_url=settings.mongo_connection_string,
                    )
                    await self._barrier_sync.initialize()

                # Mark task as failed in barrier
                barrier_group_id = meta["barrier_group_id"]
                task_name = meta["task_name"]

                await self._barrier_sync.mark_task_complete(
                    group_id=barrier_group_id,
                    task_name=task_name,
                    error_message=error_msg,
                )

            if self.queue_service:
                await self.queue_service.backend.fail_job(job_id, error_msg, retry=True)

    async def _trigger_merge_job(
        self, barrier_group_id: str, meta: Dict[str, Any]
    ) -> None:
        """Trigger the final merge job when all barrier tasks are complete."""
        try:
            # Extract entity info from barrier group ID
            entity_type = meta.get("entity_type", "company")
            entity_id = meta.get(
                "entity_id",
                barrier_group_id.split(":", 1)[1]
                if ":" in barrier_group_id
                else barrier_group_id,
            )

            # Enqueue merge job
            from app.queueing.interfaces import JobPriority

            merge_job_id = await self.queue_service.enqueue_job(  # type: ignore
                job_func="merge_comprehensive_enrichment_results",
                job_args={
                    "barrier_group_id": barrier_group_id,
                    "entity_type": entity_type,
                    "entity_id": entity_id,
                },
                priority=JobPriority.HIGH,
            )

            logger.info(
                f"Triggered merge job {merge_job_id} for barrier {barrier_group_id}"
            )

        except Exception as e:
            logger.error(
                f"Failed to trigger merge job for barrier {barrier_group_id}: {e}"
            )


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="TractionX Data Pipeline Worker")
    parser.add_argument("--worker-id", default="worker-1", help="Worker ID")
    parser.add_argument(
        "--concurrency", type=int, default=4, help="Number of concurrent workers"
    )
    parser.add_argument(
        "--poll-interval", type=float, default=1.0, help="Poll interval in seconds"
    )

    args = parser.parse_args()

    logger.info("Starting TractionX Data Pipeline Worker")
    logger.info(f"Worker ID: {args.worker_id}")
    logger.info(f"Concurrency: {args.concurrency}")
    logger.info(f"Poll Interval: {args.poll_interval}s")

    worker = SimpleRoundRobinWorker(
        worker_id=args.worker_id,
        concurrency=args.concurrency,
        poll_interval=args.poll_interval,
    )

    try:
        await worker.start()
    except KeyboardInterrupt:
        logger.info("Worker stopped by user")
    except Exception as e:
        logger.error(f"Worker failed: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
