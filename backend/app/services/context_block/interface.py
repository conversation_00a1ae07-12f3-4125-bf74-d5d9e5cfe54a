"""
Context Block Service Interface

Defines the interface for the Context Block Service.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

from app.models.deal import Deal
from app.models.form import Form, Submission


class IContextBlockService(ABC):
    """Interface for Context Block Service."""

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the service."""
        pass

    @abstractmethod
    async def generate_context_block(
        self,
        deal: Deal,
        submission: Optional[Submission] = None,
        form: Optional[Form] = None,
    ) -> Dict[str, Any]:
        """
        Generate a comprehensive context block for a deal.

        Args:
            deal: The deal object
            submission: Optional submission data
            form: Optional form data

        Returns:
            Dictionary containing the context block
        """
        pass

    @abstractmethod
    async def store_context_block(
        self, deal_id: str, context_block: Dict[str, Any]
    ) -> str:
        """
        Store context block in S3.

        Args:
            deal_id: The deal ID
            context_block: The context block data

        Returns:
            S3 URL of the stored context block
        """
        pass

    @abstractmethod
    async def get_context_block(self, deal_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve context block from S3.

        Args:
            deal_id: The deal ID

        Returns:
            Context block data or None if not found
        """
        pass

    @abstractmethod
    async def update_context_block(
        self, deal: Deal, submission: Optional[Submission] = None
    ) -> Optional[str]:
        """
        Update context block for a deal.

        Args:
            deal: The deal object
            submission: Optional updated submission

        Returns:
            S3 URL of the updated context block
        """
        pass
