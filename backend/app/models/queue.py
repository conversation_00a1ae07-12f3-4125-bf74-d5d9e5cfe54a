"""
Queue system data models.

This module defines the data models used by the queue system, including
job definitions, queue configurations, and related schemas.
"""

import time
import uuid
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, field_validator

from app.models.job import JobStatus


class JobPriority(str, Enum):
    """Priority levels for jobs."""

    HIGH = "high"
    NORMAL = "normal"
    LOW = "low"


class QueueType(str, Enum):
    """Types of queues available in the system."""

    DEFAULT = "default"
    HIGH_PRIORITY = "high_priority"
    BACKGROUND = "background"
    SCHEDULED = "scheduled"
    DEAD_LETTER = "dead_letter"
    AI = "ai"


class RetryStrategy(str, Enum):
    """Retry strategies for failed jobs."""

    EXPONENTIAL = "exponential"  # Exponential backoff
    LINEAR = "linear"  # Linear backoff
    FIXED = "fixed"  # Fixed delay
    NONE = "none"  # No retry


class RetryConfig(BaseModel):
    """Configuration for job retries."""

    max_attempts: int = 3
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL
    base_delay: int = 5  # Base delay in seconds
    max_delay: int = 1800  # Maximum delay in seconds (30 minutes)

    @field_validator("max_attempts")
    def validate_max_attempts(cls, v):
        if v < 0:
            raise ValueError("max_attempts must be non-negative")
        return v

    @field_validator("base_delay", "max_delay")
    def validate_delays(cls, v):
        if v < 0:
            raise ValueError("delay values must be non-negative")
        return v


class JobMetadata(BaseModel):
    """Metadata for a job."""

    source: Optional[str] = None  # Source of the job (service, user, etc.)
    correlation_id: Optional[str] = None  # For tracking related jobs
    tags: List[str] = Field(default_factory=list)  # Tags for categorization
    custom: Dict[str, Any] = Field(default_factory=dict)  # Custom metadata


class Job(BaseModel):
    """Represents a job in the queue."""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    type: str
    payload: Dict[str, Any]
    status: JobStatus = JobStatus.PENDING
    priority: JobPriority = JobPriority.NORMAL
    queue: QueueType = QueueType.DEFAULT

    # Timing information
    created_at: int = Field(default_factory=lambda: int(time.time() * 1000))
    updated_at: int = Field(default_factory=lambda: int(time.time() * 1000))
    scheduled_for: Optional[int] = (
        None  # When the job is scheduled to run (ms timestamp)
    )
    started_at: Optional[int] = None  # When the job started processing
    completed_at: Optional[int] = None  # When the job completed
    failed_at: Optional[int] = None  # When the job failed

    # Retry information
    attempts: int = 0
    max_attempts: int = 3
    retry_strategy: RetryStrategy = RetryStrategy.EXPONENTIAL
    base_delay: int = 5
    max_delay: int = 1800
    next_retry_at: Optional[int] = None

    # Result information
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

    # Additional metadata
    metadata: JobMetadata = Field(default_factory=JobMetadata)

    def calculate_next_retry_delay(self) -> int:
        """Calculate the delay for the next retry based on the retry strategy."""
        if self.retry_strategy == RetryStrategy.NONE:
            return 0

        if self.retry_strategy == RetryStrategy.FIXED:
            return self.base_delay

        if self.retry_strategy == RetryStrategy.LINEAR:
            delay = self.base_delay * self.attempts
            return min(delay, self.max_delay)

        if self.retry_strategy == RetryStrategy.EXPONENTIAL:
            delay = self.base_delay * (2 ** (self.attempts - 1))
            return min(delay, self.max_delay)

        # Default to exponential backoff
        delay = self.base_delay * (2 ** (self.attempts - 1))
        return min(delay, self.max_delay)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the job to a dictionary for storage."""
        return self.model_dump()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Job":
        """Create a job from a dictionary."""
        # Handle metadata conversion
        if "metadata" in data and not isinstance(data["metadata"], JobMetadata):
            data["metadata"] = JobMetadata(**data["metadata"])
        return cls(**data)


class QueueStats(BaseModel):
    """Statistics for a queue."""

    queue: QueueType
    pending: int = 0
    processing: int = 0
    completed: int = 0
    failed: int = 0
    retried: int = 0
    scheduled: int = 0
    total_enqueued: int = 0
    total_processed: int = 0
