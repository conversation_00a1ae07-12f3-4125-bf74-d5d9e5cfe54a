# #!/usr/bin/env python3
# import os
# import re
# import sys
#
# def fix_imports(directory):
#     """Fix various import errors in Python files after moving from app/ to backend/app/"""
#
#     # Patterns to fix
#     patterns = [
#         # Fix direct app imports
#         (re.compile(r'from\s+app\.'), lambda m: 'from backend.app.'),
#         (re.compile(r'import\s+app\.'), lambda m: 'import backend.app.'),
#
#         # Fix relative imports that might be broken
#         (re.compile(r'from\s+backend\.backend\.'), lambda m: 'from backend.'),
#
#         # Fix specific commented imports in token.py
#         (re.compile(r'# from backend\.app\.'), lambda m: '# from app.'),
#
#         # Fix any other specific patterns you identify
#         # Add more patterns as needed
#     ]
#
#     for root, _, files in os.walk(directory):
#         for file in files:
#             if file.endswith('.py'):
#                 filepath = os.path.join(root, file)
#                 with open(filepath, 'r') as f:
#                     content = f.read()
#
#                 modified = False
#                 new_content = content
#
#                 for pattern, replacement_func in patterns:
#                     if pattern.search(new_content):
#                         new_content = pattern.sub(replacement_func, new_content)
#                         modified = True
#
#                 if modified:
#                     with open(filepath, 'w') as f:
#                         f.write(new_content)
#                     print(f"Fixed imports in {filepath}")
#
# if __name__ == "__main__":
#     directory = sys.argv[1] if len(sys.argv) > 1 else "backend"
#     fix_imports(directory)