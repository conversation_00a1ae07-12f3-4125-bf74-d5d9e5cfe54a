/**
 * Founder Analysis API Client
 * Handles fetching enriched founder data and triggering enrichment pipelines
 */

import apiClient from "../api-client"



// Types for founder analysis data
export interface FounderSignals {
  id: string
  founderId: string
  score: number
  tags: string[]
  strengths: {
    items: string[]
  }
  risks: {
    items: string[]
  }
  skillProfile: {
    tech: number
    product: number
    business: number
    operations: number
    fundraising: number
  }
  generatedAt?: string
}

export interface FounderExperience {
  id: string
  founderId: string
  companyName: string
  title: string
  industry?: string
  companySize?: string
  startDate: string
  endDate?: string
  isPrimary?: boolean
  location?: string
}

export interface FounderEducation {
  id: string
  founderId: string
  schoolName: string
  degrees: string[]
  majors: string[]
  startDate?: string
  endDate?: string
  location?: string
}

export interface FounderSkill {
  id: string
  founderId: string
  skill: string
}

export interface FounderProfile {
  id: string
  founderId: string
  network: string
  url: string
}

export interface FounderData {
  id: string
  founderId?: string
  fullName: string
  firstName?: string
  lastName?: string
  currentJobTitle?: string
  currentJobCompany?: string
  email?: string
  title?: string
  bio?: string
  location?: string
  locationCountry?: string
  country?: string
  city?: string
  linkedinUrl?: string
  twitterUrl?: string
  githubUrl?: string
  confidenceScore?: number
  serialFounder?: boolean
  orgId?: string
  companyId?: string
  source?: string
  enrichmentDate?: string
  s3RawDataKey?: string
  createdAt?: string
  updatedAt?: string
}

export interface EnrichedFounder {
  founder: FounderData
  experiences: FounderExperience[]
  education: FounderEducation[]
  skills: FounderSkill[]
  profiles: FounderProfile[]
  signals: FounderSignals | null
}

export interface FounderAnalysisResponse {
  founders: EnrichedFounder[]
}

export interface PipelineTriggerRequest {
  company_id: string
  org_id: string
  company_name: string
  domain?: string
  form_data?: {
    founder_name?: string
    founder_linkedin?: string
  }
  pipeline_types: string[]
  priority: 'low' | 'normal' | 'high'
}

export interface PipelineTriggerResponse {
  success: boolean
  message: string
  jobIds?: Record<string, string>
}

export const FounderAPI = {
  /**
   * Get enriched founder data for a deal
   */
  async getDealFounders(dealId: string): Promise<FounderAnalysisResponse> {
    const response = await apiClient.get(`/deals/${dealId}/founders`)
    return response.data
  },

  /**
   * Trigger founder enrichment pipeline
   */
  async triggerEnrichment(request: PipelineTriggerRequest): Promise<PipelineTriggerResponse> {
    const response = await apiClient.post('/pipelines/trigger', request)
    return response.data
  },

  /**
   * Generate AI summary for founders (placeholder for future implementation)
   */
  async generateFounderSummary(dealId: string): Promise<{ summary: string }> {
    // This would call an AI service to generate a team summary
    // For now, return a placeholder
    return {
      summary: "AI-generated team summary will be available soon."
    }
  }
}
