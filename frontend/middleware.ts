import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

// This middleware is simplified to avoid conflicts with the custom auth system
export function middleware(req: NextRequest) {
  // We'll let the client-side auth handling take care of redirections
  // This avoids conflicts between server and client auth checks
  return NextResponse.next()
}
    // "/dashboard/:path*",
    // "/editor/:path*",
export const config = {
  matcher: [
    "/forms/:path*",
    "/login",
    "/register"
  ],
}
