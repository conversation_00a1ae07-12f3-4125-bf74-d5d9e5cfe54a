import { <PERSON>ada<PERSON> } from "next"
import { Suspense } from "react"

import { AuthLayout } from "@/components/auth/auth-layout"
import { ResetPasswordForm } from "@/components/auth/reset-password-form"

export const metadata: Metadata = {
  title: "Set new password - TractionX",
  description: "Create a new password for your TractionX account.",
}

export default function ResetPasswordPage() {
  return (
    <AuthLayout
      title="Set new password"
      subtitle="Create a strong password to secure your account"
    >
      <Suspense fallback={
        <div className="flex items-center justify-center p-8">
          <div className="size-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
        </div>
      }>
        <ResetPasswordForm />
      </Suspense>
    </AuthLayout>
  )
}
