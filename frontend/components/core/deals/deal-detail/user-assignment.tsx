"use client"

import React, { useState, useEffect } from 'react'
import { Check, ChevronsUpDown, User as UserIcon, Plus, X, ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/use-toast'
import { Deal, User } from '@/lib/types/deal'
import { DealAPI } from '@/lib/api/deal-api'

interface UserAssignmentProps {
  deal: Deal
  onDealUpdate: (updatedDeal: Deal) => void
  className?: string
}

export function UserAssignment({ deal, onDealUpdate, className }: UserAssignmentProps) {
  const [open, setOpen] = useState(false)
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [assignedUsers, setAssignedUsers] = useState<User[]>([])
  const { toast } = useToast()

  // Load organization users
  useEffect(() => {
    const loadUsers = async () => {
      try {
        const orgUsers = await DealAPI.getOrgUsers()
        setUsers(orgUsers)
        
        // Find assigned users if deal has any
        if (deal.assigned_user_ids && deal.assigned_user_ids.length > 0) {
          const assigned = orgUsers.filter(user => 
            deal.assigned_user_ids!.includes(user.id)
          )
          setAssignedUsers(assigned)
        } else {
          setAssignedUsers([])
        }
      } catch (error) {
        console.error('Failed to load users:', error)
        toast({
          title: "Error",
          description: "Failed to load team members",
          variant: "destructive",
        })
      }
    }

    loadUsers()
  }, [deal.assigned_user_ids, toast])

  const handleAssignUser = async (user: User) => {
    if (loading) return
    
    // Check if already assigned to same user (no-op)
    if (deal.assigned_user_ids?.includes(user.id)) {
      setOpen(false)
      return
    }

    setLoading(true)
    try {
      const updatedDeal = await DealAPI.assignUserToDeal(deal.id, user.id)
      setAssignedUsers([user]) // Replace with single user assignment
      onDealUpdate(updatedDeal)
      setOpen(false)
      
      toast({
        title: "Assigned",
        description: `Deal assigned to ${user.name}`,
      })
    } catch (error) {
      console.error('Failed to assign user:', error)
      toast({
        title: "Error",
        description: "Failed to assign user to deal",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleUnassign = async () => {
    if (loading || assignedUsers.length === 0) return

    setLoading(true)
    try {
      const updatedDeal = await DealAPI.unassignAllUsers(deal.id)
      setAssignedUsers([])
      onDealUpdate(updatedDeal)
      
      toast({
        title: "Unassigned",
        description: "Deal unassigned from all users",
      })
    } catch (error) {
      console.error('Failed to unassign users:', error)
      toast({
        title: "Error",
        description: "Failed to unassign users from deal",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <span className="hidden text-sm font-medium text-muted-foreground sm:block">
        Owner
      </span>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "h-9 min-w-[160px] justify-between sm:min-w-[200px]",
              "transition-colors hover:bg-muted/50",
              assignedUsers.length === 0 && "text-muted-foreground"
            )}
            disabled={loading}
          >
            <div className="flex items-center gap-2">
              {assignedUsers.length > 0 ? (
                <>
                  <div className="flex -space-x-1">
                    {assignedUsers.slice(0, 2).map((user) => (
                      <Avatar key={user.id} className="size-6 border-2 border-background">
                        <AvatarImage src={user.email} />
                        <AvatarFallback className="bg-primary/10 text-xs text-primary">
                          {getInitials(user.name)}
                        </AvatarFallback>
                      </Avatar>
                    ))}
                    {assignedUsers.length > 2 && (
                      <div className="flex size-6 items-center justify-center rounded-full border-2 border-background bg-muted text-xs">
                        +{assignedUsers.length - 2}
                      </div>
                    )}
                  </div>
                  <span className="truncate">
                    {assignedUsers.length === 1 
                      ? assignedUsers[0].name 
                      : `${assignedUsers.length} assigned`
                    }
                  </span>
                </>
              ) : (
                <>
                  <UserIcon className="size-4" />
                  <span>Assign to...</span>
                </>
              )}
              <ChevronDown className="ml-auto size-4" />
            </div>
          </Button>
        </PopoverTrigger>
        
        <PopoverContent className="w-[300px] p-0" align="start">
          <Command>
            <CommandInput placeholder="Search team members..." />
            <CommandEmpty>No team members found.</CommandEmpty>
            <CommandGroup>
              {users.map((user) => (
                <CommandItem
                  key={user.id}
                  value={user.name}
                  onSelect={() => handleAssignUser(user)}
                  className="flex cursor-pointer items-center gap-2"
                >
                  <Avatar className="size-6">
                    <AvatarImage src={user.email} />
                    <AvatarFallback className="bg-primary/10 text-xs text-primary">
                      {getInitials(user.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="font-medium">{user.name}</div>
                    <div className="text-xs text-muted-foreground">{user.email}</div>
                  </div>
                  <Check
                    className={cn(
                      "ml-auto size-4",
                      assignedUsers.some(assigned => assigned.id === user.id) ? "opacity-100" : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Unassign button - only show on desktop when users are assigned */}
      {assignedUsers.length > 0 && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleUnassign}
          className="hidden size-6 p-0 hover:bg-destructive/10 hover:text-destructive sm:flex"
          disabled={loading}
        >
          <X className="size-3" />
        </Button>
      )}
    </div>
  )
}
