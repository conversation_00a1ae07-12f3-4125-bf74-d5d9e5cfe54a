"""
Centralized Task Completion Handlers for Barrier Orchestration.

This module contains all the completion handlers that trigger dependent tasks
when specific tasks complete successfully. This ensures that dependent tasks
are triggered immediately and atomically when their prerequisites complete.
"""

from typing import Any, Dict

from app.configs import get_logger
from app.queueing.interfaces import JobPriority

logger = get_logger(__name__)


async def handle_crunchbase_resolver_completion(
    barrier_group_id: str, task_result: Dict[str, Any], queue_service
) -> None:
    """Handle completion of resolve_crunchbase_url_task. Triggers: process_brightdata_company_data_task"""
    try:
        # Extract S3 key from the result
        metadata = task_result.get("metadata", {})
        result_data = metadata.get("result", {})
        s3_key = result_data.get("s3_key")

        if s3_key:
            # Fetch data from S3 to get snapshot ID
            from app.storage.s3_storage import S3Storage

            s3_storage = S3Storage()
            await s3_storage.initialize()

            try:
                # Fetch data from S3
                raw_data = await s3_storage.get_object(s3_key)
                if isinstance(raw_data, bytes):
                    raw_data = raw_data.decode("utf-8")
                if isinstance(raw_data, str):
                    import json

                    s3_data = json.loads(raw_data)
                else:
                    s3_data = raw_data

                # Extract snapshot ID from the S3 data
                output_data = (
                    s3_data.get("output", {}) if isinstance(s3_data, dict) else {}
                )
                snapshot_id = (
                    output_data.get("brightdata_snapshot_id")
                    if isinstance(output_data, dict)
                    else None
                )

                if snapshot_id:
                    # Extract company info from barrier_group_id
                    barrier_parts = barrier_group_id.split(":")
                    if len(barrier_parts) >= 3:
                        org_id = barrier_parts[1]
                        company_domain = barrier_parts[2]

                        # Trigger process_brightdata_company_data
                        dependent_task_payload = {
                            "snapshot_id": snapshot_id,
                            "company_domain": company_domain,
                            "org_id": org_id,
                            "barrier_group_id": barrier_group_id,
                        }

                        # Register the dependent task with barrier
                        from app.configs.settings import settings
                        from app.queueing.barrier_sync import BarrierSync

                        barrier_sync = BarrierSync(
                            redis_url=settings.redis_connection_string,
                            mongo_url=settings.mongo_connection_string,
                        )
                        await barrier_sync.initialize()

                        try:
                            await barrier_sync.register_tasks(
                                group_id=barrier_group_id,
                                new_tasks=["process_brightdata_company_data_task"],
                            )

                            # Enqueue the dependent task
                            await queue_service.enqueue_job(
                                job_func="process_brightdata_company_data_task",
                                job_args=dependent_task_payload,
                                meta={
                                    "barrier_group_id": barrier_group_id,
                                    "task_name": "process_brightdata_company_data_task",
                                    "entity_type": "company",
                                    "dependent_on": "resolve_crunchbase_url_task",
                                },
                                priority=JobPriority.NORMAL,
                            )

                            logger.info(
                                f"Triggered Crunchbase processing for {company_domain}"
                            )
                        finally:
                            await barrier_sync.cleanup()
                    else:
                        logger.warning(
                            f"Invalid barrier_group_id format: {barrier_group_id}"
                        )
                else:
                    logger.info("No snapshot ID found for Crunchbase processing")
            finally:
                await s3_storage.cleanup()
        else:
            logger.info("No S3 key found for Crunchbase processing")

    except Exception as e:
        logger.error(f"Error handling Crunchbase completion: {e}")
        raise


async def handle_linkedin_resolver_completion(
    barrier_group_id: str, task_result: Dict[str, Any], queue_service
) -> None:
    """Handle completion of resolve_linkedin_url_task. Triggers: process_brightdata_linkedin_data_task"""
    try:
        # Extract S3 key from the result
        metadata = task_result.get("metadata", {})
        result_data = metadata.get("result", {})
        s3_key = result_data.get("s3_key")

        if s3_key:
            # Fetch data from S3 to get snapshot ID
            from app.storage.s3_storage import S3Storage

            s3_storage = S3Storage()
            await s3_storage.initialize()

            try:
                # Fetch data from S3
                raw_data = await s3_storage.get_object(s3_key)
                if isinstance(raw_data, bytes):
                    raw_data = raw_data.decode("utf-8")
                if isinstance(raw_data, str):
                    import json

                    s3_data = json.loads(raw_data)
                else:
                    s3_data = raw_data

                # Extract snapshot ID from the S3 data
                output_data = (
                    s3_data.get("output", {}) if isinstance(s3_data, dict) else {}
                )
                snapshot_id = (
                    output_data.get("brightdata_snapshot_id")
                    if isinstance(output_data, dict)
                    else None
                )

                if snapshot_id:
                    # Extract company info from barrier_group_id
                    barrier_parts = barrier_group_id.split(":")
                    if len(barrier_parts) >= 3:
                        org_id = barrier_parts[1]
                        company_domain = barrier_parts[2]

                        # Trigger process_brightdata_linkedin_data
                        dependent_task_payload = {
                            "snapshot_id": snapshot_id,
                            "company_domain": company_domain,
                            "org_id": org_id,
                            "barrier_group_id": barrier_group_id,
                        }

                        # Register the dependent task with barrier
                        from app.configs.settings import settings
                        from app.queueing.barrier_sync import BarrierSync

                        barrier_sync = BarrierSync(
                            redis_url=settings.redis_connection_string,
                            mongo_url=settings.mongo_connection_string,
                        )
                        await barrier_sync.initialize()

                        try:
                            await barrier_sync.register_tasks(
                                group_id=barrier_group_id,
                                new_tasks=["process_brightdata_linkedin_data_task"],
                            )

                            # Enqueue the dependent task
                            await queue_service.enqueue_job(
                                job_func="process_brightdata_linkedin_data_task",
                                job_args=dependent_task_payload,
                                meta={
                                    "barrier_group_id": barrier_group_id,
                                    "task_name": "process_brightdata_linkedin_data_task",
                                    "entity_type": "company",
                                    "dependent_on": "resolve_linkedin_url_task",
                                },
                                priority=JobPriority.NORMAL,
                            )

                            logger.info(
                                f"Triggered LinkedIn processing for {company_domain}"
                            )
                        finally:
                            await barrier_sync.cleanup()
                    else:
                        logger.warning(
                            f"Invalid barrier_group_id format: {barrier_group_id}"
                        )
                else:
                    logger.info("No snapshot ID found for LinkedIn processing")
            finally:
                await s3_storage.cleanup()
        else:
            logger.info("No S3 key found for LinkedIn processing")

    except Exception as e:
        logger.error(f"Error handling LinkedIn completion: {e}")
        raise


async def handle_sitemap_generation_completion(
    barrier_group_id: str, task_result: Dict[str, Any], queue_service
) -> None:
    """Handle completion of generate_sitemap_task. Triggers: generate_website_insights_task for each URL"""
    try:
        # Extract URLs from the result
        metadata = task_result.get("metadata", {})
        result_data = metadata.get("result", {}).get("data", {})
        urls = result_data.get("urls", [])

        # Extract company info from barrier_group_id
        barrier_parts = barrier_group_id.split(":")
        if len(barrier_parts) >= 3:
            org_id = barrier_parts[1]
            company_domain = barrier_parts[2]

            # Ensure the domain is included in the URLs list
            domain_urls = []

            # Add www version if not already present
            www_domain_url = f"https://www.{company_domain}"
            if www_domain_url not in urls:
                domain_urls.append(www_domain_url)

            # Add all existing URLs
            domain_urls.extend(urls)

            # Remove duplicates while preserving order
            unique_urls = []
            seen_urls = set()
            for url in domain_urls:
                if url not in seen_urls:
                    unique_urls.append(url)
                    seen_urls.add(url)

            if unique_urls:
                # Register generate_website_insights tasks for each URL
                from app.configs.settings import settings
                from app.queueing.barrier_sync import BarrierSync

                barrier_sync = BarrierSync(
                    redis_url=settings.redis_connection_string,
                    mongo_url=settings.mongo_connection_string,
                )
                await barrier_sync.initialize()

                try:
                    insight_tasks = [
                        f"generate_website_insights_task_{i}"
                        for i in range(len(unique_urls))
                    ]
                    await barrier_sync.register_tasks(
                        group_id=barrier_group_id, new_tasks=insight_tasks
                    )

                    # Enqueue generate_website_insights for each URL
                    for i, url in enumerate(unique_urls):
                        insight_task_payload = {
                            "url": url,
                            "company_domain": company_domain,
                            "org_id": org_id,
                            "barrier_group_id": barrier_group_id,
                            "url_index": i,
                        }

                        await queue_service.enqueue_job(
                            job_func="generate_website_insights_task",
                            job_args=insight_task_payload,
                            meta={
                                "barrier_group_id": barrier_group_id,
                                "task_name": f"generate_website_insights_task_{i}",
                                "entity_type": "company",
                                "dependent_on": "generate_sitemap_task",
                                "url": url,
                            },
                            priority=JobPriority.NORMAL,
                        )

                    logger.info(
                        f"Triggered {len(unique_urls)} website insights tasks for {company_domain}"
                    )
                finally:
                    await barrier_sync.cleanup()
            else:
                logger.warning(
                    f"No URLs found for website insights generation: {company_domain}"
                )
        else:
            logger.warning(f"Invalid barrier_group_id format: {barrier_group_id}")

    except Exception as e:
        logger.error(f"Error handling sitemap completion: {e}")
        raise


async def handle_apollo_company_data_completion(
    barrier_group_id: str, task_result: Dict[str, Any], queue_service
) -> None:
    """Handle completion of process_apollo_company_data_task. No dependent tasks needed."""
    try:
        # Apollo task doesn't trigger any dependent tasks - it just stores data in S3
        # The comprehensive_data_processor will handle the final merging

        metadata = task_result.get("metadata", {})
        result_data = metadata.get("result", {})

        if result_data.get("success"):
            logger.info(
                f"Apollo company data processing completed successfully for {result_data.get('company_domain')}"
            )
        else:
            logger.warning(
                f"Apollo company data processing failed: {result_data.get('error')}"
            )

    except Exception as e:
        logger.error(f"Error handling Apollo completion: {e}")
        raise


async def handle_pitchbook_resolver_completion(
    barrier_group_id: str, task_result: Dict[str, Any], queue_service
) -> None:
    """Handle completion of resolve_pitchbook_url_task. Triggers: process_brightdata_pitchbook_data_task"""
    try:
        # Extract S3 key from the result - PitchBook resolver returns s3_key directly
        metadata = task_result.get("metadata", {})
        result_data = metadata.get("result", {})
        s3_key = result_data.get("s3_key")

        logger.info(
            f"PitchBook completion handler - task_result keys: {list(task_result.keys())}"
        )
        logger.info(f"PitchBook completion handler - s3_key: {s3_key}")

        if s3_key:
            # Fetch data from S3 to get snapshot ID
            from app.storage.s3_storage import S3Storage

            s3_storage = S3Storage()
            await s3_storage.initialize()

            try:
                # Fetch data from S3
                raw_data = await s3_storage.get_object(s3_key)
                if isinstance(raw_data, bytes):
                    raw_data = raw_data.decode("utf-8")
                if isinstance(raw_data, str):
                    import json

                    s3_data = json.loads(raw_data)
                else:
                    s3_data = raw_data

                # Extract snapshot ID from the S3 data
                output_data = (
                    s3_data.get("output", {}) if isinstance(s3_data, dict) else {}
                )
                snapshot_id = (
                    output_data.get("brightdata_snapshot_id")
                    if isinstance(output_data, dict)
                    else None
                )

                if snapshot_id:
                    # Extract company info from barrier_group_id
                    barrier_parts = barrier_group_id.split(":")
                    if len(barrier_parts) >= 3:
                        org_id = barrier_parts[1]
                        company_domain = barrier_parts[2]

                        # Trigger process_brightdata_pitchbook_data
                        dependent_task_payload = {
                            "snapshot_id": snapshot_id,
                            "company_domain": company_domain,
                            "org_id": org_id,
                            "barrier_group_id": barrier_group_id,
                        }

                        # Register the dependent task with barrier
                        from app.configs.settings import settings
                        from app.queueing.barrier_sync import BarrierSync

                        barrier_sync = BarrierSync(
                            redis_url=settings.redis_connection_string,
                            mongo_url=settings.mongo_connection_string,
                        )
                        await barrier_sync.initialize()

                        try:
                            await barrier_sync.register_tasks(
                                group_id=barrier_group_id,
                                new_tasks=["process_brightdata_pitchbook_data_task"],
                            )

                            # Enqueue the dependent task
                            await queue_service.enqueue_job(
                                job_func="process_brightdata_pitchbook_data_task",
                                job_args=dependent_task_payload,
                                meta={
                                    "barrier_group_id": barrier_group_id,
                                    "task_name": "process_brightdata_pitchbook_data_task",
                                    "entity_type": "company",
                                    "dependent_on": "resolve_pitchbook_url_task",
                                },
                                priority=JobPriority.NORMAL,
                            )

                            logger.info(
                                f"Triggered PitchBook processing for {company_domain}"
                            )
                        finally:
                            await barrier_sync.cleanup()
                    else:
                        logger.warning(
                            f"Invalid barrier_group_id format: {barrier_group_id}"
                        )
                else:
                    logger.info("No snapshot ID found for PitchBook processing")
            finally:
                await s3_storage.cleanup()
        else:
            logger.info("No S3 key found for PitchBook processing")

    except Exception as e:
        logger.error(f"Error handling PitchBook completion: {e}")
        raise


def register_all_completion_handlers(barrier_sync, queue_service) -> None:
    """Register all task completion handlers with the barrier sync instance."""
    # Register completion handlers for initial tasks
    barrier_sync.register_task_completion_handler(
        "resolve_crunchbase_url_task",
        handle_crunchbase_resolver_completion,
        queue_service,
    )

    barrier_sync.register_task_completion_handler(
        "resolve_linkedin_url_task", handle_linkedin_resolver_completion, queue_service
    )

    barrier_sync.register_task_completion_handler(
        "generate_sitemap_task", handle_sitemap_generation_completion, queue_service
    )

    barrier_sync.register_task_completion_handler(
        "process_apollo_company_data_task",
        handle_apollo_company_data_completion,
        queue_service,
    )

    barrier_sync.register_task_completion_handler(
        "resolve_pitchbook_url_task",
        handle_pitchbook_resolver_completion,
        queue_service,
    )

    logger.info("Registered task completion handlers")
