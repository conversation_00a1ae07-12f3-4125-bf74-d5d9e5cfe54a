import React from 'react';
import { NoteContent } from '@/lib/types/deal-notes';

/**
 * Convert structured content to plain text for display
 */
export function structuredContentToText(content: NoteContent): string {
  let text = '';
  
  for (const element of content) {
    if (element.type === 'paragraph') {
      for (const child of element.children) {
        if ('type' in child && child.type === 'mention') {
          text += `@${child.name}`;
        } else if ('text' in child && typeof child.text === 'string') {
          text += child.text;
        }
      }
    }
  }
  
  return text;
}

/**
 * Render structured content as JSX with proper mention styling
 */
export function renderStructuredContent(content: NoteContent): JSX.Element {
  const text = structuredContentToText(content);
  
  // Simple mention highlighting for now
  const parts = text.split(/(@\w+)/g);
  const elements = parts.map((part, index) => {
    if (part.startsWith('@')) {
      return (
        <span key={index} className="inline-flex items-center gap-1 rounded-md bg-blue-100 px-1.5 py-0.5 text-sm font-medium text-blue-700">
          <span className="text-xs"></span>
          {part}
        </span>
      );
    }
    return part;
  });
  
  return <div className="whitespace-pre-wrap">{elements}</div>;
} 