"""
Barrier-Orchestrated Enrichment & Processing Framework (Async Version)

This module provides Redis + MongoDB coordination for parallel, dynamically expanding,
and dependency-aware orchestration of enrichment tasks using async MongoDB operations.
"""

import asyncio
import time
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

import redis.asyncio as redis  # type: ignore
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from redis.asyncio import Redis  # type: ignore

from app.configs import get_logger, settings
from app.models.base import Field, TractionXModel

logger = get_logger(__name__)


class BarrierStatus(str, Enum):
    """Status of a barrier group."""

    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    DONE = "done"
    ERROR = "error"
    TIMED_OUT = "timed_out"


class TaskLog(TractionXModel):
    """Log entry for a task execution."""

    task: str = Field(..., description="Task name")
    status: str = Field(..., description="Task status (success, error, timeout)")
    started_at: datetime = Field(..., description="When task started")
    finished_at: Optional[datetime] = Field(None, description="When task finished")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Task metadata")
    error_message: Optional[str] = Field(None, description="Error message if failed")


class BarrierDocument(TractionXModel):
    """MongoDB document for barrier tracking."""

    id: str = Field(
        ..., alias="_id", description="Barrier group ID (e.g., 'company:uuid')"
    )
    entity_type: str = Field(
        ..., description="Entity type (e.g., 'company', 'founder')"
    )
    entity_id: str = Field(..., description="Entity ID")
    expected_tasks: List[str] = Field(
        default_factory=list, description="All expected tasks"
    )
    completed_tasks: List[str] = Field(
        default_factory=list, description="Completed tasks"
    )
    status: BarrierStatus = Field(
        default=BarrierStatus.PENDING, description="Barrier status"
    )
    task_logs: List[TaskLog] = Field(
        default_factory=list, description="Task execution logs"
    )
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    created_at_unix: int = Field(
        default_factory=lambda: int(time.time()),
        description="UNIX timestamp when barrier was created",
    )
    updated_at_unix: int = Field(
        default_factory=lambda: int(time.time()),
        description="UNIX timestamp when barrier was last updated",
    )
    merge_triggered: bool = Field(
        default=False, description="Whether merge has been triggered"
    )
    merge_job_id: Optional[str] = Field(
        None, description="ID of the merge job if triggered"
    )


class BarrierSync:
    """
    Barrier synchronization for coordinating task completion across multiple workers.

    Uses Redis for fast coordination and MongoDB for persistent state tracking.
    """

    def __init__(
        self,
        redis_url: str,
        mongo_url: str,
        mongo_db: str = settings.MONGODB_DB_NAME,
        key_prefix: str = settings.REDIS_KEY_PREFIX,
        ttl_hours: int = 2,
    ):
        """
        Initialize barrier sync.

        Args:
            redis_url: Redis connection URL
            mongo_url: MongoDB connection URL
            mongo_db: MongoDB database name
            key_prefix: Redis key prefix
            ttl_hours: TTL for Redis keys in hours
        """
        self.redis_url = redis_url
        self.mongo_url = mongo_url
        self.mongo_db_name = mongo_db
        self.key_prefix = key_prefix
        self.ttl_seconds = ttl_hours * 3600

        # Connection instances
        self._redis_client: Optional[Redis] = None
        self.mongo_db_instance: Optional[AsyncIOMotorDatabase] = None

        # LUA script management
        self.lua_scripts: Dict[str, str] = {}
        self.lua_script_shas: Dict[str, str] = {}

        # Task completion handlers - centralized system for triggering dependent tasks
        self._task_completion_handlers: Dict[str, Callable] = {}
        self._queue_service = None

        # initialize
        self._initialized = False
        self._initializing = False

    async def initialize(self) -> None:
        """Initialize Redis and MongoDB connections."""
        # Prevent multiple initializations of the same instance
        if hasattr(self, "_initializing") and self._initializing:
            logger.debug(
                "Barrier sync initialization already in progress for this instance"
            )
            return

        if self._initialized:
            # Check if connections are still valid
            try:
                if self.redis is not None:
                    await self.redis.ping()
                if self.mongo_db_instance is not None:
                    await self.mongo_db_instance.command("ping")
                logger.debug(
                    "Barrier sync already initialized and connections are valid, skipping"
                )
                return
            except Exception as e:
                logger.warning(
                    f"Barrier sync connections are invalid, re-initializing: {e}"
                )
                # Reset instance state and continue with initialization
                self._initialized = False
                self.redis = None
                self.mongo_client = None
                self.mongo_db_instance = None

        self._initializing = True

        try:
            logger.info("Starting barrier sync initialization")

            # Initialize Redis
            self.redis = redis.from_url(
                self.redis_url,
                decode_responses=True,
                socket_connect_timeout=10,
                socket_timeout=30,
                retry_on_timeout=True,
                health_check_interval=30,
            )
            await self.redis.ping()

            # Initialize MongoDB
            self.mongo_client = AsyncIOMotorClient(self.mongo_url)
            self.mongo_db_instance = self.mongo_client[self.mongo_db_name]

            # Test MongoDB connection
            await self.mongo_db_instance.command("ping")

            # Load LUA scripts
            await self._load_lua_scripts(self.redis)

            # Create indexes
            await self._create_indexes()

            logger.info("Barrier sync initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize barrier sync: {e}")
            raise
        finally:
            self._initializing = False

    async def cleanup(self) -> None:
        """Clean up connections."""
        if self.redis is not None:
            try:
                await self.redis.close()
            except Exception as e:
                logger.warning(f"Error closing Redis connection: {e}")
            self.redis = None

        if self.mongo_client is not None:
            try:
                self.mongo_client.close()
            except Exception as e:
                logger.warning(f"Error closing MongoDB connection: {e}")
            self.mongo_client = None

        self.mongo_db_instance = None
        self._initialized = False

        # Reset instance state

        logger.info("Barrier sync cleaned up")

    async def _ensure_redis(self) -> Redis:
        """Ensure Redis connection is initialized and return it."""
        if not self._initialized or self.redis is None:
            await self.initialize()
        else:
            # Check if Redis connection is still valid
            try:
                await self.redis.ping()
            except Exception:
                logger.warning("Redis connection lost, reinitializing...")
                await self.initialize()

        assert self.redis is not None, "Redis connection not initialized"
        return self.redis

    async def _ensure_mongo(self) -> AsyncIOMotorDatabase:
        """Ensure MongoDB connection is initialized and return it."""
        if not self._initialized or self.mongo_db_instance is None:
            await self.initialize()
        else:
            # Check if MongoDB connection is still valid
            try:
                # Try to ping the database
                await self.mongo_db_instance.command("ping")
            except Exception:
                logger.warning("MongoDB connection lost, reinitializing...")
                await self.initialize()

        assert self.mongo_db_instance is not None, "MongoDB connection not initialized"
        return self.mongo_db_instance

    async def _load_lua_scripts(self, redis_client: Optional[Redis] = None) -> None:
        """Load LUA scripts for atomic operations."""
        # Use provided redis_client or get from _ensure_redis
        if redis_client is None:
            redis_client = await self._ensure_redis()

        # Atomic task completion and merge check script
        self.lua_scripts["atomic_complete_and_check"] = """
            local expected_key = KEYS[1]
            local completed_key = KEYS[2]
            local lock_key = KEYS[3]
            local task_name = ARGV[1]
            local current_time = ARGV[2]
            
            -- Add task to completed set
            redis.call('SADD', completed_key, task_name)
            
            -- Check if all expected tasks are completed
            local expected = redis.call('SMEMBERS', expected_key)
            local completed = redis.call('SMEMBERS', completed_key)
            
            -- Convert to sets for comparison
            local expected_set = {}
            for i, task in ipairs(expected) do
                expected_set[task] = true
            end
            
            local completed_set = {}
            for i, task in ipairs(completed) do
                completed_set[task] = true
            end
            
            -- Check if all expected tasks are completed
            local all_completed = true
            for task, _ in pairs(expected_set) do
                if not completed_set[task] then
                    all_completed = false
                    break
                end
            end
            
            -- If all completed, try to acquire lock for merge
            if all_completed then
                local lock_acquired = redis.call('SET', lock_key, current_time, 'NX', 'EX', 60)
                if lock_acquired then
                    return {1, 'merge_ready'}  -- Merge should be triggered
                else
                    return {0, 'merge_already_triggered'}  -- Another process is handling merge
                end
            else
                return {0, 'not_ready'}  -- Not all tasks completed
            end
        """

        # Register new tasks script
        self.lua_scripts["register_new_tasks"] = """
            local expected_key = KEYS[1]
            local num_tasks = tonumber(ARGV[1])
            
            local added_count = 0
            for i = 1, num_tasks do
                local task = ARGV[i + 1]
                local added = redis.call('SADD', expected_key, task)
                if added == 1 then
                    added_count = added_count + 1
                end
            end
            
            return added_count
        """

        # Load scripts into Redis (only if not already loaded)
        for script_name, script_content in self.lua_scripts.items():
            try:
                # Check if script is already loaded in this instance
                if script_name in self.lua_script_shas:
                    logger.debug(
                        f"LUA script {script_name} already loaded in this instance, skipping"
                    )
                    continue

                # Try to load the script - Redis will return the SHA if it already exists
                sha = await redis_client.script_load(script_content)
                self.lua_script_shas[script_name] = sha
                logger.debug(f"Loaded LUA script {script_name} with SHA: {sha[:8]}...")
            except Exception as e:
                logger.error(f"Failed to load LUA script {script_name}: {e}")
                raise

    async def _create_indexes(self) -> None:
        """Create MongoDB indexes for barrier documents."""
        mongo_db = await self._ensure_mongo()
        collection = mongo_db.barriers  # type: ignore

        # Create indexes
        indexes = [
            ("entity_type", 1),
            ("entity_id", 1),
            ("status", 1),
            ("created_at", 1),
            ("merge_triggered", 1),
        ]

        for field, direction in indexes:
            try:
                await collection.create_index(field)
            except Exception as e:
                logger.warning(f"Failed to create index on {field}: {e}")

        # Create TTL index for cleanup (skip if already exists)
        try:
            # Check if TTL index already exists
            existing_indexes = collection.list_indexes()
            ttl_index_exists = False

            async for index in existing_indexes:
                if (
                    index.get("name") == "updated_at_1"
                    and "expireAfterSeconds" in index
                ):
                    ttl_index_exists = True
                    break

            if not ttl_index_exists:
                await collection.create_index(
                    [("updated_at", 1)],
                    expireAfterSeconds=86400 * 7,  # 7 days
                )
                logger.info("Created TTL index on updated_at field")
            else:
                logger.info("TTL index on updated_at field already exists")

        except Exception as e:
            logger.warning(f"Failed to create TTL index: {e}")

    def _barrier_key(self, group_id: str, key_type: str) -> str:
        """Get Redis key for barrier operations."""
        return f"{self.key_prefix}:{group_id}:{key_type}"

    async def register_barrier(
        self,
        group_id: str,
        entity_type: str,
        entity_id: str,
        initial_tasks: List[str],
    ) -> bool:
        """
        Register a new barrier group with initial tasks.

        Args:
            group_id: Unique barrier group ID (e.g., "company:uuid")
            entity_type: Entity type (e.g., "company", "founder")
            entity_id: Entity ID
            initial_tasks: Initial list of expected tasks

        Returns:
            True if registration successful
        """
        try:
            redis_client = await self._ensure_redis()
            mongo_db = await self._ensure_mongo()

            # Create barrier document with explicit timestamps
            current_time = datetime.now(timezone.utc)
            current_unix = int(time.time())

            barrier_doc = BarrierDocument(
                id=group_id,  # type: ignore
                entity_type=entity_type,
                entity_id=entity_id,
                expected_tasks=initial_tasks,
                status=BarrierStatus.PENDING,
                created_at=current_time,
                updated_at=current_time,
                created_at_unix=current_unix,
                updated_at_unix=current_unix,
                merge_job_id=None,
            )  # type: ignore

            # Store in MongoDB
            collection = mongo_db.barriers  # type: ignore
            await collection.replace_one(
                {"_id": group_id},
                barrier_doc.model_dump(for_db=True),
                upsert=True,
            )

            # Initialize Redis sets
            expected_key = self._barrier_key(group_id, "expected")
            completed_key = self._barrier_key(group_id, "completed")

            # Add initial tasks to expected set
            if initial_tasks:
                await redis_client.sadd(expected_key, *initial_tasks)  # type: ignore
                await redis_client.expire(expected_key, self.ttl_seconds)  # type: ignore

            # Initialize completed set
            await redis_client.sadd(completed_key, "placeholder")  # type: ignore # Ensure set exists
            await redis_client.srem(completed_key, "placeholder")  # type: ignore # Remove placeholder
            await redis_client.expire(completed_key, self.ttl_seconds)  # type: ignore

            logger.info(
                f"Registered barrier {group_id} with {len(initial_tasks)} initial tasks"
            )
            return True

        except Exception as e:
            logger.error(f"Failed to register barrier {group_id}: {e}")
            return False

    async def register_tasks(
        self,
        group_id: str,
        new_tasks: List[str],
    ) -> int:
        """
        Register new tasks to an existing barrier group.

        Args:
            group_id: Barrier group ID
            new_tasks: List of new task names to register

        Returns:
            Number of new tasks added
        """
        try:
            redis_client = await self._ensure_redis()
            mongo_db = await self._ensure_mongo()

            # Add to Redis expected set using LUA script
            expected_key = self._barrier_key(group_id, "expected")

            # Ensure LUA scripts are loaded
            if "register_new_tasks" not in self.lua_script_shas:
                logger.warning(
                    "LUA script 'register_new_tasks' not loaded, falling back to basic Redis operations"
                )
                # Fallback to basic Redis operations
                added_count = 0
                for task in new_tasks:
                    if await redis_client.sadd(expected_key, task):  # type: ignore
                        added_count += 1
            else:
                script_sha = self.lua_script_shas["register_new_tasks"]
                args = [str(len(new_tasks))] + new_tasks
                added_count = await redis_client.evalsha(
                    script_sha, 1, expected_key, *args
                )  # type: ignore
                added_count = int(added_count)

            if added_count > 0:
                # Update MongoDB document
                collection = mongo_db.barriers  # type: ignore
                current_time = datetime.now(timezone.utc)
                current_unix = int(time.time())

                await collection.update_one(
                    {"_id": group_id},
                    {
                        "$addToSet": {"expected_tasks": {"$each": new_tasks}},
                        "$set": {
                            "updated_at": current_time,
                            "updated_at_unix": current_unix,
                        },
                    },
                )

                logger.info(f"Registered {added_count} new tasks to barrier {group_id}")

            return added_count

        except Exception as e:
            logger.error(f"Failed to register tasks for barrier {group_id}: {e}")
            return 0

    async def mark_task_complete(
        self,
        group_id: str,
        task_name: str,
        metadata: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None,
    ) -> bool:
        """
        Mark a task as completed and check if barrier is ready for merge.

        This method now includes a centralized task completion handler system
        that triggers dependent tasks immediately when specific tasks complete,
        before marking them as complete in the barrier.

        Args:
            group_id: Barrier group ID
            task_name: Name of the completed task
            metadata: Optional task metadata
            error_message: Error message if task failed

        Returns:
            True if task was marked complete, False if already completed
        """
        try:
            logger.info(
                f"Starting mark_task_complete for {task_name} in barrier {group_id}"
            )

            redis_client = await self._ensure_redis()
            mongo_db = await self._ensure_mongo()

            # Check if task is already completed
            completed_key = self._barrier_key(group_id, "completed")
            if await redis_client.sismember(completed_key, task_name):  # type: ignore
                logger.debug(
                    f"Task {task_name} already marked complete for barrier {group_id}"
                )
                return False

            # STEP 1: Execute task completion handler BEFORE marking as complete
            # This ensures dependent tasks are triggered before the barrier considers this task done
            if not error_message and metadata:
                task_result = {
                    "barrier_group_id": group_id,
                    "task_name": task_name,
                    "metadata": metadata,
                    "result": metadata.get("result", {}),
                }

                handler_success = await self._execute_task_completion_handler(
                    task_name, group_id, task_result
                )

                if not handler_success:
                    logger.warning(
                        f"Task completion handler failed for {task_name}, but continuing with barrier completion"
                    )

            # STEP 2: Update MongoDB with task log
            collection = mongo_db.barriers  # type: ignore

            task_log = TaskLog(
                task=task_name,
                status="success" if not error_message else "error",
                started_at=datetime.now(timezone.utc),
                finished_at=datetime.now(timezone.utc),  # type: ignore
                metadata=metadata or {},
                error_message=error_message,  # type: ignore
            )

            current_time = datetime.now(timezone.utc)
            current_unix = int(time.time())

            update_data = {
                "$push": {"task_logs": task_log.model_dump(for_db=True)},
                "$set": {
                    "updated_at": current_time,
                    "updated_at_unix": current_unix,
                },
            }

            if not error_message:
                update_data["$addToSet"] = {"completed_tasks": task_name}

            logger.info(
                f"Updating MongoDB for barrier {group_id} with task {task_name}"
            )
            result = await collection.update_one({"id": group_id}, update_data)
            logger.info(
                f"MongoDB update result: {result.modified_count} documents modified"
            )

            # STEP 3: Use LUA script for atomic completion check
            expected_key = self._barrier_key(group_id, "expected")
            lock_key = self._barrier_key(group_id, "lock")

            # Ensure LUA scripts are loaded
            if "atomic_complete_and_check" not in self.lua_script_shas:
                logger.warning(
                    "LUA script 'atomic_complete_and_check' not loaded, falling back to basic Redis operations"
                )
                # Fallback to basic Redis operations
                await redis_client.sadd(completed_key, task_name)  # type: ignore
                # Simple check if all expected tasks are completed
                expected_tasks = await redis_client.smembers(expected_key)  # type: ignore
                completed_tasks = await redis_client.smembers(completed_key)  # type: ignore

                if len(expected_tasks) == len(completed_tasks):
                    logger.info(
                        f"Barrier {group_id} ready for merge - all tasks completed (fallback)"
                    )
                    return True
                else:
                    logger.debug(
                        f"Barrier {group_id} not ready for merge yet (fallback)"
                    )
                    return False
            else:
                script_sha = self.lua_script_shas["atomic_complete_and_check"]

                logger.info(
                    f"Executing LUA script for barrier {group_id}, task {task_name}"
                )
                current_time = str(int(time.time()))
                result = await redis_client.evalsha(
                    script_sha,
                    3,
                    expected_key,
                    completed_key,
                    lock_key,
                    task_name,
                    current_time,
                )  # type: ignore
                logger.info(f"LUA script result: {result}")

                if result[0] == 1 and result[1] == "merge_ready":
                    logger.info(
                        f"Barrier {group_id} ready for merge - all tasks completed"
                    )
                    return True
                elif result[1] == "merge_already_triggered":
                    logger.debug(f"Merge already triggered for barrier {group_id}")
                    return False
                else:
                    logger.debug(f"Barrier {group_id} not ready for merge yet")
                    return False

        except Exception as e:
            logger.error(
                f"Failed to mark task {task_name} complete for barrier {group_id}: {e}"
            )
            return False

    async def get_barrier_status(self, group_id: str) -> Optional[BarrierDocument]:
        """
        Get the current status of a barrier group.

        Args:
            group_id: Barrier group ID

        Returns:
            Barrier document or None if not found
        """
        try:
            mongo_db = await self._ensure_mongo()
            collection = mongo_db.barriers  # type: ignore
            doc = await collection.find_one({"id": group_id})
            if doc:
                return BarrierDocument(**doc)
            return None

        except Exception as e:
            logger.error(f"Failed to get barrier status for {group_id}: {e}")
            return None

    async def list_barriers(
        self,
        entity_type: Optional[str] = None,
        status: Optional[BarrierStatus] = None,
        limit: int = 100,
    ) -> List[BarrierDocument]:
        """
        List barrier groups with optional filtering.

        Args:
            entity_type: Filter by entity type
            status: Filter by barrier status
            limit: Maximum number of results

        Returns:
            List of barrier documents
        """
        try:
            mongo_db = await self._ensure_mongo()
            collection = mongo_db.barriers  # type: ignore

            # Build filter
            filter_query = {}
            if entity_type:
                filter_query["entity_type"] = entity_type
            if status:
                filter_query["status"] = status

            cursor = collection.find(filter_query).sort("updated_at", -1).limit(limit)
            docs = await cursor.to_list(length=limit)

            return [BarrierDocument(**doc) for doc in docs]

        except Exception as e:
            logger.error(f"Failed to list barriers: {e}")
            return []

    async def cleanup_barrier(self, group_id: str) -> bool:
        """
        Clean up barrier data from Redis and MongoDB.

        Args:
            group_id: Barrier group ID

        Returns:
            True if cleanup successful
        """
        try:
            redis_client = await self._ensure_redis()
            mongo_db = await self._ensure_mongo()

            # Remove Redis keys
            expected_key = self._barrier_key(group_id, "expected")
            completed_key = self._barrier_key(group_id, "completed")
            lock_key = self._barrier_key(group_id, "lock")

            await redis_client.delete(expected_key, completed_key, lock_key)

            # Remove MongoDB document
            collection = mongo_db.barriers  # type: ignore
            await collection.delete_one({"_id": group_id})

            logger.info(f"Cleaned up barrier {group_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to cleanup barrier {group_id}: {e}")
            return False

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on barrier sync system."""
        try:
            # Check Redis
            redis_healthy = False
            if self.redis is not None:
                await self.redis.ping()
                redis_healthy = True

            # Check MongoDB
            mongo_healthy = False
            if self.mongo_db_instance is not None:
                await self.mongo_db_instance.command("ping")
                mongo_healthy = True

            # Get some basic stats
            stats = {}
            if mongo_healthy:
                collection = self.mongo_db_instance.barriers  # type: ignore
                stats["total_barriers"] = await collection.count_documents({})
                stats["pending_barriers"] = await collection.count_documents({
                    "status": BarrierStatus.PENDING
                })
                stats["in_progress_barriers"] = await collection.count_documents({
                    "status": BarrierStatus.IN_PROGRESS
                })

            return {
                "status": "healthy" if redis_healthy and mongo_healthy else "unhealthy",
                "redis": "healthy" if redis_healthy else "unhealthy",
                "mongodb": "healthy" if mongo_healthy else "unhealthy",
                "stats": stats,
            }

        except Exception as e:
            logger.error(f"Barrier sync health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "redis": "unknown",
                "mongodb": "unknown",
                "stats": {},
            }

    def register_task_completion_handler(
        self, task_name: str, handler: Callable, queue_service=None
    ) -> None:
        """
        Register a completion handler for a specific task.

        This is a synchronous method that stores handler references.

        Args:
            task_name: Name of the task to handle
            handler: Async function that takes (barrier_group_id, task_result, queue_service) and triggers dependent tasks
            queue_service: Queue service instance for enqueueing dependent tasks
        """
        # Explicitly ensure this is not treated as a coroutine
        if asyncio.iscoroutinefunction(handler):
            # Store the handler as-is (it will be awaited when called)
            self._task_completion_handlers[task_name] = handler
        else:
            # For non-async handlers, wrap them to maintain consistency
            async def wrapped_handler(*args, **kwargs):
                return handler(*args, **kwargs)

            self._task_completion_handlers[task_name] = wrapped_handler

        if queue_service:
            self._queue_service = queue_service
        logger.info(f"Registered completion handler for task: {task_name}")

    async def _execute_task_completion_handler(
        self, task_name: str, barrier_group_id: str, task_result: Dict[str, Any]
    ) -> bool:
        """
        Execute the completion handler for a specific task.

        Args:
            task_name: Name of the completed task
            barrier_group_id: Barrier group ID
            task_result: Result from the completed task

        Returns:
            True if handler executed successfully, False otherwise
        """
        if task_name not in self._task_completion_handlers:
            logger.debug(f"No completion handler registered for task: {task_name}")
            return True  # No handler means no dependent tasks to trigger

        try:
            handler = self._task_completion_handlers[task_name]
            logger.info(f"Executing completion handler for task: {task_name}")

            # Execute the handler
            if asyncio.iscoroutinefunction(handler):
                await handler(barrier_group_id, task_result, self._queue_service)
            else:
                # Run sync handler in thread pool
                loop = asyncio.get_running_loop()
                await loop.run_in_executor(
                    None, handler, barrier_group_id, task_result, self._queue_service
                )

            logger.info(
                f"Successfully executed completion handler for task: {task_name}"
            )
            return True

        except Exception as e:
            logger.error(
                f"Error executing completion handler for task {task_name}: {e}"
            )
            return False
