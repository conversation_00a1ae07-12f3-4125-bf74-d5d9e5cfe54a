/**
 * Dashboard Data Hook
 * 
 * Custom hook for fetching and managing dashboard summary data.
 */

import { useState, useEffect, useCallback } from 'react';
import { DashboardAPI, DashboardSummary } from '@/lib/api/dashboard-api';
import { useToast } from '@/components/ui/use-toast';

interface UseDashboardReturn {
  data: DashboardSummary | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useDashboard(): UseDashboardReturn {
  const [data, setData] = useState<DashboardSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchDashboard = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const summary = await DashboardAPI.getSummary();
      setData(summary);
      
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to fetch dashboard data';
      setError(errorMessage);
      
      toast({
        title: "Error",
        description: "Failed to load dashboard data. Please try again.",
        variant: "destructive",
      });
      
      console.error('Dashboard fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const refetch = useCallback(async () => {
    await fetchDashboard();
  }, [fetchDashboard]);

  useEffect(() => {
    fetchDashboard();
  }, [fetchDashboard]);

  return {
    data,
    loading,
    error,
    refetch,
  };
}
