"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Users, Plus, X, Mail, ArrowRight, UserCheck } from "lucide-react"
import { toast } from "sonner"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { SettingsAPI } from "@/lib/api/settings-api"
import { cn } from "@/lib/utils"

interface TeamMembersStepProps {
  user: any
  stepData: any
  onNext: (data?: any) => void
  onBack: () => void
  onSkip: () => void
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
  currentStep: number
  totalSteps: number
}

interface TeamMember {
  email: string
  role: 'admin' | 'member'
}

export function TeamMembersStep({ 
  onNext, 
  onSkip, 
  isLoading, 
  setIsLoading 
}: TeamMembersStepProps) {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([])
  const [currentEmail, setCurrentEmail] = useState("")
  const [currentRole, setCurrentRole] = useState<'admin' | 'member'>('member')
  const [emailError, setEmailError] = useState("")

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const handleAddMember = () => {
    setEmailError("")

    if (!currentEmail.trim()) {
      setEmailError("Email is required")
      return
    }

    if (!validateEmail(currentEmail)) {
      setEmailError("Please enter a valid email address")
      return
    }

    if (teamMembers.some(member => member.email.toLowerCase() === currentEmail.toLowerCase())) {
      setEmailError("This email has already been added")
      return
    }

    setTeamMembers(prev => [...prev, { email: currentEmail.trim(), role: currentRole }])
    setCurrentEmail("")
    setCurrentRole('member')
  }

  const handleRemoveMember = (index: number) => {
    setTeamMembers(prev => prev.filter((_, i) => i !== index))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAddMember()
    }
  }

  const handleSendInvites = async () => {
    try {
      setIsLoading(true)
      
      // Send invitations using the existing API
      const emails = teamMembers.map(member => member.email)
      await SettingsAPI.inviteMembers({ 
        emails,
        message: "Welcome to our TractionX workspace! You've been invited to join our investment team."
      })
      
      toast.success(`Invitations sent to ${teamMembers.length} team member${teamMembers.length > 1 ? 's' : ''}!`)
      
      // Pass data to next step
      onNext({ teamMembers })
    } catch (error: any) {
      console.error("Failed to send invitations:", error)
      toast.error(error.message || "Failed to send invitations")
    } finally {
      setIsLoading(false)
    }
  }

  const handleSkipForNow = () => {
    onSkip()
  }

  return (
    <div className="flex-1 flex flex-col p-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <div className="flex items-center justify-center mb-4">
          <div className="flex size-16 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg">
            <Users className="size-8 text-white" />
          </div>
        </div>
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          Who else should have access?
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Invite your team members to collaborate on deal evaluation and investment decisions.
        </p>
      </motion.div>

      {/* Add Member Form */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="max-w-2xl mx-auto w-full mb-8"
      >
        <div className="bg-gray-50 rounded-xl p-6 border border-gray-100">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Label htmlFor="email" className="text-sm font-medium text-gray-700 mb-2 block">
                Email Address
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={currentEmail}
                onChange={(e) => {
                  setCurrentEmail(e.target.value)
                  setEmailError("")
                }}
                onKeyPress={handleKeyPress}
                className={cn(
                  "h-12",
                  emailError && "border-red-300 focus:border-red-500 focus:ring-red-200"
                )}
              />
              {emailError && (
                <p className="text-sm text-red-600 mt-1">{emailError}</p>
              )}
            </div>

            <div className="sm:w-40">
              <Label className="text-sm font-medium text-gray-700 mb-2 block">
                Role
              </Label>
              <Select value={currentRole} onValueChange={(value: 'admin' | 'member') => setCurrentRole(value)}>
                <SelectTrigger className="h-12">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="member">Member</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="sm:w-auto sm:flex sm:items-end">
              <Button
                onClick={handleAddMember}
                className="h-12 px-6 bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Plus className="size-4 mr-2" />
                Add
              </Button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Team Members List */}
      {teamMembers.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="max-w-2xl mx-auto w-full mb-8"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Team Members ({teamMembers.length})
          </h3>
          <div className="space-y-3">
            {teamMembers.map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200 shadow-sm"
              >
                <div className="flex items-center gap-3">
                  <div className="flex size-10 items-center justify-center rounded-full bg-gray-100">
                    <Mail className="size-5 text-gray-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{member.email}</p>
                    <Badge variant={member.role === 'admin' ? 'default' : 'secondary'} className="text-xs">
                      {member.role === 'admin' ? 'Admin' : 'Member'}
                    </Badge>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveMember(index)}
                  className="text-gray-400 hover:text-red-600"
                >
                  <X className="size-4" />
                </Button>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="flex items-center justify-center gap-4 pt-8"
      >
        <Button
          variant="outline"
          onClick={handleSkipForNow}
          size="lg"
          className="px-8 py-3 text-lg"
        >
          Skip for Now
        </Button>

        {teamMembers.length > 0 && (
          <Button
            onClick={handleSendInvites}
            disabled={isLoading}
            size="lg"
            className={cn(
              "bg-gradient-to-r from-blue-500 to-blue-600 text-white",
              "hover:from-blue-600 hover:to-blue-700",
              "px-8 py-3 text-lg font-semibold",
              "shadow-lg hover:shadow-xl transition-all duration-300",
              "flex items-center gap-2"
            )}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent" />
                Sending Invites...
              </>
            ) : (
              <>
                <UserCheck className="size-5" />
                Send Invites
                <ArrowRight className="size-5" />
              </>
            )}
          </Button>
        )}
      </motion.div>

      {/* Helper Text */}
      <motion.p
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="text-center text-sm text-gray-500 mt-4"
      >
        You can always manage team members later in Settings
      </motion.p>
    </div>
  )
}
