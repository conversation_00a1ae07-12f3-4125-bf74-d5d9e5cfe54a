"use client"

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { FileText } from 'lucide-react';
import { Icons } from '@/components/icons';
import { cn } from '@/lib/utils';
import { type Question } from '@/lib/types/form';
import { FileUpload } from './file-upload';
import { FileDisplay } from './file-display';

interface QuestionRendererProps {
  question: Question;
  value: any;
  error?: string;
  onChange: (value: any) => void;
  submissionId?: string;
  accessToken?: string;
  mode?: 'edit' | 'view'; // Add mode prop for read-only display
  readOnly?: boolean; // Add readOnly prop for preview mode
}

export function QuestionRenderer({ question, value, error, onChange, submissionId, accessToken, mode = 'edit', readOnly = false }: QuestionRendererProps) {
  const [focused, setFocused] = useState(false);
  const [calendarOpen, setCalendarOpen] = useState(false);

  const handleChange = (newValue: any) => {
    onChange(newValue);
  };

  const renderInput = () => {
    const questionType = question.type.toLowerCase();

    switch (questionType) {
      case 'short_text':
        return (
          <Input
            value={value || ''}
            onChange={readOnly ? undefined : (e) => handleChange(e.target.value)}
            onFocus={readOnly ? undefined : () => setFocused(true)}
            onBlur={readOnly ? undefined : () => setFocused(false)}
            placeholder={readOnly ? '' : (question.help_text || `Enter ${question.label.toLowerCase()}...`)}
            required={question.required}
            disabled={readOnly}
            className={cn(
              "border-2 transition-all duration-200",
              focused && !readOnly ? "border-blue-400 shadow-lg" : "border-gray-200",
              error ? "border-red-400" : "",
              readOnly ? "cursor-not-allowed bg-gray-50" : ""
            )}
          />
        );

      case 'long_text':
        return (
          <Textarea
            value={value || ''}
            onChange={readOnly ? undefined : (e) => handleChange(e.target.value)}
            onFocus={readOnly ? undefined : () => setFocused(true)}
            onBlur={readOnly ? undefined : () => setFocused(false)}
            placeholder={readOnly ? '' : (question.help_text || `Enter ${question.label.toLowerCase()}...`)}
            required={question.required}
            disabled={readOnly}
            className={cn(
              "min-h-[120px] resize-y border-2 transition-all duration-200",
              focused && !readOnly ? "border-blue-400 shadow-lg" : "border-gray-200",
              error ? "border-red-400" : "",
              readOnly ? "cursor-not-allowed bg-gray-50" : ""
            )}
          />
        );

      case 'number':
        return (
          <Input
            type="number"
            value={value || ''}
            onChange={readOnly ? undefined : (e) => handleChange(e.target.value ? Number(e.target.value) : null)}
            onFocus={readOnly ? undefined : () => setFocused(true)}
            onBlur={readOnly ? undefined : () => setFocused(false)}
            placeholder={readOnly ? '' : "Enter number..."}
            min={question.validation?.min}
            max={question.validation?.max}
            disabled={readOnly}
            className={cn(
              "border-2 transition-all duration-200",
              focused && !readOnly ? "border-blue-400 shadow-lg" : "border-gray-200",
              error ? "border-red-400" : "",
              readOnly ? "cursor-not-allowed bg-gray-50" : ""
            )}
          />
        );

      case 'range':
        return (
          <div className="space-y-4">
            <Slider
              value={[value || question.validation?.min || 0]}
              onValueChange={readOnly ? undefined : (values) => handleChange(values[0])}
              min={question.validation?.min || 0}
              max={question.validation?.max || 100}
              step={1}
              disabled={readOnly}
              className={cn("w-full", readOnly ? "cursor-not-allowed opacity-50" : "")}
            />
            <div className="flex justify-between text-sm text-gray-500">
              <span>{question.validation?.min || 0}</span>
              <span className="font-medium text-blue-600">{value || question.validation?.min || 0}</span>
              <span>{question.validation?.max || 100}</span>
            </div>
          </div>
        );

      case 'single_select':
        return (
          <div className="space-y-3">
            {question.options?.map((option) => (
              <motion.div
                key={option.value}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  variant={value === option.value ? "default" : "outline"}
                  onClick={readOnly ? undefined : () => handleChange(option.value)}
                  disabled={readOnly}
                  className={cn(
                    "h-auto w-full justify-start p-4 text-left transition-all duration-200",
                    value === option.value
                      ? "bg-blue-600 text-white shadow-lg"
                      : "border-2 border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50",
                    readOnly ? "cursor-not-allowed opacity-75" : ""
                  )}
                >
                  <div className="flex items-center space-x-3">
                    <div className={cn(
                      "flex size-4 items-center justify-center rounded-full border-2",
                      value === option.value ? "border-white" : "border-gray-400"
                    )}>
                      {value === option.value && (
                        <div className="size-2 rounded-full bg-white" />
                      )}
                    </div>
                    <span className="font-medium">{option.label}</span>
                  </div>
                </Button>
              </motion.div>
            ))}
          </div>
        );

      case 'multi_select':
        const selectedValues = Array.isArray(value) ? value : [];
        return (
          <div className="space-y-3">
            {question.options?.map((option) => {
              const isSelected = selectedValues.includes(option.value);
              return (
                <motion.div
                  key={option.value}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    variant="outline"
                    onClick={readOnly ? undefined : () => {
                      const newValues = isSelected
                        ? selectedValues.filter(v => v !== option.value)
                        : [...selectedValues, option.value];
                      handleChange(newValues);
                    }}
                    disabled={readOnly}
                    className={cn(
                      "h-auto w-full justify-start p-4 text-left transition-all duration-200",
                      isSelected
                        ? "border-blue-400 bg-blue-50 text-blue-900"
                        : "border-2 border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50",
                      readOnly ? "cursor-not-allowed opacity-75" : ""
                    )}
                  >
                    <div className="flex items-center space-x-3">
                      <div className={cn(
                        "flex size-4 items-center justify-center rounded border-2",
                        isSelected ? "border-blue-600 bg-blue-600" : "border-gray-400"
                      )}>
                        {isSelected && (
                          <Icons.check className="size-3 text-white" />
                        )}
                      </div>
                      <span className="font-medium">{option.label}</span>
                    </div>
                  </Button>
                </motion.div>
              );
            })}
          </div>
        );

      case 'boolean':
        return (
          <div className="flex items-center justify-start space-x-8 rounded-lg bg-gray-50 p-6">
            <div className="flex items-center space-x-3">
              <Switch
                checked={value === true}
                onCheckedChange={readOnly ? undefined : (checked) => handleChange(checked)}
                disabled={readOnly}
                className={cn(
                  "data-[state=checked]:bg-blue-600",
                  readOnly ? "cursor-not-allowed opacity-50" : ""
                )}
              />
              <Label className="text-lg font-medium">
                {value === true ? 'Yes' : value === false ? 'No' : 'Select'}
              </Label>
            </div>
          </div>
        );

      case 'date':
        return (
          <Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                disabled={readOnly}
                className={cn(
                  "h-12 w-full justify-start border-2 text-left font-normal transition-all duration-200",
                  !value && "text-muted-foreground",
                  focused && !readOnly ? "border-blue-400 shadow-lg" : "border-gray-200",
                  error ? "border-red-400" : "",
                  readOnly ? "cursor-not-allowed bg-gray-50 opacity-75" : ""
                )}
                onFocus={readOnly ? undefined : () => setFocused(true)}
                onBlur={readOnly ? undefined : () => setFocused(false)}
              >
                <Icons.calendar className="mr-2 size-4" />
                {value ? format(new Date(value), "PPP") : "Pick a date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={value ? new Date(value) : undefined}
                onSelect={(date) => {
                  handleChange(date?.toISOString());
                  setCalendarOpen(false);
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        );

      case 'file':
        const maxSizeMB = question.validation?.max || 10;
        const allowedTypes = question.validation?.regex || '.pdf,.doc,.docx,.jpg,.jpeg,.png';

        // Check if we have a file answer to display
        const fileAnswer = value && typeof value === 'object' && value.file ? value : null;

        // In view mode or read-only mode, just display the file if it exists
        if ((mode === 'view' || readOnly) && fileAnswer) {
          return (
            <FileDisplay
              fileAnswer={fileAnswer}
              accessToken={accessToken}
              mode="view"
            />
          );
        }

        // In edit mode with submissionId and not read-only, use FileUpload component
        if (mode === 'edit' && submissionId && !readOnly) {
          const questionId = question._id || question.id;
          if (!questionId) return null;
          
          return (
            <FileUpload
              questionId={questionId}
              submissionId={submissionId}
              value={value}
              onChange={handleChange}
              maxSizeMB={maxSizeMB}
              allowedTypes={allowedTypes}
              accessToken={accessToken}
              error={error}
            />
          );
        }

        // Fallback for when submissionId is not available or read-only mode
        return (
          <div className="space-y-3">
            <div className={cn(
              "rounded-lg border-2 border-dashed p-8 text-center",
              readOnly ? "bg-gray-100" : "bg-gray-50"
            )}>
              <FileText className="mx-auto mb-4 size-12 text-gray-400" />
              {fileAnswer ? (
                <div>
                  <p className="mb-2 font-medium text-gray-800">{fileAnswer.filename}</p>
                  <p className="text-sm text-gray-600">
                    {readOnly ? "File (preview mode)" : "File uploaded"}
                  </p>
                </div>
              ) : (
                <div>
                  <p className="mb-2 text-gray-600">
                    {readOnly ? "File upload field" : "File upload"}
                  </p>
                  <p className="text-sm text-gray-500">
                    Supports: {allowedTypes} (Max {maxSizeMB}MB)
                    {readOnly && " - Preview mode"}
                  </p>
                </div>
              )}
            </div>
          </div>
        );

      default:
        return (
          <Input
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={`Enter ${question.label.toLowerCase()}...`}
            className="border-2 border-gray-200 focus:border-blue-400"
          />
        );
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-3"
      data-error={!!error}
    >
      {/* Question Label */}
      <div className="flex items-center space-x-2">
        <Label className="text-lg font-semibold text-gray-900">
          {question.label}
        </Label>
        {question.required && (
          <Badge variant="destructive" className="text-xs">
            Required
          </Badge>
        )}
      </div>

      {/* Help Text */}
      {question.help_text && (
        <p className="text-sm leading-relaxed text-gray-600">
          {question.help_text}
        </p>
      )}

      {/* Input Field */}
      <div className="relative">
        {renderInput()}

        {/* Error Message */}
        {error && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-2 flex items-center space-x-1 text-sm text-red-600"
          >
            <Icons.alertCircle className="size-4" />
            <span>{error}</span>
          </motion.p>
        )}
      </div>
    </motion.div>
  );
}
