"use client"

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Icons } from '@/components/icons';
import { QuestionRenderer } from './question-renderer';
import { evaluateVisibility } from '@/lib/utils/form-logic';

interface ControllerRepeatableSectionProps {
  controllerQuestion: any;
  controllerValue: any;
  form: any;
  answers: Record<string, any>;
  errors: Record<string, string>;
  onAnswerChange: (questionId: string, value: any, repeatIndex?: number) => void;
  onRemoveRepeatAnswers?: (sectionId: string, repeatIndex: number) => void;
  submissionId?: string;
  accessToken?: string;
}

interface FormSectionProps {
  section: {
    _id: string;
    title: string;
    description?: string;
    repeatable: boolean;
    max_repeats?: number;
    questions: any[];
    visibility_condition?: any;
  };
  form?: any; // Add form data to support controller-based repeatable sections
  answers: Record<string, any>;
  errors: Record<string, string>;
  onAnswerChange: (questionId: string, value: any, repeatIndex?: number) => void;
  onRemoveRepeatAnswers?: (sectionId: string, repeatIndex: number) => void;
  sectionIndex: number;
  submissionId?: string;
  accessToken?: string;
  readOnly?: boolean; // Add read-only support for preview mode
}

function ControllerRepeatableSection({
  controllerQuestion,
  controllerValue,
  form,
  answers,
  errors,
  onAnswerChange,
  onRemoveRepeatAnswers,
  submissionId,
  accessToken
}: ControllerRepeatableSectionProps) {
  // Find the target section that this question controls
  const targetSection = form.sections?.find((s: any) => s._id === controllerQuestion.repeat_section_id);
  
  if (!targetSection) {
    return null;
  }

  // Calculate number of instances based on controller value
  const numInstances = Math.max(0, Math.min(Number(controllerValue) || 0, controllerQuestion.max_repeats || 10));
  
  if (numInstances === 0) {
    return null;
  }

  return (
    <div className="mt-6 space-y-4">
      {Array.from({ length: numInstances }, (_, instanceIndex) => (
        <motion.div
          key={`${targetSection._id}_${instanceIndex}`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="border-l-4 border-l-blue-500 bg-gray-50/50">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg font-medium text-gray-800">
                {targetSection.title}
                <span className="rounded-full bg-blue-100 px-2 py-1 text-sm font-normal text-blue-700">
                  #{instanceIndex + 1}
                </span>
              </CardTitle>
              {targetSection.description && (
                <p className="mt-1 text-sm text-gray-600">
                  {targetSection.description}
                </p>
              )}
            </CardHeader>
            <CardContent className="space-y-4">
              {targetSection.questions?.map((subQuestion: any, subQuestionIndex: number) => {
                // Check if sub-question is visible
                const subQuestionVisible = subQuestion.visibility_condition
                  ? evaluateVisibility(subQuestion.visibility_condition, answers)
                  : true;

                if (!subQuestionVisible) {
                  return null;
                }

                const scopedQuestionId = `${subQuestion._id}_${instanceIndex}`;

                return (
                  <motion.div
                    key={scopedQuestionId}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: subQuestionIndex * 0.1 }}
                  >
                    <QuestionRenderer
                      question={subQuestion}
                      value={answers[scopedQuestionId]}
                      error={errors[scopedQuestionId]}
                      onChange={(value) => onAnswerChange(subQuestion._id, value, instanceIndex)}
                      submissionId={submissionId}
                      accessToken={accessToken}
                    />
                  </motion.div>
                );
              })}
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );
}

export function FormSection({
  section,
  form,
  answers,
  errors,
  onAnswerChange,
  onRemoveRepeatAnswers,
  sectionIndex,
  submissionId,
  accessToken,
  readOnly = false
}: FormSectionProps) {
  const [repeatInstances, setRepeatInstances] = useState<number[]>([0]);

  // Create visibility context with useMemo to ensure it updates when answers change
  const visibilityContext = useMemo(() => {
    // Debug: Log when visibility context is updated
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 Visibility context updated for section:', section.title, {
        answersCount: Object.keys(answers).length,
        answers: answers,
        sectionId: section._id
      });
    }
    
    return answers; // Use answers directly for form-logic.ts evaluateVisibility
  }, [answers, section._id, section.title]);

  // Check if section is visible with enhanced debugging
  const isVisible = useMemo(() => {
    const visible = section.visibility_condition
      ? evaluateVisibility(section.visibility_condition, visibilityContext)
      : true;
      
    return visible;
  }, [section.visibility_condition, visibilityContext, section.title, section._id]);

  if (!isVisible) {
    return null;
  }

  const addRepeatInstance = () => {
    const maxRepeats = section.max_repeats || 10;
    if (repeatInstances.length < maxRepeats) {
      setRepeatInstances([...repeatInstances, repeatInstances.length]);
    }
  };

  const removeRepeatInstance = (index: number) => {
    if (repeatInstances.length > 1) {
      const newInstances = repeatInstances.filter((_, i) => i !== index);
      setRepeatInstances(newInstances);

      // Clean up answers for removed instance
      if (onRemoveRepeatAnswers) {
        onRemoveRepeatAnswers(section._id, index);
      }
    }
  };

  const renderSectionInstance = (instanceIndex: number) => (
    <motion.div
      key={`${section._id}_${instanceIndex}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="relative"
    >
      <Card className="border-0 bg-white/80 shadow-lg backdrop-blur-sm transition-shadow duration-300 hover:shadow-xl">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-3 text-2xl font-semibold text-gray-900">
                {section.title}
                {section.repeatable && repeatInstances.length > 1 && (
                  <span className="rounded-full bg-blue-100 px-2 py-1 text-sm font-normal text-blue-700">
                    #{instanceIndex + 1}
                  </span>
                )}
              </CardTitle>
              {section.description && (
                <p className="mt-2 leading-relaxed text-gray-600">
                  {section.description}
                </p>
              )}
            </div>

            {section.repeatable && instanceIndex > 0 && !readOnly && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeRepeatInstance(instanceIndex)}
                className="text-red-500 hover:bg-red-50 hover:text-red-700"
              >
                <Icons.trash className="size-4" />
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Precompute question visibility to avoid calling hooks in a loop */}
          {(() => {
            const questionVisibilities = section.questions.map((question) => {
              return question.visibility_condition
                ? evaluateVisibility(question.visibility_condition, visibilityContext)
                : true;
            });
            return section.questions.map((question, questionIndex) => {
              const questionVisible = questionVisibilities[questionIndex];
            if (!questionVisible) {
              return null;
            }

            const questionId = section.repeatable
              ? `${question._id}_${instanceIndex}`
              : question._id;

            return (
              <motion.div
                key={questionId}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: questionIndex * 0.1 }}
              >
                <QuestionRenderer
                  question={question}
                  value={answers[questionId]}
                  error={errors[questionId]}
                  onChange={readOnly ? () => {} : (value) => {
                    onAnswerChange(question._id, value, section.repeatable ? instanceIndex : undefined);
                  }}
                  submissionId={submissionId}
                  accessToken={accessToken}
                  readOnly={readOnly}
                />
                {/* Render controller-based repeatable sections */}
                {question.repeat_section_id && !section.repeatable && form && (
                  <ControllerRepeatableSection
                    controllerQuestion={question}
                    controllerValue={answers[question._id]}
                    form={form}
                    answers={answers}
                    errors={errors}
                    onAnswerChange={onAnswerChange}
                    onRemoveRepeatAnswers={onRemoveRepeatAnswers}
                    submissionId={submissionId}
                    accessToken={accessToken}
                  />
                )}
              </motion.div>
            );
            });
          })()}
        </CardContent>
      </Card>
    </motion.div>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: sectionIndex * 0.1 }}
      className="space-y-6"
    >
      <AnimatePresence>
        {repeatInstances.map((_, index) => renderSectionInstance(index))}
      </AnimatePresence>

      {/* Add Another Button for Repeatable Sections */}
      {section.repeatable && repeatInstances.length < (section.max_repeats || 10) && !readOnly && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <Button
            variant="outline"
            onClick={addRepeatInstance}
            className="border-2 border-dashed border-gray-300 bg-white/80 px-6 py-3 backdrop-blur-sm transition-all duration-200 hover:border-blue-400 hover:bg-blue-50"
          >
            <Icons.add className="mr-2 size-4" />
            Add Another {section.title}
          </Button>
        </motion.div>
      )}

      {/* Read-only indicator for repeatable sections */}
      {section.repeatable && readOnly && (
        <div className="text-center text-sm italic text-gray-500">
          Repeatable section (preview mode)
        </div>
      )}
    </motion.div>
  );
}
