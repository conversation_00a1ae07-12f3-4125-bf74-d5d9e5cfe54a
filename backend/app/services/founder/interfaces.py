"""Founder service interfaces."""

from typing import Any, Dict, List, Optional, Protocol


class FounderServiceInterface(Protocol):
    """Interface for founder services."""

    async def initialize(self) -> None:
        """Initialize service dependencies."""
        ...

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        ...

    async def get_founder_with_all_relations(self, founder_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a single founder with all related data.
        
        Args:
            founder_id: The founder UUID
            
        Returns:
            Dictionary with founder data and all relations, or None if not found
        """
        ...

    async def get_founders_by_company_id(self, company_id: str) -> List[Dict[str, Any]]:
        """
        Get all founders for a company with their related data.
        
        Args:
            company_id: The company identifier (website URL)
            
        Returns:
            List of founder dictionaries with all relations
        """
        ...
