"""
Audit logging helper for TractionX AI Agent system.
"""

from typing import Dict, Any, Optional, List
import logging
import time
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorDatabase
from ai.models import AgentLog, AgentLogLevel, PyObjectId
from ai.db import get_ai_database
from ai.config import settings

logger = logging.getLogger(__name__)


class AuditLogger:
    """Audit logger for AI agent operations."""

    def __init__(self):
        self.db = None

    async def initialize(self) -> None:
        """Initialize audit logger."""
        try:
            async for database in get_ai_database():
                self.db = database
                break

            if self.db is None:
                raise Exception("Could not get database connection")

            logger.info("Audit logger initialized")

        except Exception as e:
            logger.error(f"Failed to initialize audit logger: {e}")
            raise

    async def log_agent_execution(
        self,
        agent_id: str,
        user_id: str,
        org_id: str,
        input_data: Optional[Dict[str, Any]] = None,
        output_data: Optional[Dict[str, Any]] = None,
        tools_used: Optional[List[str]] = None,
        execution_time: Optional[float] = None,
        token_usage: Optional[Dict[str, int]] = None,
        error: Optional[str] = None,
        stack_trace: Optional[str] = None,
        session_id: Optional[str] = None,
        deal_id: Optional[str] = None,
        level: AgentLogLevel = AgentLogLevel.INFO,
        message: str = "Agent execution",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """Log agent execution."""

        if not settings.AUDIT_ENABLED:
            return None

        try:
            # Create log entry
            log_entry = AgentLog(
                agent_id=PyObjectId(agent_id),
                user_id=PyObjectId(user_id),
                org_id=PyObjectId(org_id),
                session_id=session_id,
                deal_id=PyObjectId(deal_id) if deal_id else None,
                level=level,
                message=message,
                input_data=input_data if settings.AUDIT_LOG_INPUTS else None,
                output_data=output_data if settings.AUDIT_LOG_OUTPUTS else None,
                tools_used=tools_used or [],
                execution_time=execution_time,
                token_usage=token_usage,
                error=error,
                stack_trace=stack_trace,
                metadata=metadata or {}
            )

            # Insert into database
            result = await self.db.agent_logs.insert_one(
                log_entry.dict(by_alias=True, exclude_none=True)
            )

            log_id = str(result.inserted_id)
            logger.debug(f"Logged agent execution: {log_id}")

            return log_id

        except Exception as e:
            logger.error(f"Failed to log agent execution: {e}")
            return None

    async def log_error(
        self,
        agent_id: str,
        user_id: str,
        org_id: str,
        error: str,
        stack_trace: Optional[str] = None,
        input_data: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None,
        deal_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """Log agent error."""

        return await self.log_agent_execution(
            agent_id=agent_id,
            user_id=user_id,
            org_id=org_id,
            input_data=input_data,
            error=error,
            stack_trace=stack_trace,
            session_id=session_id,
            deal_id=deal_id,
            level=AgentLogLevel.ERROR,
            message=f"Agent execution error: {error}",
            metadata=metadata
        )

    async def log_tool_usage(
        self,
        agent_id: str,
        user_id: str,
        org_id: str,
        tool_name: str,
        tool_input: Optional[Dict[str, Any]] = None,
        tool_output: Optional[Dict[str, Any]] = None,
        execution_time: Optional[float] = None,
        session_id: Optional[str] = None,
        deal_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """Log tool usage."""

        if not settings.AUDIT_LOG_TOOLS:
            return None

        return await self.log_agent_execution(
            agent_id=agent_id,
            user_id=user_id,
            org_id=org_id,
            input_data=tool_input,
            output_data=tool_output,
            tools_used=[tool_name],
            execution_time=execution_time,
            session_id=session_id,
            deal_id=deal_id,
            level=AgentLogLevel.DEBUG,
            message=f"Tool usage: {tool_name}",
            metadata=metadata
        )

    async def get_agent_logs(
        self,
        org_id: str,
        agent_id: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        deal_id: Optional[str] = None,
        level: Optional[AgentLogLevel] = None,
        limit: int = 100,
        skip: int = 0
    ) -> List[Dict[str, Any]]:
        """Get agent logs with filtering."""

        try:
            # Build filter
            filter_query = {"org_id": PyObjectId(org_id)}

            if agent_id:
                filter_query["agent_id"] = PyObjectId(agent_id)
            if user_id:
                filter_query["user_id"] = PyObjectId(user_id)
            if session_id:
                filter_query["session_id"] = session_id
            if deal_id:
                filter_query["deal_id"] = PyObjectId(deal_id)
            if level:
                filter_query["level"] = level

            # Query logs
            cursor = self.db.agent_logs.find(filter_query).sort(
                "timestamp", -1
            ).skip(skip).limit(limit)

            logs = []
            async for log in cursor:
                # Convert ObjectIds to strings
                log["_id"] = str(log["_id"])
                log["agent_id"] = str(log["agent_id"])
                log["user_id"] = str(log["user_id"])
                log["org_id"] = str(log["org_id"])
                if log.get("deal_id"):
                    log["deal_id"] = str(log["deal_id"])

                logs.append(log)

            return logs

        except Exception as e:
            logger.error(f"Failed to get agent logs: {e}")
            return []

    async def get_usage_stats(
        self,
        org_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get usage statistics."""

        try:
            # Build filter
            filter_query = {"org_id": PyObjectId(org_id)}

            if start_date or end_date:
                timestamp_filter = {}
                if start_date:
                    timestamp_filter["$gte"] = start_date
                if end_date:
                    timestamp_filter["$lte"] = end_date
                filter_query["timestamp"] = timestamp_filter

            # Aggregate statistics
            pipeline = [
                {"$match": filter_query},
                {
                    "$group": {
                        "_id": None,
                        "total_executions": {"$sum": 1},
                        "total_execution_time": {"$sum": "$execution_time"},
                        "total_tokens": {"$sum": "$token_usage.total_tokens"},
                        "unique_agents": {"$addToSet": "$agent_id"},
                        "unique_users": {"$addToSet": "$user_id"},
                        "error_count": {
                            "$sum": {
                                "$cond": [{"$ne": ["$error", None]}, 1, 0]
                            }
                        }
                    }
                }
            ]

            result = await self.db.agent_logs.aggregate(pipeline).to_list(1)

            if result:
                stats = result[0]
                stats["unique_agents_count"] = len(stats.get("unique_agents", []))
                stats["unique_users_count"] = len(stats.get("unique_users", []))
                del stats["unique_agents"]
                del stats["unique_users"]
                del stats["_id"]
                return stats
            else:
                return {
                    "total_executions": 0,
                    "total_execution_time": 0,
                    "total_tokens": 0,
                    "unique_agents_count": 0,
                    "unique_users_count": 0,
                    "error_count": 0
                }

        except Exception as e:
            logger.error(f"Failed to get usage stats: {e}")
            return {}


# Global audit logger instance
audit_logger = AuditLogger()
