"use client"

import { motion, AnimatePresence, easeOut, easeIn, easeInOut } from 'framer-motion';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Star, ArrowRight, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Deal } from '@/lib/types/deal';

const linear = (t: number) => t;

// Stage color utility moved inline
const getStageColor = (stage: string) => {
  const stageColors: Record<string, string> = {
    'Pre-Seed': 'bg-blue-100 text-blue-800',
    'Seed': 'bg-green-100 text-green-800',
    'Series A': 'bg-purple-100 text-purple-800',
    'Series B': 'bg-orange-100 text-orange-800',
    'Series C+': 'bg-red-100 text-red-800',
    'Growth': 'bg-indigo-100 text-indigo-800'
  };
  return stageColors[stage] || 'bg-gray-100 text-gray-800';
};

interface DealModalProps {
  deal: Deal | null;
  isOpen: boolean;
  onClose: () => void;
}

export function DealModal({ deal, isOpen, onClose }: DealModalProps) {
  if (!deal) return null;

  const modalVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.95,
      y: 20
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: easeOut
      }
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      y: 20,
      transition: {
        duration: 0.2,
        ease: easeIn
      }
    }
  };

  const contentVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        delay: 0.1,
        duration: 0.3,
        ease: easeOut
      }
    }
  };

  const sparkleVariants = {
    hidden: { opacity: 0, rotate: -10, scale: 0.8 },
    visible: { 
      opacity: 1, 
      rotate: 0, 
      scale: 1,
      transition: {
        delay: 0.2,
        duration: 0.4,
        ease: easeOut
      }
    }
  };

  // Extract sector as string
  const sectorDisplay = Array.isArray(deal.sector) 
    ? deal.sector.join(', ') 
    : deal.sector || 'Unknown';

  return (
    <AnimatePresence>
      {isOpen && (
        <Dialog open={isOpen} onOpenChange={onClose}>
          <DialogContent className="border-0 shadow-2xl sm:max-w-md">
            <motion.div
              variants={modalVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              className="relative"
            >
              {/* Background gradient */}
              <div className="absolute inset-0 rounded-lg bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-950/20 dark:via-indigo-950/20 dark:to-purple-950/20" />
              
              {/* Close button */}
              <Button
                onClick={onClose}
                variant="ghost"
                size="sm"
                className="absolute right-4 top-4 z-10 size-8 p-0 hover:bg-white/50"
              >
                <X className="size-4" />
              </Button>

              <div className="relative space-y-6 p-6">
                {/* Header with company info */}
                <motion.div variants={contentVariants} className="space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <h2 className="text-xl font-bold text-foreground">
                        {deal.company_name || 'Unnamed Company'}
                      </h2>
                      <p className="text-sm text-muted-foreground">
                        {sectorDisplay}
                      </p>
                    </div>
                    <Badge 
                      variant="secondary" 
                      className={cn(
                        "border text-xs font-medium",
                        getStageColor(deal.stage || '')
                      )}
                    >
                      {deal.stage || 'Unknown'}
                    </Badge>
                  </div>
                </motion.div>

                {/* Coming Soon Content */}
                <motion.div variants={contentVariants} className="space-y-4 text-center">
                  <motion.div variants={sparkleVariants} className="flex justify-center">
                    <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900">
                      <Star className="size-8 text-blue-600 dark:text-blue-400" />
                    </div>
                  </motion.div>

                  <div className="space-y-2">
                    <h3 className="text-lg font-semibold text-foreground">
                      Deal Details Coming Soon
                    </h3>
                    <p className="text-sm leading-relaxed text-muted-foreground">
                      We're building something amazing! Full deal profiles, analytics, 
                      and collaboration tools are coming soon.
                    </p>
                  </div>

                  {/* Feature preview list */}
                  <motion.div 
                    variants={contentVariants}
                    className="space-y-2 rounded-lg bg-white/50 p-4 dark:bg-gray-900/50"
                  >
                    <h4 className="mb-3 text-sm font-medium text-foreground">
                      What's Coming:
                    </h4>
                    <div className="space-y-2 text-xs text-muted-foreground">
                      <div className="flex items-center gap-2">
                        <ArrowRight className="size-3" />
                        <span>Detailed company profiles & financials</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <ArrowRight className="size-3" />
                        <span>AI-powered deal scoring & insights</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <ArrowRight className="size-3" />
                        <span>Team collaboration & notes</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <ArrowRight className="size-3" />
                        <span>Document management & due diligence</span>
                      </div>
                    </div>
                  </motion.div>
                </motion.div>

                {/* Action buttons */}
                <motion.div variants={contentVariants} className="flex gap-3">
                  <Button 
                    onClick={onClose}
                    variant="outline" 
                    className="flex-1"
                  >
                    Back to Deals
                  </Button>
                  <Button 
                    onClick={onClose}
                    className="flex-1"
                  >
                    Got it!
                  </Button>
                </motion.div>
              </div>
            </motion.div>
          </DialogContent>
        </Dialog>
      )}
    </AnimatePresence>
  );
}
