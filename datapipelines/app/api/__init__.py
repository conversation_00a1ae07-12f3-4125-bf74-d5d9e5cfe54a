"""
API endpoints for TractionX Data Pipeline Service.
"""

from fastapi import APIRouter
from app.api.barriers import router as barriers_router
from app.api.pipelines import router as pipelines_router
from app.api.status import router as status_router

# Create main API router
router = APIRouter()

# Include sub-routers
router.include_router(pipelines_router, prefix="/pipelines", tags=["pipelines"])
router.include_router(status_router, prefix="/status", tags=["status"])
router.include_router(barriers_router, prefix="/barriers", tags=["barriers"])

__all__ = ["router"]
