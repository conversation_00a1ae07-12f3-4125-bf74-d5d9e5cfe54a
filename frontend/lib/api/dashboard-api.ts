/**
 * Dashboard API Client
 * 
 * Handles all dashboard-related API calls including the unified summary endpoint.
 */

import apiClient from "../api-client";


// Types for dashboard API responses
export interface SectorDistribution {
  sector: string;
  count: number;
}

export interface DealStage {
  stage: string;
  count: number;
}

export interface AIActivity {
  active: boolean;
  last_sync?: string;
}

export interface OnboardingStatus {
  has_form: boolean;
  has_thesis: boolean;
}

export interface DashboardSummary {
  active_deals: number;
  active_deals_change_pct: number;
  forms: number;
  theses: number;
  ai_activity: AIActivity;
  sector_distribution: SectorDistribution[];
  deal_stages: DealStage[];
  onboarding: OnboardingStatus;
}

// Types for insights API responses
export interface DealFunnelBreakdown {
  status: string;
  count: number;
}

export interface DealActivityTimeline {
  date: string;
  count: number;
}

export interface UserStatusTransition {
  name: string;
  status_transitions: Record<string, number>;
}

export interface DealInsights {
  deal_funnel_breakdown: DealFunnelBreakdown[];
  sector_stage_matrix: Record<string, Record<string, number>>;
  deal_activity_timeline: DealActivityTimeline[];
  exclusion_filters_triggered: Record<string, number>;
  deal_velocity_by_user: Record<string, UserStatusTransition>;
  forms_count: number;
  deals_assigned_to_me: number;
}

export const DashboardAPI = {
  /**
   * Get unified dashboard summary with all real-time data
   */
  async getSummary(): Promise<DashboardSummary> {
    console.log('Fetching dashboard summary');
    
    const response = await apiClient.get('/dashboard/summary');
    console.log('Dashboard summary fetched:', response.data);
    
    return response.data;
  },

  /**
   * Get comprehensive deal insights for Dashboard charts
   */
  async getInsights(params?: {
    date_range_start?: number;
    date_range_end?: number;
    assigned_user_ids?: string;
  }): Promise<DealInsights> {
    console.log('Fetching deal insights', params);
    
    const response = await apiClient.get('/dashboard/insights', { params });
    console.log('Deal insights fetched:', response.data);
    
    return response.data;
  },
};
