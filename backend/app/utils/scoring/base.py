"""
Base Utility Functions

This module provides fundamental utility functions used across the application,
including embedding and similarity calculations.
"""
from typing import List, Any, Dict
import math

def evaluate_condition(condition: Dict[str, Any], value: Any) -> bool:
    """
    Evaluate a condition against a value.
    
    Args:
        condition: Dictionary with operator and value, e.g., {"eq": 10}
        value: Value to evaluate against
        
    Returns:
        True if condition is met, False otherwise
    """
    if not condition:
        return True
        
    operator = next(iter(condition))
    condition_value = condition[operator]
    
    if operator == "eq":
        return value == condition_value
    elif operator == "ne":
        return value != condition_value
    elif operator == "gt":
        return value > condition_value
    elif operator == "lt":
        return value < condition_value
    elif operator == "gte":
        return value >= condition_value
    elif operator == "lte":
        return value <= condition_value
    elif operator == "contains":
        if isinstance(value, str) and isinstance(condition_value, str):
            return condition_value in value
        elif isinstance(value, list):
            return condition_value in value
        return False
    elif operator == "not_contains":
        if isinstance(value, str) and isinstance(condition_value, str):
            return condition_value not in value
        elif isinstance(value, list):
            return condition_value not in value
        return True
    elif operator == "starts_with":
        return isinstance(value, str) and value.startswith(condition_value)
    elif operator == "ends_with":
        return isinstance(value, str) and value.endswith(condition_value)
    elif operator == "in":
        return value in condition_value if isinstance(condition_value, list) else False
    elif operator == "not_in":
        return value not in condition_value if isinstance(condition_value, list) else True
    elif operator == "between":
        if isinstance(condition_value, list) and len(condition_value) == 2:
            return condition_value[0] <= value <= condition_value[1]
        return False
    elif operator == "not_between":
        if isinstance(condition_value, list) and len(condition_value) == 2:
            return value < condition_value[0] or value > condition_value[1]
        return True
    else:
        return False
    
def get_embedding(text: str) -> List[float]:
    """
    Convert text to embedding vector.
    
    This is a stub implementation that will be replaced with a real embedding provider.
    Currently returns a simple vector based on character counts as a placeholder.
    
    Args:
        text: The text to convert to an embedding
        
    Returns:
        A list of floats representing the text embedding
    """
    # Stub implementation - will be replaced with actual embedding logic
    if not text:
        return [0.0] * 10
        
    # Simple character frequency as placeholder
    result = []
    for i in range(10):  # Arbitrary 10-dim vector
        # Use different character sets for each dimension to create variation
        char_set = chr(ord('a') + i) + chr(ord('A') + i) + str(i)
        count = sum(1 for c in text if c in char_set)
        # Normalize by text length
        result.append(count / (len(text) + 1))
    
    return result


def cosine_similarity(vec1: List[float], vec2: List[float]) -> float:
    """
    Calculate cosine similarity between two vectors.
    
    Args:
        vec1: First vector
        vec2: Second vector
        
    Returns:
        Cosine similarity as a float between 0 and 1
    """
    if not vec1 or not vec2:
        return 0.0
        
    # Ensure vectors are of same length
    min_len = min(len(vec1), len(vec2))
    vec1 = vec1[:min_len]
    vec2 = vec2[:min_len]
    
    # Calculate dot product
    dot_product = sum(a * b for a, b in zip(vec1, vec2))
    
    # Calculate magnitudes
    magnitude1 = math.sqrt(sum(a * a for a in vec1))
    magnitude2 = math.sqrt(sum(b * b for b in vec2))
    
    # Avoid division by zero
    if magnitude1 == 0 or magnitude2 == 0:
        return 0.0
        
    # Calculate cosine similarity
    return dot_product / (magnitude1 * magnitude2)


def jaccard_similarity(set1: set, set2: set) -> float:
    """
    Calculate Jaccard similarity between two sets.
    
    Args:
        set1: First set
        set2: Second set
        
    Returns:
        Jaccard similarity as a float between 0 and 1
    """
    if not set1 or not set2:
        return 0.0
        
    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))
    
    # Avoid division by zero
    if union == 0:
        return 0.0
        
    return intersection / union


def normalize_range(value: float, min_val: float, max_val: float) -> float:
    """
    Normalize a value to the range [0, 1] based on min and max values.
    
    Args:
        value: Value to normalize
        min_val: Minimum value in the range
        max_val: Maximum value in the range
        
    Returns:
        Normalized value between 0 and 1
    """
    if min_val == max_val:
        return 1.0 if value == min_val else 0.0
        
    # Clamp value to range
    clamped = max(min_val, min(value, max_val))
    
    # Normalize
    return (clamped - min_val) / (max_val - min_val)
