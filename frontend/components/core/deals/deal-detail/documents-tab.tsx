"use client"

import { useState, useRef, useEffect } from "react"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { 
  Upload, 
  FileText, 
  Download, 
  Eye, 
  Trash2,
  File,
  Image,
  FileSpreadsheet,
  Monitor,
  Calendar,
  User
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData, DealDocument } from "@/lib/types/deal-detail"
import { EmptyPlaceholder } from "@/components/empty-placeholder"
import { useToast } from "@/components/ui/use-toast"
import { DealDetailAPI } from "@/lib/api/deal-detail-api"
import { PitchDeckUpload } from "./pitch-deck-upload"

interface DocumentsTabProps {
  deal: DealDetailData
}

const getFileIcon = (type: DealDocument['document_type']) => {
  switch (type) {
    case 'pdf':
      return FileText
    case 'doc':
    case 'docx':
      return FileText
    case 'xls':
    case 'xlsx':
      return FileSpreadsheet
    case 'ppt':
    case 'pptx':
      return Monitor
    case 'image':
      return Image
    default:
      return File
  }
}

const getFileColor = (type: DealDocument['document_type']) => {
  switch (type) {
    case 'pdf':
      return 'bg-red-100 text-red-600 border-red-200'
    case 'doc':
    case 'docx':
      return 'bg-blue-100 text-blue-600 border-blue-200'
    case 'xls':
    case 'xlsx':
      return 'bg-green-100 text-green-600 border-green-200'
    case 'ppt':
    case 'pptx':
      return 'bg-orange-100 text-orange-600 border-orange-200'
    case 'image':
      return 'bg-purple-100 text-purple-600 border-purple-200'
    default:
      return 'bg-gray-100 text-gray-600 border-gray-200'
  }
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

export function DocumentsTab({ deal }: DocumentsTabProps) {
  const [documents, setDocuments] = useState(deal.documents || [])
  const [uploading, setUploading] = useState(false)
  const [loading, setLoading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()

  // Fetch fresh documents when component mounts
  const fetchDocuments = async () => {
    try {
      setLoading(true)
      const freshDocuments = await DealDetailAPI.getDocuments(deal.id)
      setDocuments(freshDocuments)
      console.log('Documents fetched:', freshDocuments)
    } catch (error) {
      console.error('Error fetching documents:', error)
      toast({
        title: "Failed to load documents",
        description: "There was an error loading the documents. Please refresh the page.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // Load documents on mount
  useEffect(() => {
    fetchDocuments()
  }, [])

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    const file = files[0]
    
    // Basic validation
    if (file.size > 50 * 1024 * 1024) { // 50MB limit
      toast({
        title: "File too large",
        description: "Please select a file smaller than 50MB.",
        variant: "destructive"
      })
      return
    }

    // Check file type
    const allowedTypes = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.csv', '.jpg', '.jpeg', '.png', '.gif', '.bmp']
    const fileExt = '.' + file.name.split('.').pop()?.toLowerCase()
    
    if (!allowedTypes.includes(fileExt)) {
      toast({
        title: "File type not supported",
        description: `Please select a supported file type: ${allowedTypes.join(', ')}`,
        variant: "destructive"
      })
      return
    }

    try {
      setUploading(true)
      const uploadedDoc = await DealDetailAPI.uploadDocument(deal.id, file)
      
      // Refresh documents list to get the latest data
      await fetchDocuments()
      
      toast({
        title: "File uploaded",
        description: `${file.name} has been uploaded successfully.`
      })
    } catch (error) {
      console.error('Upload error:', error)
      toast({
        title: "Upload failed",
        description: "There was an error uploading your file. Please try again.",
        variant: "destructive"
      })
    } finally {
      setUploading(false)
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handlePreview = (document: DealDocument) => {
    if (document.preview_url) {
      window.open(document.preview_url, '_blank', 'noopener,noreferrer')
    } else {
      // Fallback to download
      handleDownload(document)
    }
  }

  const handleDownload = async (document: DealDocument) => {
    try {
      const downloadUrl = await DealDetailAPI.getDocumentDownloadUrl(deal.id, document.id)
      window.open(downloadUrl, '_blank', 'noopener,noreferrer')
    } catch (error) {
      console.error('Download error:', error)
      toast({
        title: "Download failed",
        description: "There was an error downloading the file. Please try again.",
        variant: "destructive"
      })
    }
  }

  const handleDelete = async (document: DealDocument) => {
    if (!document.can_delete) {
      toast({
        title: "Cannot delete",
        description: "You don't have permission to delete this document.",
        variant: "destructive"
      })
      return
    }

    try {
      await DealDetailAPI.deleteDocument(deal.id, document.id)
      
      // Refresh documents list to get the latest data
      await fetchDocuments()
      
      toast({
        title: "Document deleted",
        description: `${document.filename} has been deleted.`
      })
    } catch (error) {
      console.error('Delete error:', error)
      toast({
        title: "Delete failed",
        description: "There was an error deleting the document. Please try again.",
        variant: "destructive"
      })
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <Card className="border-2 border-dashed">
          <CardContent className="p-8">
            <div className="space-y-4 text-center">
              <div className="flex justify-center">
                <div className="flex size-16 animate-pulse items-center justify-center rounded-full bg-muted">
                  <Upload className="size-8 text-muted-foreground" />
                </div>
              </div>
              <div>
                <h3 className="mb-2 font-semibold">Loading documents...</h3>
                <p className="text-sm text-muted-foreground">
                  Please wait while we fetch the latest documents
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (documents.length === 0) {
    return (
      <div className="space-y-6">
        {/* Pitch Deck Upload Section */}
        <PitchDeckUpload 
          deal={deal} 
          onPitchUploaded={fetchDocuments}
        />
        
        <Separator />
        
        {/* General Document Upload Area */}
        <Card className="border-2 border-dashed transition-colors hover:border-primary/50">
          <CardContent className="p-8">
            <div className="space-y-4 text-center">
              <div className="flex justify-center">
                <div className="flex size-16 items-center justify-center rounded-full bg-muted">
                  <Upload className="size-8 text-muted-foreground" />
                </div>
              </div>
              <div>
                <h3 className="mb-2 font-semibold">Upload documents</h3>
                <p className="mb-4 text-sm text-muted-foreground">
                  Drag and drop files here, or click to browse
                </p>
                <Button onClick={handleUploadClick} disabled={uploading}>
                  {uploading ? 'Uploading...' : 'Choose Files'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <input
          ref={fileInputRef}
          type="file"
          onChange={handleFileSelect}
          className="hidden"
          accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.png,.jpg,.jpeg,.gif"
        />

        <EmptyPlaceholder>
          <EmptyPlaceholder.Icon name="page" />
          <EmptyPlaceholder.Title>No documents yet</EmptyPlaceholder.Title>
          <EmptyPlaceholder.Description>
            Upload documents to share with your team and track deal progress.
          </EmptyPlaceholder.Description>
        </EmptyPlaceholder>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Pitch Deck Upload Section */}
      <PitchDeckUpload 
        deal={deal} 
        onPitchUploaded={fetchDocuments}
      />
      
      <Separator />
      
      {/* General Document Upload Area */}
      <Card className="border-2 border-dashed transition-colors hover:border-primary/50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold">Upload new document</h3>
              <p className="text-sm text-muted-foreground">
                PDF, Word, Excel, PowerPoint, and images supported
              </p>
            </div>
            <Button onClick={handleUploadClick} disabled={uploading} className="gap-2">
              <Upload className="size-4" />
              {uploading ? 'Uploading...' : 'Upload'}
            </Button>
          </div>
        </CardContent>
      </Card>

      <input
        ref={fileInputRef}
        type="file"
        onChange={handleFileSelect}
        className="hidden"
        accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.png,.jpg,.jpeg,.gif"
      />

      {/* Documents List */}
      <div className="space-y-3">
        {documents
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
          .map((document, index) => {
            const Icon = getFileIcon(document.document_type)
            
            return (
              <motion.div
                key={document.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <Card className="transition-shadow hover:shadow-md">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                      {/* File Icon */}
                      <div className={cn(
                        "flex size-12 items-center justify-center rounded-lg border",
                        getFileColor(document.document_type)
                      )}>
                        <Icon className="size-6" />
                      </div>

                      {/* File Info */}
                      <div className="min-w-0 flex-1">
                        <div className="flex items-center gap-2">
                          <h4 className="truncate text-sm font-medium">
                            {document.filename}
                          </h4>
                          {/* Source Badge */}
                          <Badge 
                            variant={document.source === 'startup_submission' ? 'secondary' : 'outline'}
                            className="text-xs"
                          >
                            {document.source === 'startup_submission' ? 'Startup Submission' : 'Investor Upload'}
                          </Badge>
                        </div>
                        <div className="mt-1 flex items-center gap-4 text-xs text-muted-foreground">
                          <span>{formatFileSize(document.file_size)}</span>
                          <div className="flex items-center gap-1">
                            <Calendar className="size-3" />
                            {formatDate(document.created_at)}
                          </div>
                          {document.uploaded_by_name && (
                            <div className="flex items-center gap-1">
                              <User className="size-3" />
                              {document.uploaded_by_name}
                            </div>
                          )}
                          {document.download_count > 0 && (
                            <div className="flex items-center gap-1">
                              <Download className="size-3" />
                              {document.download_count} downloads
                            </div>
                          )}
                        </div>
                        {/* Tags */}
                        {document.tags && document.tags.length > 0 && (
                          <div className="mt-2 flex items-center gap-1">
                            {document.tags.map((tag, tagIndex) => (
                              <Badge key={tagIndex} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>

                      {/* File Type Badge */}
                      <Badge variant="outline" className="text-xs uppercase">
                        {document.document_type}
                      </Badge>

                      {/* Actions */}
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePreview(document)}
                          className="gap-1"
                        >
                          <Eye className="size-4" />
                          Preview
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDownload(document)}
                          className="gap-1"
                        >
                          <Download className="size-4" />
                          Download
                        </Button>
                        
                        {document.can_delete && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(document)}
                            className="gap-1 text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="size-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
      </div>

      {/* Summary */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">
              {documents.length} document{documents.length !== 1 ? 's' : ''} total
            </span>
            <span className="text-muted-foreground">
              {formatFileSize(documents.reduce((acc, doc) => acc + doc.file_size, 0))} total size
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
