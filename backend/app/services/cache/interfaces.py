from typing import Optional, List, Any
from abc import ABC, abstractmethod

class CacheServiceInterface(ABC):
    """Interface for cache services."""
    
    @abstractmethod
    async def get(self, key: str, namespace: Optional[str] = None) -> Optional[Any]:
        """Get value by key."""
        pass

    @abstractmethod
    async def set(
        self,
        key: str,
        value: Any,
        namespace: Optional[str] = None,
        ttl: Optional[int] = None
    ) -> bool:
        """Set value for key with optional TTL."""
        pass

    @abstractmethod
    async def delete(self, key: str, namespace: Optional[str] = None) -> bool:
        """Delete key from cache."""
        pass

    @abstractmethod
    async def list_keys(
        self,
        pattern: str = "*",
        namespace: Optional[str] = None,
        count: int = 100
    ) -> List[str]:
        """List cache keys."""
        pass

    @abstractmethod
    async def exists(self, key: str, namespace: Optional[str] = None) -> bool:
        """Check if key exists."""
        pass

    @abstractmethod
    async def ttl(self, key: str, namespace: Optional[str] = None) -> Optional[int]:
        """Get TTL for key."""
        pass 