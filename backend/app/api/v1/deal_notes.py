"""
Deal Notes API Endpoints

This module provides REST API endpoints for managing deal notes with team mentions.
Supports CRUD operations, team mentions, pinning, and timeline integration.
"""

from typing import Any, Dict, Optional

from bson import ObjectId
from fastapi import Body, Depends, HTTPException, Query, status

from app.api.base import BaseAPIRouter
from app.core.logging import get_logger
from app.dependencies.auth import get_current_user
from app.dependencies.org import get_org_context
from app.models.deal_note import DealNote
from app.models.user import User
from app.schemas.deal_note import (
    DealNoteCreate,
    DealNoteCreateResponse,
    DealNoteDeleteResponse,
    DealNoteListResponse,
    DealNotePinRequest,
    DealNoteResponse,
    DealNoteUpdate,
)
from app.services.deal.interfaces import DealServiceInterface
from app.services.deal_note.interfaces import DealNoteServiceInterface
from app.services.factory import get_deal_note_service, get_deal_service
from app.utils.rbac.rbac import rbac_register

logger = get_logger(__name__)
router = BaseAPIRouter(prefix="/deals", tags=["deal_notes"])


def _note_to_response(
    note: DealNote, created_by_user: Optional[User] = None
) -> Dict[str, Any]:
    """Convert DealNote model to DealNoteResponse schema."""
    note_data = note.model_dump(by_alias=True)

    # Add created_by user information if available
    if created_by_user:
        note_data["created_by"] = {
            "_id": str(created_by_user.id),
            "name": created_by_user.name,
            "email": created_by_user.email,
        }
    else:
        note_data["created_by"] = {
            "_id": str(note.created_by),
            "name": "Unknown User",
            "email": "<EMAIL>",
        }

    return note_data


# Helper to extract and validate mentions
async def extract_and_validate_mentions(structured_content, org_id):
    from app.models.user import User

    user_ids = set()

    async def walk(nodes):
        for node in nodes:
            if isinstance(node, dict):
                if node.get("type") == "mention":
                    user_id = node.get("user_id")
                    if user_id:
                        # Validate user exists, is active, and in org
                        user = await User.find_one({
                            "_id": ObjectId(user_id),
                            "org_id": ObjectId(org_id),
                            "status": "active",
                        })
                        if not user:
                            raise HTTPException(
                                status_code=400,
                                detail=f"Mentioned user {user_id} is invalid or not in org",
                            )
                        user_ids.add(str(user_id))
                children = node.get("children")
                if children:
                    await walk(children)

    await walk(structured_content)
    return list(user_ids)


@router.get("/{deal_id}/notes", response_model=DealNoteListResponse)
@rbac_register(
    resource="deal_note", action="view", group="Deals", description="List deal notes"
)
async def list_deal_notes(
    deal_id: str,
    skip: int = Query(0, ge=0, description="Number of notes to skip"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of notes to return"
    ),
    include_deleted: bool = Query(False, description="Include soft-deleted notes"),
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
    deal_note_service: DealNoteServiceInterface = Depends(get_deal_note_service),
) -> DealNoteListResponse:
    """
    List notes for a deal.

    Returns paginated list of notes with pinned notes first,
    then sorted by creation date descending.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Verify deal exists and user has access
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Get notes
        notes, total = await deal_note_service.list_notes(
            deal_id=deal_id,
            org_id=org_id,
            skip=skip,
            limit=limit,
            include_deleted=include_deleted,
        )
        logger.info(f"Listed {len(notes)} notes for deal {deal_id}")
        # Convert to response format with user information
        note_responses = []
        for note in notes:
            # Get created_by user information
            created_by_user = await User.find_one({"_id": note.created_by})
            note_response = _note_to_response(note, created_by_user)
            note_responses.append(DealNoteResponse(**note_response))

        logger.info(f"Listed {len(notes)} notes for deal {deal_id}")
        return DealNoteListResponse(
            notes=note_responses,
            total=total,
            skip=skip,
            limit=limit,
            has_more=skip + len(notes) < total,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing notes for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post("/{deal_id}/notes", response_model=DealNoteCreateResponse)
@rbac_register(
    resource="deal_note", action="create", group="Deals", description="Create deal note"
)
async def create_deal_note(
    deal_id: str,
    request: DealNoteCreate = Body(...),
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
    deal_note_service: DealNoteServiceInterface = Depends(get_deal_note_service),
) -> DealNoteCreateResponse:
    """
    Create a new note for a deal.

    Supports team mentions, rich text content, and pinning.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid deal ID format"
            )

        # Verify deal exists and user has access
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Validate mentions and extract tagged_user_ids
        tagged_user_ids = (
            request.tagged_user_ids
            or await extract_and_validate_mentions(request.structured_content, org_id)
        )
        # Create note
        note = await deal_note_service.create_note(
            deal_id=deal_id,
            org_id=org_id,
            created_by=current_user.id,
            structured_content=request.structured_content,
            tagged_user_ids=tagged_user_ids,  # type: ignore
            pinned=request.pinned or False,
        )

        if not note:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to create note"
            )

        # Add timeline event to deal
        await deal_service.add_timeline_event(
            deal_id=deal_id,
            event="Note added",
            notes=f"Note created by {current_user.email}",
            user_id=current_user.id,
        )

        logger.info(f"Created note {note.id} for deal {deal_id}")
        return DealNoteCreateResponse(
            success=True,
            note_id=str(note.id),
            message="Note created successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating note for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/{deal_id}/notes/{note_id}", response_model=DealNoteResponse)
@rbac_register(
    resource="deal_note", action="view", group="Deals", description="Get deal note"
)
async def get_deal_note(
    deal_id: str,
    note_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
    deal_note_service: DealNoteServiceInterface = Depends(get_deal_note_service),
) -> DealNoteResponse:
    """
    Get a specific note for a deal.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId formats
        try:
            ObjectId(deal_id)
            ObjectId(note_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid ID format"
            )

        # Verify deal exists and user has access
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Get note
        note = await deal_note_service.get_note(note_id, org_id)
        if not note:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Note not found"
            )

        # Get created_by user information
        created_by_user = await User.find_one({"_id": note.created_by})
        note_response = _note_to_response(note, created_by_user)

        return DealNoteResponse(**note_response)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting note {note_id} for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.patch("/{deal_id}/notes/{note_id}", response_model=DealNoteResponse)
@rbac_register(
    resource="deal_note", action="edit", group="Deals", description="Update deal note"
)
async def update_deal_note(
    deal_id: str,
    note_id: str,
    request: DealNoteUpdate = Body(...),
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
    deal_note_service: DealNoteServiceInterface = Depends(get_deal_note_service),
) -> DealNoteResponse:
    """
    Update a note for a deal.

    Only the creator or org admin can edit notes.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId formats
        try:
            ObjectId(deal_id)
            ObjectId(note_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid ID format"
            )

        # Verify deal exists and user has access
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Only creator or admin can edit
        note = await deal_note_service.get_note(note_id, org_id)
        if not note:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Note not found"
            )
        # Use a placeholder for org admin check; adjust as needed
        is_org_admin = getattr(current_user, "is_org_admin", False)
        if str(note.created_by) != str(current_user.id) and not is_org_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only creator or admin can edit",
            )

        # Validate mentions and extract tagged_user_ids
        structured_content = (
            request.structured_content
            if request.structured_content is not None
            else note.structured_content
        )
        tagged_user_ids = (
            request.tagged_user_ids
            or await extract_and_validate_mentions(structured_content, org_id)
        )
        updated_note = await deal_note_service.update_note(
            note_id=note_id,
            org_id=org_id,
            updated_by=current_user.id,
            structured_content=structured_content,
            tagged_user_ids=tagged_user_ids,  # type: ignore
            pinned=request.pinned,
        )

        if not updated_note:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Note not found or access denied",
            )

        # Get created_by user information
        created_by_user = await User.find_one({"_id": updated_note.created_by})
        note_response = _note_to_response(updated_note, created_by_user)

        logger.info(f"Updated note {note_id} for deal {deal_id}")
        return DealNoteResponse(**note_response)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating note {note_id} for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/{deal_id}/notes/{note_id}", response_model=DealNoteDeleteResponse)
@rbac_register(
    resource="deal_note", action="delete", group="Deals", description="Delete deal note"
)
async def delete_deal_note(
    deal_id: str,
    note_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
    deal_note_service: DealNoteServiceInterface = Depends(get_deal_note_service),
) -> DealNoteDeleteResponse:
    """
    Soft delete a note for a deal.

    Only the creator or org admin can delete notes.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId formats
        try:
            ObjectId(deal_id)
            ObjectId(note_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid ID format"
            )

        # Verify deal exists and user has access
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Delete note
        success = await deal_note_service.delete_note(
            note_id=note_id,
            org_id=org_id,
            deleted_by=current_user.id,
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Note not found or access denied",
            )

        logger.info(f"Deleted note {note_id} for deal {deal_id}")
        return DealNoteDeleteResponse(
            success=True,
            message="Note deleted successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting note {note_id} for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.patch("/{deal_id}/notes/{note_id}/pin", response_model=DealNoteResponse)
@rbac_register(
    resource="deal_note", action="edit", group="Deals", description="Toggle note pin"
)
async def toggle_note_pin(
    deal_id: str,
    note_id: str,
    request: DealNotePinRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service),
    deal_note_service: DealNoteServiceInterface = Depends(get_deal_note_service),
) -> DealNoteResponse:
    """
    Toggle the pinned status of a note.

    Only the creator or org admin can pin/unpin notes.
    """
    try:
        org_id, _ = org_context

        # Validate ObjectId formats
        try:
            ObjectId(deal_id)
            ObjectId(note_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid ID format"
            )

        # Verify deal exists and user has access
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Update note pinned status
        updated_note = await deal_note_service.update_note(
            note_id=note_id,
            org_id=org_id,
            updated_by=current_user.id,
            pinned=request.pinned,
        )

        if not updated_note:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Note not found or access denied",
            )

        # Get created_by user information
        created_by_user = await User.find_one({"_id": updated_note.created_by})
        note_response = _note_to_response(updated_note, created_by_user)

        logger.info(f"Updated pin status for note {note_id} to {request.pinned}")
        return DealNoteResponse(**note_response)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating pin status for note {note_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/notes/tagged", response_model=DealNoteListResponse)
@rbac_register(
    resource="deal_note",
    action="view",
    group="Deals",
    description="Get notes where user is tagged",
)
async def get_notes_by_user(
    skip: int = Query(0, ge=0, description="Number of notes to skip"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of notes to return"
    ),
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_note_service: DealNoteServiceInterface = Depends(get_deal_note_service),
) -> DealNoteListResponse:
    """
    Get notes where the current user is tagged.

    Returns paginated list of notes where the user is mentioned.
    """
    try:
        org_id, _ = org_context

        # Get notes where user is tagged
        notes, total = await deal_note_service.get_notes_by_user(
            user_id=current_user.id,
            org_id=org_id,
            skip=skip,
            limit=limit,
        )

        # Convert to response format with user information
        note_responses = []
        for note in notes:
            # Get created_by user information
            created_by_user = await User.find_one({"_id": note.created_by})
            note_response = _note_to_response(note, created_by_user)
            note_responses.append(DealNoteResponse(**note_response))

        logger.info(f"Listed {len(notes)} notes where user {current_user.id} is tagged")
        return DealNoteListResponse(
            notes=note_responses,
            total=total,
            skip=skip,
            limit=limit,
            has_more=skip + len(notes) < total,
        )

    except Exception as e:
        logger.error(f"Error getting notes for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
