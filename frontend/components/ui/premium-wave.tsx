"use client"

/**
 * PremiumWave - Ultra-premium, highly visible wave animation
 *
 * Features:
 * - Bold, visible wave patterns
 * - Rich amber/orange gradients
 * - Floating animated elements
 * - Guaranteed visibility
 * - Zero hydration issues
 */
export default function PremiumWave() {
  return (
    <div className="relative size-full overflow-hidden bg-gradient-to-br from-amber-100/80 via-orange-100/60 to-amber-50/40">
      {/* Wave Layer 1 - Primary - Much more visible */}
      <div
        className="absolute inset-0 opacity-90"
        style={{
          background: `linear-gradient(45deg,
            hsl(20, 90%, 45%) 0%,
            hsl(25, 80%, 55%) 50%,
            hsl(15, 95%, 40%) 100%)`,
          clipPath: `polygon(
            0% 60%,
            25% 55%,
            50% 65%,
            75% 50%,
            100% 58%,
            100% 100%,
            0% 100%
          )`,
          animation: 'wave-drift 8s ease-in-out infinite alternate'
        }}
      />

      {/* Wave Layer 2 - Secondary - More visible */}
      <div
        className="absolute inset-0 opacity-75"
        style={{
          background: `linear-gradient(135deg,
            hsl(18, 85%, 50%) 0%,
            hsl(22, 75%, 60%) 50%,
            hsl(16, 90%, 45%) 100%)`,
          clipPath: `polygon(
            0% 70%,
            30% 62%,
            60% 75%,
            85% 65%,
            100% 72%,
            100% 100%,
            0% 100%
          )`,
          animation: 'wave-drift 12s ease-in-out infinite alternate-reverse'
        }}
      />

      {/* Wave Layer 3 - Accent - More visible */}
      <div
        className="absolute inset-0 opacity-60"
        style={{
          background: `linear-gradient(90deg,
            hsl(24, 80%, 55%) 0%,
            hsl(19, 90%, 48%) 50%,
            hsl(21, 85%, 52%) 100%)`,
          clipPath: `polygon(
            0% 80%,
            20% 75%,
            45% 85%,
            70% 78%,
            100% 82%,
            100% 100%,
            0% 100%
          )`,
          animation: 'wave-drift 10s ease-in-out infinite alternate'
        }}
      />

      {/* Floating Geometric Elements - Much more visible */}
      <div className="animate-float-slow absolute left-1/4 top-1/4 size-6 rounded-full bg-gradient-to-br from-amber-400 to-orange-500 opacity-80 shadow-2xl" />
      <div className="animate-float-medium absolute right-1/3 top-1/3 size-4 rounded-full bg-gradient-to-br from-amber-300 to-orange-400 opacity-70 shadow-xl" />
      <div className="animate-float-fast absolute bottom-1/3 left-1/2 size-5 rounded-full bg-gradient-to-br from-amber-500 to-orange-600 opacity-75 shadow-2xl" />

      {/* Highly visible animated lines */}
      <div className="absolute left-0 top-0 h-2 w-full animate-pulse bg-gradient-to-r from-transparent via-amber-400/80 to-transparent" />
      <div className="absolute bottom-0 left-0 h-1.5 w-full animate-pulse bg-gradient-to-r from-transparent via-orange-400/70 to-transparent" style={{ animationDelay: '1s' }} />

      {/* More visible grid overlay */}
      <div
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `radial-gradient(circle at 2px 2px, hsl(20, 70%, 50%) 1px, transparent 0)`,
          backgroundSize: '40px 40px',
          animation: 'grid-drift 15s linear infinite'
        }}
      />

      {/* Additional glow effects */}
      <div className="bg-gradient-radial absolute left-1/2 top-1/2 size-40 -translate-x-1/2 -translate-y-1/2 animate-ping rounded-full from-amber-300/30 via-orange-200/20 to-transparent opacity-60" style={{ animationDuration: '4s' }} />
      
      <style jsx>{`
        @keyframes wave-drift {
          0% { transform: translateX(-2%) scaleY(0.95); }
          50% { transform: translateX(1%) scaleY(1.05); }
          100% { transform: translateX(-1%) scaleY(0.98); }
        }

        @keyframes grid-drift {
          0% { transform: translateX(0px) translateY(0px); }
          100% { transform: translateX(40px) translateY(40px); }
        }
        
        @keyframes float-slow {
          0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
          50% { transform: translateY(-8px) rotate(180deg); opacity: 0.6; }
        }
        
        @keyframes float-medium {
          0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.4; }
          50% { transform: translateY(-12px) rotate(-180deg); opacity: 0.7; }
        }
        
        @keyframes float-fast {
          0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.2; }
          50% { transform: translateY(-6px) rotate(360deg); opacity: 0.5; }
        }
        
        .animate-float-slow {
          animation: float-slow 6s ease-in-out infinite;
        }
        
        .animate-float-medium {
          animation: float-medium 4s ease-in-out infinite;
        }
        
        .animate-float-fast {
          animation: float-fast 8s ease-in-out infinite;
        }
      `}</style>
    </div>
  )
}