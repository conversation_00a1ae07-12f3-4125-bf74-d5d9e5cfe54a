"""
Deal Document models for TractionX deal document management.

This module defines models for tracking documents uploaded by investors
or transferred from startup submissions, with proper audit trails and access control.
"""

from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, Optional

from pydantic import Field

from app.models.base import TractionXModel
from app.utils.common import ObjectIdField, PyObjectId


class DocumentType(str, Enum):
    """Document type enumeration."""

    PDF = "pdf"
    DOC = "doc"
    DOCX = "docx"
    XLS = "xls"
    XLSX = "xlsx"
    PPT = "ppt"
    PPTX = "pptx"
    CSV = "csv"
    IMAGE = "image"
    OTHER = "other"


class DocumentSource(str, Enum):
    """Document source enumeration."""

    INVESTOR_UPLOAD = "investor_upload"
    STARTUP_SUBMISSION = "startup_submission"
    PITCH_DECK = "pitch_deck"


class DocumentStatus(str, Enum):
    """Document status enumeration."""

    UPLOADING = "uploading"
    UPLOADED = "uploaded"
    READY = "ready"
    ERROR = "error"
    DELETED = "deleted"


class DealDocument(TractionXModel):
    """
    Represents a document associated with a deal.

    This model tracks documents from two sources:
    1. Investor uploads - Documents uploaded by org members
    2. Startup submissions - Documents transferred from form submissions

    Key features:
    - S3 storage with presigned URL access
    - Source tracking (investor vs startup)
    - User access control and audit trail
    - File validation and metadata tracking
    - Preview URL generation for supported formats
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")

    # Core relationships
    deal_id: ObjectIdField = Field(
        ..., description="ID of the deal this document belongs to"
    )
    org_id: ObjectIdField = Field(..., description="Organization ID for access control")

    # Source tracking
    source: DocumentSource = Field(
        ...,
        description="Source of the document (investor_upload or startup_submission)",
    )
    submission_id: Optional[ObjectIdField] = Field(
        None, description="ID of the original submission (if from startup)"
    )
    submission_file_id: Optional[ObjectIdField] = Field(
        None, description="ID of the original submission file"
    )

    # User tracking
    uploaded_by_user_id: ObjectIdField = Field(
        ..., description="ID of the user who uploaded the file"
    )
    uploaded_by_email: str = Field(
        ..., description="Email of the user who uploaded the file"
    )
    uploaded_by_name: Optional[str] = Field(
        None, description="Name of the user who uploaded the file"
    )
    uploaded_by_role: Optional[str] = Field(
        None, description="Role of the user who uploaded the file"
    )

    # File metadata
    filename: str = Field(..., description="Original filename as uploaded")
    s3_key: str = Field(..., description="S3 object key for the file")
    s3_bucket: str = Field(..., description="S3 bucket name")
    mime_type: str = Field(..., description="MIME type of the file")
    file_size: int = Field(..., description="File size in bytes")
    document_type: DocumentType = Field(..., description="Type of document")

    # File status and processing
    status: DocumentStatus = Field(
        default=DocumentStatus.UPLOADING, description="Current status of the document"
    )
    upload_completed_at: Optional[int] = Field(
        default=None, description="Timestamp when upload was completed"
    )

    # URLs
    download_url: Optional[str] = Field(
        default=None, description="Pre-signed download URL (temporary)"
    )
    preview_url: Optional[str] = Field(
        default=None, description="Preview URL for supported file types"
    )

    # Validation and security
    checksum: Optional[str] = Field(
        default=None, description="File checksum for integrity verification"
    )
    virus_scan_status: Optional[str] = Field(
        default=None, description="Virus scan status (clean, infected, pending)"
    )

    # Tags and categorization
    tags: list[str] = Field(
        default_factory=list, description="Tags for categorizing the document"
    )

    # Access tracking
    download_count: int = Field(
        default=0, description="Number of times document has been downloaded"
    )
    last_accessed_at: Optional[int] = Field(
        default=None, description="Last time document was accessed"
    )
    last_accessed_by: Optional[str] = Field(
        default=None, description="Email of last user to access document"
    )

    # Metadata and configuration
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional document metadata"
    )
    is_active: bool = Field(
        default=True, description="Whether the document is active (not soft-deleted)"
    )

    # Timestamps
    created_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
    updated_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )

    def mark_uploaded(self) -> None:
        """Mark the document as successfully uploaded."""
        self.status = DocumentStatus.UPLOADED
        self.upload_completed_at = int(datetime.now(timezone.utc).timestamp())
        self.updated_at = int(datetime.now(timezone.utc).timestamp())

    def mark_ready(self) -> None:
        """Mark the document as ready for access."""
        self.status = DocumentStatus.READY
        self.updated_at = int(datetime.now(timezone.utc).timestamp())

    def mark_error(self, error_message: Optional[str] = None) -> None:
        """Mark the document as having an error."""
        self.status = DocumentStatus.ERROR
        if error_message:
            self.metadata["error_message"] = error_message
        self.updated_at = int(datetime.now(timezone.utc).timestamp())

    def soft_delete(self) -> None:
        """Soft delete the document."""
        self.is_active = False
        self.status = DocumentStatus.DELETED
        self.updated_at = int(datetime.now(timezone.utc).timestamp())

    def track_access(self, accessed_by_email: str) -> None:
        """Track document access."""
        self.download_count += 1
        self.last_accessed_at = int(datetime.now(timezone.utc).timestamp())
        self.last_accessed_by = accessed_by_email
        self.updated_at = int(datetime.now(timezone.utc).timestamp())

    def can_be_deleted_by(self, user_email: str, user_role: str) -> bool:
        """
        Check if a user can delete this document.

        Args:
            user_email: Email of the user requesting deletion
            user_role: Role of the user (admin, investor, analyst)

        Returns:
            bool: True if user can delete the document
        """
        if not self.is_active or self.status == DocumentStatus.DELETED:
            return False

        # Admins can delete any document
        if user_role == "admin":
            return True

        # Users can only delete their own uploads (not startup submissions)
        if self.source == DocumentSource.INVESTOR_UPLOAD:
            return self.uploaded_by_email == user_email

        # Startup submission documents can only be deleted by admins
        return False

    def add_tag(self, tag: str) -> None:
        """Add a tag to the document if it doesn't exist."""
        if tag not in self.tags:
            self.tags.append(tag)
            self.updated_at = int(datetime.now(timezone.utc).timestamp())

    def remove_tag(self, tag: str) -> None:
        """Remove a tag from the document."""
        if tag in self.tags:
            self.tags.remove(tag)
            self.updated_at = int(datetime.now(timezone.utc).timestamp())

    @property
    def file_extension(self) -> str:
        """Get file extension from filename."""
        if "." in self.filename:
            return self.filename.split(".")[-1].lower()
        return ""

    @property
    def display_size(self) -> str:
        """Get human-readable file size."""
        if self.file_size < 1024:
            return f"{self.file_size} B"
        elif self.file_size < 1024 * 1024:
            return f"{self.file_size / 1024:.1f} KB"
        elif self.file_size < 1024 * 1024 * 1024:
            return f"{self.file_size / (1024 * 1024):.1f} MB"
        else:
            return f"{self.file_size / (1024 * 1024 * 1024):.1f} GB"

    @property
    def source_display(self) -> str:
        """Get human-readable source name."""
        if self.source == DocumentSource.INVESTOR_UPLOAD:
            return "Investor Upload"
        else:
            return "Startup Submission"

    @classmethod
    def determine_document_type(cls, filename: str, mime_type: str) -> DocumentType:
        """Determine document type from filename and mime type."""
        extension = filename.split(".")[-1].lower() if "." in filename else ""

        # Map extensions to document types
        type_mapping = {
            "pdf": DocumentType.PDF,
            "doc": DocumentType.DOC,
            "docx": DocumentType.DOCX,
            "xls": DocumentType.XLS,
            "xlsx": DocumentType.XLSX,
            "ppt": DocumentType.PPT,
            "pptx": DocumentType.PPTX,
            "csv": DocumentType.CSV,
            "jpg": DocumentType.IMAGE,
            "jpeg": DocumentType.IMAGE,
            "png": DocumentType.IMAGE,
            "gif": DocumentType.IMAGE,
            "bmp": DocumentType.IMAGE,
        }

        return type_mapping.get(extension, DocumentType.OTHER)
