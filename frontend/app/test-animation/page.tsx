"use client"

import { motion } from 'framer-motion'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Zap, 
  Wand2, 
  <PERSON><PERSON>ding<PERSON><PERSON>, 
  <PERSON>, 
  Target,
  ArrowRight
} from 'lucide-react'
import { cn } from '@/lib/utils'

export default function TestAnimationPage() {
  return (
    <div className="min-h-screen bg-tx-surface p-4 md:p-6 lg:p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center space-y-4"
        >
          <h1 className="text-3xl font-bold text-text">
            Animation Test Page
          </h1>
          <p className="text-muted">
            Testing the new institutional-grade design system
          </p>
        </motion.div>

        {/* Test Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Shimmer Effect */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card className="border-0 bg-surface shadow-institutional hover:shadow-institutional-lg transition-all duration-300 rounded-xl overflow-hidden">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-primary/10 text-primary">
                      <Zap className="w-5 h-5" />
                    </div>
                    <h3 className="font-semibold text-text">Shimmer Effect</h3>
                  </div>
                  <div className="relative h-20 rounded-lg bg-gradient-to-br from-primary/10 to-secondary/10 overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/5 to-transparent animate-shimmer" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Live Pulse */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="border-0 bg-surface shadow-institutional hover:shadow-institutional-lg transition-all duration-300 rounded-xl overflow-hidden">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-secondary/10 text-secondary">
                      <Wand2 className="w-5 h-5" />
                    </div>
                    <h3 className="font-semibold text-text">Live Pulse</h3>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-primary animate-live-pulse" />
                    <span className="text-sm text-muted">Live Data</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Glow Effects */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card className="border-0 bg-surface shadow-institutional hover:shadow-glow-mint transition-all duration-300 rounded-xl overflow-hidden">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-primary/10 text-primary">
                      <TrendingUp className="w-5 h-5" />
                    </div>
                    <h3 className="font-semibold text-text">Glow Effects</h3>
                  </div>
                  <div className="h-20 rounded-lg bg-gradient-to-br from-primary/5 to-secondary/5 animate-glow-fade" />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Feature Preview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-4"
        >
          <Card className="border-0 bg-surface shadow-institutional rounded-xl">
            <CardContent className="p-4 text-center">
              <TrendingUp className="w-8 h-8 text-primary mx-auto mb-2" />
              <h3 className="font-semibold text-text text-sm">Advanced Analytics</h3>
              <p className="text-xs text-muted mt-1">Real-time insights</p>
            </CardContent>
          </Card>
          
          <Card className="border-0 bg-surface shadow-institutional rounded-xl">
            <CardContent className="p-4 text-center">
              <Users className="w-8 h-8 text-secondary mx-auto mb-2" />
              <h3 className="font-semibold text-text text-sm">Team Collaboration</h3>
              <p className="text-xs text-muted mt-1">Enhanced workflows</p>
            </CardContent>
          </Card>
          
          <Card className="border-0 bg-surface shadow-institutional rounded-xl">
            <CardContent className="p-4 text-center">
              <Target className="w-8 h-8 text-primary mx-auto mb-2" />
              <h3 className="font-semibold text-text text-sm">AI-Powered</h3>
              <p className="text-xs text-muted mt-1">Smart automation</p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Action Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="text-center"
        >
          <Button size="lg" className="bg-primary hover:bg-primary/90">
            <Wand2 className="w-4 h-4 mr-2" />
            View Dashboard
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </motion.div>
      </div>
    </div>
  )
}