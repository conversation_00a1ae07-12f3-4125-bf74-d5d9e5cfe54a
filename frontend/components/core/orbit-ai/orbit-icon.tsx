"use client"

import { motion, useAnimationControls } from "framer-motion"
import { useEffect, useState } from "react"
import { cn } from "@/lib/utils"

interface OrbitIconProps {
  className?: string
  animated?: boolean
  glowing?: boolean
}

export function OrbitIcon({ className, animated = false, glowing = false }: OrbitIconProps) {
  return (
    <div className={cn("relative", className)}>
      <motion.svg
        width="24"
        height="24"
        viewBox="0 0 128 128"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={cn(
          "text-foreground",
          glowing && "drop-shadow-[0_0_8px_currentColor]"
        )}
        animate={animated ? {
          rotate: [0, 360],
          scale: [1, 1.05, 1],
        } : {}}
        transition={{
          rotate: {
            duration: 8,
            repeat: Infinity,
            ease: "linear"
          },
          scale: {
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }
        }}
      >
        {/* Outer ring */}
        <motion.circle
          cx="64"
          cy="64"
          r="45"
          stroke="currentColor"
          strokeWidth="0.8"
          fill="none"
          opacity="0.4"
          strokeDasharray="2 4"
          animate={animated ? {
            rotate: [0, -360],
            strokeDashoffset: [0, -8]
          } : {}}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{ transformOrigin: "64px 64px" }}
        />
        
        {/* Middle ring */}
        <motion.circle
          cx="64"
          cy="64"
          r="32"
          stroke="currentColor"
          strokeWidth="1"
          fill="none"
          opacity="0.6"
          strokeDasharray="3 3"
          animate={animated ? {
            rotate: [0, 360],
            strokeDashoffset: [0, 6]
          } : {}}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{ transformOrigin: "64px 64px" }}
        />
        
        {/* Inner ring */}
        <circle
          cx="64"
          cy="64"
          r="20"
          stroke="currentColor"
          strokeWidth="1.2"
          fill="none"
          opacity="0.8"
        />

        {/* Central core */}
        <motion.circle
          cx="64"
          cy="64"
          r="6"
          fill="currentColor"
          opacity="0.9"
          animate={animated ? {
            scale: [1, 1.2, 1],
            opacity: [0.9, 1, 0.9]
          } : {}}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        {/* Orbital dots */}
        <motion.circle
          cx="96"
          cy="64"
          r="2"
          fill="currentColor"
          opacity="0.7"
          animate={animated ? {
            rotate: [0, 360]
          } : {}}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{ transformOrigin: "64px 64px" }}
        />
        
        <motion.circle
          cx="64"
          cy="19"
          r="1.5"
          fill="currentColor"
          opacity="0.5"
          animate={animated ? {
            rotate: [0, -360]
          } : {}}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{ transformOrigin: "64px 64px" }}
        />
        
        <motion.circle
          cx="32"
          cy="64"
          r="1"
          fill="currentColor"
          opacity="0.4"
          animate={animated ? {
            rotate: [0, 360]
          } : {}}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{ transformOrigin: "64px 64px" }}
        />

        {/* Energy lines */}
        <g opacity="0.3">
          <line x1="64" y1="10" x2="64" y2="25" stroke="currentColor" strokeWidth="0.5" />
          <line x1="64" y1="103" x2="64" y2="118" stroke="currentColor" strokeWidth="0.5" />
          <line x1="10" y1="64" x2="25" y2="64" stroke="currentColor" strokeWidth="0.5" />
          <line x1="103" y1="64" x2="118" y2="64" stroke="currentColor" strokeWidth="0.5" />
        </g>

        {/* Corner accents */}
        <g opacity="0.2">
          <circle cx="20" cy="20" r="1" fill="currentColor" />
          <circle cx="108" cy="20" r="1" fill="currentColor" />
          <circle cx="20" cy="108" r="1" fill="currentColor" />
          <circle cx="108" cy="108" r="1" fill="currentColor" />
        </g>
      </motion.svg>
      
      {/* Optional glow effect overlay */}
      {glowing && animated && (
        <motion.div
          className="absolute inset-0 rounded-full bg-current opacity-10 blur-sm"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.2, 0.1]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      )}
    </div>
  )
}


// export function OrbitPulseGrid({
//   className,
//   size = 100,
//   dotCount = 8,
//   duration = 4000
// }: {
//   className?: string
//   size?: number
//   dotCount?: number
//   duration?: number
// }) {
//   const controls = useAnimationControls()
//   const [isClient, setIsClient] = useState(false)
//   const radius = size / 2.2
//   const rings = Math.min(5, Math.floor(dotCount / 2))

//   // Ensure client-side only rendering to prevent hydration mismatch
//   useEffect(() => {
//     setIsClient(true)
//   }, [])

//   const dots: {
//     key: string
//     x: number
//     y: number
//     ring: number
//   }[] = []

//   // Generate dots only on client side
//   if (isClient) {
//     for (let ring = 0; ring < rings; ring++) {
//       const ringRadius = (radius / rings) * (ring + 1)
//       const dotsInRing = Math.max(6, 8 + ring * 4)

//       for (let i = 0; i < dotsInRing; i++) {
//         const angle = (i / dotsInRing) * 2 * Math.PI
//         const x = Math.cos(angle) * ringRadius
//         const y = Math.sin(angle) * ringRadius
//         dots.push({
//           key: `ring-${ring}-dot-${i}`,
//           x,
//           y,
//           ring
//         })
//       }
//     }

//     // Add center dot
//     dots.push({ key: "center", x: 0, y: 0, ring: -1 })
//   }

//   useEffect(() => {
//     if (!isClient) return
    
//     controls.start((dot: { ring: number }) => {
//       const delay = (dot.ring + 1) * 0.15 // ring 0 = fast, outer = slower
//       return {
//         scale: [1, 1.6, 1],
//         opacity: [0.5, 1, 0.5],
//         transition: {
//           duration: duration / 1000,
//           ease: "easeInOut",
//           repeat: Infinity,
//           delay,
//         },
//       }
//     })
//   }, [controls, duration, isClient])

//   // Show loading state during SSR
//   if (!isClient) {
//     return (
//       <div
//         className={cn("relative flex items-center justify-center", className)}
//         style={{ width: size, height: size }}
//       >
//         <div className="w-2 h-2 bg-black rounded-full animate-pulse" />
//       </div>
//     )
//   }

//   return (
//     <div
//       className={cn("relative flex items-center justify-center", className)}
//       style={{ width: size, height: size }}
//     >
//       {dots.map((dot) => (
//         <motion.div
//           key={dot.key}
//           custom={dot}
//           animate={controls}
//           className="absolute bg-black rounded-full"
//           style={{
//             width: dot.ring === -1 ? 8 : 5,
//             height: dot.ring === -1 ? 8 : 5,
//             left: `calc(50% + ${dot.x}px - ${dot.ring === -1 ? 4 : 2.5}px)`,
//             top: `calc(50% + ${dot.y}px - ${dot.ring === -1 ? 4 : 2.5}px)`,
//           }}
//         />
//       ))}
//     </div>
//   )
// }


// For chatbot usage - keep the OrbitPulseGrid with cool aesthetic colors
export function OrbitPulseGrid({
  className,
  size = 50,
  dotCount = 20,
  duration = 1200
}: {
  className?: string
  size?: number
  dotCount?: number
  duration?: number
}) {
  const controls = useAnimationControls()
  const [isClient, setIsClient] = useState(false)
  const radius = size / 2.2 // Adjust radius to fit within size
  const dots: { key: string; x: number; y: number; delay: number; ring: number }[] = []

  // Ensure client-side only rendering to prevent hydration mismatch
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Create concentric rings for perfect circular pattern
  const rings = Math.min(5, Math.floor(dotCount / 3)) // Maximum 5 rings
  
  for (let ring = 0; ring < rings; ring++) {
    const ringRadius = (radius / rings) * (ring + 1)
    const dotsInRing = Math.max(6, Math.floor(8 + ring * 4)) // More dots in outer rings
    
    for (let i = 0; i < dotsInRing; i++) {
      const angle = (i / dotsInRing) * 2 * Math.PI
      const x = Math.cos(angle) * ringRadius
      const y = Math.sin(angle) * ringRadius
      const delay = ring * 0.1 + (i / dotsInRing) * 0.3
      
      dots.push({ 
        key: `ring-${ring}-dot-${i}`, 
        x, 
        y, 
        delay,
        ring 
      })
    }
  }

  // Add center dot
  dots.push({ key: 'center', x: 0, y: 0, delay: 0, ring: -1 })

  useEffect(() => {
    if (isClient) {
      controls.start(i => ({
        scale: [0.6, 1, 0.6],
        opacity: [0.5, 1, 0.5],
        transition: {
          duration: duration / 1000,
          ease: "easeInOut",
          repeat: Infinity,
          delay: i.delay
        }
      }))
    }
  }, [controls, duration, isClient])

  // Don't render anything on server side
  if (!isClient) {
    return (
      <div
        className={cn("relative flex items-center justify-center", className)}
        style={{ width: size, height: size }}
      >
        {/* Static fallback for SSR */}
        <div className="size-2 rounded-full bg-black" />
      </div>
    )
  }

  return (
    <div
      className={cn("relative flex items-center justify-center", className)}
      style={{ width: size, height: size }}
    >
      {dots.map((dot, idx) => (
        <motion.div
          key={dot.key}
          custom={dot}
          animate={controls}
          className="absolute rounded-full bg-black"
          style={{
            width: dot.ring === -1 ? 8 : 5, // Center dot slightly larger
            height: dot.ring === -1 ? 8 : 5,
            left: `calc(50% + ${dot.x}px - ${dot.ring === -1 ? 4 : 2.5}px)`,
            top: `calc(50% + ${dot.y}px - ${dot.ring === -1 ? 4 : 2.5}px)`,
          }}
        />
      ))}
    </div>
  )
}

// export function OrbitPulseGrid({
//   className,
//   size = 50,
//   dotCount = 6,
//   duration = 1200
// }: {
//   className?: string
//   size?: number
//   dotCount?: number
//   duration?: number
// }) {
//   const controls = useAnimationControls()
//   const mid = Math.floor(dotCount / 2)
//   const spacing = size / dotCount
//   const dots: { key: string; x: number; y: number; delay: number }[] = []

//   for (let row = 0; row < dotCount; row++) {
//     for (let col = 0; col < dotCount; col++) {
//       const dx = col - mid
//       const dy = row - mid
//       const dist = Math.sqrt(dx * dx + dy * dy)

//       if (dist <= mid + 0.2) {
//         const x = dx * spacing
//         const y = dy * spacing
//         const delay = dist * 0.08
//         dots.push({ key: `${row}-${col}`, x, y, delay })
//       }
//     }
//   }

//   useEffect(() => {
//     controls.start(i => ({
//       scale: [0.6, 0.8, 0.6],
//       opacity: [0.7, 1, 0.7],
//       transition: {
//         duration: duration / 1000,
//         ease: "easeInOut",
//         repeat: Infinity,
//         delay: i.delay
//       }
//     }))
//   }, [controls, duration])

//   return (
//     <div
//       className={cn("relative flex items-center justify-center", className)}
//       style={{ width: size, height: size }}
//     >
//       {dots.map((dot, idx) => (
//         <motion.div
//           key={dot.key}
//           custom={dot}
//           animate={controls}
//           // className="absolute bg-slate-600 dark:bg-slate-300 rounded-full"
//           className="absolute bg-black rounded-full"
//           style={{
//             width: 6,
//             height: 6,
//             left: `calc(50% + ${dot.x}px - 3px)`,
//             top: `calc(50% + ${dot.y}px - 3px)`,
//           }}
//         />
//       ))}
//     </div>
//   )
// }


