"use client"

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertTriangle, Play } from 'lucide-react';
import { ThesisAPI } from '@/lib/api/thesis-api';
import { ThesisStatus } from '@/lib/types/thesis';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'passed' | 'failed';
  message?: string;
  error?: string;
}

export function ThesisBuilderTest() {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'API Connection Test', status: 'pending' },
    { name: 'Create Thesis Test', status: 'pending' },
    { name: 'Get Thesis Test', status: 'pending' },
    { name: 'Update Thesis Test', status: 'pending' },
    { name: 'List Theses Test', status: 'pending' },
    { name: 'Error Handling Test', status: 'pending' },
    { name: 'Data Validation Test', status: 'pending' },
  ]);

  const [isRunning, setIsRunning] = useState(false);

  const updateTest = (index: number, updates: Partial<TestResult>) => {
    setTests(prev => prev.map((test, i) => i === index ? { ...test, ...updates } : test));
  };

  const runTests = async () => {
    setIsRunning(true);
    
    try {
      // Test 1: API Connection Test
      updateTest(0, { status: 'running' });
      try {
        await ThesisAPI.listTheses({ limit: 1 });
        updateTest(0, { status: 'passed', message: 'API connection successful' });
      } catch (error: any) {
        updateTest(0, { status: 'failed', error: error.message });
      }

      // Test 2: Create Thesis Test
      updateTest(1, { status: 'running' });
      let testThesisId: string | undefined;
      try {
        const testThesis = await ThesisAPI.createThesis({
          name: 'Test Thesis - ' + Date.now(),
          description: 'Test thesis for validation',
          form_id: '507f1f77bcf86cd799439011', // Mock form ID
          status: ThesisStatus.DRAFT,
          is_active: true,
          scoring_rules: [],
          match_rules: []
        });
        testThesisId = testThesis.id || testThesis._id;
        updateTest(1, { status: 'passed', message: `Thesis created with ID: ${testThesisId}` });
      } catch (error: any) {
        updateTest(1, { status: 'failed', error: error.message });
      }

      // Test 3: Get Thesis Test
      if (testThesisId) {
        updateTest(2, { status: 'running' });
        try {
          const fetchedThesis = await ThesisAPI.getThesis(testThesisId);
          if (fetchedThesis && (fetchedThesis.id || fetchedThesis._id)) {
            updateTest(2, { status: 'passed', message: 'Thesis fetched successfully' });
          } else {
            updateTest(2, { status: 'failed', error: 'Invalid thesis data structure' });
          }
        } catch (error: any) {
          updateTest(2, { status: 'failed', error: error.message });
        }

        // Test 4: Update Thesis Test
        updateTest(3, { status: 'running' });
        try {
          const updatedThesis = await ThesisAPI.updateThesis(testThesisId, {
            description: 'Updated test thesis description'
          });
          if (updatedThesis && updatedThesis.description?.includes('Updated')) {
            updateTest(3, { status: 'passed', message: 'Thesis updated successfully' });
          } else {
            updateTest(3, { status: 'failed', error: 'Update not reflected in response' });
          }
        } catch (error: any) {
          updateTest(3, { status: 'failed', error: error.message });
        }
      } else {
        updateTest(2, { status: 'failed', error: 'No thesis ID from create test' });
        updateTest(3, { status: 'failed', error: 'No thesis ID from create test' });
      }

      // Test 5: List Theses Test
      updateTest(4, { status: 'running' });
      try {
        const theses = await ThesisAPI.listTheses();
        if (Array.isArray(theses)) {
          updateTest(4, { status: 'passed', message: `Found ${theses.length} theses` });
        } else {
          updateTest(4, { status: 'failed', error: 'Response is not an array' });
        }
      } catch (error: any) {
        updateTest(4, { status: 'failed', error: error.message });
      }

      // Test 6: Error Handling Test
      updateTest(5, { status: 'running' });
      try {
        await ThesisAPI.getThesis('invalid-id-12345');
        updateTest(5, { status: 'failed', error: 'Should have thrown error for invalid ID' });
      } catch (error: any) {
        if (error.message && error.message.includes('Failed to get thesis')) {
          updateTest(5, { status: 'passed', message: 'Error handling working correctly' });
        } else {
          updateTest(5, { status: 'failed', error: 'Unexpected error format: ' + error.message });
        }
      }

      // Test 7: Data Validation Test
      updateTest(6, { status: 'running' });
      try {
        await ThesisAPI.createThesis({
          name: '',
          description: '',
          form_id: '',
        } as any);
        updateTest(6, { status: 'failed', error: 'Should have thrown validation error' });
      } catch (error: any) {
        if (error.message && (error.message.includes('required') || error.message.includes('name'))) {
          updateTest(6, { status: 'passed', message: 'Validation working correctly' });
        } else {
          updateTest(6, { status: 'failed', error: 'Unexpected validation error: ' + error.message });
        }
      }

    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="size-4 text-green-600" />;
      case 'failed':
        return <XCircle className="size-4 text-red-600" />;
      case 'running':
        return <div className="size-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />;
      default:
        return <div className="size-4 rounded-full border-2 border-gray-300" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'passed':
        return <Badge variant="default" className="bg-green-600">Passed</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      case 'running':
        return <Badge variant="secondary">Running</Badge>;
      default:
        return <Badge variant="outline">Pending</Badge>;
    }
  };

  const passedTests = tests.filter(t => t.status === 'passed').length;
  const failedTests = tests.filter(t => t.status === 'failed').length;
  const totalTests = tests.length;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="size-5" />
            Thesis Builder Robustness Test
          </CardTitle>
          <CardDescription>
            Test all critical backend/frontend integrations and error handling
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium">
                Progress: {passedTests + failedTests}/{totalTests}
              </span>
              <Badge variant={failedTests > 0 ? "destructive" : passedTests === totalTests ? "default" : "secondary"}>
                {passedTests} passed, {failedTests} failed
              </Badge>
            </div>
            <Button 
              onClick={runTests} 
              disabled={isRunning}
              className="flex items-center gap-2"
            >
              <Play className="size-4" />
              {isRunning ? 'Running Tests...' : 'Run All Tests'}
            </Button>
          </div>

          <div className="space-y-2">
            {tests.map((test, index) => (
              <div key={index} className="flex items-center justify-between rounded-lg border p-3">
                <div className="flex items-center gap-3">
                  {getStatusIcon(test.status)}
                  <span className="font-medium">{test.name}</span>
                </div>
                <div className="flex items-center gap-3">
                  {test.message && (
                    <span className="text-sm text-muted-foreground">{test.message}</span>
                  )}
                  {test.error && (
                    <span className="text-sm text-red-600">{test.error}</span>
                  )}
                  {getStatusBadge(test.status)}
                </div>
              </div>
            ))}
          </div>

          {failedTests > 0 && (
            <Alert variant="destructive">
              <XCircle className="size-4" />
              <AlertDescription>
                {failedTests} test(s) failed. Check the error messages above for details.
              </AlertDescription>
            </Alert>
          )}

          {passedTests === totalTests && totalTests > 0 && (
            <Alert>
              <CheckCircle className="size-4" />
              <AlertDescription>
                All tests passed! The Thesis Builder is robust and ready for production.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
