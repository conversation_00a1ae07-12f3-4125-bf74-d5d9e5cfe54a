"use client"

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { PremiumSummaryTiles } from '@/components/core/dashboard/premium-summary-tiles';
import { PremiumInsightsCharts } from '@/components/core/dashboard/premium-insights-charts';
import { PremiumQuickActions } from '@/components/core/dashboard/premium-quick-actions';
import { useDashboardInsights } from '@/lib/hooks/use-dashboard-insights';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  RefreshCw, 
  AlertCircle, 
  TrendingUp,
  Zap,
  Radio
} from 'lucide-react';
import { cn } from '@/lib/utils';

export default function PremiumDashboardPage() {
  const { data: insightsData, loading: isLoading, error, refetch } = useDashboardInsights();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await refetch();
    setIsRefreshing(false);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-tx-surface p-4 md:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <Skeleton className="h-8 w-64 mb-2" />
              <Skeleton className="h-4 w-96" />
            </div>
            <Skeleton className="h-10 w-32" />
          </div>

          {/* Summary Tiles */}
          <div className="grid grid-cols-2 gap-4 md:gap-6 lg:gap-8">
            {Array.from({ length: 4 }).map((_, i) => (
              <Skeleton key={i} className="h-32 rounded-xl" />
            ))}
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 md:gap-8 lg:grid-cols-6 lg:gap-8 xl:grid-cols-12 xl:gap-8">
            {Array.from({ length: 6 }).map((_, i) => (
              <Skeleton key={i} className="h-80 rounded-xl" />
            ))}
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {Array.from({ length: 6 }).map((_, i) => (
              <Skeleton key={i} className="h-24 rounded-xl" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-tx-surface p-4 md:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto">
          <Card className="border-0 bg-tx-surface shadow-institutional rounded-xl">
            <CardContent className="p-8 text-center">
              <AlertCircle className="w-12 h-12 text-tx-info mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-tx-fg mb-2">
                Unable to load dashboard data
              </h2>
              <p className="text-tx-muted mb-6">
                There was an error loading your premium insights. Please try again.
              </p>
              <Button onClick={handleRefresh} disabled={isRefreshing}>
                <RefreshCw className={cn("w-4 h-4 mr-2", isRefreshing && "animate-spin")} />
                Retry
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // No data state
  if (!insightsData) {
    return (
      <div className="min-h-screen bg-tx-surface p-4 md:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto">
          <Card className="border-0 bg-tx-surface shadow-institutional rounded-xl">
            <CardContent className="p-8 text-center">
              <TrendingUp className="w-12 h-12 text-tx-primary mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-tx-fg mb-2">
                No insights data available
              </h2>
              <p className="text-tx-muted mb-6">
                Start creating deals to see your premium insights and analytics.
              </p>
              <Button>
                <Zap className="w-4 h-4 mr-2" />
                Create First Deal
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-tx-surface">
      <div className="max-w-7xl mx-auto p-4 md:p-6 lg:p-8 space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="flex items-center justify-between"
        >
          <div>
            <h1 className="text-2xl font-bold text-tx-fg mb-2">
              Dashboard
            </h1>
            <p className="text-tx-muted">
              Institutional-grade insights for elite private market investors
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <Badge variant="outline" className="border-tx-primary/20 text-tx-primary bg-tx-primary/5">
              <Radio className="w-3 h-3 mr-1" />
              Live Data
            </Badge>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="border-tx-primary/20 text-tx-primary hover:bg-tx-primary/10"
            >
              <RefreshCw className={cn("w-4 h-4 mr-2", isRefreshing && "animate-spin")} />
              Refresh
            </Button>
          </div>
        </motion.div>

        {/* Summary Tiles */}
        <PremiumSummaryTiles dashboardData={{ insights: insightsData }} />

        {/* Insights Charts */}
        <PremiumInsightsCharts insightsData={insightsData} />

        {/* Quick Actions */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-tx-fg">
              Quick Actions
            </h2>
            <p className="text-sm text-tx-muted">
              Accelerate your deal flow
            </p>
          </div>
          <PremiumQuickActions />
        </div>
      </div>
    </div>
  );
} 