from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, TypeVar, Union

from app.models.queue import Job, JobPriority, JobStatus, QueueStats, QueueType

# Type for job handler functions
T = TypeVar("T")
JobHandler = Callable[[Dict[str, Any]], Union[Dict[str, Any], T]]
AsyncJobHandler = Callable[[Dict[str, Any]], Union[Dict[str, Any], T]]


class QueueBackendInterface(ABC):
    """Interface for queue backend implementations."""

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the queue backend."""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up resources used by the queue backend."""
        pass

    @abstractmethod
    async def enqueue(self, job: Job) -> Job:
        """
        Add a job to the queue.

        Args:
            job: The job to enqueue

        Returns:
            The enqueued job with updated information
        """
        pass

    @abstractmethod
    async def dequeue(
        self,
        queue_type: QueueType = QueueType.DEFAULT,
        job_types: Optional[List[str]] = None,
        wait_timeout: int = 0,
    ) -> Optional[Job]:
        """
        Get a job from the queue for processing.

        Args:
            queue_type: Which queue to get jobs from
            job_types: Optional list of job types to filter by
            wait_timeout: How long to wait for a job (0 = no wait)

        Returns:
            Job or None if no jobs available
        """
        pass

    @abstractmethod
    async def complete(
        self, job_id: str, result: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Mark a job as completed.

        Args:
            job_id: ID of the job to mark as completed
            result: Optional result data from job processing

        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    async def fail(self, job_id: str, error: str, retry: bool = True) -> bool:
        """
        Mark a job as failed.

        Args:
            job_id: ID of the job to mark as failed
            error: Error message or reason for failure
            retry: Whether to retry the job if retries are available

        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    async def get_job(self, job_id: str) -> Optional[Job]:
        """
        Get job details by ID.

        Args:
            job_id: ID of the job to retrieve

        Returns:
            Job or None if not found
        """
        pass

    @abstractmethod
    async def cancel(self, job_id: str) -> bool:
        """
        Cancel a pending or scheduled job.

        Args:
            job_id: ID of the job to cancel

        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    async def retry(self, job_id: str, delay_seconds: int = 0) -> bool:
        """
        Retry a failed job.

        Args:
            job_id: ID of the job to retry
            delay_seconds: Delay before job is available for processing

        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    async def purge(
        self, queue_type: QueueType, older_than: Optional[Union[datetime, int]] = None
    ) -> int:
        """
        Remove all jobs from a queue.

        Args:
            queue_type: Which queue to purge
            older_than: Optional time threshold for purging

        Returns:
            Number of jobs purged
        """
        pass

    @abstractmethod
    async def list_jobs(
        self,
        queue_type: QueueType = QueueType.DEFAULT,
        status: Optional[JobStatus] = None,
        job_type: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Job]:
        """
        List jobs in a queue.

        Args:
            queue_type: Which queue to list jobs from
            status: Optional filter by job status
            job_type: Optional filter by job type
            limit: Maximum number of jobs to return
            offset: Offset for pagination

        Returns:
            List of jobs
        """
        pass

    @abstractmethod
    async def get_stats(
        self, queue_type: Optional[QueueType] = None
    ) -> Union[QueueStats, Dict[str, QueueStats]]:
        """
        Get statistics about the queue(s).

        Args:
            queue_type: Optional specific queue to get stats for

        Returns:
            Queue statistics for the specified queue or all queues
        """
        pass

    @abstractmethod
    async def process_scheduled_jobs(self) -> int:
        """
        Process scheduled jobs that are due.

        Returns:
            Number of jobs processed
        """
        pass

    @abstractmethod
    async def process_stalled_jobs(self) -> int:
        """
        Process stalled jobs (jobs that have been processing for too long).

        Returns:
            Number of jobs processed
        """
        pass


class QueueServiceInterface(ABC):
    """Interface for the queue service."""

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the queue service."""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up resources used by the queue service."""
        pass

    @abstractmethod
    async def enqueue_job(
        self,
        job_type: str,
        payload: Dict[str, Any],
        queue_type: QueueType = QueueType.DEFAULT,
        priority: JobPriority = JobPriority.NORMAL,
        job_id: Optional[str] = None,
        delay_seconds: int = 0,
        retry_config: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Job:
        """
        Add a job to the queue.

        Args:
            job_type: Type of job to process
            payload: Job data
            queue_type: Which queue to use
            priority: Job priority
            job_id: Optional custom job ID
            delay_seconds: Delay before job is available for processing
            retry_config: Configuration for retries
            metadata: Additional metadata for the job

        Returns:
            The enqueued job
        """
        pass

    @abstractmethod
    async def schedule_job(
        self,
        job_type: str,
        payload: Dict[str, Any],
        scheduled_time: Union[datetime, int],
        job_id: Optional[str] = None,
        retry_config: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Job:
        """
        Schedule a job to run at a specific time.

        Args:
            job_type: Type of job to process
            payload: Job data
            scheduled_time: When to run the job (datetime or unix timestamp)
            job_id: Optional custom job ID
            retry_config: Configuration for retries
            metadata: Additional metadata for the job

        Returns:
            The scheduled job
        """
        pass

    @abstractmethod
    async def get_backend(self) -> QueueBackendInterface:
        """
        Get the queue backend implementation.

        Returns:
            The queue backend
        """
        pass
