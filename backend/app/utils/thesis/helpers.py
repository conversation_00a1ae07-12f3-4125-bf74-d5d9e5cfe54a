"""
Investment Thesis Service Helper Functions

This module provides helper functions for the thesis service, including validation,
error handling, and rule management utilities.
"""

import traceback
from typing import Dict, List, Any, Optional, Union, Callable, TypeVar
from bson import ObjectId

from app.models.thesis import (
    InvestmentThesis, ScoringRule, MatchRule
)
from app.core.logging import get_logger

logger = get_logger(__name__)

# Type variables for generic functions
T = TypeVar('T', ScoringRule, MatchRule)


async def validate_thesis_ownership(
    thesis_id: Union[str, ObjectId],
    org_id: Union[str, ObjectId],
    get_thesis_func: Callable[[Union[str, ObjectId]], Any]
) -> InvestmentThesis:
    """
    Validate that a thesis exists and belongs to the organization.

    Args:
        thesis_id: ID of the thesis to validate
        org_id: Organization ID to check ownership against
        get_thesis_func: Function to get the thesis

    Returns:
        The thesis if it exists and belongs to the organization

    Raises:
        ValueError: If thesis doesn't exist or doesn't belong to the organization
    """
    thesis = await get_thesis_func(thesis_id)
    if not thesis:
        raise ValueError(f"Thesis not found with ID: {thesis_id}")

    if str(thesis.org_id) != str(org_id):
        raise ValueError(f"Thesis with ID {thesis_id} does not belong to organization {org_id}")

    return thesis


async def validate_rule_ownership(
    rule_id: Union[str, ObjectId],
    org_id: Union[str, ObjectId],
    get_rule_func: Callable[[Union[str, ObjectId]], Any],
    get_thesis_func: Callable[[Union[str, ObjectId]], Any],
    rule_type: str = "rule"
) -> Union[ScoringRule, MatchRule]:
    """
    Validate that a rule exists and belongs to an organization's thesis.

    Args:
        rule_id: ID of the rule to validate
        org_id: Organization ID to check ownership against
        get_rule_func: Function to get the rule (get_scoring_rule or get_match_rule)
        get_thesis_func: Function to get the thesis
        rule_type: Type of rule for error messages

    Returns:
        The rule if it exists and belongs to the organization's thesis

    Raises:
        ValueError: If rule doesn't exist or doesn't belong to the organization's thesis
    """
    rule = await get_rule_func(rule_id)
    if not rule:
        raise ValueError(f"{rule_type.capitalize()} not found with ID: {rule_id}")

    # Check if thesis belongs to the organization
    thesis = await get_thesis_func(rule.thesis_id)
    if not thesis or str(thesis.org_id) != str(org_id):
        raise ValueError(f"{rule_type.capitalize()} with ID {rule_id} does not belong to organization {org_id}")

    return rule


def log_exception(e: Exception, context: Optional[Dict[str, Any]] = None) -> None:
    """
    Log exceptions consistently with proper context.

    Args:
        e: The exception that was raised
        context: Additional context information for logging
    """
    error_detail = f"Error: {str(e)}\n{traceback.format_exc()}"
    logger.error(error_detail, extra={"context": context})


async def create_scoring_rules(
    thesis_id: Union[str, ObjectId],
    rules_data: List[Dict[str, Any]],
    create_rule_func: Callable
) -> List[ScoringRule]:
    """
    Create scoring rules for a thesis.

    Args:
        thesis_id: ID of the thesis to create rules for
        rules_data: List of rule data dictionaries
        create_rule_func: Function to create a scoring rule

    Returns:
        List of created scoring rules
    """
    created_rules = []

    for rule_data in rules_data:
        try:
            # Allow bonus rules without question_id
            if not rule_data.get("question_id") and rule_data.get("rule_type") != "bonus":
                continue  # Skip rules without question_id unless it's a bonus rule
            data = rule_data.copy()
            if "thesis_id" in data:
                del data["thesis_id"]
            rule = await create_rule_func(
                thesis_id,
                **data  # Pass all rule data to the service
            )
            if rule:
                created_rules.append(rule)
        except Exception as rule_error:
            # Log error but continue with other rules
            error_detail = f"Error creating scoring rule: {str(rule_error)}\n{traceback.format_exc()}"
            logger.error(error_detail)
            # Continue with other rules

    return created_rules


async def create_match_rules(
    thesis_id: Union[str, ObjectId],
    rules_data: List[Dict[str, Any]],
    create_rule_func: Callable
) -> List[MatchRule]:
    """
    Create match rules for a thesis.

    Args:
        thesis_id: ID of the thesis to create rules for
        rules_data: List of rule data dictionaries
        create_rule_func: Function to create a match rule

    Returns:
        List of created match rules
    """
    created_rules = []

    for rule_data in rules_data:
        try:
            if not rule_data.get("name"):
                continue  # Skip rules without name

            rule = await create_rule_func(
                thesis_id,
                name=rule_data["name"],
                description=rule_data.get("description"),
                operator=rule_data.get("operator", "and"),
                conditions=rule_data.get("conditions", [])
            )
            if rule:
                created_rules.append(rule)
        except Exception as rule_error:
            # Log error but continue with other rules
            error_detail = f"Error creating match rule: {str(rule_error)}\n{traceback.format_exc()}"
            logger.error(error_detail)
            # Continue with other rules

    return created_rules


async def update_scoring_rules_for_thesis(
    thesis_id: Union[str, ObjectId],
    scoring_rules: List[Dict[str, Any]],
    list_rules_func: Callable,
    update_rule_func: Callable,
    create_rule_func: Callable,
    delete_rule_func: Callable
) -> None:
    """
    Update scoring rules for a thesis.

    Args:
        thesis_id: ID of the thesis to update rules for
        scoring_rules: List of rule data dictionaries
        list_rules_func: Function to list existing rules
        update_rule_func: Function to update a rule
        create_rule_func: Function to create a rule
        delete_rule_func: Function to delete a rule
    """
    # Get existing rules
    existing_rules = await list_rules_func(thesis_id)
    existing_rule_map = {str(rule.id): rule for rule in existing_rules}

    # Process each rule
    for rule_data in scoring_rules:
        rule_id = rule_data.get("id")

        if rule_id and rule_id in existing_rule_map:
            # Update existing rule
            await update_rule_func(rule_id, rule_data)
        elif rule_data.get("question_id"):  # Ensure question_id is present for new rules
            # Create new rule
            await create_rule_func(
                thesis_id,
                question_id=rule_data["question_id"],
                weight=rule_data.get("weight", 1.0),
                expected_value=rule_data.get("expected_value"),
                condition=rule_data.get("condition"),
                is_repeatable=rule_data.get("is_repeatable", False),
                aggregation_type=rule_data.get("aggregation_type"),
                aggregation_field=rule_data.get("aggregation_field"),
                filters=rule_data.get("filters"),
                exclude_from_scoring=rule_data.get("exclude_from_scoring", False),
                rule_type=rule_data.get("rule_type", "scoring"),
                bonus_points=rule_data.get("bonus_points")
            )

    # Remove rules not in the update
    if scoring_rules:
        update_rule_ids = {rule.get("id") for rule in scoring_rules if rule.get("id")}
        for rule_id in existing_rule_map.keys():
            if rule_id not in update_rule_ids:
                await delete_rule_func(rule_id)


async def update_match_rules_for_thesis(
    thesis_id: Union[str, ObjectId],
    match_rules: List[Dict[str, Any]],
    list_rules_func: Callable,
    update_rule_func: Callable,
    create_rule_func: Callable,
    delete_rule_func: Callable
) -> None:
    """
    Update match rules for a thesis.

    Args:
        thesis_id: ID of the thesis to update rules for
        match_rules: List of rule data dictionaries
        list_rules_func: Function to list existing rules
        update_rule_func: Function to update a rule
        create_rule_func: Function to create a rule
        delete_rule_func: Function to delete a rule
    """
    # Get existing rules
    existing_rules = await list_rules_func(thesis_id)
    existing_rule_map = {str(rule.id): rule for rule in existing_rules}

    logger.info(f"Existing rules: {existing_rules}")
    logger.info(f"Existing rule map: {existing_rule_map}")
    logger.info(f"Match rules: {match_rules}")

    # Process each rule
    for rule_data in match_rules:
        rule_id = rule_data.get("id")
        
        if rule_id and rule_id in existing_rule_map:
            # Update existing rule
            logger.info(f"Updating match rule {rule_id} with data: {rule_data}")
            await update_rule_func(rule_id, rule_data)
        elif rule_data.get("name"):  # Ensure name is present for new rules
            # Create new rule
            await create_rule_func(
                thesis_id,
                name=rule_data["name"],
                description=rule_data.get("description"),
                operator=rule_data.get("operator", "and"),
                conditions=rule_data.get("conditions", [])
            )

    # Remove rules not in the update
    if match_rules:
        update_rule_ids = {rule.get("id") for rule in match_rules if rule.get("id")}
        for rule_id in existing_rule_map.keys():
            if rule_id not in update_rule_ids:
                await delete_rule_func(rule_id)
