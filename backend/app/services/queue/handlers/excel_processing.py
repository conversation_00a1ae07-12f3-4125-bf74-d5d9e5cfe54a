"""
Excel Processing Queue Handler

This module handles the processing of Excel/CSV file uploads for bulk deal creation.
It parses the uploaded files, validates the data, and creates deals using the existing
deal creation logic.
"""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional

import boto3
import pandas as pd

from app.core.config import settings
from app.core.logging import get_logger
from app.models.deal import Deal, DealStatus
from app.models.queue import Job
from app.services.base import BaseService
from app.services.deal.interfaces import DealServiceInterface
from app.services.queue.worker_interface import JobHandlerInterface

logger = get_logger(__name__)


class ExcelProcessingHandler(BaseService, JobHandlerInterface):
    """
    Handler for processing Excel/CSV uploads and creating deals.

    This handler:
    1. Downloads the uploaded file from S3
    2. Parses the Excel/CSV file using pandas
    3. Validates each row against required fields
    4. Creates deals using existing deal creation logic
    5. Stores results in S3 for audit trail
    """

    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.s3_client = None
        self.deal_service: Optional[DealServiceInterface] = None

    async def handle(self, job: Job) -> Dict[str, Any]:
        """
        Handle a job (required by JobHandlerInterface).

        Args:
            job: The job to handle

        Returns:
            Result of job processing
        """
        return await self.process_job(job.payload)

    async def initialize(self):
        """Initialize the handler with required services."""
        self.s3_client = boto3.client(
            "s3",
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_REGION,
        )
        # We'll use the Deal model directly instead of the service interface

    async def process_job(self, job_payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the Excel upload job.

        Args:
            job_payload: Job payload containing upload details

        Returns:
            Dict containing processing results
        """
        try:
            await self.initialize()

            upload_id = job_payload["upload_id"]
            org_id = job_payload["org_id"]
            user_id = job_payload["user_id"]
            s3_key = job_payload["s3_key"]
            file_extension = job_payload["file_extension"]
            metadata = job_payload.get("metadata", {})

            self.logger.info(f"Starting Excel processing for upload {upload_id}")

            # Download file from S3
            file_content = await self._download_file_from_s3(s3_key)
            if not file_content:
                raise Exception("Failed to download file from S3")

            # Parse Excel/CSV file
            rows = await self._parse_file(file_content, file_extension)
            if not rows:
                raise Exception("No data found in uploaded file")

            # Process rows and create deals
            results = await self._process_rows(
                rows, org_id, user_id, upload_id, metadata
            )

            # Store results in S3
            await self._store_results(upload_id, org_id, results)

            self.logger.info(
                f"Completed Excel processing for upload {upload_id}: {results['created']} created, {results['failed']} failed"
            )

            return {"success": True, "upload_id": upload_id, "results": results}

        except Exception as e:
            self.logger.error(f"Error processing Excel upload: {e}")
            return {"success": False, "error": str(e)}

    async def _download_file_from_s3(self, s3_key: str) -> Optional[bytes]:
        """Download file from S3."""
        try:
            if not self.s3_client:
                raise Exception("S3 client not initialized")

            # Extract bucket and key from s3:// format
            if s3_key.startswith("s3://"):
                s3_key = s3_key.replace("s3://", "")
                bucket, key = s3_key.split("/", 1)
            else:
                raise ValueError("Invalid S3 key format")

            response = self.s3_client.get_object(Bucket=bucket, Key=key)
            return response["Body"].read()

        except Exception as e:
            self.logger.error(f"Failed to download file from S3: {e}")
            return None

    async def _parse_file(
        self, file_content: bytes, file_extension: str
    ) -> List[Dict[str, Any]]:
        """Parse Excel/CSV file and return list of dictionaries."""
        try:
            import io

            if file_extension == "csv":
                df = pd.read_csv(io.BytesIO(file_content))
            elif file_extension == "xlsx":
                df = pd.read_excel(io.BytesIO(file_content))
            else:
                raise ValueError(f"Unsupported file extension: {file_extension}")

            # Convert DataFrame to list of dictionaries
            rows = df.to_dict("records")

            # Clean up any NaN values and convert to proper types
            cleaned_rows = []
            for row in rows:
                cleaned_row = {}
                for key, value in row.items():
                    if pd.isna(value):
                        cleaned_row[str(key)] = None
                    else:
                        cleaned_row[str(key)] = value
                cleaned_rows.append(cleaned_row)

            self.logger.info(f"Parsed {len(cleaned_rows)} rows from file")
            return cleaned_rows

        except Exception as e:
            self.logger.error(f"Failed to parse file: {e}")
            raise

    async def _process_rows(
        self,
        rows: List[Dict[str, Any]],
        org_id: str,
        user_id: str,
        upload_id: str,
        metadata: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Process rows and create deals."""
        created_count = 0
        failed_count = 0
        errors = []

        for row_index, row in enumerate(rows, start=1):
            try:
                # Validate required fields
                validation_error = self._validate_row(row)
                if validation_error:
                    errors.append({"row": row_index, "reason": validation_error})
                    failed_count += 1
                    continue

                # Create deal
                deal = await self._create_deal_from_row(
                    row, org_id, user_id, upload_id, metadata
                )
                if deal:
                    created_count += 1
                    self.logger.info(f"Created deal {deal.id} from row {row_index}")
                else:
                    errors.append({"row": row_index, "reason": "Failed to create deal"})
                    failed_count += 1

            except Exception as e:
                self.logger.error(f"Error processing row {row_index}: {e}")
                errors.append({"row": row_index, "reason": str(e)})
                failed_count += 1

        return {"created": created_count, "failed": failed_count, "errors": errors}

    def _validate_row(self, row: Dict[str, Any]) -> Optional[str]:
        """Validate a row against required fields."""
        required_fields = ["company_name"]

        for field in required_fields:
            if field not in row or not row[field]:
                return f"Missing required field: {field}"

        # Validate email format if provided
        if "invited_email" in row and row["invited_email"]:
            import re

            email_pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
            if not re.match(email_pattern, row["invited_email"]):
                return f"Invalid email format: {row['invited_email']}"

        return None

    async def _create_deal_from_row(
        self,
        row: Dict[str, Any],
        org_id: str,
        user_id: str,
        upload_id: str,
        metadata: Dict[str, Any],
    ) -> Optional[Deal]:
        """Create a deal from a row using existing deal creation logic."""
        try:
            # Parse tags as list
            tags = row.get("tags")
            if isinstance(tags, str):
                tags = [t.strip() for t in tags.split(",") if t.strip()]
            elif not isinstance(tags, list):
                tags = []

            # Prepare deal data
            deal_data = {
                "company_name": row["company_name"],
                "stage": row.get("stage"),
                "sector": row.get("sector"),
                "status": row.get("status", DealStatus.NEW),
                "company_website": row.get("company_website"),
                "short_description": row.get("short_description"),
                "invited_email": row.get("invited_email"),
                "notes": row.get("notes"),
                "tags": tags,
                "org_id": org_id,
                "created_by": user_id,
                "metadata": {
                    **metadata,
                    "upload_id": upload_id,
                    "imported_at": datetime.utcnow().isoformat(),
                },
            }

            # Remove None values
            deal_data = {k: v for k, v in deal_data.items() if v is not None}

            # Create deal using existing logic
            deal = Deal(**deal_data)
            await deal.save()

            # Add timeline event
            await self._add_timeline_event(deal, "Created via Excel upload")

            # Trigger company enrichment if website is provided
            if deal.company_website:
                try:
                    await self._trigger_company_enrichment(deal)
                except Exception as enrichment_error:
                    self.logger.warning(
                        f"Failed to trigger company enrichment for deal {deal.id}: {enrichment_error}"
                    )

            return deal

        except Exception as e:
            self.logger.error(f"Failed to create deal from row: {e}")
            return None

    async def _add_timeline_event(self, deal: Deal, event_type: str):
        """Add timeline event to the deal."""
        try:
            timeline_event = {
                "type": event_type,
                "timestamp": datetime.utcnow(),
                "metadata": {"source": "excel_upload"},
            }

            if not deal.timeline:
                deal.timeline = []

            deal.timeline.append(timeline_event)
            await deal.save()

        except Exception as e:
            self.logger.warning(f"Failed to add timeline event: {e}")

    async def _trigger_company_enrichment(self, deal: Deal):
        """Trigger company enrichment for the deal."""
        try:
            if not deal.company_website:
                return

            # Extract domain from company website
            domain = ".".join(deal.company_website.split(".")[-2:])

            # Prepare enrichment request
            enrichment_request = {
                "company_id": deal.company_website,
                "org_id": str(deal.org_id),
                "company_name": deal.company_name,
                "domain": domain,
                "pipeline_types": ["company"],
                "priority": "high",
            }

            # Make internal HTTP call to datapipelines service
            import httpx

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "http://datapipelines-api:8001/api/v1/pipelines/trigger",
                    json=enrichment_request,
                    timeout=30.0,
                )
                response.raise_for_status()

            self.logger.info(f"Triggered company enrichment for deal {deal.id}")

        except Exception as e:
            self.logger.error(f"Failed to trigger company enrichment: {e}")
            raise

    async def _store_results(
        self, upload_id: str, org_id: str, results: Dict[str, Any]
    ):
        """Store processing results in S3."""
        try:
            if not self.s3_client:
                raise Exception("S3 client not initialized")

            results_data = {
                "upload_id": upload_id,
                "org_id": org_id,
                "processed_at": datetime.utcnow().isoformat(),
                **results,
            }

            results_key = f"org_{org_id}/deal_uploads/{upload_id}_results.json"

            self.s3_client.put_object(
                Bucket=settings.S3_BUCKET_ASSETS,
                Key=results_key,
                Body=json.dumps(results_data, indent=2),
                ContentType="application/json",
            )

            self.logger.info(f"Stored results for upload {upload_id}")

        except Exception as e:
            self.logger.error(f"Failed to store results: {e}")
            # Don't fail the job if results storage fails

    def cleanup(self):
        pass
