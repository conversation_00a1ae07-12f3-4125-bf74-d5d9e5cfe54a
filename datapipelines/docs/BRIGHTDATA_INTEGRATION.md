# BrightData Integration for TractionX Data Pipeline Service

## Overview

This document describes the BrightData fallback integration for the TractionX founder enrichment pipeline. When People Data Labs (PDL) enrichment fails or returns insufficient data, the system automatically falls back to BrightData's LinkedIn scraping infrastructure.

## Architecture

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                    BRIGHTDATA FALLBACK INTEGRATION                         │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐  │
│  │   PDL API   │───▶│  PDL Check  │───▶│ BrightData  │───▶│   Storage   │  │
│  │             │    │             │    │   Fallback  │    │             │  │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘  │
│         │                   │                   │                   │      │
│         ▼                   ▼                   ▼                   ▼      │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐  │
│  │   Success   │    │ Insufficient│    │   Trigger   │    │   RDS/S3    │  │
│  │   Process   │    │   Data?     │    │   Scraping  │    │   Storage   │  │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘  │
│         │                   │                   │                   │      │
│         ▼                   ▼                   ▼                   ▼      │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐  │
│  │   Signal    │    │   BrightData│    │   Poll for  │    │   Deal      │  │
│  │ Generation  │    │   Service   │    │ Completion  │    │   Update    │  │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘  │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Components

### 1. BrightData Service (`app/services/brightdata_fallback.py`)

**Purpose**: Handles BrightData API integration for LinkedIn profile scraping.

**Key Features**:
- Triggers LinkedIn scraping jobs
- Polls for job completion (up to 10 minutes)
- Downloads snapshot data
- Stores raw data to S3
- Creates structured BrightData models

**API Endpoints**:
- `POST /datasets/v3/trigger` - Start scraping job
- `GET /datasets/v3/progress/{snapshot_id}` - Check job status
- `GET /datasets/v3/snapshot/{snapshot_id}` - Download results

**Configuration**:
```python
BRIGHTDATA_API_KEY: str
BRIGHTDATA_BASE_URL: str = "https://api.brightdata.com"
BRIGHTDATA_RATE_LIMIT: int = 100
ENABLE_BRIGHTDATA_ENRICHMENT: bool = True
```

### 2. BrightData Processor (`app/tasks/brightdata_processor.py`)

**Purpose**: Transforms BrightData responses into normalized database records.

**Key Features**:
- Validates BrightData responses
- Cleans and normalizes data
- Extracts experiences, education, skills, and profiles
- Stores data using the same schema as PDL
- Generates deterministic UUIDs for consistency

**Data Flow**:
```
BrightData Response → Validation → Cleaning → Normalization → RDS Storage
```

### 3. Enhanced Founder Enrichment Service

**Purpose**: Orchestrates the complete enrichment workflow with fallback logic.

**Key Features**:
- PDL insufficiency detection
- Automatic BrightData fallback
- LinkedIn profile matching for deal updates
- Comprehensive error handling
- Audit trail maintenance

## Fallback Logic

### Trigger Conditions

BrightData fallback is triggered when:

1. **PDL returns no data**: `enrichment_data` is empty or None
2. **PDL data is insufficient**: Missing skills, experience, AND education

```python
def _is_pdl_data_insufficient(self, enrichment_data: Any) -> bool:
    has_skills = bool(enrichment_data.get("skills"))
    has_experience = bool(enrichment_data.get("experience"))
    has_education = bool(enrichment_data.get("education"))
    
    # If all three are missing, data is insufficient
    return not (has_skills or has_experience or has_education)
```

### Fallback Flow

1. **PDL Attempt**: Try PDL enrichment first
2. **Sufficiency Check**: Evaluate if PDL data is sufficient
3. **BrightData Trigger**: If insufficient, trigger BrightData scraping
4. **Polling**: Wait for BrightData job completion (max 10 minutes)
5. **Processing**: Transform and store BrightData results
6. **Signal Generation**: Generate AI signals from enriched data
7. **Deal Update**: Check for LinkedIn profile matches and trigger updates

## Data Models

### BrightDataFounderData

```python
class BrightDataFounderData(TractionXModel):
    # BrightData specific fields
    snapshot_id: str
    url: str
    
    # Profile data
    full_name: Optional[str]
    first_name: Optional[str]
    last_name: Optional[str]
    headline: Optional[str]
    summary: Optional[str]
    location: Optional[str]
    
    # Experience and education
    experience: List[Dict[str, Any]]
    education: List[Dict[str, Any]]
    skills: List[Dict[str, Any]]
    profiles: List[Dict[str, Any]]
    
    # Metadata
    profile_picture_url: Optional[str]
    connection_count: Optional[int]
    follower_count: Optional[int]
    scraped_at: Optional[datetime]
    confidence_score: Optional[float]
```

### Storage Schema

BrightData data is stored using the same normalized schema as PDL:

- `founders` - Main founder records
- `founder_experiences` - Work experience
- `founder_education` - Education history
- `founder_skills` - Skills and endorsements
- `founder_profiles` - Social profiles

## Configuration

### Environment Variables

Add to your `.env` file:

```bash
# BrightData Configuration
BRIGHTDATA_API_KEY=your_brightdata_api_key
BRIGHTDATA_BASE_URL=https://api.brightdata.com
BRIGHTDATA_RATE_LIMIT=100
ENABLE_BRIGHTDATA_ENRICHMENT=true
```

### Settings

Update `app/configs/settings.py`:

```python
# BrightData Configuration
BRIGHTDATA_API_KEY: Optional[str] = None
BRIGHTDATA_BASE_URL: str = "https://api.brightdata.com"
BRIGHTDATA_RATE_LIMIT: int = 100
ENABLE_BRIGHTDATA_ENRICHMENT: bool = True
```

## Usage

### Basic Enrichment with Fallback

```python
from app.tasks.founder_enrichment import enrich_founder_data

# Job data
job_data = {
    "job_id": "job_123",
    "founder_id": "founder_456",
    "company_id": "company.com",
    "org_id": "org_789",
    "founder_name": "John Doe",
    "founder_linkedin": "https://linkedin.com/in/johndoe"
}

# Process enrichment (automatically handles fallback)
result = await enrich_founder_data(job_data)

if result["success"]:
    print(f"Enrichment successful: {result['founder_uuid']}")
    if result.get("fallback_source") == "brightdata":
        print("Used BrightData fallback")
else:
    print(f"Enrichment failed: {result['error']}")
```

### Direct BrightData Usage

```python
from app.services.brightdata_fallback import get_brightdata_service

# Get service
service = await get_brightdata_service()

# Enrich profile
result = await service.enrich_founder_profile(
    founder_id="founder_123",
    linkedin_url="https://linkedin.com/in/johndoe",
    org_id="org_456"
)

if result["success"]:
    print(f"BrightData enrichment successful: {result['snapshot_id']}")
else:
    print(f"BrightData enrichment failed: {result['error']}")
```

## Error Handling

### BrightData Service Errors

- **API Key Missing**: Service initialization fails
- **Trigger Failure**: LinkedIn URL invalid or API error
- **Polling Timeout**: Job takes longer than 10 minutes
- **Download Failure**: Snapshot data unavailable
- **Empty Data**: No profile data returned

### Fallback Errors

- **Both PDL and BrightData Fail**: Creates minimal founder record
- **Processing Errors**: Stores errors in `founder_processing_errors` table
- **Storage Errors**: Logs errors and continues with minimal data

### Error Recovery

1. **Automatic Retries**: Built into the job queue system
2. **Minimal Records**: Creates basic founder records when enrichment fails
3. **Error Logging**: Comprehensive error tracking and debugging
4. **Graceful Degradation**: System continues operating even with failures

## Monitoring and Observability

### Logging

All operations are logged with structured logging:

```python
logger.info(
    "🔄 Attempting BrightData fallback",
    founder_name=job_context.founder_name,
    linkedin_url=job_context.linkedin_url,
)
```

### Metrics

Key metrics to monitor:

- **Fallback Rate**: Percentage of PDL failures that trigger BrightData
- **BrightData Success Rate**: Percentage of successful BrightData enrichments
- **Processing Time**: Time from trigger to completion
- **Error Rates**: Different types of failures

### S3 Storage

Raw data is stored in S3 for audit trails:

```
s3://founder_raw/brightdata_snapshots/{org_id}/{founder_id}/{snapshot_id}_{timestamp}.json
```

## Testing

### Run Integration Tests

```bash
cd datapipelines
python test_brightdata_integration.py
```

### Test Coverage

- Service initialization
- API integration
- Data processing
- Fallback logic
- Error handling

## Deployment

### Prerequisites

1. **BrightData Account**: Active account with LinkedIn scraping access
2. **API Key**: Valid BrightData API key
3. **Dataset ID**: LinkedIn scraping dataset configured
4. **Environment Variables**: All required config set

### Deployment Steps

1. **Update Environment**: Add BrightData configuration
2. **Deploy Code**: Deploy updated services
3. **Test Integration**: Run integration tests
4. **Monitor**: Watch logs and metrics
5. **Scale**: Adjust rate limits as needed

## Troubleshooting

### Common Issues

1. **API Key Errors**
   - Verify `BRIGHTDATA_API_KEY` is set correctly
   - Check API key permissions and quotas

2. **Timeout Errors**
   - Increase `max_poll_time` if needed
   - Check LinkedIn URL validity

3. **Empty Data**
   - Verify LinkedIn profile is public
   - Check BrightData dataset configuration

4. **Processing Errors**
   - Check database connectivity
   - Verify S3 permissions

### Debug Commands

```bash
# Check configuration
python -c "from app.configs import settings; print(settings.BRIGHTDATA_API_KEY)"

# Test service
python test_brightdata_integration.py

# Check logs
tail -f logs/datapipelines.log | grep brightdata
```

## Future Enhancements

### Planned Features

1. **Rate Limiting**: Implement intelligent rate limiting
2. **Caching**: Cache successful enrichments
3. **Batch Processing**: Process multiple profiles efficiently
4. **Advanced Matching**: Improve company domain matching
5. **Real-time Updates**: Webhook-based deal updates

### Performance Optimizations

1. **Connection Pooling**: Reuse HTTP connections
2. **Async Processing**: Parallel job processing
3. **Data Compression**: Compress S3 storage
4. **Indexing**: Optimize database queries

## Support

For issues or questions:

1. **Check Logs**: Review structured logs for errors
2. **Run Tests**: Execute integration tests
3. **Verify Config**: Confirm environment variables
4. **Contact Team**: Reach out to data engineering team

---

**Last Updated**: December 2024  
**Version**: 1.0.0  
**Maintainer**: Data Engineering Team 