"use client"

import { useState, useCallback } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronLeft, ChevronRight } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"

// Step Components
import { WelcomeStep } from "./steps/welcome-step"
import { ThesisStep } from "./steps/thesis-step"
import { TeamMembersStep } from "./steps/team-members-step"
import { WorkflowsOverviewStep } from "./steps/workflows-overview-step"
import { CompletionStep } from "./steps/completion-step"

interface OnboardingWizardProps {
  user: any
  onComplete: () => void
}

interface StepData {
  thesis?: any
  teamMembers?: any[]
  [key: string]: any
}

const STEPS = [
  {
    id: 1,
    title: "Welcome to TractionX",
    description: "Your decision support system is live",
    component: WelcomeStep
  },
  {
    id: 2,
    title: "Set Org Thesis",
    description: "Define your investment preferences",
    component: ThesisStep
  },
  {
    id: 3,
    title: "Add Team Members",
    description: "Invite your team to collaborate",
    component: TeamMembersStep
  },
  {
    id: 4,
    title: "Key Workflows Overview",
    description: "Learn how TractionX works",
    component: WorkflowsOverviewStep
  },
  {
    id: 5,
    title: "You're All Set",
    description: "Your decision layer is ready",
    component: CompletionStep
  }
]

export function OnboardingWizard({ user, onComplete }: OnboardingWizardProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [stepData, setStepData] = useState<StepData>({})
  const [isLoading, setIsLoading] = useState(false)

  const progress = (currentStep / STEPS.length) * 100
  const currentStepConfig = STEPS.find(step => step.id === currentStep)

  const handleNext = useCallback(async (data?: any) => {
    // Track step completion
    try {
      console.log(`Onboarding: Step ${currentStep} completed`, {
        step: currentStep,
        stepTitle: currentStepConfig?.title,
        data: data ? Object.keys(data) : [],
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error("Failed to track step completion:", error)
    }

    if (data) {
      setStepData(prev => ({ ...prev, [`step${currentStep}`]: data }))
    }

    if (currentStep < STEPS.length) {
      setCurrentStep(prev => prev + 1)
    } else {
      // Final step - complete onboarding
      try {
        console.log("Onboarding: Flow completed", {
          totalSteps: STEPS.length,
          completedAt: new Date().toISOString(),
          stepData: Object.keys(stepData)
        })
      } catch (error) {
        console.error("Failed to track onboarding completion:", error)
      }
      onComplete()
    }
  }, [currentStep, onComplete, currentStepConfig, stepData])

  const handleBack = useCallback(() => {
    if (currentStep > 1) {
      // Track back navigation
      try {
        console.log(`Onboarding: Step ${currentStep} - back navigation`, {
          fromStep: currentStep,
          toStep: currentStep - 1,
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        console.error("Failed to track back navigation:", error)
      }
      setCurrentStep(prev => prev - 1)
    }
  }, [currentStep])

  const handleSkip = useCallback(() => {
    // Track skip action
    try {
      console.log(`Onboarding: Step ${currentStep} - skipped`, {
        step: currentStep,
        stepTitle: currentStepConfig?.title,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error("Failed to track skip action:", error)
    }
    handleNext()
  }, [handleNext, currentStep, currentStepConfig])

  if (!currentStepConfig) {
    return null
  }

  const StepComponent = currentStepConfig.component

  return (
    <div className="mx-auto w-full max-w-4xl px-4 sm:px-6 lg:px-8">
      {/* Progress Bar */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6 sm:mb-8"
      >
        <div className="mb-4 flex items-center justify-between">
          <div className="text-xs sm:text-sm text-gray-500">
            Step {currentStep} of {STEPS.length}
          </div>
          <div className="text-xs sm:text-sm text-gray-500">
            {Math.round(progress)}% Complete
          </div>
        </div>
        <Progress value={progress} className="h-2" />
      </motion.div>

      {/* Step Content */}
      <motion.div
        className={cn(
          "bg-white/70 backdrop-blur-xl rounded-2xl shadow-md",
          "w-full max-w-3xl mx-auto",
          "min-h-[500px] sm:min-h-[600px] flex flex-col",
          "overflow-hidden"
        )}
        layout
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="flex-1 flex flex-col"
          >
            <StepComponent
              user={user}
              stepData={stepData}
              onNext={handleNext}
              onBack={handleBack}
              onSkip={handleSkip}
              isLoading={isLoading}
              setIsLoading={setIsLoading}
              currentStep={currentStep}
              totalSteps={STEPS.length}
            />
          </motion.div>
        </AnimatePresence>

        {/* Navigation Footer */}
        {currentStep < STEPS.length && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex items-center justify-between p-4 sm:p-6 lg:p-8 border-t border-gray-100"
          >
            <Button
              variant="ghost"
              onClick={handleBack}
              disabled={currentStep === 1}
              className="flex items-center gap-2 text-sm sm:text-base"
              size={currentStep === 1 ? "sm" : "default"}
            >
              <ChevronLeft className="size-4" />
              <span className="hidden sm:inline">Back</span>
            </Button>

            <div className="flex items-center gap-1 sm:gap-2">
              {/* Step indicators */}
              {STEPS.map((step) => (
                <div
                  key={step.id}
                  className={cn(
                    "size-2 rounded-full transition-all duration-300",
                    step.id === currentStep
                      ? "bg-purple-600 w-6 sm:w-8"
                      : step.id < currentStep
                        ? "bg-purple-400"
                        : "bg-gray-200"
                  )}
                />
              ))}
            </div>

            <div className="w-[60px] sm:w-[72px]" /> {/* Spacer to center indicators */}
          </motion.div>
        )}
      </motion.div>
    </div>
  )
}
