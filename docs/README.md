# Form and Thesis System Documentation

## Introduction

This documentation provides comprehensive information about the Form and Thesis systems, including API usage, technical implementation details, and frontend integration guidelines. These systems allow you to create dynamic forms, configure investment theses, and calculate compatibility scores between startups and investment criteria.

## Documentation Structure

- **[Integration Guide](integration_guide.md)** - Start here for a comprehensive overview of the entire system
- **[Form and Thesis Integration Guide](form_thesis_integration.md)** - Basic API usage with curl examples
- **[Form and Thesis Technical Implementation](form_thesis_technical.md)** - Technical details and data models
- **[Form Sharing Guide](form_sharing_guide.md)** - Sharing forms via links, embeds, and QR codes
- **[Form Engine Examples](form_engine_examples.md)** - Detailed examples of using the Form Engine API

## Key Features

### Form System

- Multi-section forms with various question types
- Conditional visibility logic
- Repeatable sections
- Sequential or full-form rendering modes
- Form sharing via links, embeds, and QR codes

### Thesis Management System

- Investment thesis configuration
- Scoring rules for form responses
- Match criteria for filtering startups
- Compatibility score calculation
- Thesis matching for form submissions

## Getting Started

1. Review the [Integration Guide](integration_guide.md) for a high-level overview
2. Explore the [Form and Thesis Integration Guide](form_thesis_integration.md) for basic API usage
3. Refer to the [Technical Implementation](form_thesis_technical.md) for data models and architecture
4. Use the [Form Engine Examples](form_engine_examples.md) for detailed implementation examples

## API Endpoints

The system provides the following main API endpoints:

### Form System

- `/api/v1/forms` - Create and manage forms
- `/api/v1/forms/{form_id}/sections` - Manage form sections
- `/api/v1/forms/sections/{section_id}/questions` - Manage questions
- `/api/v1/forms/{form_id}/engine` - Use the Form Engine API
- `/api/v1/forms/{form_id}/submit` - Submit form responses

### Thesis System

- `/api/v1/scoring/thesis` - Create and manage theses
- `/api/v1/scoring/thesis/{thesis_id}/calculate-score` - Calculate scores
- `/api/v1/scoring/thesis/find-matching` - Find matching theses
- `/api/v1/scoring/thesis/preview/{form_id}` - Preview thesis configuration

### Sharing System

- `/api/v1/sharing` - Create sharing configurations
- `/api/v1/sharing/{config_id}/link` - Generate sharing links
- `/api/v1/sharing/{config_id}/embed` - Generate embed codes
- `/api/v1/sharing/{config_id}/qr` - Generate QR codes

## Authentication

All API requests require authentication using JWT tokens and organization context:

```bash
# Headers required for all API calls
-H "Authorization: Bearer YOUR_JWT_TOKEN" 
-H "X-ORG-ID: YOUR_ORGANIZATION_ID"
```

## Example Workflow

1. Create a form with sections and questions
2. Configure repeatable sections and visibility conditions
3. Create an investment thesis linked to the form
4. Configure scoring and match rules
5. Share the form with startups
6. Collect form submissions
7. Calculate scores and find matching theses
8. Visualize results

## Best Practices

- Implement robust error handling for all API calls
- Cache form structures to improve performance
- Use progressive loading for form sections
- Implement client-side validation matching server rules
- Ensure forms are accessible to all users
- Design responsive interfaces for all screen sizes

## Need Help?

If you have any questions or need assistance, please contact the development team or refer to the API documentation available at:

- Swagger UI: `http://localhost:8000/docs`
- OpenAPI JSON: `http://localhost:8000/openapi.json`
