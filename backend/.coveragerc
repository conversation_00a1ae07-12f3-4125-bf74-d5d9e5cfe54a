[run]
source = app
omit =
    app/worker.py
    app/main.py
    app/core/logging.py
    app/core/errors.py
    app/core/cache.py
    app/services/queue/*
    app/services/email/*
    app/services/cache/*
    app/services/audit/*
    app/services/trigger/*
    app/services/sharing/*
    app/middleware/*
    app/dependencies/*
    app/utils/common.py
    app/utils/model/helper.py
    app/utils/rbac/rbac.py
    app/utils/rbac/decorators.py
    app/utils/rbac/permissions.py
    app/utils/rbac/registry.py
    app/utils/rbac/scanner.py
    app/utils/rbac/types.py
    app/utils/rbac/utils.py
    app/utils/rbac/__init__.py
    app/utils/__init__.py
    app/models/__init__.py
    app/schemas/__init__.py
    app/api/__init__.py
    app/api/v1/__init__.py
    app/api/base.py
    app/core/__init__.py
    app/services/__init__.py
    app/services/base.py
    app/services/factory.py
    app/services/interfaces.py
    app/services/token/__init__.py
    app/services/auth/__init__.py
    app/services/user/__init__.py
    app/services/role/__init__.py
    app/services/form/__init__.py
    app/services/thesis/__init__.py
    app/services/qualifier_form/__init__.py
    app/services/rbac/__init__.py
    app/services/organization/__init__.py
    app/services/submission/__init__.py
    app/services/section/__init__.py
    app/services/question/__init__.py
    app/services/cache/__init__.py
    app/services/audit/__init__.py
    app/services/trigger/__init__.py
    app/services/sharing/__init__.py
    app/services/queue/__init__.py
    app/services/email/__init__.py
    app/services/queue/handlers/__init__.py
    app/services/queue/handlers/*.py
    app/services/queue/tasks/__init__.py
    app/services/queue/tasks/*.py
    app/services/queue/worker.py
    app/services/queue/registry.py
    app/services/queue/interfaces.py
    app/services/queue/base.py
    app/services/queue/redis.py
    app/services/queue/mongo.py
    app/services/queue/memory.py
    app/services/queue/factory.py
    app/services/queue/exceptions.py
    app/services/queue/decorators.py
    app/services/queue/constants.py
    app/services/queue/config.py
    app/services/queue/utils.py
    app/services/queue/models.py
    app/services/queue/schemas.py
    app/services/queue/types.py
    app/services/queue/enums.py
    app/services/queue/events.py
    app/services/queue/handlers.py
    app/services/queue/tasks.py
    app/services/queue/worker.py
    app/services/queue/registry.py
    app/services/queue/interfaces.py
    app/services/queue/base.py
    app/services/queue/redis.py
    app/services/queue/mongo.py
    app/services/queue/memory.py
    app/services/queue/factory.py
    app/services/queue/exceptions.py
    app/services/queue/decorators.py
    app/services/queue/constants.py
    app/services/queue/config.py
    app/services/queue/utils.py
    app/services/queue/models.py
    app/services/queue/schemas.py
    app/services/queue/types.py
    app/services/queue/enums.py
    app/services/queue/events.py
    app/services/queue/handlers.py
    app/services/queue/tasks.py

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise NotImplementedError
    if __name__ == .__main__.:
    pass
    raise ImportError
    except ImportError
    raise Exception
    except Exception
    def __str__
    def __unicode__
    def __bytes__
    def __format__
    def __hash__
    def __eq__
    def __ne__
    def __lt__
    def __le__
    def __gt__
    def __ge__
    def __reduce__
    def __reduce_ex__
    def __getstate__
    def __setstate__
    def __getnewargs__
    def __getnewargs_ex__
    def __getattr__
    def __setattr__
    def __delattr__
    def __dir__
    def __init_subclass__
    def __new__
    def __prepare__
    def __instancecheck__
    def __subclasscheck__
    def __class_getitem__
    def __call__
    def __get__
    def __set__
    def __delete__
    def __set_name__
    def __enter__
    def __exit__
    def __await__
    def __aiter__
    def __anext__
    def __aenter__
    def __aexit__
    def __contains__
    def __getitem__
    def __setitem__
    def __delitem__
    def __len__
    def __reversed__
    def __iter__
    def __next__
    def __missing__
    def __copy__
    def __deepcopy__
    def __bool__
    def __complex__
    def __int__
    def __float__
    def __index__
    def __round__
    def __trunc__
    def __floor__
    def __ceil__
    def __pos__
    def __neg__
    def __abs__
    def __invert__
    def __add__
    def __sub__
    def __mul__
    def __matmul__
    def __truediv__
    def __floordiv__
    def __mod__
    def __divmod__
    def __pow__
    def __lshift__
    def __rshift__
    def __and__
    def __xor__
    def __or__
    def __radd__
    def __rsub__
    def __rmul__
    def __rmatmul__
    def __rtruediv__
    def __rfloordiv__
    def __rmod__
    def __rdivmod__
    def __rpow__
    def __rlshift__
    def __rrshift__
    def __rand__
    def __rxor__
    def __ror__
    def __iadd__
    def __isub__
    def __imul__
    def __imatmul__
    def __itruediv__
    def __ifloordiv__
    def __imod__
    def __ipow__
    def __ilshift__
    def __irshift__
    def __iand__
    def __ixor__
    def __ior__
    def __getstate__
    def __setstate__
    def __reduce__
    def __reduce_ex__
    def __getattr__
    def __setattr__
    def __delattr__
    def __dir__
    def __init_subclass__
    def __new__
    def __prepare__
    def __instancecheck__
    def __subclasscheck__
    def __class_getitem__
    def __call__
    def __get__
    def __set__
    def __delete__
    def __set_name__
    def __enter__
    def __exit__
    def __await__
    def __aiter__
    def __anext__
    def __aenter__
    def __aexit__
    def __contains__
    def __getitem__
    def __setitem__
    def __delitem__
    def __len__
    def __reversed__
    def __iter__
    def __next__
    def __missing__
    def __copy__
    def __deepcopy__
    def __bool__
    def __complex__
    def __int__
    def __float__
    def __index__
    def __round__
    def __trunc__
    def __floor__
    def __ceil__
    def __pos__
    def __neg__
    def __abs__
    def __invert__
    def __add__
    def __sub__
    def __mul__
    def __matmul__
    def __truediv__
    def __floordiv__
    def __mod__
    def __divmod__
    def __pow__
    def __lshift__
    def __rshift__
    def __and__
    def __xor__
    def __or__
    def __radd__
    def __rsub__
    def __rmul__
    def __rmatmul__
    def __rtruediv__
    def __rfloordiv__
    def __rmod__
    def __rdivmod__
    def __rpow__
    def __rlshift__
    def __rrshift__
    def __rand__
    def __rxor__
    def __ror__
    def __iadd__
    def __isub__
    def __imul__
    def __imatmul__
    def __itruediv__
    def __ifloordiv__
    def __imod__
    def __ipow__
    def __ilshift__
    def __irshift__
    def __iand__
    def __ixor__
    def __ior__

[html]
directory = htmlcov
