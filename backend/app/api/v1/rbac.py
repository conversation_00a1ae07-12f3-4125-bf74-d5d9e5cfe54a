from fastapi import Depends
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.database import get_database
from app.dependencies.auth import get_current_user
from app.models.user import User
from app.services.factory import get_rbac_service
from app.services.rbac.mongo import RBACService
from app.api.base import BaseAPIRouter
from app.utils.rbac.rbac import rbac_register

router = BaseAPIRouter(prefix="/rbac", tags=["rbac"])


@router.get("/resources")
@rbac_register(resource="rbac", action="view", group="RBAC", description="View resources")
async def list_resources(
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
    rbac_service: RBACService = Depends(get_rbac_service)
):
    """List all available resources in the system."""
    resources = await rbac_service._resources
    return resources


@router.get("/actions")
@rbac_register(resource="rbac", action="view", group="RBAC", description="View actions")
async def list_actions(
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
    rbac_service: RBACService = Depends(get_rbac_service)
):
    """List all available actions in the system."""
    actions = await rbac_service._actions
    return actions


@router.get("/permissions")
@rbac_register(resource="rbac", action="view", group="RBAC", description="View permissions")
async def list_permissions(
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """List all permissions for the current user."""
    rbac_service = RBACService(db)
    permissions = await rbac_service.get_user_permissions(str(current_user.id), current_user.org_id)
    return permissions


@router.get("/roles")
@rbac_register(resource="rbac", action="view", group="RBAC", description="View roles")
async def list_roles(
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """List all roles in the system."""
    rbac_service = RBACService(db)
    roles = await rbac_service.get_all_roles(current_user.org_id)
    return roles


@router.get("/role/{role_id}/permissions")
@rbac_register(resource="rbac", action="view", group="RBAC", description="View role permissions")
async def get_role_permissions(
    role_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Get permissions for a specific role."""
    rbac_service = RBACService(db)
    permissions = await rbac_service.get_role_permissions(role_id, current_user.org_id)
    return permissions
