"use client"

import { motion } from 'framer-motion';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  RadialBarChart,
  RadialBar,
  AreaChart,
  Area
} from 'recharts';
import { cn } from '@/lib/utils';
import { DealInsights } from '@/lib/api/dashboard-api';
import { Button } from '@/components/ui/button';
import { 
  Filter, 
  TrendingUp, 
  Users, 
  Activity, 
  Eye, 
  Plus, 
  ArrowRight,
  Radio,
  Clock,
  Zap
} from 'lucide-react';
import { format } from 'date-fns';
import React from 'react'; // Added for React.Fragment
import { Tooltip as TooltipRoot, TooltipTrigger, TooltipContent, TooltipProvider } from '@/components/ui/tooltip';

interface PremiumInsightsChartsProps {
  insightsData: DealInsights;
  className?: string;
}

// Institutional-grade color palette
const COLORS = {
  primary: '#3FE0C5', // Mint green
  secondary: '#8389FD', // Lavender-indigo
  background: '#FAFAFA',
  surface: '#FFFFFF',
  text: '#1A1A1A',
  muted: '#6B7280',
  grid: 'rgba(0, 0, 0, 0.03)',
  axis: '#9CA3AF',
};

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
};

const cardVariants = {
  hidden: { 
    opacity: 0, 
    y: 10,
    scale: 0.98
  },
  visible: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.4,
      ease: [0.25, 0.46, 0.45, 0.94] as const
    }
  }
};

// Elegant custom tooltip for Deal Activity
const DealActivityTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="rounded-lg bg-white/95 shadow-lg px-4 py-2 border border-gray-100 text-left transition-opacity duration-150">
        <div className="font-semibold text-sm text-black">{format(new Date(label), 'PPP')}</div>
        <div className="text-xs text-muted-foreground mt-1">{payload[0].value} deals created</div>
      </div>
    );
  }
  return null;
};

export function PremiumInsightsCharts({
  insightsData,
  className
}: PremiumInsightsChartsProps) {

  // Guard against undefined insightsData
  if (!insightsData) {
    return null;
  }

  // Refined funnel palette: main pipeline = indigo-500 to-blue-500, drop-off = gray
  const FUNNEL_STAGES = [
    { key: 'new', label: 'New' },
    { key: 'triage', label: 'Triage' },
    { key: 'reviewed', label: 'Reviewed' },
    { key: 'approved', label: 'Approved' },
    { key: 'negotiating', label: 'Negotiating' },
    { key: 'closed', label: 'Closed' },
    { key: 'excluded', label: 'Excluded' },
    { key: 'rejected', label: 'Rejected' },
  ];
  const isDropoff = (key: string) => key === 'excluded' || key === 'rejected';
  const funnelCounts = FUNNEL_STAGES.map(stage => {
    const found = insightsData.deal_funnel_breakdown.find(s => s.status === stage.key);
    return { ...stage, count: found ? found.count : 0 };
  });
  const funnelTotal = funnelCounts.reduce((sum, s) => sum + s.count, 0);

  // 2. Deal Activity Timeline - Thin line chart with glow
  const timelineData = insightsData.deal_activity_timeline.slice(-14);

  // 3. Sector Focus - Horizontal bars with fading trail
  const sectorData = Object.entries(insightsData.sector_stage_matrix)
    .map(([sector, stages]) => ({
      sector,
      total: Object.values(stages).reduce((sum, count) => sum + count, 0)
    }))
    .sort((a, b) => b.total - a.total)
    .slice(0, 5);

  // 4. Team Velocity - Minimalist list with action buttons
  const velocityData = Object.entries(insightsData.deal_velocity_by_user)
    .map(([userId, userData]) => ({
      userId,
      name: userData.name,
      totalTransitions: Object.values(userData.status_transitions).reduce((sum, count) => sum + count, 0)
    }))
    .sort((a, b) => b.totalTransitions - a.totalTransitions)
    .slice(0, 5);

  // 5. Exclusion Filters - Clean list with expand action
  const exclusionData = Object.entries(insightsData.exclusion_filters_triggered)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5);

  const totalDeals = insightsData.deal_funnel_breakdown.reduce((sum, item) => sum + item.count, 0);

  // Layout: 2x2 grid for all charts
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full">
      {/* Deal Funnel */}
      <Card className="h-full min-h-[320px] flex flex-col justify-between">
        {/* Accent gradient top bar */}
        <div className="h-[3px] w-full bg-gradient-to-r from-[#6366F1] via-[#4F46E5] to-[#06B6D4] transition-all duration-300 group-hover:from-[#4F46E5] group-hover:to-[#0EA5E9]" />
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg font-semibold text-text flex items-center gap-2">
                <Filter className="w-4 h-4 text-primary" />
                Deal Funnel
              </CardTitle>
              <p className="text-sm text-muted mt-1">Pipeline health overview</p>
            </div>
            <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
              <Eye className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="flex-1 w-full py-6">
          <div className="space-y-5">
            {funnelCounts.map((stage, idx) => {
              const percent = funnelTotal > 0 ? (stage.count / funnelTotal) * 100 : 0;
              const barGradient = isDropoff(stage.key)
                ? 'from-gray-300 to-gray-400'
                : 'from-indigo-500 to-blue-500';
              return (
                <div key={stage.key} className="w-full">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-semibold text-text">{stage.label}</span>
                    <span className="flex items-center gap-2">
                      <span className="text-xs font-medium text-muted-foreground">{stage.count}</span>
                      <span className="text-base font-bold text-muted-foreground">{percent.toFixed(1)}%</span>
                    </span>
                  </div>
                  <div
                    className={cn(
                      "h-4 rounded bg-gradient-to-r transition-shadow duration-200",
                      barGradient,
                      stage.count === 0 ? 'opacity-30' : '',
                      "hover:shadow-lg hover:scale-[1.01]"
                    )}
                    style={{ width: '100%' }}
                  />
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
      {/* Deal Activity (Velocity) */}
      <Card className="h-full min-h-[320px] flex flex-col justify-between">
        {/* Accent gradient top bar */}
        <div className="h-[3px] w-full bg-gradient-to-r from-[#6366F1] via-[#4F46E5] to-[#06B6D4] transition-all duration-300 group-hover:from-[#4F46E5] group-hover:to-[#0EA5E9]" />
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg font-semibold text-text flex items-center gap-2">
                <Activity className="w-4 h-4 text-secondary" />
                Deal Activity
              </CardTitle>
              <p className="text-sm text-muted mt-1">Daily creation velocity</p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex-1 w-full flex flex-col p-0">
          <div className="w-full min-w-0 flex-1 h-full">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={timelineData} margin={{ top: 10, right: 10, left: 10, bottom: 20 }}>
                <defs>
                  <linearGradient id="dealVelocity" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor="#7b61ff" stopOpacity={0.4} />
                    <stop offset="100%" stopColor="#7b61ff" stopOpacity={0} />
                  </linearGradient>
                </defs>
                <XAxis
                  dataKey="date"
                  tickFormatter={(d) => format(new Date(d), "MMM d")}
                  tick={{ fontSize: 12, fill: "#999", fontFamily: 'Menlo, monospace' }}
                  axisLine={false}
                  tickLine={false}
                  interval={0}
                  padding={{ left: 20, right: 20 }}
                  allowDataOverflow={true}
                />
                <YAxis
                  tick={{ fontSize: 12, fill: "#999" }}
                  axisLine={false}
                  tickLine={false}
                />
                <Tooltip
                  content={<DealActivityTooltip />}
                />
                <Area
                  type="monotone"
                  dataKey="count"
                  stroke="#7b61ff"
                  strokeWidth={2}
                  fill="url(#dealVelocity)"
                  dot={{ stroke: "#7b61ff", strokeWidth: 2, fill: "#fff", r: 3 }}
                  activeDot={{ r: 5, style: { filter: 'drop-shadow(0 0 6px #7b61ff66)' } }}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
      {/* Sector Focus */}
      <Card className="h-full min-h-[320px] flex flex-col justify-between">
        {/* Accent gradient top bar */}
        <div className="h-[3px] w-full bg-gradient-to-r from-[#6366F1] via-[#4F46E5] to-[#06B6D4] transition-all duration-300 group-hover:from-[#4F46E5] group-hover:to-[#0EA5E9]" />
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg font-semibold text-text flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-primary" />
                Sector Focus
              </CardTitle>
              <p className="text-sm text-muted mt-1">Top sectors by deal count</p>
            </div>
            <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
              <ArrowRight className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="flex-1 w-full">
          <div className="w-full min-w-0 h-[300px] flex flex-col justify-center">
            {Object.keys(insightsData.sector_stage_matrix).length > 0 ? (
              (() => {
                // Canonical stage order
                const STAGE_ORDER = [
                  "Pre-Seed",
                  "Seed",
                  "Series A",
                  "Series B",
                  "Series C+",
                  "Growth"
                ];
                // Top 5 sectors by total
                const sectorTotals = Object.entries(insightsData.sector_stage_matrix)
                  .map(([sector, stages]) => ({
                    sector,
                    total: Object.values(stages).reduce((sum, count) => sum + count, 0),
                    stages
                  }))
                  .sort((a, b) => b.total - a.total)
                  .slice(0, 5);
                // Column totals
                const stageTotals = STAGE_ORDER.map(stage =>
                  sectorTotals.reduce((sum, s) => sum + (s.stages[stage] || 0), 0)
                );
                const grandTotal = sectorTotals.reduce((sum, s) => sum + s.total, 0);
                // Pill color logic
                const pillClass = (count: number) => {
                  if (count >= 4) return "bg-blue-500 text-white";
                  if (count >= 2) return "bg-blue-300 text-white";
                  if (count === 1) return "bg-blue-100 text-blue-800";
                  return "bg-muted text-muted-foreground";
                };
                // Utility to capitalize sector names
                const capitalize = (str: string) => str.toUpperCase();
                return (
                  <div className="w-full h-full">
                    <div className="hidden md:block overflow-x-auto rounded-xl">
                      <table className="w-full text-sm table-fixed border-separate border-spacing-1 tracking-tight bg-transparent">
                        <thead className="text-muted-foreground font-normal">
                          <tr>
                            <th className="text-left font-medium text-muted-foreground">Sector</th>
                            {STAGE_ORDER.map(stage => (
                              <th key={stage} className="text-center font-normal text-muted-foreground">{stage}</th>
                            ))}
                            <th className="text-left font-medium text-muted-foreground">Total</th>
                          </tr>
                        </thead>
                        <tbody>
                          {sectorTotals.map(sector => (
                            <tr key={sector.sector}>
                              <td className="font-semibold text-text whitespace-nowrap pr-2 min-w-[140px]">{capitalize(sector.sector)}</td>
                              {STAGE_ORDER.map(stage => {
                                const count = sector.stages[stage] || 0;
                                return (
                                  <td key={stage} className="text-center">
                                    <TooltipRoot>
                                      <TooltipTrigger asChild>
                                        <div className={`inline-block px-3 py-1 rounded-md font-semibold transition-colors duration-150 ${pillClass(count)}`}
                                          style={{ minWidth: 32 }}>
                                          {count > 0 ? count : "-"}
                                        </div>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        {count > 0 ? `${count} deal${count > 1 ? 's' : ''} in ${stage} stage` : `No deals in ${stage}`}
                                      </TooltipContent>
                                    </TooltipRoot>
                                  </td>
                                );
                              })}
                              <td className="text-left font-bold text-text pl-2">{sector.total}</td>
                            </tr>
                          ))}
                        </tbody>
                        <tfoot>
                          <tr>
                            <td className="text-left font-medium text-muted-foreground pr-2">Total</td>
                            {stageTotals.map((total, idx) => (
                              <td key={STAGE_ORDER[idx]} className="text-center font-medium text-muted-foreground">{total > 0 ? total : "-"}</td>
                            ))}
                            <td className="text-left font-bold text-text pl-2">{grandTotal}</td>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                    {/* Mobile fallback: stacked by sector */}
                    <div className="block md:hidden space-y-4">
                      {sectorTotals.map(sector => (
                        <div key={sector.sector} className="rounded-xl bg-muted/30 p-3">
                          <div className="font-semibold text-text mb-1">{sector.sector}</div>
                          <div className="flex flex-wrap gap-2 mb-2">
                            {STAGE_ORDER.map(stage => {
                              const count = sector.stages[stage] || 0;
                              return count > 0 ? (
                                <span key={stage}>
                                  <TooltipRoot>
                                    <TooltipTrigger asChild>
                                      <span className={`inline-block px-3 py-1 rounded-md font-semibold ${pillClass(count)}`}>{stage}: {count}</span>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      {`${count} deal${count > 1 ? 's' : ''} in ${stage} stage`}
                                    </TooltipContent>
                                  </TooltipRoot>
                                </span>
                              ) : null;
                            })}
                          </div>
                          <div className="text-right font-bold text-text">Total: {sector.total}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })()
            ) : (
              <div className="flex h-full items-center justify-center">
                <div className="text-center">
                  <p className="text-sm text-muted">No sector data yet</p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      {/* Team Velocity */}
      <Card className="h-full min-h-[320px] flex flex-col justify-between">
        {/* Accent gradient top bar */}
        <div className="h-[3px] w-full bg-gradient-to-r from-[#6366F1] via-[#4F46E5] to-[#06B6D4] transition-all duration-300 group-hover:from-[#4F46E5] group-hover:to-[#0EA5E9]" />
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg font-semibold text-text flex items-center gap-2">
                <Users className="w-4 h-4 text-secondary" />
                Deal Ownership
              </CardTitle>
              <p className="text-sm text-muted mt-1">Deal actions taken by team</p>
            </div>
            <div className="flex items-center gap-2">
              <span className="inline-flex items-center gap-1 text-xs font-semibold text-green-600 bg-green-100 rounded-full px-2 py-0.5">
                <span className="w-2 h-2 rounded-full bg-green-500 inline-block" />
                {Object.keys(insightsData.deal_velocity_by_user).length} Active Users
              </span>
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex-1 w-full">
          <div className="w-full min-w-0 h-[300px] flex flex-col justify-center">
            {Object.keys(insightsData.deal_velocity_by_user).length > 0 ? (
              (() => {
                // Funnel color map (match deal funnel)
                const STAGE_COLORS: Record<string, string> = {
                  new: "from-indigo-400 to-blue-400",
                  triage: "from-blue-400 to-cyan-400",
                  reviewed: "from-purple-400 to-indigo-400",
                  approved: "from-green-400 to-emerald-400",
                  negotiating: "from-yellow-400 to-orange-400",
                  closed: "from-gray-400 to-gray-500",
                  excluded: "from-gray-300 to-gray-400",
                  rejected: "from-red-400 to-pink-400"
                };
                // Canonical order for transitions
                const FUNNEL_ORDER = [
                  "new",
                  "triage",
                  "reviewed",
                  "approved",
                  "negotiating",
                  "closed",
                  "excluded",
                  "rejected"
                ];
                // Parse users
                const users = Object.entries(insightsData.deal_velocity_by_user).map(([userId, userData]) => {
                  const transitions = Object.entries(userData.status_transitions || {});
                  const total = transitions.reduce((sum, [, count]) => sum + count, 0);
                  // Map transitions to canonical order
                  const orderedTransitions = FUNNEL_ORDER.map(stage => {
                    const key = Object.keys(userData.status_transitions || {}).find(k => k.replace('→', '') === stage);
                    return {
                      to: stage,
                      count: key ? userData.status_transitions[key] : 0
                    };
                  });
                  return {
                    name: userData.name || userId,
                    total,
                    transitions: orderedTransitions
                  };
                }).sort((a, b) => b.total - a.total);
                // Avatar gradient (deterministic by name)
                const avatarGradient = (name: string) => {
                  // Pick from a set of gradients based on hash of name
                  const gradients = [
                    "from-[#6366F1] to-[#D946EF]",
                    "from-[#06B6D4] to-[#6366F1]",
                    "from-[#F472B6] to-[#6366F1]",
                    "from-[#F59E42] to-[#6366F1]",
                    "from-[#6366F1] to-[#06B6D4]"
                  ];
                  let hash = 0;
                  for (let i = 0; i < name.length; i++) hash = name.charCodeAt(i) + ((hash << 5) - hash);
                  return gradients[Math.abs(hash) % gradients.length];
                };
                // User initials
                const getInitials = (name: string) => name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
                return (
                  <div className="space-y-5">
                    {users.map((user, idx) => (
                      <div key={user.name} className={idx > 0 ? "pt-2 border-t border-muted/40" : ""}>
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center gap-3">
                            <div className={`w-7 h-7 rounded-full bg-gradient-to-br ${avatarGradient(user.name)} flex items-center justify-center text-white font-bold text-sm shrink-0`}>
                              {getInitials(user.name)}
                            </div>
                            <span className="text-sm font-medium text-text">{user.name}</span>
                          </div>
                          <span className="text-xs text-muted-foreground font-semibold">{user.total} transitions</span>
                        </div>
                        <div className="flex h-3 rounded overflow-hidden">
                          {user.transitions.map((t, i) => t.count > 0 && (
                            <div
                              key={t.to}
                              className={`flex-1 h-full bg-gradient-to-r ${STAGE_COLORS[t.to] || 'from-gray-200 to-gray-300'} relative group`}
                              style={{ minWidth: 0, flexBasis: `${t.count} 0 0` }}
                              title={`${t.to.charAt(0).toUpperCase() + t.to.slice(1)} – ${t.count}`}
                            >
                              <span className="sr-only">{t.to}: {t.count}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                );
              })()
            ) : (
              <div className="flex h-full items-center justify-center">
                <div className="text-center py-8">
                  <p className="text-sm text-muted">No user activity yet</p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 