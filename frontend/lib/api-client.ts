import axios from 'axios';

// Create an Axios instance with default configuration
const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Important for handling cookies/authentication
});

// Log the API URL being used for debugging
console.log('API Client using baseURL:', process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1');

// Helper to safely check if we're on the client side
function isClient() {
  return typeof window !== 'undefined';
}

// Helper to get tokens and org ID - SSR safe
function getAccessToken() {
  if (!isClient()) return null;
  const token = localStorage.getItem('token');
  console.log('Getting access token:', token ? `${token.substring(0, 20)}...` : 'none');
  return token;
}

function getRefreshToken() {
  if (!isClient()) return null;
  return localStorage.getItem('refreshToken');
}

function getOrgId() {
  if (!isClient()) return null;
  const orgId = localStorage.getItem('orgId');
  console.log('Getting org ID:', orgId);
  return orgId;
}

// Helper to store new tokens - SSR safe
function storeTokens({ accessToken, refreshToken }: { accessToken: string, refreshToken: string }) {
  if (!isClient()) return;
  localStorage.setItem('token', accessToken);
  localStorage.setItem('refreshToken', refreshToken);
}

// Helper to logout - SSR safe
function logoutAndRedirect() {
  if (!isClient()) return;
  localStorage.removeItem('token');
  localStorage.removeItem('refreshToken');
  window.location.href = '/login';
}

// Request interceptor: always attach token and org ID
apiClient.interceptors.request.use(config => {
  const token = getAccessToken();
  const orgId = getOrgId();

  if (!config.headers) config.headers = {};

  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  if (orgId) {
    config.headers['X-ORG-ID'] = orgId;
  }

  // Debug logging for API requests
  console.log('🌐 API Request:', {
    url: config.url,
    method: config.method,
    hasToken: !!token,
    hasOrgId: !!orgId,
    headers: {
      Authorization: typeof config.headers.Authorization === 'string' 
        ? `${config.headers.Authorization.substring(0, 30)}...` 
        : 'none',
      'X-ORG-ID': config.headers['X-ORG-ID'] || 'none'
    }
  });

  return config;
});

// Global refresh state to prevent race conditions
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value: any) => void;
  reject: (error: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

// Response interceptor: handle 401s with token refresh
apiClient.interceptors.response.use(
  response => {
    // Log successful responses
    console.log('✅ API Response:', {
      url: response.config.url,
      method: response.config.method,
      status: response.status,
      hasData: !!response.data
    });
    return response;
  },
  async error => {
    const originalRequest = error.config;

    // Log API errors for debugging
    if (error.response) {
      console.log('❌ API Error:', {
        url: error.config?.url,
        method: error.config?.method,
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
        headers: error.config?.headers
      });
    } else {
      console.log('❌ Network Error:', error.message);
    }

    // Handle 401 Unauthorized errors
    if (error.response?.status === 401 && !originalRequest._retry) {
      console.log('🔄 401 detected, attempting token refresh...');

      // If already refreshing, queue this request
      if (isRefreshing) {
        console.log('⏳ Refresh in progress, queueing request...');
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then(token => {
          if (token) {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return apiClient(originalRequest);
          } else {
            // Token refresh failed, reject with auth error
            return Promise.reject(new Error('Authentication failed'));
          }
        }).catch(err => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        // Attempt to refresh the token
        const refreshToken = getRefreshToken();
        if (!refreshToken) {
          console.error('No refresh token available');
          throw new Error('No refresh token available');
        }

        // Call your refresh token endpoint - refresh_token should be a query parameter
        const response = await axios.post(
          `${apiClient.defaults.baseURL}/refresh?refresh_token=${encodeURIComponent(refreshToken)}`
        );

        // Check if refresh endpoint returned non-200 status
        if (response.status !== 200) {
          console.error('❌ Refresh endpoint returned non-200 status:', response.status);
          throw new Error(`Refresh failed with status: ${response.status}`);
        }

        const { access_token, refresh_token } = response.data;

        // Update tokens
        localStorage.setItem('token', access_token);

        // Update headers for future requests
        apiClient.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;
        originalRequest.headers.Authorization = `Bearer ${access_token}`;

        // Process queued requests with the new token
        processQueue(null, access_token);

        console.log('🔄 Token refresh successful, retrying original request');
        
        // Retry the original request
        return apiClient(originalRequest);
      } catch (err) {
        console.error('Token refresh failed:', err);
        
        // For refresh endpoint failures, logout and redirect to login
        console.log('🚪 Refresh failed - logging out user and redirecting to login');
        processQueue(err, null);
        
        // Clear tokens and redirect to login
        logoutAndRedirect();
        
        return Promise.reject(err);

      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

export default apiClient;
