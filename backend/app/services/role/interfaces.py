from typing import List, Optional, Dict, Tuple
from abc import ABC, abstractmethod

from app.models.role import Role, Permission


class RoleServiceInterface(ABC):
    """Interface for role management services."""

    @abstractmethod
    async def create_role(
        self,
        name: str,
        description: str,
        permissions: List[Permission],
        is_system: bool = False,
        org_id: Optional[str] = None
    ) -> Role:
        """Create a new role."""
        pass

    @abstractmethod
    async def get_role(self, role_id: str, org_id: Optional[str] = None) -> Optional[Role]:
        """Get a role by ID."""
        pass

    @abstractmethod
    async def get_roles_by_org(self, org_id: str) -> List[Role]:
        """Get all roles for an organization."""
        pass

    @abstractmethod
    async def update_role(
        self,
        role_id: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        permissions: Optional[List[Permission]] = None,
        org_id: Optional[str] = None
    ) -> Optional[Role]:
        """Update an existing role."""
        pass

    @abstractmethod
    async def delete_role(self, role_id: str, org_id: Optional[str] = None) -> bool:
        """Delete a role."""
        pass

    @abstractmethod
    async def create_default_roles(self, org_id: str) -> Tuple[Role, Role]:
        """Create default roles for a new organization."""
        pass

    @abstractmethod
    async def get_all_roles(self, org_id: Optional[str] = None) -> List[Dict]:
        """Get all roles in the system."""
        pass

    @abstractmethod
    async def add_permission(self, role_id: str, permission: Permission, org_id: Optional[str] = None) -> Role:
        """Add a permission to a role."""
        pass

    @abstractmethod
    async def remove_permission(self, role_id: str, permission: Permission, org_id: Optional[str] = None) -> Role:
        """Remove a permission from a role."""
        pass

    @abstractmethod
    async def check_permission(self, role_id: str, permission: Permission, org_id: Optional[str] = None) -> bool:
        """Check if a role has a specific permission."""
        pass

    @abstractmethod
    async def get_role_permissions(self, role_id: str, org_id: Optional[str] = None) -> List[Dict]:
        """Get all permissions for a specific role."""
        pass
