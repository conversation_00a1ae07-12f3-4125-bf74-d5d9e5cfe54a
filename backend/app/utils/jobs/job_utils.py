"""
Job Utilities

This module provides utility functions for working with the job tracking system.
"""

from typing import Any, Awaitable, Callable, Dict, List, Optional, Union

from app.models.job import EntityType, JobStatus, JobType, TrackedJob
from app.services.factory import get_job_service
from app.services.queue import JobPriority, QueueType
from bson import ObjectId


async def create_tracked_job(
    entity_type: Union[str, EntityType],
    entity_id: Union[str, ObjectId],
    job_type: Union[str, JobType],
    payload: Dict[str, Any],
    queue_job_type: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
    config: Optional[Dict[str, Any]] = None,
    priority: int = 0,
    parent_job_id: Optional[Union[str, ObjectId]] = None,
    depends_on: Optional[List[Union[str, ObjectId]]] = None,
    scheduled_at: Optional[int] = None,
    queue_priority: JobPriority = JobPriority.NORMAL,
    queue_type: QueueType = QueueType.DEFAULT,
) -> TrackedJob:
    """
    Create a tracked job and enqueue it for processing.

    This is a convenience function that creates a tracked job and enqueues it
    in the queue system in a single operation.

    Args:
        entity_type: Type of entity this job is associated with
        entity_id: ID of the entity this job is associated with
        job_type: Type of job being performed
        payload: Job payload to be passed to the queue
        queue_job_type: Type of job in the queue system (defaults to {job_type}_job)
        metadata: Additional metadata about the job
        config: Configuration for the job
        priority: Priority of the job (higher values = higher priority)
        parent_job_id: ID of the parent job if this is a child job
        depends_on: IDs of jobs this job depends on
        scheduled_at: When the job is scheduled to run
        queue_priority: Priority in the queue system
        queue_type: Type of queue to use
        job_service: Job service instance (will be created if not provided)

    Returns:
        The created job
    """
    # Get job service if not provided
    from app.services.job.mongo import JobService

    job_service: JobService = await get_job_service()  # type: ignore

    # Convert string values to enums if needed
    if isinstance(entity_type, str):
        entity_type = EntityType(entity_type)

    if isinstance(job_type, str):
        job_type = JobType(job_type)

    # Convert string IDs to ObjectId if needed
    if isinstance(entity_id, str):
        entity_id = ObjectId(entity_id)

    if isinstance(parent_job_id, str) and parent_job_id:
        parent_job_id = ObjectId(parent_job_id)

    if depends_on:
        depends_on = [
            ObjectId(job_id) if isinstance(job_id, str) else job_id
            for job_id in depends_on
        ]

    # Set default queue job type if not provided
    if queue_job_type is None:
        queue_job_type = f"{job_type.value}"

    # Create job with additional fields
    job = await job_service.create_job(
        entity_type=entity_type,
        entity_id=entity_id,
        job_type=job_type,
        payload=payload,
        metadata=metadata or {},
        config=config or {},
        priority=priority,
        parent_job_id=parent_job_id,
        depends_on=depends_on or [],
        scheduled_at=scheduled_at,
        queue_priority=queue_priority,
        queue_type=queue_type,
        queue_job_type=queue_job_type,
    )

    return job


async def create_job_chain(
    entity_type: Union[str, EntityType],
    entity_id: Union[str, ObjectId],
    job_configs: List[Dict[str, Any]],
    metadata: Optional[Dict[str, Any]] = None,
) -> List[TrackedJob]:
    """
    Create a chain of dependent jobs.

    This function creates a series of jobs where each job depends on the
    completion of the previous job in the chain.

    Args:
        entity_type: Type of entity these jobs are associated with
        entity_id: ID of the entity these jobs are associated with
        job_configs: List of job configurations, each containing:
            - job_type: Type of job being performed
            - payload: Job payload to be passed to the queue
            - queue_job_type: (Optional) Type of job in the queue system
            - metadata: (Optional) Additional metadata about the job
            - config: (Optional) Configuration for the job
            - priority: (Optional) Priority of the job
        metadata: Common metadata to apply to all jobs
        job_service: Job service instance (will be created if not provided)

    Returns:
        List of created jobs
    """
    # Get job service if not provided
    from app.services.job.mongo import JobService

    job_service: JobService = await get_job_service()  # type: ignore
    await job_service.initialize()

    jobs = []
    previous_job_id = None

    for job_config in job_configs:
        # Create job with dependency on previous job
        if previous_job_id:
            job_config.setdefault("depends_on", []).append(previous_job_id)

        # Add common metadata
        if metadata:
            job_config.setdefault("metadata", {}).update(metadata)

        # Create job
        job = await create_tracked_job(
            entity_type=entity_type,
            entity_id=entity_id,
            **job_config,
        )

        jobs.append(job)
        previous_job_id = job.id

    return jobs


async def create_job_group(
    entity_type: Union[str, EntityType],
    entity_id: Union[str, ObjectId],
    job_configs: List[Dict[str, Any]],
    metadata: Optional[Dict[str, Any]] = None,
    parent_job: Optional[TrackedJob] = None,
) -> List[TrackedJob]:
    """
    Create a group of related jobs with an optional parent job.

    This function creates multiple jobs that are related to each other,
    optionally with a parent job that tracks the overall progress.

    Args:
        entity_type: Type of entity these jobs are associated with
        entity_id: ID of the entity these jobs are associated with
        job_configs: List of job configurations, each containing:
            - job_type: Type of job being performed
            - payload: Job payload to be passed to the queue
            - queue_job_type: (Optional) Type of job in the queue system
            - metadata: (Optional) Additional metadata about the job
            - config: (Optional) Configuration for the job
            - priority: (Optional) Priority of the job
        metadata: Common metadata to apply to all jobs
        parent_job: Optional parent job to link these jobs to
        job_service: Job service instance (will be created if not provided)

    Returns:
        List of created jobs
    """
    # Get job service if not provided
    from app.services.job.mongo import JobService

    job_service: JobService = await get_job_service()  # type: ignore
    await job_service.initialize()

    jobs = []

    for job_config in job_configs:
        # Add parent job ID if provided
        if parent_job:
            job_config["parent_job_id"] = parent_job.id

        # Add common metadata
        if metadata:
            job_config.setdefault("metadata", {}).update(metadata)

        # Create job
        job = await create_tracked_job(
            entity_type=entity_type,
            entity_id=entity_id,
            **job_config,
        )

        jobs.append(job)

    return jobs


async def with_job_tracking(
    entity_type: Union[str, EntityType],
    entity_id: Union[str, ObjectId],
    job_type: Union[str, JobType],
    func: Callable[..., Awaitable[Any]],
    *args,
    metadata: Optional[Dict[str, Any]] = None,
    **kwargs,
) -> Any:
    """
    Execute a function with job tracking.

    This function creates a tracked job, executes the provided function,
    and updates the job status based on the result.

    Args:
        entity_type: Type of entity this job is associated with
        entity_id: ID of the entity this job is associated with
        job_type: Type of job being performed
        func: Async function to execute
        *args: Arguments to pass to the function
        metadata: Additional metadata about the job
        **kwargs: Keyword arguments to pass to the function

    Returns:
        The result of the function
    """
    from app.services.job.mongo import JobService

    job_service: JobService = await get_job_service()  # type: ignore

    # Create job
    job = await job_service.create_job(
        entity_type=entity_type,
        entity_id=entity_id,
        job_type=job_type,
        payload={},  # No queue payload needed
        metadata=metadata or {},
    )

    try:
        # Update job status to in progress
        await job_service.update_job_status(
            job_id=job.id, status=JobStatus.IN_PROGRESS, progress=0.1
        )

        # Execute function
        result = await func(*args, **kwargs)

        # Update job status to completed
        await job_service.update_job_status(
            job_id=job.id,
            status=JobStatus.COMPLETED,
            progress=1.0,
            output={"result": result},
        )

        return result
    except Exception as e:
        # Update job status to failed
        await job_service.update_job_status(
            job_id=job.id, status=JobStatus.FAILED, error=str(e)
        )

        raise
