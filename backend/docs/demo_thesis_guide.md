# Demo Thesis Guide for Beta Users

## Overview

When beta users create a new organization and default form, a comprehensive demo thesis is automatically created to help them understand the investment thesis builder's capabilities. This thesis demonstrates all available scoring features including match rules, scoring rules, bonus rules, and exclusion filters.

## Thesis Structure

The demo thesis is titled **"Demo Thesis - Understanding Investment Scoring"** and includes:

### Match Rules (2 rules)
**Purpose**: Determine which form submissions this thesis applies to

1. **Early Stage Focus**
   - **Description**: Matches companies in early funding stages (Pre-Seed, Seed, Series A)
   - **Operator**: OR
   - **Condition**: Funding Stage is in ["pre_seed", "seed", "series_a"]

2. **Tech Sector Focus**
   - **Description**: Matches companies in technology sectors
   - **Operator**: OR
   - **Conditions**: 
     - Business Sectors contains "ai_ml"
     - Business Sectors contains "fintech"
     - Business Sectors contains "saas"

### Scoring Rules (6 rules)
**Purpose**: Award points based on specific criteria

1. **Company Name Score**
   - **Type**: Scoring
   - **Weight**: 1.0
   - **Condition**: Company Name is not empty
   - **Notes**: Score for having a company name

2. **HTTPS Website Bonus**
   - **Type**: Scoring
   - **Weight**: 1.5
   - **Condition**: Company Website starts with "https://"
   - **Notes**: Bonus for having HTTPS website

3. **Preferred Funding Stages**
   - **Type**: Scoring
   - **Weight**: 2.0
   - **Condition**: Funding Stage is in ["seed", "series_a"]
   - **Notes**: Preferred funding stages

4. **AI/ML Sector Bonus**
   - **Type**: Scoring
   - **Weight**: 1.5
   - **Condition**: Business Sectors contains "ai_ml"
   - **Notes**: AI/ML sector bonus

5. **Team Size Score**
   - **Type**: Scoring
   - **Weight**: 1.0
   - **Condition**: Team Size >= 5
   - **Notes**: Teams with 5+ members

6. **Revenue Score**
   - **Type**: Scoring
   - **Weight**: 2.0
   - **Condition**: Monthly Revenue >= 10 (thousands)
   - **Notes**: Companies with $10k+ monthly revenue

### Bonus Rules (3 rules)
**Purpose**: Award additional points for special combinations

1. **Tech Company with Technical Co-founder**
   - **Type**: Bonus
   - **Points**: 5.0
   - **Condition**: 
     - Do you have a technical co-founder? = Yes
     - AND What type of product are you building? is in ["mobile_app", "web_app"]
   - **Notes**: Bonus for tech companies with technical co-founder

2. **Paying Customers Bonus**
   - **Type**: Bonus
   - **Points**: 3.0
   - **Condition**: Do you have paying customers? = Yes
   - **Notes**: Bonus for having paying customers

3. **Pre-seed with Team**
   - **Type**: Bonus
   - **Points**: 2.0
   - **Condition**:
     - Funding Stage = "pre_seed"
     - AND Team Size >= 3
   - **Notes**: Bonus for pre-seed companies with 3+ team members

### Exclusion Filter (1 rule)
**Purpose**: Filter out submissions that don't meet basic criteria

1. **Exclusion Criteria**
   - **Type**: Bonus (with negative points)
   - **Points**: -1000.0 (large negative score to exclude)
   - **Condition**:
     - Funding Stage = "ipo" (exclude IPO companies)
     - OR Team Size < 1 (exclude solo founders)
     - OR Business Sectors contains "gaming" (exclude gaming companies)
   - **Notes**: Exclusion filter: IPO companies, solo founders, or gaming companies

## Key Features Demonstrated

### Match Rules
- ✅ **Simple Conditions**: Single field matching
- ✅ **Multiple Conditions**: OR logic for multiple criteria
- ✅ **List Matching**: Using "in" operator for multiple values
- ✅ **Contains Matching**: Using "contains" for multi-select fields

### Scoring Rules
- ✅ **Basic Scoring**: Points for having required information
- ✅ **Weighted Scoring**: Different weights for different criteria
- ✅ **String Matching**: Pattern matching with "starts_with"
- ✅ **Numeric Comparisons**: Greater than/equal to comparisons
- ✅ **List Matching**: Using "in" operator for single-select fields
- ✅ **Contains Matching**: Using "contains" for multi-select fields

### Bonus Rules
- ✅ **Simple Bonus**: Single condition bonus points
- ✅ **Compound Bonus**: AND logic for multiple conditions
- ✅ **Cross-field Logic**: Combining different question types
- ✅ **Conditional Logic**: Complex rule combinations

### Exclusion Filters
- ✅ **Negative Scoring**: Using large negative points to exclude
- ✅ **Multiple Exclusion Criteria**: OR logic for different exclusion reasons
- ✅ **Automatic Filtering**: Submissions with negative scores are effectively excluded

## Learning Objectives

This demo thesis helps beta users understand:

1. **Match Rule Creation**: How to define which submissions a thesis applies to
2. **Scoring Strategy**: How to assign points based on different criteria
3. **Bonus Logic**: How to reward special combinations or achievements
4. **Exclusion Filters**: How to filter out unsuitable submissions
5. **Condition Types**: How to use different operators and data types
6. **Rule Combinations**: How to create complex logical conditions
7. **Weight Management**: How to prioritize different scoring criteria

## Usage Instructions

1. **Explore the Rules**: Review each rule to understand the logic
2. **Test with Sample Data**: Create test submissions to see how scoring works
3. **Modify Rules**: Try editing rules to see how changes affect scoring
4. **Add New Rules**: Practice creating additional rules
5. **Study Patterns**: Use this as a template for creating your own theses

## Example Scoring Scenarios

### High-Scoring Company
- **Company**: "TechCorp AI"
- **Website**: "https://techcorp.ai"
- **Stage**: "Series A"
- **Sectors**: ["ai_ml", "fintech"]
- **Team Size**: 8
- **Revenue**: $25k/month
- **Technical Co-founder**: Yes
- **Product Type**: "web_app"
- **Paying Customers**: Yes
- **Expected Score**: ~15+ points (high match)

### Medium-Scoring Company
- **Company**: "StartupXYZ"
- **Website**: "http://startupxyz.com"
- **Stage**: "Seed"
- **Sectors**: ["saas"]
- **Team Size**: 4
- **Revenue**: $5k/month
- **Technical Co-founder**: No
- **Product Type**: "service"
- **Paying Customers**: No
- **Expected Score**: ~5-8 points (medium match)

### Excluded Company
- **Company**: "GamingStudio"
- **Website**: "https://gamingstudio.com"
- **Stage**: "Seed"
- **Sectors**: ["gaming"]
- **Team Size**: 2
- **Revenue**: $15k/month
- **Expected Score**: ~-1000 points (excluded due to gaming sector)

## Customization

Users can:
- Edit rule conditions and values
- Adjust scoring weights and bonus points
- Add new match rules for different criteria
- Create additional scoring rules
- Modify exclusion filters
- Change the overall thesis strategy

This demo thesis serves as both a learning tool and a starting template for creating effective investment theses that can automatically score and filter startup submissions. 