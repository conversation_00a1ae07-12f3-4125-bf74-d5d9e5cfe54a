"use client"

import { motion } from "framer-motion"
import { FileText, Target, FolderOpen, ArrowRight, Sparkles } from "lucide-react"

import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface WorkflowsOverviewStepProps {
  user: any
  stepData: any
  onNext: (data?: any) => void
  onBack: () => void
  onSkip: () => void
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
  currentStep: number
  totalSteps: number
}

const WORKFLOWS = [
  {
    icon: FileText,
    title: "Forms",
    description: "Collect structured startup data",
    details: "Create custom forms to gather consistent information from startups. Standardize your deal intake process with intelligent form builders.",
    color: "from-blue-500 to-blue-600",
    bgColor: "bg-blue-50",
    iconColor: "text-blue-600"
  },
  {
    icon: Target,
    title: "Theses",
    description: "Define how you invest and score deals",
    details: "Set up investment criteria and scoring rules. Let AI evaluate deals against your thesis automatically with detailed match analysis.",
    color: "from-purple-500 to-purple-600",
    bgColor: "bg-purple-50",
    iconColor: "text-purple-600"
  },
  {
    icon: FolderOpen,
    title: "Deals",
    description: "Evaluate opportunities with AI insights",
    details: "Track and analyze investment opportunities. Get AI-powered insights, scoring, and recommendations based on your investment thesis.",
    color: "from-emerald-500 to-emerald-600",
    bgColor: "bg-emerald-50",
    iconColor: "text-emerald-600"
  }
]

export function WorkflowsOverviewStep({ onNext }: WorkflowsOverviewStepProps) {
  const handleTakeMeToDashboard = () => {
    onNext()
  }

  return (
    <div className="flex-1 flex flex-col p-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-12"
      >
        <div className="flex items-center justify-center mb-4">
          <div className="flex size-16 items-center justify-center rounded-2xl bg-gradient-to-br from-emerald-500 to-emerald-600 shadow-lg">
            <Sparkles className="size-8 text-white" />
          </div>
        </div>
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          How TractionX Works
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          A quick tour of your new decision support system
        </p>
      </motion.div>

      {/* Workflow Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="flex-1 max-w-5xl mx-auto w-full"
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {WORKFLOWS.map((workflow, index) => {
            const Icon = workflow.icon
            
            return (
              <motion.div
                key={workflow.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
                whileHover={{ scale: 1.02, y: -4 }}
                className={cn(
                  "bg-white rounded-2xl p-8 border border-gray-100 shadow-sm",
                  "hover:shadow-lg transition-all duration-300 cursor-pointer",
                  "flex flex-col h-full"
                )}
              >
                {/* Icon */}
                <div className={cn(
                  "flex size-16 items-center justify-center rounded-2xl mb-6",
                  workflow.bgColor
                )}>
                  <Icon className={cn("size-8", workflow.iconColor)} />
                </div>

                {/* Content */}
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">
                    {workflow.title}
                  </h3>
                  <p className="text-lg text-gray-600 mb-4 font-medium">
                    {workflow.description}
                  </p>
                  <p className="text-gray-500 leading-relaxed">
                    {workflow.details}
                  </p>
                </div>

                {/* Visual Indicator */}
                <div className="mt-6 pt-4 border-t border-gray-100">
                  <div className={cn(
                    "h-2 rounded-full bg-gradient-to-r",
                    workflow.color
                  )} />
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Flow Arrows (Desktop) */}
        <div className="hidden lg:flex items-center justify-center mt-8 space-x-8">
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.8 }}
            className="flex items-center text-gray-400"
          >
            <ArrowRight className="size-6" />
          </motion.div>
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 1.0 }}
            className="flex items-center text-gray-400"
          >
            <ArrowRight className="size-6" />
          </motion.div>
        </div>

        {/* Process Flow Description */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2 }}
          className="text-center mt-8"
        >
          <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 max-w-3xl mx-auto">
            <h4 className="font-semibold text-gray-900 mb-2">Your Investment Workflow</h4>
            <p className="text-gray-600">
              <span className="font-medium text-blue-600">Create forms</span> to collect startup data → 
              <span className="font-medium text-purple-600 mx-2">Define scoring theses</span> → 
              <span className="font-medium text-emerald-600 ml-2">Evaluate deals with AI insights</span>
            </p>
          </div>
        </motion.div>
      </motion.div>

      {/* Action Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.4 }}
        className="flex items-center justify-center pt-12"
      >
        <Button
          onClick={handleTakeMeToDashboard}
          size="lg"
          className={cn(
            "bg-gradient-to-r from-emerald-500 to-emerald-600 text-white",
            "hover:from-emerald-600 hover:to-emerald-700",
            "px-8 py-3 text-lg font-semibold",
            "shadow-lg hover:shadow-xl transition-all duration-300",
            "flex items-center gap-2"
          )}
        >
          Take Me to My Dashboard
          <ArrowRight className="size-5" />
        </Button>
      </motion.div>

      {/* Helper Text */}
      <motion.p
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.5 }}
        className="text-center text-sm text-gray-500 mt-4"
      >
        You can explore these features at your own pace
      </motion.p>
    </div>
  )
}
