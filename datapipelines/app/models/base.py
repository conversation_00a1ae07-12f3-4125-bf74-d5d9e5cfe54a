"""
Base models and enums for TractionX Data Pipeline Service.
"""

from datetime import datetime, timezone
from enum import Enum
from typing import (
    Any,
    ClassVar,
    Dict,
    Optional,
    TypeVar,
)

from bson import ObjectId
from pydantic import BaseModel, ConfigDict, Field, field_validator

from app.configs.logging import get_logger
from app.utils.common import ObjectIdField

logger = get_logger(__name__)

T = TypeVar("T", bound="TractionXModel")


class TractionXModel(BaseModel):
    """
    Base document class with advanced MongoDB features for TractionX.

    Usage for modular config:
    ----------------------------------------
    # In your subclass, define only extra/override config:
    class Question(TractionXModel):
        extra_model_config = {
            'json_schema_extra': {
                'example': {...}
            }
        }
        ...
    # The merged config will be used as model_config for Pydantic.
    ----------------------------------------
    """

    # Default config for all models. Subclasses can add/override by defining 'extra_model_config'.
    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        populate_by_name=True,
        json_encoders={ObjectIdField: str, datetime: lambda v: v.isoformat()},  # type: ignore
        from_attributes=True,
    )

    # Subclasses can define this as a dict or ConfigDict to add/override config (e.g., json_schema_extra)
    extra_model_config: ClassVar[dict] = {}

    def __init_subclass__(cls, **kwargs):
        """
        Merge base model_config with any extra_model_config defined in subclasses.
        This allows subclasses to specify only the config they need to add or override
        (e.g., json_schema_extra) without redefining the entire config.
        """
        super().__init_subclass__(**kwargs)
        base_config = dict(TractionXModel.model_config)
        extra = getattr(cls, "extra_model_config", {}) or {}
        # Merge, with subclass config taking precedence
        merged = {**base_config, **extra}
        cls.model_config = ConfigDict(**merged)


    @staticmethod
    def serialize_value(value: Any, for_db: bool) -> Any:
        """
        Serialize a value for database storage or API response.

        Args:
            value: The value to serialize
            for_db: If True, serialize for database storage. If False, serialize for API response.

        Returns:
            Serialized value
        """
        if isinstance(value, BaseModel):
            return value.model_dump(mode="json" if for_db else "python", by_alias=True)
        elif isinstance(value, ObjectId):
            return str(value) if not for_db else value
        elif isinstance(value, str) and len(value) == 24 and for_db:
            # Check if this is a string that looks like an ObjectId and we're serializing for DB
            try:
                return ObjectId(value)
            except Exception:
                # logger.error(f"Error converting string to ObjectId: {e}")
                return value
        elif isinstance(value, datetime):
            return value.isoformat() if not for_db else value
        elif isinstance(value, list):
            return [TractionXModel.serialize_value(v, for_db) for v in value]
        elif isinstance(value, dict):
            return {
                k: TractionXModel.serialize_value(v, for_db) for k, v in value.items()
            }
        return value

    @classmethod
    def get_collection_name(cls) -> str:
        """
        Returns the MongoDB collection name for this model.
        By default, it uses the lowercase class name with an 's' appended (e.g., 'User' -> 'users').
        Override this method if you want a custom collection name.
        """
        return cls.__name__.lower() + "s"

    @classmethod
    async def get_collection(cls):  # type: ignore
        """
        Asynchronously retrieves the MongoDB collection object for this model.
        Uses the configured database connection and the collection name from get_collection_name().
        """
        # implement later, as for datapipelines use rds instead of mongodb
        return None

    def model_dump(self, *, for_db=False, **kwargs) -> Dict[str, Any]:
        """Override model_dump to:
        - Exclude internal/config fields (e.g., extra_model_config)
        - Only persist declared fields
        - Ensure values are of correct types as per model schema
        - Only convert ObjectId to str for API/JSON, not for DB
        """
        raw_data = super().model_dump(**kwargs)
        output = {}

        # Prefer model_fields (Pydantic v2), fallback to __fields__ (v1 compat)
        fields = getattr(self, "model_fields", getattr(self, "__fields__", {}))
        field_names = set(fields.keys())
        if "id" in field_names:
            field_names.add("_id")

        exclude_fields = {"extra_model_config", "model_config"}
        field_names -= exclude_fields

        for field_name in field_names:
            alias = None
            if kwargs.get("by_alias", False):
                if field_name in fields and hasattr(fields[field_name], "alias"):
                    alias = fields[field_name].alias
            key = alias or field_name
            if key in raw_data:
                value = raw_data[key]
                output[key] = TractionXModel.serialize_value(value, for_db)

        return output

    def to_json(self, *, indent: Optional[int] = None, **kwargs) -> str:
        """
        Convert model to JSON string with proper serialization of all types.

        This method handles:
        - ObjectId conversion to strings
        - Datetime objects to ISO format
        - Nested models and lists
        - All MongoDB-specific types

        Args:
            indent: JSON indentation (default: None for compact)
            **kwargs: Additional arguments passed to json.dumps

        Returns:
            JSON string representation of the model
        """
        import json

        # Use model_dump with for_db=False to get API-friendly format
        data = self.model_dump(for_db=False, **kwargs)

        # The serialize_value method already handles all type conversions
        # so we can directly serialize to JSON
        return json.dumps(data, indent=indent, **kwargs)

    @classmethod
    def json_serialize(cls, obj: Any, *, indent: Optional[int] = None, **kwargs) -> str:
        """
        Static method to serialize any object to JSON with proper type handling.

        This is a utility method that can be used on any object, not just model instances.
        It uses the same serialization logic as the model system.

        Args:
            obj: Object to serialize
            indent: JSON indentation (default: None for compact)
            **kwargs: Additional arguments passed to json.dumps

        Returns:
            JSON string representation
        """
        import json

        # Use the same serialization logic as the model
        serialized = cls.serialize_value(obj, for_db=False)
        return json.dumps(serialized, indent=indent, **kwargs)


# Export BaseModel for compatibility
__all__ = [
    "BaseModel",
    "TractionXModel",
    "PipelineStatus",
    "EnrichmentSource",
    "ProcessingResult",
    "PipelineJobMetadata",
]


class PipelineStatus(str, Enum):
    """Status of a pipeline job."""

    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class EnrichmentSource(str, Enum):
    """Source of enrichment data."""

    FORM_SUBMISSION = "form_submission"
    CLAY = "clay"
    PDL = "pdl"
    APOLLO = "apollo"
    BING_NEWS = "bing_news"
    OPENAI = "openai"
    MANUAL = "manual"


class ProcessingResult(TractionXModel):
    """Result of a pipeline processing operation."""

    success: bool = Field(..., description="Whether processing was successful")
    data: Optional[Dict[str, Any]] = Field(None, description="Processed data")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Processing metadata"
    )

    # Error information
    error_type: Optional[str] = Field(None, description="Type of error if failed")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    error_details: Optional[Dict[str, Any]] = Field(
        None, description="Additional error details"
    )

    # Processing statistics
    processing_time: Optional[float] = Field(
        None, description="Processing time in seconds"
    )
    records_processed: Optional[int] = Field(
        None, description="Number of records processed"
    )
    records_failed: Optional[int] = Field(
        None, description="Number of records that failed"
    )

    # Source tracking
    source: Optional[EnrichmentSource] = Field(None, description="Source of the data")
    source_metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Source-specific metadata"
    )

    @classmethod
    def success_result(
        cls, data: Dict[str, Any], metadata: Optional[Dict[str, Any]] = None, **kwargs
    ) -> "ProcessingResult":
        """Create a successful result."""
        return cls(success=True, data=data, metadata=metadata or {}, **kwargs)

    @classmethod
    def error_result(
        cls,
        error_message: str,
        error_type: Optional[str] = None,
        error_details: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> "ProcessingResult":
        """Create an error result."""
        return cls(
            success=False,
            error_message=error_message,
            error_type=error_type,
            error_details=error_details,
            **kwargs,
        )


class PipelineJobMetadata(TractionXModel):
    """Metadata for pipeline jobs."""

    job_id: str = Field(..., description="Unique job identifier")
    pipeline_name: str = Field(..., description="Name of the pipeline")
    status: PipelineStatus = Field(
        default=PipelineStatus.PENDING, description="Current job status"
    )

    # Timing
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    started_at: Optional[datetime] = Field(
        None, description="When job started processing"
    )
    completed_at: Optional[datetime] = Field(None, description="When job completed")

    # Progress tracking
    progress: float = Field(
        default=0.0, ge=0.0, le=1.0, description="Job progress (0.0 to 1.0)"
    )
    current_step: Optional[str] = Field(None, description="Current processing step")
    total_steps: Optional[int] = Field(None, description="Total number of steps")

    # Error handling
    error_message: Optional[str] = Field(None, description="Error message if failed")
    retry_count: int = Field(default=0, description="Number of retry attempts")
    max_retries: int = Field(default=3, description="Maximum retry attempts")

    # Context
    org_id: Optional[str] = Field(None, description="Organization ID")
    deal_id: Optional[str] = Field(None, description="Company ID")
    company_id: Optional[str] = Field(None, description="Company ID")

    # Additional metadata
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )

    @property
    def duration(self) -> Optional[float]:
        """Calculate duration in seconds if job has started."""
        if not self.started_at:
            return None
        end_time = self.completed_at or datetime.now(timezone.utc)
        return (end_time - self.started_at).total_seconds()

    @property
    def is_completed(self) -> bool:
        """Check if job is in a completed state."""
        return self.status in [
            PipelineStatus.SUCCESS,
            PipelineStatus.FAILED,
            PipelineStatus.CANCELLED,
        ]

    @property
    def is_running(self) -> bool:
        """Check if job is currently running."""
        return self.status in [PipelineStatus.RUNNING, PipelineStatus.RETRYING]
