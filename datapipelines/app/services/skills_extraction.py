"""
Skills Extraction Service for TractionX Data Pipeline Service.

This module provides LLM-powered skills extraction from BrightData LinkedIn profiles.
Since BrightData doesn't directly provide skills, we use OpenAI to extract them
from the profile content with an extremely sophisticated system prompt.
"""

from typing import Any, Dict, List, Optional

from app.clients.openai_client import OpenAIClient
from app.configs import get_logger, settings
from app.models.founder import BrightDataSnapshotRecord

logger = get_logger(__name__)


class SkillsExtractionService:
    """
    LLM-powered skills extraction service with sophisticated prompt engineering.

    Extracts skills from LinkedIn profile content using OpenAI GPT models with
    an extremely comprehensive system prompt that handles all edge cases.
    """

    def __init__(self):
        self.logger = get_logger(f"{__name__}.SkillsExtractionService")
        self.openai_client = OpenAIClient()

        # Extremely sophisticated skills extraction system prompt
        self.system_prompt = """You are an expert professional skills extraction AI with deep expertise in:
1. **Technical Skills Analysis**: Programming languages, frameworks, tools, platforms, methodologies
2. **Business Skills Assessment**: Leadership, management, strategy, operations, finance, marketing
3. **Domain Expertise Recognition**: Industry-specific knowledge, sector expertise, market understanding
4. **Soft Skills Identification**: Communication, collaboration, problem-solving, adaptability
5. **Career Progression Analysis**: Skill development patterns, role transitions, expertise evolution
6. **Startup & Entrepreneurship**: Founder skills, venture building, fundraising, scaling
7. **Global Market Knowledge**: Regional expertise, cultural understanding, international business

Your mission is to extract comprehensive professional skills from LinkedIn profiles with exceptional accuracy and depth.

**ANALYSIS FRAMEWORK:**

**1. CONTEXTUAL INFERENCE**
- Analyze job titles for implied skills (e.g., "Senior Software Engineer" → advanced programming, system design, mentoring)
- Extract skills from company names and industries (e.g., "Tesla" → electric vehicles, manufacturing, innovation)
- Infer skills from education and certifications
- Recognize skills from project descriptions and achievements

**2. SKILL CATEGORIZATION**
- **Technical Skills**: Programming, tools, platforms, methodologies, technical expertise
- **Business Skills**: Leadership, strategy, operations, finance, marketing, sales
- **Domain Skills**: Industry knowledge, sector expertise, market understanding
- **Soft Skills**: Communication, collaboration, problem-solving, adaptability
- **Management Skills**: Team leadership, project management, decision-making
- **Entrepreneurial Skills**: Startup experience, fundraising, scaling, innovation

**3. SKILL EXTRACTION RULES**
- Extract both explicit and implicit skills
- Infer skills from context and experience patterns
- Consider career progression and role evolution
- Recognize emerging and trending skills
- Include both broad and specific skill variations
- Extract skills from achievements and accomplishments
- Consider geographical and cultural context

**4. QUALITY STANDARDS**
- Prioritize relevant and current skills
- Avoid overly generic terms unless clearly justified
- Include skill levels where evident (e.g., "Advanced Python", "Expert Leadership")
- Extract domain-specific terminology and expertise
- Recognize cross-functional and interdisciplinary skills
- Consider industry trends and emerging technologies

**5. EDGE CASE HANDLING**
- For minimal profiles: Extract skills from available context (title, company, location)
- For extensive profiles: Focus on most relevant and recent skills
- For career transitions: Extract skills from both old and new domains
- For founders/entrepreneurs: Emphasize startup and business skills
- For technical roles: Prioritize technical expertise and tools
- For leadership roles: Emphasize management and strategic skills

**6. OUTPUT FORMAT**
Return a comprehensive, comma-separated list of skills that includes:
- Primary technical skills
- Business and leadership skills
- Domain expertise
- Soft skills and competencies
- Industry-specific knowledge
- Emerging and trending skills
- Cross-functional capabilities

**7. SKILL VALIDATION**
- Ensure skills are professional and relevant
- Avoid personal hobbies or non-professional interests
- Focus on career-relevant competencies
- Include both hard and soft skills
- Consider skill combinations and synergies

**8. CONTEXTUAL ENHANCEMENT**
- Extract skills from achievements and metrics
- Infer skills from project descriptions
- Recognize skills from team sizes and responsibilities
- Extract skills from company growth and scaling
- Consider skills from international experience
- Recognize skills from industry transitions

**9. INNOVATION DETECTION**
- Identify emerging technology skills
- Recognize innovative methodologies
- Extract skills from cutting-edge projects
- Identify skills from disruptive technologies
- Recognize skills from new market entries

**10. COMPREHENSIVE COVERAGE**
- Technical expertise and tools
- Business acumen and strategy
- Leadership and management
- Domain knowledge and expertise
- Soft skills and competencies
- Innovation and creativity
- Global and cultural awareness
- Industry-specific knowledge

You are capable of extracting skills from even the most minimal profiles by analyzing:
- Job titles and their implications
- Company names and industry context
- Educational background and certifications
- Location and market context
- Career progression patterns
- Achievement descriptions
- Project involvement
- Team and organizational context

Your analysis should be thorough, accurate, and comprehensive, providing valuable insights into the professional capabilities of each individual."""

        # User prompt template
        self.user_prompt_template = """
**LINKEDIN PROFILE ANALYSIS FOR SKILLS EXTRACTION**

**PROFILE INFORMATION:**
- **Name**: {name}
- **Current Position**: {position}
- **Current Company**: {current_company}
- **Location**: {location}
- **About**: {about}

**EXPERIENCE DETAILS:**
{experience}

**EDUCATION BACKGROUND:**
{education}

**LANGUAGE SKILLS:**
{languages}

**CERTIFICATIONS:**
{certifications}

**PROJECTS & ACHIEVEMENTS:**
{projects}

**ADDITIONAL CONTEXT:**
- Profile URL: {linkedin_url}
- Industry: {industry}
- Years of Experience: {years_experience}

**ANALYSIS INSTRUCTIONS:**
1. Perform comprehensive skills analysis using all available information
2. Extract both explicit and implicit skills from every section
3. Consider career progression and role evolution
4. Analyze company context and industry implications
5. Infer skills from achievements and project descriptions
6. Consider geographical and cultural context
7. Identify emerging and trending skills
8. Extract domain-specific expertise
9. Recognize cross-functional capabilities
10. Consider startup and entrepreneurial context

**OUTPUT REQUIREMENTS:**
- Return ONLY a comma-separated list of professional skills
- Include 20-40 most relevant and valuable skills
- Prioritize current and emerging skills
- Include both technical and business skills
- Consider skill levels where evident
- Focus on career-relevant competencies
- Avoid personal or non-professional skills

**SKILL EXTRACTION EXAMPLES:**
- "Senior Software Engineer at Tesla" → Python, System Design, Electric Vehicles, Manufacturing, Innovation, Team Leadership
- "Founder of Renewable Energy Startup" → Entrepreneurship, Renewable Energy, Business Development, Fundraising, Strategic Planning, Sustainability
- "VP of Engineering at FinTech Company" → Engineering Leadership, Financial Technology, Team Management, Product Development, Regulatory Compliance, Innovation

Extract the most comprehensive and relevant professional skills from this profile.
"""

    async def extract_skills_from_profile(
        self, profile_data: BrightDataSnapshotRecord
    ) -> List[str]:
        """
        Extract skills from a BrightData LinkedIn profile using sophisticated LLM analysis.

        Args:
            profile_data: BrightData snapshot record

        Returns:
            List of extracted skills
        """
        try:
            self.logger.info(
                "Extracting skills from profile with sophisticated LLM analysis",
                name=profile_data.name,
                linkedin_id=profile_data.linkedin_id,
            )

            # Prepare comprehensive profile content for LLM
            profile_content = self._prepare_comprehensive_profile_content(profile_data)

            # Extract skills using sophisticated LLM analysis
            skills_text = await self._extract_skills_with_sophisticated_llm(
                profile_content
            )

            # Parse and clean skills
            skills = self._parse_skills_advanced(skills_text)

            self.logger.info(
                "Sophisticated skills extraction completed",
                name=profile_data.name,
                skills_count=len(skills),
                skills=skills[:10],  # Log first 10 skills
            )

            return skills

        except Exception as e:
            self.logger.error(
                f"Sophisticated skills extraction failed: {str(e)}",
                name=profile_data.name if profile_data else "unknown",
                exc_info=True,
            )
            # Even on failure, try to extract basic skills from available data
            return self._extract_basic_skills_from_context(profile_data)

    def _prepare_comprehensive_profile_content(
        self, profile_data: BrightDataSnapshotRecord
    ) -> Dict[str, Any]:
        """Prepare comprehensive profile content for sophisticated LLM processing."""

        # Extract detailed experience information
        experience_details = []
        for exp in profile_data.experience or []:
            exp_detail = f"• {exp.get('title', '')} at {exp.get('company', '')}"
            if exp.get("description"):
                exp_detail += f"\n  Description: {exp.get('description')}"
            if exp.get("duration"):
                exp_detail += f"\n  Duration: {exp.get('duration')}"
            if exp.get("location"):
                exp_detail += f"\n  Location: {exp.get('location')}"
            experience_details.append(exp_detail)

        # Extract detailed education information
        education_details = []
        for edu in profile_data.education or []:
            edu_detail = f"• {edu.get('title', '')}"
            if edu.get("degree"):
                edu_detail += f" - {edu.get('degree')}"
            if edu.get("field"):
                edu_detail += f" in {edu.get('field')}"
            if edu.get("duration"):
                edu_detail += f" ({edu.get('duration')})"
            education_details.append(edu_detail)

        # Extract language skills with proficiency levels
        language_details = []
        for lang in profile_data.languages or []:
            lang_detail = f"• {lang.get('title', '')}"
            if lang.get("subtitle"):
                lang_detail += f" ({lang.get('subtitle')})"
            language_details.append(lang_detail)

        # Extract certification details
        certification_details = []
        for cert in profile_data.certifications or []:
            cert_detail = f"• {cert.get('title', '')}"
            if cert.get("issuer"):
                cert_detail += f" from {cert.get('issuer')}"
            if cert.get("duration"):
                cert_detail += f" ({cert.get('duration')})"
            certification_details.append(cert_detail)

        # Extract project details
        project_details = []
        for project in profile_data.projects or []:
            project_detail = f"• {project.get('title', '')}"
            if project.get("description"):
                project_detail += f": {project.get('description')}"
            project_details.append(project_detail)

        # Extract current company information
        current_company = ""
        if profile_data.current_company and isinstance(
            profile_data.current_company, dict
        ):
            company_name = profile_data.current_company.get("name", "")
            company_industry = profile_data.current_company.get("industry", "")
            current_company = f"{company_name}"
            if company_industry:
                current_company += f" ({company_industry})"

        # Calculate years of experience from experience data
        years_experience = self._estimate_years_experience(
            profile_data.experience or []
        )

        # Determine industry from current company or experience
        industry = self._determine_industry(profile_data)

        return {
            "name": profile_data.name or "",
            "position": profile_data.position or "",
            "current_company": current_company,
            "location": profile_data.location or "",
            "about": profile_data.about or "",
            "experience": "\n".join(experience_details)
            if experience_details
            else "No experience data available",
            "education": "\n".join(education_details)
            if education_details
            else "No education data available",
            "languages": "\n".join(language_details)
            if language_details
            else "No language data available",
            "certifications": "\n".join(certification_details)
            if certification_details
            else "No certification data available",
            "projects": "\n".join(project_details)
            if project_details
            else "No project data available",
            "linkedin_url": profile_data.url or "",
            "industry": industry,
            "years_experience": years_experience,
        }

    async def _extract_skills_with_sophisticated_llm(
        self, profile_content: Dict[str, Any]
    ) -> str:
        """Extract skills using sophisticated OpenAI LLM analysis."""

        # Format the user prompt
        user_prompt = self.user_prompt_template.format(**profile_content)

        try:
            # Call OpenAI with sophisticated prompt
            response = await self.openai_client.create_chat_completion(
                messages=[
                    {
                        "role": "system",
                        "content": self.system_prompt,
                    },
                    {"role": "user", "content": user_prompt},
                ],
                model=settings.OPENAI_MODEL,
                temperature=0.2,  # Slightly higher for more creative analysis
            )

            if response and response.get("choices"):
                skills_text = response["choices"][0]["message"]["content"].strip()
                self.logger.info(
                    "LLM skills extraction successful",
                    skills_text_length=len(skills_text),
                    skills_preview=skills_text[:200],
                )
                return skills_text
            else:
                self.logger.warning(
                    "No response from OpenAI for sophisticated skills extraction"
                )
                return ""

        except Exception as e:
            self.logger.error(
                f"Sophisticated OpenAI skills extraction failed: {str(e)}"
            )
            return ""

    def _estimate_years_experience(self, experience: List[Dict[str, Any]]) -> str:
        """Estimate years of experience from experience data."""
        if not experience:
            return "Unknown"

        # Simple estimation based on number of roles and typical durations
        total_years = len(experience) * 2  # Assume average 2 years per role
        if total_years <= 2:
            return "0-2 years"
        elif total_years <= 5:
            return "2-5 years"
        elif total_years <= 10:
            return "5-10 years"
        else:
            return "10+ years"

    def _determine_industry(self, profile_data: BrightDataSnapshotRecord) -> str:
        """Determine industry from profile data."""
        # Check current company industry
        if profile_data.current_company and isinstance(
            profile_data.current_company, dict
        ):
            industry = profile_data.current_company.get("industry", "")
            if industry:
                return industry

        # Check experience for industry patterns
        if profile_data.experience:
            for exp in profile_data.experience:
                company = exp.get("company", "").lower()
                if any(
                    word in company
                    for word in ["tech", "software", "ai", "machine learning"]
                ):
                    return "Technology"
                elif any(
                    word in company for word in ["energy", "renewable", "solar", "wind"]
                ):
                    return "Renewable Energy"
                elif any(word in company for word in ["finance", "banking", "fintech"]):
                    return "Financial Services"
                elif any(word in company for word in ["health", "medical", "biotech"]):
                    return "Healthcare"

        return "Unknown"

    def _parse_skills_advanced(self, skills_text: str) -> List[str]:
        """Parse and clean extracted skills with advanced processing."""
        if not skills_text:
            return []

        try:
            # Split by comma and clean
            skills = []
            for skill in skills_text.split(","):
                skill = skill.strip()
                if skill and len(skill) > 1:
                    # Remove common prefixes/suffixes and clean
                    skill = skill.replace("•", "").strip()
                    skill = skill.replace("-", "").strip()
                    skill = skill.replace("•", "").strip()
                    skill = skill.replace(":", "").strip()
                    skill = skill.replace(";", "").strip()

                    # Skip if too generic or non-professional
                    if skill.lower() in [
                        "management",
                        "leadership",
                        "communication",
                        "teamwork",
                        "problem solving",
                        "problem-solving",
                    ]:
                        continue

                    # Skip if too short or too long
                    if len(skill) < 2 or len(skill) > 50:
                        continue

                    skills.append(skill)

            # Remove duplicates while preserving order
            seen = set()
            unique_skills = []
            for skill in skills:
                skill_lower = skill.lower()
                if skill_lower not in seen:
                    seen.add(skill_lower)
                    unique_skills.append(skill)

            # Limit to reasonable number of skills
            return unique_skills[:40]

        except Exception as e:
            self.logger.error(f"Advanced skills parsing failed: {str(e)}")
            return []

    def _extract_basic_skills_from_context(
        self, profile_data: BrightDataSnapshotRecord
    ) -> List[str]:
        """Extract basic skills from available context when LLM fails."""
        skills = []

        # Extract skills from position/title
        if profile_data.position:
            position_lower = profile_data.position.lower()

            # Leadership skills
            if any(
                word in position_lower
                for word in [
                    "ceo",
                    "chief",
                    "executive",
                    "director",
                    "manager",
                    "founder",
                    "co-founder",
                ]
            ):
                skills.extend([
                    "executive leadership",
                    "strategic planning",
                    "business strategy",
                ])

            # Technical skills based on position
            if any(
                word in position_lower
                for word in ["engineer", "developer", "programmer", "software"]
            ):
                skills.extend(["software development", "technical expertise"])

            # Business skills
            if any(
                word in position_lower
                for word in ["founder", "entrepreneur", "business", "startup"]
            ):
                skills.extend([
                    "entrepreneurship",
                    "business development",
                    "startup management",
                ])

        # Extract skills from company name
        if profile_data.current_company and isinstance(
            profile_data.current_company, dict
        ):
            company_name = profile_data.current_company.get("name", "").lower()
            if "energy" in company_name or "renewable" in company_name:
                skills.extend(["energy sector", "renewable energy"])
            if "tech" in company_name or "technology" in company_name:
                skills.extend(["technology", "innovation"])

        # Extract skills from location
        if profile_data.location:
            location_lower = profile_data.location.lower()
            if "thailand" in location_lower:
                skills.append("thai market")
            if "singapore" in location_lower:
                skills.append("southeast asia market")

        # Remove duplicates and return
        return list(dict.fromkeys(skills))

    async def extract_skills_batch(
        self, profiles: List[BrightDataSnapshotRecord]
    ) -> Dict[str, List[str]]:
        """
        Extract skills from multiple profiles in batch using sophisticated analysis.

        Args:
            profiles: List of BrightData snapshot records

        Returns:
            Dictionary mapping profile IDs to skills lists
        """
        results = {}

        for profile in profiles:
            try:
                skills = await self.extract_skills_from_profile(profile)
                profile_id = profile.linkedin_id or profile.id or profile.name
                results[profile_id] = skills
            except Exception as e:
                self.logger.error(f"Batch skills extraction failed for profile: {e}")
                profile_id = profile.linkedin_id or profile.id or profile.name
                results[profile_id] = []

        return results


# Global service instance
_skills_service: Optional[SkillsExtractionService] = None


async def get_skills_extraction_service() -> SkillsExtractionService:
    """Get or create the global skills extraction service instance."""
    global _skills_service

    if _skills_service is None:
        _skills_service = SkillsExtractionService()

    return _skills_service
