"use client"

import React from 'react';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';

interface CompactBlockProps {
  type: 'section' | 'question';
  title: string;
  label?: string;
  required?: boolean;
  repeatable?: boolean;
  isCollapsed: boolean;
  onToggle: () => void;
  onExpand: () => void;
  children: React.ReactNode;
  className?: string;
}

export function CompactBlock({
  type,
  title,
  label,
  required,
  repeatable,
  isCollapsed,
  onToggle,
  onExpand,
  children,
  className,
}: CompactBlockProps) {
  const displayTitle = label || title;
  const typeLabel = type === 'section' ? 'Section' : 'Question';

  return (
    <div className={cn("group relative", className)}>
      {isCollapsed ? (
        <div
          className="flex cursor-pointer items-center justify-between rounded-md border bg-card px-4 py-3 shadow-sm transition-all duration-300 ease-in-out hover:bg-accent/50"
          onClick={onExpand}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              onExpand();
            }
          }}
          role="button"
          tabIndex={0}
          aria-expanded={!isCollapsed}
        >
          <div className="flex min-w-0 flex-1 items-center gap-3">
            <span className="whitespace-nowrap text-xs font-semibold text-muted-foreground">
              {typeLabel}
            </span>
            <span 
              className="truncate font-medium" 
              title={displayTitle}
            >
              {displayTitle}
            </span>
          </div>
          <div className="ml-4 flex items-center gap-2">
            {typeof required !== "undefined" && (
              <Badge 
                variant="outline" 
                className={cn(
                  "text-xs",
                  required ? "border-green-200 bg-green-100 text-green-800" : "bg-muted"
                )}
              >
                {required ? "Required" : "Optional"}
              </Badge>
            )}
            {typeof repeatable !== "undefined" && (
              <Badge 
                variant="outline"
                className={cn(
                  "text-xs",
                  repeatable ? "border-blue-200 bg-blue-100 text-blue-800" : "bg-muted"
                )}
              >
                {repeatable ? "Repeatable" : "Single"}
              </Badge>
            )}
            <button
              className="ml-2 rounded-full p-1 hover:bg-accent focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              onClick={(e) => {
                e.stopPropagation();
                onToggle();
              }}
              aria-label={isCollapsed ? "Expand" : "Collapse"}
            >
              <ChevronDown 
                className={cn(
                  "size-4 text-muted-foreground transition-transform duration-300",
                  isCollapsed ? "" : "rotate-180"
                )} 
              />
            </button>
          </div>
        </div>
      ) : (
        <div 
          className={cn(
            "transition-all duration-300 ease-in-out",
            "origin-top scale-100 opacity-100",
            "max-h-[2000px] overflow-hidden"
          )}
        >
          {children}
        </div>
      )}
    </div>
  );
} 