# Sharing Framework

A comprehensive framework for sharing resources via links, embeds, and QR codes.

## Features

- Multiple sharing methods (link, embed, QR code)
- Configurable security settings
- Domain restrictions for embeds
- Analytics tracking
- Expiration support

## Usage

### Creating a Sharing Configuration

```python
from app.services.factory import get_sharing_service
from app.services.sharing.interfaces import SharingType, EmbedType

async def create_sharing_example():
    sharing_service = await get_sharing_service()
    
    # Create sharing configuration
    config = await sharing_service.create_sharing_config(
        resource_type="form",
        resource_id="form123",
        org_id="org456",
        enabled=True,
        sharing_types=[SharingType.LINK, SharingType.EMBED, SharingType.QR_CODE],
        allowed_domains=["example.com", "trusted-partner.com"],
        embed_type=EmbedType.INLINE,
        custom_styles={"theme": "light", "primaryColor": "#007bff"},
        tracking_enabled=True
    )
    
    return config
```

### Generating Sharing Links

```python
async def generate_link_example(config_id, user_id):
    sharing_service = await get_sharing_service()
    
    # Generate sharing link
    link = await sharing_service.generate_sharing_link(
        config_id=config_id,
        created_by=user_id,
        expires_at=datetime.now() + timedelta(days=30),
        metadata={"source": "dashboard", "campaign": "spring2023"}
    )
    
    return link
```

### Generating Embed Codes

```python
async def generate_embed_example(config_id, user_id):
    sharing_service = await get_sharing_service()
    
    # Generate embed code
    embed = await sharing_service.generate_embed_code(
        config_id=config_id,
        created_by=user_id,
        embed_type=EmbedType.MODAL,
        custom_styles={"width": "100%", "height": "500px"}
    )
    
    return embed
```

### Generating QR Codes

```python
async def generate_qr_example(config_id, user_id):
    sharing_service = await get_sharing_service()
    
    # Generate QR code
    qr = await sharing_service.generate_qr_code(
        config_id=config_id,
        created_by=user_id,
        size=300,
        error_correction_level="M"
    )
    
    return qr
```

### Tracking Views

```python
async def track_view_example(sharing_id, sharing_type):
    sharing_service = await get_sharing_service()
    
    # Track a view
    await sharing_service.track_view(
        sharing_id=sharing_id,
        sharing_type=sharing_type,
        metadata={"referrer": "email", "user_agent": "..."}
    )
```

### Getting Sharing Statistics

```python
async def get_stats_example(resource_type, resource_id):
    sharing_service = await get_sharing_service()
    
    # Get sharing statistics
    stats = await sharing_service.get_sharing_stats(
        resource_type=resource_type,
        resource_id=resource_id
    )
    
    return stats
```

## Security Considerations

- All sharing tokens are cryptographically secure
- Embed codes can be restricted to specific domains
- Sharing links and QR codes can have expiration dates
- All views are tracked for audit purposes

## Architecture

The sharing framework consists of:

1. **Sharing Configuration** - Settings for how a resource can be shared
2. **Sharing Links** - Unique URLs for accessing shared resources
3. **Embed Codes** - HTML snippets for embedding resources in websites
4. **QR Codes** - Visual codes for accessing resources from mobile devices
5. **Analytics** - Tracking of views and usage

The framework is designed to be extensible and can be used for any type of resource in the system.
