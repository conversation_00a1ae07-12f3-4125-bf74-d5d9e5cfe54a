"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { XCircle, CheckCircle, Shield, AlertTriangle } from "lucide-react"
import { cn } from "@/lib/utils"
import { visualRetreat, mobileRetreat } from "@/lib/utils/responsive"
import { ExclusionFilterResult } from "@/lib/types/deal"

interface ExclusionDisplayProps {
  exclusionResult: ExclusionFilterResult
  className?: string
}

export function ExclusionDisplay({ exclusionResult, className }: ExclusionDisplayProps) {
  const isExcluded = exclusionResult.excluded

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Alert className={cn(
        "rounded-2xl border-2 shadow-sm",
        visualRetreat.card.base,
        isExcluded
          ? "border-red-200 bg-red-50/80 backdrop-blur-sm"
          : "border-green-200 bg-green-50/80 backdrop-blur-sm"
      )}>
        <div className="flex items-start gap-4 p-6">
          {isExcluded ? (
            <XCircle className="mt-1 size-6 shrink-0 text-red-600 md:size-8" />
          ) : (
            <CheckCircle className="mt-1 size-6 shrink-0 text-green-600 md:size-8" />
          )}

          <div className="flex-1 space-y-4">
            <div>
              <h3 className={cn(
                "text-lg font-bold leading-tight md:text-xl",
                isExcluded ? "text-red-900" : "text-green-900"
              )}>
                {isExcluded ? "Excluded from Thesis" : "Passed Exclusion Filters"}
              </h3>

              {exclusionResult.filter_name && (
                <p className={cn(
                  "mt-2 text-sm font-medium md:text-base",
                  isExcluded ? "text-red-700" : "text-green-700"
                )}>
                  Filter: {exclusionResult.filter_name}
                </p>
              )}
            </div>

            {exclusionResult.reason && (
              <div className={cn(
                "rounded-xl border p-4",
                isExcluded
                  ? "border-red-200 bg-red-100 text-red-800"
                  : "border-green-200 bg-green-100 text-green-800"
              )}>
                <p className="mb-2 text-sm font-medium md:text-base">
                  {isExcluded ? "Exclusion Reason:" : "Filter Status:"}
                </p>
                <p className="text-sm leading-relaxed md:text-base">{exclusionResult.reason}</p>
              </div>
            )}

            {isExcluded && (
              <div className="flex items-start gap-3 rounded-xl border border-amber-200 bg-amber-50 p-4">
                <AlertTriangle className="mt-0.5 size-5 shrink-0 text-amber-600" />
                <div className="text-sm md:text-base">
                  <p className="mb-1 font-medium text-amber-800">Impact on Scoring</p>
                  <p className="leading-relaxed text-amber-700">
                    This deal has been excluded by the filter and will not show thesis match scores.
                    All scoring analysis is hidden for excluded deals.
                  </p>
                </div>
              </div>
            )}

            <div className="flex flex-wrap items-center gap-3">
              <Badge
                variant={isExcluded ? "destructive" : "default"}
                className={cn(
                  "rounded-full px-3 py-2 text-sm font-semibold",
                  isExcluded
                    ? "border-red-200 bg-red-100 text-red-800"
                    : "border-green-200 bg-green-100 text-green-800"
                )}
              >
                <Shield className="mr-2 size-4" />
                {isExcluded ? "Excluded" : "Approved"}
              </Badge>

              {exclusionResult.filter_id && (
                <Badge variant="outline" className="rounded-full px-3 py-1 text-sm">
                  ID: {exclusionResult.filter_id}
                </Badge>
              )}
            </div>
          </div>
        </div>
      </Alert>
    </motion.div>
  )
}
