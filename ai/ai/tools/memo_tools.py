"""
Deal memo and executive summary generation tools for TractionX AI Agent system.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field
from bson import ObjectId

from ai.tools.base import BaseTool, ToolInput, ToolOutput, tool
from ai.tools.document_tools import PitchBookData, ExtractedContent
from ai.tools.vector_tools import VectorSearchTool, VectorSearchInput
from ai.models import AgentContext
from ai.config import settings

# OpenAI imports for LLM functionality
from openai import AsyncOpenAI
import json

logger = logging.getLogger(__name__)


class CompanyContext(BaseModel):
    """Company context data for memo generation."""
    company_id: Optional[str] = Field(None, description="Company ID")
    company_name: Optional[str] = Field(None, description="Company name")
    form_submissions: List[Dict[str, Any]] = Field(default_factory=list, description="Form submission data")
    vector_search_results: List[Dict[str, Any]] = Field(default_factory=list, description="Vector search results")
    pitch_book_data: Optional[PitchBookData] = Field(None, description="Extracted pitch book data")
    external_data: Dict[str, Any] = Field(default_factory=dict, description="External enrichment data")


class MemoSection(BaseModel):
    """A section of the deal memo."""
    title: str = Field(..., description="Section title")
    content: str = Field(..., description="Section content")
    key_points: List[str] = Field(default_factory=list, description="Key bullet points")
    score: Optional[float] = Field(None, description="Section score (0-10)")
    confidence: Optional[float] = Field(None, description="Confidence level (0-1)")


class DealMemoInput(ToolInput):
    """Input for deal memo generation."""
    company_context: CompanyContext = Field(..., description="Company context data")
    memo_style: str = Field(default="analyst", description="Memo style: analyst, executive, deep")
    include_scoring: bool = Field(default=True, description="Include scoring analysis")
    include_risks: bool = Field(default=True, description="Include risk assessment")
    include_recommendations: bool = Field(default=True, description="Include investment recommendations")
    custom_sections: Optional[List[str]] = Field(None, description="Custom sections to include")


class DealMemoOutput(ToolOutput):
    """Output from deal memo generation."""
    memo_title: str = Field(..., description="Memo title")
    executive_summary: str = Field(..., description="Executive summary")
    sections: List[MemoSection] = Field(default_factory=list, description="Memo sections")
    overall_score: Optional[float] = Field(None, description="Overall investment score (0-10)")
    recommendation: Optional[str] = Field(None, description="Investment recommendation")
    key_risks: List[str] = Field(default_factory=list, description="Key risk factors")
    key_opportunities: List[str] = Field(default_factory=list, description="Key opportunities")
    generation_time: float = Field(default=0.0, description="Generation time in seconds")


class ExecutiveSummaryInput(ToolInput):
    """Input for executive summary generation."""
    company_context: CompanyContext = Field(..., description="Company context data")
    focus_areas: List[str] = Field(default_factory=list, description="Areas to focus on")
    max_length: int = Field(default=500, description="Maximum length in words")


class ExecutiveSummaryOutput(ToolOutput):
    """Output from executive summary generation."""
    summary: str = Field(..., description="Executive summary")
    key_highlights: List[str] = Field(default_factory=list, description="Key highlights")
    investment_thesis: Optional[str] = Field(None, description="Investment thesis")
    recommendation: Optional[str] = Field(None, description="Investment recommendation")
    generation_time: float = Field(default=0.0, description="Generation time in seconds")


@tool(
    name="deal_memo_generator",
    description="Generate comprehensive investment deal memos with analysis and recommendations",
    category="memo",
    tags=["memo", "deal", "analysis", "investment", "llm", "scoring"]
)
class DealMemoGeneratorTool(BaseTool):
    """
    Tool for generating comprehensive investment deal memos.

    Features:
    - Context retrieval from Qdrant and form submissions
    - LLM-powered analysis and memo generation
    - Structured output with multiple sections
    - Scoring and risk assessment
    - Multiple memo styles (analyst, executive, deep)
    """

    def __init__(self):
        super().__init__()
        self.vector_search_tool = VectorSearchTool()
        
        # Initialize OpenAI client
        self.openai_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
    
    async def execute(
        self,
        input_data: DealMemoInput,
        context: AgentContext
    ) -> DealMemoOutput:
        """Generate deal memo."""
        
        start_time = datetime.now()
        logger.info(f"Starting deal memo generation for: {input_data.company_context.company_name}")
        
        try:
            # Enrich company context with vector search
            enriched_context = await self._enrich_company_context(
                input_data.company_context, 
                context
            )
            
            # Generate memo sections based on style
            sections = await self._generate_memo_sections(
                enriched_context,
                input_data,
                context
            )
            
            # Generate executive summary
            executive_summary = await self._generate_executive_summary(
                enriched_context,
                sections,
                context
            )
            
            # Calculate overall score if scoring is enabled
            overall_score = None
            if input_data.include_scoring:
                overall_score = self._calculate_overall_score(sections)
            
            # Generate recommendation
            recommendation = None
            if input_data.include_recommendations:
                recommendation = await self._generate_recommendation(
                    enriched_context,
                    sections,
                    overall_score,
                    context
                )
            
            # Extract key risks and opportunities
            key_risks, key_opportunities = self._extract_key_points(sections)
            
            # Generate memo title
            memo_title = self._generate_memo_title(enriched_context, input_data.memo_style)
            
            generation_time = (datetime.now() - start_time).total_seconds()
            
            return DealMemoOutput(
                memo_title=memo_title,
                executive_summary=executive_summary,
                sections=sections,
                overall_score=overall_score,
                recommendation=recommendation,
                key_risks=key_risks,
                key_opportunities=key_opportunities,
                generation_time=generation_time,
                success=True
            )
            
        except Exception as e:
            logger.error(f"Deal memo generation failed: {e}")
            return DealMemoOutput(
                memo_title="Deal Memo Generation Failed",
                executive_summary="Failed to generate memo due to an error.",
                success=False,
                error=str(e),
                generation_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def _enrich_company_context(
        self,
        company_context: CompanyContext,
        context: AgentContext
    ) -> CompanyContext:
        """Enrich company context with vector search results."""
        logger.debug("Enriching company context with vector search")
        
        try:
            if company_context.company_name:
                # Search for relevant company information
                search_input = VectorSearchInput(
                    query=f"company information about {company_context.company_name}",
                    limit=10,
                    include_metadata=True
                )
                
                search_result = await self.vector_search_tool.execute(search_input, context)
                
                if search_result.success and search_result.data:
                    company_context.vector_search_results = search_result.data.get("results", [])
            
            return company_context
            
        except Exception as e:
            logger.warning(f"Failed to enrich company context: {e}")
            return company_context
    
    async def _generate_memo_sections(
        self,
        company_context: CompanyContext,
        input_data: DealMemoInput,
        context: AgentContext
    ) -> List[MemoSection]:
        """Generate memo sections based on style and requirements."""
        logger.debug(f"Generating memo sections for style: {input_data.memo_style}")
        
        # Define sections based on memo style
        if input_data.memo_style == "executive":
            section_templates = [
                "Company Overview",
                "Investment Highlights",
                "Key Risks",
                "Recommendation"
            ]
        elif input_data.memo_style == "deep":
            section_templates = [
                "Company Overview",
                "Founding Team Analysis",
                "Market Opportunity",
                "Product & Technology",
                "Business Model",
                "Traction & Metrics",
                "Competitive Landscape",
                "Financial Analysis",
                "Risk Assessment",
                "Investment Thesis",
                "Recommendation"
            ]
        else:  # analyst style (default)
            section_templates = [
                "Company Overview",
                "Founding Team",
                "Market & Opportunity",
                "Traction",
                "Business Model",
                "Risks & Challenges",
                "Investment Recommendation"
            ]
        
        # Add custom sections if specified
        if input_data.custom_sections:
            section_templates.extend(input_data.custom_sections)
        
        # Generate each section
        sections = []
        for section_title in section_templates:
            try:
                section = await self._generate_section(
                    section_title,
                    company_context,
                    input_data,
                    context
                )
                sections.append(section)
                
                # Small delay to avoid rate limiting
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.warning(f"Failed to generate section '{section_title}': {e}")
                # Add fallback section
                sections.append(MemoSection(
                    title=section_title,
                    content=f"Unable to generate {section_title} section due to processing error.",
                    key_points=[],
                    confidence=0.0
                ))
        
        return sections

    async def _generate_section(
        self,
        section_title: str,
        company_context: CompanyContext,
        input_data: DealMemoInput,
        context: AgentContext
    ) -> MemoSection:
        """Generate a specific memo section."""
        logger.debug(f"Generating section: {section_title}")

        # Prepare context data for the section
        context_data = self._prepare_section_context(company_context, section_title)

        # Create section-specific prompt
        prompt = self._create_section_prompt(section_title, context_data, input_data.memo_style)

        try:
            # Call OpenAI for section generation
            response = await self.openai_client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "You are an expert investment analyst writing professional deal memos."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=800,
                temperature=0.3,
                timeout=60
            )

            if response.choices and response.choices[0].message:
                content = response.choices[0].message.content.strip()

                # Extract key points and score if available
                key_points, score = self._parse_section_content(content)

                return MemoSection(
                    title=section_title,
                    content=content,
                    key_points=key_points,
                    score=score,
                    confidence=0.8  # Default confidence
                )
            else:
                return self._create_fallback_section(section_title, company_context)

        except Exception as e:
            logger.warning(f"Failed to generate section '{section_title}': {e}")
            return self._create_fallback_section(section_title, company_context)

    def _prepare_section_context(
        self,
        company_context: CompanyContext,
        section_title: str
    ) -> str:
        """Prepare relevant context data for a specific section."""
        context_parts = []

        # Add company basic info
        if company_context.company_name:
            context_parts.append(f"Company: {company_context.company_name}")

        # Add pitch book data if available
        if company_context.pitch_book_data:
            pitch_data = company_context.pitch_book_data

            if section_title.lower() in ["company overview", "overview"]:
                if pitch_data.company_name:
                    context_parts.append(f"Company Name: {pitch_data.company_name}")
                if pitch_data.tagline:
                    context_parts.append(f"Tagline: {pitch_data.tagline}")
                if pitch_data.business_model:
                    context_parts.append(f"Business Model: {pitch_data.business_model}")

            elif section_title.lower() in ["founding team", "team", "founders"]:
                if pitch_data.founders:
                    context_parts.append(f"Founders: {', '.join(pitch_data.founders)}")
                if pitch_data.team_size:
                    context_parts.append(f"Team Size: {pitch_data.team_size}")

            elif section_title.lower() in ["market", "opportunity", "market & opportunity"]:
                if pitch_data.market_size:
                    context_parts.append(f"Market Size: {pitch_data.market_size}")

            elif section_title.lower() in ["traction", "metrics"]:
                if pitch_data.traction:
                    context_parts.append(f"Traction: {', '.join(pitch_data.traction)}")

            elif section_title.lower() in ["financial", "funding", "investment"]:
                if pitch_data.funding_ask:
                    context_parts.append(f"Funding Ask: {pitch_data.funding_ask}")
                if pitch_data.use_of_funds:
                    context_parts.append(f"Use of Funds: {', '.join(pitch_data.use_of_funds)}")
                if pitch_data.financial_projections:
                    context_parts.append(f"Financial Projections: {pitch_data.financial_projections}")

        # Add form submission data
        if company_context.form_submissions:
            context_parts.append("Form Submission Data:")
            for submission in company_context.form_submissions[:3]:  # Limit to first 3
                if isinstance(submission, dict):
                    for key, value in submission.items():
                        if value and len(str(value)) < 200:
                            context_parts.append(f"  {key}: {value}")

        # Add vector search results
        if company_context.vector_search_results:
            context_parts.append("Additional Context:")
            for result in company_context.vector_search_results[:5]:  # Limit to top 5
                if isinstance(result, dict) and result.get("content"):
                    content = str(result["content"])[:300]  # Limit length
                    context_parts.append(f"  {content}")

        return "\n".join(context_parts)

    def _create_section_prompt(
        self,
        section_title: str,
        context_data: str,
        memo_style: str
    ) -> str:
        """Create a prompt for generating a specific section."""

        style_instructions = {
            "executive": "Write in a concise, high-level style suitable for senior executives. Focus on key insights and strategic implications.",
            "analyst": "Write in a detailed analytical style with specific metrics and evidence. Include both opportunities and risks.",
            "deep": "Write in a comprehensive, thorough style with detailed analysis and supporting evidence. Include nuanced insights and implications."
        }

        style_instruction = style_instructions.get(memo_style, style_instructions["analyst"])

        return f"""Write a professional investment memo section titled "{section_title}" based on the following company information.

Style: {style_instruction}

Company Information:
{context_data}

Requirements:
1. Write 2-4 paragraphs of analysis
2. Include specific details and evidence where available
3. Maintain objectivity while highlighting key insights
4. If scoring is relevant, include a score out of 10 at the end in format "Score: X/10"
5. Focus on investment-relevant insights

Section: {section_title}

Write the section content now:"""

    def _parse_section_content(self, content: str) -> tuple[List[str], Optional[float]]:
        """Parse section content to extract key points and score."""
        key_points = []
        score = None

        # Extract score if present
        import re
        score_match = re.search(r'Score:\s*(\d+(?:\.\d+)?)/10', content, re.IGNORECASE)
        if score_match:
            try:
                score = float(score_match.group(1))
            except ValueError:
                pass

        # Extract key points (simple heuristic - sentences with strong indicators)
        sentences = content.split('.')
        for sentence in sentences:
            sentence = sentence.strip()
            if any(indicator in sentence.lower() for indicator in [
                'key', 'important', 'significant', 'notable', 'strong', 'weak', 'risk', 'opportunity'
            ]) and len(sentence) > 20:
                key_points.append(sentence + '.')

        return key_points[:5], score  # Limit to 5 key points

    def _create_fallback_section(
        self,
        section_title: str,
        company_context: CompanyContext
    ) -> MemoSection:
        """Create a basic fallback section when generation fails."""
        company_name = company_context.company_name or "the company"

        fallback_content = {
            "Company Overview": f"Analysis of {company_name} is pending. Additional information is needed to provide a comprehensive overview.",
            "Founding Team": f"Information about {company_name}'s founding team and leadership is being evaluated.",
            "Market & Opportunity": f"Market analysis for {company_name} requires additional research and validation.",
            "Traction": f"Traction metrics and performance indicators for {company_name} are under review.",
            "Business Model": f"The business model and revenue strategy of {company_name} needs further analysis.",
            "Risks & Challenges": f"Risk assessment for {company_name} is in progress and will be updated with additional data.",
            "Investment Recommendation": f"Investment recommendation for {company_name} is pending completion of full analysis."
        }

        content = fallback_content.get(section_title, f"Analysis of {section_title} for {company_name} is in progress.")

        return MemoSection(
            title=section_title,
            content=content,
            key_points=[],
            confidence=0.1
        )

    async def _generate_executive_summary(
        self,
        company_context: CompanyContext,
        sections: List[MemoSection],
        context: AgentContext
    ) -> str:
        """Generate executive summary from memo sections."""
        logger.debug("Generating executive summary")

        try:
            # Prepare summary of all sections
            sections_summary = []
            for section in sections:
                if section.content and len(section.content) > 50:
                    summary = section.content[:300] + "..." if len(section.content) > 300 else section.content
                    sections_summary.append(f"{section.title}: {summary}")

            sections_text = "\n\n".join(sections_summary)
            company_name = company_context.company_name or "the company"

            prompt = f"""Create a concise executive summary for an investment memo about {company_name}.

Based on the following analysis sections:

{sections_text}

Write a 2-3 paragraph executive summary that:
1. Provides a high-level overview of the investment opportunity
2. Highlights the most compelling aspects
3. Notes key risks or concerns
4. Gives a clear sense of the investment potential

Keep it professional, objective, and suitable for senior decision-makers."""

            response = await self.openai_client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "You are an expert investment analyst writing executive summaries."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.3,
                timeout=60
            )

            if response.choices and response.choices[0].message:
                return response.choices[0].message.content.strip()
            else:
                return self._create_fallback_executive_summary(company_context)

        except Exception as e:
            logger.warning(f"Failed to generate executive summary: {e}")
            return self._create_fallback_executive_summary(company_context)

    def _create_fallback_executive_summary(self, company_context: CompanyContext) -> str:
        """Create fallback executive summary."""
        company_name = company_context.company_name or "the company"
        return f"Investment analysis of {company_name} is in progress. This executive summary will be updated upon completion of the full evaluation process."

    def _calculate_overall_score(self, sections: List[MemoSection]) -> float:
        """Calculate overall investment score from section scores."""
        scores = [section.score for section in sections if section.score is not None]
        if not scores:
            return 5.0  # Default neutral score

        # Weight different sections differently
        section_weights = {
            "Company Overview": 1.0,
            "Founding Team": 1.5,
            "Market & Opportunity": 1.3,
            "Traction": 1.4,
            "Business Model": 1.2,
            "Risks & Challenges": 0.8,  # Lower weight for risks
            "Investment Recommendation": 1.1
        }

        weighted_sum = 0.0
        total_weight = 0.0

        for section in sections:
            if section.score is not None:
                weight = section_weights.get(section.title, 1.0)
                weighted_sum += section.score * weight
                total_weight += weight

        if total_weight > 0:
            return round(weighted_sum / total_weight, 1)
        else:
            return round(sum(scores) / len(scores), 1)

    async def _generate_recommendation(
        self,
        company_context: CompanyContext,
        sections: List[MemoSection],
        overall_score: Optional[float],
        context: AgentContext
    ) -> str:
        """Generate investment recommendation."""
        logger.debug("Generating investment recommendation")

        try:
            company_name = company_context.company_name or "the company"
            score_text = f" (Overall Score: {overall_score}/10)" if overall_score else ""

            # Extract key insights from sections
            key_insights = []
            for section in sections:
                if section.key_points:
                    key_insights.extend(section.key_points[:2])  # Top 2 points per section

            insights_text = "\n".join(key_insights[:8])  # Limit to 8 total insights

            prompt = f"""Based on the investment analysis of {company_name}{score_text}, provide a clear investment recommendation.

Key insights from analysis:
{insights_text}

Provide a recommendation that:
1. States clearly whether to invest, pass, or continue evaluation
2. Explains the primary reasoning (2-3 key factors)
3. Notes any important conditions or next steps
4. Is suitable for investment committee decision-making

Keep it concise but decisive."""

            response = await self.openai_client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "You are an expert investment analyst providing investment recommendations."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=300,
                temperature=0.2,
                timeout=60
            )

            if response.choices and response.choices[0].message:
                return response.choices[0].message.content.strip()
            else:
                return self._create_fallback_recommendation(overall_score)

        except Exception as e:
            logger.warning(f"Failed to generate recommendation: {e}")
            return self._create_fallback_recommendation(overall_score)

    def _create_fallback_recommendation(self, overall_score: Optional[float]) -> str:
        """Create fallback recommendation based on score."""
        if overall_score is None:
            return "Continue evaluation - additional analysis required to make investment recommendation."
        elif overall_score >= 7.5:
            return "Recommend investment - strong opportunity with compelling fundamentals."
        elif overall_score >= 6.0:
            return "Continue evaluation - promising opportunity requiring additional due diligence."
        elif overall_score >= 4.0:
            return "Proceed with caution - mixed signals requiring careful evaluation of risks."
        else:
            return "Pass - significant concerns outweigh potential opportunities."

    def _extract_key_points(self, sections: List[MemoSection]) -> tuple[List[str], List[str]]:
        """Extract key risks and opportunities from sections."""
        risks = []
        opportunities = []

        for section in sections:
            if "risk" in section.title.lower() or "challenge" in section.title.lower():
                risks.extend(section.key_points)
            else:
                # Look for risk/opportunity indicators in key points
                for point in section.key_points:
                    point_lower = point.lower()
                    if any(risk_word in point_lower for risk_word in ["risk", "concern", "challenge", "weakness", "threat"]):
                        risks.append(point)
                    elif any(opp_word in point_lower for opp_word in ["opportunity", "strength", "advantage", "potential", "growth"]):
                        opportunities.append(point)

        return risks[:5], opportunities[:5]  # Limit to top 5 each

    def _generate_memo_title(self, company_context: CompanyContext, memo_style: str) -> str:
        """Generate memo title."""
        company_name = company_context.company_name or "Investment Opportunity"

        style_prefixes = {
            "executive": "Executive Brief:",
            "analyst": "Investment Analysis:",
            "deep": "Comprehensive Analysis:"
        }

        prefix = style_prefixes.get(memo_style, "Investment Memo:")
        return f"{prefix} {company_name}"


@tool(
    name="executive_summary",
    description="Generate concise executive summaries for investment opportunities",
    category="memo",
    tags=["summary", "executive", "investment", "llm", "brief"]
)
class ExecutiveSummaryTool(BaseTool):
    """
    Tool for generating concise executive summaries.

    Features:
    - Condensed analysis for decision-makers
    - Key highlights and recommendations
    - Customizable focus areas and length
    - Investment thesis generation
    """

    def __init__(self):
        super().__init__()
        self.vector_search_tool = VectorSearchTool()

        # Initialize OpenAI client
        self.openai_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)

    async def execute(
        self,
        input_data: ExecutiveSummaryInput,
        context: AgentContext
    ) -> ExecutiveSummaryOutput:
        """Generate executive summary."""

        start_time = datetime.now()
        logger.info(f"Starting executive summary generation for: {input_data.company_context.company_name}")

        try:
            # Enrich company context
            enriched_context = await self._enrich_company_context(
                input_data.company_context,
                context
            )

            # Generate summary
            summary = await self._generate_summary(
                enriched_context,
                input_data,
                context
            )

            # Extract key highlights
            key_highlights = self._extract_highlights(enriched_context)

            # Generate investment thesis
            investment_thesis = await self._generate_investment_thesis(
                enriched_context,
                context
            )

            # Generate recommendation
            recommendation = await self._generate_summary_recommendation(
                enriched_context,
                summary,
                context
            )

            generation_time = (datetime.now() - start_time).total_seconds()

            return ExecutiveSummaryOutput(
                summary=summary,
                key_highlights=key_highlights,
                investment_thesis=investment_thesis,
                recommendation=recommendation,
                generation_time=generation_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Executive summary generation failed: {e}")
            return ExecutiveSummaryOutput(
                summary="Failed to generate executive summary due to an error.",
                success=False,
                error=str(e),
                generation_time=(datetime.now() - start_time).total_seconds()
            )

    async def _enrich_company_context(
        self,
        company_context: CompanyContext,
        context: AgentContext
    ) -> CompanyContext:
        """Enrich company context with vector search results."""
        logger.debug("Enriching company context for executive summary")

        try:
            if company_context.company_name:
                # Search for relevant company information
                search_input = VectorSearchInput(
                    query=f"executive summary information about {company_context.company_name}",
                    limit=5,
                    include_metadata=True
                )

                search_result = await self.vector_search_tool.execute(search_input, context)

                if search_result.success and search_result.data:
                    company_context.vector_search_results = search_result.data.get("results", [])

            return company_context

        except Exception as e:
            logger.warning(f"Failed to enrich company context: {e}")
            return company_context

    async def _generate_summary(
        self,
        company_context: CompanyContext,
        input_data: ExecutiveSummaryInput,
        context: AgentContext
    ) -> str:
        """Generate the main executive summary."""
        logger.debug("Generating executive summary content")

        try:
            # Prepare context data
            context_data = self._prepare_summary_context(company_context, input_data.focus_areas)

            # Create prompt
            prompt = self._create_summary_prompt(
                company_context.company_name or "the company",
                context_data,
                input_data.focus_areas,
                input_data.max_length
            )

            response = await self.openai_client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "You are an expert investment analyst writing executive summaries for senior decision-makers."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=min(input_data.max_length * 2, 1000),  # Rough token estimate
                temperature=0.3,
                timeout=60
            )

            if response.choices and response.choices[0].message:
                return response.choices[0].message.content.strip()
            else:
                return self._create_fallback_summary(company_context)

        except Exception as e:
            logger.warning(f"Failed to generate summary: {e}")
            return self._create_fallback_summary(company_context)

    def _prepare_summary_context(
        self,
        company_context: CompanyContext,
        focus_areas: List[str]
    ) -> str:
        """Prepare context data for summary generation."""
        context_parts = []

        # Add company basic info
        if company_context.company_name:
            context_parts.append(f"Company: {company_context.company_name}")

        # Add pitch book data if available
        if company_context.pitch_book_data:
            pitch_data = company_context.pitch_book_data

            if pitch_data.company_name:
                context_parts.append(f"Company Name: {pitch_data.company_name}")
            if pitch_data.tagline:
                context_parts.append(f"Tagline: {pitch_data.tagline}")
            if pitch_data.business_model:
                context_parts.append(f"Business Model: {pitch_data.business_model}")
            if pitch_data.founders:
                context_parts.append(f"Founders: {', '.join(pitch_data.founders)}")
            if pitch_data.funding_ask:
                context_parts.append(f"Funding Ask: {pitch_data.funding_ask}")
            if pitch_data.traction:
                context_parts.append(f"Traction: {', '.join(pitch_data.traction)}")

        # Add form submission highlights
        if company_context.form_submissions:
            context_parts.append("Key Information:")
            for submission in company_context.form_submissions[:2]:  # Limit to first 2
                if isinstance(submission, dict):
                    for key, value in submission.items():
                        if value and len(str(value)) < 150:
                            context_parts.append(f"  {key}: {value}")

        # Add vector search results
        if company_context.vector_search_results:
            context_parts.append("Additional Context:")
            for result in company_context.vector_search_results[:3]:  # Limit to top 3
                if isinstance(result, dict) and result.get("content"):
                    content = str(result["content"])[:200]  # Limit length
                    context_parts.append(f"  {content}")

        return "\n".join(context_parts)

    def _create_summary_prompt(
        self,
        company_name: str,
        context_data: str,
        focus_areas: List[str],
        max_length: int
    ) -> str:
        """Create prompt for summary generation."""

        focus_text = ""
        if focus_areas:
            focus_text = f"\n\nFocus particularly on: {', '.join(focus_areas)}"

        return f"""Write a concise executive summary for {company_name} based on the following information.

Company Information:
{context_data}{focus_text}

Requirements:
1. Maximum {max_length} words
2. Write for senior executives and decision-makers
3. Include the most compelling investment highlights
4. Note key risks or concerns briefly
5. Provide clear sense of investment potential
6. Use professional, objective tone
7. Structure as 2-3 short paragraphs

Write the executive summary now:"""

    def _create_fallback_summary(self, company_context: CompanyContext) -> str:
        """Create fallback summary when generation fails."""
        company_name = company_context.company_name or "the company"
        return f"Executive summary for {company_name} is being prepared. Initial analysis indicates this is an investment opportunity requiring further evaluation to determine strategic fit and potential returns."

    def _extract_highlights(self, company_context: CompanyContext) -> List[str]:
        """Extract key highlights from company context."""
        highlights = []

        # Extract from pitch book data
        if company_context.pitch_book_data:
            pitch_data = company_context.pitch_book_data

            if pitch_data.traction:
                highlights.extend([f"Traction: {metric}" for metric in pitch_data.traction[:3]])

            if pitch_data.competitive_advantage:
                highlights.extend([f"Advantage: {adv}" for adv in pitch_data.competitive_advantage[:2]])

            if pitch_data.funding_ask:
                highlights.append(f"Funding Ask: {pitch_data.funding_ask}")

            if pitch_data.market_size:
                highlights.append(f"Market: {pitch_data.market_size}")

        # Extract from form submissions
        if company_context.form_submissions:
            for submission in company_context.form_submissions[:1]:  # Just first submission
                if isinstance(submission, dict):
                    for key, value in submission.items():
                        if value and "revenue" in key.lower():
                            highlights.append(f"Revenue: {value}")
                        elif value and "growth" in key.lower():
                            highlights.append(f"Growth: {value}")

        return highlights[:5]  # Limit to 5 highlights

    async def _generate_investment_thesis(
        self,
        company_context: CompanyContext,
        context: AgentContext
    ) -> Optional[str]:
        """Generate investment thesis."""
        logger.debug("Generating investment thesis")

        try:
            company_name = company_context.company_name or "the company"

            # Prepare key data points
            key_points = []
            if company_context.pitch_book_data:
                pitch_data = company_context.pitch_book_data
                if pitch_data.business_model:
                    key_points.append(f"Business Model: {pitch_data.business_model}")
                if pitch_data.market_size:
                    key_points.append(f"Market: {pitch_data.market_size}")
                if pitch_data.competitive_advantage:
                    key_points.extend([f"Advantage: {adv}" for adv in pitch_data.competitive_advantage[:2]])

            key_data = "\n".join(key_points)

            prompt = f"""Create a concise investment thesis for {company_name}.

Key Information:
{key_data}

Write a 1-2 sentence investment thesis that:
1. Captures the core investment opportunity
2. Highlights the key value proposition
3. Is suitable for investment committee discussions

Investment Thesis:"""

            response = await self.openai_client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "You are an expert investment analyst creating investment theses."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=150,
                temperature=0.2,
                timeout=30
            )

            if response.choices and response.choices[0].message:
                return response.choices[0].message.content.strip()
            else:
                return None

        except Exception as e:
            logger.warning(f"Failed to generate investment thesis: {e}")
            return None

    async def _generate_summary_recommendation(
        self,
        company_context: CompanyContext,
        summary: str,
        context: AgentContext
    ) -> Optional[str]:
        """Generate recommendation for executive summary."""
        logger.debug("Generating summary recommendation")

        try:
            company_name = company_context.company_name or "the company"

            prompt = f"""Based on this executive summary for {company_name}, provide a clear, concise recommendation.

Executive Summary:
{summary}

Provide a 1-2 sentence recommendation that:
1. States whether to proceed, pass, or continue evaluation
2. Gives the primary reason
3. Is suitable for executive decision-making

Recommendation:"""

            response = await self.openai_client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "You are an expert investment analyst providing executive recommendations."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=100,
                temperature=0.2,
                timeout=30
            )

            if response.choices and response.choices[0].message:
                return response.choices[0].message.content.strip()
            else:
                return None

        except Exception as e:
            logger.warning(f"Failed to generate recommendation: {e}")
            return None
