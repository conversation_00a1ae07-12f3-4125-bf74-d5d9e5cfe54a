.PHONY: install start test test-verbose test-cov test-html lint format clean docker-up docker-down install-dev install-prod start-worker docker-up-d docker-logs docker-restart git-local-push

install: install-prod install-dev

install-prod:
	poetry install --no-dev

install-dev:
	poetry install --with dev

start:
	poetry run uvicorn app.main:app --reload

start-worker:
	poetry run python -m app.worker

test:
	poetry run pytest

test-verbose:
	poetry run pytest -v

test-cov:
	poetry run pytest --cov=app

test-html:
	poetry run pytest --cov=app --cov-report=html

lint:
	poetry run black .
	poetry run isort .
	poetry run mypy .

format:
	poetry run black .
	poetry run isort .

clean:
	find . -type d -name "__pycache__" -exec rm -r {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.pyd" -delete
	find . -type f -name ".coverage" -delete
	find . -type d -name "*.egg-info" -exec rm -r {} +
	find . -type d -name "*.egg" -exec rm -r {} +
	find . -type d -name ".pytest_cache" -exec rm -r {} +
	find . -type d -name ".mypy_cache" -exec rm -r {} +

docker-up:
	docker compose up --build

docker-down:
	docker compose down

docker-up-d:
	docker compose up -d --build

docker-logs:
	docker compose logs -f

docker-restart:
	docker compose down
	docker compose up -d --build

git-local-push:
	git add .
	git commit -m 'local'
	git push origin dev