"use client"

import React from 'react';
import { Plus, Save, Undo } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { Question, QuestionType, toApiQuestionType } from '@/lib/types/form';
import FormAPI from '@/lib/api/form-api';
import { v4 as uuidv4 } from 'uuid';
import { SortableContextProvider, SortableItem } from './sortable-context';
import { useOrderManager } from '@/hooks/use-order-manager';
import { cn } from '@/lib/utils';
import { QuestionCard } from './question-card';
import { DragEndEvent } from '@dnd-kit/core';
import { FormWithDetails } from '@/lib/types/form';

interface DraggableQuestionListProps {
  questions: Partial<Question>[];
  sectionId: string;
  allQuestions?: Question[];
  onQuestionsChange: (questions: Partial<Question>[]) => void;
}

interface OrderUpdate {
  question_id: string;
  order: number;
}

// Type guard to check if a question has a valid ID
function hasValidId(q: Partial<Question>): q is Partial<Question> & { _id: string } | Partial<Question> & { id: string } {
  return typeof (q._id || q.id) === 'string';
}

// Helper function to get a stable question ID
function getStableQuestionId(question: Partial<Question>, index: number): string {
  return question._id || question.id || question.local_id || `temp-${index}`;
}

export function DraggableQuestionList({
  questions: initialQuestions,
  sectionId,
  allQuestions = [],
  onQuestionsChange,
}: DraggableQuestionListProps) {
  // Use our order manager hook with proper type casting
  const {
    items: questions,
    hasUnsavedChanges,
    isSaving,
    saveOrder,
    undoLastChange,
    updateItems,
  } = useOrderManager<Question>({
    items: initialQuestions.map(q => ({
      ...q,
      section_id: sectionId,
      order: q.order ?? 0,
      type: q.type || QuestionType.SHORT_TEXT,
      label: q.label || 'New Question',
      required: q.required ?? false,
      help_text: q.help_text || '',
      options: q.options || [],
      validation: q.validation || {},
    } as Question)),
    onSave: async (updatedQuestions) => {
      try {
        // Create reorder items array
        const reorderItems = updatedQuestions
          .filter(question => question._id || question.id)
          .map((question, index) => ({
            id: question._id || question.id!,
            order: index
          }));

        // Call the new reorder API
        await FormAPI.reorderQuestions(reorderItems);

        // Update parent component
        onQuestionsChange(updatedQuestions.map((q, index) => ({
          ...q,
          order: index
        })));

        toast({
          title: "Question order updated",
          description: "The question order has been saved successfully.",
        });
      } catch (error) {
        console.error('Error saving question order:', error);
        throw error; // Propagate error to trigger error handling
      }
    },
    getItemId: (question) => getStableQuestionId(question, 0),
    updateOrder: (question, order) => ({ ...question, order }),
    onError: (error) => {
      console.error('Error in question order management:', error);
      toast({
        title: "Error saving question order",
        description: "There was an error saving the question order. Please try again.",
        variant: "destructive",
      });
    },
    resourceType: 'question',
    parentId: sectionId,
  });

  // Add a new question
  const handleAddQuestion = () => {
    if (!sectionId) {
      console.error('Cannot add question: Missing section ID');
      toast({
        title: "Error adding question",
        description: "Missing section ID. Please try again or refresh the page.",
        variant: "destructive",
      });
      return;
    }

    // Get the next available order
    const nextOrder = questions.length;

    const newQuestion: Question = {
      section_id: sectionId,
      type: QuestionType.SHORT_TEXT,
      label: 'New Question',
      help_text: '',
      required: false,
      order: nextOrder,
      local_id: uuidv4(),
    };

    // Update questions with the new one
    updateItems([...questions, newQuestion]);
  };

  // Update a question
  const handleUpdateQuestion = (index: number, updatedQuestion: Partial<Question>) => {
    // Ensure the section_id is set correctly
    if (!updatedQuestion.section_id || updatedQuestion.section_id !== sectionId) {
      updatedQuestion.section_id = sectionId;
    }

    // Create a new array with the updated question
    const newQuestions = [...questions];
    newQuestions[index] = {
      ...newQuestions[index],
      ...updatedQuestion,
    } as Question;

    // Update the questions array
    updateItems(newQuestions);
  };

  // Delete a question
  const handleDeleteQuestion = (index: number) => {
    // Create a new array without the deleted question
    const newQuestions = questions.filter((_, i) => i !== index);
    // Update the questions array
    updateItems(newQuestions);
  };

  // Get question IDs for sortable context
  const questionIds = questions.map((q, i) => getStableQuestionId(q, i));

  // Create a minimal valid form object for the QuestionCard
  const minimalForm: FormWithDetails = {
    _id: 'temp-form',
    name: 'Temporary Form',
    description: 'Temporary form for question editing',
    status: 'draft',
    sections: [{
      _id: sectionId,
      title: 'Current Section',
      description: '',
      order: 0,
      repeatable: false,
      questions: allQuestions,
    }],
    is_active: true,
    version: 1,
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Questions</h3>
        <div className="flex items-center gap-2">
          {hasUnsavedChanges && (
              <Button
              variant="outline"
                size="sm"
                onClick={undoLastChange}
                disabled={isSaving}
              >
                Undo
              </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={handleAddQuestion}
            disabled={isSaving}
          >
            Add Question
          </Button>
          {hasUnsavedChanges && (
              <Button
                size="sm"
                onClick={saveOrder}
                disabled={isSaving}
              >
              {isSaving ? 'Saving...' : 'Save Order'}
              </Button>
          )}
        </div>
      </div>

      {questions.length === 0 ? (
        <div className="rounded-xl border-2 border-dashed border-border/30 bg-muted/10 p-8 text-center">
          <div className="space-y-3">
            <div className="mx-auto flex size-12 items-center justify-center rounded-full bg-muted/30">
              <Plus className="size-6 text-muted-foreground" />
            </div>
            <div className="space-y-1">
              <h4 className="font-medium text-foreground">No questions yet</h4>
              <p className="text-sm text-muted-foreground">Add your first question to get started</p>
            </div>
          </div>
        </div>
      ) : (
        <SortableContextProvider
          items={questions}
          itemIds={questionIds}
          onDragEnd={({ active, over }) => {
            if (over && active.id !== over.id) {
            const oldIndex = questions.findIndex(q => getStableQuestionId(q, 0) === active.id);
            const newIndex = questions.findIndex(q => getStableQuestionId(q, 0) === over.id);
              if (oldIndex !== -1 && newIndex !== -1) {
            const newQuestions = [...questions];
            const [movedQuestion] = newQuestions.splice(oldIndex, 1);
            newQuestions.splice(newIndex, 0, movedQuestion);
            updateItems(newQuestions);
              }
            }
          }}
          strategy="vertical"
        >
          {questions.map((question, index) => (
            <SortableItem
              key={getStableQuestionId(question, index)}
              id={getStableQuestionId(question, index)}
              disabled={isSaving}
                        >
                          <QuestionCard
                            question={question}
                form={minimalForm}
                isSelected={false}
                onUpdate={(updatedQuestion) => handleUpdateQuestion(index, updatedQuestion)}
                onDelete={() => handleDeleteQuestion(index)}
                onDuplicate={() => {
                  // Create a duplicate of the question
                  const duplicateQuestion = {
                    ...question,
                    _id: undefined,
                    id: undefined,
                    local_id: uuidv4(),
                    label: `${question.label} (Copy)`,
                    order: questions.length,
                  };
                  updateItems([...questions, duplicateQuestion]);
                            }}
                          />
            </SortableItem>
          ))}
        </SortableContextProvider>
      )}
    </div>
  );
}
