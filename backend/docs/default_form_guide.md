# Default Form Guide for Beta Users

## Overview

When beta users create a new organization, a comprehensive demo form is automatically created to help them understand the form builder's capabilities. This form demonstrates all available question types, visibility conditions, validation rules, and advanced features.

## Form Structure

The demo form is titled **"Demo Form - Understanding Form Builder"** and contains 3 sections:

### Section 1: Basic Information
**Purpose**: Essential company and founder information with core fields

**Questions**:
1. **Company Name** (short_text, required, core_field: company_name)
   - Simple text input for company name
   - Demonstrates core field mapping

2. **Company Website** (short_text, required, core_field: company_website)
   - Text input with URL validation
   - Shows regex validation: `^https?://.*`
   - Custom validation message

3. **Funding Stage** (single_select, required, core_field: stage)
   - Dropdown with funding stages
   - Pre-seed through IPO options
   - Demonstrates single selection

4. **Business Sectors** (multi_select, required, core_field: sector)
   - Multiple choice selection
   - 17 different sector options
   - Shows multi-select capabilities

5. **Team Size** (number, optional)
   - Numeric input with validation
   - Min: 1, Max: 10,000
   - Demonstrates number validation

6. **Monthly Revenue** (range, optional)
   - Range slider input
   - Min: 0, Max: 1,000 (in thousands)
   - Shows range input type

### Section 2: Advanced Features Demo
**Purpose**: Demonstrates visibility conditions and conditional logic

**Questions**:
1. **Do you have a technical co-founder?** (boolean, optional)
   - Simple yes/no question
   - Controls visibility of technical questions

2. **What type of product are you building?** (single_select, optional)
   - Mobile App, Web Application, Hardware, Service, Other
   - Controls visibility of product-specific questions

3. **What programming languages does your technical co-founder use?** (short_text, optional)
   - ⚡ **Visibility Condition**: Only visible if "Do you have a technical co-founder?" = Yes
   - Demonstrates boolean-based visibility

4. **Describe your mobile app's key features** (long_text, optional)
   - ⚡ **Visibility Condition**: Only visible if "What type of product?" = Mobile App
   - Shows conditional text areas

5. **Describe your web application's architecture** (long_text, optional)
   - ⚡ **Visibility Condition**: Only visible if "What type of product?" = Web Application
   - Demonstrates single-select-based visibility

6. **What hardware components are you using?** (multi_select, optional)
   - ⚡ **Visibility Condition**: Only visible if "What type of product?" = Hardware
   - Shows conditional multi-select

7. **Do you have paying customers?** (boolean, optional)
   - Controls revenue-related questions

8. **How many paying customers do you have?** (number, optional)
   - ⚡ **Visibility Condition**: Only visible if "Do you have paying customers?" = Yes
   - Validation: Min: 1, Max: 100,000
   - Demonstrates complex conditional logic

### Section 3: Additional Details
**Purpose**: Shows file uploads, dates, and advanced validation examples

**Questions**:
1. **When did you start working on this idea?** (date, optional)
   - Date picker input
   - Demonstrates date input type

2. **Upload your pitch deck** (file, optional)
   - File upload with validation
   - Custom validation: "File must be PDF and under 10MB"
   - Shows file upload capabilities

3. **Company logo** (file, optional)
   - File upload with image validation
   - Custom validation: "File must be image format (PNG, JPG, SVG) and under 5MB"
   - Demonstrates file type restrictions

4. **Tell us about your biggest challenge** (long_text, optional)
   - Multi-line text input
   - Shows long text capabilities

5. **How did you hear about us?** (single_select, optional)
   - Marketing attribution tracking
   - Google, Social Media, Referral, Event, Advertisement, Other
   - Demonstrates tracking use cases

6. **Would you like to receive updates about our platform?** (boolean, optional)
   - Simple opt-in question
   - Shows basic boolean usage

## Key Features Demonstrated

### Question Types
- ✅ **short_text**: Simple text inputs
- ✅ **long_text**: Multi-line text areas
- ✅ **number**: Numeric inputs with validation
- ✅ **range**: Slider inputs
- ✅ **single_select**: Dropdown selections
- ✅ **multi_select**: Multiple choice selections
- ✅ **boolean**: Yes/No questions
- ✅ **file**: File uploads
- ✅ **date**: Date pickers

### Core Fields
- 🎯 **company_name**: Maps to company name field
- 🎯 **company_website**: Maps to company website field
- 🎯 **stage**: Maps to funding stage field
- 🎯 **sector**: Maps to business sector field

### Validation Rules
- ✅ **Min/Max**: Number and range validation
- ✅ **Regex**: URL pattern matching
- ✅ **Custom**: File type and size restrictions

### Visibility Conditions
- ⚡ **Boolean-based**: Show/hide based on yes/no answers
- ⚡ **Single-select-based**: Show/hide based on dropdown selections
- ⚡ **Complex logic**: Multiple conditions and dependencies

### Advanced Features
- 📁 **File uploads**: PDF and image file handling
- 📅 **Date inputs**: Date picker functionality
- 🔗 **Cross-references**: Questions that depend on other questions
- 📊 **Validation**: Real-time input validation
- 🎯 **Core field mapping**: Automatic deal creation fields

## Learning Objectives

This demo form helps beta users understand:

1. **Question Type Selection**: When to use each question type
2. **Validation Strategies**: How to ensure data quality
3. **Conditional Logic**: How to create dynamic forms
4. **Core Field Mapping**: How to integrate with deal creation
5. **User Experience**: How to create intuitive form flows
6. **Data Collection**: How to gather comprehensive information

## Usage Instructions

1. **Explore the Form**: Fill out the demo form to see how different question types work
2. **Test Visibility**: Try different answers to see conditional questions appear/disappear
3. **Test Validation**: Try invalid inputs to see validation messages
4. **Study Structure**: Use this as a template for creating your own forms
5. **Copy Elements**: Use individual questions or sections as starting points

## Customization

Users can:
- Edit question text and options
- Modify validation rules
- Adjust visibility conditions
- Add new questions and sections
- Remove unnecessary elements
- Change the form structure

This demo form serves as both a learning tool and a starting template for creating effective investment forms. 