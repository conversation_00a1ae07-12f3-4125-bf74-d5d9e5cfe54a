"use client"

import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  ExclusionOperator,
  getSupportedOperators,
  getOperatorInfo
} from '@/lib/types/exclusion-filter';

interface OperatorSelectorProps {
  questionType?: string;
  value: ExclusionOperator;
  onChange: (operator: ExclusionOperator) => void;
  disabled?: boolean;
}

export function OperatorSelector({
  questionType,
  value,
  onChange,
  disabled = false
}: OperatorSelectorProps) {
  // Get supported operators for the current question type
  const supportedOperators = questionType ? getSupportedOperators(questionType) : [];

  // If no question type is selected, show placeholder
  if (!questionType) {
    return (
      <Select disabled={true}>
        <SelectTrigger>
          <SelectValue placeholder="Select question first..." />
        </SelectTrigger>
      </Select>
    );
  }

  // If no operators are supported for this question type
  if (supportedOperators.length === 0) {
    return (
      <Select disabled={true}>
        <SelectTrigger>
          <SelectValue placeholder="No operators available" />
        </SelectTrigger>
      </Select>
    );
  }

  return (
    <Select 
      value={value} 
      onValueChange={(newValue) => onChange(newValue as ExclusionOperator)}
      disabled={disabled}
    >
      <SelectTrigger className="w-[60px]">
        <SelectValue>
          {(() => {
            const op = supportedOperators.find(o => o.value === value);
            return op ? (
              <span className="font-mono text-lg font-semibold">{op.symbol}</span>
            ) : '=';
          })()}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        {supportedOperators.map((operatorInfo) => (
          <SelectItem key={operatorInfo.value} value={operatorInfo.value}>
            <div className="flex items-center gap-2">
              <span className="font-mono text-sm">{operatorInfo.symbol}</span>
              <span className="text-sm">{operatorInfo.label}</span>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
