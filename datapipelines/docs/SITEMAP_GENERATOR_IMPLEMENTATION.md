# Website Sitemap Extractor & JS Awareness Engine - Implementation

This document describes the complete implementation of the Website Sitemap Extractor & JS Awareness Engine as specified in the PRD.

## Overview

The implementation provides a modular, callable pipeline for parsing, analyzing, and validating sitemap-level intelligence from company websites. It combines BrightData Web Unlocker, HTML cleaning, and LLM analysis to extract meaningful internal URLs.

## Architecture

```
Input: Domain → BrightData Web Unlocker → HTML Cleaning → Text Chunking → LLM Analysis → URL Validation → S3 Storage
```

## Components

### 1. Centralized Client Modules

#### BrightData Client (`app/services/brightdata_client.py`)
- **Purpose**: Unified wrapper for BrightData Web Unlocker API
- **Features**:
  - Web Unlocker requests with JS rendering
  - Automatic retries and error handling
  - Response caching and S3 storage
  - Rate limiting and timeout management

**Key Methods**:
```python
async def fetch_webpage(url: str, render_js: bool = True, format_type: str = "raw")
async def fetch_and_store_webpage(url: str, org_id: str = "unknown", render_js: bool = True)
```

#### Together AI Client (`app/services/together_client.py`)
- **Purpose**: Unified wrapper for Together AI LLM generation
- **Features**:
  - LLM chat completions with Together AI
  - Automatic retries and error handling
  - Response parsing and validation
  - Specialized sitemap analysis

**Key Methods**:
```python
async def create_chat_completion(messages: List[Dict[str, str]], model: Optional[str] = None)
async def generate_sitemap_analysis(domain: str, page_title: str, content: str)
```

### 2. HTML Cleaning Utility (`app/utils/html_cleaner.py`)

**Purpose**: Extract clean text from HTML content for analysis

**Key Functions**:
```python
def clean_html_to_text(html_content: str) -> str
def extract_page_title(html_content: str) -> str
def is_js_rendered_app(html_content: str, cleaned_text: str) -> bool
def chunk_text_by_chars(text: str, max_chars: int = 10000) -> list[str]
```

**Features**:
- Removes scripts, styles, and non-content elements
- Handles HTML entities and whitespace
- Detects JS-rendered SPAs
- Intelligent text chunking with natural breakpoints

### 3. Main Sitemap Generator (`app/services/sitemap_generator.py`)

**Purpose**: Main orchestrator that combines all components

**Key Methods**:
```python
async def generate_sitemap(domain: str, page_title: str = "", org_id: str = "unknown")
```

**Process Flow**:
1. **Fetch Webpage**: Use BrightData Web Unlocker
2. **Extract Title**: Parse HTML for page title
3. **Clean HTML**: Convert to clean text
4. **JS Detection**: Check if it's a JS-rendered app
5. **Text Chunking**: Split into manageable chunks
6. **LLM Analysis**: Analyze each chunk for URLs
7. **URL Processing**: Deduplicate and validate URLs
8. **Storage**: Store results to S3

### 4. Task Integration (`app/tasks/sitemap_generator.py`)

**Purpose**: Queue-compatible task for job processing

**Usage**:
```python
from app.queueing.dispatcher import dispatch_job

job_id = await dispatch_job(
    job_func="generate_sitemap",
    job_args={
        "domain": "example.com",
        "page_title": "Example Company",
        "org_id": "your_org_id"
    },
    priority="normal"
)
```

## Configuration

### Environment Variables

Add to your `.env` file:
```bash
# BrightData Configuration
BRIGHTDATA_API_KEY=your_brightdata_api_key
BRIGHTDATA_WEB_UNLOCKER_ZONE=your_web_unlocker_zone

# Together AI Configuration
TOGETHER_API_KEY=your_together_api_key

# S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
S3_BUCKET=your_s3_bucket
```

### Settings Update

The following setting was added to `app/configs/settings.py`:
```python
BRIGHTDATA_WEB_UNLOCKER_ZONE: Optional[str] = None
```

## Usage Examples

### 1. Direct Service Usage

```python
from app.services.sitemap_generator import get_sitemap_generator

# Get service
sitemap_generator = await get_sitemap_generator()

# Generate sitemap
result = await sitemap_generator.generate_sitemap(
    domain="tractionx.ai",
    page_title="TractionX - Startup Investing Platform",
    org_id="your_org_id"
)

print(f"Success: {result['success']}")
print(f"JS Rendered: {result['js_rendered_app']}")
print(f"URLs: {result['urls']}")
```

### 2. Queue-Based Processing

```python
from app.queueing.dispatcher import dispatch_job, get_job_status

# Queue the job
job_id = await dispatch_job(
    job_func="generate_sitemap",
    job_args={
        "domain": "example.com",
        "page_title": "Example Company",
        "org_id": "your_org_id"
    },
    priority="normal"
)

# Check status
status = await get_job_status(job_id)
print(f"Job status: {status.status.value}")
```

### 3. Test Script

Run the test script to see the system in action:
```bash
cd datapipelines
python test_sitemap_generator.py
```

## Output Format

### Success Response
```json
{
  "success": true,
  "js_rendered_app": false,
  "urls": [
    "https://www.tractionx.ai/about",
    "https://www.tractionx.ai/contact",
    "https://www.tractionx.ai/careers",
    "https://www.tractionx.ai/blog"
  ],
  "domain": "tractionx.ai",
  "org_id": "your_org_id",
  "s3_key": "company_sitemaps/your_org_id/tractionx.ai/sitemap_20241201_143022.json",
  "processing_time": 45.2,
  "generated_at": "2024-12-01T14:30:22.123456+00:00"
}
```

### JS-Rendered App Response
```json
{
  "success": true,
  "js_rendered_app": true,
  "urls": [],
  "domain": "example.com",
  "org_id": "your_org_id",
  "s3_key": "company_sitemaps/your_org_id/example.com/sitemap_20241201_143022.json",
  "processing_time": 12.5,
  "generated_at": "2024-12-01T14:30:22.123456+00:00"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Failed to fetch webpage: HTTP error 403",
  "processing_time": 5.2
}
```

## S3 Storage Structure

### Raw Webpage Data
```
s3://your-bucket/brightdata_webpages/{org_id}/{domain}/{timestamp}_{hash}.json
```

### Sitemap Results
```
s3://your-bucket/company_sitemaps/{org_id}/{domain}/sitemap_{timestamp}.json
```

### Task Results
```
s3://your-bucket/sitemap_generator_results/{org_id}/{domain}/{timestamp}_result.json
```

## Error Handling

### Common Error Scenarios

1. **BrightData Failures**:
   - Network timeouts
   - API rate limits
   - Invalid zone configuration
   - Authentication errors

2. **LLM Analysis Failures**:
   - Together AI API errors
   - JSON parsing failures
   - Invalid responses

3. **Processing Failures**:
   - Empty HTML content
   - Invalid domain format
   - S3 storage errors

### Error Recovery

- **Automatic Retries**: Built into client modules
- **Graceful Degradation**: Continue processing with partial results
- **Comprehensive Logging**: All errors logged with context
- **S3 Audit Trail**: All inputs and outputs stored for debugging

## Performance Considerations

### Timeouts
- **BrightData Fetch**: 60 seconds (with JS rendering)
- **LLM Analysis**: 120 seconds per chunk
- **Total Pipeline**: ~2-5 minutes for typical sites

### Resource Usage
- **Memory**: Moderate (HTML processing, text chunking)
- **CPU**: Low to moderate (text processing, LLM calls)
- **Network**: High (BrightData + Together AI API calls)

### Optimization
- **Chunking**: Intelligent text splitting to minimize LLM calls
- **Caching**: S3 storage for raw data and results
- **Parallel Processing**: Multiple chunks can be processed concurrently

## Monitoring and Debugging

### Logging
Comprehensive logging at each step:
- Input validation
- API calls and responses
- Processing steps
- Error conditions
- Performance metrics

### S3 Audit Trail
All data stored for debugging:
- Raw webpage responses
- Processing results
- Task execution logs

### Health Checks
```python
# Check service health
sitemap_generator = await get_sitemap_generator()
# Service will raise exceptions if not properly configured
```

## Integration Points

### With Existing Pipeline
The sitemap generator can be integrated into existing enrichment pipelines:

```python
# In company enrichment pipeline
sitemap_result = await sitemap_generator.generate_sitemap(domain)
if sitemap_result['success'] and not sitemap_result['js_rendered_app']:
    # Process extracted URLs for further enrichment
    for url in sitemap_result['urls']:
        # Trigger additional analysis
        pass
```

### API Endpoints
Can be exposed via FastAPI endpoints for external consumption:

```python
@router.post("/sitemap/generate")
async def generate_sitemap_endpoint(request: SitemapRequest):
    result = await sitemap_generator.generate_sitemap(
        domain=request.domain,
        page_title=request.page_title,
        org_id=request.org_id
    )
    return result
```

## Future Enhancements

### Planned Features
1. **URL Categorization**: Automatically categorize extracted URLs
2. **Content Analysis**: Analyze page content for insights
3. **Recursive Crawling**: Follow extracted URLs for deeper analysis
4. **Performance Optimization**: Caching and parallel processing improvements

### Integration Opportunities
1. **Website Insight Generator**: Consume sitemap output for AI summaries
2. **Legal Signal Extraction**: Analyze legal/privacy pages
3. **Product Tagging**: Categorize products/services pages
4. **Competitive Analysis**: Compare sitemaps across competitors

## Testing

### Unit Tests
```bash
cd datapipelines
python -m pytest tests/test_sitemap_generator.py -v
```

### Integration Tests
```bash
# Test with real domains
python test_sitemap_generator.py
```

### Manual Testing
```python
# Test individual components
from app.utils.html_cleaner import clean_html_to_text, chunk_text_by_chars
from app.services.together_client import get_together_client

# Test HTML cleaning
cleaned = clean_html_to_text("<html><body>Test content</body></html>")
chunks = chunk_text_by_chars(cleaned)

# Test LLM analysis
together_client = await get_together_client()
result = await together_client.generate_sitemap_analysis(
    domain="example.com",
    page_title="Test",
    content="Test content"
)
```

This implementation provides a robust, scalable, and maintainable solution for website sitemap extraction with comprehensive error handling, monitoring, and integration capabilities. 