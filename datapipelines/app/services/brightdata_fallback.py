"""
BrightData Fallback Service for TractionX Data Pipeline Service.

This module provides BrightData LinkedIn scraping integration as a fallback
when PDL enrichment fails or returns insufficient data.
"""

import asyncio
import json
import time
from datetime import datetime, timezone
from typing import Any, Dict, Optional

import httpx
from app.configs import get_logger, settings
from app.models.founder import BrightDataFounderData
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class BrightDataFallbackService:
    """
    BrightData fallback service for LinkedIn profile enrichment.

    Handles:
    - Triggering LinkedIn scraping jobs
    - Polling for job completion
    - Downloading snapshot data
    - Storing raw data to S3
    """

    def __init__(self):
        self.logger = get_logger(f"{__name__}.BrightDataFallbackService")
        self.api_key = settings.BRIGHTDATA_API_KEY
        self.base_url = settings.BRIGHTDATA_BASE_URL
        self.dataset_id = "gd_l1viktl72bvl7bjuj0"  # LinkedIn dataset ID
        self.s3_storage: Optional[S3Storage] = None

        # API client
        self.client: Optional[httpx.AsyncClient] = None

        # Configuration
        self.max_poll_time = 600  # 10 minutes
        self.poll_interval = 10  # 10 seconds
        self.timeout = 30.0  # API timeout

        # Debug logging
        self.logger.info(
            f"BrightData service config: base_url={self.base_url}, dataset_id={self.dataset_id}"
        )

    async def initialize(self) -> None:
        """Initialize the BrightData service."""
        if not self.api_key:
            raise ValueError("BRIGHTDATA_API_KEY not configured")

        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            },
            timeout=self.timeout,
        )

        self.s3_storage = S3Storage()
        await self.s3_storage.initialize()

        self.logger.info("BrightData fallback service initialized")

    async def cleanup(self) -> None:
        """Clean up service resources."""
        if self.client:
            await self.client.aclose()
        if self.s3_storage:
            await self.s3_storage.cleanup()

        self.logger.info("BrightData fallback service cleaned up")

    async def enrich_founder_profile(
        self, founder_id: str, linkedin_url: str, org_id: str
    ) -> Dict[str, Any]:
        """
        Enrich founder profile using BrightData LinkedIn scraping.

        Args:
            founder_id: Unique founder identifier
            linkedin_url: LinkedIn profile URL to scrape
            org_id: Organization identifier

        Returns:
            Processing result with success status and data
        """
        try:
            self.logger.info(
                "Starting BrightData enrichment",
                founder_id=founder_id,
                linkedin_url=linkedin_url,
                org_id=org_id,
            )

            # Step 1: Trigger scraping job
            trigger_result = await self._trigger_scraping_job(linkedin_url)
            if not trigger_result["success"]:
                return {
                    "success": False,
                    "founder_id": founder_id,
                    "error": f"Failed to trigger scraping: {trigger_result['error']}",
                    "brightdata_status": "trigger_failed",
                }

            snapshot_id = trigger_result["snapshot_id"]
            self.logger.info(f"Triggered scraping job: {snapshot_id}")

            # Step 2: Poll for completion
            poll_result = await self._poll_for_completion(snapshot_id)
            if not poll_result["success"]:
                return {
                    "success": False,
                    "founder_id": founder_id,
                    "snapshot_id": snapshot_id,
                    "error": f"Polling failed: {poll_result['error']}",
                    "brightdata_status": poll_result.get("status", "poll_failed"),
                }

            # Step 3: Download snapshot
            download_result = await self._download_snapshot(snapshot_id)
            if not download_result["success"]:
                return {
                    "success": False,
                    "founder_id": founder_id,
                    "snapshot_id": snapshot_id,
                    "error": f"Download failed: {download_result['error']}",
                    "brightdata_status": "download_failed",
                }

            raw_data = download_result["data"]

            # Step 4: Store raw data to S3
            s3_key = await self._store_raw_data(
                founder_id, org_id, snapshot_id, raw_data
            )

            # Step 5: Create BrightData model
            brightdata_data = self._create_brightdata_model(
                snapshot_id, linkedin_url, raw_data
            )

            self.logger.info(
                "BrightData enrichment completed successfully",
                founder_id=founder_id,
                snapshot_id=snapshot_id,
                s3_key=s3_key,
            )

            return {
                "success": True,
                "founder_id": founder_id,
                "snapshot_id": snapshot_id,
                "brightdata_data": brightdata_data,
                "s3_key": s3_key,
                "brightdata_status": "ready",
                "raw_data": raw_data,
            }

        except Exception as e:
            error_msg = f"BrightData enrichment failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            return {
                "success": False,
                "founder_id": founder_id,
                "error": error_msg,
                "brightdata_status": "error",
            }

    async def _trigger_scraping_job(self, linkedin_url: str) -> Dict[str, Any]:
        """Trigger a LinkedIn scraping job."""
        try:
            if not self.client:
                return {
                    "success": False,
                    "error": "Client not initialized",
                }

            # Parameters for query string
            params = {
                "dataset_id": self.dataset_id,
                "include_errors": "true",
            }

            # Data for request body
            data = [{"url": linkedin_url}]

            self.logger.info(
                f"Triggering BrightData job with params: {params}, data: {data}"
            )
            self.logger.info(f"Making request to: {self.base_url}/datasets/v3/trigger")

            response = await self.client.post(
                "/datasets/v3/trigger", params=params, json=data
            )

            self.logger.info(f"BrightData response status: {response.status_code}")
            self.logger.info(f"BrightData response headers: {response.headers}")

            response.raise_for_status()
            result = response.json()

            self.logger.info(f"BrightData response body: {result}")

            snapshot_id = result.get("snapshot_id")
            if not snapshot_id:
                return {
                    "success": False,
                    "error": "No snapshot_id in response",
                }

            return {
                "success": True,
                "snapshot_id": snapshot_id,
            }

        except Exception as e:
            self.logger.error(f"BrightData trigger error: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": f"Trigger failed: {str(e)}",
            }

    async def _poll_for_completion(self, snapshot_id: str) -> Dict[str, Any]:
        """Poll for job completion."""
        start_time = time.time()

        while time.time() - start_time < self.max_poll_time:
            try:
                if not self.client:
                    return {
                        "success": False,
                        "status": "error",
                        "error": "Client not initialized",
                    }

                response = await self.client.get(f"/datasets/v3/progress/{snapshot_id}")

                response.raise_for_status()
                result = response.json()

                status = result.get("status")
                self.logger.info(f"Polling status: {status} for {snapshot_id}")

                if status == "ready":
                    return {
                        "success": True,
                        "status": "ready",
                    }
                elif status in ["error", "failed"]:
                    return {
                        "success": False,
                        "status": "error",
                        "error": f"Job failed with status: {status}",
                    }

                # Wait before next poll
                await asyncio.sleep(self.poll_interval)

            except Exception as e:
                self.logger.warning(f"Poll error: {e}")
                await asyncio.sleep(self.poll_interval)

        # Timeout
        return {
            "success": False,
            "status": "timeout",
            "error": f"Polling timed out after {self.max_poll_time} seconds",
        }

    async def _download_snapshot(self, snapshot_id: str) -> Dict[str, Any]:
        """Download snapshot data."""
        try:
            if not self.client:
                return {
                    "success": False,
                    "error": "Client not initialized",
                }

            response = await self.client.get(
                f"/datasets/v3/snapshot/{snapshot_id}", params={"format": "json"}
            )

            response.raise_for_status()
            data = response.json()

            if not data:
                return {
                    "success": False,
                    "error": "Empty snapshot data",
                }

            return {
                "success": True,
                "data": data,
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Download failed: {str(e)}",
            }

    async def _store_raw_data(
        self, founder_id: str, org_id: str, snapshot_id: str, raw_data: Any
    ) -> str:
        """Store raw BrightData snapshot to S3."""
        if not self.s3_storage:
            raise RuntimeError("S3 storage not initialized")

        s3_key = f"brightdata_linkedin_profiles/{founder_id}.json"

        await self.s3_storage.put_object(s3_key, json.dumps(raw_data, default=str))

        return s3_key

    def _create_brightdata_model(
        self, snapshot_id: str, linkedin_url: str, raw_data: Any
    ) -> BrightDataFounderData:
        """Create BrightData model from raw data."""
        # Extract the first profile from the data
        if isinstance(raw_data, list) and raw_data:
            profile_data = raw_data[0]
        else:
            profile_data = raw_data

        # Ensure profile_data is a dict
        if not isinstance(profile_data, dict):
            profile_data = {}

        return BrightDataFounderData(
            snapshot_id=snapshot_id,
            url=linkedin_url,
            full_name=profile_data.get(
                "name"
            ),  # BrightData uses "name" not "full_name"
            first_name=profile_data.get("first_name"),
            last_name=profile_data.get("last_name"),
            headline=profile_data.get(
                "position"
            ),  # BrightData uses "position" not "headline"
            summary=profile_data.get("about"),  # BrightData uses "about" not "summary"
            location=profile_data.get("location") or profile_data.get("city"),
            experience=profile_data.get("experience") or [],
            education=profile_data.get("education") or [],
            skills=profile_data.get("skills") or [],
            profiles=profile_data.get("profiles") or [],
            profile_picture_url=profile_data.get("avatar"),  # BrightData uses "avatar"
            connection_count=profile_data.get("connections"),
            follower_count=profile_data.get("followers"),
            scraped_at=datetime.now(timezone.utc),
            confidence_score=0.8,  # Default confidence for BrightData
        )


# Global service instance
_brightdata_service: Optional[BrightDataFallbackService] = None


async def get_brightdata_service() -> BrightDataFallbackService:
    """Get or create the global BrightData service instance."""
    global _brightdata_service

    if _brightdata_service is None:
        _brightdata_service = BrightDataFallbackService()
        await _brightdata_service.initialize()

    return _brightdata_service
