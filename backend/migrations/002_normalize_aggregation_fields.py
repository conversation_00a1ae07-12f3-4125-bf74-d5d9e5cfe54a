#!/usr/bin/env python3
"""
Migration: Normalize aggregation fields in scoring rules

This migration fixes existing scoring rules that have aggregation fields
(aggregation, section_id, aggregate_threshold, aggregate_operator) nested
in the condition object by moving them to the top level of the rule.

This is required because the Pydantic validation now expects these fields
at the top level, not nested in conditions.
"""

import asyncio
import os
import sys
from datetime import datetime, timezone
from typing import Any, Dict, List

from bson import ObjectId

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

import logging

from app.models.thesis import ScoringRule

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def find_rules_with_nested_aggregation() -> List[Dict[str, Any]]:
    """Find all scoring rules that have aggregation fields nested in conditions."""

    # Find rules where condition contains aggregation fields
    pipeline = [
        {
            "$match": {
                "is_deleted": False,
                "$or": [
                    {"condition.aggregation": {"$exists": True}},
                    {"condition.section_id": {"$exists": True}},
                    {"condition.aggregate_threshold": {"$exists": True}},
                    {"condition.aggregate_operator": {"$exists": True}},
                    {"condition.conditions.aggregation": {"$exists": True}},
                    {"condition.conditions.section_id": {"$exists": True}},
                    {"condition.conditions.aggregate_threshold": {"$exists": True}},
                    {"condition.conditions.aggregate_operator": {"$exists": True}},
                ],
            }
        }
    ]

    # Use the collection to run aggregation
    collection = await ScoringRule.get_collection()
    cursor = collection.aggregate(pipeline)

    rules = []
    async for rule in cursor:
        rules.append(rule)

    return rules


def extract_aggregation_from_condition(
    condition: Dict[str, Any],
) -> tuple[Dict[str, Any], Dict[str, Any]]:
    """
    Extract aggregation fields from a condition and return clean condition + extracted fields.

    Returns:
        tuple: (clean_condition, extracted_aggregation_fields)
    """
    clean_condition = condition.copy()
    extracted_fields = {}

    # Fields to extract
    aggregation_fields = [
        "aggregation",
        "section_id",
        "aggregate_threshold",
        "aggregate_operator",
    ]

    # Extract from top level of condition
    for field in aggregation_fields:
        if field in clean_condition:
            extracted_fields[field] = clean_condition.pop(field)

    # Extract from nested conditions if it's a compound condition
    if "conditions" in clean_condition and isinstance(
        clean_condition["conditions"], list
    ):
        cleaned_nested_conditions = []
        for nested_condition in clean_condition["conditions"]:
            if isinstance(nested_condition, dict):
                for field in aggregation_fields:
                    if field in nested_condition:
                        # Extract aggregation fields from first nested condition that has them
                        if field not in extracted_fields:
                            extracted_fields[field] = nested_condition.pop(field)
                        else:
                            # Remove from subsequent conditions to avoid conflicts
                            nested_condition.pop(field, None)
            cleaned_nested_conditions.append(nested_condition)
        clean_condition["conditions"] = cleaned_nested_conditions

    return clean_condition, extracted_fields


async def normalize_scoring_rule(rule_data: Dict[str, Any]) -> bool:
    """
    Normalize a single scoring rule by moving aggregation fields to top level.

    Returns:
        bool: True if rule was updated, False if no changes needed
    """
    try:
        rule_id = rule_data["_id"]
        logger.info(f"Processing rule {rule_id}")

        # Extract aggregation fields from condition
        condition = rule_data.get("condition", {})
        clean_condition, extracted_fields = extract_aggregation_from_condition(
            condition
        )

        # Check if we extracted any fields
        if not extracted_fields:
            logger.info(f"Rule {rule_id}: No aggregation fields found in condition")
            return False

        logger.info(
            f"Rule {rule_id}: Extracted fields: {list(extracted_fields.keys())}"
        )

        # Prepare update document
        update_doc = {
            "condition": clean_condition,
            "updated_at": int(datetime.now(timezone.utc).timestamp()),
        }

        # Add extracted aggregation fields to top level
        update_doc.update(extracted_fields)

        # Update the rule in database
        collection = await ScoringRule.get_collection()
        result = await collection.update_one({"_id": rule_id}, {"$set": update_doc})

        if result.modified_count > 0:
            logger.info(f"Rule {rule_id}: Successfully normalized")
            return True
        else:
            logger.warning(f"Rule {rule_id}: No documents modified")
            return False

    except Exception as e:
        logger.error(f"Error normalizing rule {rule_data.get('_id', 'unknown')}: {e}")
        return False


async def validate_normalized_rule(rule_id: ObjectId) -> bool:
    """
    Validate that a normalized rule passes Pydantic validation.

    Returns:
        bool: True if validation passes, False otherwise
    """
    try:
        # Fetch the updated rule
        rule = await ScoringRule.find_one({"_id": rule_id})
        if not rule:
            logger.error(f"Rule {rule_id} not found after normalization")
            return False

        # Try to create a new ScoringRule instance to validate
        # This will trigger all Pydantic validators
        validated_rule = ScoringRule(**rule.model_dump())
        logger.info(f"Rule {rule_id}: Validation passed")
        return True

    except Exception as e:
        logger.error(f"Rule {rule_id}: Validation failed: {e}")
        return False


async def run_migration():
    """Run the complete migration process."""
    try:
        logger.info("Starting aggregation fields normalization migration...")

        # Find all rules with nested aggregation fields
        rules_to_fix = await find_rules_with_nested_aggregation()

        if not rules_to_fix:
            logger.info("No rules found with nested aggregation fields")
            return

        logger.info(f"Found {len(rules_to_fix)} rules with nested aggregation fields")

        # Process each rule
        normalized_count = 0
        validation_passed_count = 0

        for rule_data in rules_to_fix:
            rule_id = rule_data["_id"]

            # Normalize the rule
            if await normalize_scoring_rule(rule_data):
                normalized_count += 1

                # Validate the normalized rule
                if await validate_normalized_rule(rule_id):
                    validation_passed_count += 1

        logger.info("Migration completed:")
        logger.info(f"  - Rules processed: {len(rules_to_fix)}")
        logger.info(f"  - Rules normalized: {normalized_count}")
        logger.info(f"  - Rules passing validation: {validation_passed_count}")

        if validation_passed_count < normalized_count:
            logger.warning(
                f"  - {normalized_count - validation_passed_count} rules still failing validation"
            )

    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise


async def dry_run():
    """Run a dry run to see what would be changed without making changes."""
    try:
        logger.info("Starting dry run...")

        # Find all rules with nested aggregation fields
        rules_to_fix = await find_rules_with_nested_aggregation()

        if not rules_to_fix:
            logger.info("No rules found with nested aggregation fields")
            return

        logger.info(f"Found {len(rules_to_fix)} rules with nested aggregation fields:")

        for rule_data in rules_to_fix:
            rule_id = rule_data["_id"]
            thesis_id = rule_data.get("thesis_id", "unknown")
            rule_type = rule_data.get("rule_type", "unknown")

            # Extract aggregation fields to show what would be moved
            condition = rule_data.get("condition", {})
            _, extracted_fields = extract_aggregation_from_condition(condition)

            logger.info(f"  Rule {rule_id} (thesis: {thesis_id}, type: {rule_type})")
            logger.info(f"    Would extract: {list(extracted_fields.keys())}")

            if extracted_fields:
                for field, value in extracted_fields.items():
                    logger.info(f"      {field}: {value}")

    except Exception as e:
        logger.error(f"Dry run failed: {e}")
        raise


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(
        description="Normalize aggregation fields in scoring rules"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be changed without making changes",
    )
    args = parser.parse_args()

    if args.dry_run:
        asyncio.run(dry_run())
    else:
        # Confirm before running
        response = input(
            "This will modify scoring rules in the database. Continue? (y/N): "
        )
        if response.lower() == "y":
            asyncio.run(run_migration())
        else:
            logger.info("Migration cancelled")
