"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { MoreHorizontal, Mail, Trash2, UserCheck, UserX, Crown, User, Clock, Users } from "lucide-react"
import { formatDistanceToNow } from "date-fns"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"

export interface Member {
  id: string
  name: string
  email: string
  profile_picture?: string
  status: string
  role_name?: string
  last_login?: number
  created_at: number
}

interface MembersTableProps {
  members: Member[]
  currentUserId: string
  isAdmin?: boolean
  onResendInvite: (email: string) => Promise<void>
  onRemoveMember: (memberId: string) => Promise<void>
  onUpdateRole?: (memberId: string, role: string) => Promise<void>
  className?: string
}

export function MembersTable({
  members,
  currentUserId,
  isAdmin = true,
  onResendInvite,
  onRemoveMember,
  onUpdateRole,
  className
}: MembersTableProps) {
  const [loadingActions, setLoadingActions] = useState<Record<string, boolean>>({})

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map(n => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2)
  }

  const getStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case "ACTIVE":
        return (
          <span className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-gradient-to-r from-[#e0e7ef] via-[#f3f4f6] to-[#cbd5e1] text-[#22292f] text-xs font-medium shadow-sm backdrop-blur-sm border border-gray-200">
            <UserCheck className="mr-1 size-3" />
            Active
          </span>
        )
      case "INVITED":
      case "PENDING":
        return (
          <Badge variant="outline" className="border-border text-muted-foreground">
            <Clock className="mr-1 size-3" />
            Invited
          </Badge>
        )
      case "SUSPENDED":
      case "INACTIVE":
        return (
          <Badge variant="outline" className="border-border text-muted-foreground opacity-60">
            <UserX className="mr-1 size-3" />
            Suspended
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="border-border text-muted-foreground">
            <User className="mr-1 size-3" />
            {status}
          </Badge>
        )
    }
  }

  const getRoleBadge = (role_name?: string) => {
    if (role_name?.toLowerCase().includes("admin") || role_name?.toLowerCase() === "gp") {
      return (
        <Badge variant="secondary" className="border-0 bg-muted text-foreground">
          <Crown className="mr-1 size-3" />
          Admin
        </Badge>
      )
    }
    return (
      <Badge variant="outline" className="border-border text-muted-foreground">
        <User className="mr-1 size-3" />
        {role_name || "Member"}
      </Badge>
    )
  }

  const handleAction = async (action: () => Promise<void>, memberId: string, actionName: string) => {
    setLoadingActions(prev => ({ ...prev, [memberId]: true }))
    try {
      await action()
      toast({
        title: "Success",
        description: `${actionName} completed successfully.`,
      })
    } catch (error) {
      console.error(`${actionName} error:`, error)
      toast({
        title: "Error",
        description: `Failed to ${actionName.toLowerCase()}. Please try again.`,
        variant: "destructive",
      })
    } finally {
      setLoadingActions(prev => ({ ...prev, [memberId]: false }))
    }
  }

  const formatLastLogin = (timestamp?: number) => {
    if (!timestamp) return "Never"
    return formatDistanceToNow(new Date(timestamp * 1000), { addSuffix: true })
  }

  return (
    <div className={cn("space-y-4", className)}>
      <div className="overflow-hidden rounded-lg border bg-card">
        <Table>
          <TableHeader>
            <TableRow className="border-border hover:bg-transparent">
              <TableHead className="font-medium text-foreground">Member</TableHead>
              <TableHead className="font-medium text-foreground">Status</TableHead>
              <TableHead className="font-medium text-foreground">Role</TableHead>
              <TableHead className="hidden font-medium text-foreground sm:table-cell">Last Login</TableHead>
              {isAdmin && (
                <TableHead className="text-right font-medium text-foreground">Actions</TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {members.map((member, index) => (
              <motion.tr
                key={member.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="border-border transition-colors hover:bg-muted/50"
              >
                <TableCell className="py-4">
                  <div className="flex items-center space-x-3">
                    <Avatar className="size-8 sm:size-10">
                      <AvatarImage src={member.profile_picture} alt={member.name} />
                      <AvatarFallback className="bg-muted text-sm font-medium text-muted-foreground">
                        {getInitials(member.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center space-x-2">
                        <p className="truncate font-medium text-foreground">
                          {member.name}
                        </p>
                        {member.id === currentUserId && (
                          <Badge variant="outline" className="shrink-0 text-xs">You</Badge>
                        )}
                      </div>
                      <p className="truncate text-sm text-muted-foreground">{member.email}</p>
                    </div>
                  </div>
                </TableCell>
                <TableCell className="py-4">
                  {getStatusBadge(member.status)}
                </TableCell>
                <TableCell className="py-4">
                  {getRoleBadge(member.role_name)}
                </TableCell>
                <TableCell className="hidden py-4 sm:table-cell">
                  <span className="text-sm text-muted-foreground">
                    {member.status.toUpperCase() === "INVITED" || member.status.toUpperCase() === "PENDING"
                      ? `Invited ${formatLastLogin(member.created_at)}`
                      : formatLastLogin(member.last_login)
                    }
                  </span>
                </TableCell>
                {isAdmin && (
                  <TableCell className="py-4 text-right">
                    {member.id !== currentUserId && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="size-8 p-0"
                            disabled={loadingActions[member.id]}
                          >
                            <MoreHorizontal className="size-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-56">
                          {(member.status.toUpperCase() === "INVITED" || member.status.toUpperCase() === "PENDING") && (
                            <DropdownMenuItem
                              onClick={() => handleAction(
                                () => onResendInvite(member.email),
                                member.id,
                                "Resend invite"
                              )}
                              className="cursor-pointer"
                            >
                              <Mail className="mr-2 size-4" />
                              Resend Invite
                            </DropdownMenuItem>
                          )}
                          
                          {onUpdateRole && member.status.toUpperCase() === "ACTIVE" && (
                            <>
                              <DropdownMenuItem
                                onClick={() => handleAction(
                                  () => onUpdateRole(member.id, member.role_name?.toLowerCase().includes("admin") ? "member" : "admin"),
                                  member.id,
                                  member.role_name?.toLowerCase().includes("admin") ? "Remove admin" : "Make admin"
                                )}
                                className="cursor-pointer"
                              >
                                {member.role_name?.toLowerCase().includes("admin") ? (
                                  <>
                                    <User className="mr-2 size-4" />
                                    Remove Admin
                                  </>
                                ) : (
                                  <>
                                    <Crown className="mr-2 size-4" />
                                    Make Admin
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                            </>
                          )}
                          
                          <DropdownMenuItem
                            onClick={() => handleAction(
                              () => onRemoveMember(member.id),
                              member.id,
                              "Remove member"
                            )}
                            className="cursor-pointer text-destructive focus:text-destructive"
                          >
                            <Trash2 className="mr-2 size-4" />
                            Remove Member
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </TableCell>
                )}
              </motion.tr>
            ))}
          </TableBody>
        </Table>
      </div>

      {members.length === 0 && (
        <div className="py-12 text-center">
          <Users className="mx-auto mb-4 size-12 text-muted-foreground" />
          <h3 className="mb-2 text-lg font-semibold text-foreground">
            No members yet
          </h3>
          <p className="text-muted-foreground">
            Invite team members to start collaborating on TractionX.
          </p>
        </div>
      )}
    </div>
  )
}
