"""
S3 storage implementation for TractionX Data Pipeline Service.
"""

import json
from typing import Any, Dict, List, Optional, Union

import boto3
from app.core.config import settings
from app.core.logging import get_logger


class S3Storage:
    """
    S3 storage implementation that provides access to AWS S3 bucket.

    This class implements both the basic StorageInterface and the more
    specific ObjectStorageInterface for S3 operations.
    """

    def __init__(self):
        """Initialize S3 storage with logger."""
        self.logger = get_logger(__name__)
        self.s3_client = None
        self.bucket_name = settings.S3_BUCKET_SUBMISSIONS
        self.prefix = ""
        self.region = settings.AWS_REGION
        self.initialized = False

    async def initialize(self) -> None:
        """
        Initialize S3 storage by creating a boto3 S3 client.

        This method sets up the connection to AWS S3 using credentials
        from environment variables.
        """
        try:
            self.s3_client = boto3.client(
                "s3",
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=self.region,
            )
            # Verify connection by checking if bucket exists
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            self.initialized = True
            self.logger.info(
                f"S3 storage initialized successfully. Bucket: {self.bucket_name}"
            )
        except Exception as e:
            self.logger.error(f"Failed to initialize S3 storage: {str(e)}")
            self.initialized = False
            raise

    async def cleanup(self) -> None:
        """
        Clean up S3 storage resources.

        This method releases any resources used by the S3 client.
        """
        self.s3_client = None
        self.initialized = False
        self.logger.info("S3 storage resources cleaned up")

    async def health_check(self) -> bool:
        """
        Check S3 storage health by verifying connection to the bucket.

        Returns:
            bool: True if the connection is healthy, False otherwise.
        """
        if not self.initialized or not self.s3_client:
            return False

        try:
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            return True
        except Exception as e:
            self.logger.error(f"S3 health check failed: {str(e)}")
            return False

    async def store(self, key: str, data: Any) -> bool:
        """
        Store data in S3 (compatibility method).

        This method is a wrapper around put_object for compatibility with StorageInterface.

        Args:
            key: The object key (path) in S3
            data: The data to store

        Returns:
            bool: True if successful, False otherwise
        """
        return await self.put_object(key, data)

    async def retrieve(self, key: str) -> Optional[Any]:
        """
        Retrieve data from S3 (compatibility method).

        This method is a wrapper around get_object for compatibility with StorageInterface.

        Args:
            key: The object key (path) in S3

        Returns:
            Optional[Any]: The retrieved data or None if not found
        """
        try:
            data = await self.get_object(key)
            if data is None:
                return None

            # Try to decode as JSON if it's a string or bytes
            if isinstance(data, (str, bytes)):
                try:
                    return json.loads(data)
                except (json.JSONDecodeError, UnicodeDecodeError):
                    # Return raw data if not valid JSON
                    return data
            return data
        except Exception as e:
            self.logger.error(f"Error retrieving data from S3: {str(e)}")
            return None

    async def delete(self, key: str) -> bool:
        """
        Delete data from S3 (compatibility method).

        This method is a wrapper around delete_object for compatibility with StorageInterface.

        Args:
            key: The object key (path) in S3

        Returns:
            bool: True if successful, False otherwise
        """
        return await self.delete_object(key)

    async def list_keys(self, prefix: str = "") -> List[str]:
        """
        List keys in S3 (compatibility method).

        This method is a wrapper around list_objects for compatibility with StorageInterface.

        Args:
            prefix: The prefix to filter keys by

        Returns:
            List[str]: List of keys matching the prefix
        """
        return await self.list_objects(prefix)

    async def put_object(
        self,
        key: str,
        data: Union[bytes, str, Dict[str, Any]],
        metadata: Optional[Dict[str, str]] = None,
    ) -> bool:
        """
        Store an object in S3.

        Args:
            key: The object key (path) in S3
            data: The data to store (bytes, string, or dict that will be converted to JSON)
            metadata: Optional metadata to store with the object

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.initialized or not self.s3_client:
            self.logger.error("S3 client not initialized")
            return False

        try:
            # Prepare the full key with prefix
            full_key = f"{self.prefix}/{key}" if self.prefix else key

            # Convert data to appropriate format
            if isinstance(data, dict):
                body = json.dumps(data).encode("utf-8")
                content_type = "application/json"
            elif isinstance(data, str):
                body = data.encode("utf-8")
                content_type = "text/plain"
            elif isinstance(data, bytes):
                body = data
                content_type = "application/octet-stream"
            else:
                # Try to serialize other objects to JSON
                try:
                    body = json.dumps(data).encode("utf-8")
                    content_type = "application/json"
                except (TypeError, ValueError):
                    self.logger.error(
                        f"Unsupported data type for S3 storage: {type(data)}"
                    )
                    return False

            # Prepare upload parameters
            upload_args = {
                "Bucket": self.bucket_name,
                "Key": full_key,
                "Body": body,
                "ContentType": content_type,
            }

            # Add metadata if provided
            if metadata:
                upload_args["Metadata"] = metadata

            # Upload to S3
            self.s3_client.put_object(**upload_args)
            self.logger.info(f"Successfully stored object in S3: {full_key}")
            return True

        except Exception as e:
            self.logger.error(f"Error storing object in S3: {str(e)}")
            return False

    async def get_object(self, key: str) -> Optional[bytes]:
        """
        Retrieve an object from S3.

        Args:
            key: The object key (path) in S3

        Returns:
            Optional[bytes]: The object data as bytes, or None if not found
        """
        if not self.initialized or not self.s3_client:
            self.logger.error("S3 client not initialized")
            return None

        try:
            # Prepare the full key with prefix
            full_key = f"{self.prefix}/{key}" if self.prefix else key

            # Get object from S3
            response = self.s3_client.get_object(Bucket=self.bucket_name, Key=full_key)
            data = response["Body"].read()
            self.logger.info(f"Successfully retrieved object from S3: {full_key}")
            return data

        except Exception as e:
            self.logger.error(f"Unexpected error retrieving object from S3: {str(e)}")
            return None

    async def get_object_metadata(self, key: str) -> Optional[Dict[str, str]]:
        """
        Get object metadata from S3.

        Args:
            key: The object key (path) in S3

        Returns:
            Optional[Dict[str, str]]: The object metadata, or None if not found
        """
        if not self.initialized or not self.s3_client:
            self.logger.error("S3 client not initialized")
            return None

        try:
            # Prepare the full key with prefix
            full_key = f"{self.prefix}/{key}" if self.prefix else key

            # Get object metadata from S3
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=full_key)
            metadata = response.get("Metadata", {})
            self.logger.info(f"Successfully retrieved metadata for object: {full_key}")
            return metadata

        # except botocore.exceptions.ClientError as e:
        #     if (
        #         e.response["Error"]["Code"] == "NoSuchKey"
        #         or e.response["Error"]["Code"] == "404"
        #     ):
        #         self.logger.warning(f"Object not found in S3: {full_key}")
        #     else:
        #         self.logger.error(f"Error retrieving object metadata from S3: {str(e)}")
        #     return None
        except Exception as e:
            self.logger.error(
                f"Unexpected error retrieving object metadata from S3: {str(e)}"
            )
            return None

    async def delete_object(self, key: str) -> bool:
        """
        Delete an object from S3.

        Args:
            key: The object key (path) in S3

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.initialized or not self.s3_client:
            self.logger.error("S3 client not initialized")
            return False

        try:
            # Prepare the full key with prefix
            full_key = f"{self.prefix}/{key}" if self.prefix else key

            # Delete object from S3
            self.s3_client.delete_object(Bucket=self.bucket_name, Key=full_key)
            self.logger.info(f"Successfully deleted object from S3: {full_key}")
            return True

        except Exception as e:
            self.logger.error(f"Error deleting object from S3: {str(e)}")
            return False

    async def list_objects(self, prefix: str = "", limit: int = 1000) -> List[str]:
        """
        List objects in S3 with optional prefix.

        Args:
            prefix: The prefix to filter objects by
            limit: Maximum number of objects to return

        Returns:
            List[str]: List of object keys matching the prefix
        """
        if not self.initialized or not self.s3_client:
            self.logger.error("S3 client not initialized")
            return []

        try:
            # Prepare the full prefix with the base prefix
            full_prefix = f"{self.prefix}/{prefix}" if self.prefix else prefix

            # List objects from S3
            paginator = self.s3_client.get_paginator("list_objects_v2")
            page_iterator = paginator.paginate(
                Bucket=self.bucket_name,
                Prefix=full_prefix,
                PaginationConfig={"MaxItems": limit},
            )

            keys = []
            for page in page_iterator:
                if "Contents" in page:
                    for obj in page["Contents"]:
                        # Remove the base prefix from the key
                        key = obj["Key"]
                        if self.prefix and key.startswith(self.prefix + "/"):
                            key = key[len(self.prefix) + 1 :]
                        keys.append(key)

            self.logger.info(
                f"Listed {len(keys)} objects from S3 with prefix: {full_prefix}"
            )
            return keys

        except Exception as e:
            self.logger.error(f"Error listing objects from S3: {str(e)}")
            return []

    async def object_exists(self, key: str) -> bool:
        """
        Check if an object exists in S3.

        Args:
            key: The object key (path) in S3

        Returns:
            bool: True if the object exists, False otherwise
        """
        if not self.initialized or not self.s3_client:
            self.logger.error("S3 client not initialized")
            return False

        try:
            # Prepare the full key with prefix
            full_key = f"{self.prefix}/{key}" if self.prefix else key

            # Check if object exists in S3
            self.s3_client.head_object(Bucket=self.bucket_name, Key=full_key)
            return True

        # except botocore.exceptions.ClientError as e:
        #     if (
        #         e.response["Error"]["Code"] == "NoSuchKey"
        #         or e.response["Error"]["Code"] == "404"
        #     ):
        #         return False
        #     else:
        #         self.logger.error(f"Error checking if object exists in S3: {str(e)}")
        #         return False
        except Exception as e:
            self.logger.error(
                f"Unexpected error checking if object exists in S3: {str(e)}"
            )
            return False

    async def generate_presigned_url(self, key: str, expiration: int = 3600) -> str:
        """
        Generate a presigned URL for object access.

        Args:
            key: The object key (path) in S3
            expiration: URL expiration time in seconds (default: 1 hour)

        Returns:
            str: Presigned URL for the object
        """
        if not self.initialized or not self.s3_client:
            self.logger.error("S3 client not initialized")
            return ""

        try:
            # Prepare the full key with prefix
            full_key = f"{self.prefix}/{key}" if self.prefix else key

            # Generate presigned URL
            url = self.s3_client.generate_presigned_url(
                "get_object",
                Params={"Bucket": self.bucket_name, "Key": full_key},
                ExpiresIn=expiration,
            )

            self.logger.info(f"Generated presigned URL for object: {full_key}")
            return url

        except Exception as e:
            self.logger.error(f"Error generating presigned URL for object: {str(e)}")
            return ""
