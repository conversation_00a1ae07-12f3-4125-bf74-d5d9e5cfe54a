"""
Together AI Client Module for TractionX Data Pipeline Service.

This module provides a unified wrapper for Together AI LLM generation,
centralizing all LLM interactions across the platform.
"""

import json
import time
from typing import Any, Dict, List, Optional

from app.clients.base_client import BaseClient
from app.configs import get_logger, settings

logger = get_logger(__name__)


class TogetherClient(BaseClient):
    """
    Unified Together AI client for LLM generation.

    Handles:
    - LLM chat completions with Together AI
    - Automatic retries and error handling
    - Response parsing and validation
    - Rate limiting and timeout management
    """

    def __init__(self):
        """Initialize the Together AI client."""
        super().__init__(
            base_url="https://api.together.xyz/v1",
            api_key=settings.TOGETHER_API_KEY,
            timeout=120.0,  # Longer timeout for LLM generation
            max_retries=5,  # More retries for rate limiting
            retry_delay=5.0,  # Longer base delay for rate limiting
            headers={
                "Content-Type": "application/json",
            },
        )

        # Default model
        self.default_model = "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free"

        self.logger.info(f"Together client config: model={self.default_model}")

    async def initialize(self) -> None:
        """Initialize the Together AI client."""
        if not self.api_key:
            raise ValueError("TOGETHER_API_KEY not configured")

        # Initialize base client
        await super().initialize()

        self.logger.info("Together AI client initialized")
        return None

    async def cleanup(self) -> None:
        """Clean up client resources."""
        await super().cleanup()

        self.logger.info("Together AI client cleaned up")

    async def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: int = 2000,
        temperature: float = 0.3,
        stream: bool = False,
    ) -> Dict[str, Any]:
        """
        Create a chat completion using Together AI.

        Args:
            messages: List of message dictionaries with 'role' and 'content'
            model: Model to use (defaults to self.default_model)
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature (0.0 to 2.0)
            stream: Whether to stream the response

        Returns:
            Dictionary with success status and completion data
        """
        try:
            start_time = time.time()
            model = model or self.default_model

            self.logger.info(
                "Creating chat completion",
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                message_count=len(messages),
            )

            # Prepare request data
            data = {
                "model": model,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": stream,
            }

            # Use base client's post method
            result = await self.post("/chat/completions", data=data)

            if result["success"]:
                # Extract content from response
                response_data = result["data"]
                if "choices" in response_data and response_data["choices"]:
                    content = (
                        response_data["choices"][0]
                        .get("message", {})
                        .get("content", "")
                    )
                else:
                    content = ""

                response_time_ms = int((time.time() - start_time) * 1000)

                self.logger.info(
                    "Chat completion successful",
                    model=model,
                    response_time_ms=response_time_ms,
                    content_length=len(content),
                    tokens_used=response_data.get("usage", {}).get("total_tokens", 0),
                )

                return {
                    "success": True,
                    "content": content,
                    "full_response": response_data,
                    "response_time_ms": response_time_ms,
                    "model": model,
                    "attempts": result.get("attempts", 1),
                }

            return result

        except Exception as e:
            error_msg = f"Chat completion failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            return {
                "success": False,
                "error": error_msg,
            }

    async def generate_sitemap_analysis(
        self, domain: str, page_title: str, content: str
    ) -> Dict[str, Any]:
        """
        Generate sitemap analysis using LLM.

        Args:
            domain: Company domain
            page_title: Page title for context
            content: Cleaned HTML content

        Returns:
            Dictionary with success status and analysis result
        """
        try:
            # Create the prompt
            prompt = self._create_sitemap_prompt(domain, page_title, content)

            # Create messages for chat completion
            messages = [
                {
                    "role": "user",
                    "content": prompt,
                }
            ]

            # Get completion
            result = await self.create_chat_completion(
                messages=messages,
                max_tokens=1000,
                temperature=0.1,  # Lower temperature for more consistent output
            )

            if not result["success"]:
                return result

            # Parse the response
            content = result["content"].strip()

            # Check if it's a JS rendered app
            if content == "JS_RENDERED_APP":
                return {
                    "success": True,
                    "js_rendered_app": True,
                    "urls": [],
                    "raw_response": content,
                }

            # Try to parse as JSON
            try:
                urls = json.loads(content)
                if isinstance(urls, list) and all(isinstance(u, str) for u in urls):
                    return {
                        "success": True,
                        "js_rendered_app": False,
                        "urls": urls,
                        "raw_response": content,
                    }
                else:
                    return {
                        "success": False,
                        "error": "Invalid URL format in response",
                        "raw_response": content,
                    }
            except json.JSONDecodeError:
                return {
                    "success": False,
                    "error": "Failed to parse response as JSON",
                    "raw_response": content,
                }

        except Exception as e:
            error_msg = f"Sitemap analysis failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            return {
                "success": False,
                "error": error_msg,
            }

    def _create_sitemap_prompt(self, domain: str, page_title: str, content: str) -> str:
        """
        Create a prompt for sitemap analysis.

        Args:
            domain: Company domain
            page_title: Page title for context
            content: Cleaned HTML content

        Returns:
            Formatted prompt string
        """
        prompt = f"""
        You are an expert web auditor working for an investment platform. Your job is to extract the most relevant internal pages from a company website to help with enrichment and analysis.

        The input below includes:
        - The page title (use this for company context)
        - Cleaned plain text of the homepage (extracted from HTML)
        - Sometimes the HTML text

        Your task:
        1. Find all **important internal links** from this homepage content.
        2. Only include links that clearly belong to the company’s own website (skip external or social links).
        3. Prioritize links that likely lead to:
            - About or Team pages
            - Products or Services
            - Contact or Support
            - Blog or News
            - Privacy Policy or Legal
            - Careers or Jobs
            - Pricing or Plans
            - Coming Soon or Launching Soon pages
            - Any other pages that are likely to be important for the company or the investor
        4. If the homepage content is clearly missing, meta-only, or the site looks like a Single Page App (SPA), return: "JS_RENDERED_APP"
        5. Do not hallucinate. Only return URLs that are clearly internal to the company's own website, and also in the


        You are analyzing the website content for {domain} to extract internal URLs for sitemap generation.

        Page Title: {page_title}

        Content:
        {content}

        Instructions:
        1. Analyze the content and extract internal URLs that belong to {domain}
        2. Focus on navigation links, menu items, and internal page references
        3. Exclude external links, social media links, and non-page URLs
        4. If this appears to be a JavaScript-rendered single-page application (SPA), respond with exactly: "JS_RENDERED_APP"
        5. Otherwise, return a JSON array of URLs, e.g.: ["https://{domain}/about", "https://{domain}/contact"]
        6. Do not return capital letters in the URLs. Be very specific and careful.

        Output format:
        Return only a valid JSON array of full URLs — or the string "JS_RENDERED_APP". Do not guess default routes. Do not hallucinate. No markdown, no extra text, no commentary.
        """

        return prompt.strip()

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the Together AI service.

        Returns:
            Dictionary with health status
        """
        try:
            # Try a simple request to check if the service is accessible
            result = await self.get("/models")

            return {
                "success": result["success"],
                "service": "together_ai",
                "status": "healthy" if result["success"] else "unhealthy",
                "error": result.get("error") if not result["success"] else None,
            }
        except Exception as e:
            return {
                "success": False,
                "service": "together_ai",
                "status": "unhealthy",
                "error": str(e),
            }


# Global client instance
_together_client: Optional[TogetherClient] = None


async def get_together_client() -> TogetherClient:
    """Get the global Together AI client instance."""
    global _together_client

    if _together_client is None:
        _together_client = TogetherClient()
        await _together_client.initialize()

    return _together_client


async def cleanup_together_client():
    """Clean up the global Together AI client instance."""
    global _together_client

    if _together_client:
        await _together_client.cleanup()
        _together_client = None
