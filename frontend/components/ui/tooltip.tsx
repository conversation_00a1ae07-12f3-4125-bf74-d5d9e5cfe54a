"use client"

import * as React from "react"
import {
  Provider as Too<PERSON><PERSON><PERSON>rovider,
  <PERSON> as <PERSON><PERSON><PERSON>,
  <PERSON><PERSON> as Toolt<PERSON>Trigger,
  Content as TooltipContent,
} from "@radix-ui/react-tooltip"

import { cn } from "@/lib/utils"

const TooltipContentComponent = React.forwardRef<
  React.ElementRef<typeof TooltipContent>,
  React.ComponentPropsWithoutRef<typeof TooltipContent>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <TooltipContent
    ref={ref}
    sideOffset={sideOffset}
    className={cn(
      "z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-50 data-[side=bottom]:slide-in-from-top-1 data-[side=left]:slide-in-from-right-1 data-[side=right]:slide-in-from-left-1 data-[side=top]:slide-in-from-bottom-1",
      className
    )}
    {...props}
  />
))
TooltipContentComponent.displayName = "TooltipContent"

export { Toolt<PERSON>, TooltipTrigger, TooltipContentComponent as TooltipContent, TooltipProvider }
