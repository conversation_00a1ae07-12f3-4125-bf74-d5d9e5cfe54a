from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from app.models.user import Invite<PERSON><PERSON>, User
from app.models.organization import Organization


class IOnboardingService(ABC):
    """Interface for onboarding service operations."""

    @abstractmethod
    async def create_invite_code(
        self, 
        email: str, 
        org_name: Optional[str] = None
    ) -> InviteCode:
        """Create a new invite code for organization onboarding."""
        pass

    @abstractmethod
    async def validate_invite_code(self, code: str) -> InviteCode:
        """Validate and retrieve an invite code."""
        pass

    @abstractmethod
    async def create_organization(
        self,
        invite_code: InviteCode,
        org_name: str,
        subdomain: str,
        website: Optional[str] = None,
        logo_url: Optional[str] = None
    ) -> Organization:
        """Create a new organization during onboarding."""
        pass

    @abstractmethod
    async def create_user_profile(
        self,
        invite_code: InviteCode,
        organization: Organization,
        name: str,
        password: str,
        profile_picture_url: Optional[str] = None
    ) -> User:
        """Create user profile and complete onboarding."""
        pass

    @abstractmethod
    async def complete_onboarding(
        self,
        invite_code: InviteCode,
        user: User,
        organization: Organization
    ) -> Dict[str, Any]:
        """Complete the onboarding process and return auth tokens."""
        pass

    @abstractmethod
    async def send_peer_invites(
        self,
        org_id: str,
        invited_by: str,
        emails: list[str],
        role_id: Optional[str] = None,
        message: Optional[str] = None
    ) -> Dict[str, Any]:
        """Send invitations to peers to join the organization."""
        pass

    @abstractmethod
    async def check_subdomain_availability(self, subdomain: str) -> bool:
        """Check if a subdomain is available for use."""
        pass
