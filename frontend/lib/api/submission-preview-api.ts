// API client for deal submission preview operations
import apiClient from '@/lib/api-client'
import { 
  DealSubmissionPreviewResponse,
  SubmissionPreview,
  FormattedSubmission,
  FormattedSection,
  FormattedAnswer
} from '@/lib/types/submission-preview'

export const SubmissionPreviewAPI = {
  /**
   * Get submission preview data for a deal
   */
  async getSubmissionPreview(dealId: string): Promise<DealSubmissionPreviewResponse> {
    console.log(`Fetching submission preview for deal ${dealId}`)
    
    try {
      const response = await apiClient.get(`/deals/${dealId}/submissions/preview`)
      console.log('Submission preview fetched:', response.data)
      return response.data
    } catch (error: any) {
      console.error('Error fetching submission preview:', error)
      throw error
    }
  },

  /**
   * Format submission data for UI rendering
   */
  formatSubmissionForUI(submission: SubmissionPreview): FormattedSubmission {
    const formattedSections: FormattedSection[] = submission.sections.map(section => {
      if (section.repeatable && section.instances) {
        // Handle repeatable sections
        return {
          id: section.section_id,
          title: section.title,
          isRepeatable: true,
          instances: section.instances.map(instance => ({
            index: instance.index,
            title: `${section.title} ${instance.index + 1}`,
            answers: instance.answers.map(answer => this.formatAnswer(answer))
          }))
        }
      } else {
        // Handle regular sections
        return {
          id: section.section_id,
          title: section.title,
          isRepeatable: false,
          questions: section.questions?.map(question => this.formatAnswer(question)) || []
        }
      }
    })

    return {
      id: submission.submission_id,
      submittedAt: new Date(submission.submitted_at),
      formName: submission.form_name,
      formVersion: submission.form_version,
      sections: formattedSections
    }
  },

  /**
   * Format individual answer for display
   */
  formatAnswer(answer: any): FormattedAnswer {
    const { label, answer: value, type } = answer
    
    // Handle different answer types
    let formattedValue: string | string[]
    let isEmpty = false

    if (value === null || value === undefined || value === '') {
      formattedValue = ''
      isEmpty = true
    } else if (Array.isArray(value)) {
      formattedValue = value.length > 0 ? value : []
      isEmpty = value.length === 0
    } else if (typeof value === 'boolean') {
      formattedValue = value ? 'Yes' : 'No'
    } else if (typeof value === 'number') {
      // Format numbers with commas for readability
      formattedValue = value.toLocaleString()
    } else {
      formattedValue = String(value)
      isEmpty = formattedValue.trim() === ''
    }

    return {
      label: label.trim(),
      value: formattedValue,
      type,
      isEmpty
    }
  },

  /**
   * Format dropdown options for submission selector
   */
  formatDropdownOptions(options: any[]): Array<{
    value: string
    label: string
    submittedAt: Date
  }> {
    return options.map(option => ({
      value: option.submission_id,
      label: `${option.form_name} (${new Date(option.submitted_at).toLocaleDateString()})`,
      submittedAt: new Date(option.submitted_at)
    }))
  },

  /**
   * Get display value for empty answers
   */
  getEmptyDisplayValue(): string {
    return '—'
  },

  /**
   * Check if a section has any non-empty answers
   */
  sectionHasContent(section: FormattedSection): boolean {
    if (section.isRepeatable && section.instances) {
      return section.instances.some(instance => 
        instance.answers.some(answer => !answer.isEmpty)
      )
    } else if (section.questions) {
      return section.questions.some(question => !question.isEmpty)
    }
    return false
  },

  /**
   * Get submission summary for display
   */
  getSubmissionSummary(submission: FormattedSubmission): {
    totalSections: number
    sectionsWithContent: number
    totalQuestions: number
    answeredQuestions: number
  } {
    let totalQuestions = 0
    let answeredQuestions = 0
    let sectionsWithContent = 0

    submission.sections.forEach(section => {
      const hasContent = this.sectionHasContent(section)
      if (hasContent) sectionsWithContent++

      if (section.isRepeatable && section.instances) {
        section.instances.forEach(instance => {
          totalQuestions += instance.answers.length
          answeredQuestions += instance.answers.filter(a => !a.isEmpty).length
        })
      } else if (section.questions) {
        totalQuestions += section.questions.length
        answeredQuestions += section.questions.filter(q => !q.isEmpty).length
      }
    })

    return {
      totalSections: submission.sections.length,
      sectionsWithContent,
      totalQuestions,
      answeredQuestions
    }
  }
}
