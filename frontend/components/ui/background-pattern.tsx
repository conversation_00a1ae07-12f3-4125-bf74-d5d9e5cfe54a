"use client"

import { cn } from "@/lib/utils"

interface BackgroundPatternProps {
  variant?: 'dots' | 'grid' | 'diagonal'
  opacity?: number
  className?: string
}

export function BackgroundPattern({ 
  variant = 'dots', 
  opacity = 0.03,
  className 
}: BackgroundPatternProps) {
  const patterns = {
    dots: (
      <svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
        <g fill="none" fillRule="evenodd">
          <g fill="currentColor" fillOpacity={opacity}>
            <circle cx="30" cy="30" r="1.5"/>
          </g>
        </g>
      </svg>
    ),
    grid: (
      <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
        <g fill="none" fillRule="evenodd">
          <g fill="currentColor" fillOpacity={opacity}>
            <path d="M40 0v40H0V0h40zm-1 1H1v38h38V1z"/>
          </g>
        </g>
      </svg>
    ),
    diagonal: (
      <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
        <g fill="none" fillRule="evenodd">
          <g fill="currentColor" fillOpacity={opacity}>
            <path d="M0 40L40 0H20L0 20M40 40V20L20 40"/>
          </g>
        </g>
      </svg>
    )
  }

  return (
    <div 
      className={cn(
        "pointer-events-none absolute inset-0 select-none",
        "bg-repeat",
        className
      )}
      style={{
        backgroundImage: `url("data:image/svg+xml,${encodeURIComponent(patterns[variant].props.children)}")`,
      }}
    />
  )
}
