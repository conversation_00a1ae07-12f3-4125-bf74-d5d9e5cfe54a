"""
Migration utilities for thesis scoring rules.

This module provides functions to migrate existing scoring rules to the new format,
including conversion of legacy rules and validation of the migration process.
"""

from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timezone
import logging
from bson import ObjectId

from app.models.thesis import (
    ScoringRule, RuleType, FilterCondition, CompoundCondition,
    AggregationType, ConditionOperator, LogicalOperator
)
from app.services.thesis.interfaces import ThesisServiceInterface
from app.core.logging import get_logger

logger = get_logger(__name__)

def convert_legacy_rule(legacy_rule: Dict[str, Any]) -> Tuple[Optional[Dict[str, Any]], List[str]]:
    """
    Convert a legacy scoring rule to the new format.

    Args:
        legacy_rule: Dictionary containing the legacy rule data

    Returns:
        Tuple of (converted_rule, warnings)
        - converted_rule: Dictionary with the new rule format, or None if conversion failed
        - warnings: List of warning messages about the conversion
    """
    warnings = []
    try:
        # Determine rule type
        rule_type = RuleType.BONUS if legacy_rule.get("compound_condition") else RuleType.SCORING

        # Start building new rule
        new_rule = {
            "rule_type": rule_type.value,
            "question_id": legacy_rule.get("question_id"),
            "weight": legacy_rule.get("weight", 1.0),
            "is_deleted": legacy_rule.get("is_deleted", False),
            "created_at": legacy_rule.get("created_at", int(datetime.now(timezone.utc).timestamp())),
            "updated_at": int(datetime.now(timezone.utc).timestamp())
        }

        # Handle bonus points
        if rule_type == RuleType.BONUS:
            if "bonus_points" not in legacy_rule:
                warnings.append("Bonus rule missing bonus_points, using default of 1.0")
                new_rule["bonus_points"] = 1.0
            else:
                new_rule["bonus_points"] = legacy_rule["bonus_points"]

        # Convert condition
        if rule_type == RuleType.SCORING:
            # Convert simple scoring rule
            if "expected_value" in legacy_rule:
                new_rule["condition"] = {
                    "question_id": str(legacy_rule["question_id"]),
                    "operator": ConditionOperator.EQUALS.value,
                    "value": legacy_rule["expected_value"]
                }
            else:
                warnings.append("Scoring rule missing expected_value, skipping conversion")
                return None, warnings

        else:  # BONUS rule
            # Convert compound condition
            if "compound_condition" in legacy_rule:
                new_rule["condition"] = convert_compound_condition(
                    legacy_rule["compound_condition"],
                    warnings
                )
            else:
                warnings.append("Bonus rule missing compound_condition, skipping conversion")
                return None, warnings

        # Handle repeatable section logic
        if legacy_rule.get("is_repeatable"):
            new_rule["section_id"] = legacy_rule.get("section_id")
            
            # Convert aggregation type
            old_agg = legacy_rule.get("aggregation_type")
            if old_agg:
                agg_map = {
                    "avg": AggregationType.AVG,
                    "min": AggregationType.MIN,
                    "max": AggregationType.MAX,
                    "count": AggregationType.COUNT,
                    "percent": AggregationType.PERCENTAGE,
                    "all_match": AggregationType.ALL,
                    "any_match": AggregationType.ANY
                }
                new_rule["aggregation"] = agg_map.get(old_agg, AggregationType.NONE).value
            else:
                new_rule["aggregation"] = AggregationType.NONE.value

            # Convert aggregation field
            if legacy_rule.get("aggregation_field"):
                new_rule["value_field"] = legacy_rule["aggregation_field"]

            # Convert compound filters
            if legacy_rule.get("compound_filter"):
                new_rule["filter"] = convert_compound_filter(
                    legacy_rule["compound_filter"],
                    warnings
                )

        return new_rule, warnings

    except Exception as e:
        logger.error(f"Error converting legacy rule: {str(e)}", exc_info=True)
        return None, [f"Conversion failed: {str(e)}"]

def convert_compound_condition(
    legacy_condition: Dict[str, Any],
    warnings: List[str]
) -> Dict[str, Any]:
    """
    Convert a legacy compound condition to the new format.

    Args:
        legacy_condition: Dictionary containing the legacy compound condition
        warnings: List to append warnings to

    Returns:
        Dictionary with the new compound condition format
    """
    try:
        # Handle simple condition
        if "operator" in legacy_condition and "value" in legacy_condition:
            return {
                "question_id": str(legacy_condition.get("question_id", "")),
                "operator": legacy_condition["operator"],
                "value": legacy_condition["value"]
            }

        # Handle compound condition
        if "operator" in legacy_condition and "conditions" in legacy_condition:
            operator = legacy_condition["operator"].lower()
            if operator not in [op.value for op in LogicalOperator]:
                warnings.append(f"Unknown logical operator: {operator}, using AND")
                operator = LogicalOperator.AND.value

            return {
                "operator": operator,
                "conditions": [
                    convert_compound_condition(cond, warnings)
                    for cond in legacy_condition["conditions"]
                ]
            }

        warnings.append("Invalid compound condition structure")
        return {
            "operator": LogicalOperator.AND.value,
            "conditions": []
        }

    except Exception as e:
        logger.error(f"Error converting compound condition: {str(e)}", exc_info=True)
        warnings.append(f"Failed to convert compound condition: {str(e)}")
        return {
            "operator": LogicalOperator.AND.value,
            "conditions": []
        }

def convert_compound_filter(
    legacy_filter: List[Dict[str, Any]],
    warnings: List[str]
) -> Dict[str, Any]:
    """
    Convert a legacy compound filter to the new format.

    Args:
        legacy_filter: List of legacy filter conditions
        warnings: List to append warnings to

    Returns:
        Dictionary with the new filter condition format
    """
    try:
        if not legacy_filter:
            return None

        # Convert to a single compound condition with AND operator
        return {
            "operator": LogicalOperator.AND.value,
            "conditions": [
                {
                    "question_id": str(cond.get("question_id", "")),
                    "operator": cond.get("operator", ConditionOperator.EQUALS.value),
                    "value": cond.get("value")
                }
                for cond in legacy_filter
                if "question_id" in cond and "operator" in cond and "value" in cond
            ]
        }

    except Exception as e:
        logger.error(f"Error converting compound filter: {str(e)}", exc_info=True)
        warnings.append(f"Failed to convert compound filter: {str(e)}")
        return None

async def migrate_thesis_rules(
    thesis_id: str,
    thesis_service: ThesisServiceInterface
) -> Dict[str, Any]:
    """
    Migrate all scoring rules for a thesis to the new format.

    Args:
        thesis_id: ID of the thesis to migrate
        thesis_service: Thesis service instance

    Returns:
        Dictionary with migration results
    """
    try:
        # Get all existing rules
        legacy_rules = await thesis_service.list_scoring_rules(thesis_id)
        if not legacy_rules:
            return {
                "success": True,
                "message": "No rules to migrate",
                "migrated": 0,
                "failed": 0,
                "warnings": []
            }

        results = {
            "success": True,
            "migrated": 0,
            "failed": 0,
            "warnings": []
        }

        # Convert each rule
        for legacy_rule in legacy_rules:
            converted_rule, warnings = convert_legacy_rule(legacy_rule.model_dump())
            
            if converted_rule:
                try:
                    # Create new rule
                    await thesis_service.create_scoring_rule(
                        thesis_id=thesis_id,
                        **converted_rule
                    )
                    results["migrated"] += 1
                    results["warnings"].extend(warnings)
                except Exception as e:
                    logger.error(f"Error creating converted rule: {str(e)}", exc_info=True)
                    results["failed"] += 1
                    results["warnings"].append(f"Failed to create converted rule: {str(e)}")
            else:
                results["failed"] += 1
                results["warnings"].extend(warnings)

        # Update results
        if results["failed"] > 0:
            results["success"] = False
            results["message"] = f"Migrated {results['migrated']} rules, {results['failed']} failed"
        else:
            results["message"] = f"Successfully migrated {results['migrated']} rules"

        return results

    except Exception as e:
        logger.error(f"Error migrating thesis rules: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": f"Migration failed: {str(e)}",
            "migrated": 0,
            "failed": len(legacy_rules) if 'legacy_rules' in locals() else 0,
            "warnings": [f"Migration failed: {str(e)}"]
        } 