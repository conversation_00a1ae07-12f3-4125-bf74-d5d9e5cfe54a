"""
Pitch Parser Service Implementation

Handles LLM-based parsing of pitch decks and one-pagers to extract structured data.
"""

import json
import re
from typing import Any, Dict, List, Optional

from app.core.logging import get_logger
from app.services.base import BaseService
from app.services.openai.client import OpenAIClient
from app.services.pitch_parser.interface import PitchParserInterface

logger = get_logger(__name__)


class PitchParserService(BaseService, PitchParserInterface):
    """LLM-based pitch parser service using OpenAI."""

    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.openai_client = None

    async def initialize(self) -> None:
        """Initialize the pitch parser service."""
        try:
            self.openai_client = OpenAIClient()
            self.logger.info("Pitch parser service initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize pitch parser service: {str(e)}")
            raise

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        try:
            self.openai_client = None
            self.logger.info("Pitch parser service cleanup completed")
        except Exception as e:
            self.logger.error(f"Error during pitch parser service cleanup: {str(e)}")

    async def parse_pitch_content(
        self,
        text_content: str,
        pitch_type: str,
        pages: List[Dict[str, Any]],
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Parse pitch content using LLM to extract structured data."""
        try:
            if not self.openai_client:
                await self.initialize()

            if not self.openai_client:
                raise RuntimeError("Failed to initialize OpenAI client")
            logger.info(f"Text content of pitch parser: {text_content}")
            # Create the parsing prompt based on pitch type
            prompt = self._create_parsing_prompt(text_content, pitch_type, pages)

            self.logger.info(
                f"Starting pitch content parsing for {pitch_type} with {len(pages)} pages"
            )

            # Call OpenAI to parse the content with increased timeout for complex parsing
            response = await self.openai_client.create_chat_completion(
                messages=[
                    {"role": "system", "content": self._get_system_prompt(pitch_type)},
                    {"role": "user", "content": prompt},
                ],
                model="gpt-4",
                temperature=0.0,  # Low temperature for consistent extraction
                # max_tokens=8000,  # Increased for complex pitch parsing
            )

            # Extract content from response
            content = self.openai_client.extract_content_from_completion(response)
            logger.info(f"Content from pitch parser LLM CALL: {content}")
            # Parse the JSON response
            parsed_data = self._parse_llm_response(content)

            # Add metadata
            parsed_data["parsing_metadata"] = {
                "pitch_type": pitch_type,
                "page_count": len(pages),
                "text_length": len(text_content),
                "model_used": "gpt-4",
                "confidence_score": self._calculate_confidence_score(parsed_data),
                "processing_time_ms": self.openai_client.extract_performance_metadata(
                    response
                ).get("response_time_ms", 0),
            }

            self.logger.info(
                f"Successfully parsed {pitch_type} content with confidence score: {parsed_data['parsing_metadata']['confidence_score']}"
            )
            return parsed_data

        except Exception as e:
            error_msg = str(e)
            if "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                self.logger.error(f"Timeout error parsing pitch content: {error_msg}")
                error_msg = "Request timed out during pitch parsing. Please try again."
            else:
                self.logger.error(f"Error parsing pitch content: {error_msg}")

            # Return empty structure with error
            return {
                "company_name": None,
                "company_website": None,
                "tagline": None,
                "ask_amount": None,
                "team_summary": None,
                "traction": None,
                "market_size": None,
                "sector": [],
                "founders": [],
                "source_page_map": {},
                "parsing_metadata": {
                    "error": error_msg,
                    "pitch_type": pitch_type,
                    "confidence_score": 0.0,
                    "page_count": len(pages),
                    "text_length": len(text_content),
                },
            }

    async def generate_markdown_summary(
        self, parsed_data: Dict[str, Any], text_content: str, pitch_type: str
    ) -> str:
        """Generate a clean markdown summary for investor view."""
        try:
            if not self.openai_client:
                await self.initialize()

            if not self.openai_client:
                raise RuntimeError("Failed to initialize OpenAI client")

            # Create markdown generation prompt
            prompt = self._create_markdown_prompt(parsed_data, text_content, pitch_type)

            self.logger.info(f"Starting markdown summary generation for {pitch_type}")

            response = await self.openai_client.create_chat_completion(
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at creating clean, professional markdown summaries of startup pitches for investors. Create well-structured, scannable summaries with clear headers and bullet points.",
                    },
                    {"role": "user", "content": prompt},
                ],
                model="gpt-4",
                temperature=0.3,
                max_tokens=4000,  # Increased for comprehensive summaries
            )

            # Extract content from response
            markdown_content = self.openai_client.extract_content_from_completion(
                response
            )

            # Add metadata footer
            markdown_content += f"\n\n---\n*Generated from {pitch_type} • {parsed_data.get('parsing_metadata', {}).get('page_count', 'Unknown')} pages*"

            self.logger.info(
                f"Successfully generated markdown summary for {pitch_type}"
            )
            return markdown_content

        except Exception as e:
            error_msg = str(e)
            if "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                self.logger.error(
                    f"Timeout error generating markdown summary: {error_msg}"
                )
            else:
                self.logger.error(f"Error generating markdown summary: {error_msg}")

            # Return basic markdown with available data
            return self._create_fallback_markdown(parsed_data, pitch_type)

    async def generate_short_description(
        self, parsed_data: Dict[str, Any], text_content: str, pitch_type: str
    ) -> str:
        """Generate a concise short description (1-2 sentences) for the deal."""
        try:
            if not self.openai_client:
                await self.initialize()

            if not self.openai_client:
                raise RuntimeError("Failed to initialize OpenAI client")

            # Create short description prompt
            prompt = self._create_short_description_prompt(
                parsed_data, text_content, pitch_type
            )

            self.logger.info(f"Starting short description generation for {pitch_type}")

            response = await self.openai_client.create_chat_completion(
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at creating concise, compelling descriptions of startups for investors. Create a 1-2 sentence description that captures the essence of the company, its value proposition, and key differentiators.",
                    },
                    {"role": "user", "content": prompt},
                ],
                model="gpt-4",
                temperature=0.3,
                max_tokens=300,  # Increased for better descriptions
            )

            # Extract content from response
            short_description = self.openai_client.extract_content_from_completion(
                response
            )

            # Clean up the description
            short_description = short_description.strip()
            if short_description.startswith('"') and short_description.endswith('"'):
                short_description = short_description[1:-1]

            self.logger.info(
                f"Successfully generated short description for {pitch_type}"
            )
            return short_description

        except Exception as e:
            error_msg = str(e)
            if "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                self.logger.error(
                    f"Timeout error generating short description: {error_msg}"
                )
            else:
                self.logger.error(f"Error generating short description: {error_msg}")

            # Return fallback description
            return self._create_fallback_short_description(parsed_data, pitch_type)

    async def generate_executive_summary(
        self, parsed_data: Dict[str, Any], text_content: str, pitch_type: str
    ) -> Dict[str, Any]:
        """Generate a comprehensive executive summary in structured JSON format."""
        try:
            if not self.openai_client:
                await self.initialize()

            if not self.openai_client:
                raise RuntimeError("Failed to initialize OpenAI client")

            # Create executive summary prompt
            prompt = self._create_executive_summary_prompt(
                parsed_data, text_content, pitch_type
            )

            self.logger.info(f"Starting executive summary generation for {pitch_type}")

            response = await self.openai_client.create_chat_completion(
                messages=[
                    {
                        "role": "system",
                        "content": "You are a world-class venture capital analyst at a top-tier firm like Sequoia, analyzing a startup pitch deck for internal investment committee use.\n\nWrite a sharp, insight-rich, investor-grade executive summary, tailored to the contents of the deck.\n\nDo not use a fixed format. Instead:\n- Identify the strongest signals and themes in the deck.\n- Create custom headers and a logical flow based on the startup's story.\n- Use crisp, institutional language—avoid vague or promotional fluff.\n- If something is unclear or weak (e.g. traction, team), surface it gently as a soft risk.\n- Extract actual numbers, percentages, and specific data from the text - do not use placeholders like 'XX' or 'X%'.\n- If specific data is not available, state that clearly rather than using placeholders.\n\nOutput should be returned in structured JSON with multiple titled blocks for rendering and PDF export.",
                    },
                    {"role": "user", "content": prompt},
                ],
                model="gpt-4",
                temperature=0.2,
                max_tokens=4000,  # Increased for comprehensive executive summaries
            )

            # Extract content from response
            response_content = self.openai_client.extract_content_from_completion(
                response
            )

            # Parse the JSON response
            executive_summary_json = self._parse_executive_summary_json(
                response_content
            )

            # If parsing failed, use fallback with actual parsed_data
            if executive_summary_json is None:
                self.logger.warning(
                    "Using fallback executive summary with actual parsed data"
                )
                executive_summary_json = self._create_fallback_executive_summary_json(
                    parsed_data, pitch_type
                )

            self.logger.info(
                f"Successfully generated executive summary for {pitch_type}"
            )
            return executive_summary_json

        except Exception as e:
            error_msg = str(e)
            if "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                self.logger.error(
                    f"Timeout error generating executive summary: {error_msg}"
                )
            else:
                self.logger.error(f"Error generating executive summary: {error_msg}")

            # Return fallback executive summary
            return self._create_fallback_executive_summary_json(parsed_data, pitch_type)

    def _get_system_prompt(self, pitch_type: str) -> str:
        """Get the system prompt for the LLM based on pitch type."""
        base_prompt = """You are an expert startup pitch analyzer. Extract structured information from pitch content and return it as valid JSON.

            IMPORTANT: Always return valid JSON with ALL required fields, even if some information is not found (use null for missing values).

            Required JSON structure:
            {
            "company_name": "string or null",
            "company_website": "string or null",
            "tagline": "string or null", 
            "ask_amount": "string or null",
            "team_summary": "string or null",
            "traction": "string or null",
            "market_size": "string or null",
            "sector": ["array", "of", "strings"],
            "founders": [
                {
                    "name": "string or null",
                    "role": ["array", "of", "roles"],
                    "linkedin": "string or null",
                    "email": "string or null",
                    "serial_founder": boolean,
                    "experience": [
                        {
                            "company": "string",
                            "position": "string",
                            "duration": "string",
                            "description": "string"
                        }
                    ],
                    "skills": ["array", "of", "skills"],
                    "education": [
                        {
                            "institution": "string",
                            "degree": "string",
                            "field": "string",
                            "year": "string"
                        }
                    ],
                    "achievements": ["array", "of", "achievements"]
                }
            ],
            "source_page_map": {
                "company_name": 1,
                "company_website": 1,
                "ask_amount": 8,
                "founders": [2, 3]
            }
            }

            FOUNDER EXTRACTION GUIDELINES:
            - Look for team/founder sections, about us, leadership slides
            - Extract full names, titles/roles, LinkedIn profiles, emails
            - Identify serial founders (those with previous startup experience)
            - Extract relevant work experience, education, skills, and achievements
            - If founder info is limited, extract what's available and use null for missing fields
            - For roles, include both current role and any mentioned previous roles
            - Look for patterns like "CEO & Co-founder", "CTO", "Head of Engineering", etc."""

        if pitch_type == "one_pager":
            return (
                base_prompt
                + "\n\nThis is a ONE-PAGER document (1-2 pages). Information will be condensed and may be in paragraph form rather than distinct slides."
            )
        else:
            return (
                base_prompt
                + "\n\nThis is a PITCH DECK (multiple slides). Look for information across different slides/sections."
            )

    def _create_parsing_prompt(
        self, text_content: str, pitch_type: str, pages: List[Dict[str, Any]]
    ) -> str:
        """Create the parsing prompt for the LLM."""
        prompt = f"""Analyze this {pitch_type} and extract the following information:

1. Company name
2. Company website (look for URLs, contact info, footer, also there is a chance the website given is of the product, not the company. So check for things like v1 or app.com or something like that. Make sure you get the company website.)
3. Tagline/description
4. Funding ask amount (e.g., "$1.5M", "Series A $5M")
5. Team summary (key founders and their backgrounds)
6. Traction (metrics, revenue, customers, growth)
7. Market size (TAM, SAM, market opportunity)
8. Sector/industry (as array of strings)
9. Founders (detailed information for each founder)
10. Source page map (which page each key info was found on)

FOUNDER EXTRACTION FOCUS:
- Look for team/founder sections, about us, leadership slides
- Extract full names, titles/roles, LinkedIn profiles, emails
- Identify serial founders (those with previous startup experience)
- Extract relevant work experience, education, skills, and achievements
- Pay attention to patterns like "CEO & Co-founder", "CTO", "Head of Engineering"
- Look for educational background, previous companies, and notable achievements
- If founder info is limited, extract what's available

CONTENT TO ANALYZE:
{text_content}  # Limit content to avoid token limits

PAGE BREAKDOWN:
"""

        for page in pages[:10]:  # Limit to first 10 pages
            prompt += f"Page {page['page_number']}: {page['text'][:500]}...\n\n"

        prompt += """Return the extracted information as valid JSON following this exact structure:

{
  "company_name": "Company Name",
  "company_website": "https://company.com",
  "tagline": "Company tagline or description",
  "ask_amount": "$1.5M Series A",
  "team_summary": "Brief team overview",
  "traction": "Revenue, customers, growth metrics",
  "market_size": "TAM/SAM information",
  "sector": ["Technology", "SaaS", "AI"],
  "founders": [
    {
      "name": "Founder Name",
      "role": ["CEO", "Co-founder"],
      "linkedin": "https://linkedin.com/in/founder",
      "email": "<EMAIL>",
      "serial_founder": true,
      "experience": [
        {
          "company": "Previous Company",
          "position": "CEO",
          "duration": "2018-2022"
        }
      ],
      "education": [
        {
          "institution": "University Name",
          "degree": "MBA",
          "field": "Business"
        }
      ]
    }
  ],
  "source_page_map": {
    "company_name": 1,
    "team": 3,
    "traction": 5
  }
}

Return ONLY valid JSON with the exact structure specified above."""

        return prompt

    def _create_markdown_prompt(
        self, parsed_data: Dict[str, Any], text_content: str, pitch_type: str
    ) -> str:
        """Create the markdown generation prompt."""
        return f"""Create a professional markdown summary for investors based on this parsed pitch data:

PARSED DATA:
{json.dumps(parsed_data, indent=2)}

        PITCH TYPE: {pitch_type}

        Create a clean, scannable markdown summary with:
        - Clear headers (## Company, ## Team, ## Traction, etc.)
        - Bullet points for key information
        - Professional tone suitable for investor review
        - Page references where available (e.g., "Ask: $1.5M (Page 8)")
        - Company website and contact information, also there is a change the website give is of the product, not the company. So check for things like v1 or app.com or something like that. Make sure you get the company website.
        - Detailed founder information including roles, experience, and achievements

        FOUNDER SECTION GUIDELINES:
        - List each founder with their name, role, and key background
        - Highlight serial founders and their previous startup experience
        - Include relevant education, skills, and notable achievements
        - Mention LinkedIn profiles and contact information if available
        - Focus on experience that's relevant to the current venture

        Focus on the most important information for investment decisions."""

    def _parse_llm_response(self, response_content: str) -> Dict[str, Any]:
        """Parse the LLM response and extract JSON."""
        self.logger.info(f"Parsing LLM response: {response_content[:200]}...")

        try:
            # Try to find JSON in the response - be more flexible with regex
            json_match = re.search(r"\{[\s\S]*\}", response_content)
            if json_match:
                json_str = json_match.group()
                self.logger.info(f"Found JSON match: {json_str[:200]}...")

                try:
                    parsed_json = json.loads(json_str)
                    self.logger.info("Successfully parsed JSON from match")
                    return parsed_json
                except json.JSONDecodeError as e:
                    self.logger.error(f"JSON decode error in match: {str(e)}")

            # If no JSON found or invalid, try parsing the whole response
            try:
                parsed_json = json.loads(response_content)
                self.logger.info("Successfully parsed full response as JSON")
                return parsed_json
            except json.JSONDecodeError as e:
                self.logger.error(f"JSON decode error in full response: {str(e)}")

        except Exception as e:
            self.logger.error(f"Unexpected error parsing LLM response: {str(e)}")

        # If we get here, parsing failed - return empty structure
        self.logger.warning("Using empty structure due to parsing failure")
        return {
            "company_name": None,
            "company_website": None,
            "tagline": None,
            "ask_amount": None,
            "team_summary": None,
            "traction": None,
            "market_size": None,
            "sector": [],
            "founders": [],
            "source_page_map": {},
        }

    def _calculate_confidence_score(self, parsed_data: Dict[str, Any]) -> float:
        """Calculate confidence score based on how much data was extracted."""
        total_fields = 9  # company_name, company_website, tagline, ask_amount, team_summary, traction, market_size, sector, founders
        filled_fields = 0

        key_fields = [
            "company_name",
            "company_website",
            "tagline",
            "ask_amount",
            "team_summary",
            "traction",
            "market_size",
            "sector",
            "founders",
        ]

        for field in key_fields:
            value = parsed_data.get(field)
            if (
                value
                and (isinstance(value, str) and value.strip())
                or (isinstance(value, list) and len(value) > 0)
            ):
                filled_fields += 1

        return round(filled_fields / total_fields, 2)

    def _create_fallback_markdown(
        self, parsed_data: Dict[str, Any], pitch_type: str
    ) -> str:
        """Create a basic markdown summary when LLM generation fails."""
        markdown = "# Pitch Summary\n\n"

        if parsed_data.get("company_name"):
            markdown += f"**Company:** {parsed_data['company_name']}\n\n"

        if parsed_data.get("company_website"):
            markdown += f"**Website:** {parsed_data['company_website']}\n\n"

        if parsed_data.get("tagline"):
            markdown += f"**Tagline:** {parsed_data['tagline']}\n\n"

        if parsed_data.get("ask_amount"):
            markdown += f"**Ask:** {parsed_data['ask_amount']}\n\n"

        if parsed_data.get("sector"):
            markdown += f"**Sector:** {', '.join(parsed_data['sector'])}\n\n"

        # Add founder information
        founders = parsed_data.get("founders", [])
        if founders:
            markdown += "## Founders\n\n"
            for founder in founders:
                markdown += f"**{founder.get('name', 'Unknown')}**"
                if founder.get("role"):
                    roles = (
                        founder["role"]
                        if isinstance(founder["role"], list)
                        else [founder["role"]]
                    )
                    markdown += f" - {', '.join(roles)}"
                markdown += "\n"

                if founder.get("serial_founder"):
                    markdown += "  - Serial founder\n"

                if founder.get("experience"):
                    markdown += "  - **Experience:**\n"
                    for exp in founder["experience"][:3]:  # Show first 3 experiences
                        if isinstance(exp, dict):
                            company = exp.get("company", "Unknown")
                            position = exp.get("position", "")
                            markdown += f"    - {position} at {company}\n"

                if founder.get("education"):
                    markdown += "  - **Education:**\n"
                    for edu in founder["education"][
                        :2
                    ]:  # Show first 2 education entries
                        if isinstance(edu, dict):
                            institution = edu.get("institution", "Unknown")
                            degree = edu.get("degree", "")
                            markdown += f"    - {degree} from {institution}\n"

                if founder.get("linkedin"):
                    markdown += f"  - LinkedIn: {founder['linkedin']}\n"

                markdown += "\n"
        elif parsed_data.get("team_summary"):
            markdown += f"## Team\n{parsed_data['team_summary']}\n\n"

        if parsed_data.get("traction"):
            markdown += f"## Traction\n{parsed_data['traction']}\n\n"

        if parsed_data.get("market_size"):
            markdown += f"## Market\n{parsed_data['market_size']}\n\n"

        markdown += f"\n---\n*Generated from {pitch_type} • Fallback summary*"

        return markdown

    def _create_short_description_prompt(
        self, parsed_data: Dict[str, Any], text_content: str, pitch_type: str
    ) -> str:
        """Create the short description generation prompt."""
        return f"""Create a compelling 1-2 sentence description for this {pitch_type} that would appear in a deal database or investor dashboard.

PARSED DATA:
{json.dumps(parsed_data, indent=2)}

PITCH TYPE: {pitch_type}

Requirements:
- 1-2 sentences maximum
- Capture the company's core value proposition
- Include key differentiators or unique aspects
- Professional tone suitable for investors
- Focus on what makes this company special

Create a concise, compelling description that would make an investor want to learn more."""

    def _create_fallback_short_description(
        self, parsed_data: Dict[str, Any], pitch_type: str
    ) -> str:
        """Create a fallback short description when LLM generation fails."""
        company_name = parsed_data.get("company_name", f"Company from {pitch_type}")
        tagline = parsed_data.get("tagline", "")
        sector = parsed_data.get("sector", [])

        if tagline:
            return f"{company_name}: {tagline}"
        elif sector:
            return f"{company_name} is a {', '.join(sector[:2])} company."
        else:
            return f"{company_name} is an innovative startup seeking investment."

    def _create_executive_summary_prompt(
        self, parsed_data: Dict[str, Any], text_content: str, pitch_type: str
    ) -> str:
        """Create the executive summary generation prompt for structured JSON output."""
        return f"""Below is the raw content extracted from the pitch deck:

{text_content}

Create a one-pager executive summary that adapts to the content above.

Use custom headers. Think like a VC partner writing a note to IC.

Output MUST be JSON in this structure:

{{
  "executive_summary": [
    {{
      "title": "Team Strength",
      "content": "..."
    }},
    {{
      "title": "Market Opportunity", 
      "content": "..."
    }},
    ...
  ],
  "metadata": {{
    "model_used": "gpt-4o",
    "generated_at": "ISO 8601 timestamp"
  }}
}}

GUIDELINES:
- Identify the strongest signals and themes in the deck
- Create custom headers based on the startup's story (e.g., "Why Now", "Team Strength", "Traction to Date")
- Use crisp, institutional language—avoid vague or promotional fluff
- If something is unclear or weak (e.g. traction, team), surface it gently as a soft risk
- Focus on actionable intelligence for investors
- Each section should be 2-4 sentences maximum
- Include specific data points and metrics when available
- Reference founder experience, market size, traction metrics, etc.
- Extract actual numbers, percentages, and specific data from the text - do not use placeholders like "XX" or "X%"

TEAM SECTION GUIDELINES:
- Highlight each founder's key qualifications and experience
- Emphasize serial founders and their previous startup success
- Include relevant educational background and industry experience
- Focus on experience that directly relates to the current venture
- Mention any notable achievements or recognitions

Return ONLY valid JSON with the exact structure specified above."""

    def _parse_executive_summary_json(
        self, response_content: str
    ) -> Optional[Dict[str, Any]]:
        """Parse the LLM response for executive summary JSON."""
        self.logger.info(
            f"Parsing executive summary response: {response_content[:200]}..."
        )

        try:
            # Try to find JSON in the response - be more flexible with regex
            json_match = re.search(r"\{[\s\S]*\}", response_content)
            if json_match:
                json_str = json_match.group()
                self.logger.info(f"Found JSON match: {json_str[:200]}...")

                try:
                    parsed_json = json.loads(json_str)

                    # Validate the structure
                    if "executive_summary" in parsed_json and "metadata" in parsed_json:
                        self.logger.info(
                            "Successfully parsed valid executive summary JSON"
                        )
                        return parsed_json
                    else:
                        self.logger.warning(
                            f"Invalid executive summary JSON structure. Keys found: {list(parsed_json.keys())}"
                        )
                        # Try to fix the structure if possible
                        if "executive_summary" in parsed_json:
                            # Add missing metadata
                            from datetime import datetime, timezone

                            parsed_json["metadata"] = {
                                "model_used": "gpt-4",
                                "generated_at": datetime.now(timezone.utc).isoformat(),
                            }
                            return parsed_json
                except json.JSONDecodeError as e:
                    self.logger.error(f"JSON decode error in match: {str(e)}")

            # If no JSON found or invalid, try parsing the whole response
            try:
                parsed_json = json.loads(response_content)

                # Validate the structure
                if "executive_summary" in parsed_json and "metadata" in parsed_json:
                    self.logger.info("Successfully parsed full response as JSON")
                    return parsed_json
                else:
                    self.logger.warning(
                        f"Invalid JSON structure in full response. Keys found: {list(parsed_json.keys())}"
                    )
            except json.JSONDecodeError as e:
                self.logger.error(f"JSON decode error in full response: {str(e)}")

        except Exception as e:
            self.logger.error(
                f"Unexpected error parsing executive summary JSON: {str(e)}"
            )

        # If we get here, we need to use fallback
        self.logger.warning("Using fallback executive summary due to parsing failure")
        return None  # Return None to indicate fallback needed

    def _create_fallback_executive_summary_json(
        self, parsed_data: Dict[str, Any], pitch_type: str
    ) -> Dict[str, Any]:
        """Create a fallback executive summary in structured JSON format using actual parsed data."""
        company_name = parsed_data.get("company_name", f"Company from {pitch_type}")
        company_website = parsed_data.get("company_website", "")
        tagline = parsed_data.get("tagline", "")
        sector = parsed_data.get("sector", [])
        team_summary = parsed_data.get("team_summary", "")
        traction = parsed_data.get("traction", "")
        market_size = parsed_data.get("market_size", "")
        ask_amount = parsed_data.get("ask_amount", "")
        founders = parsed_data.get("founders", [])

        # Create structured JSON with dynamic sections based on available data
        executive_summary_sections = []

        # Company Overview section - always include
        company_overview = f"{company_name}"
        if tagline:
            company_overview += f": {tagline}"
        if company_website:
            company_overview += f" Website: {company_website}"
        if sector:
            company_overview += f" Sector: {', '.join(sector)}"

        executive_summary_sections.append({
            "title": "Company Overview",
            "content": company_overview,
        })

        # Market Opportunity section
        if market_size:
            executive_summary_sections.append({
                "title": "Market Opportunity",
                "content": market_size,
            })

        # Team section - prioritize founders data over team_summary
        if founders:
            team_content = "Key team members:\n"
            for founder in founders:
                founder_name = founder.get("name", "Unknown")
                roles = founder.get("role", [])
                if isinstance(roles, list):
                    roles_str = ", ".join(roles)
                else:
                    roles_str = str(roles)
                team_content += f"• {founder_name} - {roles_str}\n"

                if founder.get("serial_founder"):
                    team_content += (
                        "  (Serial founder with previous startup experience)\n"
                    )

                if founder.get("experience"):
                    team_content += "  Key Experience:\n"
                    for exp in founder["experience"][:2]:  # Show first 2 experiences
                        if isinstance(exp, dict):
                            company = exp.get("company", "Unknown")
                            position = exp.get("position", "")
                            team_content += f"    - {position} at {company}\n"

                if founder.get("education"):
                    team_content += "  Education:\n"
                    for edu in founder["education"][:1]:  # Show first education entry
                        if isinstance(edu, dict):
                            institution = edu.get("institution", "Unknown")
                            degree = edu.get("degree", "")
                            team_content += f"    - {degree} from {institution}\n"

                team_content += "\n"
        elif team_summary:
            team_content = team_summary
        else:
            team_content = "Team information not available"

        executive_summary_sections.append({"title": "Team", "content": team_content})

        # Traction section
        if traction:
            executive_summary_sections.append({
                "title": "Traction",
                "content": traction,
            })

        # Financial Highlights section
        if ask_amount:
            executive_summary_sections.append({
                "title": "Financial Highlights",
                "content": f"Funding Ask: {ask_amount}",
            })

        # Add metadata
        from datetime import datetime, timezone

        metadata = {
            "model_used": "fallback",
            "generated_at": datetime.now(timezone.utc).isoformat(),
            "note": "Generated from parsed data due to AI response parsing failure",
        }

        return {"executive_summary": executive_summary_sections, "metadata": metadata}
