# Scoring Utility Functions

This module provides utility functions for computing normalized match scores (0 to 1) between investor thesis preferences (T) and startup responses (S), based on question types.

## Overview

The scoring utilities are designed to be:

- **Fast**: All logic is synchronous and optimized for performance
- **Extensible**: Each function accepts `**kwargs` for optional configuration
- **Composable**: All functions are pure with no side effects or I/O
- **Maintainable**: Logic is documented with clear comments
- **Traceable**: Shared logic is defined in `base.py` and reused

## Usage

```python
from app.utils.score_match import score_short_text, score_number, score_multi_select

# Basic usage
text_score = score_short_text("AI technology", "Artificial Intelligence solutions")
number_score = score_number(100, 95)  # Within 10% buffer, returns 1.0
options_score = score_multi_select(["ai", "ml", "nlp"], ["ai", "ml"])  # Returns 2/3

# With configuration options
text_score = score_short_text("AI technology", "Blockchain", min_score=0.1)
number_score = score_number(100, 150, buffer=0.2, decay_factor=0.01)
options_score = score_multi_select(
    ["ai", "ml", "nlp"], 
    ["ai", "cloud"], 
    strategy='weighted',
    weights={"ai": 5, "ml": 2, "nlp": 1}
)
```

## Scoring Functions

### `score_short_text(thesis_text: str, answer_text: str, **kwargs) -> float`

Uses embedding-based cosine similarity to compare short text values.

**Configuration options:**
- `min_score`: Minimum score to return (default: 0.0)

### `score_long_text(thesis_text: str, answer_text: str, **kwargs) -> float`

Uses embedding-based cosine similarity to compare long text values.

**Configuration options:**
- `min_score`: Minimum score to return (default: 0.0)
- `semantic_threshold`: Threshold for semantic similarity (default: 0.7)

### `score_number(thesis_number: float, answer_number: float, **kwargs) -> float`

Scores numeric values based on proximity and thresholds.

**Configuration options:**
- `buffer`: Percentage buffer for near matches (default: 0.1 or 10%)
- `decay_factor`: Rate of score decay outside buffer (default: 0.05)
- `range_min`: Minimum acceptable value (default: thesis_number * 0.9)
- `range_max`: Maximum acceptable value (default: thesis_number * 1.1)

### `score_range(thesis_range: tuple, answer_range: tuple, **kwargs) -> float`

Scores range values based on overlap.

**Configuration options:**
- `normalize_by`: How to normalize overlap ('thesis', 'answer', 'union', 'min', 'max')
  Default is 'thesis' - normalize by thesis range size

### `score_single_select(thesis_option: str, answer_option: str, **kwargs) -> float`

Scores single select options with support for soft matching.

**Configuration options:**
- `soft_match_groups`: Dict mapping options to sets of acceptable alternatives
- `partial_match_score`: Score to assign for soft matches (default: 0.5)

### `score_multi_select(thesis_options: list, answer_options: list, **kwargs) -> float`

Scores multi-select options using set operations.

**Configuration options:**
- `strategy`: Scoring strategy ('coverage', 'jaccard', 'weighted')
- `weights`: Dict mapping options to weights (for 'weighted' strategy)
- `default_weight`: Default weight for options not in weights dict (default: 1.0)

### `score_boolean(thesis_bool: bool, answer_bool: bool, **kwargs) -> float`

Scores boolean values with configurable null handling.

**Configuration options:**
- `null_score`: Score to assign when answer is None (default: 0.5)
- `mismatch_score`: Score to assign for mismatches (default: 0.0)

### `score_file(thesis_text: str, extracted_file_text: str, **kwargs) -> float`

Scores file content against thesis text using embedding similarity.

**Configuration options:**
- Same as `score_long_text`

### `score_date(thesis_date: date, answer_date: date, **kwargs) -> float`

Scores dates based on proximity or range inclusion.

**Configuration options:**
- `max_days`: Maximum days difference for scoring (default: 180)
- `decay_factor`: Rate of score decay with distance (default: 0.05)
- `date_range`: Tuple of (start_date, end_date) for acceptable range
- `linear_decay`: Use linear instead of exponential decay (default: False)

## Base Utilities

The scoring functions rely on base utilities defined in `app/utils/base.py`:

- `get_embedding(text: str) -> List[float]`: Converts text to embedding vector
- `cosine_similarity(vec1: List[float], vec2: List[float]) -> float`: Calculates similarity between vectors
- `jaccard_similarity(set1: set, set2: set) -> float`: Calculates set similarity
- `normalize_range(value: float, min_val: float, max_val: float) -> float`: Normalizes value to [0,1]

## Integration

These scoring functions are designed to be integrated with the thesis matching system. They can be used to:

1. Score individual question responses against thesis preferences
2. Combine scores with question weights to calculate overall match scores
3. Support complex matching logic with configurable parameters

## Testing

Comprehensive tests are available in `tests/utils/test_score_match.py`.
