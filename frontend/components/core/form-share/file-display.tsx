"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FileText, Download, Eye, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { FileAPI } from '@/lib/api/file';

interface FileDisplayProps {
  fileAnswer: {
    file: string; // S3 key or file ID
    filename: string;
    size?: number;
    type?: string;
    uploaded_at?: string;
  };
  accessToken?: string;
  mode?: 'edit' | 'view'; // edit mode allows delete, view mode is read-only
  onDelete?: () => void;
  className?: string;
}

export function FileDisplay({
  fileAnswer,
  accessToken,
  mode = 'view',
  onDelete,
  className
}: FileDisplayProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadError, setDownloadError] = useState<string | null>(null);

  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return 'Unknown size';
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUploadDate = (dateString?: string): string => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return '';
    }
  };

  const getFileIcon = (filename: string, mimeType?: string) => {
    const extension = filename.split('.').pop()?.toLowerCase();
    
    // Use MIME type if available, otherwise fall back to extension
    if (mimeType?.startsWith('image/') || ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension || '')) {
      return <FileText className="size-8 text-blue-600" />;
    }
    
    if (mimeType === 'application/pdf' || extension === 'pdf') {
      return <FileText className="size-8 text-red-600" />;
    }
    
    if (mimeType?.includes('word') || ['doc', 'docx'].includes(extension || '')) {
      return <FileText className="size-8 text-blue-700" />;
    }
    
    if (mimeType?.includes('excel') || mimeType?.includes('spreadsheet') || ['xls', 'xlsx', 'csv'].includes(extension || '')) {
      return <FileText className="size-8 text-green-600" />;
    }
    
    return <FileText className="size-8 text-gray-600" />;
  };

  const handleDownload = async () => {
    if (!fileAnswer.file) return;
    
    setIsDownloading(true);
    setDownloadError(null);
    
    try {
      const downloadResponse = await FileAPI.generateDownloadUrl(
        { file_id: fileAnswer.file },
        accessToken
      );
      
      // Open download URL in new tab for preview or trigger download
      const link = document.createElement('a');
      link.href = downloadResponse.presigned_url;
      link.download = fileAnswer.filename;
      
      // For images and PDFs, try to open in new tab for preview
      if (fileAnswer.type?.startsWith('image/') || fileAnswer.type === 'application/pdf') {
        link.target = '_blank';
        link.rel = 'noopener noreferrer';
      }
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
    } catch (error) {
      console.error('Download error:', error);
      setDownloadError(
        error instanceof Error 
          ? error.message 
          : 'Download failed. Please try again or contact support.'
      );
    } finally {
      setIsDownloading(false);
    }
  };

  const handleDelete = async () => {
    if (!fileAnswer.file || !onDelete) return;
    
    try {
      await FileAPI.deleteFile(fileAnswer.file, accessToken);
      onDelete();
    } catch (error) {
      console.error('Delete error:', error);
      setDownloadError(
        error instanceof Error 
          ? error.message 
          : 'Delete failed. Please try again.'
      );
    }
  };

  const canPreview = fileAnswer.type?.startsWith('image/') || fileAnswer.type === 'application/pdf';

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn(
        "rounded-lg border bg-white p-4 shadow-sm transition-shadow hover:shadow-md",
        className
      )}
    >
      <div className="flex items-start space-x-3">
        {/* File Icon */}
        <div className="shrink-0">
          {getFileIcon(fileAnswer.filename, fileAnswer.type)}
        </div>
        
        {/* File Info */}
        <div className="min-w-0 flex-1">
          <h4 className="truncate text-sm font-medium text-gray-900">
            {fileAnswer.filename}
          </h4>
          
          <div className="mt-1 space-y-1 text-xs text-gray-500">
            {fileAnswer.size && (
              <p>{formatFileSize(fileAnswer.size)}</p>
            )}
            {fileAnswer.uploaded_at && (
              <p>Uploaded {formatUploadDate(fileAnswer.uploaded_at)}</p>
            )}
          </div>
          
          {downloadError && (
            <div className="mt-2 flex items-center space-x-1 text-xs text-red-600">
              <AlertCircle className="size-3" />
              <span>{downloadError}</span>
            </div>
          )}
        </div>
        
        {/* Actions */}
        <div className="flex shrink-0 items-center space-x-2">
          {/* Download/Preview Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
            disabled={isDownloading}
            className="text-xs"
          >
            {isDownloading ? (
              <>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="mr-1 size-3 rounded-full border border-gray-400 border-t-transparent"
                />
                Loading...
              </>
            ) : canPreview ? (
              <>
                <Eye className="mr-1 size-3" />
                Preview
              </>
            ) : (
              <>
                <Download className="mr-1 size-3" />
                Download
              </>
            )}
          </Button>
          
          {/* Delete Button (only in edit mode) */}
          {mode === 'edit' && onDelete && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleDelete}
              className="text-xs text-red-600 hover:bg-red-50 hover:text-red-800"
            >
              Remove
            </Button>
          )}
        </div>
      </div>
    </motion.div>
  );
}

interface FileListDisplayProps {
  files: Array<{
    file: string;
    filename: string;
    size?: number;
    type?: string;
    uploaded_at?: string;
  }>;
  accessToken?: string;
  mode?: 'edit' | 'view';
  onFileDelete?: (index: number) => void;
  className?: string;
}

export function FileListDisplay({
  files,
  accessToken,
  mode = 'view',
  onFileDelete,
  className
}: FileListDisplayProps) {
  if (!files || files.length === 0) {
    return (
      <div className={cn("text-sm italic text-gray-500", className)}>
        No files uploaded
      </div>
    );
  }

  return (
    <div className={cn("space-y-3", className)}>
      {files.map((file, index) => (
        <FileDisplay
          key={`${file.file}-${index}`}
          fileAnswer={file}
          accessToken={accessToken}
          mode={mode}
          onDelete={onFileDelete ? () => onFileDelete(index) : undefined}
        />
      ))}
    </div>
  );
}
