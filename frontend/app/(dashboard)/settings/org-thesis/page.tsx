"use client"

import { useState, useEffect, useCallback } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { toast } from "sonner"
import { Star, AlertCircle } from "lucide-react"

import { SettingsLayout } from "@/components/core/settings/settings-layout"
import { OrgThesisForm } from "@/components/core/settings/org-thesis-form"
import { SettingsAPI, ThesisConfig } from "@/lib/api/settings-api"
import { useAuth } from "@/lib/auth-context"
import { cn } from "@/lib/utils"

export default function OrgThesisSettingsPage() {
  const router = useRouter()
  const { user } = useAuth()
  const [thesisConfig, setThesisConfig] = useState<ThesisConfig | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Check if user is admin based on superuser status or role
  const isAdmin = user?.is_superuser || (user?.role_id !== null && user?.role_id !== undefined)

  const loadThesisConfig = useCallback(async () => {
    try {
      console.log("🔄 Loading thesis config...")
      setIsLoading(true)
      const configData = await SettingsAPI.getThesisConfig()
      console.log("✅ Thesis config loaded:", configData)
      setThesisConfig(configData)
    } catch (error: any) {
      console.error("Failed to load thesis configuration:", error)
      // Don't show error toast for 404 - just means no config exists yet
      if (error.status !== 404) {
        toast.error("Failed to load thesis configuration")
      } else {
        // Set empty config for new organizations
        console.log("📝 Setting empty config for new organization")
        setThesisConfig({
          geography: [],
          sector: [],
          stage: [],
          business_model: []
        })
      }
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    // Redirect non-admin users
    if (!isLoading && !isAdmin) {
      toast.error("Access denied. Only organization admins can access thesis configuration.")
      router.push('/settings/profile')
      return
    }

    // Only load config once when admin status is confirmed and we don't have config yet
    if (isAdmin && !isLoading && !thesisConfig) {
      loadThesisConfig()
    }
  }, [isAdmin, isLoading, router, thesisConfig, loadThesisConfig])

  const handleThesisConfigUpdate = useCallback(async (data: ThesisConfig) => {
    try {
      await SettingsAPI.updateThesisConfig(data)
      toast.success("Org thesis updated successfully!")

      // Update local state instead of reloading from API
      setThesisConfig(data)
    } catch (error: any) {
      console.error("Failed to update thesis configuration:", error)
      toast.error(error.message || "Failed to update thesis configuration")
    }
  }, [])

  // Don't render anything for non-admin users while checking
  if (!isAdmin && !isLoading) {
    return null
  }

  if (isLoading) {
    return (
      <SettingsLayout activeTab="org-thesis">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex items-center justify-center p-8 md:p-12"
        >
          <div className={cn("bg-white/60 backdrop-blur-md rounded-2xl shadow-sm", "mx-auto max-w-md p-8 text-center")}>
            <div className="relative mb-6">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="border-3 mx-auto size-12 rounded-full border-blue-200 border-t-blue-600"
              />
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="absolute inset-0 flex items-center justify-center"
              >
                <Star className="size-6 text-blue-600" />
              </motion.div>
            </div>
            <h3 className="mb-2 text-lg font-semibold text-gray-900">Loading Thesis Configuration</h3>
            <p className="text-sm text-gray-600">Preparing your investment preferences...</p>
          </div>
        </motion.div>
      </SettingsLayout>
    )
  }

  if (!thesisConfig) {
    return (
      <SettingsLayout activeTab="org-thesis">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center py-8 md:py-12"
        >
          <div className={cn("bg-white/60 backdrop-blur-md rounded-2xl shadow-sm", "mx-auto max-w-md p-8 text-center")}>
            <div className="mx-auto mb-4 flex size-16 items-center justify-center rounded-full bg-red-50">
              <AlertCircle className="size-8 text-red-500" />
            </div>
            <h3 className="mb-2 text-xl font-semibold text-gray-900">Configuration Load Error</h3>
            <p className="mb-6 text-gray-600">Unable to load your thesis configuration.</p>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => window.location.reload()}
              className="touch-target rounded-xl bg-blue-600 px-6 py-3 font-medium text-white transition-colors hover:bg-blue-700"
            >
              Try Again
            </motion.button>
          </div>
        </motion.div>
      </SettingsLayout>
    )
  }

  return (
    <SettingsLayout activeTab="org-thesis">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="space-y-8"
      >
        <div className="w-full">
          <OrgThesisForm
            thesisConfig={thesisConfig}
            onUpdate={handleThesisConfigUpdate}
          />
        </div>
      </motion.div>
    </SettingsLayout>
  )
}
