// import { Metadata } from "next"
// import { PlaceholderScreen } from "@/components/placeholder-screen"
// import { siteConfig } from "@/config/site"

// export const metadata: Metadata = {
//   title: {
//     default: siteConfig.name,
//     template: `%s | ${siteConfig.name}`,
//   },
//   description: siteConfig.description,
// }

// export default function HomePage() {
//   return (
//     <PlaceholderScreen 
//       title="Welcome to TractionX"
//       description="The world's first agentic OS for private markets"
//       actionText="Get Started"
//       actionHref="/login"
//       showAnimation={true}
//     />
//   )
// }

import { Metadata } from "next"
import OrbitHero from "@/components/placeholder-screen"
import { siteConfig } from "@/config/site"

export const metadata: Metadata = {
  title: {
    default: siteConfig.name,
    template: `%s | ${siteConfig.name}`,
  },
  description: siteConfig.description,
}

export default function HomePage() {
  return (
    <OrbitHero />
  )
}