from typing import Optional

from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase

from app.core.config import settings


class Database:
    client: Optional[AsyncIOMotorClient]
    db: Optional[AsyncIOMotorDatabase] = None

    async def connect_to_database(self) -> None:
        """Create database connection."""
        self.client = AsyncIOMotorClient(settings.mongodb_connection_string)
        self.db = self.client[settings.MONGODB_DB_NAME]

    async def close_database_connection(self) -> None:
        """Close database connection."""
        if self.client:
            self.client.close()

    async def get_database(self) -> AsyncIOMotorDatabase:  # type: ignore
        """Get database instance."""
        if self.db is None:
            await self.connect_to_database()
        return self.db  # type: ignore


db = Database()


async def get_database() -> AsyncIOMotorDatabase:  # type: ignore
    """FastAPI dependency that provides a database session."""
    return await db.get_database()
