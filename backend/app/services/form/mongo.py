import asyncio
import re
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Set, Union

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.errors import DatabaseError
from app.core.logging import get_logger
from app.models.form import (
    Form,
    FormWithDetails,
    Question,
    Section,
    SectionWithQuestions,
    Submission,
    VisibilityCondition,
)
from app.services.base import BaseService
from app.services.form.interfaces import FormServiceInterface
from app.services.versioning import VersioningMixin
from app.utils.common import PyObjectId
from app.utils.form.form_logic import (
    build_visibility_graph,
    detect_cycles,
    evaluate_visibility,
    get_repeat_count,
    get_scoped_answer_key,
    validate_repeatable_section_nesting,
    validate_visibility_references,
)
from app.utils.model.type_casting import update_model_with_type_casting
from app.utils.scoring.score_match import (
    score_boolean,
    score_date,
    score_file,
    score_long_text,
    score_multi_select,
    score_number,
    score_range,
    score_short_text,
    score_single_select,
)

logger = get_logger(__name__)


class FormService(BaseService, FormServiceInterface, VersioningMixin):
    """
    MongoDB implementation of FormService.

    Enhanced with versioning capabilities through VersioningMixin.
    """

    def __init__(self, db: AsyncIOMotorDatabase):
        super().__init__(db=db)
        self.cache = None

    async def initialize(self) -> None:
        """Initialize database connection."""
        # self.cache = await ServiceFactory.get_service(RedisService)
        pass

    async def cleanup(self) -> None:
        """Cleanup database connection."""
        pass

    async def list_forms(
        self, org_id: str, is_active: bool = True
    ) -> List[Dict[str, Any]]:
        """List all forms in the organization."""
        try:
            query = {}
            if org_id is not None:
                query["org_id"] = ObjectId(org_id)
            if is_active is not None:
                query["is_active"] = bool(is_active)

            forms = await Form.find_many(query)
            await asyncio.gather(*[
                form.populate(fields=["created_by"]) for form in forms
            ])
            now_ts = int(datetime.now(timezone.utc).timestamp())
            result = []
            for form in forms:
                data = form.model_dump(by_alias=True)
                # Patch created_at/updated_at if missing or invalid
                for field in ("created_at", "updated_at"):
                    val = data.get(field)
                    if not isinstance(val, int) or val is None or val < 1000000000:
                        data[field] = now_ts
                result.append(data)
            return result
        except Exception as e:
            await self.handle_error(e, {"org_id": org_id, "is_active": is_active})
            return []

    async def get_form(self, form_id: str) -> Optional[Dict[str, Any]]:
        """Get form by ID with caching."""
        # Note: Caching is currently disabled but can be re-enabled in the future
        try:
            # Use the base model's find_one method
            form = await Form.find_one({"_id": ObjectId(form_id)})
            # Convert to dict before returning
            return form.model_dump() if form else None
        except Exception as e:
            await self.handle_error(e, {"form_id": form_id})

    async def get_form_with_details(self, form_id: str) -> Optional[FormWithDetails]:
        """
        Get form with nested sections and questions using standard base models.

        This method uses the base model's find_one method with detail=True to populate
        all related sections and questions, similar to the qualifier_form implementation.
        """
        try:
            # Get the form with all details populated
            form = await Form.find_one({"_id": ObjectId(form_id)})
            if not form:
                return None

            # Get all sections for this form
            sections = await Section.find_many(
                {"_id": {"$in": form.sections}}, sort=[("order", 1)]
            )

            # Create section models with populated questions
            section_models = []
            for section in sections:
                # Get questions for this section
                questions = await Question.find_many(
                    {"section_id": section.id}, sort=[("order", 1)]
                )

                # Create SectionWithQuestions model
                section_model = SectionWithQuestions(
                    id=section.id,
                    form_id=section.form_id,
                    title=section.title,
                    description=section.description,
                    order=section.order,
                    questions=questions,
                    repeatable=section.repeatable,
                    created_at=section.created_at,
                    updated_at=section.updated_at,
                )
                section_models.append(section_model)

            # Create the final response
            form_with_details = FormWithDetails(
                id=form.id,
                name=form.name,
                description=form.description,
                version=form.version,
                is_active=form.is_active,
                sections=section_models,
                default_section_ids=form.default_section_ids,
                created_at=form.created_at,
                updated_at=form.updated_at,
                created_by=form.created_by,
            )

            return form_with_details
        except Exception as e:
            await self.handle_error(e, {"form_id": form_id})

    async def create_form(
        self,
        name: str,
        description: str,
        is_active: bool = True,
        org_id: Optional[str] = None,
        user_id: Optional[str] = None,
    ) -> Optional[Dict[str, Any]]:
        """Create a new form with default Basic section and core field questions."""
        try:
            # Get org_id from request context if not provided
            if not org_id:
                # Try to get from request state
                request = getattr(self, "request", None)
                if request and hasattr(request.state, "org_id"):
                    org_id = request.state.org_id
                else:
                    raise ValueError("Organization ID is required to create a form")

            form = Form(
                name=name,
                description=description,
                is_active=is_active,
                version=1,
                org_id=PyObjectId(org_id),
                created_at=int(datetime.now(timezone.utc).timestamp()),
                updated_at=int(datetime.now(timezone.utc).timestamp()),
                created_by=PyObjectId(user_id) if user_id else PyObjectId(),
            )
            await form.save()

            # Create default "Basic" section with core field questions
            await self._create_default_basic_section(form.id)

            # Convert to dict before returning
            return form.model_dump(by_alias=True)
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "name": name,
                    "description": description,
                    "is_active": is_active,
                    "org_id": org_id,
                },
            )

    async def create_default_form(self, org_id: ObjectId) -> Optional[Dict[str, Any]]:
        """
        Create a comprehensive default form that demonstrates all question types,
        visibility conditions, and exclusions to help beta users understand the form builder.

        The form includes 3 sections:
        1. Basic Information - Core fields and simple questions
        2. Advanced Features - Demonstrates visibility conditions and complex logic
        3. Additional Details - Shows file uploads, dates, and validation rules
        """
        try:
            # Create the main form
            form = Form(
                name="Demo Form - Understanding Form Builder",
                description="This form demonstrates all available question types, visibility conditions, and validation rules. Use this as a reference to understand how to build effective forms.",
                is_active=True,
                version=1,
                org_id=PyObjectId(org_id),
                created_at=int(datetime.now(timezone.utc).timestamp()),
                updated_at=int(datetime.now(timezone.utc).timestamp()),
                created_by=PyObjectId(),  # System user
            )
            await form.save()
            await self._create_default_basic_section(form.id)
            form = await Form.find_one({"_id": form.id})

            # # Section 1: Basic Information
            # basic_section = Section(
            #     form_id=form.id,
            #     title="Basic Information",
            #     description="Essential company and founder information with core fields",
            #     order=0,
            #     repeatable=False,
            # )
            # await basic_section.save()

            # # Add section to form
            # form.sections.append(basic_section.id)
            # await form.save(is_update=True)

            # # Basic section questions
            # basic_questions = [
            #     {
            #         "type": "short_text",
            #         "label": "Company Name",
            #         "help_text": "Enter your company's official name",
            #         "required": True,
            #         "core_field": "company_name",
            #         "order": 0,
            #     },
            #     {
            #         "type": "short_text",
            #         "label": "Company Website",
            #         "help_text": "Your company's website URL",
            #         "required": True,
            #         "core_field": "company_website",
            #         "validation": {
            #             "regex": r"^https?://.*",
            #             "custom": "Must be a valid URL starting with http:// or https://",
            #         },
            #         "order": 1,
            #     },
            #     {
            #         "type": "single_select",
            #         "label": "Funding Stage",
            #         "help_text": "Select your current funding stage",
            #         "required": True,
            #         "core_field": "stage",
            #         "options": [
            #             {"value": "pre_seed", "label": "Pre-Seed"},
            #             {"value": "seed", "label": "Seed"},
            #             {"value": "series_a", "label": "Series A"},
            #             {"value": "series_b", "label": "Series B"},
            #             {"value": "series_c", "label": "Series C"},
            #             {"value": "series_d_plus", "label": "Series D+"},
            #             {"value": "growth", "label": "Growth"},
            #             {"value": "ipo", "label": "IPO"},
            #             {"value": "other", "label": "Other"},
            #         ],
            #         "order": 2,
            #     },
            #     {
            #         "type": "multi_select",
            #         "label": "Business Sectors",
            #         "help_text": "Select all sectors that apply to your business",
            #         "required": True,
            #         "core_field": "sector",
            #         "options": [
            #             {"value": "fintech", "label": "Fintech"},
            #             {"value": "saas", "label": "SaaS"},
            #             {"value": "ecommerce", "label": "E-commerce"},
            #             {"value": "healthtech", "label": "HealthTech"},
            #             {"value": "edtech", "label": "EdTech"},
            #             {"value": "proptech", "label": "PropTech"},
            #             {"value": "cleantech", "label": "CleanTech"},
            #             {"value": "biotech", "label": "BioTech"},
            #             {"value": "ai_ml", "label": "AI/ML"},
            #             {"value": "blockchain", "label": "Blockchain"},
            #             {"value": "cybersecurity", "label": "Cybersecurity"},
            #             {"value": "mobility", "label": "Mobility"},
            #             {"value": "gaming", "label": "Gaming"},
            #             {"value": "media", "label": "Media"},
            #             {"value": "retail", "label": "Retail"},
            #             {"value": "manufacturing", "label": "Manufacturing"},
            #             {"value": "other", "label": "Other"},
            #         ],
            #         "order": 3,
            #     },
            #     {
            #         "type": "number",
            #         "label": "Team Size",
            #         "help_text": "How many employees does your company have?",
            #         "required": False,
            #         "validation": {"min": 1, "max": 10000},
            #         "order": 4,
            #     },
            #     {
            #         "type": "range",
            #         "label": "Monthly Revenue (in thousands)",
            #         "help_text": "Select your monthly revenue range",
            #         "required": False,
            #         "validation": {"min": 0, "max": 1000},
            #         "order": 5,
            #     },
            # ]

            # # Create basic section questions
            # for question_data in basic_questions:
            #     question = Question(section_id=basic_section.id, **question_data)
            #     await question.save()
            #     basic_section.questions.append(question.id)

            # await basic_section.save(is_update=True)

            # Section 2: Advanced Features (demonstrates visibility conditions)
            advanced_section = Section(
                form_id=form.id,
                title="Advanced Features Demo",
                description="This section demonstrates visibility conditions and conditional logic",
                order=1,
                repeatable=False,
            )
            await advanced_section.save()

            form.sections.append(advanced_section.id)
            await form.save(is_update=True)

            # Create questions for advanced section (we'll reference them for visibility)
            advanced_questions = [
                {
                    "type": "boolean",
                    "label": "Do you have a technical co-founder?",
                    "help_text": "This question controls visibility of technical questions below",
                    "required": False,
                    "order": 0,
                },
                {
                    "type": "single_select",
                    "label": "What type of product are you building?",
                    "help_text": "This will show different follow-up questions",
                    "required": False,
                    "options": [
                        {"value": "mobile_app", "label": "Mobile App"},
                        {"value": "web_app", "label": "Web Application"},
                        {"value": "hardware", "label": "Hardware Product"},
                        {"value": "service", "label": "Service/Consulting"},
                        {"value": "other", "label": "Other"},
                    ],
                    "order": 1,
                },
                {
                    "type": "short_text",
                    "label": "What programming languages does your technical co-founder use?",
                    "help_text": "Only visible if you have a technical co-founder",
                    "required": False,
                    "order": 2,
                },
                {
                    "type": "long_text",
                    "label": "Describe your mobile app's key features",
                    "help_text": "Only visible if building a mobile app",
                    "required": False,
                    "order": 3,
                },
                {
                    "type": "long_text",
                    "label": "Describe your web application's architecture",
                    "help_text": "Only visible if building a web app",
                    "required": False,
                    "order": 4,
                },
                {
                    "type": "multi_select",
                    "label": "What hardware components are you using?",
                    "help_text": "Only visible if building hardware",
                    "required": False,
                    "options": [
                        {"value": "sensors", "label": "Sensors"},
                        {"value": "microcontrollers", "label": "Microcontrollers"},
                        {"value": "cameras", "label": "Cameras"},
                        {"value": "displays", "label": "Displays"},
                        {"value": "motors", "label": "Motors/Actuators"},
                        {"value": "other", "label": "Other"},
                    ],
                    "order": 5,
                },
                {
                    "type": "boolean",
                    "label": "Do you have paying customers?",
                    "help_text": "This controls revenue-related questions",
                    "required": False,
                    "order": 6,
                },
                {
                    "type": "number",
                    "label": "How many paying customers do you have?",
                    "help_text": "Only visible if you have paying customers",
                    "required": False,
                    "validation": {"min": 1, "max": 100000},
                    "order": 7,
                },
            ]

            # Create advanced section questions
            for question_data in advanced_questions:
                question = Question(section_id=advanced_section.id, **question_data)
                await question.save()
                advanced_section.questions.append(question.id)

            await advanced_section.save(is_update=True)

            # Now add visibility conditions after questions are created
            # Get the questions we just created to set up visibility conditions
            technical_cofounder_question = await Question.find_one({
                "section_id": advanced_section.id,
                "order": 0,
            })
            product_type_question = await Question.find_one({
                "section_id": advanced_section.id,
                "order": 1,
            })
            paying_customers_question = await Question.find_one({
                "section_id": advanced_section.id,
                "order": 6,
            })

            if technical_cofounder_question:
                # Update the programming languages question to be conditional
                programming_question = await Question.find_one({
                    "section_id": advanced_section.id,
                    "order": 2,
                })
                if programming_question:
                    programming_question.visibility_condition = VisibilityCondition(
                        operator="==",
                        conditions=[
                            {
                                "question_id": str(technical_cofounder_question.id),
                                "value": True,
                            }
                        ],
                    )
                    await programming_question.save(is_update=True)

            if product_type_question:
                # Update mobile app question
                mobile_question = await Question.find_one({
                    "section_id": advanced_section.id,
                    "order": 3,
                })
                if mobile_question:
                    mobile_question.visibility_condition = VisibilityCondition(
                        operator="==",
                        conditions=[
                            {
                                "question_id": str(product_type_question.id),
                                "value": "mobile_app",
                            }
                        ],
                    )
                    await mobile_question.save(is_update=True)

                # Update web app question
                web_question = await Question.find_one({
                    "section_id": advanced_section.id,
                    "order": 4,
                })
                if web_question:
                    web_question.visibility_condition = VisibilityCondition(
                        operator="==",
                        conditions=[
                            {
                                "question_id": str(product_type_question.id),
                                "value": "web_app",
                            }
                        ],
                    )
                    await web_question.save(is_update=True)

                # Update hardware question
                hardware_question = await Question.find_one({
                    "section_id": advanced_section.id,
                    "order": 5,
                })
                if hardware_question:
                    hardware_question.visibility_condition = VisibilityCondition(
                        operator="==",
                        conditions=[
                            {
                                "question_id": str(product_type_question.id),
                                "value": "hardware",
                            }
                        ],
                    )
                    await hardware_question.save(is_update=True)

            if paying_customers_question:
                # Update customer count question
                customer_count_question = await Question.find_one({
                    "section_id": advanced_section.id,
                    "order": 7,
                })
                if customer_count_question:
                    customer_count_question.visibility_condition = VisibilityCondition(
                        operator="==",
                        conditions=[
                            {
                                "question_id": str(paying_customers_question.id),
                                "value": True,
                            }
                        ],
                    )
                    await customer_count_question.save(is_update=True)

            # Section 3: Additional Details (demonstrates file uploads, dates, validation)
            details_section = Section(
                form_id=form.id,
                title="Additional Details",
                description="File uploads, dates, and advanced validation examples",
                order=2,
                repeatable=False,
            )
            await details_section.save()

            form.sections.append(details_section.id)
            await form.save(is_update=True)

            # Create questions for details section
            details_questions = [
                {
                    "type": "date",
                    "label": "When did you start working on this idea?",
                    "help_text": "Select the date when you first started working on this project",
                    "required": False,
                    "order": 0,
                },
                {
                    "type": "file",
                    "label": "Upload your pitch deck",
                    "help_text": "Upload a PDF of your pitch deck (max 10MB)",
                    "required": False,
                    "validation": {"custom": "File must be PDF and under 10MB"},
                    "order": 1,
                },
                {
                    "type": "file",
                    "label": "Company logo",
                    "help_text": "Upload your company logo (PNG, JPG, SVG)",
                    "required": False,
                    "validation": {
                        "custom": "File must be image format (PNG, JPG, SVG) and under 5MB"
                    },
                    "order": 2,
                },
                {
                    "type": "long_text",
                    "label": "Tell us about your biggest challenge",
                    "help_text": "Describe the biggest challenge you're currently facing",
                    "required": False,
                    "order": 3,
                },
                {
                    "type": "single_select",
                    "label": "How did you hear about us?",
                    "help_text": "This helps us understand our marketing channels",
                    "required": False,
                    "options": [
                        {"value": "google", "label": "Google Search"},
                        {"value": "social_media", "label": "Social Media"},
                        {"value": "referral", "label": "Referral"},
                        {"value": "event", "label": "Event/Conference"},
                        {"value": "advertisement", "label": "Advertisement"},
                        {"value": "other", "label": "Other"},
                    ],
                    "order": 4,
                },
                {
                    "type": "boolean",
                    "label": "Would you like to receive updates about our platform?",
                    "help_text": "We'll send you updates about new features and opportunities",
                    "required": False,
                    "order": 5,
                },
            ]

            # Create details section questions
            for question_data in details_questions:
                question = Question(section_id=details_section.id, **question_data)
                await question.save()
                details_section.questions.append(question.id)

            await details_section.save(is_update=True)
            # Validate core fields are present
            await form.save(is_update=True)

            logger.info(
                f"Created comprehensive demo form {form.id} for organization {org_id}"
            )
            return form.model_dump(by_alias=True)

        except Exception as e:
            logger.error(f"Error creating default form: {str(e)}")
            await self.handle_error(e, {"org_id": str(org_id)})
            return None

    async def _create_default_basic_section(self, form_id: ObjectId) -> None:
        """Create the default Basic section with core field questions."""
        try:
            # Create the Basic section
            basic_section = Section(
                form_id=PyObjectId(form_id),
                title="Basic",
                description="Essential information for deal creation",
                order=0,
                repeatable=False,
            )
            await basic_section.save()

            # Add section to form
            form = await Form.find_one({"_id": form_id})
            if form:
                form.sections.append(basic_section.id)
                await form.save(is_update=True)

            # Create core field questions
            core_questions = [
                {
                    "type": "short_text",
                    "label": "Company Name",
                    "help_text": "Enter the name of your company",
                    "required": True,
                    "core_field": "company_name",
                    "order": 0,
                },
                {
                    "type": "short_text",
                    "label": "Company Website",
                    "help_text": "Enter the website of your company",
                    "required": True,
                    "core_field": "company_website",
                    "order": 1,
                },
                {
                    "type": "single_select",
                    "label": "Stage",
                    "help_text": "Select your current funding stage",
                    "required": True,
                    "core_field": "stage",
                    "options": [
                        {"value": "pre_seed", "label": "Pre-Seed"},
                        {"value": "seed", "label": "Seed"},
                        {"value": "series_a", "label": "Series A"},
                        {"value": "series_b", "label": "Series B"},
                        {"value": "series_c", "label": "Series C"},
                        {"value": "series_d_plus", "label": "Series D+"},
                        {"value": "growth", "label": "Growth"},
                        {"value": "ipo", "label": "IPO"},
                        {"value": "other", "label": "Other"},
                    ],
                    "order": 2,
                },
                {
                    "type": "multi_select",
                    "label": "Sector",
                    "help_text": "Select the sectors that best describe your business",
                    "required": True,
                    "core_field": "sector",
                    "options": [
                        {"value": "fintech", "label": "Fintech"},
                        {"value": "saas", "label": "SaaS"},
                        {"value": "ecommerce", "label": "E-commerce"},
                        {"value": "healthtech", "label": "HealthTech"},
                        {"value": "edtech", "label": "EdTech"},
                        {"value": "proptech", "label": "PropTech"},
                        {"value": "cleantech", "label": "CleanTech"},
                        {"value": "biotech", "label": "BioTech"},
                        {"value": "ai_ml", "label": "AI/ML"},
                        {"value": "blockchain", "label": "Blockchain"},
                        {"value": "cybersecurity", "label": "Cybersecurity"},
                        {"value": "mobility", "label": "Mobility"},
                        {"value": "gaming", "label": "Gaming"},
                        {"value": "media", "label": "Media"},
                        {"value": "retail", "label": "Retail"},
                        {"value": "manufacturing", "label": "Manufacturing"},
                        {"value": "other", "label": "Other"},
                    ],
                    "order": 3,
                },
                {
                    "type": "file",
                    "label": "Pitch Deck",
                    "help_text": "Upload your latest pitch deck (PDF, PPT, etc.)",
                    "required": False,
                    "core_field": "pitch_deck",
                    "order": 4,
                },
            ]

            # Create each core question
            for question_data in core_questions:
                question = Question(section_id=basic_section.id, **question_data)
                await question.save()

                # Add question to section
                basic_section.questions.append(question.id)

            # Save section with questions
            await basic_section.save(is_update=True)

            founders_section = Section(
                form_id=form_id if isinstance(form_id, ObjectId) else ObjectId(form_id),  # type: ignore
                title="Founders",
                description="Details about each founder (this section is repeatable).",
                order=1,  # Next position after the Basic section
                repeatable=True,  # Mark this section as repeatable
            )
            await founders_section.save()

            # 2. Append the new section to the form's sections array
            form = await Form.find_one({"_id": form_id})
            if form:
                form.sections.append(founders_section.id)
                await form.save(is_update=True)

            # 3. Define the questions for the "Founders" section

            founder_questions = [
                {
                    "type": "short_text",
                    "label": "Founder Name",
                    "help_text": "Enter the full name of the founder.",
                    "required": True,
                    "core_field": "founder_name",
                    "order": 0,
                },
                {
                    "type": "multi_select",
                    "label": "Role(s) / Title(s)",
                    "help_text": "Select all roles/titles that apply to this founder.",
                    "required": True,
                    "core_field": "founder_role",
                    "options": [
                        {"value": "ceo", "label": "CEO"},
                        {"value": "cto", "label": "CTO"},
                        {"value": "coo", "label": "COO"},
                        {"value": "cfo", "label": "CFO"},
                        {"value": "cmo", "label": "CMO"},
                        {"value": "cio", "label": "CIO"},
                        {"value": "engineer", "label": "Engineer"},
                        {"value": "designer", "label": "Designer"},
                        {"value": "product_pm", "label": "Product Manager"},
                        {"value": "marketing", "label": "Marketing"},
                        {"value": "sales", "label": "Sales"},
                        {"value": "founder", "label": "Founder"},
                        {"value": "co_founder", "label": "Co-Founder"},
                        {"value": "other", "label": "Other"},
                    ],
                    "order": 1,
                },
                {
                    "type": "short_text",
                    "label": "LinkedIn Profile URL",
                    "help_text": "Paste the LinkedIn URL for this founder (optional).",
                    "required": False,
                    "core_field": "founder_linkedin",
                    "order": 2,
                },
            ]

            # 4. Create each question in the "Founders" section
            for question_data in founder_questions:
                question = Question(section_id=founders_section.id, **question_data)
                await question.save()
                founders_section.questions.append(question.id)

            # 5. Save the "Founders" section (which now contains its question IDs)
            await founders_section.save(is_update=True)

        except Exception as e:
            logger.error(f"Error creating default Basic section: {str(e)}")
            # Don't raise here to avoid breaking form creation
            # The form will be created without the default section

    async def _validate_core_fields_present(self, form_id: str) -> None:
        """Validate that all 3 core field questions are present in the form."""
        try:
            # Get form with details
            form_details = await self.get_form_with_details(form_id)
            if not form_details:
                return

            # Get all questions from all sections
            all_questions = []
            for section in form_details.sections:
                all_questions.extend(section.questions)

            # Check for required core fields
            required_core_fields = {"company_name", "stage", "sector"}
            found_core_fields = set()

            for question in all_questions:
                if hasattr(question, "core_field") and question.core_field:
                    found_core_fields.add(question.core_field)

            missing_core_fields = required_core_fields - found_core_fields
            if missing_core_fields:
                missing_fields_str = ", ".join(missing_core_fields)
                raise ValueError(
                    f"Form is missing required core fields: {missing_fields_str}"
                )

        except Exception as e:
            logger.error(f"Error validating core fields: {str(e)}")
            raise

    async def create_form_version(
        self, form_id: str, actor_id: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Create a new version of an existing form.

        This method creates a snapshot of the current form and then creates a new form
        with an incremented version number.

        Args:
            form_id: ID of the form to version
            actor_id: Optional ID of the user performing the operation

        Returns:
            The new form as a dictionary, or None if the form doesn't exist
        """
        try:
            # Get the form first
            form = await Form.find_one({"_id": ObjectId(form_id)})
            if not form:
                return None

            # Convert actor_id to ObjectId if provided
            actor_obj_id = ObjectId(actor_id) if actor_id else None

            # Save a snapshot of the current form
            snapshot = await self.save_snapshot(form, actor_obj_id)

            # Create new form with incremented version
            new_form = Form(
                name=form.name,
                description=form.description,
                version=form.version + 1,
                is_active=True,
                org_id=form.org_id,
                sections=form.sections,
                default_section_ids=form.default_section_ids,
                created_by=actor_obj_id or form.created_by,  # type: ignore
                created_at=int(datetime.now(timezone.utc).timestamp()),
                updated_at=int(datetime.now(timezone.utc).timestamp()),
            )
            await new_form.save()

            # Update the snapshot with the new resource ID
            snapshot.new_resource_id = new_form.id
            await snapshot.save(is_update=True)

            # Invalidate cache
            # await self.cache.delete(f"form:{form_id}")
            # await self.cache.delete(f"form_details:{form_id}")

            # Convert to dict before returning
            return new_form.model_dump()
        except Exception as e:
            await self.handle_error(e, {"form_id": form_id, "actor_id": actor_id})

    async def get_form_version_history(
        self, form_id: str, limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Get version history for a form.

        Args:
            form_id: ID of the form
            limit: Maximum number of versions to return (default: 10)

        Returns:
            List of version snapshots, ordered by version (descending), with links to new versions
        """
        try:
            # Use the versioning mixin to get the version history
            versions = await self.get_version_history("Form", form_id, limit)

            # Convert to dict and enhance with additional information
            result = []
            for version in versions:
                version_dict = version.model_dump()

                # If this version has a new_resource_id, add the form details
                if version.new_resource_id:
                    try:
                        new_form = await Form.find_one({"_id": version.new_resource_id})
                        if new_form:
                            version_dict["new_version"] = {
                                "id": str(new_form.id),
                                "version": new_form.version,
                                "name": new_form.name,
                                "created_at": new_form.created_at,
                            }
                    except Exception as e:
                        logger.warning(f"Error fetching new form version: {e}")

                result.append(version_dict)

            return result
        except Exception as e:
            await self.handle_error(e, {"form_id": form_id, "limit": limit})
            return []

    async def rollback_form_to_version(
        self, form_id: str, target_version: int, actor_id: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Roll back a form to a specific version.

        Args:
            form_id: ID of the form to roll back
            target_version: Version to roll back to
            actor_id: Optional ID of the user performing the rollback

        Returns:
            The rolled-back form as a dictionary, or None if the version doesn't exist
        """
        try:
            # Convert actor_id to ObjectId if provided
            actor_obj_id = ObjectId(actor_id) if actor_id else None

            # Use the versioning mixin to roll back the form
            rolled_back_form = await self.rollback_to_version(
                "Form", form_id, target_version, actor_obj_id, Form
            )

            if not rolled_back_form:
                return None

            # Invalidate cache
            # await self.cache.delete(f"form:{form_id}")
            # await self.cache.delete(f"form_details:{form_id}")

            # Convert to dict before returning
            return rolled_back_form.model_dump()
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "form_id": form_id,
                    "target_version": target_version,
                    "actor_id": actor_id,
                },
            )

    async def update_form(
        self, form_id: str, update: Dict[str, Any], actor_id: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        try:
            # Get the form first
            form = await Form.find_one({"_id": ObjectId(form_id)})
            if not form:
                return None

            # Convert actor_id to ObjectId if provided
            actor_obj_id = ObjectId(actor_id) if actor_id else None

            # Handle ObjectId fields properly before updating
            if "org_id" in update and isinstance(update["org_id"], str):
                try:
                    update["org_id"] = ObjectId(update["org_id"])
                except Exception:
                    pass

            # Handle sections list if present
            if "sections" in update and isinstance(update["sections"], list):
                try:
                    update["sections"] = [
                        ObjectId(s) if isinstance(s, str) else s
                        for s in update["sections"]
                    ]
                except Exception:
                    pass

            # Handle default_section_ids list if present
            if "default_section_ids" in update and isinstance(
                update["default_section_ids"], list
            ):
                try:
                    update["default_section_ids"] = [
                        ObjectId(s) if isinstance(s, str) else s
                        for s in update["default_section_ids"]
                    ]
                except Exception:
                    pass

            # Handle created_by if present
            # if 'created_by' in update and isinstance(update['created_by'], str):
            #     try:
            #         update['created_by'] = ObjectId(update['created_by'])
            #     except Exception:
            #         pass

            # Use the type casting utility to update the form
            form = update_model_with_type_casting(form, update)

            # Use the versioning mixin to handle the update
            updated_form = await self.bump_version(form, update, actor_obj_id)

            return updated_form.model_dump()
        except Exception as e:
            await self.handle_error(
                e, {"form_id": form_id, "update": update, "actor_id": actor_id}
            )

    async def archive_form(self, form_id: str, actor_id: Optional[str] = None) -> bool:
        """
        Archive a form by setting its is_active flag to False.

        This is a soft delete operation.

        Args:
            form_id: ID of the form to archive
            actor_id: Optional ID of the user performing the archive

        Returns:
            True if the form was successfully archived, False otherwise
        """
        try:
            form = await Form.find_one({"_id": ObjectId(form_id)})
            if not form:
                return False

            # Use the update_form method to ensure versioning and proper handling
            updated_form = await self.update_form(
                form_id, {"is_active": False}, actor_id=actor_id
            )
            return updated_form is not None
        except Exception as e:
            await self.handle_error(
                e, {"form_id": form_id, "actor_id": actor_id, "action": "archive"}
            )
            return False

    async def delete_form(self, form_id: str) -> bool:
        """
        Delete a form and all its related sections and questions.

        Args:
            form_id: ID of the form to delete

        Returns:
            True if the form was successfully deleted, False otherwise
        """
        try:
            form = await Form.find_one({"_id": ObjectId(form_id)})
            if not form:
                return False

            # Delete all sections and questions
            sections = await Section.find_many({"_id": {"$in": form.sections}})
            for section in sections:
                questions = await Question.find_many({
                    "_id": {"$in": section.questions}
                })
                for question in questions:
                    await question.hard_delete()
                await section.hard_delete()

            # Delete the form itself
            await form.hard_delete()

            return True
        except Exception as e:
            await self.handle_error(e, {"form_id": form_id})
            return False

    async def create_section(
        self, form_id: str, create_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Create a new section."""
        try:
            # validate if the order is unique
            if "order" in create_data:
                existing_section = await Section.find_one({
                    "form_id": ObjectId(form_id),
                    "order": int(create_data["order"]),
                })
                if existing_section:
                    raise ValueError("Section order must be unique within a form")

            # Create the section
            section = Section(form_id=PyObjectId(form_id), **create_data)
            await section.save()

            # Get the form and add the section to it
            form = await Form.find_one({"_id": ObjectId(form_id)})
            if form:
                form.sections.append(section.id)
                await form.save(is_update=True)

            # Invalidate cache
            # await self.cache.delete(f"form_details:{form_id}")

            # Convert to dict before returning
            return section.model_dump()
        except Exception as e:
            await self.handle_error(e, {"form_id": form_id, **create_data})

    async def update_section(
        self, section_id: str, update: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        try:
            # Get section
            section = await Section.find_one({"_id": ObjectId(section_id)})
            if not section:
                return None

            if "order" in update:
                existing_section = await Section.find_one({
                    "form_id": ObjectId(section.form_id),
                    "order": int(update["order"]),
                })
                if existing_section and existing_section.id != section.id:
                    raise ValueError("Section order must be unique within a form")

            # Handle ObjectId fields properly before updating
            if "form_id" in update and isinstance(update["form_id"], str):
                try:
                    update["form_id"] = ObjectId(update["form_id"])
                except Exception:
                    pass

            # Handle questions list if present
            if "questions" in update and isinstance(update["questions"], list):
                try:
                    update["questions"] = [
                        ObjectId(q) if isinstance(q, str) else q
                        for q in update["questions"]
                    ]
                except Exception:
                    pass

            # Use the type casting utility to update the section
            section = update_model_with_type_casting(section, update)

            # Save the updated section with is_update=True to prevent duplicate key errors
            await section.save(is_update=True)

            return section.model_dump()
        except Exception as e:
            await self.handle_error(e, {"section_id": section_id, "update": update})

    async def delete_section(self, section_id: str) -> Optional[bool]:
        """Delete a section."""
        try:
            # Get section to find form_id
            section = await Section.find_one({"_id": ObjectId(section_id)})
            if not section:
                return False

            # Get the form to remove the section from it
            form = await Form.find_one({"_id": section.form_id})
            if form:
                # Remove section from form
                if section.id in form.sections:
                    form.sections.remove(section.id)
                    await form.save(is_update=True)

            # Delete section
            await section.hard_delete()

            # Invalidate cache
            # await self.cache.delete(f"form_details:{section.form_id}")

            return True
        except Exception as e:
            await self.handle_error(e, {"section_id": section_id})

    async def create_question(
        self, section_id: str, create_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Create a new question.

        Enhanced to support repeatable section embedding through the repeat_section_id
        and max_repeats parameters.

        Args:
            section_id: ID of the section to add the question to
            create_data: Dictionary containing question data

        Returns:
            The created question as a dictionary
        """
        try:
            # Ensure section_id is not in create_data to avoid conflicts
            if "section_id" in create_data:
                logger.warning(
                    "section_id in create_data will be overridden with the provided section_id parameter"
                )

            # Set section_id in create_data
            create_data["section_id"] = ObjectId(section_id)

            # Extract fields for validation
            label = create_data.get("label", "")
            visibility_condition = create_data.get("visibility_condition")
            repeat_section_id = create_data.get("repeat_section_id")
            max_repeats = create_data.get("max_repeats")
            order = create_data.get("order")

            # check if order is unique within the section
            existing_question = await Question.find_one({
                "section_id": ObjectId(section_id),
                "order": int(order),
            })
            if existing_question:
                raise ValueError("Question order must be unique within a section")

            # Get parent section and form for validation
            parent_section = await Section.find_one({"_id": ObjectId(section_id)})
            if parent_section:
                form = await Form.find_one({"_id": parent_section.form_id})
                if form:
                    # Get all sections in the form
                    sections = await Section.find_many({"_id": {"$in": form.sections}})

                    # Create a section map for validation
                    section_map = {str(s.id): s.model_dump() for s in sections}

                    # Get all questions in the form
                    all_questions = []
                    for s in sections:
                        s_questions = await Question.find_many({"section_id": s.id})
                        all_questions.extend([q.model_dump() for q in s_questions])

                    # Create a temporary question for validation
                    temp_question = {
                        "label": label,
                        "visibility_condition": visibility_condition,
                    }

                    # Validate repeatable section reference if provided
                    if repeat_section_id:
                        try:
                            # Check if section exists
                            repeat_section = await Section.find_one({
                                "_id": ObjectId(repeat_section_id)
                            })
                            if not repeat_section:
                                raise ValueError(
                                    f"Referenced section ID {repeat_section_id} does not exist"
                                )

                            # Prevent circular references (section can't reference itself)
                            if str(repeat_section_id) == str(section_id):
                                raise ValueError(
                                    "A question cannot reference its own section as a repeatable section"
                                )

                            # Validate max_repeats
                            if max_repeats is None:
                                create_data["max_repeats"] = 10  # Default maximum
                            elif max_repeats <= 0:
                                raise ValueError(
                                    "max_repeats must be a positive integer"
                                )
                            elif max_repeats > 100:
                                create_data["max_repeats"] = (
                                    100  # Cap at reasonable maximum
                                )

                            # Add repeat_section_id to temp question for validation
                            temp_question["repeat_section_id"] = ObjectId(
                                repeat_section_id
                            )

                            # Convert repeat_section_id to ObjectId in create_data
                            try:
                                create_data["repeat_section_id"] = ObjectId(
                                    repeat_section_id
                                )
                                logger.debug(
                                    f"Successfully converted repeat_section_id {repeat_section_id} to ObjectId"
                                )
                            except Exception as e:
                                logger.error(
                                    f"Error converting repeat_section_id to ObjectId: {e}"
                                )
                                raise ValueError(
                                    f"Invalid repeat_section_id format: {repeat_section_id}"
                                )
                        except Exception as e:
                            logger.error(f"Error validating repeat_section_id: {e}")
                            raise

                        # Validate repeatable section nesting
                        nesting_errors = validate_repeatable_section_nesting(
                            [temp_question], section_map
                        )
                        if nesting_errors:
                            raise ValueError(nesting_errors[0])

                    # If visibility condition is provided, validate references
                    if visibility_condition:
                        # Directly validate that referenced questions exist in the database
                        if isinstance(visibility_condition, dict):
                            conditions = visibility_condition.get("conditions", [])
                        else:
                            # Handle Pydantic model
                            conditions = getattr(visibility_condition, "conditions", [])

                        for condition in conditions:
                            if isinstance(condition, dict):
                                question_id_to_check = condition.get("question_id")
                            else:
                                # Handle Pydantic model
                                question_id_to_check = getattr(
                                    condition, "question_id", None
                                )

                            if question_id_to_check:
                                try:
                                    # Check if the question exists in the database
                                    referenced_question = await Question.find_one({
                                        "_id": ObjectId(question_id_to_check)
                                    })
                                    if not referenced_question:
                                        raise ValueError(
                                            f"Question '{label}' references non-existent question ID '{question_id_to_check}'"
                                        )
                                except Exception as e:
                                    raise ValueError(
                                        f"Invalid question ID format or non-existent question: {question_id_to_check}. Error: {str(e)}"
                                    )

                        # Add the temporary question to the list for validation of circular dependencies
                        temp_questions = all_questions.copy()
                        temp_questions.append(temp_question)

                        # Check for circular dependencies
                        graph = build_visibility_graph(temp_questions)
                        logger.debug(f"Visibility graph: {graph}")
                        cycles = detect_cycles(graph)
                        logger.debug(f"Detected cycles: {cycles}")
                        if cycles:
                            cycle_str = " -> ".join([str(node) for node in cycles[0]])
                            raise ValueError(
                                f"Circular dependency detected in visibility conditions: {cycle_str}"
                            )

            # Create the question
            try:
                # Create the question with the validated data
                question = Question(**create_data)

                # Verify the repeat_section_id was set correctly if provided
                if repeat_section_id and not question.repeat_section_id:
                    logger.warning(
                        f"Warning: repeat_section_id was not set correctly. Input: {repeat_section_id}, Result: {question.repeat_section_id}"
                    )

                await question.save()

                if (
                    "repeat_section_id" in create_data
                    and create_data["repeat_section_id"]
                ):
                    repeat_section = await Section.find_one({
                        "_id": ObjectId(create_data["repeat_section_id"])
                    })
                    if repeat_section:
                        repeat_section.repeatable = True
                        await repeat_section.save(is_update=True)

                # Verify the saved question has the correct repeat_section_id if provided
                if repeat_section_id:
                    saved_question = await Question.find_one({"_id": question.id})
                    if not saved_question.repeat_section_id:
                        logger.warning(
                            f"Warning: repeat_section_id was not saved correctly. Expected: {repeat_section_id}, Saved: {saved_question.repeat_section_id}"
                        )
            except Exception as e:
                logger.error(f"Error creating question: {e}")
                raise

            # Add question to section
            section = await Section.find_one({"_id": ObjectId(section_id)})
            if section:
                section.questions.append(question.id)
                # Pass is_update=True to indicate this is an update operation, not an insert
                await section.save(is_update=True)

                # Invalidate cache
                # await self.cache.delete(f"form_details:{section.form_id}")

            # Convert to dict before returning
            form_with_details = await self.get_form_with_details(section.form_id)  # type: ignore
            return form_with_details.model_dump(by_alias=True)  # type: ignore
        except Exception as e:
            await self.handle_error(
                e, {"section_id": section_id, "create_data": create_data}
            )

    async def update_question(
        self, question_id: str, update: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        try:
            # Get question to find section_id
            question = await Question.find_one({"_id": ObjectId(question_id)})
            if not question:
                return None

            # Prevent updating core_field attribute for existing core questions
            if question.core_field and "core_field" in update:
                if update["core_field"] != question.core_field:
                    raise ValueError(
                        f"Cannot modify core_field attribute for core field question: {question.core_field}"
                    )

            # Validate core field type changes
            if question.core_field and "type" in update:
                new_type = update["type"]
                if question.core_field == "company_name" and new_type != "short_text":
                    raise ValueError(
                        "Company name core field must be of type short_text"
                    )
                elif question.core_field == "stage" and new_type != "single_select":
                    raise ValueError("Stage core field must be of type single_select")
                elif question.core_field == "sector" and new_type not in [
                    "single_select",
                    "multi_select",
                ]:
                    raise ValueError(
                        "Sector core field must be of type single_select or multi_select"
                    )

            # Validate that core field questions with options don't have all options removed
            if (
                question.core_field
                and question.core_field in ["stage", "sector"]
                and "options" in update
            ):
                if not update["options"] or len(update["options"]) == 0:
                    raise ValueError(
                        f"At least one option is required for {question.core_field} core field"
                    )

            # if order is present, need to check if its unique within the section
            if "order" in update:
                existing_question = await Question.find_one({
                    "section_id": ObjectId(question.section_id),
                    "order": int(update["order"]),
                })
                if existing_question and existing_question.id != question.id:
                    raise ValueError("Question order must be unique within a section")

            # Initialize repeat_section_id and repeat_section variables
            repeat_section_id = None
            repeat_section = None

            # Validate repeatable section reference if provided
            if "repeat_section_id" in update:
                repeat_section_id = update["repeat_section_id"]
                try:
                    if repeat_section_id:
                        # Check if section exists
                        try:
                            repeat_section = await Section.find_one({
                                "_id": ObjectId(repeat_section_id)
                            })
                            if not repeat_section:
                                raise ValueError(
                                    f"Referenced section ID {repeat_section_id} does not exist"
                                )
                        except Exception as e:
                            print(
                                f"Error finding section with ID {repeat_section_id}: {e}"
                            )
                            raise ValueError(
                                f"Invalid or non-existent section ID: {repeat_section_id}"
                            )

                        # Prevent circular references (section can't reference itself)
                        if str(repeat_section_id) == str(question.section_id):
                            raise ValueError(
                                "A question cannot reference its own section as a repeatable section"
                            )

                        # Validate max_repeats
                        max_repeats = update.get("max_repeats")
                        if max_repeats is not None:
                            if max_repeats <= 0:
                                raise ValueError(
                                    "max_repeats must be a positive integer"
                                )
                            elif max_repeats > 100:
                                update["max_repeats"] = 100  # Cap at reasonable maximum
                        elif "max_repeats" not in update:
                            update["max_repeats"] = 10  # Default maximum

                except Exception as e:
                    logger.error(f"Error processing repeat_section_id: {e}")
                    raise

            # Get parent section and form for validation
            section = await Section.find_one({"_id": question.section_id})
            if section:
                form = await Form.find_one({"_id": section.form_id})
                if form:
                    # Get all sections in the form
                    sections = await Section.find_many({"_id": {"$in": form.sections}})

                    # Create a section map for validation
                    section_map = {str(s.id): s.model_dump() for s in sections}

                    # Get all questions in the form
                    all_questions = []
                    for s in sections:
                        s_questions = await Question.find_many({"section_id": s.id})
                        all_questions.extend([q.model_dump() for q in s_questions])

                    # Handle ObjectId fields in the update dictionary
                    if "section_id" in update and isinstance(update["section_id"], str):
                        try:
                            update["section_id"] = ObjectId(update["section_id"])
                        except Exception:
                            pass

                    if (
                        "repeat_section_id" in update
                        and isinstance(update["repeat_section_id"], str)
                        and update["repeat_section_id"]
                    ):
                        try:
                            update["repeat_section_id"] = ObjectId(
                                update["repeat_section_id"]
                            )
                        except Exception:
                            pass

                    # Create a temporary question with the updates
                    temp_question = question.model_dump()
                    for key, value in update.items():
                        temp_question[key] = value

                    # Replace the question in the list for validation
                    all_questions = [
                        q if str(q.get("_id", "")) != question_id else temp_question
                        for q in all_questions
                    ]

                    # Validate visibility condition if provided
                    if "visibility_condition" in update:
                        # Check for circular dependencies
                        graph = build_visibility_graph(all_questions)
                        cycles = detect_cycles(graph)
                        if cycles:
                            cycle_str = " -> ".join([str(node) for node in cycles[0]])
                            raise ValueError(
                                f"Circular dependency detected in visibility conditions: {cycle_str}"
                            )

                        # Check if referenced questions exist in the database
                        if "visibility_condition" in update and isinstance(
                            update["visibility_condition"], dict
                        ):
                            conditions = update["visibility_condition"].get(
                                "conditions", []
                            )
                            for condition in conditions:
                                if isinstance(condition, dict):
                                    question_id_to_check = condition.get("question_id")
                                    if question_id_to_check:
                                        # Check if the question exists in the database
                                        referenced_question = await Question.find_one({
                                            "_id": ObjectId(question_id_to_check)
                                        })
                                        if not referenced_question:
                                            raise ValueError(
                                                f"Question references non-existent question ID '{question_id_to_check}'"
                                            )

                        # Validate other references
                        ref_errors = validate_visibility_references(
                            all_questions, section_map
                        )
                        if ref_errors:
                            raise ValueError(ref_errors[0])

                    # Validate repeatable section nesting if provided
                    if "repeat_section_id" in update and update["repeat_section_id"]:
                        nesting_errors = validate_repeatable_section_nesting(
                            [temp_question], section_map
                        )
                        if nesting_errors:
                            raise ValueError(nesting_errors[0])
            # Log the question before update
            logger.info(
                f"🔍 Question Model before update: id={question.id} section_id={question.section_id} type={question.type} label={question.label}"
            )
            logger.info(f"📥 Update data received: {update}")

            # Use the type casting utility to update the question
            question = update_model_with_type_casting(question, update)

            # Log the question after update with explicit field access to avoid string conversion
            logger.info(
                f"✅ Question Model after update: id={question.id} section_id={question.section_id} type={question.type} label={question.label}"
            )

            # Verify repeat_section_id was set correctly if it was in the update
            if (
                "repeat_section_id" in update
                and update["repeat_section_id"]
                and not question.repeat_section_id
            ):
                print(
                    f"Warning: repeat_section_id was not set correctly. Input: {update['repeat_section_id']}, Result: {question.repeat_section_id}"
                )

            # Save the updated question with is_update=True to prevent duplicate key errors
            await question.save(is_update=True)

            # Update the repeat section if needed
            if repeat_section_id and repeat_section:
                repeat_section.repeatable = True
                await repeat_section.save(is_update=True)

            # Get the final result to return
            result = question.model_dump(by_alias=True)
            logger.info(
                f"📤 Final result being returned: type={result.get('type')} id={result.get('_id')}"
            )

            return result
        except Exception as e:
            await self.handle_error(e, {"question_id": question_id, "update": update})

    async def delete_question(self, question_id: str) -> Optional[bool]:
        """Delete a question."""
        try:
            # Get question to find section_id
            question = await Question.find_one({"_id": ObjectId(question_id)})
            if not question:
                return False

            # Prevent deletion of core field questions
            if question.core_field:
                raise ValueError(
                    f"Cannot delete core field question: {question.core_field}"
                )

            # Get the section to remove the question from it
            section = await Section.find_one({"_id": question.section_id})
            if section:
                # Remove question from section
                if question.id in section.questions:
                    section.questions.remove(question.id)
                    await section.save(is_update=True)

                # Invalidate cache
                # await self.cache.delete(f"form_details:{section.form_id}")

            # Delete question
            await question.hard_delete()

            return True
        except Exception as e:
            await self.handle_error(e, {"question_id": question_id})

    async def submit_form(
        self,
        form_id: str,
        answers: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Submit a form with answers.

        Args:
            form_id: ID of the form to submit
            answers: Dictionary of answers (question_id -> answer)
            metadata: Optional metadata to include with the submission

        Returns:
            The created submission
        """
        try:
            # Get the form to get the org_id
            form = await Form.find_one({"_id": ObjectId(form_id)})
            if not form:
                raise ValueError(f"Form with ID {form_id} not found")

            # Create submission object
            submission_data = {
                "form_id": ObjectId(form_id),
                "org_id": form.org_id,
                "answers": answers,
            }

            # Add metadata if provided
            if metadata:
                submission_data["metadata"] = metadata

            submission = Submission(**submission_data)
            await submission.save()

            # Convert to dict before returning
            return submission.model_dump(by_alias=True)
        except Exception as e:
            await self.handle_error(
                e, {"form_id": form_id, "answers": answers, "metadata": metadata}
            )

    async def get_submission(self, submission_id: str) -> Optional[Dict[str, Any]]:
        """Get a submission by ID."""
        try:
            submission = await Submission.find_one({"_id": ObjectId(submission_id)})
            # Convert to dict before returning
            return submission.model_dump() if submission else None
        except Exception as e:
            await self.handle_error(e, {"submission_id": submission_id})

    async def update_submission(
        self,
        submission_id: str,
        answers: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        status: Optional[str] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        Update an existing submission.

        Args:
            submission_id: ID of the submission to update
            answers: Optional new answers to update
            metadata: Optional metadata to update
            status: Optional status to update

        Returns:
            The updated submission, or None if not found
        """
        try:
            # Get the existing submission
            submission = await Submission.find_one({"_id": ObjectId(submission_id)})
            if not submission:
                return None

            # Prepare update data
            update_data = {}

            if answers is not None:
                update_data["answers"] = answers

            if metadata is not None:
                # Merge with existing metadata if it exists
                existing_metadata = submission.metadata or {}
                existing_metadata.update(metadata)
                update_data["metadata"] = existing_metadata

            if status is not None:
                update_data["status"] = status

            # Always update the updated_at timestamp
            update_data["updated_at"] = int(datetime.now(timezone.utc).timestamp())

            # If there's nothing to update, return the existing submission
            if not update_data:
                return submission.model_dump()

            # Use the type casting utility to update the submission
            submission = update_model_with_type_casting(submission, update_data)

            # Save the updated submission
            await submission.save(is_update=True)

            # Convert to dict before returning
            return submission.model_dump()
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "submission_id": submission_id,
                    "answers": answers,
                    "metadata": metadata,
                    "status": status,
                },
            )
            return None

    async def get_form_submissions(
        self, form_id: str, skip: int = 0, limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get all submissions for a form."""
        try:
            submissions = await Submission.find_many(
                {"form_id": ObjectId(form_id)}, skip=skip, limit=limit
            )
            return submissions
        except Exception as e:
            await self.handle_error(
                e, {"form_id": form_id, "skip": skip, "limit": limit}
            )

    async def validate_submission(
        self, form_id: str, answers: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate a submission against form rules.

        This method performs comprehensive validation of form submissions including:
        1. Required field validation
        2. Type-specific validation rules (min/max/regex)
        3. Conditional visibility validation
        4. Repeatable section validation

        Args:
            form_id: ID of the form to validate
            answers: Dictionary of answers to validate or an AnswerSchema instance

        Returns:
            Dictionary with validation results:
            - valid: Boolean indicating if validation passed
            - errors: List of error messages (if any)
        """
        try:
            # Get form details with all sections and questions
            form_details = await self.get_form_with_details(form_id)
            if not form_details:
                return {"valid": False, "errors": ["Form not found"]}

            # Build a map of section IDs to section objects for quick lookup
            section_map = {}
            for section in form_details.sections:
                section_map[str(section.id)] = section

            # Track questions that embed repeatable sections
            repeatable_embeddings = {}
            errors = []

            # Check if we have a structured AnswerSchema with repeatable_answers
            has_structured_answers = False
            if isinstance(answers, dict) and "repeatable_answers" in answers:
                has_structured_answers = True

            # First validate regular questions
            for section in form_details.sections:
                # Skip validation for repeatable sections - they'll be handled separately
                if section.repeatable:
                    continue

                for question in section.questions:
                    question_id = str(question.id)

                    # Check if this question embeds a repeatable section
                    if question.repeat_section_id:
                        repeat_section_id = str(question.repeat_section_id)
                        if repeat_section_id in section_map:
                            # Store for later processing
                            repeatable_embeddings[question_id] = {
                                "question": question,
                                "section": section_map[repeat_section_id],
                            }

                    # Skip validation if question is not visible
                    if not await self._is_question_visible(question, answers):
                        continue

                    # Validate required fields - check in the appropriate location based on answer format
                    # We already checked visibility above, so we know the question is visible
                    if question.required:
                        if has_structured_answers:
                            # For structured answers, check in the answers dictionary
                            if question_id not in answers.get("answers", {}):
                                errors.append(
                                    f"Question '{question.label}' is required"
                                )
                        else:
                            # For flat answers, check directly
                            if question_id not in answers:
                                errors.append(
                                    f"Question '{question.label}' is required"
                                )

                    # Validate against validation rules
                    if has_structured_answers:
                        if (
                            question_id in answers.get("answers", {})
                            and question.validation
                        ):
                            errors.extend(
                                self._validate_answer(
                                    question.validation,
                                    answers["answers"][question_id],
                                    question.label,
                                )
                            )
                    else:
                        if question_id in answers and question.validation:
                            errors.extend(
                                self._validate_answer(
                                    question.validation,
                                    answers[question_id],
                                    question.label,
                                )
                            )

            # Validate repeatable section questions
            for question_id, embedding in repeatable_embeddings.items():
                errors.extend(
                    await self._validate_repeatable_section(
                        embedding["question"],
                        embedding["section"],
                        answers,
                        question_id,
                    )
                )

            return {"valid": len(errors) == 0, "errors": errors}
        except Exception as e:
            await self.handle_error(e, {"form_id": form_id, "answers": answers})
            # Return a generic error for production use
            return {
                "valid": False,
                "errors": ["An error occurred during validation. Please try again."],
            }

    def _validate_answer(
        self, validation: Dict[str, Any], value: Any, label: str
    ) -> List[str]:
        """
        Validate a single answer against validation rules.

        Args:
            validation: Dictionary of validation rules
            value: The answer value to validate
            label: Question label for error messages

        Returns:
            List of error messages (empty if validation passed)
        """
        errors = []

        # Numeric validation
        if "min" in validation and value < validation["min"]:
            errors.append(f"Question '{label}' must be at least {validation['min']}")

        if "max" in validation and value > validation["max"]:
            errors.append(f"Question '{label}' must be at most {validation['max']}")

        # Regex validation
        if "regex" in validation and not re.match(validation["regex"], str(value)):
            errors.append(f"Question '{label}' has invalid format")

        return errors

    async def _validate_repeatable_section(
        self,
        controller_question: Any,
        repeat_section: Any,
        answers: Dict[str, Any],
        controller_id: str,
    ) -> List[str]:
        """
        Validate questions within a repeatable section.

        Args:
            controller_question: The question that controls the repeatable section
            repeat_section: The section to repeat
            answers: All form answers
            controller_id: ID of the controller question

        Returns:
            List of error messages (empty if validation passed)
        """
        errors = []

        # Skip if the controller question is not visible
        if not await self._is_question_visible(controller_question, answers):
            return errors

        # Get the repeat count from the answer
        repeat_count = get_repeat_count(answers, controller_id)
        max_repeats = controller_question.max_repeats or 10
        repeat_count = min(repeat_count, max_repeats)

        # Check if we have a structured answer schema with repeatable_answers
        # This handles the new AnswerSchema format
        section_id = str(repeat_section.id)

        # Check if answers is a dict with repeatable_answers (from AnswerSchema.model_dump())
        if (
            isinstance(answers, dict)
            and "repeatable_answers" in answers
            and section_id in answers["repeatable_answers"]
        ):
            # If using structured format, validate using that instead
            instances = answers["repeatable_answers"].get(section_id, {})

            # Validate each instance
            for instance_id, instance_answers in instances.items():
                # Create instance_index_map for visibility conditions
                try:
                    instance_idx = int(instance_id)
                except ValueError:
                    instance_idx = 0  # Default to 0 if not a valid integer

                instance_index_map = {
                    str(q.id): instance_idx for q in repeat_section.questions
                }

                # Validate each question in this instance
                for question in repeat_section.questions:
                    sub_question_id = str(question.id)

                    # Skip validation if question is not visible
                    if not await self._is_question_visible(
                        question, answers, instance_index_map
                    ):
                        continue

                    # Validate required fields - we already checked visibility above
                    if question.required and sub_question_id not in instance_answers:
                        errors.append(
                            f"Question '{question.label}' (instance {instance_idx + 1}) is required"
                        )

                    # Validate against validation rules
                    if sub_question_id in instance_answers and question.validation:
                        instance_errors = self._validate_answer(
                            question.validation,
                            instance_answers[sub_question_id],
                            f"{question.label} (instance {instance_idx + 1})",
                        )
                        errors.extend(instance_errors)

            return errors  # Return early if we processed structured answers

        # If we don't have structured answers, fall back to the original flat format validation
        # For each instance, validate the questions
        for instance_idx in range(repeat_count):
            # Create instance_index_map for visibility conditions
            instance_index_map = {
                str(q.id): instance_idx for q in repeat_section.questions
            }

            # Validate each question in the repeatable section
            for question in repeat_section.questions:
                sub_question_id = str(question.id)
                scoped_id = get_scoped_answer_key(sub_question_id, instance_idx)

                # Skip validation if question is not visible
                if not await self._is_question_visible(
                    question, answers, instance_index_map
                ):
                    continue

                # Validate required fields - we already checked visibility above
                if question.required and scoped_id not in answers:
                    errors.append(
                        f"Question '{question.label}' (instance {instance_idx + 1}) is required"
                    )

                # Validate against validation rules
                if scoped_id in answers and question.validation:
                    instance_errors = self._validate_answer(
                        question.validation,
                        answers[scoped_id],
                        f"{question.label} (instance {instance_idx + 1})",
                    )
                    errors.extend(instance_errors)

        return errors

    async def get_visible_questions(
        self, form_id: str, answers: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Get all visible questions based on current answers.

        Enhanced to handle repeatable sections and advanced visibility conditions.
        Questions from repeatable sections are included with appropriate scoping.
        """
        try:
            form_details = await self.get_form_with_details(form_id)
            if not form_details:
                return []

            # Build a map of section IDs to section objects for quick lookup
            section_map = {}
            # Handle both dict and FormWithDetails model
            sections = (
                form_details.get("sections")
                if isinstance(form_details, dict)
                else form_details.sections
            )

            for section in sections:
                if isinstance(section, dict):
                    section_map[str(section["id"])] = section
                else:
                    section_map[str(section.id)] = section

            # Track questions that embed repeatable sections
            repeatable_embeddings = {}

            # Process regular questions first
            visible_questions = []
            # Handle both dict and FormWithDetails model
            sections = (
                form_details.get("sections")
                if isinstance(form_details, dict)
                else form_details.sections
            )

            for section in sections:
                section_questions = []
                # Handle both dict and SectionWithQuestions model
                questions = (
                    section.get("questions")
                    if isinstance(section, dict)
                    else section.questions
                )

                for question in questions:
                    # Check if this question embeds a repeatable section
                    repeat_section_id = None
                    question_id = None

                    if isinstance(question, dict):
                        repeat_section_id = question.get("repeat_section_id")
                        question_id = str(question.get("_id", ""))
                    else:
                        repeat_section_id = getattr(question, "repeat_section_id", None)
                        question_id = str(getattr(question, "id", ""))

                    if repeat_section_id:
                        repeat_section_id = str(repeat_section_id)
                        if repeat_section_id in section_map:
                            # Store for later processing
                            repeatable_embeddings[question_id] = {
                                "question": question,
                                "section": section_map[repeat_section_id],
                            }

                    # Check visibility
                    if await self._is_question_visible(question, answers):
                        # Convert model to dict if needed
                        if not isinstance(question, dict):
                            question = question.model_dump()
                        section_questions.append(question)

                # Sort questions based on dependencies and order
                sorted_questions = await self._sort_questions(section_questions)
                visible_questions.extend(sorted_questions)

            # Process repeatable sections
            for question_id, embedding in repeatable_embeddings.items():
                embedding_question = embedding["question"]
                repeat_section = embedding["section"]

                # Check if the embedding question is visible
                if not await self._is_question_visible(embedding_question, answers):
                    continue

                # Get the repeat count from the answer
                repeat_count = get_repeat_count(answers, question_id)
                # Handle both dictionary and model instance
                if isinstance(embedding_question, dict):
                    max_repeats = embedding_question.get("max_repeats", 10)
                else:
                    max_repeats = getattr(embedding_question, "max_repeats", 10)
                repeat_count = min(repeat_count, max_repeats)

                # For each instance, add the questions with appropriate scoping
                for instance_idx in range(repeat_count):
                    # Create instance_index_map handling both dict and model instances
                    instance_index_map = {}
                    if (
                        isinstance(repeat_section, dict)
                        and "questions" in repeat_section
                    ):
                        for q in repeat_section["questions"]:
                            if isinstance(q, dict):
                                instance_index_map[str(q.get("_id", ""))] = instance_idx
                            else:
                                instance_index_map[str(getattr(q, "id", ""))] = (
                                    instance_idx
                                )
                    else:
                        for q in getattr(repeat_section, "questions", []):
                            if isinstance(q, dict):
                                instance_index_map[str(q.get("_id", ""))] = instance_idx
                            else:
                                instance_index_map[str(getattr(q, "id", ""))] = (
                                    instance_idx
                                )

                    # Handle both dict and model instances
                    questions = repeat_section.get("questions", [])
                    for question in questions:
                        # Create a scoped copy of the question
                        if isinstance(question, dict):
                            scoped_question = dict(question)
                            scoped_question["_id"] = (
                                f"{question.get('_id', '')}__{instance_idx}"
                            )
                            scoped_question["instance_index"] = instance_idx
                            scoped_question["parent_question_id"] = question_id
                        else:
                            # Convert model to dict for scoping
                            scoped_question = question.model_dump()
                            scoped_question["_id"] = (
                                f"{getattr(question, 'id', '')}__{instance_idx}"
                            )
                            scoped_question["instance_index"] = instance_idx
                            scoped_question["parent_question_id"] = question_id

                        # Check visibility with instance scoping
                        if await self._is_question_visible(
                            question, answers, instance_index_map
                        ):
                            visible_questions.append(scoped_question)

            # Make sure all questions are dictionaries
            result = []
            for q in visible_questions:
                if not isinstance(q, dict):
                    q = q.model_dump(by_alias=True)
                result.append(q)

            # Final sort by order
            return sorted(result, key=lambda q: q.get("order", 0))
        except Exception as e:
            await self.handle_error(e, {"form_id": form_id, "answers": answers})

    async def _is_question_visible(
        self,
        question: Union[Dict[str, Any], Question],
        answers: Dict[str, Any],
        instance_index_map: Optional[Dict[str, int]] = None,
    ) -> bool:
        """
        Check if a question should be visible based on current answers.

        Enhanced to support advanced visibility conditions and scoped targeting
        for repeatable sections.

        Args:
            question: The question to check visibility for (can be a dict or Question model)
            answers: Dictionary of question_id -> answer or an AnswerSchema instance
            instance_index_map: Optional mapping of section_id -> instance_index for scoped targeting

        Returns:
            True if the question should be visible, False otherwise
        """
        # Handle both dictionary and model instance
        visibility_condition = None
        if isinstance(question, dict):
            visibility_condition = question.get("visibility_condition")
        else:
            # It's a Question model instance
            visibility_condition = getattr(question, "visibility_condition", None)

        if not visibility_condition:
            return True

        # Check if we have a structured AnswerSchema (as dict from model_dump)
        if (
            isinstance(answers, dict)
            and "repeatable_answers" in answers
            and "answers" in answers
        ):
            # Create a flat dict for visibility evaluation
            flat_answers = answers["answers"].copy()

            # Add repeatable section answers
            for section_id, instances in answers["repeatable_answers"].items():
                for instance_id, instance_answers in instances.items():
                    try:
                        instance_idx = int(instance_id)
                    except ValueError:
                        instance_idx = 0

                    for question_id, answer in instance_answers.items():
                        key = f"{section_id}_{instance_id}_{question_id}"
                        flat_answers[key] = answer

                        # Also add in format question_id__instance_idx for visibility conditions
                        key2 = f"{question_id}__{instance_idx}"
                        flat_answers[key2] = answer

            return evaluate_visibility(
                flat_answers, visibility_condition, instance_index_map
            )
        else:
            # Use the form_logic utility for evaluation with regular dict
            return evaluate_visibility(
                answers, visibility_condition, instance_index_map
            )

    async def _sort_questions(
        self, questions: List[Union[Dict[str, Any], Question]]
    ) -> List[Union[Dict[str, Any], Question]]:
        """Sort questions based on dependencies and order."""
        # Create a graph of question dependencies
        graph = {}
        for question in questions:
            # Handle both dictionary and model instance
            if isinstance(question, dict):
                question_id = str(question["id"])
            else:
                question_id = str(question.id)

            graph[question_id] = {
                "question": question,
                "dependencies": set(),
                "show_after": set(),
            }

        # Build dependency graph
        for question in questions:
            # Handle both dictionary and model instance
            if isinstance(question, dict):
                # Check if using "id" or "_id" key
                if "id" in question:
                    question_id = str(question["id"])
                elif "_id" in question:
                    question_id = str(question["_id"])
                else:
                    # Skip questions without ID
                    continue
                visibility_condition = question.get("visibility_condition")
            else:
                # Handle model instance
                if hasattr(question, "id") and question.id:
                    question_id = str(question.id)
                elif hasattr(question, "_id") and question._id:
                    question_id = str(question._id)
                else:
                    # Skip questions without ID
                    continue
                visibility_condition = getattr(question, "visibility_condition", None)

            if visibility_condition:
                # Handle both dict and Pydantic model
                conditions = []
                if isinstance(visibility_condition, dict):
                    conditions = visibility_condition.get("conditions", [])
                else:
                    conditions = getattr(visibility_condition, "conditions", [])

                for cond in conditions:
                    # Handle both dict and Pydantic model
                    if isinstance(cond, dict):
                        dep_id = str(cond.get("question_id", ""))
                    else:
                        dep_id = str(getattr(cond, "question_id", ""))

                    if dep_id and dep_id in graph:
                        graph[question_id]["dependencies"].add(dep_id)
                        graph[dep_id]["show_after"].add(question_id)

        # Topological sort
        sorted_questions = []
        visited = set()
        temp = set()

        def visit(node_id):
            if node_id in temp:
                # Circular dependency detected
                return
            if node_id in visited:
                return

            temp.add(node_id)
            for dep_id in graph[node_id]["dependencies"]:
                visit(dep_id)
            temp.remove(node_id)
            visited.add(node_id)
            sorted_questions.append(graph[node_id]["question"])

        # Visit all nodes
        for node_id in graph:
            if node_id not in visited:
                visit(node_id)

        # Convert any model instances to dictionaries
        result = []
        for q in sorted_questions:
            if not isinstance(q, dict):
                q = q.model_dump(by_alias=True)
            result.append(q)

        # Sort questions within each dependency level by their order
        return sorted(result, key=lambda q: q.get("order", 0))

    async def update_question_order(
        self, section_id: str, question_orders: List[Dict[str, Any]]
    ) -> bool:
        """Update the order of questions within a section."""
        try:
            # Validate all question IDs exist
            question_ids = [order["question_id"] for order in question_orders]
            questions = await Question.find_many({
                "_id": {"$in": [ObjectId(qid) for qid in question_ids]}
            }).to_list(length=None)

            if len(questions) != len(question_ids):
                raise ValueError("Some question IDs do not exist")

            # Update orders
            for order in question_orders:
                question = await Question.find_one({
                    "_id": ObjectId(order["question_id"])
                })
                if question:
                    question.order = order["order"]
                    await question.save(is_update=True)

            # Invalidate cache
            section = await Section.find_one({"_id": ObjectId(section_id)})
            if section:
                # await self.cache.delete(f"form_details:{section.form_id}")
                pass

            return True
        except Exception as e:
            await self.handle_error(
                e, {"section_id": section_id, "question_orders": question_orders}
            )

    async def get_section_with_questions(
        self, section_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get section with all its questions."""
        try:
            # Get section
            section = await Section.find_one({"_id": ObjectId(section_id)})
            if not section:
                return None

            # Get all questions in one query
            questions = await Question.find_many(
                {"section_id": section.id}, sort=[("order", 1)]
            )
            questions_dict = [q.model_dump(by_alias=True) for q in questions]

            # Create SectionWithQuestions model
            section_with_questions = SectionWithQuestions(
                id=section.id,
                form_id=section.form_id,
                title=section.title,
                description=section.description,
                order=section.order,
                questions=questions_dict,
                repeatable=section.repeatable,
                created_at=section.created_at,
                updated_at=section.updated_at,
            )

            return section_with_questions.model_dump()
        except Exception as e:
            await self.handle_error(e, {"section_id": section_id})

    async def get_question(self, question_id: str) -> Optional[Dict[str, Any]]:
        """Get question by ID."""
        try:
            question = await Question.find_one({"_id": ObjectId(question_id)})
            # Convert model to dict before returning
            logger.info(f"Question pulling: {question}")
            return question.model_dump() if question else None
        except Exception as e:
            await self.handle_error(e, {"question_id": question_id})

    async def get_form_engine_data(
        self,
        form_id: str,
        answers: Dict[str, Any],
        mode: str,
        current_question_id: Optional[str] = None,
        include_dependencies: bool = True,
        include_validation: bool = True,
        include_visibility_rules: bool = True,
    ) -> Dict[str, Any]:
        """
        Unified form engine API that supports both sequential and full form modes.

        This method provides a flexible interface for form rendering and navigation:

        - In "sequential" mode: Returns the form with sections and questions ordered based on
          dependencies, only including questions that are currently visible based on answers.

        - In "full" mode: Returns the entire form structure with all logic (visibility rules,
          conditions, dependencies) for visualization and rendering the complete form.

        - In "next" mode: Returns only the next question based on the current question and answers,
          supporting step-by-step navigation.

        All modes properly handle repeatable sections, with appropriate scoping and instance
        management based on numeric answers.

        Args:
            form_id: The ID of the form to process
            answers: Current answers to form questions
            mode: The processing mode ("sequential", "full", or "next")
            current_question_id: The current question ID (for "next" mode)
            include_dependencies: Whether to include dependency metadata
            include_validation: Whether to include validation rules
            include_visibility_rules: Whether to include visibility conditions

        Returns:
            A dictionary containing the processed form data based on the requested mode
        """
        try:
            # Validate form_id
            if not form_id:
                raise ValueError("Form ID is required")

            # Initialize answers if None
            if answers is None:
                answers = {}

            # Process based on mode
            if mode == "sequential":
                return await self._get_sequential_mode_data(
                    form_id, answers, include_dependencies
                )
            elif mode == "full":
                return await self._get_full_mode_data(
                    form_id,
                    answers,
                    include_dependencies,
                    include_validation,
                    include_visibility_rules,
                )
            elif mode == "next":
                return await self._get_next_mode_data(
                    form_id, answers, current_question_id
                )
            else:
                raise ValueError(
                    f"Invalid mode: {mode}. Must be one of: sequential, full, next"
                )
        except ValueError:
            # Re-raise ValueError for client-side validation errors
            raise
        except Exception as e:
            # Handle other errors through the service's error handler
            await self.handle_error(
                e,
                {
                    "form_id": form_id,
                    "mode": mode,
                    "answers": answers,
                    "current_question_id": current_question_id,
                },
            )

    async def _get_sequential_mode_data(
        self,
        form_id: str,
        answers: Dict[str, Any],
        include_dependencies: bool = True,  # Not used directly but kept for future expansion
    ) -> Dict[str, Any]:
        """
        Get form data in sequential mode, returning only visible questions
        ordered by dependencies.

        Args:
            form_id: The ID of the form to process
            answers: Current answers to form questions
            include_dependencies: Whether to include dependency metadata (reserved for future use)

        Returns:
            A dictionary containing the sequential form data
        """
        # Get the sequential form
        sequential_form = await self.get_sequential_form(form_id, answers)

        # Add metadata about the mode
        visible_question_count = sum(
            len(section.get("questions", []))
            for section in sequential_form.get("sections", [])
        )

        # Add metadata if not already present
        if "_metadata" not in sequential_form:
            sequential_form["_metadata"] = {}

        # Find repeatable controllers
        repeatable_controllers = []
        for section in sequential_form.get("sections", []):
            for question in section.get("questions", []):
                if question.get("repeat_section_id") or question.get(
                    "_metadata", {}
                ).get("is_repeatable_controller"):
                    repeatable_controllers.append(question)

        # Extract repeatable sections info
        repeatable_sections_info = {}
        for controller in repeatable_controllers:
            controller_id = str(controller.get("_id"))
            metadata = controller.get("_metadata", {})

            if "instances" in metadata:
                repeatable_sections_info[controller_id] = {
                    "controller_id": controller_id,
                    "controller_label": controller.get("label"),
                    "section_id": str(metadata.get("repeat_section_id")),
                    "section_title": metadata.get("repeat_section_title"),
                    "repeat_count": metadata.get("repeat_count", 0),
                    "max_repeats": metadata.get("max_repeats", 10),
                    "instances": metadata.get("instances", []),
                }

        # Update metadata
        sequential_form["_metadata"].update({
            "mode": "sequential",
            "visible_question_count": visible_question_count,
            "total_sections": len(sequential_form.get("sections", [])),
            "has_repeatable_sections": len(repeatable_controllers) > 0,
            "repeatable_controller_count": len(repeatable_controllers),
            "repeatable_sections": repeatable_sections_info,
        })

        return sequential_form

    async def _get_full_mode_data(
        self,
        form_id: str,
        answers: Dict[str, Any],
        include_dependencies: bool,
        include_validation: bool = True,  # Not used yet but kept for future expansion
        include_visibility_rules: bool = True,  # Not used yet but kept for future expansion
    ) -> Dict[str, Any]:
        """
        Get form data in full mode, returning the entire form structure with
        all logic and metadata.

        Args:
            form_id: The ID of the form to process
            answers: Current answers to form questions
            include_dependencies: Whether to include dependency metadata
            include_validation: Whether to include validation rules (reserved for future use)
            include_visibility_rules: Whether to include visibility conditions (reserved for future use)

        Returns:
            A dictionary containing the full form data with metadata
        """
        # Get the full form details
        form_details = await self.get_form_with_details(form_id)
        if not form_details:
            raise ValueError(f"Form not found: {form_id}")

        # Convert to dict if it's a model
        if not isinstance(form_details, dict):
            form_details = form_details.model_dump(by_alias=True)

        # Get visible questions to mark which ones are currently visible
        visible_questions = await self.get_visible_questions(form_id, answers)
        visible_question_ids = {q.get("_id") for q in visible_questions}

        # Build dependency graph for visualization
        dependency_graph = {}

        # Process all sections and questions
        for section in form_details.get("sections", []):
            for question in section.get("questions", []):
                q_id = question.get("_id")

                # Add visibility status
                question["_metadata"] = {
                    "is_visible": q_id in visible_question_ids,
                }

                # Add dependency information if requested
                if include_dependencies:
                    self._add_dependency_metadata(question, dependency_graph)

                # Handle repeatable sections
                if question.get("repeat_section_id"):
                    await self._process_repeatable_section(
                        question, form_details, answers, visible_question_ids
                    )

        # Count repeatable sections
        repeatable_controllers = [
            question
            for section in form_details.get("sections", [])
            for question in section.get("questions", [])
            if question.get("repeat_section_id")
        ]

        # Get repeatable sections
        repeatable_sections = {
            str(section.get("_id")): section
            for section in form_details.get("sections", [])
            if section.get("repeatable", False)
        }

        # Add overall metadata
        form_details["_metadata"] = {
            "mode": "full",
            "dependency_graph": dependency_graph if include_dependencies else {},
            "visible_question_count": len(visible_question_ids),
            "total_question_count": sum(
                len(section.get("questions", []))
                for section in form_details.get("sections", [])
            ),
            "has_visibility_conditions": any(
                question.get("visibility_condition")
                for section in form_details.get("sections", [])
                for question in section.get("questions", [])
            ),
            "has_repeatable_sections": len(repeatable_controllers) > 0,
            "repeatable_controller_count": len(repeatable_controllers),
            "repeatable_section_count": len(repeatable_sections),
        }

        # Add repeatable sections metadata if it exists
        if "_repeatable_sections" in form_details:
            form_details["_metadata"]["repeatable_sections"] = form_details[
                "_repeatable_sections"
            ]
            # Remove from top level to keep the structure clean
            del form_details["_repeatable_sections"]

        # Future: Add validation rules if include_validation is True
        # Future: Add visibility rules if include_visibility_rules is True

        return form_details

    async def _get_next_mode_data(
        self, form_id: str, answers: Dict[str, Any], current_question_id: Optional[str]
    ) -> Dict[str, Any]:
        """
        Get form data in next mode, returning only the next question based on
        the current question and answers.

        If no current_question_id is provided, returns the first question.
        If answers is empty, ensures the first question is returned regardless of visibility conditions.
        """
        # Initialize answers if None
        if answers is None:
            answers = {}

        if not current_question_id:
            # If no current question, get the first visible question
            return await self._get_first_question(form_id, answers)
        else:
            # Find the next question after the current one
            return await self._get_next_question(form_id, answers, current_question_id)

    async def _get_first_question(
        self, form_id: str, answers: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Get the first visible question in a form.

        If answers is empty, ensures the first question is returned regardless of visibility conditions.
        """
        # Initialize answers if None
        if answers is None:
            answers = {}

        # Get the form details first
        form_details = await self.get_form_with_details(form_id)
        if not form_details:
            return {
                "next_question": None,
                "is_complete": True,
                "error": "Form not found",
            }

        # If there are no answers yet, return the first question from the first section
        if len(answers) == 0:
            # Get the first section with questions
            sections = (
                form_details.sections
                if hasattr(form_details, "sections")
                else form_details.get("sections", [])
            )
            for section in sections:
                questions = (
                    section.questions
                    if hasattr(section, "questions")
                    else section.get("questions", [])
                )
                if questions:
                    # Return the first question
                    first_question = questions[0]
                    # Convert to dict if needed
                    if not isinstance(first_question, dict):
                        first_question = first_question.model_dump(by_alias=True)

                    return {
                        "next_question": first_question,
                        "is_complete": False,
                        "_metadata": {
                            "mode": "next",
                            "total_remaining": len(questions) - 1,
                            "current_index": 0,
                            "total_questions": len(questions),
                        },
                    }

        # Get the sequential form to leverage our enhanced repeatable section handling
        sequential_form = await self.get_sequential_form(form_id, answers)

        # If no sections or questions, return empty result
        if not sequential_form.get("sections"):
            return {"next_question": None, "is_complete": True}

        # Get repeatable sections info
        repeatable_sections = sequential_form.get("_metadata", {}).get(
            "repeatable_sections", {}
        )

        # Flatten all questions from all sections, including repeatable section instances
        all_questions = []
        for section in sequential_form.get("sections", []):
            for question in section.get("questions", []):
                # Check if this is a repeatable section controller
                if (
                    question.get("repeat_section_id")
                    and str(question.get("_id")) in repeatable_sections
                ):
                    # Add the controller question itself
                    all_questions.append(question)

                    # Get the instances for this controller
                    controller_info = repeatable_sections.get(
                        str(question.get("_id")), {}
                    )
                    instances = controller_info.get("instances", [])

                    # Add all questions from all instances
                    for instance in instances:
                        for instance_question in instance.get("questions", []):
                            if instance_question.get("is_visible"):
                                # Create a virtual question that represents this instance
                                virtual_question = {
                                    "_id": instance_question.get("scoped_id"),
                                    "original_id": instance_question.get("original_id"),
                                    "label": instance_question.get("label"),
                                    "type": instance_question.get("type"),
                                    "instance_index": instance_question.get(
                                        "instance_index"
                                    ),
                                    "section_id": instance.get("section_id"),
                                    "is_instance_question": True,
                                    "instance_title": instance.get("title"),
                                    "_metadata": {
                                        "is_repeatable_instance": True,
                                        "instance_index": instance_question.get(
                                            "instance_index"
                                        ),
                                        "controller_id": str(question.get("_id")),
                                        "section_id": instance.get("section_id"),
                                    },
                                }
                                all_questions.append(virtual_question)
                else:
                    # Regular question
                    all_questions.append(question)

        if all_questions:
            next_question = all_questions[0]

            # Add navigation metadata
            navigation_metadata = {
                "mode": "next",
                "total_remaining": len(all_questions) - 1,
                "current_index": 0,
                "total_questions": len(all_questions),
            }

            # Add repeatable section metadata if applicable
            if next_question.get("is_instance_question"):
                navigation_metadata["is_repeatable_instance"] = True
                navigation_metadata["instance_index"] = next_question.get(
                    "instance_index"
                )
                navigation_metadata["instance_title"] = next_question.get(
                    "instance_title"
                )
                navigation_metadata["section_id"] = next_question.get("section_id")
                navigation_metadata["controller_id"] = next_question.get(
                    "_metadata", {}
                ).get("controller_id")

            return {
                "next_question": next_question,
                "is_complete": False,
                "_metadata": navigation_metadata,
            }
        else:
            return {"next_question": None, "is_complete": True}

    async def _get_next_question(
        self, form_id: str, answers: Dict[str, Any], current_question_id: str
    ) -> Dict[str, Any]:
        """
        Get the next question after the current one.
        """
        sequential_form = await self.get_sequential_form(form_id, answers)

        # Check if we're dealing with a scoped question ID (from a repeatable section)
        is_scoped = "_" in current_question_id

        # Get repeatable sections info
        repeatable_sections = sequential_form.get("_metadata", {}).get(
            "repeatable_sections", {}
        )

        # Flatten all questions from all sections, including repeatable section instances
        all_questions = []
        for section in sequential_form.get("sections", []):
            for question in section.get("questions", []):
                # Check if this is a repeatable section controller
                if (
                    question.get("repeat_section_id")
                    and str(question.get("_id")) in repeatable_sections
                ):
                    # Add the controller question itself
                    all_questions.append(question)

                    # Get the instances for this controller
                    controller_info = repeatable_sections.get(
                        str(question.get("_id")), {}
                    )
                    instances = controller_info.get("instances", [])

                    # Add all questions from all instances
                    for instance in instances:
                        for instance_question in instance.get("questions", []):
                            if instance_question.get("is_visible"):
                                # Create a virtual question that represents this instance
                                virtual_question = {
                                    "_id": instance_question.get("scoped_id"),
                                    "original_id": instance_question.get("original_id"),
                                    "label": instance_question.get("label"),
                                    "type": instance_question.get("type"),
                                    "instance_index": instance_question.get(
                                        "instance_index"
                                    ),
                                    "section_id": instance.get("section_id"),
                                    "is_instance_question": True,
                                    "instance_title": instance.get("title"),
                                    "_metadata": {
                                        "is_repeatable_instance": True,
                                        "instance_index": instance_question.get(
                                            "instance_index"
                                        ),
                                        "controller_id": str(question.get("_id")),
                                        "section_id": instance.get("section_id"),
                                    },
                                }
                                all_questions.append(virtual_question)
                else:
                    # Regular question
                    all_questions.append(question)

        # Find the current question index
        current_index = -1
        for i, question in enumerate(all_questions):
            q_id = question.get("_id")

            # Handle both scoped and non-scoped IDs
            if is_scoped:
                # If we're looking for a scoped ID, match exactly
                if q_id == current_question_id:
                    current_index = i
                    break
            else:
                # If we're looking for a regular ID, match either the ID or the original_id
                if (
                    q_id == current_question_id
                    or question.get("original_id") == current_question_id
                ):
                    current_index = i
                    break

        if current_index == -1:
            # Current question not found or not visible
            return {"next_question": None, "is_complete": True}

        if current_index < len(all_questions) - 1:
            # Return the next question
            next_question = all_questions[current_index + 1]

            # Add navigation metadata
            navigation_metadata = {
                "mode": "next",
                "total_remaining": len(all_questions) - current_index - 1,
                "current_index": current_index,
                "total_questions": len(all_questions),
            }

            # Add repeatable section metadata if applicable
            if next_question.get("is_instance_question"):
                navigation_metadata["is_repeatable_instance"] = True
                navigation_metadata["instance_index"] = next_question.get(
                    "instance_index"
                )
                navigation_metadata["instance_title"] = next_question.get(
                    "instance_title"
                )
                navigation_metadata["section_id"] = next_question.get("section_id")
                navigation_metadata["controller_id"] = next_question.get(
                    "_metadata", {}
                ).get("controller_id")

            return {
                "next_question": next_question,
                "is_complete": False,
                "_metadata": navigation_metadata,
            }
        else:
            # No more questions
            return {"next_question": None, "is_complete": True}

    def _add_dependency_metadata(
        self, question: Dict[str, Any], dependency_graph: Dict[str, Any]
    ) -> None:
        """
        Add dependency metadata to a question and update the dependency graph.
        """
        q_id = question.get("_id")
        visibility_condition = question.get("visibility_condition")

        # Ensure question is in the dependency graph even if it has no dependencies
        if q_id and q_id not in dependency_graph:
            dependency_graph[q_id] = {"controls": [], "depends_on": []}

        # Check if visibility condition exists and has conditions
        has_conditions = False
        conditions = []

        if visibility_condition:
            if isinstance(visibility_condition, dict):
                conditions = visibility_condition.get("conditions", [])
                has_conditions = bool(conditions)
            else:
                # Handle Pydantic model
                conditions = getattr(visibility_condition, "conditions", [])
                has_conditions = bool(conditions)

        if has_conditions:
            dependencies = []
            for condition in conditions:
                # Handle both dict and Pydantic model
                if isinstance(condition, dict):
                    dep_id = condition.get("question_id")
                else:
                    dep_id = getattr(condition, "question_id", None)

                if dep_id:
                    dependencies.append(dep_id)

                    # Add to dependency graph
                    if dep_id not in dependency_graph:
                        dependency_graph[dep_id] = {"controls": [], "depends_on": []}
                    if q_id not in dependency_graph:
                        dependency_graph[q_id] = {"controls": [], "depends_on": []}

                    # Add this question to the controls list of the dependency
                    if q_id not in dependency_graph[dep_id]["controls"]:
                        dependency_graph[dep_id]["controls"].append(q_id)

                    # Add the dependency to this question's depends_on list
                    if dep_id not in dependency_graph[q_id]["depends_on"]:
                        dependency_graph[q_id]["depends_on"].append(dep_id)

            # Ensure _metadata exists
            if "_metadata" not in question:
                question["_metadata"] = {}

            question["_metadata"]["dependencies"] = dependencies

    async def _process_repeatable_section(
        self,
        question: Dict[str, Any],
        form_details: Dict[str, Any],
        answers: Dict[str, Any],
        visible_question_ids: Set[
            str
        ] = None,  # Not used directly but kept for future expansion
    ) -> None:
        """
        Process a repeatable section controller question.

        Args:
            question: The question that controls the repeatable section
            form_details: The full form details
            answers: Current answers to form questions
            visible_question_ids: Set of visible question IDs (reserved for future use)
        """
        q_id = question.get("_id")
        repeat_section_id = question.get("repeat_section_id")

        # Ensure _metadata exists
        if "_metadata" not in question:
            question["_metadata"] = {}

        question["_metadata"]["is_repeatable_controller"] = True

        # Find the target section
        target_section = next(
            (
                s
                for s in form_details.get("sections", [])
                if s.get("_id") == repeat_section_id
            ),
            None,
        )

        if not target_section:
            # If target section not found, add error to metadata
            question["_metadata"]["repeatable_error"] = (
                f"Target section {repeat_section_id} not found"
            )
            return

        # Calculate instances based on answer
        repeat_count = 0
        if q_id in answers:
            try:
                repeat_count = int(answers[q_id])
                repeat_count = max(
                    0, min(repeat_count, question.get("max_repeats", 10))
                )
            except (ValueError, TypeError):
                pass

        # Add comprehensive metadata about the repeatable section
        question["_metadata"].update({
            "repeat_count": repeat_count,
            "repeat_section_id": str(repeat_section_id),
            "repeat_section_title": target_section.get("title"),
            "repeat_section_description": target_section.get("description"),
            "max_repeats": question.get("max_repeats", 10),
            "current_value": answers.get(q_id, 0),
        })  # type: ignore

        # Add the repeatable section to the form metadata if it doesn't exist
        if "_repeatable_sections" not in form_details:
            form_details["_repeatable_sections"] = {}

        # Add this controller to the repeatable sections metadata
        form_details["_repeatable_sections"][str(q_id)] = {
            "controller_id": str(q_id),
            "controller_label": question.get("label"),
            "section_id": str(repeat_section_id),
            "section_title": target_section.get("title"),
            "repeat_count": repeat_count,
            "max_repeats": question.get("max_repeats", 10),
        }

        # For each repeated section instance, mark which questions would be visible
        if repeat_count > 0:
            instances = []
            for i in range(repeat_count):
                # Create a detailed instance object
                instance = {
                    "index": i,
                    "instance_number": i + 1,  # 1-based for display
                    "title": f"{target_section.get('title')} #{i + 1}",
                    "section_id": str(repeat_section_id),
                    "visible_questions": [],
                    "questions": [],
                }

                # Create instance_index_map for this instance
                instance_index_map = {}
                for q in target_section.get("questions", []):
                    q_id_str = str(q.get("_id"))
                    instance_index_map[q_id_str] = i

                # Process each question in this instance
                for q in target_section.get("questions", []):
                    q_id_str = str(q.get("_id"))

                    # Create a scoped question ID for this instance
                    scoped_q_id = f"{q_id_str}_{i}"

                    # Check if this question is visible in this instance
                    is_visible = await self._is_question_visible(
                        q, answers, instance_index_map
                    )

                    # Create a question instance with proper scoping
                    question_instance = {
                        "original_id": q_id_str,
                        "scoped_id": scoped_q_id,
                        "instance_index": i,
                        "label": q.get("label"),
                        "type": q.get("type"),
                        "is_visible": is_visible,
                        "answer_key": f"{q_id_str}_{i}" if i > 0 else q_id_str,
                    }

                    # Add to the instance
                    instance["questions"].append(question_instance)

                    if is_visible:
                        instance["visible_questions"].append(scoped_q_id)

                instances.append(instance)

            # Add instances to the question metadata
            question["_metadata"]["instances"] = instances

            # Also add to the form's repeatable sections metadata
            form_details["_repeatable_sections"][str(q_id)]["instances"] = instances

    async def get_sequential_form(
        self, form_id: str, answers: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Get form in a sequential format for filling.

        Returns the form with sections and questions ordered based on dependencies,
        and only includes questions that are currently visible based on answers.

        Args:
            form_id: The ID of the form to retrieve
            answers: Optional dictionary of current answers to determine visibility

        Returns:
            A structured form object with sections and questions in dependency-based order
        """
        try:
            # Get form details
            form_details = await self.get_form_with_details(form_id)
            if not form_details:
                return {"error": "Form not found"}

            # If no answers provided, initialize empty dict
            if answers is None:
                answers = {}

            # Get visible questions
            visible_questions = await self.get_visible_questions(form_id, answers)

            # Create a map of visible question IDs for quick lookup
            visible_question_ids = {}
            for q in visible_questions:
                q_id = (
                    (q.get("_id") or q.get("id"))
                    if isinstance(q, dict)
                    else (getattr(q, "id", None) or getattr(q, "_id", None))
                )
                if q_id:
                    visible_question_ids[str(q_id)] = True

            # Build dependency graph for all questions
            dependency_graph = {}
            all_questions = []

            # Handle both dict and FormWithDetails model
            sections = (
                form_details.get("sections")
                if isinstance(form_details, dict)
                else form_details.sections
            )

            for section in sections:
                # Handle both dict and SectionWithQuestions model
                questions = (
                    section.get("questions")
                    if isinstance(section, dict)
                    else section.questions
                )
                all_questions.extend(questions)

            # Build the dependency graph
            for question in all_questions:
                # Get question ID
                if isinstance(question, dict):
                    question_id = str(question.get("_id", ""))
                    visibility_condition = question.get("visibility_condition")
                else:
                    question_id = str(getattr(question, "id", ""))
                    visibility_condition = getattr(
                        question, "visibility_condition", None
                    )

                dependency_graph[question_id] = {"depends_on": [], "controls": []}

                # Find dependencies
                if visibility_condition:
                    # Handle both dict and Pydantic model
                    conditions = []
                    if isinstance(visibility_condition, dict):
                        conditions = visibility_condition.get("conditions", [])
                    else:
                        conditions = getattr(visibility_condition, "conditions", [])

                    for condition in conditions:
                        # Handle both dict and Pydantic model
                        if isinstance(condition, dict):
                            dep_id = condition.get("question_id")
                        else:
                            dep_id = getattr(condition, "question_id", None)

                        if dep_id:
                            dependency_graph[question_id]["depends_on"].append(
                                str(dep_id)
                            )

            # Add controlling questions
            for question_id, deps in dependency_graph.items():
                for dep_id in deps["depends_on"]:
                    if dep_id in dependency_graph:
                        dependency_graph[dep_id]["controls"].append(question_id)

            # Create sequential form structure
            sequential_form = {}
            if isinstance(form_details, dict):
                sequential_form = {
                    "id": form_details.get("id") or form_details.get("_id"),
                    "name": form_details.get("name"),
                    "description": form_details.get("description"),
                    "version": form_details.get("version"),
                    "sections": [],
                }
            else:
                # Convert model to dict with by_alias=True for consistent field names
                form_dict = form_details.model_dump(by_alias=True)
                sequential_form = {
                    "id": form_dict.get("_id"),
                    "name": form_dict.get("name"),
                    "description": form_dict.get("description"),
                    "version": form_dict.get("version"),
                    "sections": [],
                }

            # Process sections
            for section in sections:
                section_id = None
                section_title = None
                section_description = None
                section_order = None
                section_repeatable = None
                section_questions = None

                if isinstance(section, dict):
                    section_id = str(section.get("id", "") or section.get("_id", ""))
                    section_title = section.get("title", "")
                    section_description = section.get("description", "")
                    section_order = section.get("order", 0)
                    section_repeatable = section.get("repeatable", False)
                    section_questions = section.get("questions", [])
                else:
                    # Convert model to dict with by_alias=True for consistent field names
                    section_dict = section.model_dump(by_alias=True)
                    section_id = str(section_dict.get("_id", ""))
                    section_title = section_dict.get("title", "")
                    section_description = section_dict.get("description", "")
                    section_order = section_dict.get("order", 0)
                    section_repeatable = section_dict.get("repeatable", False)
                    section_questions = section_dict.get("questions", [])

                # Skip sections with no visible questions
                section_has_visible_questions = False
                for q in section_questions:
                    q_id = (
                        str(q.get("_id", "") or q.get("id", ""))
                        if isinstance(q, dict)
                        else str(getattr(q, "id", "") or getattr(q, "_id", ""))
                    )
                    if q_id in visible_question_ids:
                        section_has_visible_questions = True
                        break

                if not section_has_visible_questions:
                    continue

                # Create section with only visible questions
                sequential_section = {
                    "id": section_id,
                    "title": section_title,
                    "description": section_description,
                    "order": section_order,
                    "repeatable": section_repeatable,
                    "questions": [],
                }

                # Add visible questions in dependency-based order
                visible_section_questions = []
                for q in section_questions:
                    q_id = (
                        str(q.get("_id", "") or q.get("id", ""))
                        if isinstance(q, dict)
                        else str(getattr(q, "id", "") or getattr(q, "_id", ""))
                    )
                    if q_id in visible_question_ids:
                        # Convert to dict if needed
                        if not isinstance(q, dict):
                            q = q.model_dump(by_alias=True)
                        visible_section_questions.append(q)

                # Sort questions based on dependencies
                sorted_questions = await self._sort_questions(visible_section_questions)

                # Add metadata for frontend rendering
                for question in sorted_questions:
                    question_id = str(question.get("_id", "") or question.get("id", ""))

                    # Add metadata for frontend rendering
                    question["metadata"] = {
                        "is_dependency_source": len(
                            dependency_graph.get(question_id, {}).get("controls", [])
                        )
                        > 0,
                        "depends_on_count": len(
                            dependency_graph.get(question_id, {}).get("depends_on", [])
                        ),
                        "depends_on": dependency_graph.get(question_id, {}).get(
                            "depends_on", []
                        ),
                        "controls": dependency_graph.get(question_id, {}).get(
                            "controls", []
                        ),
                    }

                    # Add to section
                    sequential_section["questions"].append(question)

                sequential_form["sections"].append(sequential_section)

            # Sort sections by order
            sequential_form["sections"] = sorted(
                sequential_form["sections"], key=lambda s: s["order"]
            )

            return sequential_form
        except Exception as e:
            await self.handle_error(e, {"form_id": form_id, "answers": answers})

    async def process_submission(
        self,
        org_id: str,
        form_id: str,
        submission_id: str,
        thesis_values: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Process a form submission, including scoring against thesis values if provided.
        This method will score the submission even if it was excluded by filters.

        Args:
            org_id: Organization ID
            form_id: Form ID
            submission_id: Submission ID
            thesis_values: Optional dictionary of thesis values to score against (question_id -> thesis_value)

        Returns:
            Dictionary containing:
            - submission: The submission data
            - scores: Dictionary of question scores (if thesis_values provided)
            - total_score: Overall score (if thesis_values provided)
            - excluded: Whether the submission was excluded by filters
            - exclusion_details: Details about exclusion if excluded

        Raises:
            ValueError: If submission or form not found
            RuntimeError: If scoring fails for any reason
        """
        try:
            self.logger.info(
                f"Processing submission {submission_id} for form {form_id} in org {org_id}"
            )

            # Validate inputs
            if not org_id or not form_id or not submission_id:
                raise ValueError("org_id, form_id, and submission_id are required")

            # Get the submission
            submission = await self.get_submission(submission_id)
            if not submission:
                raise ValueError(f"Submission {submission_id} not found")

            # Verify submission belongs to the correct form and org
            if str(submission.get("form_id")) != form_id:
                raise ValueError(
                    f"Submission {submission_id} does not belong to form {form_id}"
                )
            if str(submission.get("org_id")) != org_id:
                raise ValueError(
                    f"Submission {submission_id} does not belong to org {org_id}"
                )

            # Get form details for scoring
            form_details = await self.get_form_with_details(form_id)
            if not form_details:
                raise ValueError(f"Form {form_id} not found")

            # Initialize result
            result = {
                "submission": submission,
                "scores": {},
                "total_score": 0.0,
                "excluded": False,
                "exclusion_details": None,
                "metadata": {
                    "processed_at": datetime.now(timezone.utc).timestamp(),
                    "has_thesis_values": bool(thesis_values),
                    "scored_questions": 0,
                    "total_questions": 0,
                },
            }

            # Score the submission if thesis values are provided
            if thesis_values:
                self.logger.debug(
                    f"Scoring submission {submission_id} against {len(thesis_values)} thesis values"
                )
                scores = {}
                total_score = 0.0
                scored_questions = 0
                total_questions = 0

                # Get all questions from form details
                questions = []
                for section in form_details.get("sections", []):
                    questions.extend(section.get("questions", []))

                total_questions = len(questions)
                result["metadata"]["total_questions"] = total_questions

                # Score each question that has a thesis value
                for question in questions:
                    question_id = str(question.get("_id"))
                    if question_id in thesis_values and question_id in submission.get(
                        "answers", {}
                    ):
                        thesis_value = thesis_values[question_id]
                        answer_value = submission["answers"][question_id]
                        question_type = question.get("type")
                        question_label = question.get("label")

                        try:
                            # Score based on question type
                            score = None
                            if question_type == "short_text":
                                score = score_short_text(thesis_value, answer_value)
                            elif question_type == "long_text":
                                score = score_long_text(thesis_value, answer_value)
                            elif question_type == "number":
                                score = score_number(thesis_value, answer_value)
                            elif question_type == "range":
                                score = score_range(thesis_value, answer_value)
                            elif question_type == "single_select":
                                score = score_single_select(thesis_value, answer_value)
                            elif question_type == "multi_select":
                                score = score_multi_select(thesis_value, answer_value)
                            elif question_type == "boolean":
                                score = score_boolean(thesis_value, answer_value)
                            elif question_type == "file":
                                score = score_file(thesis_value, answer_value)
                            elif question_type == "date":
                                score = score_date(thesis_value, answer_value)
                            else:
                                self.logger.warning(
                                    f"Unsupported question type for scoring: {question_type} for question {question_label}"
                                )
                                continue

                            if score is not None:
                                scores[question_id] = {
                                    "score": score,
                                    "thesis_value": thesis_value,
                                    "answer_value": answer_value,
                                    "question_type": question_type,
                                    "question_label": question_label,
                                    "scored_at": datetime.now(timezone.utc).timestamp(),
                                }
                                total_score += score
                                scored_questions += 1
                                self.logger.debug(
                                    f"Scored question {question_label} with score {score}"
                                )
                            else:
                                self.logger.warning(
                                    f"No score calculated for question {question_label}"
                                )

                        except Exception as e:
                            self.logger.error(
                                f"Error scoring question {question_label} ({question_id}): {str(e)}",
                                exc_info=True,
                                extra={
                                    "question_id": question_id,
                                    "question_type": question_type,
                                    "thesis_value": thesis_value,
                                    "answer_value": answer_value,
                                },
                            )
                            scores[question_id] = {
                                "error": str(e),
                                "thesis_value": thesis_value,
                                "answer_value": answer_value,
                                "question_type": question_type,
                                "question_label": question_label,
                                "scored_at": datetime.now(timezone.utc).timestamp(),
                            }

                # Calculate average score
                if scored_questions > 0:
                    result["total_score"] = total_score / scored_questions
                    result["metadata"]["scored_questions"] = scored_questions
                    result["metadata"]["scoring_success_rate"] = (
                        scored_questions / total_questions
                    )
                else:
                    self.logger.warning(
                        f"No questions were successfully scored for submission {submission_id}"
                    )

                result["scores"] = scores

                # Update submission with scores
                try:
                    await Submission.find_one({"_id": ObjectId(submission_id)}).update({
                        "$set": {
                            "scores": scores,
                            "total_score": result["total_score"],
                            "scoring_metadata": result["metadata"],
                        }
                    })
                    self.logger.debug(f"Updated submission {submission_id} with scores")
                except Exception as e:
                    self.logger.error(
                        f"Failed to update submission {submission_id} with scores: {str(e)}",
                        exc_info=True,
                    )
                    raise RuntimeError(f"Failed to save scores to database: {str(e)}")

            # Update submission with exclusion status if it exists in metadata
            if submission.get("metadata", {}).get("excluded"):
                result["excluded"] = True
                result["exclusion_details"] = submission["metadata"].get(
                    "exclusion_details"
                )
                self.logger.info(
                    f"Submission {submission_id} was excluded: {result['exclusion_details']}"
                )

            self.logger.info(
                f"Successfully processed submission {submission_id}",
                extra={
                    "submission_id": submission_id,
                    "form_id": form_id,
                    "org_id": org_id,
                    "excluded": result["excluded"],
                    "has_scores": bool(result["scores"]),
                    "total_score": result["total_score"],
                },
            )
            return result

        except ValueError as ve:
            self.logger.error(
                f"Validation error processing submission {submission_id}: {str(ve)}"
            )
            raise
        except Exception as e:
            self.logger.error(
                f"Error processing submission {submission_id}: {str(e)}",
                exc_info=True,
                extra={
                    "submission_id": submission_id,
                    "form_id": form_id,
                    "org_id": org_id,
                    "has_thesis_values": bool(thesis_values),
                },
            )
            await self.handle_error(
                e,
                {
                    "org_id": org_id,
                    "form_id": form_id,
                    "submission_id": submission_id,
                    "has_thesis_values": bool(thesis_values),
                },
            )
            raise RuntimeError(f"Failed to process submission: {str(e)}")

    async def reorder_sections(self, items: List[Dict[str, Any]]) -> bool:
        """
        Atomically reorder sections with strict validation.

        Args:
            items: List of dicts containing section id and new order

        Returns:
            bool: True if reordering was successful

        Raises:
            ValueError: If validation fails (missing IDs, duplicate orders, etc.)
            DatabaseError: If database operation fails
        """
        logger.info(f"Reordering sections: {items}")
        try:
            # Extract IDs and orders for validation
            section_ids = {item.id for item in items}
            orders = [item.order for item in items]

            # Get all sections to validate
            sections = await Section.find_many({
                "_id": {"$in": [ObjectId(id) for id in section_ids]}
            })

            # Validate all sections exist
            if len(sections) != len(section_ids):
                raise ValueError("One or more section IDs do not exist")

            # Validate no duplicate orders
            if len(orders) != len(set(orders)):
                raise ValueError("Duplicate order values detected")

            # Validate all sections from the same form
            form_ids = {str(section.form_id) for section in sections}
            if len(form_ids) > 1:
                raise ValueError("Sections must be from the same form")

            # Get all sections from the form to validate completeness
            form_id = form_ids.pop()
            all_form_sections = await Section.find_many({"form_id": ObjectId(form_id)})
            all_form_section_ids = {str(section.id) for section in all_form_sections}

            # Validate all sections from the form are included
            if section_ids != all_form_section_ids:
                raise ValueError(
                    "All sections from the form must be included in the reorder request"
                )

            for item in items:
                result = await Section.update_one(
                    {"_id": ObjectId(item.id)},
                    {"$set": {"order": item.order}},
                )
                if result.modified_count != 1:
                    raise ValueError(f"Failed to update section {item.id}")

            return True

        except Exception as e:
            if isinstance(e, ValueError):
                raise
            raise DatabaseError(f"Failed to reorder sections: {str(e)}")

    async def reorder_questions(self, items: List[Dict[str, Any]]) -> bool:
        """
        Atomically reorder questions with strict validation.

        Args:
            items: List of dicts containing question id and new order

        Returns:
            bool: True if reordering was successful

        Raises:
            ValueError: If validation fails (missing IDs, duplicate orders, etc.)
            DatabaseError: If database operation fails
        """
        try:
            # Extract IDs and orders for validation
            question_ids = {item.id for item in items}
            orders = [item.order for item in items]

            # Get all questions to validate
            questions = await Question.find_many({
                "_id": {"$in": [ObjectId(id) for id in question_ids]}
            })

            # Validate all questions exist
            if len(questions) != len(question_ids):
                raise ValueError("One or more question IDs do not exist")

            # Validate no duplicate orders
            if len(orders) != len(set(orders)):
                raise ValueError("Duplicate order values detected")

            # Validate all questions from the same section
            section_ids = {str(question.section_id) for question in questions}
            if len(section_ids) > 1:
                raise ValueError("Questions must be from the same section")

            # Get all questions from the section to validate completeness
            section_id = section_ids.pop()
            all_section_questions = await Question.find_many({
                "section_id": ObjectId(section_id)
            })
            all_section_question_ids = {
                str(question.id) for question in all_section_questions
            }

            # Validate all questions from the section are included
            if question_ids != all_section_question_ids:
                raise ValueError(
                    "All questions from the section must be included in the reorder request"
                )

            # Perform atomic update

            for item in items:
                await Question.update_many(
                    {"_id": ObjectId(item.id)},
                    {
                        "$set": {
                            "order": item.order,
                            "updated_at": int(datetime.now(timezone.utc).timestamp()),
                        }
                    },
                )

            return True

        except Exception as e:
            if isinstance(e, ValueError):
                raise
            raise DatabaseError(f"Failed to reorder questions: {str(e)}")
