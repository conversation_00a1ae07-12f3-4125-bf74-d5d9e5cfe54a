"""
Thesis matching and scoring tools for TractionX AI Agent system.
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from ai.tools.base import BaseTool, DataRetrievalTool, AnalysisTool, ToolInput, ToolOutput, tool
from ai.models import AgentContext


class ThesisMatcherInput(ToolInput):
    """Input for thesis matcher tool."""
    thesis_id: str = Field(..., description="Thesis ID to match against")
    submission_id: Optional[str] = Field(None, description="Submission ID to analyze")
    deal_id: Optional[str] = Field(None, description="Deal ID to analyze")
    custom_data: Optional[Dict[str, Any]] = Field(None, description="Custom data to analyze")


class ThesisMatcherOutput(ToolOutput):
    """Output for thesis matcher tool."""
    match_score: Optional[float] = Field(None, description="Overall match score (0-1)")
    category_scores: Optional[Dict[str, float]] = Field(None, description="Scores by category")
    matching_criteria: Optional[List[str]] = Field(None, description="Criteria that matched")
    non_matching_criteria: Optional[List[str]] = Field(None, description="Criteria that didn't match")
    recommendations: Optional[List[str]] = Field(None, description="Recommendations")


@tool(
    name="thesis_matcher",
    description="Analyzes investment opportunities against thesis criteria and calculates match scores",
    category="analysis",
    tags=["thesis", "matching", "scoring", "investment"]
)
class ThesisMatcherTool(DataRetrievalTool, AnalysisTool):
    """Tool for matching deals/submissions against investment thesis."""

    def __init__(self):
        super().__init__()
    
    async def execute(
        self,
        input_data: ThesisMatcherInput,
        context: AgentContext
    ) -> ThesisMatcherOutput:
        """Execute thesis matching analysis."""
        
        try:
            # Get thesis data
            thesis_data = await self._get_thesis_data(input_data.thesis_id, context)
            if not thesis_data:
                return ThesisMatcherOutput(
                    success=False,
                    error=f"Thesis not found: {input_data.thesis_id}"
                )
            
            # Get data to analyze
            analysis_data = None
            if input_data.submission_id:
                analysis_data = await self._get_submission_data(input_data.submission_id, context)
            elif input_data.deal_id:
                analysis_data = await self._get_deal_data(input_data.deal_id, context)
            elif input_data.custom_data:
                analysis_data = input_data.custom_data
            
            if not analysis_data:
                return ThesisMatcherOutput(
                    success=False,
                    error="No data provided for analysis"
                )
            
            # Perform matching analysis
            match_result = await self._perform_thesis_matching(
                thesis_data, analysis_data, context
            )
            
            return ThesisMatcherOutput(
                success=True,
                data=match_result,
                match_score=match_result.get("overall_score"),
                category_scores=match_result.get("category_scores"),
                matching_criteria=match_result.get("matching_criteria"),
                non_matching_criteria=match_result.get("non_matching_criteria"),
                recommendations=match_result.get("recommendations"),
                metadata={
                    "thesis_id": input_data.thesis_id,
                    "analysis_type": "thesis_matching"
                }
            )
            
        except Exception as e:
            return await self._handle_error(e, input_data)
    
    async def _get_thesis_data(self, thesis_id: str, context: AgentContext) -> Optional[Dict[str, Any]]:
        """Get thesis configuration data."""
        # TODO: Implement thesis data retrieval from backend
        self.logger.debug(f"Getting thesis data for {thesis_id}")
        
        # Placeholder thesis data
        return {
            "id": thesis_id,
            "name": "Sample Thesis",
            "criteria": {
                "market_size": {"min_value": 1000000000, "weight": 0.3},
                "team_experience": {"min_years": 5, "weight": 0.25},
                "traction": {"required": True, "weight": 0.2},
                "business_model": {"types": ["saas", "marketplace"], "weight": 0.25}
            },
            "scoring_rules": [],
            "bonus_rules": []
        }
    
    async def _perform_thesis_matching(
        self,
        thesis_data: Dict[str, Any],
        analysis_data: Dict[str, Any],
        context: AgentContext
    ) -> Dict[str, Any]:
        """Perform the actual thesis matching analysis."""
        
        criteria = thesis_data.get("criteria", {})
        category_scores = {}
        matching_criteria = []
        non_matching_criteria = []
        
        # Analyze each criterion
        for criterion_name, criterion_config in criteria.items():
            score = await self._evaluate_criterion(
                criterion_name, criterion_config, analysis_data
            )
            
            category_scores[criterion_name] = score
            
            if score >= 0.7:  # Threshold for matching
                matching_criteria.append(criterion_name)
            else:
                non_matching_criteria.append(criterion_name)
        
        # Calculate overall weighted score
        overall_score = 0.0
        total_weight = 0.0
        
        for criterion_name, score in category_scores.items():
            weight = criteria.get(criterion_name, {}).get("weight", 1.0)
            overall_score += score * weight
            total_weight += weight
        
        if total_weight > 0:
            overall_score /= total_weight
        
        # Generate recommendations
        recommendations = await self._generate_recommendations(
            category_scores, matching_criteria, non_matching_criteria
        )
        
        return {
            "overall_score": overall_score,
            "category_scores": category_scores,
            "matching_criteria": matching_criteria,
            "non_matching_criteria": non_matching_criteria,
            "recommendations": recommendations,
            "analysis_timestamp": context.metadata.get("timestamp")
        }
    
    async def _evaluate_criterion(
        self,
        criterion_name: str,
        criterion_config: Dict[str, Any],
        analysis_data: Dict[str, Any]
    ) -> float:
        """Evaluate a single criterion."""
        
        # TODO: Implement actual criterion evaluation logic
        # This would analyze the data against the specific criterion
        
        self.logger.debug(f"Evaluating criterion: {criterion_name}")
        
        # Placeholder scoring logic
        if criterion_name == "market_size":
            market_size = analysis_data.get("market_size", 0)
            min_value = criterion_config.get("min_value", 0)
            return min(1.0, market_size / min_value) if min_value > 0 else 0.5
        
        elif criterion_name == "team_experience":
            team_years = analysis_data.get("team_experience_years", 0)
            min_years = criterion_config.get("min_years", 0)
            return min(1.0, team_years / min_years) if min_years > 0 else 0.5
        
        elif criterion_name == "traction":
            has_traction = analysis_data.get("has_traction", False)
            return 1.0 if has_traction else 0.0
        
        elif criterion_name == "business_model":
            business_model = analysis_data.get("business_model", "")
            allowed_types = criterion_config.get("types", [])
            return 1.0 if business_model in allowed_types else 0.0
        
        return 0.5  # Default neutral score
    
    async def _generate_recommendations(
        self,
        category_scores: Dict[str, float],
        matching_criteria: List[str],
        non_matching_criteria: List[str]
    ) -> List[str]:
        """Generate recommendations based on analysis."""
        
        recommendations = []
        
        if len(matching_criteria) > len(non_matching_criteria):
            recommendations.append("Strong thesis match - recommend for further evaluation")
        
        for criterion in non_matching_criteria:
            if category_scores.get(criterion, 0) < 0.3:
                recommendations.append(f"Address concerns in {criterion.replace('_', ' ')}")
        
        if not recommendations:
            recommendations.append("Mixed results - requires detailed review")
        
        return recommendations


class ScoringEngineInput(ToolInput):
    """Input for scoring engine tool."""
    data: Dict[str, Any] = Field(..., description="Data to score")
    scoring_rules: List[Dict[str, Any]] = Field(..., description="Scoring rules to apply")
    weights: Optional[Dict[str, float]] = Field(None, description="Weights for different categories")


class ScoringEngineOutput(ToolOutput):
    """Output for scoring engine tool."""
    total_score: Optional[float] = Field(None, description="Total weighted score")
    category_scores: Optional[Dict[str, float]] = Field(None, description="Scores by category")
    rule_results: Optional[List[Dict[str, Any]]] = Field(None, description="Individual rule results")


@tool(
    name="scoring_engine",
    description="Applies configurable scoring rules to data and calculates weighted scores",
    category="analysis",
    tags=["scoring", "rules", "analysis", "evaluation"]
)
class ScoringEngineTool(AnalysisTool):
    """Tool for applying scoring rules to data."""

    def __init__(self):
        super().__init__()
    
    async def execute(
        self,
        input_data: ScoringEngineInput,
        context: AgentContext
    ) -> ScoringEngineOutput:
        """Execute scoring engine."""
        
        try:
            rule_results = []
            category_scores = {}
            
            # Apply each scoring rule
            for rule in input_data.scoring_rules:
                result = await self._apply_scoring_rule(rule, input_data.data)
                rule_results.append(result)
                
                # Aggregate by category
                category = result.get("category", "default")
                if category not in category_scores:
                    category_scores[category] = []
                category_scores[category].append(result.get("score", 0))
            
            # Calculate category averages
            for category in category_scores:
                scores = category_scores[category]
                category_scores[category] = sum(scores) / len(scores) if scores else 0
            
            # Calculate total weighted score
            total_score = await self._calculate_weighted_score(
                category_scores, input_data.weights
            )
            
            return ScoringEngineOutput(
                success=True,
                data={
                    "total_score": total_score,
                    "category_scores": category_scores,
                    "rule_results": rule_results
                },
                total_score=total_score,
                category_scores=category_scores,
                rule_results=rule_results,
                metadata={
                    "rules_applied": len(input_data.scoring_rules),
                    "categories": list(category_scores.keys())
                }
            )
            
        except Exception as e:
            return await self._handle_error(e, input_data)
    
    async def _apply_scoring_rule(
        self,
        rule: Dict[str, Any],
        data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Apply a single scoring rule."""
        
        rule_type = rule.get("type", "numeric")
        field = rule.get("field")
        value = data.get(field)
        
        if rule_type == "numeric":
            return await self._apply_numeric_rule(rule, value)
        elif rule_type == "categorical":
            return await self._apply_categorical_rule(rule, value)
        elif rule_type == "boolean":
            return await self._apply_boolean_rule(rule, value)
        else:
            return {
                "rule_id": rule.get("id"),
                "category": rule.get("category"),
                "score": 0,
                "matched": False,
                "error": f"Unknown rule type: {rule_type}"
            }
    
    async def _apply_numeric_rule(self, rule: Dict[str, Any], value: Any) -> Dict[str, Any]:
        """Apply numeric scoring rule."""
        # TODO: Implement numeric rule logic
        return {
            "rule_id": rule.get("id"),
            "category": rule.get("category"),
            "score": 0.5,
            "matched": True
        }
    
    async def _apply_categorical_rule(self, rule: Dict[str, Any], value: Any) -> Dict[str, Any]:
        """Apply categorical scoring rule."""
        # TODO: Implement categorical rule logic
        return {
            "rule_id": rule.get("id"),
            "category": rule.get("category"),
            "score": 0.5,
            "matched": True
        }
    
    async def _apply_boolean_rule(self, rule: Dict[str, Any], value: Any) -> Dict[str, Any]:
        """Apply boolean scoring rule."""
        # TODO: Implement boolean rule logic
        return {
            "rule_id": rule.get("id"),
            "category": rule.get("category"),
            "score": 1.0 if value else 0.0,
            "matched": bool(value)
        }
    
    async def _calculate_weighted_score(
        self,
        category_scores: Dict[str, float],
        weights: Optional[Dict[str, float]]
    ) -> float:
        """Calculate weighted total score."""
        
        if not weights:
            # Equal weights
            scores = list(category_scores.values())
            return sum(scores) / len(scores) if scores else 0
        
        total_score = 0
        total_weight = 0
        
        for category, score in category_scores.items():
            weight = weights.get(category, 1.0)
            total_score += score * weight
            total_weight += weight
        
        return total_score / total_weight if total_weight > 0 else 0
