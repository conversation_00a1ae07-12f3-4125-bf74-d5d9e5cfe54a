#!/usr/bin/env python3
"""
Create unique compound index on deals collection to prevent duplicate submissions.

This script creates a compound index on (org_id, submission_ids) to ensure
that no two deals can have the same submission_id within the same organization.

Usage:
    poetry run python create_unique_deal_index.py
"""

import asyncio
import logging
import sys

from app.core.config import settings
from motor.motor_asyncio import AsyncIOMotorClient

logger = logging.getLogger(__name__)


async def create_unique_deal_index():
    """Create unique compound index to prevent duplicate deals for same submission."""
    try:
        # Connect to MongoDB
        client = AsyncIOMotorClient(settings.MONGODB_URL)
        db = client[settings.MONGODB_DB_NAME]
        deals_collection = db.deals

        print("Connected to MongoDB")
        print(f"Database: {settings.MONGODB_DB_NAME}")

        # Check existing indexes
        existing_indexes = await deals_collection.list_indexes().to_list(length=None)
        print("\nExisting indexes:")
        for idx in existing_indexes:
            print(f"  - {idx}")

        # Check if our unique index already exists
        unique_index_name = "org_submission_unique"
        index_exists = any(
            idx.get("name") == unique_index_name for idx in existing_indexes
        )

        if index_exists:
            print(f"\nUnique index '{unique_index_name}' already exists.")
        else:
            print(f"\nCreating unique compound index '{unique_index_name}'...")

            # Create unique compound index
            # This ensures no two deals can have overlapping submission_ids within same org
            result = await deals_collection.create_index(
                [("org_id", 1), ("submission_ids", 1)],
                name=unique_index_name,
                unique=True,
                background=True,
                sparse=False,
            )

            print(f"✅ Successfully created unique index: {result}")

        # Check for existing duplicate deals
        print("\nChecking for existing duplicate deals...")

        pipeline = [
            {"$unwind": "$submission_ids"},
            {
                "$group": {
                    "_id": {"org_id": "$org_id", "submission_id": "$submission_ids"},
                    "deal_ids": {"$push": "$_id"},
                    "count": {"$sum": 1},
                }
            },
            {"$match": {"count": {"$gt": 1}}},
        ]

        duplicates = await deals_collection.aggregate(pipeline).to_list(length=None)

        if duplicates:
            print(f"⚠️  Found {len(duplicates)} duplicate submission-deal mappings:")
            for dup in duplicates:
                org_id = dup["_id"]["org_id"]
                submission_id = dup["_id"]["submission_id"]
                deal_ids = dup["deal_ids"]
                print(
                    f"  Org {org_id}, Submission {submission_id}: {len(deal_ids)} deals - {deal_ids}"
                )

            print("\n❌ Cannot create unique index with existing duplicates.")
            print(
                "Please clean up duplicate deals first or use --force to remove duplicates."
            )
            return False
        else:
            print("✅ No duplicate deals found.")

        # Verify the index was created
        updated_indexes = await deals_collection.list_indexes().to_list(length=None)
        unique_index_created = any(
            idx.get("name") == unique_index_name for idx in updated_indexes
        )

        if unique_index_created:
            print(f"\n✅ Unique index '{unique_index_name}' is now active.")
            print(
                "This will prevent duplicate deals for the same submission going forward."
            )
            return True
        else:
            print(f"\n❌ Failed to create unique index '{unique_index_name}'.")
            return False

    except Exception as e:
        logger.error(f"Error creating unique index: {e}", exc_info=True)
        print(f"\n❌ Error: {e}")
        return False
    finally:
        client.close()


async def remove_duplicate_deals():
    """Remove duplicate deals, keeping only the oldest one for each submission."""
    try:
        client = AsyncIOMotorClient(settings.MONGODB_URL)
        db = client[settings.MONGODB_DB_NAME]
        deals_collection = db.deals

        print("Finding and removing duplicate deals...")

        # Find duplicates
        pipeline = [
            {"$unwind": "$submission_ids"},
            {
                "$group": {
                    "_id": {"org_id": "$org_id", "submission_id": "$submission_ids"},
                    "deals": {
                        "$push": {"deal_id": "$_id", "created_at": "$created_at"}
                    },
                    "count": {"$sum": 1},
                }
            },
            {"$match": {"count": {"$gt": 1}}},
        ]

        duplicates = await deals_collection.aggregate(pipeline).to_list(length=None)

        removed_count = 0
        for dup in duplicates:
            deals = dup["deals"]
            # Sort by created_at to keep the oldest
            deals.sort(key=lambda x: x["created_at"])

            # Remove all but the first (oldest) deal
            for deal in deals[1:]:
                result = await deals_collection.delete_one({"_id": deal["deal_id"]})
                if result.deleted_count > 0:
                    removed_count += 1
                    print(f"Removed duplicate deal {deal['deal_id']}")

        print(f"✅ Removed {removed_count} duplicate deals.")
        return removed_count

    except Exception as e:
        logger.error(f"Error removing duplicates: {e}", exc_info=True)
        print(f"❌ Error removing duplicates: {e}")
        return 0
    finally:
        client.close()


async def main():
    """Main function to create unique index."""
    if len(sys.argv) > 1 and sys.argv[1] == "--remove-duplicates":
        await remove_duplicate_deals()

    success = await create_unique_deal_index()

    if success:
        print("\n🎉 Database migration completed successfully!")
        print(
            "The submission processing system is now protected against race condition duplicates."
        )
    else:
        print("\n💥 Database migration failed!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
