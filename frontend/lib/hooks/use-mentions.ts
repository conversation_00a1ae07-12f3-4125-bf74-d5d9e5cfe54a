import { useOrgMembers } from './use-org-members'

export interface MentionUser {
  _id: string
  name: string
  email: string
  avatar?: string
}

export function useMentions() {
  const { members } = useOrgMembers()

  const searchUsers = (query: string): MentionUser[] => {
    console.log('[Mentions] searchUsers query:', query, 'members:', members)
    if (!query.trim()) return members
    const searchTerm = query.toLowerCase()
    return members.filter(user => 
      user.name.toLowerCase().includes(searchTerm) ||
      user.email.toLowerCase().includes(searchTerm)
    )
  }

  const getUserById = (id: string): MentionUser | undefined => {
    return members.find(user => user._id === id)
  }

  const extractMentions = (content: string): string[] => {
    console.log('[Mentions] extractMentions content:', content)
    const mentionRegex = /@(\w+)/g
    const mentions: string[] = []
    let match
    while ((match = mentionRegex.exec(content)) !== null) {
      mentions.push(match[1])
    }
    return mentions
  }

  return {
    users: members,
    searchUsers,
    getUserById,
    extractMentions,
  }
} 