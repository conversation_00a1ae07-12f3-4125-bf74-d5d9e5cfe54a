/**
 * Form Builder Types
 *
 * This file contains TypeScript types for the Form Builder feature.
 *
 * The form data model follows a parent-child relationship:
 * Form -> Section -> Question
 *
 * Each child entity must reference its parent's _id to maintain proper relationships.
 */

// Question Types - Matching backend enum
export enum QuestionType {
  SHORT_TEXT = "short_text",
  LONG_TEXT = "long_text",
  NUMBER = "number",
  RANGE = "range",
  SINGLE_SELECT = "single_select",
  MULTI_SELECT = "multi_select",
  BOOLEAN = "boolean",
  FILE = "file",
  DATE = "date",
}

// Validation Rule
export interface ValidationRule {
  min?: number;
  max?: number;
  regex?: string;
  required_if?: {
    question_id: string;
    value: any;
  };
  custom?: string;
}

// Condition Clause - matches backend ConditionClause
export interface ConditionClause {
  question_id: string;
  value: any;
  section_instance_index?: number;
  operator?: "==" | "!=" | ">" | "<" | ">=" | "<="; // For individual condition operators
}

// Visibility Condition - matches backend VisibilityCondition
export interface VisibilityCondition {
  operator: "and" | "or" | "not" | "==" | "!=" | ">" | "<" | ">=" | "<=";
  conditions: ConditionClause[];
}

// Question Option
export interface QuestionOption {
  label: string;
  value: string;
}

// Core Field Types - Matching backend enum
export enum CoreFieldType {
  COMPANY_NAME = "company_name",
  STAGE = "stage",
  SECTOR = "sector",
}

// Question
export interface Question {
  _id?: string;  // MongoDB ID field
  id?: string;   // Keep for backward compatibility
  local_id?: string; // For stable local IDs for unsaved questions
  section_id: string;
  type: QuestionType;
  label: string;
  help_text?: string;
  required: boolean;
  core_field?: CoreFieldType; // Identifies if this is a core field for deal creation
  options?: QuestionOption[];
  validation?: ValidationRule;
  visibility_condition?: VisibilityCondition;
  repeat_section_id?: string;
  max_repeats?: number;
  order: number;
  created_at?: number;
  updated_at?: number;
}

// Section
export interface Section {
  _id?: string;  // MongoDB ID field
  id?: string;   // Keep for backward compatibility
  form_id?: string;
  title: string;  // Changed from name to title to match backend
  description: string;
  order: number;
  repeatable: boolean;
  questions: Question[];
  created_at?: number;
  updated_at?: number;
}

// Form
export interface Form {
  _id?: string;  // MongoDB ID field
  id?: string;   // Keep for backward compatibility
  name: string;
  description: string;
  status: "draft" | "active";
  sections: Section[];
  default_section_ids?: string[];
  is_active?: boolean;
  version?: number;
  created_at?: number;
  updated_at?: number;
}

// Section with populated questions
export interface SectionWithQuestions extends Omit<Section, 'questions'> {
  questions: Question[];
}

// Form with detailed sections and questions
export interface FormWithDetails extends Omit<Form, 'sections'> {
  sections: SectionWithQuestions[];
}

// Form Creation Request
export interface FormCreateRequest {
  name: string;
  description: string;
  is_active: boolean;
}

// Section Creation Request
export interface SectionCreateRequest {
  title: string;  // Changed from name to title to match backend
  description: string;
  order: number;
  repeatable: boolean;
}

// Question Creation Request
export interface QuestionCreateRequest {
  section_id: string;
  type: QuestionType | string; // Accept both enum and string
  label: string;
  help_text?: string;
  required: boolean;
  core_field?: CoreFieldType;
  options?: QuestionOption[];
  validation?: ValidationRule;
  visibility_condition?: VisibilityCondition;
  repeat_section_id?: string;
  max_repeats?: number;
  order: number;
}

/**
 * FormState interface for tracking form structure in memory
 *
 * This interface is used to track the form structure during creation and editing.
 * It maintains the parent-child relationships between form, sections, and questions.
 */
export interface FormState {
  _id?: string;
  id?: string;
  name: string;
  description: string;
  status: "draft" | "active";
  sections: {
    _id?: string;
    id?: string;
    form_id?: string;
    title: string;
    description: string;
    order: number;
    repeatable: boolean;
    questions: {
      _id?: string;
      id?: string;
      section_id?: string;
      label: string;
      type: QuestionType;
      help_text?: string;
      required: boolean;
      core_field?: CoreFieldType;
      options?: QuestionOption[];
      validation?: ValidationRule;
      visibility_condition?: VisibilityCondition;
      repeat_section_id?: string;
      max_repeats?: number;
      order: number;
    }[];
  }[];
  default_section_ids?: string[];
  is_active?: boolean;
  version?: number;
  created_at?: number;
  updated_at?: number;
}

// Question Type Metadata for UI
export interface QuestionTypeInfo {
  type: QuestionType;
  label: string;
  description: string;
  icon: string;
  hasOptions: boolean;
  supportsValidation: boolean;
  supportsVisibility: boolean;
}

// Predefined question type configurations
export const QUESTION_TYPES: QuestionTypeInfo[] = [
  {
    type: QuestionType.SHORT_TEXT,
    label: "Short Text",
    description: "Short Text",
    icon: "Type",
    hasOptions: false,
    supportsValidation: true,
    supportsVisibility: true
  },
  {
    type: QuestionType.LONG_TEXT,
    label: "Long Text",
    description: "Multi-line text area",
    icon: "AlignLeft",
    hasOptions: false,
    supportsValidation: true,
    supportsVisibility: true
  },
  {
    type: QuestionType.NUMBER,
    label: "Number",
    description: "Numeric input",
    icon: "Hash",
    hasOptions: false,
    supportsValidation: true,
    supportsVisibility: true
  },
  {
    type: QuestionType.RANGE,
    label: "Range",
    description: "Range slider input",
    icon: "Minus",
    hasOptions: false,
    supportsValidation: true,
    supportsVisibility: true
  },
  {
    type: QuestionType.SINGLE_SELECT,
    label: "Single Select",
    description: "Choose one option",
    icon: "Circle",
    hasOptions: true,
    supportsValidation: true,
    supportsVisibility: true
  },
  {
    type: QuestionType.MULTI_SELECT,
    label: "Multi Select",
    description: "Choose multiple options",
    icon: "CheckSquare",
    hasOptions: true,
    supportsValidation: true,
    supportsVisibility: true
  },
  {
    type: QuestionType.BOOLEAN,
    label: "Yes/No",
    description: "Boolean toggle",
    icon: "ToggleLeft",
    hasOptions: false,
    supportsValidation: false,
    supportsVisibility: true
  },
  {
    type: QuestionType.FILE,
    label: "File Upload",
    description: "File attachment",
    icon: "Upload",
    hasOptions: false,
    supportsValidation: true,
    supportsVisibility: true
  },
  {
    type: QuestionType.DATE,
    label: "Date",
    description: "Date picker",
    icon: "Calendar",
    hasOptions: false,
    supportsValidation: true,
    supportsVisibility: true
  }
];

// Helper functions
export function getQuestionTypeInfo(type: QuestionType): QuestionTypeInfo | undefined {
  return QUESTION_TYPES.find(t => t.type === type);
}

export function getQuestionById(form: FormWithDetails, questionId: string): Question | undefined {
  for (const section of form.sections) {
    const question = section.questions.find(q => (q._id || q.id) === questionId);
    if (question) return question;
  }
  return undefined;
}

export function getSectionById(form: FormWithDetails, sectionId: string): SectionWithQuestions | undefined {
  return form.sections.find(s => (s._id || s.id) === sectionId);
}

export function toApiQuestionType(type: string): string {
  // Convert to snake_case for API
  return type.trim().toLowerCase().replace(/\s+/g, '_');
}

export function toDisplayQuestionType(type: string): string {
  // Convert snake_case or kebab-case to Capitalized
  return type
    .replace(/[_-]/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase())
    .trim();
}

// Utility function to check if a question is a core field
export function isCoreFieldQuestion(question: Question): boolean {
  return question.core_field !== undefined && question.core_field !== null;
}

// Utility function to get core field display name
export function getCoreFieldDisplayName(coreField: CoreFieldType): string {
  switch (coreField) {
    case CoreFieldType.COMPANY_NAME:
      return "Company Name";
    case CoreFieldType.STAGE:
      return "Stage";
    case CoreFieldType.SECTOR:
      return "Sector";
    default:
      return "Core Field";
  }
}
