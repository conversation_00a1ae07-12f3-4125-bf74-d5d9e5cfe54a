"""
Queue service implementation.

This module provides the main queue service implementation that
wraps a queue backend and provides a high-level API for working with jobs.
"""

import time
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from app.core.logging import get_logger
from app.models.queue import Job, JobMetadata, JobPriority, JobStatus, QueueType
from app.services.queue.interfaces import QueueBackendInterface, QueueServiceInterface

logger = get_logger(__name__)


class QueueService(QueueServiceInterface):
    """Implementation of the queue service interface."""

    def __init__(self, backend: QueueBackendInterface):
        """
        Initialize the queue service.

        Args:
            backend: Queue backend implementation
        """
        self.backend = backend

    async def initialize(self) -> None:
        """Initialize the queue service."""
        await self.backend.initialize()

    async def cleanup(self) -> None:
        """Clean up resources used by the queue service."""
        await self.backend.cleanup()

    async def enqueue_job(
        self,
        job_type: str,
        payload: Dict[str, Any],
        queue_type: QueueType = QueueType.DEFAULT,
        priority: JobPriority = JobPriority.NORMAL,
        job_id: Optional[str] = None,
        delay_seconds: int = 0,
        retry_config: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Job:
        """Add a job to the queue."""
        # Create job ID if not provided
        if not job_id:
            job_id = str(uuid.uuid4())

        # Create job metadata
        job_metadata = JobMetadata(**(metadata or {}))

        # Create job
        job = Job(
            id=job_id,
            type=job_type,
            payload=payload,
            queue=queue_type,
            priority=priority,
            metadata=job_metadata,
        )

        # Apply retry configuration if provided
        if retry_config:
            for key, value in retry_config.items():
                if hasattr(job, key):
                    setattr(job, key, value)

        # Handle delay
        if delay_seconds > 0:
            job.scheduled_for = int(time.time() * 1000) + (delay_seconds * 1000)

        # Enqueue job using backend
        return await self.backend.enqueue(job)

    async def schedule_job(
        self,
        job_type: str,
        payload: Dict[str, Any],
        scheduled_time: Union[datetime, int],
        job_id: Optional[str] = None,
        retry_config: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Job:
        """Schedule a job to run at a specific time."""
        # Create job ID if not provided
        if not job_id:
            job_id = str(uuid.uuid4())

        # Create job metadata
        job_metadata = JobMetadata(**(metadata or {}))

        # Convert scheduled time to timestamp if needed
        if isinstance(scheduled_time, datetime):
            scheduled_for = int(scheduled_time.timestamp() * 1000)
        else:
            scheduled_for = scheduled_time

        # Create job
        job = Job(
            id=job_id,
            type=job_type,
            payload=payload,
            queue=QueueType.SCHEDULED,
            scheduled_for=scheduled_for,
            metadata=job_metadata,
        )

        # Apply retry configuration if provided
        if retry_config:
            for key, value in retry_config.items():
                if hasattr(job, key):
                    setattr(job, key, value)

        # Enqueue job using backend
        return await self.backend.enqueue(job)

    async def get_backend(self) -> QueueBackendInterface:
        """Get the queue backend implementation."""
        return self.backend

    # Delegate all other operations to the backend
    async def get_job(self, job_id: str) -> Optional[Job]:
        """Get job details by ID."""
        return await self.backend.get_job(job_id)

    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a pending or scheduled job."""
        return await self.backend.cancel(job_id)

    async def retry_job(self, job_id: str, delay_seconds: int = 0) -> bool:
        """Retry a failed job."""
        return await self.backend.retry(job_id, delay_seconds)

    async def purge_queue(
        self, queue_type: QueueType, older_than: Optional[Union[datetime, int]] = None
    ) -> int:
        """Remove all jobs from a queue."""
        return await self.backend.purge(queue_type, older_than)

    async def list_jobs(
        self,
        queue_type: QueueType = QueueType.DEFAULT,
        status: Optional[JobStatus] = None,
        job_type: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Job]:
        """List jobs in a queue."""
        return await self.backend.list_jobs(queue_type, status, job_type, limit, offset)

    async def get_queue_stats(self, queue_type: Optional[QueueType] = None):
        """Get statistics about the queue(s)."""
        return await self.backend.get_stats(queue_type)

    async def process_scheduled_jobs(self) -> int:
        """Process scheduled jobs that are due."""
        return await self.backend.process_scheduled_jobs()

    async def process_stalled_jobs(self) -> int:
        """Process stalled jobs (jobs that have been processing for too long)."""
        return await self.backend.process_stalled_jobs()
