"use client"

import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Briefcase, FileTex<PERSON>, User<PERSON>he<PERSON>, Filter, ArrowUpRight } from 'lucide-react'

import { cn } from '@/lib/utils'

interface SummaryCardProps {
  icon: React.ComponentType<any>
  title: string
  count: number
  subtitle: string
  href: string
}

interface PremiumSummaryTilesProps {
  dashboardData: any
  className?: string
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
}

const tileVariants = {
  hidden: { 
    opacity: 0, 
    y: 10,
    scale: 0.98
  },
  visible: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.4,
      ease: [0.25, 0.46, 0.45, 0.94] as const
    }
  }
}

function SummaryCard({ icon: Icon, title, count, subtitle, href }: SummaryCardProps) {
  const router = useRouter()

  const handleClick = () => {
    router.push(href)
  }

  return (
    <motion.div
      variants={tileVariants}
      className="group cursor-pointer"
    >
      <div 
        className="rounded-2xl bg-white dark:bg-[#0B0F19] p-6 shadow-sm flex flex-col justify-between hover:shadow-md transition-shadow border border-neutral-200 dark:border-neutral-800"
        onClick={handleClick}
      >
        {/* Top section: Icon and CTA */}
        <div className="flex justify-between items-start mb-4">
          <div className="text-muted-foreground">
            <Icon className="h-5 w-5 text-neutral-600 dark:text-neutral-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200" />
          </div>
          <ArrowUpRight className="h-4 w-4 text-muted-foreground group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200" />
        </div>

        {/* Bottom section: Count, Title, Subtitle */}
        <div className="flex flex-col gap-1">
          <div className="text-3xl font-semibold text-neutral-900 dark:text-white">
            {count.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">
            {title}
          </div>
          <div className="text-xs text-muted-foreground mt-1">
            {subtitle}
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export function PremiumSummaryTiles({ dashboardData, className }: PremiumSummaryTilesProps) {
  // Extract data from insights
  const insightsData = dashboardData?.insights || {}
  
  // Calculate totals
  const totalDeals = insightsData.deal_funnel_breakdown?.reduce((sum: number, item: any) => sum + item.count, 0) || 0
  const totalExclusions = insightsData.exclusion_filters_triggered ? 
    Object.values(insightsData.exclusion_filters_triggered).reduce((sum: number, count: any) => sum + count, 0) : 0
  const formsCount = insightsData.forms_count || 0
  const dealsAssignedToMe = insightsData.deals_assigned_to_me || 0

  const tiles = [
    {
      icon: Briefcase,
      title: "Total Deals",
      count: totalDeals,
      subtitle: "Pipeline volume",
      href: "/deals?sort_by=updated_at&sort_order=desc&favourites_only=false"
    },
    {
      icon: FileText,
      title: "Forms",
      count: formsCount,
      subtitle: "Active forms",
      href: "/forms"
    },
    {
      icon: UserCheck,
      title: "Assigned to Me",
      count: dealsAssignedToMe,
      subtitle: "My deals",
      href: "/deals?assigned_to_me=true&sort_by=updated_at&sort_order=desc&favourites_only=false"
    },
    {
      icon: Filter,
      title: "Exclusions",
      count: totalExclusions,
      subtitle: "Filtered out",
      href: "/deals?status=excluded&assigned_to_me=false&sort_by=updated_at&sort_order=desc&favourites_only=false"
    }
  ]

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn(
        "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",
        className
      )}
    >
      {tiles.map((tile, index) => (
        <SummaryCard
          key={tile.title}
          icon={tile.icon}
          title={tile.title}
          count={tile.count}
          subtitle={tile.subtitle}
          href={tile.href}
        />
      ))}
    </motion.div>
  )
} 