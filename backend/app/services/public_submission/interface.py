from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from app.models.public_submission import PublicSubmission, PublicSubmissionAccess


class PublicSubmissionServiceInterface(ABC):
    """Interface for public submission services."""

    @abstractmethod
    async def get_or_create_submission(
        self,
        public_user_id: str,
        public_user_email: str,
        sharing_token: str,
        form_id: str,
        org_id: str,
        token_expires_at: Optional[int] = None,
    ) -> PublicSubmission:
        """Get existing submission or create new one for public user + token."""
        pass

    @abstractmethod
    async def get_submission_by_token_and_email(
        self, sharing_token: str, public_user_email: str
    ) -> Optional[PublicSubmission]:
        """Get submission by sharing token and public user email."""
        pass

    @abstractmethod
    async def update_submission_progress(
        self,
        submission_id: str,
        answers: Dict[str, Any],
        progress: float,
        last_question: Optional[str] = None,
    ) -> Optional[PublicSubmission]:
        """Update the progress of a submission."""
        pass

    @abstractmethod
    async def finalize_submission(
        self, public_submission_id: str, final_submission_id: str
    ) -> Optional[PublicSubmission]:
        """Mark a public submission as finalized with the actual submission ID."""
        pass

    @abstractmethod
    async def validate_submission_access(
        self, sharing_token: str, public_user_email: str
    ) -> Dict[str, Any]:
        """Validate if a public user can access a submission via token."""
        pass

    @abstractmethod
    async def track_access(
        self,
        public_submission_id: str,
        public_user_email: str,
        sharing_token: str,
        access_type: str,
        success: bool,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        error_message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> PublicSubmissionAccess:
        """Track access to a public submission."""
        pass

    @abstractmethod
    async def get_submission_by_id(
        self, submission_id: str
    ) -> Optional[PublicSubmission]:
        """Get public submission by ID."""
        pass

    @abstractmethod
    async def list_submissions_by_user(
        self, public_user_email: str, skip: int = 0, limit: int = 100
    ) -> List[PublicSubmission]:
        """List all submissions by a public user."""
        pass

    @abstractmethod
    async def list_submissions_by_token(
        self, sharing_token: str, skip: int = 0, limit: int = 100
    ) -> List[PublicSubmission]:
        """List all submissions for a sharing token."""
        pass

    @abstractmethod
    async def cleanup_expired_submissions(self) -> int:
        """Clean up expired submissions and return count of cleaned up items."""
        pass
