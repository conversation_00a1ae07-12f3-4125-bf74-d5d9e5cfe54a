"""
Vector search and embedding tools for TractionX AI Agent system.
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from ai.tools.base import BaseTool, VectorTool, ToolInput, ToolOutput
from ai.models import AgentContext


class VectorSearchInput(ToolInput):
    """Input for vector search tool."""
    query_text: str = Field(..., description="Text query to search for")
    filters: Optional[Dict[str, Any]] = Field(None, description="Filters to apply")
    limit: int = Field(default=10, description="Maximum number of results")
    similarity_threshold: float = Field(default=0.7, description="Minimum similarity threshold")


class VectorSearchOutput(ToolOutput):
    """Output for vector search tool."""
    results: Optional[List[Dict[str, Any]]] = Field(None, description="Search results")
    query_embedding: Optional[List[float]] = Field(None, description="Query embedding vector")
    total_results: Optional[int] = Field(None, description="Total number of results")


class VectorSearchTool(VectorTool):
    """Tool for searching vectors in org-specific collections."""
    
    def __init__(self):
        super().__init__(
            name="vector_search",
            description="Searches for similar content using vector embeddings"
        )
    
    async def execute(
        self,
        input_data: VectorSearchInput,
        context: AgentContext
    ) -> VectorSearchOutput:
        """Execute vector search."""
        
        try:
            # Generate embedding for query
            query_embedding = await self._get_embedding(input_data.query_text)
            
            # Search vectors
            search_results = await self._search_vectors(
                query_vector=query_embedding,
                context=context,
                filters=input_data.filters,
                limit=input_data.limit
            )
            
            # Filter by similarity threshold
            filtered_results = [
                result for result in search_results
                if result.get("score", 0) >= input_data.similarity_threshold
            ]
            
            return VectorSearchOutput(
                success=True,
                data={
                    "results": filtered_results,
                    "query": input_data.query_text,
                    "filters": input_data.filters
                },
                results=filtered_results,
                query_embedding=query_embedding,
                total_results=len(filtered_results),
                metadata={
                    "query_text": input_data.query_text,
                    "org_id": str(context.org_id),
                    "similarity_threshold": input_data.similarity_threshold
                }
            )
            
        except Exception as e:
            return await self._handle_error(e, input_data)


class EmbeddingGeneratorInput(ToolInput):
    """Input for embedding generator tool."""
    texts: List[str] = Field(..., description="Texts to generate embeddings for")
    model: str = Field(default="text-embedding-3-small", description="Embedding model to use")
    batch_size: int = Field(default=100, description="Batch size for processing")


class EmbeddingGeneratorOutput(ToolOutput):
    """Output for embedding generator tool."""
    embeddings: Optional[List[List[float]]] = Field(None, description="Generated embeddings")
    model_used: Optional[str] = Field(None, description="Model used for embeddings")
    token_usage: Optional[Dict[str, int]] = Field(None, description="Token usage statistics")


class EmbeddingGeneratorTool(VectorTool):
    """Tool for generating text embeddings."""
    
    def __init__(self):
        super().__init__(
            name="embedding_generator",
            description="Generates embeddings for text data using OpenAI models"
        )
    
    async def execute(
        self,
        input_data: EmbeddingGeneratorInput,
        context: AgentContext
    ) -> EmbeddingGeneratorOutput:
        """Execute embedding generation."""
        
        try:
            embeddings = []
            total_tokens = 0
            
            # Process texts in batches
            for i in range(0, len(input_data.texts), input_data.batch_size):
                batch = input_data.texts[i:i + input_data.batch_size]
                
                for text in batch:
                    embedding = await self._get_embedding(text)
                    embeddings.append(embedding)
                    # TODO: Track actual token usage
                    total_tokens += len(text.split())
            
            return EmbeddingGeneratorOutput(
                success=True,
                data={
                    "embeddings": embeddings,
                    "model": input_data.model,
                    "token_usage": {"total_tokens": total_tokens}
                },
                embeddings=embeddings,
                model_used=input_data.model,
                token_usage={"total_tokens": total_tokens},
                metadata={
                    "texts_processed": len(input_data.texts),
                    "batch_size": input_data.batch_size
                }
            )
            
        except Exception as e:
            return await self._handle_error(e, input_data)


class VectorUpsertInput(ToolInput):
    """Input for vector upsert tool."""
    vectors: List[Dict[str, Any]] = Field(..., description="Vectors to upsert")
    collection_suffix: Optional[str] = Field(None, description="Collection suffix (optional)")


class VectorUpsertOutput(ToolOutput):
    """Output for vector upsert tool."""
    upserted_count: Optional[int] = Field(None, description="Number of vectors upserted")
    collection_name: Optional[str] = Field(None, description="Collection name used")


class VectorUpsertTool(VectorTool):
    """Tool for upserting vectors to org-specific collections."""
    
    def __init__(self):
        super().__init__(
            name="vector_upsert",
            description="Upserts vectors to org-specific vector collections"
        )
    
    async def execute(
        self,
        input_data: VectorUpsertInput,
        context: AgentContext
    ) -> VectorUpsertOutput:
        """Execute vector upsert."""
        
        try:
            from ai.context import qdrant_helper
            
            # Upsert vectors
            success = await qdrant_helper.upsert_vectors(
                org_id=str(context.org_id),
                vectors=input_data.vectors
            )
            
            if not success:
                return VectorUpsertOutput(
                    success=False,
                    error="Failed to upsert vectors"
                )
            
            collection_name = f"org_{context.org_id}_vectors"
            if input_data.collection_suffix:
                collection_name += f"_{input_data.collection_suffix}"
            
            return VectorUpsertOutput(
                success=True,
                data={
                    "upserted_count": len(input_data.vectors),
                    "collection_name": collection_name
                },
                upserted_count=len(input_data.vectors),
                collection_name=collection_name,
                metadata={
                    "org_id": str(context.org_id),
                    "vectors_count": len(input_data.vectors)
                }
            )
            
        except Exception as e:
            return await self._handle_error(e, input_data)


class SimilaritySearchInput(ToolInput):
    """Input for similarity search tool."""
    reference_text: str = Field(..., description="Reference text to find similar content")
    content_types: Optional[List[str]] = Field(None, description="Content types to search")
    date_range: Optional[Dict[str, str]] = Field(None, description="Date range filter")
    limit: int = Field(default=5, description="Maximum number of results")


class SimilaritySearchOutput(ToolOutput):
    """Output for similarity search tool."""
    similar_content: Optional[List[Dict[str, Any]]] = Field(None, description="Similar content found")
    similarity_scores: Optional[List[float]] = Field(None, description="Similarity scores")
    content_summaries: Optional[List[str]] = Field(None, description="Content summaries")


class SimilaritySearchTool(VectorTool):
    """Tool for finding similar content using semantic search."""
    
    def __init__(self):
        super().__init__(
            name="similarity_search",
            description="Finds semantically similar content using vector embeddings"
        )
    
    async def execute(
        self,
        input_data: SimilaritySearchInput,
        context: AgentContext
    ) -> SimilaritySearchOutput:
        """Execute similarity search."""
        
        try:
            # Generate embedding for reference text
            reference_embedding = await self._get_embedding(input_data.reference_text)
            
            # Build filters
            filters = {}
            if input_data.content_types:
                filters["content_type"] = input_data.content_types
            if input_data.date_range:
                filters.update(input_data.date_range)
            
            # Search for similar vectors
            search_results = await self._search_vectors(
                query_vector=reference_embedding,
                context=context,
                filters=filters,
                limit=input_data.limit
            )
            
            # Extract content and scores
            similar_content = []
            similarity_scores = []
            content_summaries = []
            
            for result in search_results:
                similar_content.append(result.get("payload", {}))
                similarity_scores.append(result.get("score", 0))
                
                # Generate summary
                content = result.get("payload", {}).get("content", "")
                summary = content[:200] + "..." if len(content) > 200 else content
                content_summaries.append(summary)
            
            return SimilaritySearchOutput(
                success=True,
                data={
                    "similar_content": similar_content,
                    "reference_text": input_data.reference_text,
                    "filters": filters
                },
                similar_content=similar_content,
                similarity_scores=similarity_scores,
                content_summaries=content_summaries,
                metadata={
                    "reference_text": input_data.reference_text[:100],
                    "results_count": len(similar_content),
                    "org_id": str(context.org_id)
                }
            )
            
        except Exception as e:
            return await self._handle_error(e, input_data)
