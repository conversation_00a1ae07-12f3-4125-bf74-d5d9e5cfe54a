/**
 * Form API Integration
 *
 * This file contains API integration for the Form Builder feature.
 *
 * The API follows a parent-child relationship:
 * Form -> Section -> Question
 *
 * Each child entity must reference its parent's _id to maintain proper relationships.
 */

import apiClient from '@/lib/api-client';
import {
  Form,
  FormWithDetails,
  FormCreateRequest,
  Section,
  SectionCreateRequest,
  Question,
  QuestionCreateRequest,
  FormState,
  QuestionType
} from '@/lib/types/form';

/**
 * Form API service for interacting with the backend
 */
export const FormAPI = {
  /**
   * Create a new form
   * @param formData Form creation data
   * @returns Created form with _id
   */
  async createForm(formData: FormCreateRequest): Promise<Form> {
    console.log('Creating form with data:', formData);

    // Validate required fields
    if (!formData.name || !formData.description) {
      throw new Error('Form name and description are required');
    }

    const response = await apiClient.post('/forms', formData);

    // Validate response contains _id
    if (!response.data._id && !response.data.id) {
      console.error('Form creation response missing _id:', response.data);
      throw new Error('Form creation failed: Missing form ID in response');
    }

    console.log('Form created with ID:', response.data._id || response.data.id);
    return response.data;
  },

  /**
   * Get a form by ID
   * @param formId Form ID
   * @returns Form with details
   */
  async getForm(formId: string): Promise<Form> {
    console.log(`Fetching form with ID: ${formId}`);
    const response = await apiClient.get(`/forms/${formId}`);
    console.log('Form fetched:', response.data);
    return response.data;
  },

  /**
   * List all forms
   * @returns List of forms
   */
  async listForms(): Promise<Form[]> {
    console.log('Fetching all forms');
    const response = await apiClient.get('/forms');
    console.log('Forms fetched:', response.data);
    return response.data;
  },

  /**
   * Update a form's basic information
   * @param formId Form ID
   * @param formData Form update data
   * @returns Updated form
   */
  async updateForm(formId: string, formData: FormCreateRequest): Promise<Form> {
    console.log(`Updating form ${formId} with data:`, formData);
    const response = await apiClient.put(`/forms/${formId}`, formData);
    console.log('Form updated:', response.data);
    return response.data;
  },

  /**
   * Delete a form
   * @param formId Form ID
   * @returns Success message
   */
  async deleteForm(formId: string): Promise<{ message: string }> {
    console.log(`Deleting form with ID: ${formId}`);
    const response = await apiClient.delete(`/forms/${formId}`);
    console.log('Form deleted:', response.data);
    return response.data;
  },

  /**
   * Create a section in a form
   * @param formId Form ID
   * @param sectionData Section creation data
   * @returns Created section with _id
   */
  async createSection(formId: string, sectionData: SectionCreateRequest): Promise<Section> {
    console.log(`Creating section in form ${formId} with data:`, sectionData);

    // Validate form ID
    if (!formId) {
      throw new Error('Form ID is required to create a section');
    }

    // Validate required fields
    if (!sectionData.title) {
      throw new Error('Section title is required');
    }

    const response = await apiClient.post(`/forms/${formId}/sections`, sectionData);

    // Validate response contains _id
    if (!response.data._id && !response.data.id) {
      console.error('Section creation response missing _id:', response.data);
      throw new Error('Section creation failed: Missing section ID in response');
    }

    // Ensure the section has the form_id property
    const section = response.data;
    if (!section.form_id) {
      section.form_id = formId;
    }

    console.log('Section created with ID:', section._id || section.id);
    return section;
  },

  /**
   * Update a section
   * @param sectionId Section ID
   * @param sectionData Section update data
   * @returns Updated section
   */
  async updateSection(sectionId: string, sectionData: SectionCreateRequest): Promise<Section> {
    console.log(`Updating section ${sectionId} with data:`, sectionData);

    if (!sectionId) {
      console.error('Section ID is required for update');
      throw new Error('Section ID is required for update');
    }

    // Validate required fields
    if (!sectionData.title) {
      console.error('Section title is required');
      throw new Error('Section title is required');
    }

    try {
      // Log the full URL for debugging
      const url = `/forms/sections/${sectionId}`;
      console.log(`Making PUT request to: ${url}`);
      console.log('With data:', JSON.stringify(sectionData, null, 2));

      // Use PUT for section updates
      const response = await apiClient.put(url, sectionData);
      console.log('Section updated successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Error updating section ${sectionId}:`, error);

      // Log more details about the error
      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
        console.error('Error response headers:', error.response.headers);
      } else if (error.request) {
        console.error('Error request:', error.request);
      } else {
        console.error('Error message:', error.message);
      }

      // Log the API client configuration
      console.log('API client base URL:', apiClient.defaults.baseURL);
      console.log('API client headers:', apiClient.defaults.headers);

      throw error;
    }
  },

  /**
   * Delete a section
   * @param sectionId Section ID
   * @returns Success message
   */
  async deleteSection(sectionId: string): Promise<{ message: string }> {
    console.log(`Deleting section with ID: ${sectionId}`);
    const response = await apiClient.delete(`/forms/sections/${sectionId}`);
    console.log('Section deleted:', response.data);
    return response.data;
  },

  /**
   * Create a question in a section
   * @param sectionId Section ID
   * @param questionData Question creation data
   * @returns Full form with details after creating the question
   */
  async createQuestion(sectionId: string, questionData: QuestionCreateRequest): Promise<FormWithDetails> {
    console.log(`➕ FormAPI.createQuestion called for section ${sectionId}`);
    console.log('📥 Input questionData:', JSON.stringify(questionData, null, 2));

    if (!sectionId) {
      throw new Error('Section ID is required to create a question');
    }
    if (!questionData.label || !questionData.type) {
      throw new Error('Question label and type are required');
    }

    console.log('🏷️ Question type for creation:', questionData.type);
    console.log('🔍 Type of questionType:', typeof questionData.type);

    const requestData = {
      ...questionData,
      type: questionData.type, // Always a string
    };

    console.log('📤 Initial requestData:', JSON.stringify(requestData, null, 2));
    // Use string comparison for select types
    if (requestData.type === 'single_select' || requestData.type === 'multi_select') {
      console.log('Processing options for select question type:', requestData.type);
      if (!requestData.options || !Array.isArray(requestData.options)) {
        requestData.options = [];
      }
      requestData.options = requestData.options
        .filter(opt => opt && typeof opt === 'object')
        .map(opt => ({
          label: opt.label || '',
          value: opt.value || opt.label?.toLowerCase().replace(/\s+/g, '_') || ''
        }))
        .filter(opt => opt.label && opt.value);
      console.log('Processed options for API request:', requestData.options);
      if (requestData.options.length === 0) {
        const defaultOption = { label: 'Default Option', value: 'default_option' };
        requestData.options = [defaultOption];
        console.log('Added default option for empty select question:', defaultOption);
      }
    } else {
      requestData.options = undefined;
    }
    Object.assign(questionData, requestData);
    const questionWithSectionId = {
      ...questionData,
      section_id: sectionId,
    };

    console.log('🌐 About to make API call to POST /forms/sections/' + sectionId + '/questions');
    console.log('📦 Final payload being sent:', JSON.stringify(questionWithSectionId, null, 2));

    const response = await apiClient.post(`/forms/sections/${sectionId}/questions`, questionWithSectionId);
    console.log('✅ API Response received (full form):', response.data);

    // The response now contains the full form with details
    if (!response.data._id && !response.data.id) {
      console.error('Form response missing _id:', response.data);
      throw new Error('Question creation failed: Missing form ID in response');
    }
    
    console.log('Question created successfully, returning full form with ID:', response.data._id || response.data.id);
    return response.data;
  },

  /**
   * Update a question
   * @param questionId Question ID
   * @param questionData Question update data
   * @returns Updated question
   */
  async updateQuestion(questionId: string, questionData: QuestionCreateRequest): Promise<Question> {
    console.log(`🔄 FormAPI.updateQuestion called for ${questionId}`);
    console.log('📥 Input questionData:', JSON.stringify(questionData, null, 2));

    if (!questionId) {
      console.error('Question ID is required for update');
      throw new Error('Question ID is required for update');
    }

    const questionType = questionData.type;
    console.log('🏷️ Question type for update:', questionType);
    console.log('🔍 Type of questionType:', typeof questionType);

    const requestData = {
      ...questionData,
      type: questionType, // Always a string
    };

    console.log('📤 Final requestData being sent to API:', JSON.stringify(requestData, null, 2));
    // Use string comparison for select types
    if (questionType === 'single_select' || questionType === 'multi_select') {
      console.log('Processing options for select question type (update):', questionType);
      if (!requestData.options || !Array.isArray(requestData.options)) {
        requestData.options = [];
      }
      requestData.options = requestData.options
        .filter(opt => opt && typeof opt === 'object')
        .map(opt => ({
          label: opt.label || '',
          value: opt.value || opt.label?.toLowerCase().replace(/\s+/g, '_') || ''
        }))
        .filter(opt => opt.label && opt.value);
      console.log('Processed options for API request (update):', requestData.options);
      if (requestData.options.length === 0) {
        const defaultOption = { label: 'Default Option', value: 'default_option' };
        requestData.options = [defaultOption];
        console.log('Added default option for empty select question (update):', defaultOption);
      }
    } else {
      requestData.options = undefined;
    }
    Object.assign(questionData, requestData);

    console.log('🌐 About to make API call to PUT /forms/questions/' + questionId);
    console.log('📦 Final payload being sent:', JSON.stringify(questionData, null, 2));

    try {
      const response = await apiClient.put(`/forms/questions/${questionId}`, questionData);
      console.log('✅ API Response received:', response.data);
      console.log('🏷️ Response question type:', response.data.type);
      return response.data;
    } catch (error) {
      console.error(`❌ Error updating question ${questionId}:`, error);
      if (error.response) {
        console.error('📥 Error response data:', error.response.data);
        console.error('📊 Error status:', error.response.status);
      } else if (error.request) {
        console.error('📤 Error request:', error.request);
      } else {
        console.error('💬 Error message:', error.message);
      }
      throw error;
    }
  },

  /**
   * Delete a question
   * @param questionId Question ID
   * @returns Success message
   */
  async deleteQuestion(questionId: string): Promise<{ message: string }> {
    console.log(`Deleting question with ID: ${questionId}`);
    const response = await apiClient.delete(`/forms/questions/${questionId}`);
    console.log('Question deleted:', response.data);
    return response.data;
  },

  /**
   * Get a form with all its sections and questions
   * @param formId Form ID
   * @returns Form with sections and questions
   */
  async getFormWithDetails(formId: string): Promise<Form> {
    console.log(`Fetching form details for ID: ${formId}`);

    // Use the /details endpoint to get the form with all sections and questions in a single request
    const response = await apiClient.get(`/forms/${formId}/details`);
    console.log('Form details fetched:', response.data);

    // The response already contains the form with all sections and questions
    return response.data;
  },

  /**
   * Get form preview data (read-only, authenticated)
   * @param formId Form ID
   * @returns Form preview data matching shared form structure
   */
  async getFormPreview(formId: string): Promise<any> {
    console.log(`Fetching form preview for ID: ${formId}`);

    const response = await apiClient.get(`/forms/${formId}/preview`);
    console.log('Form preview fetched:', response.data);

    return response.data;
  },

  /**
   * Get sections for a form
   * @param formId Form ID
   * @returns List of sections
   */
  async getFormSections(formId: string): Promise<Section[]> {
    console.log(`Fetching sections for form ID: ${formId}`);
    const response = await apiClient.get(`/forms/${formId}/sections`);
    console.log('Sections fetched:', response.data);
    return response.data;
  },

  /**
   * Get questions for a section
   * @param sectionId Section ID
   * @returns List of questions
   */
  async getSectionQuestions(sectionId: string): Promise<Question[]> {
    console.log(`Fetching questions for section ID: ${sectionId}`);
    const response = await apiClient.get(`/forms/sections/${sectionId}/questions`);
    console.log('Questions fetched:', response.data);
    return response.data;
  },

  /**
   * @deprecated Use atomic APIs instead
   * Update a form comprehensively (all sections and questions)
   * @param formId Form ID
   * @param formData Complete form data
   * @returns Updated form
   */
  async updateFormComprehensive(formId: string, formData: Form | FormState): Promise<Form> {
    console.warn('The comprehensive update endpoint is deprecated. Use atomic APIs instead.');

    // Validate form ID
    if (!formId) {
      throw new Error('Form ID is required for comprehensive update');
    }

    // Ensure all sections have form_id set
    const formDataWithIds = {
      ...formData,
      sections: formData.sections.map(section => ({
        ...section,
        form_id: section.form_id || formId,
        // Ensure all questions have section_id set
        questions: section.questions.map(question => ({
          ...question,
          section_id: question.section_id || section._id || section.id,
        })),
      })),
    };

    // Use PUT for the comprehensive update
    const response = await apiClient.put(`/forms/${formId}/comprehensive`, formDataWithIds);

    console.log('Form updated comprehensively:', response.data);
    return response.data;
  },

  // Note: Individual question order updates are deprecated for drag-and-drop
  // Use reorderQuestions() for all drag-and-drop reordering operations

  async reorderSections(items: Array<{ id: string; order: number }>) {
    const response = await apiClient.post('/forms/sections/reorder', {
      items
    });
    return response.data;
  },

  async reorderQuestions(items: Array<{ id: string; order: number }>) {
    const response = await apiClient.post('/forms/questions/reorder', {
      items
    });
    return response.data;
  },
};

export default FormAPI;
