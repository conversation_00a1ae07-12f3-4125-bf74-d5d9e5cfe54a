"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Save, Target, Globe, Building, TrendingUp, Users } from "lucide-react"

import { Button } from "@/components/ui/button"
import { ThesisMultiSelect } from "./thesis-multi-select"
import { ThesisConfig } from "@/lib/api/settings-api"
import { cn } from "@/lib/utils"

interface OrgThesisFormProps {
  thesisConfig: ThesisConfig
  onUpdate: (data: ThesisConfig) => Promise<void>
}

// Default options for each field
const GEOGRAPHY_OPTIONS = [
  "Southeast Asia",
  "US",
  "Europe", 
  "India",
  "LATAM",
  "China",
  "Middle East",
  "Africa",
  "Australia",
  "Other"
]

const SECTOR_OPTIONS = [
  "AI",
  "Climate",
  "Fintech",
  "Healthcare",
  "EdTech",
  "E-commerce",
  "SaaS",
  "DeepTech",
  "Consumer",
  "Enterprise",
  "Biotech",
  "Mobility",
  "Other"
]

const STAGE_OPTIONS = [
  "Pre-seed",
  "Seed",
  "Series A",
  "Series B",
  "Series C",
  "Growth",
  "Late Stage",
  "Other"
]

const BUSINESS_MODEL_OPTIONS = [
  "B2B",
  "B2C",
  "B2B2C",
  "Marketplace",
  "Platform",
  "SaaS",
  "Hardware",
  "Services",
  "Other"
]

export function OrgThesisForm({ thesisConfig, onUpdate }: OrgThesisFormProps) {
  const [formData, setFormData] = useState<ThesisConfig>(thesisConfig)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isDirty, setIsDirty] = useState(false)

  // Update form data when thesisConfig prop changes
  useEffect(() => {
    setFormData(thesisConfig)
    setIsDirty(false)
  }, [thesisConfig])

  // Check if form is dirty
  useEffect(() => {
    const isFormDirty = JSON.stringify(formData) !== JSON.stringify(thesisConfig)
    setIsDirty(isFormDirty)
  }, [formData, thesisConfig])

  const handleFieldChange = (field: keyof ThesisConfig, values: string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: values
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!isDirty) return

    try {
      setIsSubmitting(true)
      await onUpdate(formData)
      setIsDirty(false)
    } catch (error) {
      console.error("Failed to update thesis config:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const fieldIcons = {
    geography: Globe,
    sector: Building,
    stage: TrendingUp,
    business_model: Users
  }

  const fieldLabels = {
    geography: "Geography",
    sector: "Sector",
    stage: "Stage", 
    business_model: "Business Model"
  }

  const fieldDescriptions = {
    geography: "Select the geographic regions you focus on",
    sector: "Choose the sectors and industries you invest in",
    stage: "Pick the investment stages you target",
    business_model: "Define the business models you prefer"
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="w-full"
    >
      <div className="space-y-8">
        {/* Header Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            {/* <div className="flex size-12 items-center justify-center rounded-xl bg-blue-50">
              <Target className="size-6 text-blue-600" />
            </div> */}
            <div>
              {/* <h3 className="text-2xl font-semibold tracking-tight text-gray-900">
                Investment Thesis Configuration
              </h3>
              <p className="mt-1 text-base text-gray-600">
                Configure your organization's investment preferences and criteria.
              </p> */}
            </div>
          </div>
        </div>
        {/* Form Section */}
        <form onSubmit={handleSubmit} className="space-y-8">
            {/* Geography Field */}
            <div className="space-y-3">
              <ThesisMultiSelect
                label={fieldLabels.geography}
                description={fieldDescriptions.geography}
                icon={fieldIcons.geography}
                options={GEOGRAPHY_OPTIONS}
                values={formData.geography}
                onChange={(values) => handleFieldChange('geography', values)}
                placeholder="Select geographic regions..."
              />
            </div>

            {/* Sector Field */}
            <div className="space-y-3">
              <ThesisMultiSelect
                label={fieldLabels.sector}
                description={fieldDescriptions.sector}
                icon={fieldIcons.sector}
                options={SECTOR_OPTIONS}
                values={formData.sector}
                onChange={(values) => handleFieldChange('sector', values)}
                placeholder="Select sectors and industries..."
              />
            </div>

            {/* Stage Field */}
            <div className="space-y-3">
              <ThesisMultiSelect
                label={fieldLabels.stage}
                description={fieldDescriptions.stage}
                icon={fieldIcons.stage}
                options={STAGE_OPTIONS}
                values={formData.stage}
                onChange={(values) => handleFieldChange('stage', values)}
                placeholder="Select investment stages..."
              />
            </div>

            {/* Business Model Field */}
            <div className="space-y-3">
              <ThesisMultiSelect
                label={fieldLabels.business_model}
                description={fieldDescriptions.business_model}
                icon={fieldIcons.business_model}
                options={BUSINESS_MODEL_OPTIONS}
                values={formData.business_model}
                onChange={(values) => handleFieldChange('business_model', values)}
                placeholder="Select business models..."
              />
            </div>

            {/* Save Button - Sticky on mobile */}
            <div className={cn(
              "sticky bottom-0 z-10 bg-white/80 backdrop-blur-md pt-6",
              "md:relative md:bg-transparent md:backdrop-blur-none md:pt-8"
            )}>
              <AnimatePresence>
                {isDirty && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Button
                      type="submit"
                      disabled={!isDirty || isSubmitting}
                      className={cn(
                        "w-full bg-gradient-to-r from-indigo-500 to-purple-500 text-white",
                        "hover:opacity-90 disabled:opacity-50",
                        "touch-target rounded-xl px-6 py-3 font-medium transition-all",
                        "md:w-auto"
                      )}
                    >
                      <motion.div
                        className="flex items-center gap-2"
                        animate={isSubmitting ? { scale: [1, 0.95, 1] } : {}}
                        transition={{ duration: 1, repeat: isSubmitting ? Infinity : 0 }}
                      >
                        <Save className="size-4" />
                        {isSubmitting ? "Saving Changes..." : "Save Changes"}
                      </motion.div>
                    </Button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </form>
        </div>
    </motion.div>
  )
}
