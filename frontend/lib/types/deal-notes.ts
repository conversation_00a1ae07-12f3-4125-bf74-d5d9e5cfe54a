/**
 * Deal Notes Types
 * 
 * TypeScript definitions for structured deal notes with Slate.js content
 * and user mentions system.
 */

import { BaseEditor, Descendant } from 'slate';
import { ReactEditor } from 'slate-react';
import { HistoryEditor } from 'slate-history';

// Slate.js Editor Types
export type CustomElement = ParagraphElement | MentionElement | BulletedListElement | NumberedListElement | ListItemElement;
export type CustomText = { text: string; bold?: boolean; italic?: boolean };

declare module 'slate' {
  interface CustomTypes {
    Editor: BaseEditor & ReactEditor & HistoryEditor;
    Element: CustomElement;
    Text: CustomText;
  }
}

// Content Structure
export type ParagraphElement = {
  type: 'paragraph';
  children: (CustomText | MentionElement)[];
};

export type MentionElement = {
  type: 'mention';
  user_id: string;
  name: string;
  children: [{ text: '' }];
};

export type BulletedListElement = {
  type: 'bulleted-list';
  children: ParagraphElement[];
};

export type NumberedListElement = {
  type: 'numbered-list';
  children: ParagraphElement[];
};

export type ListItemElement = {
  type: 'list-item';
  children: CustomText[];
};

export type NoteContent = CustomElement[];

// API Types
export interface DealNote {
  _id: string;
  deal_id: string;
  org_id: string;
  structured_content: NoteContent;
  tagged_user_ids: string[];
  pinned: boolean;
  created_by: {
    _id: string;
    name: string;
    email: string;
  };
  created_at: number;
  updated_at: number;
}

export interface DealNoteCreate {
  structured_content: NoteContent;
  tagged_user_ids?: string[];
  pinned?: boolean;
}

export interface DealNoteUpdate {
  structured_content?: NoteContent;
  tagged_user_ids?: string[];
  pinned?: boolean;
}

export interface DealNoteListResponse {
  notes: DealNote[];
  total: number;
  skip: number;
  limit: number;
  has_more: boolean;
}

export interface DealNoteCreateResponse {
  success: boolean;
  note_id: string;
  message: string;
}

export interface DealNoteDeleteResponse {
  success: boolean;
  message: string;
}

// User Types for Mentions
export interface OrgUser {
  _id: string;
  name: string;
  email: string;
  avatar_url?: string;
}

// Editor State Types
export interface MentionAutocompleteState {
  target: Range | null;
  search: string;
  index: number;
}

// Validation Types
export interface NoteValidationResult {
  isValid: boolean;
  errors: string[];
}

// Utility Types
export type MentionToken = {
  user_id: string;
  name: string;
  element: MentionElement;
};

export type ExtractedMentions = {
  user_ids: string[];
  tokens: MentionToken[];
}; 