import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronDown } from 'lucide-react';
import { ThesisStatus } from '@/lib/types/thesis';

interface ThesisStatusToggleProps {
  currentStatus: ThesisStatus;
  onStatusChange: (status: ThesisStatus) => void;
  disabled?: boolean;
}

const statusConfig = {
  [ThesisStatus.DRAFT]: {
    label: 'Draft',
    description: 'Thesis is in draft mode and not yet active',
    variant: 'secondary' as const,
  },
  [ThesisStatus.ACTIVE]: {
    label: 'Active',
    description: 'Thesis is active and being used for scoring',
    variant: 'active' as const,
  },
  [ThesisStatus.ARCHIVED]: {
    label: 'Archived',
    description: 'Thesis is archived and no longer in use',
    variant: 'destructive' as const,
  },
};

export function ThesisStatusToggle({ currentStatus, onStatusChange, disabled = false }: ThesisStatusToggleProps) {
  const config = statusConfig[currentStatus];

  const renderStatusBadge = (status: ThesisStatus) => {
    const statusInfo = statusConfig[status];
    
    if (status === ThesisStatus.ACTIVE) {
      return (
        <span className="inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-[#e0e7ef] via-[#f3f4f6] to-[#cbd5e1] text-[#22292f] text-xs font-medium shadow-sm backdrop-blur-sm border border-gray-200">
          {statusInfo.label.toUpperCase()}
        </span>
      );
    }
    
    return (
      <Badge className={`${statusInfo.variant} text-white`}>
        {statusInfo.label.toUpperCase()}
      </Badge>
    );
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild disabled={disabled}>
        <Button variant="outline" className="flex items-center gap-2">
          {renderStatusBadge(currentStatus)}
          <ChevronDown className="size-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {Object.entries(statusConfig).map(([status, { label, description }]) => (
          <DropdownMenuItem
            key={status}
            onClick={() => onStatusChange(status as ThesisStatus)}
            disabled={status === currentStatus || disabled}
            className="flex flex-col items-start gap-1"
          >
            <span className="font-medium">{label}</span>
            <span className="text-xs text-muted-foreground">{description}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 