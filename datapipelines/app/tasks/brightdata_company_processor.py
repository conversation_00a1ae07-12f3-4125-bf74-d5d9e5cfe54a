"""
BrightData Company Data Processor for TractionX Data Pipeline Service.

This module provides data processing for BrightData Crunchbase scraping results,
transforming them into normalized database records for company enrichment.
"""

from datetime import date, datetime, timezone
from typing import Any, Dict, List, Optional
from uuid import NAMESPACE_URL, uuid4, uuid5

from app.configs import get_logger
from app.models.crunchbase import (
    BrightDataCompanyData,
    BrightDataSnapshotRecord,
)
from app.storage.rds_storage import RDSStorage
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class BrightDataCompanyProcessor:
    """
    Comprehensive processor for BrightData company enrichment data.
    Handles cleaning, validation, and storage of Crunchbase scraping results.
    """

    def __init__(self, rds_storage: RDSStorage, s3_storage: S3Storage):
        self.rds = rds_storage
        self.s3 = s3_storage
        self.logger = get_logger(f"{__name__}.BrightDataCompanyProcessor")

    async def process_brightdata_company_payload(
        self, payload: Dict[str, Any], s3_raw_data_key: str
    ) -> Dict[str, Any]:
        """
        Process a complete BrightData company payload and store in normalized schema.

        Args:
            payload: Complete BrightData payload with brightdata_data and metadata
            s3_raw_data_key: S3 key for raw data backup

        Returns:
            Processing result with success status and details
        """
        try:
            brightdata_data = payload.get("brightdata_data")
            metadata = payload.get("metadata", {})

            # Validate required fields
            validation_result = self._validate_brightdata_company_data(
                brightdata_data, metadata
            )
            self.logger.info(f"Validation result: {validation_result}")
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": f"Validation failed: {validation_result['errors']}",
                    "company_id": metadata.get("company_id"),
                }

            # At this point, brightdata_data is validated and not None
            if not isinstance(brightdata_data, BrightDataCompanyData):
                return {
                    "success": False,
                    "error": "Invalid BrightData company data type",
                    "company_id": metadata.get("company_id"),
                }

            # Generate deterministic UUID
            company_uuid = self._generate_company_uuid(brightdata_data, metadata)

            # Clean and extract data
            cleaned_data = await self._clean_company_enrichment_data(
                brightdata_data, metadata, company_uuid, s3_raw_data_key
            )

            # Store in database with transaction
            await self._store_company_data(cleaned_data)

            self.logger.info(
                f"Successfully processed BrightData company data for company {company_uuid}"
            )

            return {
                "success": True,
                "company_uuid": company_uuid,
                "company_id": metadata.get("company_id"),
                "records_created": {
                    "company_profile": 1,
                    "founders": len(cleaned_data.get("founders", [])),
                    "executives": len(cleaned_data.get("executives", [])),
                    "technologies": len(cleaned_data.get("technologies", [])),
                    "keywords": len(cleaned_data.get("keywords", [])),
                    "competitors": len(cleaned_data.get("competitors", [])),
                },
            }

        except Exception as e:
            error_msg = f"BrightData company processing failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            # Store error for debugging
            await self._store_processing_error(payload, error_msg, s3_raw_data_key)

            return {
                "success": False,
                "error": error_msg,
                "company_id": metadata.get("company_id"),
            }

    async def process_brightdata_company_snapshot(
        self,
        snapshot_data: List[Dict[str, Any]],
        metadata: Dict[str, Any],
        s3_raw_data_key: str,
    ) -> Dict[str, Any]:
        """
        Process BrightData company snapshot data and store in normalized schema.

        Args:
            snapshot_data: Raw BrightData snapshot array
            metadata: Processing metadata
            s3_raw_data_key: S3 key for raw data backup

        Returns:
            Processing result with success status and details
        """
        try:
            if not snapshot_data or not isinstance(snapshot_data, list):
                return {
                    "success": False,
                    "error": "Invalid snapshot data format",
                    "company_id": metadata.get("company_id"),
                }

            # Take the first company profile from the snapshot
            company_data = snapshot_data[0]

            # Ensure required list fields have default values
            company_data = {
                **company_data,
                "founders": company_data.get("founders") or [],
                "executives": company_data.get("executives") or [],
                "technologies": company_data.get("technologies") or [],
                "keywords": company_data.get("keywords") or [],
                "competitors": company_data.get("competitors") or [],
            }

            # Convert to BrightDataSnapshotRecord
            snapshot_record = BrightDataSnapshotRecord(**company_data)

            # Generate deterministic UUID
            company_uuid = self._generate_company_uuid_from_snapshot(
                snapshot_record, metadata
            )

            # Clean and extract data
            cleaned_data = await self._clean_snapshot_company_data(
                snapshot_record, metadata, company_uuid, s3_raw_data_key
            )

            # Store in database with transaction
            await self._store_company_data(cleaned_data)

            # Determine data quality status
            data_quality = self._assess_company_data_quality(snapshot_record)

            self.logger.info(
                f"Successfully processed BrightData company snapshot for company {company_uuid}",
                data_quality=data_quality,
            )

            return {
                "success": True,
                "company_uuid": company_uuid,
                "company_id": metadata.get("company_id"),
                "records_created": {
                    "company_profile": 1,
                    "founders": len(cleaned_data.get("founders", [])),
                    "executives": len(cleaned_data.get("executives", [])),
                    "technologies": len(cleaned_data.get("technologies", [])),
                    "keywords": len(cleaned_data.get("keywords", [])),
                    "competitors": len(cleaned_data.get("competitors", [])),
                },
                "data_quality": data_quality,
            }

        except Exception as e:
            error_msg = f"BrightData company snapshot processing failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            return {
                "success": False,
                "error": error_msg,
                "company_id": metadata.get("company_id"),
            }

    def _validate_brightdata_company_data(
        self, brightdata_data: Optional[BrightDataCompanyData], metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate BrightData company data."""
        errors = []

        if not brightdata_data:
            errors.append("BrightData company data is missing")

        if not metadata.get("company_id"):
            errors.append("Company ID is missing in metadata")

        if not metadata.get("org_id"):
            errors.append("Organization ID is missing in metadata")

        return {
            "valid": len(errors) == 0,
            "errors": errors,
        }

    def _generate_company_uuid(
        self, brightdata_data: BrightDataCompanyData, metadata: Dict[str, Any]
    ) -> str:
        """Generate deterministic UUID for company."""
        # Use company domain and org_id for deterministic UUID
        company_domain = brightdata_data.domain or metadata.get("company_domain", "")
        org_id = metadata.get("org_id", "")

        # Create a deterministic seed
        seed_string = f"{org_id}:{company_domain}"

        # Generate UUID from seed
        return str(uuid5(NAMESPACE_URL, seed_string))

    def _generate_company_uuid_from_snapshot(
        self, snapshot_record: BrightDataSnapshotRecord, metadata: Dict[str, Any]
    ) -> str:
        """Generate deterministic UUID for company from snapshot."""
        company_domain = snapshot_record.domain or metadata.get("company_domain", "")
        org_id = metadata.get("org_id", "")

        # Create a deterministic seed
        seed_string = f"{org_id}:{company_domain}"

        # Generate UUID from seed
        return str(uuid5(NAMESPACE_URL, seed_string))

    async def _clean_company_enrichment_data(
        self,
        brightdata_data: BrightDataCompanyData,
        metadata: Dict[str, Any],
        company_uuid: str,
        s3_raw_data_key: str,
    ) -> Dict[str, Any]:
        """Clean and extract company enrichment data."""
        return {
            "company_profile": self._create_company_record(
                brightdata_data, metadata, company_uuid, s3_raw_data_key
            ),
            "founders": self._extract_founders(brightdata_data),
            "executives": self._extract_executives(brightdata_data),
            "technologies": self._extract_technologies(brightdata_data),
            "keywords": self._extract_keywords(brightdata_data),
            "competitors": self._extract_competitors(brightdata_data),
        }

    async def _clean_snapshot_company_data(
        self,
        snapshot_record: BrightDataSnapshotRecord,
        metadata: Dict[str, Any],
        company_uuid: str,
        s3_raw_data_key: str,
    ) -> Dict[str, Any]:
        """Clean and extract company data from snapshot."""
        return {
            "company_profile": self._create_company_record_from_snapshot(
                snapshot_record, metadata, company_uuid, s3_raw_data_key
            ),
            "founders": self._extract_founders_from_snapshot(snapshot_record),
            "executives": self._extract_executives_from_snapshot(snapshot_record),
            "technologies": self._extract_technologies_from_snapshot(snapshot_record),
            "keywords": self._extract_keywords_from_snapshot(snapshot_record),
            "competitors": self._extract_competitors_from_snapshot(snapshot_record),
        }

    def _create_company_record(
        self,
        brightdata_data: BrightDataCompanyData,
        metadata: Dict[str, Any],
        company_uuid: str,
        s3_raw_data_key: str,
    ) -> Dict[str, Any]:
        """Create company record from BrightData data."""
        return {
            "id": company_uuid,
            "company_id": metadata.get("company_id"),
            "org_id": metadata.get("org_id"),
            "name": self._clean_string(brightdata_data.name),
            "description": self._clean_string(brightdata_data.description),
            "website": self._clean_url(brightdata_data.website),
            "domain": self._clean_string(brightdata_data.domain),
            "industry": self._clean_string(brightdata_data.industry),
            "sub_industry": self._clean_string(brightdata_data.sub_industry),
            "stage": self._clean_string(brightdata_data.stage),
            "founded_year": brightdata_data.founded_year,
            "employee_count": brightdata_data.employee_count,
            "employee_count_range": self._clean_string(
                brightdata_data.employee_count_range
            ),
            "headquarters": self._clean_string(brightdata_data.headquarters),
            "country": self._clean_string(brightdata_data.country),
            "city": self._clean_string(brightdata_data.city),
            "state": self._clean_string(brightdata_data.state),
            "funding_total": brightdata_data.funding_total,
            "funding_rounds": brightdata_data.funding_rounds,
            "last_funding_date": brightdata_data.last_funding_date,
            "last_funding_amount": brightdata_data.last_funding_amount,
            "valuation": brightdata_data.valuation,
            "revenue": brightdata_data.revenue,
            "linkedin_url": self._clean_url(brightdata_data.linkedin_url),
            "twitter_url": self._clean_url(brightdata_data.twitter_url),
            "facebook_url": self._clean_url(brightdata_data.facebook_url),
            "email": self._clean_string(brightdata_data.email),
            "phone": self._clean_string(brightdata_data.phone),
            "crunchbase_url": brightdata_data.crunchbase_url,
            "brightdata_snapshot_id": brightdata_data.brightdata_snapshot_id,
            "s3_raw_data_key": s3_raw_data_key,
            "source": "brightdata_crunchbase",
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
        }

    def _create_company_record_from_snapshot(
        self,
        snapshot_record: BrightDataSnapshotRecord,
        metadata: Dict[str, Any],
        company_uuid: str,
        s3_raw_data_key: str,
    ) -> Dict[str, Any]:
        """Create company record from snapshot data."""
        return {
            "id": company_uuid,
            "company_id": metadata.get("company_id"),
            "org_id": metadata.get("org_id"),
            "name": self._clean_string(snapshot_record.name),
            "description": self._clean_string(snapshot_record.description),
            "website": self._clean_url(snapshot_record.website),
            "domain": self._clean_string(snapshot_record.domain),
            "industry": self._clean_string(snapshot_record.industry),
            "sub_industry": self._clean_string(snapshot_record.sub_industry),
            "stage": self._clean_string(snapshot_record.stage),
            "founded_year": snapshot_record.founded_year,
            "employee_count": snapshot_record.employee_count,
            "employee_count_range": self._clean_string(
                snapshot_record.employee_count_range
            ),
            "headquarters": self._clean_string(snapshot_record.headquarters),
            "country": self._clean_string(snapshot_record.country),
            "city": self._clean_string(snapshot_record.city),
            "state": self._clean_string(snapshot_record.state),
            "funding_total": snapshot_record.funding_total,
            "funding_rounds": snapshot_record.funding_rounds,
            "last_funding_date": snapshot_record.last_funding_date,
            "last_funding_amount": snapshot_record.last_funding_amount,
            "valuation": snapshot_record.valuation,
            "revenue": snapshot_record.revenue,
            "linkedin_url": self._clean_url(snapshot_record.linkedin_url),
            "twitter_url": self._clean_url(snapshot_record.twitter_url),
            "facebook_url": self._clean_url(snapshot_record.facebook_url),
            "email": self._clean_string(snapshot_record.email),
            "phone": self._clean_string(snapshot_record.phone),
            "s3_raw_data_key": s3_raw_data_key,
            "source": "brightdata_crunchbase",
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
        }

    def _extract_founders(
        self, brightdata_data: BrightDataCompanyData
    ) -> List[Dict[str, Any]]:
        """Extract founders from BrightData company data."""
        founders = []
        for founder in brightdata_data.founders:
            if isinstance(founder, dict):
                founders.append({
                    "name": self._clean_string(founder.get("name")),
                    "title": self._clean_string(founder.get("title")),
                    "linkedin_url": self._clean_url(founder.get("linkedin_url")),
                    "bio": self._clean_string(founder.get("bio")),
                })
        return founders

    def _extract_executives(
        self, brightdata_data: BrightDataCompanyData
    ) -> List[Dict[str, Any]]:
        """Extract executives from BrightData company data."""
        executives = []
        for executive in brightdata_data.executives:
            if isinstance(executive, dict):
                executives.append({
                    "name": self._clean_string(executive.get("name")),
                    "title": self._clean_string(executive.get("title")),
                    "linkedin_url": self._clean_url(executive.get("linkedin_url")),
                    "bio": self._clean_string(executive.get("bio")),
                })
        return executives

    def _extract_technologies(
        self, brightdata_data: BrightDataCompanyData
    ) -> List[str]:
        """Extract technologies from BrightData company data."""
        technologies = []
        for tech in brightdata_data.technologies:
            cleaned_tech = self._clean_string(tech)
            if cleaned_tech:
                technologies.append(cleaned_tech)
        return technologies

    def _extract_keywords(self, brightdata_data: BrightDataCompanyData) -> List[str]:
        """Extract keywords from BrightData company data."""
        keywords = []
        for keyword in brightdata_data.keywords:
            cleaned_keyword = self._clean_string(keyword)
            if cleaned_keyword:
                keywords.append(cleaned_keyword)
        return keywords

    def _extract_competitors(self, brightdata_data: BrightDataCompanyData) -> List[str]:
        """Extract competitors from BrightData company data."""
        competitors = []
        for comp in brightdata_data.competitors:
            cleaned_comp = self._clean_string(comp)
            if cleaned_comp:
                competitors.append(cleaned_comp)
        return competitors

    def _extract_founders_from_snapshot(
        self, snapshot_record: BrightDataSnapshotRecord
    ) -> List[Dict[str, Any]]:
        """Extract founders from snapshot data."""
        founders = []
        for founder in snapshot_record.founders:
            if isinstance(founder, dict):
                founders.append({
                    "name": self._clean_string(founder.get("name")),
                    "title": self._clean_string(founder.get("title")),
                    "linkedin_url": self._clean_url(founder.get("linkedin_url")),
                    "bio": self._clean_string(founder.get("bio")),
                })
        return founders

    def _extract_executives_from_snapshot(
        self, snapshot_record: BrightDataSnapshotRecord
    ) -> List[Dict[str, Any]]:
        """Extract executives from snapshot data."""
        executives = []
        for executive in snapshot_record.executives:
            if isinstance(executive, dict):
                executives.append({
                    "name": self._clean_string(executive.get("name")),
                    "title": self._clean_string(executive.get("title")),
                    "linkedin_url": self._clean_url(executive.get("linkedin_url")),
                    "bio": self._clean_string(executive.get("bio")),
                })
        return executives

    def _extract_technologies_from_snapshot(
        self, snapshot_record: BrightDataSnapshotRecord
    ) -> List[str]:
        """Extract technologies from snapshot data."""
        technologies = []
        for tech in snapshot_record.technologies:
            cleaned_tech = self._clean_string(tech)
            if cleaned_tech:
                technologies.append(cleaned_tech)
        return technologies

    def _extract_keywords_from_snapshot(
        self, snapshot_record: BrightDataSnapshotRecord
    ) -> List[str]:
        """Extract keywords from snapshot data."""
        keywords = []
        for keyword in snapshot_record.keywords:
            cleaned_keyword = self._clean_string(keyword)
            if cleaned_keyword:
                keywords.append(cleaned_keyword)
        return keywords

    def _extract_competitors_from_snapshot(
        self, snapshot_record: BrightDataSnapshotRecord
    ) -> List[str]:
        """Extract competitors from snapshot data."""
        competitors = []
        for comp in snapshot_record.competitors:
            cleaned_comp = self._clean_string(comp)
            if cleaned_comp:
                competitors.append(cleaned_comp)
        return competitors

    async def _store_company_data(self, cleaned_data: Dict[str, Any]) -> None:
        """Store cleaned company data in database."""
        try:
            # Store company profile
            company_profile = cleaned_data["company_profile"]
            await self.rds.insert("company_profiles", company_profile)

            # Store founders
            for founder in cleaned_data.get("founders", []):
                founder["company_id"] = company_profile["id"]
                await self.rds.insert("company_founders", founder)

            # Store executives
            for executive in cleaned_data.get("executives", []):
                executive["company_id"] = company_profile["id"]
                await self.rds.insert("company_executives", executive)

            # Store technologies
            for technology in cleaned_data.get("technologies", []):
                tech_record = {
                    "company_id": company_profile["id"],
                    "technology": technology,
                    "created_at": datetime.now(timezone.utc),
                }
                await self.rds.insert("company_technologies", tech_record)

            # Store keywords
            for keyword in cleaned_data.get("keywords", []):
                keyword_record = {
                    "company_id": company_profile["id"],
                    "keyword": keyword,
                    "created_at": datetime.now(timezone.utc),
                }
                await self.rds.insert("company_keywords", keyword_record)

            # Store competitors
            for competitor in cleaned_data.get("competitors", []):
                competitor_record = {
                    "company_id": company_profile["id"],
                    "competitor": competitor,
                    "created_at": datetime.now(timezone.utc),
                }
                await self.rds.insert("company_competitors", competitor_record)

        except Exception as e:
            self.logger.error(f"Failed to store company data: {str(e)}", exc_info=True)
            raise

    async def _store_processing_error(
        self, payload: Dict[str, Any], error_msg: str, s3_key: str
    ) -> None:
        """Store processing error for debugging."""
        try:
            error_record = {
                "id": str(uuid4()),
                "payload": payload,
                "error_message": error_msg,
                "s3_key": s3_key,
                "created_at": datetime.now(timezone.utc),
            }
            await self.rds.insert("processing_errors", error_record)
        except Exception as e:
            self.logger.error(f"Failed to store processing error: {str(e)}")

    def _assess_company_data_quality(
        self, snapshot_record: BrightDataSnapshotRecord
    ) -> str:
        """Assess the quality of company data."""
        score = 0
        total_fields = 0

        # Basic company info
        if snapshot_record.name:
            score += 1
        total_fields += 1

        if snapshot_record.description:
            score += 1
        total_fields += 1

        if snapshot_record.website:
            score += 1
        total_fields += 1

        if snapshot_record.industry:
            score += 1
        total_fields += 1

        if snapshot_record.founded_year:
            score += 1
        total_fields += 1

        if snapshot_record.employee_count:
            score += 1
        total_fields += 1

        if snapshot_record.headquarters:
            score += 1
        total_fields += 1

        if snapshot_record.funding_total:
            score += 1
        total_fields += 1

        # Team info
        if snapshot_record.founders:
            score += 1
        total_fields += 1

        if snapshot_record.executives:
            score += 1
        total_fields += 1

        quality_percentage = (score / total_fields) * 100

        if quality_percentage >= 80:
            return "excellent"
        elif quality_percentage >= 60:
            return "good"
        elif quality_percentage >= 40:
            return "fair"
        else:
            return "poor"

    def _clean_string(self, value: Any) -> Optional[str]:
        """Clean string value."""
        if value is None:
            return None
        if isinstance(value, str):
            cleaned = value.strip()
            return cleaned if cleaned else None
        return str(value).strip() if str(value).strip() else None

    def _clean_url(self, value: Any) -> Optional[str]:
        """Clean URL value."""
        if value is None:
            return None
        if isinstance(value, str):
            cleaned = value.strip()
            if cleaned and (
                cleaned.startswith("http://") or cleaned.startswith("https://")
            ):
                return cleaned
        return None

    def _parse_date(self, date_str: Any) -> Optional[date]:
        """Parse date string."""
        if not date_str:
            return None
        try:
            if isinstance(date_str, str):
                return datetime.strptime(date_str, "%Y-%m-%d").date()
            elif isinstance(date_str, datetime):
                return date_str.date()
            elif isinstance(date_str, date):
                return date_str
        except (ValueError, TypeError):
            pass
        return None
