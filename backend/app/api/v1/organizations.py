from datetime import datetime, timezone
from typing import List, Optional, Union

from bson import ObjectId
from fastapi import Depends, HTTPException, status
from motor.motor_asyncio import AsyncIOMotorDatabase
from pydantic import BaseModel, Field

from app.api.base import BaseAPIRouter
from app.core.database import get_database
from app.dependencies.auth import get_current_user, validate_org_header
from app.models.audit import AuditLog
from app.models.organization import Organization, ThesisConfig
from app.models.user import User
from app.services.factory import get_role_service
from app.services.role.interfaces import RoleServiceInterface
from app.utils.common import ObjectIdField
from app.utils.rbac.rbac import rbac_register
from app.dependencies.org import get_org_context

# System-level router for organization management
# NOTE: Auth is DISABLED for system_router for testing. Re-enable in production.
system_router = BaseAPIRouter(
    prefix="/system/organizations",
    tags=["organizations"],
    require_org=False,
    disable_auth=True,  # Disable both org context and auth
    dependencies=[],  # Remove all dependencies
)

# Regular router for organization operations
router = BaseAPIRouter(
    prefix="/organizations",
    tags=["organizations"],
    require_org=True,
    dependencies=[Depends(validate_org_header)],
)


# --- PATCH: Allow 'created_by' to be 'System' or ObjectId for testing ---
# Patch Organization model locally for this router (for testing)
class OrganizationTestModel(Organization):
    created_by: Union[ObjectIdField, str, None] = None


class OrganizationResolution(BaseModel):
    """Enhanced organization resolution response model."""

    org_id: str
    plan: str
    org_name: str
    subdomain_enabled: bool = True
    description: Optional[str] = None
    logo_url: Optional[str] = None
    website: Optional[str] = None
    contact_email: Optional[str] = None
    contact_phone: Optional[str] = None
    features: dict = Field(default_factory=dict)
    branding: dict = Field(default_factory=dict)


@system_router.get("/resolve", response_model=OrganizationResolution)
async def resolve_organization(
    org_context=Depends(get_org_context),
):
    """Resolve organization by subdomain."""
    # Find organization by subdomain
    org_id, _ = org_context
    organization = await Organization.find_one({"_id": ObjectId(org_id)})
    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Organization with subdomain '{org_id}' not found",
        )

    # Get organization settings
    settings = organization.settings
    plan = settings.plan
    subdomain_enabled = settings.subdomain_enabled
    features = settings.features
    branding = settings.branding
    return OrganizationResolution(
        org_id=str(organization.id),
        plan=plan,
        org_name=organization.name,
        subdomain_enabled=subdomain_enabled,
        description=organization.description,
        logo_url=organization.logo_url,
        website=organization.website,
        contact_email=organization.contact_email,
        contact_phone=organization.contact_phone,
        features=features,
        branding=branding,
    )


@system_router.post("/", response_model=OrganizationTestModel)
async def create_organization_testing(
    org: OrganizationTestModel, db: AsyncIOMotorDatabase = Depends(get_database)
):
    """
    Create organization for testing. Accepts 'created_by' as ObjectId, string (e.g., 'System'), or None.
    """
    # Convert 'created_by' if string 'System' to None or handle as needed
    if isinstance(org.created_by, str) and org.created_by.lower() == "system":
        org.created_by = None
    org_dict = org.model_dump(by_alias=True, exclude_none=True)
    result = await db.organizations.insert_one(org_dict)
    org_dict["_id"] = result.inserted_id
    return org_dict


@system_router.post("", response_model=Organization)
async def create_organization(
    organization: Organization,
    db: AsyncIOMotorDatabase = Depends(get_database),
    role_service: RoleServiceInterface = Depends(get_role_service),
):
    """Create a new organization (superuser only)"""
    # Create organization
    org = Organization(**organization.model_dump(by_alias=True, exclude_none=True))
    await org.save()
    created_organization = await db.organizations.find_one({"_id": org.id})

    # Create default roles for the organization
    role_service = role_service
    gp_role, analyst_role = await role_service.create_default_roles(str(org.id))

    # Log organization creation
    audit_log = AuditLog(
        user_id=organization.created_by,  # Use system user for audit
        action="create_organization",
        entity_type="organization",
        entity_id=org.id,
        metadata={
            "name": organization.name,
            "created_by_user": "system",
            "default_roles": {
                "general_partner": str(gp_role.id),
                "analyst": str(analyst_role.id),
            },
        },
    )
    await db.audit_logs.insert_one(audit_log.dict(by_alias=True))

    return Organization(**created_organization)


@system_router.get("", response_model=List[Organization])
async def list_organizations(db: AsyncIOMotorDatabase = Depends(get_database)):
    """List all organizations (superuser only)"""
    organizations = await db.organizations.find().to_list(length=None)
    return [Organization(**org) for org in organizations]


@router.get("/{organization_id}", response_model=Organization)
async def get_organization(
    organization_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
    org_id: str = Depends(validate_org_header),
):
    """Get a specific organization by ID (superuser only)"""
    # if not current_user.is_superuser:
    #     raise HTTPException(
    #         status_code=status.HTTP_403_FORBIDDEN,
    #         detail="Only superusers can perform this action"
    #     )

    if not ObjectId.is_valid(organization_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid organization ID"
        )

    # Verify the requested org_id matches the header
    if organization_id != org_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Organization ID mismatch"
        )

    organization = await db.organizations.find_one({"_id": ObjectId(organization_id)})
    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found"
        )

    return Organization(**organization)


@router.put("/{organization_id}", response_model=Organization)
@rbac_register(
    resource="organization",
    action="edit",
    group="Organizations",
    description="Update organization",
)
async def update_organization(
    organization_id: str,
    organization_update: Organization,
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
    org_id: str = Depends(validate_org_header),
):
    """Update an organization (superuser only)"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only superusers can perform this action",
        )

    if not ObjectId.is_valid(organization_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid organization ID"
        )

    # Verify the requested org_id matches the header
    if organization_id != org_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Organization ID mismatch"
        )

    organization = await db.organizations.find_one({"_id": ObjectId(organization_id)})
    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found"
        )

    update_data = organization_update.model_dump(exclude_unset=True)
    update_data["updated_at"] = int(datetime.utcnow().timestamp())

    await db.organizations.update_one(
        {"_id": ObjectId(organization_id)}, {"$set": update_data}
    )

    # Log organization update
    audit_log = AuditLog(
        user_id=current_user.id,
        action="update_organization",
        entity_type="organization",
        entity_id=ObjectId(organization_id),
        metadata={"update_data": update_data},
    )
    await db.audit_logs.insert_one(audit_log.dict(by_alias=True))

    updated_organization = await db.organizations.find_one({
        "_id": ObjectId(organization_id)
    })
    return Organization(**updated_organization)


@router.delete("/{organization_id}", status_code=status.HTTP_204_NO_CONTENT)
@rbac_register(
    resource="organization",
    action="delete",
    group="Organizations",
    description="Delete organization",
)
async def delete_organization(
    organization_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
    org_id: str = Depends(validate_org_header),
):
    """Delete an organization (superuser only)"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only superusers can perform this action",
        )

    if not ObjectId.is_valid(organization_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid organization ID"
        )

    # Verify the requested org_id matches the header
    if organization_id != org_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Organization ID mismatch"
        )

    organization = await db.organizations.find_one({"_id": ObjectId(organization_id)})
    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found"
        )

    # Log organization deletion
    audit_log = AuditLog(
        user_id=current_user.id,
        action="delete_organization",
        entity_type="organization",
        entity_id=ObjectId(organization_id),
    )
    await db.audit_logs.insert_one(audit_log.dict(by_alias=True))

    await db.organizations.delete_one({"_id": ObjectId(organization_id)})
    return None


# Thesis Configuration Endpoints


class ThesisConfigResponse(BaseModel):
    """Response model for thesis configuration."""

    geography: List[str] = Field(default_factory=list)
    sector: List[str] = Field(default_factory=list)
    stage: List[str] = Field(default_factory=list)
    business_model: List[str] = Field(default_factory=list)


class ThesisConfigRequest(BaseModel):
    """Request model for updating thesis configuration."""

    geography: List[str] = Field(default_factory=list, max_length=5)
    sector: List[str] = Field(default_factory=list, max_length=5)
    stage: List[str] = Field(default_factory=list, max_length=5)
    business_model: List[str] = Field(default_factory=list, max_length=5)


class ThesisConfigUpdateResponse(BaseModel):
    """Response model for thesis configuration update."""

    success: bool
    message: str


async def validate_org_admin(
    org_id: str,
    current_user: User,
    db: AsyncIOMotorDatabase
) -> Organization:
    """
    Validate that the current user is an admin of the specified organization.

    Args:
        org_id: Organization ID to check
        current_user: Current authenticated user
        db: Database connection

    Returns:
        Organization object if user is admin

    Raises:
        HTTPException: If user is not admin or org not found
    """
    # Validate ObjectId format
    if not ObjectId.is_valid(org_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid organization ID format"
        )

    # Get organization
    organization = await db.organizations.find_one({"_id": ObjectId(org_id)})
    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Organization not found"
        )

    # Check if user is admin of this organization
    user_id = current_user.id
    org_admins = organization.get("admins", [])
    org_created_by = organization.get("created_by")

    # User is admin if they are in the admins list or created the organization
    is_admin = (
        user_id in org_admins or
        user_id == org_created_by or
        current_user.is_superuser
    )

    if not is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only organization admins can access thesis configuration"
        )

    return Organization(**organization)


@router.get("/{org_id}/thesis-config", response_model=ThesisConfigResponse)
@rbac_register(
    resource="organization",
    action="view",
    group="Organizations",
    description="View organization thesis configuration",
)
async def get_thesis_config(
    org_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
) -> ThesisConfigResponse:
    """
    Get the current thesis configuration for an organization.

    Only organization admins can access this endpoint.

    Args:
        org_id: Organization ID
        current_user: Current authenticated user
        db: Database connection

    Returns:
        Current thesis configuration or empty config if not set

    Raises:
        403: If user is not admin of the organization
        404: If organization not found
    """
    # Validate user is admin of this organization
    organization = await validate_org_admin(org_id, current_user, db)

    # Get thesis config or return empty config
    thesis_config = organization.thesis_config
    if thesis_config:
        return ThesisConfigResponse(
            geography=thesis_config.geography,
            sector=thesis_config.sector,
            stage=thesis_config.stage,
            business_model=thesis_config.business_model,
        )
    else:
        return ThesisConfigResponse()


@router.post("/{org_id}/thesis-config", response_model=ThesisConfigUpdateResponse)
@rbac_register(
    resource="organization",
    action="edit",
    group="Organizations",
    description="Update organization thesis configuration",
)
async def update_thesis_config(
    org_id: str,
    config_request: ThesisConfigRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
) -> ThesisConfigUpdateResponse:
    """
    Set or update the thesis configuration for an organization.

    Only organization admins can update thesis configuration.

    Args:
        org_id: Organization ID
        config_request: New thesis configuration
        current_user: Current authenticated user
        db: Database connection

    Returns:
        Success response with message

    Raises:
        403: If user is not admin of the organization
        404: If organization not found
        422: If validation fails
    """
    # Validate user is admin of this organization
    await validate_org_admin(org_id, current_user, db)

    try:
        # Create and validate ThesisConfig object
        thesis_config = ThesisConfig(
            geography=config_request.geography,
            sector=config_request.sector,
            stage=config_request.stage,
            business_model=config_request.business_model,
        )

        # Update organization with new thesis config
        update_result = await db.organizations.update_one(
            {"_id": ObjectId(org_id)},
            {
                "$set": {
                    "thesis_config": thesis_config.model_dump(),
                    "updated_at": int(datetime.now(timezone.utc).timestamp()),
                }
            }
        )

        if update_result.modified_count == 0:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update thesis configuration"
            )

        # Log the update
        audit_log = AuditLog(
            user_id=current_user.id,
            action="update_thesis_config",
            entity_type="organization",
            entity_id=ObjectId(org_id),
            metadata={
                "geography_count": len(thesis_config.geography),
                "sector_count": len(thesis_config.sector),
                "stage_count": len(thesis_config.stage),
                "business_model_count": len(thesis_config.business_model),
            },
        )
        await db.audit_logs.insert_one(audit_log.model_dump(by_alias=True))

        return ThesisConfigUpdateResponse(
            success=True,
            message="Thesis configuration updated successfully"
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update thesis configuration: {str(e)}"
        )
