/**
 * Chat API Client
 * 
 * Handles communication with the Orbit AI Investment Chat Assistant backend.
 * Supports thread management, message sending, and status polling.
 */

import apiClient from '@/lib/api-client';

export interface ChatSource {
  title: string;
  url: string;
  snippet?: string;
  domain?: string;
}

export type ChatMode = 'chat' | 'research' | 'agent';

export interface ChatMessage {
  id: string;
  thread_id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  mode: ChatMode;
  status: 'pending' | 'completed' | 'failed';
  sources: ChatSource[];
  ai_model?: string;
  ai_provider?: string;
  response_time_ms?: number;
  error_message?: string;
  created_at: number;
  updated_at: number;
}

export interface ChatThread {
  id: string;
  deal_id: string;
  user_id: string;
  mode: ChatMode;
  title?: string;
  message_count: number;
  last_message_at: number;
  last_model_used?: string;
  messages: ChatMessage[];
  created_at: number;
  updated_at: number;
}

export interface ChatHistoryResponse {
  thread: ChatThread;
  has_more: boolean;
  total_messages: number;
}

export interface SendMessageRequest {
  message: string;
  mode?: ChatMode;
  agent_type?: string;
  include_deal_context?: boolean;
}

export interface SendMessageResponse {
  message_id: string;
  thread_id: string;
  status: 'pending' | 'completed' | 'failed';
  estimated_completion_time?: number;
}

export interface MessageStatusResponse {
  message_id: string;
  status: 'pending' | 'completed' | 'failed';
  content?: string;
  sources: ChatSource[];
  error_message?: string;
  retry_count: number;
  updated_at: number;
}

export interface ChatStatsResponse {
  total_threads: number;
  total_messages: number;
  active_threads: number;
  avg_response_time?: number;
}

export class ChatAPI {
  /**
   * Get chat history for a deal
   */
  static async getChatHistory(
    dealId: string,
    mode: ChatMode = 'chat',
    limit: number = 50,
    skip: number = 0
  ): Promise<ChatHistoryResponse> {
    try {
      const response = await apiClient.get(`/deals/${dealId}/chat`, {
        params: { mode, limit, skip }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting chat history:', error);
      throw error;
    }
  }

  /**
   * Send a new chat message (synchronous - returns completed AI response)
   */
  static async sendMessage(
    dealId: string,
    request: SendMessageRequest
  ): Promise<ChatMessage> {
    try {
      const response = await apiClient.post(`/deals/${dealId}/chat`, request);
      return response.data;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  /**
   * Get chat statistics
   */
  static async getChatStats(): Promise<ChatStatsResponse> {
    try {
      const response = await apiClient.get('/chat/stats');
      return response.data;
    } catch (error) {
      console.error('Error getting chat stats:', error);
      throw error;
    }
  }
}
