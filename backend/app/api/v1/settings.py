"""
Settings API endpoints for user profile and organization management.

This module handles user profile updates, organization settings,
and member management functionality.
"""

from typing import List, Optional

from fastapi import Depends, HTTPException, status
from pydantic import BaseModel, EmailStr, Field

from app.api.base import BaseAPIRouter
from app.core.logging import get_logger
from app.core.security import get_password_hash, verify_password
from app.dependencies.auth import get_current_user
from app.models.organization import Organization
from app.models.role import Role
from app.models.user import User, UserStatus
from app.services.auth.interface import IAuthService
from app.services.factory import get_auth_service, get_user_service

logger = get_logger(__name__)

router = BaseAPIRouter(prefix="/settings", tags=["settings"])


# Profile Schemas
class UpdateProfileRequest(BaseModel):
    """Request schema for updating user profile."""

    name: Optional[str] = Field(
        None, min_length=2, max_length=100, description="User's full name"
    )
    profile_picture: Optional[str] = Field(None, description="URL to profile picture")

    class Config:
        json_schema_extra = {
            "example": {
                "name": "<PERSON>",
                "profile_picture": "https://s3.amazonaws.com/bucket/avatar.jpg",
            }
        }


class ChangePasswordRequest(BaseModel):
    """Request schema for changing password."""

    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, description="New password")
    confirm_password: str = Field(..., description="Password confirmation")

    class Config:
        json_schema_extra = {
            "example": {
                "current_password": "oldpassword123",
                "new_password": "newpassword123",
                "confirm_password": "newpassword123",
            }
        }


# Organization Schemas
class UpdateOrganizationRequest(BaseModel):
    """Request schema for updating organization."""

    name: Optional[str] = Field(
        None, min_length=2, max_length=100, description="Organization name"
    )
    logo_url: Optional[str] = Field(None, description="URL to organization logo")
    website: Optional[str] = Field(None, description="Organization website")
    contact_email: Optional[EmailStr] = Field(None, description="Contact email")
    address: Optional[str] = Field(None, description="Organization address")
    description: Optional[str] = Field(
        None, max_length=1000, description="Organization description"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "name": "TractionX Ventures",
                "logo_url": "https://s3.amazonaws.com/bucket/logo.png",
                "website": "https://tractionx.ai",
                "contact_email": "<EMAIL>",
                "address": "123 Innovation St, San Francisco, CA",
                "description": "Leading AI-driven investment platform",
            }
        }


# Member Schemas
class OrganizationMember(BaseModel):
    """Schema for organization member information."""

    id: str
    name: str
    email: EmailStr
    profile_picture: Optional[str] = None
    status: UserStatus
    role_name: Optional[str] = None
    last_login: Optional[int] = None
    created_at: int

    class Config:
        json_schema_extra = {
            "example": {
                "id": "507f1f77bcf86cd799439011",
                "name": "John Doe",
                "email": "<EMAIL>",
                "profile_picture": "https://s3.amazonaws.com/bucket/avatar.jpg",
                "status": "active",
                "role_name": "Admin",
                "last_login": 1640995200,
                "created_at": 1640995200,
            }
        }


class InviteMemberRequest(BaseModel):
    """Request schema for inviting organization members."""

    emails: List[EmailStr] = Field(
        ..., min_items=1, max_items=10, description="Email addresses to invite"
    )  # type: ignore
    role_id: Optional[str] = Field(None, description="Role ID to assign")
    message: Optional[str] = Field(
        None, max_length=500, description="Optional invitation message"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "emails": ["<EMAIL>", "<EMAIL>"],
                "role_id": "507f1f77bcf86cd799439012",
                "message": "Join our team on TractionX!",
            }
        }


# Profile Endpoints
@router.get("/profile")
async def get_profile(
    current_user: User = Depends(get_current_user),
    user_service=Depends(get_user_service),
):
    """Get current user's profile information."""
    try:
        return {
            "id": str(current_user.id),
            "name": current_user.name,
            "email": current_user.email,
            "profile_picture": getattr(current_user, "profile_picture", None),
            "status": current_user.status,
            "created_at": current_user.created_at,
            "last_login": current_user.last_login,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting profile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get profile",
        )


@router.patch("/profile")
async def update_profile(
    request: UpdateProfileRequest,
    current_user: User = Depends(get_current_user),
):
    """Update current user's profile."""
    try:
        user = await User.find_one({"_id": current_user.id})
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        # Update fields if provided
        update_data = {}
        if request.name is not None:
            update_data["name"] = request.name
        if request.profile_picture is not None:
            update_data["profile_picture"] = request.profile_picture

        if update_data:
            await User.update_one({"_id": current_user.id}, {"$set": update_data})

        logger.info(f"Profile updated for user {current_user.id}")
        return {"message": "Profile updated successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating profile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update profile",
        )


@router.post("/profile/change-password")
async def change_password(
    request: ChangePasswordRequest,
    current_user: User = Depends(get_current_user),
):
    """Change user's password."""
    try:
        # Validate password confirmation
        if request.new_password != request.confirm_password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Passwords do not match"
            )

        user = await User.find_one({"_id": current_user.id})
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        # Verify current password
        if not verify_password(request.current_password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect",
            )

        # Update password
        new_password_hash = get_password_hash(request.new_password)
        await User.update_one(
            {"_id": current_user.id},
            {"$set": {"password_hash": new_password_hash}},
        )

        logger.info(f"Password changed for user {current_user.id}")
        return {"message": "Password changed successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error changing password: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to change password",
        )


# Organization Endpoints
@router.get("/organization")
async def get_organization(
    current_user: User = Depends(get_current_user),
):
    """Get current user's organization information."""
    try:
        organization = await Organization.find_one({"_id": current_user.org_id})
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found"
            )

        return {
            "id": str(organization.id),
            "name": organization.name,
            "subdomain": getattr(organization, "subdomain", None),
            "logo_url": getattr(organization, "logo_url", None),
            "website": getattr(organization, "website", None),
            "contact_email": getattr(organization, "contact_email", None),
            "address": getattr(organization, "address", None),
            "description": getattr(organization, "description", None),
            "plan": getattr(organization.settings, "plan", "Basic")
            if organization.settings
            else "Basic",
            "created_at": organization.created_at,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting organization: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get organization",
        )


@router.patch("/organization")
async def update_organization(
    request: UpdateOrganizationRequest,
    current_user: User = Depends(get_current_user),
):
    """Update organization settings. Requires admin role."""
    try:
        # Check if user is admin
        user = await User.find_one({"_id": current_user.id})
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        # Get user's role
        role = await Role.find_one({"_id": user.role_id}) if user.role_id else None
        if not role or role.name not in ["admin", "owner"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Admin access required"
            )

        organization = await Organization.find_one({"_id": current_user.org_id})
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found"
            )

        # Update fields if provided
        update_data = {}
        if request.name is not None:
            update_data["name"] = request.name
        if request.logo_url is not None:
            update_data["logo_url"] = request.logo_url
        if request.website is not None:
            update_data["website"] = request.website
        if request.contact_email is not None:
            update_data["contact_email"] = request.contact_email
        if request.address is not None:
            update_data["address"] = request.address
        if request.description is not None:
            update_data["description"] = request.description

        if update_data:
            await Organization.update_one(
                {"_id": current_user.org_id}, {"$set": update_data}
            )

        logger.info(f"Organization updated by user {current_user.id}")
        return {"message": "Organization updated successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating organization: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update organization",
        )


# Members Endpoints
@router.get("/members", response_model=List[OrganizationMember])
async def get_organization_members(
    current_user: User = Depends(get_current_user),
):
    """Get organization members. Requires admin role."""
    try:
        # Check if user is admin
        user = await User.find_one({"_id": current_user.id})
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        # role = await Role.find_one({"_id": user.role_id}) if user.role_id else None
        # if not role or role.name.lower() not in ["gp"]:
        #     raise HTTPException(
        #         status_code=status.HTTP_403_FORBIDDEN, detail="Admin access required"
        #     )

        # Get organization members
        members = await User.find_many(query={"org_id": current_user.org_id})

        result = []
        for member in members:
            # Get role name
            member_role = (
                await Role.find_one({"_id": member.role_id}) if member.role_id else None
            )
            role_name = member_role.name if member_role else None

            result.append(
                OrganizationMember(
                    id=str(member.id),
                    name=member.name,
                    email=member.email,
                    profile_picture=getattr(member, "profile_picture", None),
                    status=member.status,
                    role_name=role_name,
                    last_login=member.last_login,
                    created_at=member.created_at,
                )
            )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting organization members: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get organization members",
        )


@router.post("/members/invite")
async def invite_members(
    request: InviteMemberRequest,
    current_user: User = Depends(get_current_user),
    auth_service: IAuthService = Depends(get_auth_service),
):
    """Invite new members to the organization. Requires admin role."""
    try:
        # Check if user is admin
        user = await User.find_one({"_id": current_user.id})
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        role = await Role.find_one({"_id": user.role_id}) if user.role_id else None
        if not role or role.name not in ["General Partner"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Admin access required"
            )

        roles = await Role.find_many(query={"org_id": current_user.org_id})
        if request.role_id == "admin":
            expected_role_name = "General Partner"
        else:
            expected_role_name = "Analyst"

        for role in roles:
            if role.name == expected_role_name:
                request.role_id = str(role.id)
                break

        results = []
        for email in request.emails:
            try:
                result = await auth_service.invite_user(
                    name=email.split("@")[0].capitalize(),
                    email=email,
                    role_id=request.role_id or "",
                    org_id=str(current_user.org_id),
                    invited_by=str(current_user.id),
                )
                results.append({"email": email, "status": "sent", "result": result})
            except Exception as e:
                logger.error(f"Failed to invite {email}: {str(e)}")
                results.append({"email": email, "status": "failed", "error": str(e)})

        total_sent = len([r for r in results if r["status"] == "sent"])
        total_failed = len([r for r in results if r["status"] == "failed"])

        return {
            "total_sent": total_sent,
            "total_failed": total_failed,
            "results": results,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error inviting members: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to invite members",
        )


@router.delete("/members/{member_id}")
async def remove_member(
    member_id: str,
    current_user: User = Depends(get_current_user),
):
    """Remove a member from the organization. Requires admin role."""
    try:
        # Check if user is admin
        user = await User.find_one({"_id": current_user.id})
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        role = await Role.find_one({"_id": user.role_id}) if user.role_id else None
        if not role or role.name not in ["General Partner"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Admin access required"
            )

        # Check if member exists and belongs to the same organization
        member = await User.find_one({"_id": member_id, "org_id": current_user.org_id})
        if not member:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Member not found"
            )

        # Prevent self-removal
        if str(member.id) == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Cannot remove yourself"
            )

        # Update member status to suspended (soft delete)
        member.status = UserStatus.SUSPENDED
        await member.save(is_update=True)

        logger.info(f"Member {member_id} removed by user {current_user.id}")
        return {"message": "Member removed successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing member: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove member",
        )
