"""
TractionX AI Agent Foundation - Main FastAPI Application
"""

import logging
from contextlib import asynccontextmanager
from fastapi import Fast<PERSON><PERSON>, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.openapi.utils import get_openapi
import uvicorn

from ai.config import settings
from ai.db import ai_db, ensure_indexes
from ai.context import qdrant_helper
from ai.registry import agent_registry
from ai.audit import audit_logger
from ai.api.agents import router as agents_router
from ai.api.deals import router as deals_router
from ai.api.news import router as news_router

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    
    # Startup
    logger.info("Starting TractionX AI Agent system...")
    
    try:
        # Initialize database
        await ai_db.connect_to_database()
        await ensure_indexes()
        
        # Initialize Qdrant
        await qdrant_helper.initialize()
        
        # Initialize audit logger
        await audit_logger.initialize()
        
        # Initialize agent registry
        await agent_registry.initialize()
        
        logger.info("AI Agent system startup complete")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to start AI Agent system: {e}")
        raise
    
    finally:
        # Shutdown
        logger.info("Shutting down TractionX AI Agent system...")
        
        try:
            await ai_db.close_database_connection()
            logger.info("AI Agent system shutdown complete")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")


# Create FastAPI app with enhanced documentation
app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.DESCRIPTION,
    version=settings.VERSION,
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    summary="AI-powered investment platform agents",
    contact={
        "name": "TractionX Team",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "Proprietary",
    },
    tags_metadata=[
        {
            "name": "agents",
            "description": "AI agent management and execution endpoints",
        },
        {
            "name": "deals",
            "description": "Deal-specific AI operations and analysis",
        },
        {
            "name": "news",
            "description": "News intelligence and real-time information gathering",
        },
        {
            "name": "system",
            "description": "System health and information endpoints",
        },
    ]
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def custom_openapi():
    """Generate custom OpenAPI schema with enhanced metadata."""
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )
    
    # Add custom OpenAPI extensions
    openapi_schema["info"]["x-logo"] = {
        "url": "https://tractionx.ai/logo.png"
    }
    
    # Add server information
    openapi_schema["servers"] = [
        {
            "url": "http://localhost:8002",
            "description": "Development server"
        },
        {
            "url": "https://ai-api.tractionx.ai",
            "description": "Production server"
        }
    ]
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "status_code": exc.status_code
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "Internal server error",
            "status_code": 500
        }
    )


# Health check endpoint
@app.get(
    "/health",
    tags=["system"],
    summary="Health Check",
    description="Check the health status of all system components"
)
async def health_check():
    """Health check endpoint."""
    
    try:
        # Check database
        db_healthy = await ai_db.health_check()
        
        # Check Qdrant
        qdrant_healthy = await qdrant_helper.health_check()
        
        health_status = {
            "status": "healthy" if db_healthy and qdrant_healthy else "unhealthy",
            "database": "healthy" if db_healthy else "unhealthy",
            "qdrant": "healthy" if qdrant_healthy else "unhealthy",
            "version": settings.VERSION
        }
        
        status_code = status.HTTP_200_OK if health_status["status"] == "healthy" else status.HTTP_503_SERVICE_UNAVAILABLE
        
        return JSONResponse(
            status_code=status_code,
            content=health_status
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "error": str(e),
                "version": settings.VERSION
            }
        )


# Root endpoint
@app.get(
    "/",
    tags=["system"],
    summary="API Root",
    description="Get basic API information and available endpoints"
)
async def root():
    """Root endpoint."""
    return {
        "message": "TractionX AI Agent Foundation",
        "version": settings.VERSION,
        "docs": "/docs",
        "redoc": "/redoc",
        "openapi": "/openapi.json",
        "health": "/health"
    }


# API info endpoint
@app.get(
    "/info",
    tags=["system"],
    summary="API Information",
    description="Get detailed API information and available endpoints"
)
async def api_info():
    """API information endpoint."""
    return {
        "name": settings.PROJECT_NAME,
        "description": settings.DESCRIPTION,
        "version": settings.VERSION,
        "api_prefix": settings.API_V1_STR,
        "documentation": {
            "swagger_ui": "/docs",
            "redoc": "/redoc",
            "openapi_json": "/openapi.json"
        },
        "endpoints": {
            "agents": f"{settings.API_V1_STR}/agents",
            "deals": f"{settings.API_V1_STR}/deals",
            "news": f"{settings.API_V1_STR}/news",
            "health": "/health",
            "info": "/info"
        },
        "features": [
            "AI Agent Management",
            "Deal Analysis",
            "News Intelligence",
            "Vector Search",
            "Audit Logging",
            "Multi-tenant Support"
        ]
    }


# OpenAPI JSON endpoint (explicit)
@app.get(
    "/openapi.json",
    tags=["system"],
    summary="OpenAPI Schema",
    description="Get the OpenAPI JSON schema for this API",
    include_in_schema=False
)
async def get_openapi_json():
    """Get OpenAPI JSON schema."""
    return app.openapi()


# Include API routers
app.include_router(agents_router, prefix=settings.API_V1_STR, tags=["agents"])
app.include_router(deals_router, prefix=settings.API_V1_STR, tags=["deals"])
app.include_router(news_router, prefix=settings.API_V1_STR, tags=["news"])


# Development server
if __name__ == "__main__":
    uvicorn.run(
        "ai.main:app",
        host="0.0.0.0",
        port=8002,  # Different port from backend
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
