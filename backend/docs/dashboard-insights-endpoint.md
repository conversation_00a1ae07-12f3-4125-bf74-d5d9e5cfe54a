# Dashboard Insights Endpoint

## Overview

The `/dashboard/insights` endpoint provides comprehensive deal analytics for dashboard charts and visualizations. This endpoint analyzes deal data across multiple dimensions to provide actionable insights for investors.

## Endpoint

```
GET /api/v1/dashboard/insights
```

## Authentication

Requires authentication via <PERSON><PERSON> token in the Authorization header.

## Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `date_range_start` | int | No | Start timestamp for filtering deals by creation date |
| `date_range_end` | int | No | End timestamp for filtering deals by creation date |
| `assigned_user_ids` | string | No | Comma-separated list of user IDs to filter deals by assigned users |

## Response Structure

The endpoint returns a comprehensive `DealInsightsResponse` with the following components:

### 1. Deal Funnel Breakdown

Shows the count of deals across each stage of the investment funnel.

```json
{
  "deal_funnel_breakdown": [
    {"status": "new", "count": 15},
    {"status": "triage", "count": 8},
    {"status": "reviewed", "count": 12},
    {"status": "excluded", "count": 5},
    {"status": "approved", "count": 3},
    {"status": "negotiating", "count": 2},
    {"status": "closed", "count": 1}
  ]
}
```

**Use Case**: Identify bottlenecks in the deal pipeline and understand where deals are getting stuck.

### 2. Sector × Stage Deal Matrix

Matrix showing deal distribution across sectors and stages.

```json
{
  "sector_stage_matrix": {
    "SaaS": {
      "Pre-Seed": 3,
      "Seed": 5,
      "Series A": 2
    },
    "FinTech": {
      "Seed": 2,
      "Series A": 1
    }
  }
}
```

**Use Case**: Identify over/underexposure by vertical and maturity, spot white-space opportunities.

### 3. Deal Activity Timeline

Daily deal creation activity over time.

```json
{
  "deal_activity_timeline": [
    {"date": "2024-01-15", "count": 3},
    {"date": "2024-01-16", "count": 1},
    {"date": "2024-01-17", "count": 5}
  ]
}
```

**Use Case**: Monitor deal flow velocity and identify trends in deal submission patterns.

### 4. Exclusion Filters Triggered

Top reasons why deals were excluded from the funnel.

```json
{
  "exclusion_filters_triggered": {
    "Too early": 12,
    "Not AI": 8,
    "Wrong geography": 5,
    "Unspecified": 3
  }
}
```

**Use Case**: Understand what's disqualifying most deals and refine filtering criteria.

### 5. Deal Velocity by User

Tracks how deals are moving through stages, broken down by assigned users.

```json
{
  "deal_velocity_by_user": {
    "user_id_1": {
      "name": "Alice Johnson",
      "status_transitions": {
        "→triage": 5,
        "→reviewed": 3,
        "→approved": 1
      }
    }
  }
}
```

**Use Case**: Monitor team performance and identify who's actively moving deals forward.

## Example Usage

### Basic Request

```bash
curl -X GET "https://api.tractionx.com/api/v1/dashboard/insights" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### With Date Range Filter

```bash
curl -X GET "https://api.tractionx.com/api/v1/dashboard/insights?date_range_start=1704067200&date_range_end=1706745600" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### With User Filter

```bash
curl -X GET "https://api.tractionx.com/api/v1/dashboard/insights?assigned_user_ids=507f1f77bcf86cd799439011,507f1f77bcf86cd799439012" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Data Processing Logic

### Deal Funnel Breakdown
- Counts all deals by their current `status`
- Includes all possible statuses (even with 0 count)
- Uses the `DealStatus` enum values

### Sector × Stage Matrix
- Normalizes `sector` field to always treat as array (handles both string and list)
- Only includes deals that have both `sector` and `stage` values
- Cleans and formats sector/stage names for display

### Deal Activity Timeline
- Groups deals by creation date (YYYY-MM-DD format)
- Sorts dates chronologically
- Uses `created_at` timestamp field

### Exclusion Filters
- Only processes deals where `exclusion_filter_result.excluded = true`
- Groups by `exclusion_filter_result.reason`
- Handles null reasons as "Unspecified"

### Deal Velocity by User
- Tracks status transitions from `timeline` events
- Includes both `created_by` and `assigned_user_ids`
- Processes "Status changed to" events in timeline

## Error Handling

The endpoint returns appropriate HTTP status codes:

- `200 OK`: Successful response with insights data
- `401 Unauthorized`: Invalid or missing authentication
- `500 Internal Server Error`: Server-side processing error

## Performance Considerations

- Processes all deals in memory for complex aggregations
- Consider implementing pagination for large datasets
- Date range filters help reduce data processing load
- User filters can significantly reduce dataset size

## Future Enhancements

- Add caching for frequently requested insights
- Implement real-time updates via WebSocket
- Add more granular filtering options
- Support for custom date ranges and time periods
- Export functionality for insights data 