"""
Sharing Utility Functions

This module provides utility functions for generating and validating sharing tokens,
URLs, and embed codes.
"""

import secrets
import time
import qrcode
import io
import base64
from typing import Any, Dict, List, Optional, Union, Tuple
from datetime import datetime, timedelta
from bson import ObjectId

from app.core.config import settings
from app.schemas.sharing import SharingType, EmbedType


def generate_token(length: int = 32) -> str:
    """
    Generate a secure random token.

    Args:
        length: Length of the token in bytes (default: 32)

    Returns:
        A URL-safe base64-encoded random token
    """
    return secrets.token_urlsafe(length)


def generate_short_code(length: int = 8) -> str:
    """
    Generate a short code for URLs.

    Args:
        length: Length of the short code in bytes (default: 8)

    Returns:
        A URL-safe base64-encoded random short code
    """
    return secrets.token_urlsafe(length)


def timestamp_ms() -> int:
    """
    Get current timestamp in milliseconds.

    Returns:
        Current timestamp in milliseconds
    """
    return int(time.time() * 1000)


def datetime_to_timestamp(dt: datetime) -> int:
    """
    Convert datetime to unix timestamp.

    Args:
        dt: Datetime object

    Returns:
        Unix timestamp (seconds since epoch)
    """
    return int(dt.timestamp())


def timestamp_to_datetime(ts: int) -> datetime:
    """
    Convert unix timestamp to datetime.

    Args:
        ts: Unix timestamp (seconds since epoch)

    Returns:
        Datetime object
    """
    return datetime.fromtimestamp(ts)


def is_expired(expires_at: Optional[int]) -> bool:
    """
    Check if a timestamp has expired.

    Args:
        expires_at: Expiration timestamp (seconds since epoch)

    Returns:
        True if expired, False otherwise
    """
    if expires_at is None:
        return False

    return expires_at < int(time.time())


def generate_sharing_url(token: str) -> str:
    """
    Generate a sharing URL.

    Args:
        token: Sharing token

    Returns:
        Full sharing URL
    """
    base_url = settings.FRONTEND_URL
    return f"{base_url}/share/{token}"


def generate_short_url(short_code: str) -> str:
    """
    Generate a short URL.

    Args:
        short_code: Short code

    Returns:
        Short URL
    """
    base_url = settings.FRONTEND_URL
    return f"{base_url}/s/{short_code}"


def generate_embed_url(token: str) -> str:
    """
    Generate an embed URL.

    Args:
        token: Embed token

    Returns:
        Embed URL
    """
    base_url = settings.FRONTEND_URL
    return f"{base_url}/embed/{token}"


def generate_qr_url(token: str) -> str:
    """
    Generate a QR code URL.

    Args:
        token: QR code token

    Returns:
        QR code URL
    """
    base_url = settings.FRONTEND_URL
    return f"{base_url}/qr/{token}"


def generate_embed_html(token: str, embed_type: str, custom_styles: Optional[Dict[str, Any]] = None) -> str:
    """
    Generate HTML for embedding.

    Args:
        token: Embed token
        embed_type: Type of embed (inline, modal, popup)
        custom_styles: Custom styles for embedding

    Returns:
        HTML code for embedding
    """
    embed_url = generate_embed_url(token)

    if embed_type == EmbedType.INLINE.value:
        # Extract custom styles
        width = custom_styles.get("width", "100%") if custom_styles else "100%"
        height = custom_styles.get("height", "500px") if custom_styles else "500px"
        return f'<iframe src="{embed_url}" width="{width}" height="{height}" frameborder="0"></iframe>'
    elif embed_type == EmbedType.MODAL.value:
        return f'<script src="{settings.FRONTEND_URL}/js/embed.js" data-token="{token}" data-type="modal"></script>'
    else:  # POPUP
        return f'<script src="{settings.FRONTEND_URL}/js/embed.js" data-token="{token}" data-type="popup"></script>'


def generate_qr_image(url: str, size: int = 300, error_correction_level: str = "M") -> str:
    """
    Generate a QR code image.

    Args:
        url: URL to encode in the QR code
        size: Size of the QR code in pixels
        error_correction_level: Error correction level (L, M, Q, H)

    Returns:
        Base64-encoded PNG image data
    """
    # Create QR code
    qr = qrcode.QRCode(
        version=1,
        error_correction=getattr(qrcode.constants, f"ERROR_CORRECT_{error_correction_level}"),
        box_size=10,
        border=4,
    )
    qr.add_data(url)
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white")

    # Convert to base64
    buffer = io.BytesIO()
    img.save(buffer)  # Remove format parameter
    image_data = base64.b64encode(buffer.getvalue()).decode("utf-8")

    return f"data:image/png;base64,{image_data}"


def validate_object_id(id_str: str) -> Tuple[bool, Optional[str]]:
    """
    Validate that a string is a valid ObjectId.

    Args:
        id_str: String to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not id_str:
        return False, "ID cannot be empty"

    try:
        ObjectId(id_str)
        return True, None
    except Exception:
        return False, f"Invalid ObjectId format: {id_str}"


def validate_sharing_config(
    resource_type: str,
    resource_id: str,
    org_id: str,
    sharing_types: Optional[List[SharingType]] = None,
    embed_type: Optional[EmbedType] = None,
    expires_at: Optional[Union[datetime, int]] = None
) -> Tuple[bool, Optional[str]]:
    """
    Validate sharing configuration parameters.

    Args:
        resource_type: Type of resource
        resource_id: ID of the resource
        org_id: Organization ID
        sharing_types: List of sharing types
        embed_type: Type of embed
        expires_at: Expiration timestamp

    Returns:
        Tuple of (is_valid, error_message)
    """
    # Validate resource type
    if not resource_type:
        return False, "Resource type cannot be empty"

    # Validate resource ID
    valid, error = validate_object_id(resource_id)
    if not valid:
        return False, f"Invalid resource ID: {error}"

    # Validate organization ID
    valid, error = validate_object_id(org_id)
    if not valid:
        return False, f"Invalid organization ID: {error}"

    # Validate sharing types
    if sharing_types:
        for sharing_type in sharing_types:
            if not isinstance(sharing_type, SharingType):
                return False, f"Invalid sharing type: {sharing_type}"

    # Validate embed type
    if embed_type and not isinstance(embed_type, EmbedType):
        return False, f"Invalid embed type: {embed_type}"

    # Validate expiration
    if expires_at:
        if isinstance(expires_at, datetime):
            if expires_at < datetime.now():
                return False, "Expiration date cannot be in the past"
        elif isinstance(expires_at, int):
            if expires_at < int(time.time()):
                return False, "Expiration timestamp cannot be in the past"
        else:
            return False, f"Invalid expiration format: {expires_at}"

    return True, None
