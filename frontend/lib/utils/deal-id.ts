/**
 * Utility functions for normalizing MongoDB _id fields to strings
 * Handles various MongoDB ObjectId formats from backend responses
 */

export interface MongoObjectId {
  $oid: string;
}

export interface DealWithId {
  _id?: string | MongoObjectId;
  id?: string;
  [key: string]: any;
}

/**
 * Normalize MongoDB _id to a string for frontend use
 * Handles both ObjectId objects and string formats
 */
export function getDealId(deal: DealWithId): string {
  // Handle MongoDB ObjectId object format: { $oid: "..." }
  if (typeof deal._id === 'object' && deal._id && '$oid' in deal._id) {
    return deal._id.$oid;
  }
  
  // Handle string _id
  if (typeof deal._id === 'string' && deal._id) {
    return deal._id;
  }
  
  // Fallback to id field
  if (typeof deal.id === 'string' && deal.id) {
    return deal.id;
  }
  
  // Log warning for debugging
  console.warn('Deal missing valid ID:', deal);
  return '';
}

/**
 * Normalize any MongoDB document ID to string
 * Generic version for other document types
 */
export function getDocumentId(doc: any): string {
  if (typeof doc._id === 'object' && doc._id && '$oid' in doc._id) {
    return doc._id.$oid;
  }
  
  if (typeof doc._id === 'string' && doc._id) {
    return doc._id;
  }
  
  if (typeof doc.id === 'string' && doc.id) {
    return doc.id;
  }
  
  console.warn('Document missing valid ID:', doc);
  return '';
}

/**
 * Validate that an ID is a valid MongoDB ObjectId format
 */
export function isValidObjectId(id: string): boolean {
  return /^[0-9a-fA-F]{24}$/.test(id);
}

/**
 * Ensure deal has a normalized id field for frontend use
 * Mutates the deal object to add/update the id field
 */
export function normalizeDealId(deal: DealWithId): DealWithId {
  const normalizedId = getDealId(deal);
  
  if (normalizedId) {
    // Ensure the deal has an id field for frontend consistency
    deal.id = normalizedId;
  }
  
  return deal;
}

/**
 * Normalize an array of deals to ensure all have proper id fields
 */
export function normalizeDealsIds(deals: DealWithId[]): DealWithId[] {
  return deals.map(normalizeDealId);
}
