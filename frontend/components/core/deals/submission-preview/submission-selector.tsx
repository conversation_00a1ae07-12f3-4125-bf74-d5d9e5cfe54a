"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ChevronDown, FileText, Calendar } from "lucide-react"
import { cn } from "@/lib/utils"
import { SubmissionPreview } from "@/lib/types/submission-preview"

interface SubmissionSelectorProps {
  submissions: SubmissionPreview[]
  selectedSubmissionId: string
  onSubmissionChange: (submissionId: string) => void
}

export function SubmissionSelector({ 
  submissions, 
  selectedSubmissionId, 
  onSubmissionChange 
}: SubmissionSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  
  const selectedSubmission = submissions.find(s => s.submission_id === selectedSubmissionId)
  
  if (!selectedSubmission) {
    return null
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const handleSelect = (submissionId: string) => {
    onSubmissionChange(submissionId)
    setIsOpen(false)
  }

  return (
    <div className="relative">
      {/* Trigger Button */}
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "h-auto w-full justify-between gap-3 rounded-xl border-gray-200/40 bg-white/95 px-4 py-3 shadow-lg backdrop-blur-sm transition-all duration-200 hover:bg-gray-50/80 hover:shadow-xl sm:min-w-[320px]",
          isOpen && "border-blue-300/60 ring-2 ring-blue-500/20"
        )}
      >
        <div className="flex min-w-0 items-center gap-3">
          <div className="flex size-10 shrink-0 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-blue-600">
            <FileText className="size-5 text-white" />
          </div>
          <div className="min-w-0 text-left">
            <div className="truncate text-base font-semibold text-gray-900">
              {selectedSubmission.form_name}
            </div>
            <div className="text-sm text-gray-600">
              v{selectedSubmission.form_version} • {formatDate(selectedSubmission.submitted_at)}
            </div>
          </div>
        </div>
        <ChevronDown className={cn(
          "size-4 shrink-0 text-gray-500 transition-transform",
          isOpen && "rotate-180"
        )} />
      </Button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 z-10" 
              onClick={() => setIsOpen(false)}
            />
            
            {/* Dropdown Content */}
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="bg-white/98 absolute inset-x-0 top-full z-20 mt-3 w-full overflow-hidden rounded-2xl border border-gray-200/40 shadow-2xl backdrop-blur-xl sm:left-auto sm:right-0 sm:w-96"
            >
              <div className="max-h-64 space-y-1 overflow-y-auto p-2">
                {submissions.map((submission, index) => {
                  const isSelected = submission.submission_id === selectedSubmissionId
                  
                  return (
                    <motion.button
                      key={submission.submission_id}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      onClick={() => handleSelect(submission.submission_id)}
                      className={cn(
                        "w-full rounded-xl p-4 text-left transition-all duration-200 hover:bg-gray-50/80 hover:shadow-md",
                        isSelected && "border border-blue-200/60 bg-gradient-to-r from-blue-50/80 to-blue-100/60 shadow-sm"
                      )}
                    >
                      <div className="flex items-start justify-between gap-3">
                        <div className="flex min-w-0 flex-1 items-center gap-3">
                          <div className="flex size-10 shrink-0 items-center justify-center rounded-lg bg-gradient-to-br from-green-500 to-emerald-600">
                            <FileText className="size-5 text-white" />
                          </div>
                          <div className="min-w-0 flex-1">
                            <div className="mb-1 flex items-center gap-2">
                              <div className="truncate font-semibold text-gray-900">
                                {submission.form_name}
                              </div>
                              <Badge variant="secondary" className="shrink-0 bg-gray-100 text-xs text-gray-700">
                                v{submission.form_version}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <Calendar className="size-3" />
                              <span>Submitted {formatDate(submission.submitted_at)}</span>
                            </div>
                            <div className="mt-1 text-sm text-gray-500">
                              {submission.sections.length} sections
                            </div>
                          </div>
                        </div>
                        
                        {isSelected && (
                          <div className="shrink-0">
                            <div className="size-2 rounded-full bg-blue-600" />
                          </div>
                        )}
                      </div>
                    </motion.button>
                  )
                })}
              </div>
              
              {/* Footer */}
              <div className="border-t border-gray-200/60 bg-gray-50/50 p-3">
                <div className="text-center text-xs text-gray-500">
                  {submissions.length} submission{submissions.length !== 1 ? 's' : ''} available
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  )
}
