"use client"

import React, { useState } from 'react';
import { Plus, Trash2, GripVertical, HelpCircle, Settings, CheckSquare } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';

import { Question, QuestionType, ValidationRule, QuestionOption, QUESTION_TYPES, isCoreFieldQuestion, getCoreFieldDisplayName, CoreFieldType } from '@/lib/types/form';

interface QuestionConfigProps {
  question: Question;
  onUpdate: (updates: Partial<Question>) => void;
}

export function QuestionConfig({ question, onUpdate }: QuestionConfigProps) {
  const [localQuestion, setLocalQuestion] = useState<Question>(question);

  const questionTypeInfo = QUESTION_TYPES.find(t => t.type === question.type);

  const handleUpdate = (field: keyof Question, value: any) => {
    const updated = { ...localQuestion, [field]: value };
    setLocalQuestion(updated);
    onUpdate({ [field]: value });
  };

  const handleValidationUpdate = (field: keyof ValidationRule, value: any) => {
    const currentValidation = localQuestion.validation || {};
    const updatedValidation = { ...currentValidation, [field]: value };
    handleUpdate('validation', updatedValidation);
  };

  const handleAddOption = () => {
    const currentOptions = localQuestion.options || [];
    const optionNumber = currentOptions.length + 1;
    const label = `Option ${optionNumber}`;
    const newOption: QuestionOption = {
      label: label,
      value: label.toLowerCase().replace(/\s+/g, '_').replace(/-/g, '_')
    };
    handleUpdate('options', [...currentOptions, newOption]);
  };

  const handleUpdateOption = (index: number, field: keyof QuestionOption, value: string) => {
    const currentOptions = [...(localQuestion.options || [])];
    if (field === 'label') {
      // Auto-generate value from label
      const autoValue = value.toLowerCase().replace(/\s+/g, '_').replace(/-/g, '_');
      currentOptions[index] = {
        ...currentOptions[index],
        label: value,
        value: autoValue
      };
    } else {
      currentOptions[index] = { ...currentOptions[index], [field]: value };
    }
    // Always create a new array reference and trigger onUpdate with the full array
    setLocalQuestion(q => ({ ...q, options: [...currentOptions] }));
    onUpdate({ options: [...currentOptions] });
  };

  const handleRemoveOption = (index: number) => {
    const currentOptions = [...(localQuestion.options || [])];
    
    // Prevent removing all options for core select fields
    if (isCoreFieldQuestion(localQuestion) && 
        (localQuestion.core_field === CoreFieldType.STAGE || localQuestion.core_field === CoreFieldType.SECTOR) &&
        currentOptions.length <= 1) {
      alert("At least one option is required for core field.");
      return;
    }
    
    currentOptions.splice(index, 1);
    handleUpdate('options', currentOptions);
  };

  const renderBasicFields = () => (
    <Card className="rounded-xl border border-muted/20 bg-white shadow-sm">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-3 text-lg font-semibold">
          <HelpCircle className="size-5 text-primary" />
          Basic Question Settings
        </CardTitle>
        <CardDescription className="text-sm leading-relaxed text-muted-foreground">
          Configure the question label, help text, type, and requirements
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {isCoreFieldQuestion(localQuestion) && (
          <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
            <div className="mb-2 flex items-center gap-2">
              <CheckSquare className="size-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">
                Core Dashboard Field: {getCoreFieldDisplayName(localQuestion.core_field!)}
              </span>
            </div>
            <p className="text-xs text-blue-700">
              This question is required for dashboard functionality. You can edit the label, help text, and options, but cannot delete or change the core field type.
            </p>
          </div>
        )}
        <div className="space-y-3">
          <Label htmlFor="question-label" className="text-sm font-medium">Question Label *</Label>
          <Input
            id="question-label"
            value={localQuestion.label}
            onChange={(e) => handleUpdate('label', e.target.value)}
            placeholder="Enter your question"
            className="mb-4 mt-2 min-h-12 w-full leading-relaxed"
          />
        </div>
        <div className="space-y-3">
          <Label htmlFor="question-help" className="text-sm font-medium">Help Text</Label>
          <Textarea
            id="question-help"
            value={localQuestion.help_text || ''}
            onChange={(e) => handleUpdate('help_text', e.target.value)}
            placeholder="Additional guidance for users (optional)"
            className="mb-4 mt-2 min-h-12 w-full leading-relaxed"
          />
        </div>
        <div className="space-y-3">
          <Label htmlFor="question-type" className="text-sm font-medium">
            Question Type
            {isCoreFieldQuestion(localQuestion) && (
              <span className="ml-2 text-xs text-muted-foreground">
                (Restricted for core field)
              </span>
            )}
          </Label>
          <Select
            value={localQuestion.type}
            onValueChange={(value) => {
              // Validate core field type restrictions
              if (isCoreFieldQuestion(localQuestion)) {
                const newType = value as QuestionType;
                if (localQuestion.core_field === CoreFieldType.COMPANY_NAME && newType !== QuestionType.SHORT_TEXT) {
                  alert("Company name core field must be of type short_text");
                  return;
                }
                if (localQuestion.core_field === CoreFieldType.STAGE && newType !== QuestionType.SINGLE_SELECT) {
                  alert("Stage core field must be of type single_select");
                  return;
                }
                if (localQuestion.core_field === CoreFieldType.SECTOR && 
                    newType !== QuestionType.SINGLE_SELECT && newType !== QuestionType.MULTI_SELECT) {
                  alert("Sector core field must be of type single_select or multi_select");
                  return;
                }
              }
              handleUpdate('type', value as QuestionType);
            }}
            disabled={isCoreFieldQuestion(localQuestion)}
          >
            <SelectTrigger className="mb-4 mt-2 min-h-12 w-full">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {QUESTION_TYPES.map((type) => (
                <SelectItem key={type.type} value={type.type}>
                  <div className="flex items-center gap-2 text-left">
                    <span>{type.label}</span>
                    <span className="text-xs text-muted-foreground">({type.description})</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {isCoreFieldQuestion(localQuestion) && (
            <p className="text-xs text-muted-foreground">
              Core field type cannot be changed. This ensures dashboard compatibility.
            </p>
          )}
        </div>
        <div className="flex items-center justify-between rounded-lg border bg-muted/30 p-6">
          <div className="space-y-1">
            <Label className="text-sm font-medium">Required Question</Label>
            <p className="text-xs leading-relaxed text-muted-foreground">
              Users must answer this question
            </p>
          </div>
          <Switch
            checked={localQuestion.required}
            onCheckedChange={(checked) => handleUpdate('required', checked)}
          />
        </div>
      </CardContent>
    </Card>
  );

  const renderValidationFields = () => {
    if (!questionTypeInfo?.supportsValidation) return null;

    const validation = localQuestion.validation || {};

    return (
      <Card className="rounded-xl border border-muted/20 bg-white shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-lg font-semibold">
            <Settings className="size-5 text-primary" />
            Validation Rules
          </CardTitle>
          <CardDescription className="text-sm leading-relaxed text-muted-foreground">
            Set constraints and validation rules for this question
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {(question.type === QuestionType.NUMBER || question.type === QuestionType.RANGE) && (
            <>
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="min-value" className="text-sm font-medium">Minimum Value</Label>
                  <Input
                    id="min-value"
                    type="number"
                    value={validation.min || ''}
                    onChange={(e) => handleValidationUpdate('min', e.target.value ? parseFloat(e.target.value) : undefined)}
                    placeholder="No minimum"
                    className="mb-4 mt-2 min-h-12 w-full leading-relaxed"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="max-value" className="text-sm font-medium">Maximum Value</Label>
                  <Input
                    id="max-value"
                    type="number"
                    value={validation.max || ''}
                    onChange={(e) => handleValidationUpdate('max', e.target.value ? parseFloat(e.target.value) : undefined)}
                    placeholder="No maximum"
                    className="mb-4 mt-2 min-h-12 w-full leading-relaxed"
                  />
                </div>
              </div>
              <Separator className="my-2" />
            </>
          )}

          {(question.type === QuestionType.SHORT_TEXT || question.type === QuestionType.LONG_TEXT) && (
            <>
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="min-length">Minimum Length</Label>
                  <Input
                    id="min-length"
                    type="number"
                    value={validation.min || ''}
                    onChange={(e) => handleValidationUpdate('min', e.target.value ? parseFloat(e.target.value) : undefined)}
                    placeholder="No minimum"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max-length">Maximum Length</Label>
                  <Input
                    id="max-length"
                    type="number"
                    value={validation.max || ''}
                    onChange={(e) => handleValidationUpdate('max', e.target.value ? parseFloat(e.target.value) : undefined)}
                    placeholder="No maximum"
                  />
                </div>
              </div>
              <Separator className="my-2" />
              <div className="space-y-2">
                <Label htmlFor="regex-pattern">Pattern (Regex)</Label>
                <Input
                  id="regex-pattern"
                  value={validation.regex || ''}
                  onChange={(e) => handleValidationUpdate('regex', e.target.value || undefined)}
                  placeholder="e.g., ^[A-Za-z]+$ for letters only"
                />
                <p className="text-xs text-muted-foreground">
                  Use regular expressions to validate the input format
                </p>
              </div>
            </>
          )}

          {question.type === QuestionType.DATE && (
            <>
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="min-date">Minimum Date</Label>
                  <Input
                    id="min-date"
                    type="date"
                    value={validation.min || ''}
                    onChange={(e) => handleValidationUpdate('min', e.target.value || undefined)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max-date">Maximum Date</Label>
                  <Input
                    id="max-date"
                    type="date"
                    value={validation.max || ''}
                    onChange={(e) => handleValidationUpdate('max', e.target.value || undefined)}
                  />
                </div>
              </div>
              <Separator className="my-2" />
            </>
          )}

          {question.type === QuestionType.FILE && (
            <>
              <div className="space-y-2">
                <Label htmlFor="max-file-size">Maximum File Size (MB)</Label>
                <Input
                  id="max-file-size"
                  type="number"
                  value={validation.max || ''}
                  onChange={(e) => handleValidationUpdate('max', e.target.value ? parseFloat(e.target.value) : undefined)}
                  placeholder="e.g., 10"
                />
              </div>
              <Separator className="my-2" />
              <div className="space-y-2">
                <Label htmlFor="file-types">Allowed File Types</Label>
                <Input
                  id="file-types"
                  value={validation.regex || ''}
                  onChange={(e) => handleValidationUpdate('regex', e.target.value || undefined)}
                  placeholder="e.g., .pdf,.doc,.docx"
                />
                <p className="text-xs text-muted-foreground">
                  Comma-separated list of allowed file extensions
                </p>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    );
  };

  const renderOptionsFields = () => {
    if (!questionTypeInfo?.hasOptions) return null;

    const options = localQuestion.options || [];

    return (
      <Card className="rounded-xl border border-muted/20 bg-white shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-lg font-semibold">
            <CheckSquare className="size-5 text-primary" />
            Answer Options
          </CardTitle>
          <CardDescription className="text-sm leading-relaxed text-muted-foreground">
            Configure the available options for this question
            {isCoreFieldQuestion(localQuestion) && 
             (localQuestion.core_field === CoreFieldType.STAGE || localQuestion.core_field === CoreFieldType.SECTOR) && (
              <span className="mt-1 block text-xs text-blue-600">
                Note: At least one option is required for core dashboard fields.
              </span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {options.length === 0 ? (
            <div className="py-6 text-center text-muted-foreground">
              <p className="text-sm">No options added yet</p>
            </div>
          ) : (
            <div className="space-y-3">
              {options.map((option, index) => (
                <div key={index} className="flex items-center gap-3 rounded-lg border p-3">
                  <GripVertical className="size-4 cursor-grab text-muted-foreground" />
                  <div className="flex-1">
                    <div className="space-y-1">
                      <Label className="text-xs">Display Label</Label>
                      <Input
                        value={option.label}
                        onChange={(e) => handleUpdateOption(index, 'label', e.target.value)}
                        placeholder="Option label"
                      />
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveOption(index)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="size-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}

          <Button
            variant="outline"
            onClick={handleAddOption}
            className="w-full"
          >
            <Plus className="size-4" />
          </Button>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {renderBasicFields()}

      <Separator />

      {renderValidationFields()}

      {renderOptionsFields()}
    </div>
  );
}
