# Trigger System

An event-driven trigger system for executing actions in response to events.

## Features

- Multiple trigger types (email, in-app, thesis form, webhook)
- Conditional execution based on event data
- Retry mechanisms for failed triggers
- Execution history and monitoring
- Extensible plugin architecture

## Usage

### Creating a Trigger Configuration

```python
from app.services.factory import get_trigger_service
from app.services.trigger.interfaces import TriggerType

async def create_trigger_example():
    trigger_service = await get_trigger_service()
    
    # Create email notification trigger
    email_trigger = await trigger_service.create_trigger_config(
        resource_type="form",
        resource_id="form123",
        org_id="org456",
        type=TriggerType.EMAIL_NOTIFICATION,
        name="New Submission Notification",
        description="Send email when a new submission is received",
        enabled=True,
        config={
            "template_id": "new_submission",
            "recipient_field": "email"
        },
        conditions={
            "event_types": ["submission"],
            "fields": {
                "score": {"gte": 80}
            }
        }
    )
    
    # Create webhook trigger
    webhook_trigger = await trigger_service.create_trigger_config(
        resource_type="form",
        resource_id="form123",
        org_id="org456",
        type=TriggerType.WEBHOOK,
        name="CRM Integration",
        description="Send submission data to CRM",
        enabled=True,
        config={
            "url": "https://api.crm.example.com/webhook",
            "method": "POST",
            "headers": {"Authorization": "Bearer token123"},
            "sign_payload": True,
            "secret": "webhook_secret"
        },
        conditions={
            "event_types": ["submission"]
        }
    )
    
    return [email_trigger, webhook_trigger]
```

### Processing Events

```python
async def process_event_example():
    trigger_service = await get_trigger_service()
    
    # Process a form submission event
    executions = await trigger_service.process_resource_event(
        resource_type="form",
        resource_id="form123",
        event_type="submission",
        event_data={
            "submission_id": "sub789",
            "email": "<EMAIL>",
            "score": 85,
            "answers": {"question1": "answer1"}
        }
    )
    
    return executions
```

### Getting Execution History

```python
async def get_history_example():
    trigger_service = await get_trigger_service()
    
    # Get execution history for a resource
    history = await trigger_service.get_execution_history(
        resource_type="form",
        resource_id="form123",
        limit=100
    )
    
    return history
```

### Retrying Failed Executions

```python
async def retry_execution_example(execution_id):
    trigger_service = await get_trigger_service()
    
    # Retry a failed execution
    execution = await trigger_service.retry_execution(execution_id)
    
    return execution
```

## Creating Custom Trigger Processors

You can extend the trigger system with custom processors:

```python
from app.services.trigger.interfaces import TriggerType
from app.services.factory import get_trigger_service

async def custom_processor(config, event_data):
    # Process the trigger
    # ...
    
    return {
        "success": True,
        "result": "Custom processing completed"
    }

async def register_custom_processor():
    trigger_service = await get_trigger_service()
    
    # Register custom processor
    trigger_service.register_processor(
        TriggerType.CUSTOM_TYPE,
        custom_processor
    )
```

## Architecture

The trigger system consists of:

1. **Trigger Configuration** - Settings for when and how to execute triggers
2. **Event Processing** - Matching events to triggers and executing them
3. **Processors** - Functions that handle specific trigger types
4. **Execution History** - Record of trigger executions and results

The system is designed to be extensible and can be used for any type of event-driven workflow in the application.
