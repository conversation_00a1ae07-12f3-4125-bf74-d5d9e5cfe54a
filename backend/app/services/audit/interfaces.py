from typing import Optional, List
from abc import ABC, abstractmethod

from app.models.audit import AuditLog

class AuditServiceInterface(ABC):
    """Interface for audit services."""
    
    @abstractmethod
    async def log_action(
        self,
        audit_log: AuditLog
    ) -> AuditLog:
        """Log an action."""
        pass

    @abstractmethod
    async def get_user_logs(
        self,
        user_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[AuditLog]:
        """Get audit logs for a user."""
        pass

    @abstractmethod
    async def get_entity_logs(
        self,
        entity_type: str,
        entity_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[AuditLog]:
        """Get audit logs for an entity."""
        pass

    @abstractmethod
    async def search_logs(
        self,
        action: Optional[str] = None,
        entity_type: Optional[str] = None,
        user_id: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[AuditLog]:
        """Search audit logs with filters."""
        pass 