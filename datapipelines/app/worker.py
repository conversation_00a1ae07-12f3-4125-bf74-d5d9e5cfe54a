"""
TractionX Data Pipeline Service - Custom Worker

This module implements a custom worker process that handles background tasks
using the custom Redis queue system, following patterns from the backend queue system.
"""

import asyncio
import importlib
import inspect
import json
import os
import signal
import sys
import time
import traceback
from typing import Any, Callable, Dict, List, Optional, Union

import redis.asyncio as redis  # type: ignore
from redis.asyncio import Redis  # type: ignore

from app.configs import get_logger, settings
from app.tasks import get_job_handlers

# Configure logging
logger = get_logger(__name__)


class CustomWorker:
    """Custom worker for processing jobs from the custom queue system."""

    def __init__(self, redis_url: Optional[str] = None):
        self.redis_url = redis_url or settings.redis_connection_string
        self._connection: Optional[Redis] = None
        self.running = False
        self.handlers = get_job_handlers()
        self.processing_tasks = set()

    @property
    async def connection(self) -> Redis:
        """Get Redis connection (lazy initialization)."""
        if not self._connection:
            self._connection = redis.from_url(self.redis_url, decode_responses=True)
        return self._connection

    def register_handler(self, job_type: str, handler: Callable) -> None:
        """Register a handler for a job type."""
        self.handlers[job_type] = handler
        logger.info(f"Registered handler for job type: {job_type}")

    def get_handler(self, job_type: str) -> Optional[Callable]:
        """Get handler for a job type."""
        return self.handlers.get(job_type)

    async def get_job_from_queue(self, queue_name: str) -> Optional[str]:
        """Get a job from the specified queue."""
        queue_key = f"queue:{queue_name}"
        try:
            conn = await self.connection
            # Use BRPOP to block until a job is available
            result = await conn.brpop(queue_key, timeout=1)  # type: ignore
            if result:
                return result[1]  # Return the job ID
            return None
        except Exception as e:
            logger.error(f"Error getting job from queue {queue_name}: {e}")
            return None

    async def get_job_data(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get job data from Redis."""
        job_key = f"job:{job_id}"
        try:
            conn = await self.connection
            job_data = await conn.hgetall(job_key)  # type: ignore

            if not job_data:
                return None

            # Parse JSON fields
            for key in ["data", "result", "error"]:
                if key in job_data and job_data[key]:
                    try:
                        job_data[key] = json.loads(job_data[key])
                    except json.JSONDecodeError:
                        pass

            return job_data
        except Exception as e:
            logger.error(f"Error getting job data for {job_id}: {e}")
            return None

    async def update_job_status(self, job_id: str, status: str, **updates) -> None:
        """Update job status in Redis."""
        job_key = f"job:{job_id}"
        try:
            conn = await self.connection

            # Prepare updates
            update_data: Dict[str, str] = {"status": status}
            for key, value in updates.items():
                if isinstance(value, (dict, list)):
                    update_data[key] = json.dumps(value)
                else:
                    update_data[key] = str(value)

            # Update each field individually to avoid type issues
            for key, value in update_data.items():
                await conn.hset(job_key, key, value)  # type: ignore

        except Exception as e:
            logger.error(f"Error updating job status for {job_id}: {e}")

    async def _fail_job(
        self, job_id: str, error: Union[str, Exception], retry: bool = True
    ) -> None:
        """Handle job failure with proper error logging and status update."""
        error_message = str(error) if isinstance(error, Exception) else error

        logger.error(f"Job {job_id} failed: {error_message}")
        if isinstance(error, Exception):
            logger.error(traceback.format_exc())

        await self.update_job_status(
            job_id, "failed", completed_at=time.time(), error=error_message, retry=retry
        )

    async def _process_job(self, job_id: str, job_data: Dict[str, Any]) -> None:
        """Process a job with proper error handling and function resolution."""
        start_time = time.time()

        try:
            function_name = job_data.get("job_func")
            data = job_data.get("job_args", {})

            if not function_name:
                await self._fail_job(
                    job_id, "No function name specified in job data", retry=False
                )
                return

            logger.info(f"Processing job {job_id}: {function_name}")

            # Update job status to running
            await self.update_job_status(job_id, "running", started_at=time.time())

            # Get the handler function
            handler = self._resolve_handler(function_name)
            if not handler:
                await self._fail_job(
                    job_id,
                    f"No handler found for function: {function_name}",
                    retry=False,
                )
                return

            # Execute the handler
            result = await self._execute_handler(handler, data)

            # Update job status to completed
            duration = time.time() - start_time
            await self.update_job_status(
                job_id,
                "completed",
                completed_at=time.time(),
                result=result,
                execution_time=duration,
            )

            logger.info(f"Job {job_id} completed successfully in {duration:.2f}s")

        except Exception as e:
            duration = time.time() - start_time
            await self._fail_job(job_id, e, retry=True)

    def _resolve_handler(self, function_name: str) -> Optional[Callable]:
        """Resolve handler function from function name."""
        # First try to get from registered handlers (simple names)
        if function_name in self.handlers:
            handler = self.handlers[function_name]
            logger.info(f"Found handler in registered handlers: {function_name}")
            return handler

        # Try to import the function dynamically
        try:
            if "." in function_name:
                # Full module path like "app.tasks.founder_enrichment.enrich_founder_data_sync"
                module_path, func_name_part = function_name.rsplit(".", 1)
                logger.info(
                    f"Importing from module: {module_path}, function: {func_name_part}"
                )
                module = importlib.import_module(module_path)
                handler = getattr(module, func_name_part)
                logger.info(
                    f"Successfully imported function: {func_name_part} from {module_path}"
                )
                return handler
            else:
                # Simple name, try to find in job handlers
                if function_name in self.handlers:
                    handler = self.handlers[function_name]
                    logger.info(f"Found handler in job handlers: {function_name}")
                    return handler
                else:
                    logger.error(f"Function {function_name} not found in job handlers")
                    return None
        except Exception as e:
            logger.error(f"Could not import function {function_name}: {e}")
            return None

    async def _execute_handler(self, handler: Callable, data: Dict[str, Any]) -> Any:
        """Execute handler function with proper async/sync handling."""
        # Check if function is async
        if inspect.iscoroutinefunction(handler):
            # Execute async function directly
            logger.info(f"Executing async function: {handler.__name__}")
            result = await handler(data)
        else:
            # Execute sync function in executor
            logger.info(f"Executing sync function: {handler.__name__}")
            loop = asyncio.get_running_loop()
            result = await loop.run_in_executor(None, handler, data)

        return result

    async def process_queue(self, queue_name: str) -> None:
        """Process jobs from a specific queue."""
        logger.info(f"Processing queue: {queue_name}")

        while self.running:
            try:
                # Get a job from the queue
                job_id = await self.get_job_from_queue(queue_name)

                if not job_id:
                    continue

                # Get job data
                job_data = await self.get_job_data(job_id)

                if not job_data:
                    logger.warning(f"Could not get data for job {job_id}")
                    continue

                # Create a task to process the job
                task = asyncio.create_task(self._process_job(job_id, job_data))
                self.processing_tasks.add(task)
                task.add_done_callback(self.processing_tasks.discard)

            except Exception as e:
                logger.error(f"Error processing queue {queue_name}: {e}")
                await asyncio.sleep(1)  # Wait before retrying

    async def start(self, queues: Optional[List[str]] = None) -> None:
        """Start the worker."""
        if queues is None:
            queues = ["high_priority", "default", "low_priority"]

        self.running = True
        logger.info(f"Starting custom worker with queues: {queues}")

        # Create tasks for each queue
        tasks = [self.process_queue(queue) for queue in queues]

        # Run all queue processors concurrently
        await asyncio.gather(*tasks)

    async def stop(self) -> None:
        """Stop the worker."""
        if not self.running:
            return

        logger.info("Stopping worker...")
        self.running = False

        # Wait for processing tasks to complete
        if self.processing_tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*self.processing_tasks, return_exceptions=True),
                    timeout=30,
                )
            except asyncio.TimeoutError:
                logger.warning("Some tasks did not complete within timeout")

        if self._connection:
            await self._connection.close()
            self._connection = None


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    logger.info(f"Received signal {signum}, shutting down worker...")
    sys.exit(0)


async def main():
    """Main worker function."""
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    logger.info("Starting TractionX Data Pipeline Custom Worker")
    logger.info(f"Redis URL: {settings.REDIS_HOST}")
    logger.info(f"Environment: {settings.ENVIRONMENT}")

    # Debug: Show current working directory and Python path
    logger.info(f"Current working directory: {os.getcwd()}")
    logger.info(f"Python path: {sys.path[:3]}...")  # Show first 3 entries

    try:
        # Create and start worker
        worker = CustomWorker()

        # Test Redis connection
        conn = await worker.connection
        await conn.ping()
        logger.info("Redis connection successful")

        logger.info("Custom worker initialized")
        logger.info(f"Registered handlers: {list(worker.handlers.keys())}")
        logger.info("Available queues: high_priority, default, low_priority")
        logger.info("Listening for jobs...")

        # Start processing jobs
        await worker.start()

    except KeyboardInterrupt:
        logger.info("Worker interrupted by user")
    except Exception as e:
        logger.error(f"Worker failed to start: {e}")
        sys.exit(1)
    finally:
        logger.info("Worker shutdown complete")


if __name__ == "__main__":
    asyncio.run(main())
