# TractionX AI Agent Foundation Configuration

# API Settings
API_V1_STR=/api/ai
PROJECT_NAME=TractionX AI Agents
VERSION=1.0.0
DESCRIPTION=TractionX AI Agent Foundation

# Security (inherit from backend)
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# MongoDB (hosted)
MONGODB_URL=mongodb+srv://your-mongodb-atlas-url
MONGODB_DB_NAME=tx_ai

# Qdrant Vector Database (hosted)
QDRANT_URL=https://your-qdrant-cloud-instance.cloud.qdrant.io
QDRANT_API_KEY=your_qdrant_api_key
QDRANT_TIMEOUT=30
QDRANT_VECTOR_SIZE=1536

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.7

# Lang<PERSON>hain Configuration
LANGCHAIN_TRACING_V2=false
LANGCHAIN_API_KEY=your_langchain_api_key
LANGCHAIN_PROJECT=tractionx-ai-agents

# Web Search API Keys
NEWSAPI_KEY=your_newsapi_key
SERPAPI_KEY=your_serpapi_key
GNEWS_KEY=your_gnews_key
BING_NEWS_API_KEY=your_bing_news_api_key
TWITTER_API_KEY=your_twitter_api_key

# News Intelligence Configuration
NEWS_SEARCH_TIMEOUT=8
NEWS_MAX_SOURCES=3
NEWS_DEDUP_THRESHOLD=0.8

# Agent Configuration
AGENT_TIMEOUT=300
MAX_AGENT_ITERATIONS=10
AGENT_MEMORY_SIZE=100

# Audit Configuration
AUDIT_ENABLED=true
AUDIT_LOG_INPUTS=true
AUDIT_LOG_OUTPUTS=true
AUDIT_LOG_TOOLS=true

# Redis Configuration (hosted)
REDIS_URL=redis://username:password@your-redis-cloud-instance:port

# Development/Testing
TEST_MODE=false
DEBUG=false
LOG_LEVEL=INFO

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000","http://localhost:8080"]
