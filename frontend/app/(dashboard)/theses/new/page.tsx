"use client"

import { Shell } from "@/components/shell"
import { ThesisBuilder } from "@/components/core/thesis-builder/thesis-builder"
import { useAuth } from "@/lib/auth-context"

export default function NewThesisPage() {
  const { isAuthenticated } = useAuth()

  if (!isAuthenticated) {
    return null // This will be handled by middleware
  }

  return (
    <Shell>
      <ThesisBuilder />
    </Shell>
  )
}
