"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { TrendingUp, TrendingDown, Zap, CheckCircle, XCircle, AlertTriangle } from "lucide-react"
import { cn } from "@/lib/utils"
import { visualRetreat, mobileRetreat } from "@/lib/utils/responsive"
import { BonusScore } from "@/lib/types/deal"
import { useMemo } from "react"

interface BonusPenaltySectionProps {
  bonusScores: Record<string, BonusScore>
  className?: string
}

const getStatusIcon = (status: string | undefined) => {
  switch (status?.toLowerCase()) {
    case 'awarded':
      return CheckCircle
    case 'blocked':
    case 'failed':
      return XCircle
    default:
      return AlertTriangle
  }
}

// Helper to determine if this is a bonus or penalty or unmatched
const getRuleNature = (bonusData: any) => {
  const bonusPoints = typeof bonusData.points === 'number' ? bonusData.points : 0;
  if (bonusPoints > 0) return 'bonus';
  if (bonusPoints < 0) return 'penalty';
  return 'unmatched';
}

const getStatusColor = (bonusData: any) => {
  const nature = getRuleNature(bonusData);
  switch (nature) {
    case 'bonus':
      return 'bg-green-50/50 text-green-700 border-green-200';
    case 'penalty':
      return 'bg-red-50/50 text-red-700 border-red-200';
    default:
      return 'bg-gray-50/50 text-gray-600 border-gray-200';
  }
}

const getRuleTypeIcon = (bonusData: any) => {
  const nature = getRuleNature(bonusData);
  if (nature === 'bonus') return TrendingUp;
  if (nature === 'penalty') return TrendingDown;
  return AlertTriangle;
}

const getRuleTypeColor = (bonusData: any) => {
  const nature = getRuleNature(bonusData);
  if (nature === 'bonus') return 'bg-green-100 text-green-700 border-green-200';
  if (nature === 'penalty') return 'bg-red-100 text-red-700 border-red-200';
  return 'bg-gray-100 text-gray-600 border-gray-200';
}

export function BonusPenaltySection({ bonusScores, className }: BonusPenaltySectionProps) {
  const bonusEntries = Object.entries(bonusScores)
  // Get all question_scores from the global deal context if available
  // We'll assume window.__TX_QUESTION_SCORES__ is set by the parent page, or pass as prop if needed
  const questionScores = (typeof window !== 'undefined' && (window as any).__TX_QUESTION_SCORES__) || {}

  // Helper to resolve label/type for a question_id
  const resolveQuestionMeta = (qid?: string | null) => {
    if (!qid) return { label: '', type: '' }
    // Try to find in questionScores
    const q = questionScores[qid]
    if (q) return { label: q.question_label, type: q.question_type }
    return { label: '', type: '' }
  }
  
  if (bonusEntries.length === 0) {
    return null
  }

  // Separate bonuses and penalties
  const bonuses = bonusEntries.filter(([_, score]) => score.rule_type === 'BONUS')
  const penalties = bonusEntries.filter(([_, score]) => score.rule_type === 'PENALTY')

  // Calculate totals
  const totalBonusAwarded = bonuses
    .filter(([_, score]) => score.status === 'awarded')
    .reduce((sum, [_, score]) => sum + score.points, 0)
  
  const totalPenaltyAwarded = penalties
    .filter(([_, score]) => score.status === 'awarded')
    .reduce((sum, [_, score]) => sum + score.points, 0)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Card className={cn("border-0 shadow-sm", visualRetreat.card.base)}>
        <CardHeader className="p-6">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div>
              <CardTitle className="flex items-center gap-3 text-xl md:text-2xl">
                <Zap className="size-6 text-blue-600" />
                Bonus & Penalty Rules
              </CardTitle>
              <p className="mt-2 text-sm text-muted-foreground md:text-base">
                Additional points awarded or deducted based on specific criteria
              </p>
            </div>

            <div className="flex flex-wrap items-center gap-3">
              {totalBonusAwarded > 0 && (
                <Badge className="rounded-full border-green-200 bg-green-100 px-3 py-2 text-green-800">
                  <TrendingUp className="mr-2 size-4" />
                  +{totalBonusAwarded} pts
                </Badge>
              )}
              {totalPenaltyAwarded > 0 && (
                <Badge className="rounded-full border-red-200 bg-red-100 px-3 py-2 text-red-800">
                  <TrendingDown className="mr-2 size-4" />
                  -{totalPenaltyAwarded} pts
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6 p-6">
          {bonusEntries.map(([ruleId, bonusData], index) => {
            const StatusIcon = getStatusIcon(bonusData.status)
            const RuleTypeIcon = getRuleTypeIcon(bonusData)
            // Debug: log bonusData structure
            if (index === 0) { console.log('bonusData sample:', bonusData); }
            // Use bonus_points (canonical)
            const bonusPoints = typeof bonusData.points === 'number' ? bonusData.points : 0;
            const pointsSign = bonusPoints > 0 ? '+' : bonusPoints < 0 ? '-' : '';
            // Compose condition details (human readable)
            const conditionDetails = bonusData.condition
              ? Array.isArray(bonusData.condition.conditions)
                ? bonusData.condition.conditions.map((cond: any, i: number) => {
                    // Fallback: resolve label/type from question_scores if missing
                    let label = cond.question_label
                    let type = cond.question_type
                    if ((!label || label === 'Unknown Question') && cond.question_id) {
                      const meta = resolveQuestionMeta(cond.question_id)
                      label = meta.label || cond.question_id
                      type = meta.type
                    }
                    return (
                      <div key={i} className="ml-2 mt-1 flex flex-wrap items-center gap-2 text-xs text-gray-800">
                        <span className="font-semibold">Question:</span>
                        <span className="font-medium text-blue-900">{label}</span>
                        {type && (
                          <span className="ml-1 rounded-full bg-gray-100 px-2 py-0.5 text-[11px] text-gray-700" title="Question Type">{type}</span>
                        )}
                        {typeof cond.value !== 'undefined' && (
                          <span className="ml-2"><span className="font-semibold">Expected:</span> <span className="text-blue-700">{JSON.stringify(cond.value)}</span></span>
                        )}
                        {typeof cond.aggregation !== 'undefined' && cond.aggregation && (
                          <span className="ml-2" title="Aggregation method"><span className="font-semibold">Aggregation:</span> <span className="text-purple-700">{cond.aggregation}</span></span>
                        )}
                        {typeof cond.aggregate_threshold !== 'undefined' && cond.aggregate_threshold !== null && (
                          <span className="ml-2" title="Threshold for aggregation"><span className="font-semibold">Threshold:</span> <span className="text-orange-700">{cond.aggregate_threshold}</span></span>
                        )}
                      </div>
                    )
                  })
                : <div className="ml-2 mt-1 text-xs text-gray-800">{JSON.stringify(bonusData.condition)}</div>
              : null
            // Compose matched answers
            const matchedAnswers = Array.isArray(bonusData.matched_answers)
              ? bonusData.matched_answers.map((m, i) => (
                  <span key={i} className={cn("ml-1 inline-block rounded px-2 py-0.5 text-xs", m ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700")}>{String(m)}</span>
                ))
              : null
            return (
              <motion.div
                key={ruleId}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={cn(
                  "flex flex-col gap-4 rounded-xl border p-6",
                  "md:flex-row md:items-start md:gap-6",
                  getStatusColor(bonusData)
                )}
              >
                <div className="flex shrink-0 items-center gap-3">
                  <div className={cn(
                    "rounded-xl border p-3",
                    getRuleTypeColor(bonusData)
                  )}>
                    <RuleTypeIcon className="size-5" />
                  </div>
                  <StatusIcon className="size-5" />
                </div>

                <div className="min-w-0 flex-1">
                  <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
                    <div className="flex-1">
                      <h4 className="text-base font-semibold md:text-lg">
                        {bonusData.rule_name || `${bonusData.rule_type || 'Rule'}`} <span className="ml-2 text-xs text-gray-400">{bonusData.rule_id}</span>
                      </h4>
                      <p className="mt-2 text-sm leading-relaxed md:text-base">
                        {bonusData.reason}
                      </p>
                      {bonusData.explanation && bonusData.explanation !== bonusData.reason && (
                        <p className="mt-3 text-xs text-muted-foreground md:text-sm">
                          {bonusData.explanation}
                        </p>
                      )}
                      {/* Show question label/id if available */}
                      {bonusData.question_id && (
                        (() => {
                          const meta = resolveQuestionMeta(bonusData.question_id)
                          return (
                            <div className="mt-2 text-xs text-gray-700">
                              <span className="font-semibold">Primary Question:</span> <span className="font-medium text-blue-900">{meta.label || bonusData.question_id}</span>
                              {meta.type && (
                                <span className="ml-2 rounded-full bg-gray-100 px-2 py-0.5 text-[11px] text-gray-700" title="Question Type">{meta.type}</span>
                              )}
                            </div>
                          )
                        })()
                      )}
                      {/* Show condition details */}
                      {conditionDetails}
                      {/* Show matched answers */}
                      {matchedAnswers && (
                        <div className="mt-2 text-xs text-gray-700">
                          <span className="font-semibold">Matched:</span> {matchedAnswers}
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-3 md:flex-col md:items-end">
                      <Badge
                        variant="outline"
                        className={cn(
                          "rounded-full px-3 py-2 text-sm font-bold",
                          bonusData.status === 'awarded'
                            ? pointsSign === '+'
                              ? "border-green-300 text-green-700"
                              : pointsSign === '-'
                                ? "border-red-300 text-red-700"
                                : "border-gray-300 text-gray-600"
                            : "border-gray-300 text-gray-600"
                        )}
                      >
                        {pointsSign}{Math.abs(bonusPoints)} pts
                      </Badge>

                      <Badge
                        variant="secondary"
                        className="rounded-full px-3 py-1 text-sm capitalize"
                      >
                        {bonusData.status}
                      </Badge>
                    </div>
                  </div>
                </div>
              </motion.div>
            )
          })}
          
          {/* Summary */}
          {(totalBonusAwarded > 0 || totalPenaltyAwarded > 0) && (
            <div className="border-t border-gray-200 pt-6">
              <div className="flex items-center justify-between text-base md:text-lg">
                <span className="font-medium text-gray-700">Net Impact:</span>
                <span className={cn(
                  "text-lg font-bold md:text-xl",
                  (totalBonusAwarded - totalPenaltyAwarded) > 0
                    ? "text-green-600"
                    : (totalBonusAwarded - totalPenaltyAwarded) < 0
                      ? "text-red-600"
                      : "text-gray-600"
                )}>
                  {totalBonusAwarded - totalPenaltyAwarded > 0 ? '+' : ''}
                  {totalBonusAwarded - totalPenaltyAwarded} points
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}
