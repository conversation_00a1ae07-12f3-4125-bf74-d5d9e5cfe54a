"""
Redis Queue Backend Implementation with Atomic LUA Scripts

This module provides a Redis-based implementation of the queue backend interface
with atomic operations using LUA scripts to prevent race conditions.
"""

import asyncio
import json
import time
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

import redis.asyncio as redis  # type: ignore
from redis.asyncio import Redis  # type: ignore

from app.configs import get_logger
from app.queueing.interfaces import JobResult, JobStatus, QueueStats

logger = get_logger(__name__)


class RedisQueueBackend:
    """Redis-based queue backend implementation with atomic LUA scripts."""

    def __init__(self, redis_url: str, key_prefix: str = "tx_datapipelines"):
        self.redis_url = redis_url
        self.key_prefix = key_prefix
        self.redis: Optional[Redis] = None
        self._initialized = False

        # LUA script storage
        self.lua_scripts: Dict[str, str] = {}
        self.lua_script_shas: Dict[str, str] = {}

    async def initialize(self) -> None:
        """Initialize the Redis connection and load LUA scripts."""
        if self._initialized:
            return

        try:
            # Create Redis connection with proper configuration
            self.redis = redis.from_url(
                self.redis_url,
                decode_responses=True,
                socket_connect_timeout=10,
                socket_timeout=30,
                retry_on_timeout=True,
                health_check_interval=30,
            )

            # Test connection
            await self.redis.ping()

            # Load LUA scripts for atomic operations
            await self._load_lua_scripts()

            self._initialized = True
            logger.info("Redis queue backend initialized successfully with LUA scripts")

        except Exception as e:
            logger.error(f"Failed to initialize Redis backend: {e}")
            raise

    async def cleanup(self) -> None:
        """Clean up Redis connection."""
        if self.redis:
            try:
                await self.redis.close()
            except Exception as e:
                logger.warning(f"Error closing Redis connection: {e}")
            self.redis = None
        self._initialized = False
        logger.info("Redis queue backend cleaned up")

    async def _ensure_redis(self) -> Redis:
        """Ensure Redis connection is initialized and return it."""
        if not self._initialized:
            await self.initialize()
        assert self.redis is not None, "Redis connection not initialized"
        return self.redis

    def _queue_key(self, queue_name: str) -> str:
        """Get Redis key for a queue."""
        return f"{self.key_prefix}:queue:{queue_name}"

    def _job_key(self, job_id: str) -> str:
        """Get Redis key for job data."""
        return f"{self.key_prefix}:job:{job_id}"

    def _processing_key(self, queue_name: str) -> str:
        """Get Redis key for processing jobs."""
        return f"{self.key_prefix}:processing:{queue_name}"

    def _stats_key(self, queue_name: str) -> str:
        """Get Redis key for queue statistics."""
        return f"{self.key_prefix}:stats:{queue_name}"

    def _delayed_key(self, queue_name: str) -> str:
        """Get Redis key for delayed jobs."""
        return f"{self.key_prefix}:delayed:{queue_name}"

    async def _load_lua_scripts(self) -> None:
        """Load LUA scripts for atomic operations."""
        if not self.redis:
            raise RuntimeError("Redis client not initialized")

        # Atomic dequeue script - prevents race conditions
        self.lua_scripts["atomic_dequeue"] = """
            local queue_keys = {}
            local processing_keys = {}
            local num_queues = tonumber(ARGV[1])
            local current_time = ARGV[2]
            
            -- Build queue and processing keys from arguments
            for i = 1, num_queues do
                queue_keys[i] = KEYS[i]
                processing_keys[i] = KEYS[i + num_queues]
            end
            
            -- Try each queue in priority order
            for i = 1, num_queues do
                local queue_key = queue_keys[i]
                local processing_key = processing_keys[i]
                
                -- Try to pop a job from this queue
                local job_id = redis.call('RPOP', queue_key)
                
                if job_id then
                    -- Successfully got a job, move to processing atomically
                    redis.call('LPUSH', processing_key, job_id)
                    
                    -- Extract queue name from key
                    local queue_name = string.match(queue_key, ":([^:]+)$")
                    
                    return {job_id, queue_name}
                end
            end
            
            return nil  -- No jobs available in any queue
        """

        # Atomic enqueue script - handles priority and delayed jobs
        self.lua_scripts["atomic_enqueue"] = """
            local queue_key = KEYS[1]
            local delayed_key = KEYS[2]
            local stats_key = KEYS[3]
            local job_key = KEYS[4]
            
            local job_id = ARGV[1]
            local job_data = ARGV[2]
            local delay_seconds = tonumber(ARGV[3])
            local current_time = tonumber(ARGV[4])
            
            -- Store job data
            redis.call('HSET', job_key, 'data', job_data)
            redis.call('EXPIRE', job_key, 86400)  -- 24 hour expiration
            
            if delay_seconds > 0 then
                -- Add to delayed queue (sorted set with execution time as score)
                local execute_at = current_time + delay_seconds
                redis.call('ZADD', delayed_key, execute_at, job_id)
            else
                -- Add to immediate queue
                redis.call('LPUSH', queue_key, job_id)
                
                -- Update pending count
                redis.call('HINCRBY', stats_key, 'pending_count', 1)
            end
            
            return job_id
        """

        # Atomic complete job script
        self.lua_scripts["atomic_complete"] = """
            local processing_key = KEYS[1]
            local stats_key = KEYS[2]
            local job_key = KEYS[3]
            
            local job_id = ARGV[1]
            local result_data = ARGV[2]
            local completed_at = ARGV[3]
            local processing_time = ARGV[4]
            
            -- Remove from processing queue
            local removed = redis.call('LREM', processing_key, 0, job_id)
            
            if removed > 0 then
                -- Update job data with result
                redis.call('HSET', job_key, 
                    'status', 'completed',
                    'result', result_data,
                    'completed_at', completed_at,
                    'processing_time', processing_time
                )
                
                -- Update stats
                redis.call('HINCRBY', stats_key, 'running_count', -1)
                redis.call('HINCRBY', stats_key, 'completed_count', 1)
                redis.call('HINCRBY', stats_key, 'total_processed', 1)
                
                return 1
            end
            
            return 0
        """

        # Atomic fail job script with retry logic
        self.lua_scripts["atomic_fail"] = """
            local processing_key = KEYS[1]
            local job_key = KEYS[2]
            local stats_key = KEYS[3]
            local queue_key = KEYS[4]
            local delayed_key = KEYS[5]
            
            local job_id = ARGV[1]
            local error_message = ARGV[2]
            local current_time = ARGV[3]
            local should_retry = ARGV[4]
            local retry_delay = ARGV[5]
            
            -- Remove from processing queue
            local removed = redis.call('LREM', processing_key, 0, job_id)
            
            if removed > 0 then
                -- Update job data with error
                redis.call('HSET', job_key, 
                    'error_message', error_message,
                    'failed_at', current_time
                )
                
                if should_retry == '1' then
                    -- Increment retry count
                    redis.call('HINCRBY', job_key, 'retry_count', 1)
                    
                    -- Mark as retrying and re-queue
                    redis.call('HSET', job_key, 'status', 'retrying')
                    
                    if tonumber(retry_delay) > 0 then
                        -- Add to delayed queue for retry
                        local retry_at = tonumber(current_time) + tonumber(retry_delay)
                        redis.call('ZADD', delayed_key, retry_at, job_id)
                    else
                        -- Retry immediately
                        redis.call('LPUSH', queue_key, job_id)
                        redis.call('HINCRBY', stats_key, 'pending_count', 1)
                    end
                    
                    redis.call('HINCRBY', stats_key, 'running_count', -1)
                    return 1
                else
                    -- Mark as permanently failed
                    redis.call('HSET', job_key, 'status', 'failed')
                    
                    -- Update stats
                    redis.call('HINCRBY', stats_key, 'running_count', -1)
                    redis.call('HINCRBY', stats_key, 'failed_count', 1)
                    
                    return 0
                end
            end
            
            return 0
        """

        # Process delayed jobs script
        self.lua_scripts["process_delayed"] = """
            local delayed_key = KEYS[1]
            local queue_key = KEYS[2]
            local stats_key = KEYS[3]
            local current_time = tonumber(ARGV[1])
            
            -- Get jobs that are ready (score <= current_time)
            local ready_jobs = redis.call('ZRANGEBYSCORE', delayed_key, 0, current_time)
            
            local moved_count = 0
            for _, job_id in ipairs(ready_jobs) do
                -- Move to regular queue
                redis.call('LPUSH', queue_key, job_id)
                redis.call('ZREM', delayed_key, job_id)
                
                -- Update stats
                redis.call('HINCRBY', stats_key, 'pending_count', 1)
                moved_count = moved_count + 1
            end
            
            return moved_count
        """

        try:
            # Load scripts and store their SHA hashes
            for script_name, script_code in self.lua_scripts.items():
                sha = await self.redis.script_load(script_code)
                self.lua_script_shas[script_name] = sha
                logger.info(f"Loaded LUA script '{script_name}' with SHA: {sha[:8]}...")

        except Exception as e:
            logger.error(f"Failed to load LUA scripts: {e}")
            logger.warning("Will fall back to direct script execution")

    async def _execute_lua_script(
        self, script_name: str, keys: List[str], args: List[str]
    ) -> Any:
        """Execute a LUA script with fallback to direct execution."""
        redis = await self._ensure_redis()

        # Ensure all keys and args are strings
        str_keys = [str(k) for k in keys]
        str_args = [str(a) for a in args]

        try:
            # Try to use pre-loaded script SHA
            script_sha = self.lua_script_shas.get(script_name)
            if script_sha:
                try:
                    return await redis.evalsha(
                        script_sha, len(str_keys), *str_keys, *str_args
                    )
                except Exception as e:
                    logger.warning(
                        f"Failed to execute LUA script SHA {script_name}, falling back: {e}"
                    )

            # Fallback: execute script directly
            script = self.lua_scripts.get(script_name)
            if script:
                return await redis.eval(script, len(str_keys), *str_keys, *str_args)
            else:
                raise ValueError(f"LUA script '{script_name}' not found")

        except Exception as e:
            logger.error(f"Error executing LUA script '{script_name}': {e}")
            raise

    async def enqueue(
        self,
        queue_name: str,
        job_func: str,
        job_args: Dict[str, Any],
        job_id: Optional[str] = None,
        delay_seconds: int = 0,
        retry_config: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Enqueue a job to the specified queue."""
        redis = await self._ensure_redis()

        # Generate job ID if not provided
        if job_id is None:
            timestamp = int(time.time() * 1000)
            job_id = f"{queue_name}:{timestamp}:{uuid.uuid4().hex[:8]}"

        # Prepare job data
        job_data = {
            "job_id": job_id,
            "queue_name": queue_name,
            "job_func": job_func,
            "job_args": job_args,
            "status": JobStatus.PENDING.value,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "retry_count": 0,
            "max_retries": retry_config.get("max_retries", 3) if retry_config else 3,
            "retry_delay": retry_config.get("delay_seconds", 5) if retry_config else 5,
        }

        # Store job data
        await redis.hset(
            self._job_key(job_id),
            mapping={
                k: json.dumps(v) if isinstance(v, (dict, list)) else str(v)
                for k, v in job_data.items()
            },
        )

        # Add to queue (with delay if specified)
        if delay_seconds > 0:
            # Add to delayed queue (sorted set with timestamp)
            execute_at = time.time() + delay_seconds
            await redis.zadd(
                f"{self.key_prefix}:delayed:{queue_name}", {job_id: execute_at}
            )
        else:
            # Add to immediate queue
            await redis.lpush(self._queue_key(queue_name), job_id)

        # Update queue stats
        await self._increment_stat(queue_name, "pending_count")

        logger.info(f"Job {job_id} enqueued to {queue_name} queue")
        return job_id

    async def dequeue(
        self,
        queue_names: List[str],
        timeout: int = 0,
    ) -> Optional[Dict[str, Any]]:
        """Dequeue a job from the specified queues (priority order) with connection retry."""
        max_retries = 3

        for attempt in range(max_retries):
            try:
                redis = await self._ensure_redis()

                # Process any delayed jobs first
                await self._process_delayed_jobs(queue_names)

                # Try to get a job from queues in priority order
                queue_keys = [self._queue_key(name) for name in queue_names]

                if timeout > 0:
                    # Use shorter timeout to allow for retries and avoid server connection drops
                    effective_timeout = min(timeout, 10)  # Max 10 seconds per attempt
                    result = await redis.brpop(queue_keys, timeout=effective_timeout)
                else:
                    # Non-blocking pop
                    for queue_key in queue_keys:
                        job_id = await redis.rpop(queue_key)
                        if job_id:
                            result = (queue_key, job_id)
                            break
                    else:
                        return None

                if not result:
                    return None

                queue_key, job_id = result
                queue_name = queue_key.split(":")[-1]  # Extract queue name from key

                # Get job data
                job_data = await self._get_job_data(job_id)
                if not job_data:
                    logger.warning(f"Job {job_id} data not found")
                    return None

                # Move job to processing
                await redis.lpush(self._processing_key(queue_name), job_id)

                # Update job status and set started timestamp
                await self._update_job_data(
                    job_id,
                    {
                        "status": JobStatus.RUNNING.value,
                        "started_at": str(
                            int(time.time() * 1000)
                        ),  # timestamp in milliseconds
                    },
                )

                # Update stats
                await self._decrement_stat(queue_name, "pending_count")
                await self._increment_stat(queue_name, "running_count")

                logger.info(f"Job {job_id} dequeued from {queue_name} queue")
                return job_data

            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"Failed to dequeue after {max_retries} attempts: {e}")
                    raise

                logger.warning(f"Dequeue attempt {attempt + 1} failed, retrying: {e}")

                # Clean up connection on connection errors
                if "Connection closed" in str(e) or "Connection" in str(e):
                    await self.cleanup()

                await asyncio.sleep(1.0 * (2**attempt))  # Exponential backoff

        return None

    async def get_job_status(self, job_id: str) -> Optional[JobResult]:
        """Get the status of a job by ID."""
        if not self._initialized:
            await self.initialize()

        job_data = await self._get_job_data(job_id)
        if not job_data:
            return None

        return JobResult(
            job_id=job_id,
            status=JobStatus(job_data["status"]),
            result=job_data.get("result"),
            error_message=job_data.get("error_message"),
            started_at=self._parse_datetime(job_data.get("started_at")),
            completed_at=self._parse_datetime(job_data.get("completed_at")),
            processing_time=job_data.get("processing_time"),
            retry_count=job_data.get("retry_count", 0),
        )

    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a pending or running job."""
        if not self._initialized:
            await self.initialize()

        job_data = await self._get_job_data(job_id)
        if not job_data:
            return False

        status = JobStatus(job_data["status"])
        if status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
            return False  # Already finished

        queue_name = job_data["queue_name"]

        # Remove from queues
        redis = await self._ensure_redis()
        await redis.lrem(self._queue_key(queue_name), 0, job_id)
        await redis.lrem(self._processing_key(queue_name), 0, job_id)
        await redis.zrem(f"{self.key_prefix}:delayed:{queue_name}", job_id)

        # Update job status
        await self._update_job_status(job_id, JobStatus.CANCELLED)

        # Update stats
        if status == JobStatus.PENDING:
            await self._decrement_stat(queue_name, "pending_count")
        elif status == JobStatus.RUNNING:
            await self._decrement_stat(queue_name, "running_count")

        logger.info(f"Job {job_id} cancelled")
        return True

    async def retry_job(self, job_id: str, delay_seconds: int = 0) -> bool:
        """Retry a job."""
        if not self._initialized:
            await self.initialize()

        job_data = await self._get_job_data(job_id)
        if not job_data:
            logger.warning(f"Job {job_id} not found for retry")
            return False

        # JobStatus(job_data["status"])
        queue_name = job_data["queue_name"]
        retry_count = int(job_data.get("retry_count", 0))
        max_retries = int(job_data.get("max_retries", 3))

        # Allow retrying jobs in FAILED, PENDING, or COMPLETED states
        # TODO: Uncomment this when we have a way to handle retries
        # if status not in [JobStatus.FAILED, JobStatus.PENDING, JobStatus.COMPLETED]:
        #     logger.warning(f"Job {job_id} cannot be retried from status {status}")
        #     return False

        # if retry_count >= max_retries:
        #     logger.warning(f"Job {job_id} has exceeded max retries ({max_retries})")
        #     return False

        try:
            redis = await self._ensure_redis()

            # Update retry count and status
            await self._update_job_data(
                job_id,
                {
                    "retry_count": retry_count + 1,
                    "status": JobStatus.PENDING.value,
                    "error_message": None,
                    "started_at": None,
                    "completed_at": None,
                },
            )

            # Re-enqueue with delay
            if delay_seconds > 0:
                execute_at = time.time() + delay_seconds
                await redis.zadd(self._delayed_key(queue_name), {job_id: execute_at})
            else:
                await redis.lpush(self._queue_key(queue_name), job_id)
                # Update stats
                await self._increment_stat(queue_name, "pending_count")

            logger.info(
                f"Job {job_id} retried successfully (attempt {retry_count + 1})"
            )
            return True

        except Exception as e:
            logger.error(f"Error retrying job {job_id}: {e}")
            return False

    async def complete_job(
        self, job_id: str, result: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Mark a job as completed."""
        if not self._initialized:
            await self.initialize()

        job_data = await self._get_job_data(job_id)
        if not job_data:
            return False

        queue_name = job_data["queue_name"]

        try:
            # Calculate processing time if job has started_at
            processing_time = 0
            if "started_at" in job_data:
                started_at = int(job_data["started_at"]) / 1000  # Convert to seconds
                processing_time = int(time.time()) - started_at

            # Use atomic complete script
            result_data = await self._execute_lua_script(
                "atomic_complete",
                keys=[
                    self._processing_key(queue_name),
                    self._stats_key(queue_name),
                    self._job_key(job_id),
                ],
                args=[
                    job_id,
                    json.dumps(result) if result else "null",
                    str(int(time.time())),  # completed timestamp (seconds)
                    str(int(processing_time)),  # processing time in seconds
                ],
            )

            if result_data:
                logger.info(f"Job {job_id} completed successfully")
                return True
            else:
                logger.warning(f"Failed to complete job {job_id}")
                return False

        except Exception as e:
            logger.error(f"Error completing job {job_id}: {e}")
            return False

    async def fail_job(
        self, job_id: str, error_message: str, retry: bool = True
    ) -> bool:
        """Mark a job as failed."""
        if not self._initialized:
            await self.initialize()

        job_data = await self._get_job_data(job_id)
        if not job_data:
            return False

        queue_name = job_data["queue_name"]
        retry_count = int(job_data.get("retry_count", 0))
        max_retries = int(job_data.get("max_retries", 3))

        try:
            # Use atomic fail script
            should_retry = retry and retry_count < max_retries
            result = await self._execute_lua_script(
                "atomic_fail",
                keys=[
                    self._processing_key(queue_name),
                    self._job_key(job_id),
                    self._stats_key(queue_name),
                    self._queue_key(queue_name),
                    self._delayed_key(queue_name),
                ],
                args=[
                    job_id,
                    error_message,
                    str(
                        int(time.time())
                    ),  # failed timestamp (seconds, not milliseconds)
                    "1" if should_retry else "0",  # should retry
                    str(job_data.get("retry_delay", 60)),  # retry delay
                ],
            )

            if result:
                if should_retry:
                    logger.info(
                        f"Job {job_id} failed and scheduled for retry: {error_message}"
                    )
                else:
                    logger.error(f"Job {job_id} failed permanently: {error_message}")
                return True
            else:
                logger.warning(f"Failed to mark job {job_id} as failed")
                return False

        except Exception as e:
            logger.error(f"Error failing job {job_id}: {e}")
            return False

    async def get_queue_stats(self, queue_name: str) -> QueueStats:
        """Get statistics for a specific queue."""
        if not self._initialized:
            await self.initialize()

        redis = await self._ensure_redis()
        stats_key = self._stats_key(queue_name)
        stats_data = await redis.hgetall(stats_key)

        return QueueStats(
            queue_name=queue_name,
            pending_count=int(stats_data.get("pending_count", 0)),
            running_count=int(stats_data.get("running_count", 0)),
            completed_count=int(stats_data.get("completed_count", 0)),
            failed_count=int(stats_data.get("failed_count", 0)),
            total_processed=int(stats_data.get("total_processed", 0)),
            avg_processing_time=float(stats_data["avg_processing_time"])
            if stats_data.get("avg_processing_time")
            else None,
            last_job_processed=self._parse_datetime(
                stats_data.get("last_job_processed")
            ),
        )

    async def list_queues(self) -> List[str]:
        """List all available queues."""
        if not self._initialized:
            await self.initialize()

        redis = await self._ensure_redis()
        pattern = f"{self.key_prefix}:*"
        keys = await redis.keys(pattern)

        queues = set()
        for key in keys:
            parts = key.split(":")
            if len(parts) >= 2 and not parts[-1].startswith((
                "job",
                "stats",
                "processing",
                "delayed",
            )):
                queues.add(parts[-1])

        return sorted(list(queues))

    async def purge_queue(self, queue_name: str) -> int:
        """Remove all jobs from a queue."""
        if not self._initialized:
            await self.initialize()

        redis = await self._ensure_redis()

        # Count jobs before purging
        queue_key = self._queue_key(queue_name)
        processing_key = self._processing_key(queue_name)
        delayed_key = f"{self.key_prefix}:delayed:{queue_name}"

        pending_count = await redis.llen(queue_key)
        processing_count = await redis.llen(processing_key)
        delayed_count = await redis.zcard(delayed_key)

        total_purged = pending_count + processing_count + delayed_count

        # Remove all jobs
        await redis.delete(queue_key, processing_key, delayed_key)

        # Reset stats
        await redis.delete(self._stats_key(queue_name))

        logger.info(f"Purged {total_purged} jobs from {queue_name} queue")
        return total_purged

    # Helper methods

    async def _get_job_data_with_redis(
        self, redis: Redis, job_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get job data using provided Redis connection."""
        job_key = self._job_key(job_id)
        data = await redis.hgetall(job_key)

        if not data:
            return None

        # Parse JSON fields
        parsed_data = {}
        for key, value in data.items():
            if key in ["job_args", "result"]:
                try:
                    parsed_data[key] = json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    parsed_data[key] = value
            else:
                parsed_data[key] = value

        return parsed_data

    async def _get_job_data(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get job data with retry logic."""
        return await self._execute_with_retry(self._get_job_data_with_redis, job_id)

    async def _update_job_status_with_redis(
        self, redis: Redis, job_id: str, status: JobStatus
    ) -> None:
        """Update job status using provided Redis connection."""
        job_key = self._job_key(job_id)
        await redis.hset(job_key, "status", status.value)

        if status == JobStatus.RUNNING:
            await redis.hset(
                job_key, "started_at", datetime.now(timezone.utc).isoformat()
            )

    async def _update_job_status(self, job_id: str, status: JobStatus) -> None:
        """Update job status with retry logic."""
        await self._execute_with_retry(
            self._update_job_status_with_redis, job_id, status
        )

    async def _update_job_data_with_redis(
        self, redis: Redis, job_id: str, data: Dict[str, Any]
    ) -> None:
        """Update job data using provided Redis connection."""
        job_key = self._job_key(job_id)
        mapping = {}
        for key, value in data.items():
            if isinstance(value, (dict, list)):
                mapping[key] = json.dumps(value)
            else:
                mapping[key] = str(value)
        await redis.hset(job_key, mapping=mapping)

    async def _update_job_data(self, job_id: str, data: Dict[str, Any]) -> None:
        """Update job data with retry logic."""
        await self._execute_with_retry(self._update_job_data_with_redis, job_id, data)

    async def _increment_stat_with_redis(
        self, redis: Redis, queue_name: str, stat_name: str
    ) -> None:
        """Increment queue statistic using provided Redis connection."""
        stats_key = self._stats_key(queue_name)
        await redis.hincrby(stats_key, stat_name, 1)

    async def _increment_stat(self, queue_name: str, stat_name: str) -> None:
        """Increment queue statistic with retry logic."""
        await self._execute_with_retry(
            self._increment_stat_with_redis, queue_name, stat_name
        )

    async def _decrement_stat_with_redis(
        self, redis: Redis, queue_name: str, stat_name: str
    ) -> None:
        """Decrement queue statistic using provided Redis connection."""
        stats_key = self._stats_key(queue_name)
        current_value = await redis.hget(stats_key, stat_name)
        if current_value and int(current_value) > 0:
            await redis.hincrby(stats_key, stat_name, -1)

    async def _decrement_stat(self, queue_name: str, stat_name: str) -> None:
        """Decrement queue statistic with retry logic."""
        await self._execute_with_retry(
            self._decrement_stat_with_redis, queue_name, stat_name
        )

    async def _update_avg_processing_time_with_redis(
        self, redis: Redis, queue_name: str, processing_time: float
    ) -> None:
        """Update average processing time using provided Redis connection."""
        stats_key = self._stats_key(queue_name)

        # Get current values
        current_avg = await redis.hget(stats_key, "avg_processing_time")
        total_processed = await redis.hget(stats_key, "total_processed")

        if current_avg and total_processed:
            current_avg = float(current_avg)
            total_processed = int(total_processed)

            # Calculate new average
            total_time = current_avg * total_processed
            new_avg = (total_time + processing_time) / (total_processed + 1)

            await redis.hset(stats_key, "avg_processing_time", str(new_avg))
        else:
            await redis.hset(stats_key, "avg_processing_time", str(processing_time))

    async def _update_avg_processing_time(
        self, queue_name: str, processing_time: float
    ) -> None:
        """Update average processing time with retry logic."""
        await self._execute_with_retry(
            self._update_avg_processing_time_with_redis, queue_name, processing_time
        )

    async def _process_delayed_jobs_with_redis(
        self, redis: Redis, queue_names: List[str]
    ) -> None:
        """Process delayed jobs using provided Redis connection."""
        current_time = time.time()

        for queue_name in queue_names:
            delayed_key = f"{self.key_prefix}:delayed:{queue_name}"

            # Get jobs ready to be processed
            ready_jobs = await redis.zrangebyscore(
                delayed_key, min=0, max=current_time, withscores=False
            )

            if ready_jobs:
                # Move jobs to regular queue
                queue_key = self._queue_key(queue_name)
                for job_id in ready_jobs:
                    await redis.lpush(queue_key, job_id)
                    await redis.zrem(delayed_key, job_id)

                logger.info(
                    f"Moved {len(ready_jobs)} delayed jobs to {queue_name} queue"
                )

    async def _process_delayed_jobs(self, queue_names: List[str]) -> None:
        """Process delayed jobs with retry logic."""
        await self._execute_with_retry(
            self._process_delayed_jobs_with_redis, queue_names
        )

    def _parse_datetime(self, dt_str: Optional[str]) -> Optional[datetime]:
        """Parse datetime string."""
        if not dt_str:
            return None
        try:
            return datetime.fromisoformat(dt_str.replace("Z", "+00:00"))
        except (ValueError, AttributeError):
            return None

    async def _execute_with_retry(self, operation, *args, **kwargs):
        """Execute Redis operation with retry logic."""
        for attempt in range(3):
            try:
                redis = await self._ensure_redis()
                return await operation(redis, *args, **kwargs)
            except Exception as e:
                if attempt == 2:
                    logger.error(f"Redis operation failed after 3 attempts: {e}")
                    raise

                delay = 1.0 * (2**attempt)
                logger.warning(
                    f"Redis operation failed (attempt {attempt + 1}), retrying in {delay}s: {e}"
                )
                await asyncio.sleep(delay)
