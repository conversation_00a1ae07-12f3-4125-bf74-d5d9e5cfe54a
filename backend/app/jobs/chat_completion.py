"""
Chat Completion Background Jobs

Handles async processing of Perplexity chat completions using RQ.
"""

import asyncio
from datetime import datetime, timezone

from app.core.logging import get_logger
from app.models.chat import ChatCompletionJob, MessageStatus
from app.services.factory import get_chat_service
from app.services.perplexity.client import PerplexityClient
from bson import ObjectId

logger = get_logger(__name__)


async def queue_chat_completion(completion_job_id: str) -> str:
    """
    Queue a chat completion job for background processing.

    Args:
        completion_job_id: ID of the ChatCompletionJob to process

    Returns:
        Queue job ID
    """
    try:
        from app.services.factory import get_queue_service
        from app.services.queue.models import JobPriority, QueueType

        # Get queue service
        queue_service = await get_queue_service()

        # Queue the job with retry logic
        job = await queue_service.enqueue_job(
            job_type="chat_completion",
            payload={"completion_job_id": completion_job_id},
            queue_type=QueueType.DEFAULT,
            priority=JobPriority.NORMAL,
            retry_config={"max_attempts": 3, "backoff_factor": 2},
        )

        logger.info(
            f"Queued chat completion job: {job['id']} for completion: {completion_job_id}"
        )
        return job["id"]

    except Exception as e:
        logger.error(f"Error queuing chat completion: {e}")
        raise


async def process_chat_completion(payload: dict):
    """
    Process a chat completion job.
    This is the main entry point called by the queue worker.
    """
    try:
        completion_job_id = payload.get("completion_job_id")
        if not completion_job_id:
            raise ValueError("completion_job_id is required in payload")

        # Process the completion
        await _process_chat_completion_async(completion_job_id)

    except Exception as e:
        logger.error(f"Error in chat completion job: {e}")
        # Mark the job as failed
        completion_job_id = payload.get("completion_job_id")
        if completion_job_id:
            await _mark_completion_failed(completion_job_id, str(e))
        raise


async def _process_chat_completion_async(completion_job_id: str):
    """
    Async implementation of chat completion processing.
    """
    db = None
    try:
        # Get database connection
        from app.core.database import db as db_instance

        await db_instance.connect_to_database()
        db = db_instance.db

        # Get completion job
        completion_job = await ChatCompletionJob.find_one({
            "_id": ObjectId(completion_job_id)
        })

        if not completion_job:
            logger.error(f"Completion job not found: {completion_job_id}")
            return

        # Update job status
        completion_job.status = MessageStatus.PENDING
        completion_job.started_at = int(datetime.now(timezone.utc).timestamp())
        await completion_job.save()

        # Initialize services
        perplexity_client = PerplexityClient()

        chat_service = await get_chat_service()

        # Poll for completion with exponential backoff
        max_attempts = 60  # 5 minutes with 5-second intervals
        attempt = 0

        while attempt < max_attempts:
            try:
                # Check completion status
                status, result_data = await perplexity_client.get_completion_status(
                    completion_job.perplexity_request_id
                )

                if status == "completed" and result_data:
                    # Extract content and sources
                    content = _extract_content_from_result(result_data)
                    sources = perplexity_client.extract_sources_from_completion(
                        result_data
                    )

                    # Convert sources to dict format
                    sources_data = [source.model_dump() for source in sources]

                    # Update message with completion
                    await chat_service.update_message_completion(
                        message_id=str(completion_job.message_id),
                        content=content,
                        sources=sources_data,
                        status=MessageStatus.COMPLETED,
                    )

                    # Update completion job
                    completion_job.status = MessageStatus.COMPLETED
                    completion_job.completed_at = int(
                        datetime.now(timezone.utc).timestamp()
                    )
                    await completion_job.save()

                    logger.info(f"Completed chat completion: {completion_job_id}")
                    return

                elif status == "failed":
                    error_msg = (
                        result_data.get("error", "Unknown error")
                        if result_data
                        else "Unknown error"
                    )
                    raise Exception(f"Perplexity completion failed: {error_msg}")

                # Still pending, wait and retry
                await asyncio.sleep(5)  # Wait 5 seconds
                attempt += 1

            except Exception as e:
                logger.error(f"Error polling completion status: {e}")
                completion_job.retry_count += 1

                if completion_job.retry_count >= completion_job.max_retries:
                    raise Exception(f"Max retries exceeded: {str(e)}")

                # Wait before retry
                await asyncio.sleep(30)
                attempt += 1

        # Timeout reached
        raise Exception("Completion polling timed out")

    except Exception as e:
        logger.error(f"Error processing chat completion: {e}")
        await _mark_completion_failed(completion_job_id, str(e))
        raise


async def _mark_completion_failed(completion_job_id: str, error_message: str):
    """Mark a completion job and associated message as failed."""
    try:
        # Get database connection
        from app.core.database import db as db_instance

        if not db_instance.db:
            await db_instance.connect_to_database()
        db = db_instance.db

        # Get completion job
        completion_job = await ChatCompletionJob.find_one({
            "_id": ObjectId(completion_job_id)
        })

        if completion_job:
            # Update completion job
            completion_job.status = MessageStatus.FAILED
            completion_job.error_message = error_message
            completion_job.completed_at = int(datetime.now(timezone.utc).timestamp())
            await completion_job.save()

            # Update associated message
            chat_service = ChatService(db)
            await chat_service.mark_message_failed(
                message_id=str(completion_job.message_id), error_message=error_message
            )

            logger.error(
                f"Marked completion as failed: {completion_job_id} - {error_message}"
            )

    except Exception as e:
        logger.error(f"Error marking completion as failed: {e}")


def _extract_content_from_result(result_data: dict) -> str:
    """Extract content from Perplexity completion result."""
    try:
        # Perplexity returns content in choices array
        choices = result_data.get("choices", [])
        if choices:
            message = choices[0].get("message", {})
            content = message.get("content", "")
            return content

        # Fallback to direct content field
        return result_data.get("content", "No response generated")

    except Exception as e:
        logger.error(f"Error extracting content from result: {e}")
        return "Error processing response"
