"use client"

import React, { useState, useCallback } from 'react';
import { CompactBlock } from './compact-block';

interface CollapsibleBlockWrapperProps {
  type: 'section' | 'question';
  title: string;
  label?: string;
  required?: boolean;
  repeatable?: boolean;
  isSaved?: boolean;
  onSave?: () => void;
  children: React.ReactElement<{ onSave?: () => void }>;
  className?: string;
}

export function CollapsibleBlockWrapper({
  type,
  title,
  label,
  required,
  repeatable,
  isSaved = false,
  onSave,
  children,
  className,
}: CollapsibleBlockWrapperProps) {
  // Start expanded if not saved, collapsed if saved
  const [isCollapsed, setIsCollapsed] = useState(isSaved);

  // Handle save and collapse
  const handleSave = useCallback(async () => {
    if (onSave) {
      await onSave();
    }
    setIsCollapsed(true);
  }, [onSave]);

  // Toggle collapse state
  const handleToggle = useCallback(() => {
    setIsCollapsed(prev => !prev);
  }, []);

  // Expand for editing
  const handleExpand = useCallback(() => {
    setIsCollapsed(false);
  }, []);

  // Clone children to inject onSave prop
  const childrenWithSave = React.Children.map(children, child => {
    if (React.isValidElement<{ onSave?: () => void }>(child)) {
      return React.cloneElement(child, {
        onSave: handleSave,
      });
    }
    return child;
  });

  return (
    <CompactBlock
      type={type}
      title={title}
      label={label}
      required={required}
      repeatable={repeatable}
      isCollapsed={isCollapsed}
      onToggle={handleToggle}
      onExpand={handleExpand}
      className={className}
    >
      {childrenWithSave}
    </CompactBlock>
  );
} 