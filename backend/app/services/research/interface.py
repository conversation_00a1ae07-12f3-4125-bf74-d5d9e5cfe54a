"""
Research service interface.

This module defines the interface for research services, including
methods for generating external signals research.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

from app.services.research.schemas import ExternalSignalsResponse


class IResearchService(ABC):
    """Interface for research services."""

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the research service."""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up resources used by the research service."""
        pass

    @abstractmethod
    async def generate_external_signals(
        self, deal_id: str, context_block: Dict[str, Any], force_refresh: bool = False
    ) -> ExternalSignalsResponse:
        """
        Generate complete external signals research for a deal.

        Args:
            deal_id: The deal ID
            context_block: Deal context block data
            force_refresh: Whether to force refresh existing research

        Returns:
            Complete external signals response
        """
        pass

    @abstractmethod
    async def get_external_signals(
        self, deal_id: str
    ) -> Optional[ExternalSignalsResponse]:
        """
        Get existing external signals for a deal.

        Args:
            deal_id: The deal ID

        Returns:
            External signals response if found, None otherwise
        """
        pass

    @abstractmethod
    async def refresh_external_signals(
        self, deal_id: str, context_block: Dict[str, Any]
    ) -> ExternalSignalsResponse:
        """
        Force refresh external signals for a deal.

        Args:
            deal_id: The deal ID
            context_block: Deal context block data

        Returns:
            Complete external signals response
        """
        pass
