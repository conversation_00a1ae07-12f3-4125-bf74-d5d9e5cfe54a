"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { 
  Smartphone, 
  Tablet, 
  Monitor, 
  Menu, 
  X, 
  Check,
  AlertCircle,
  Star,
  TrendingUp,
  Users,
  FileText
} from "lucide-react"
import { cn } from "@/lib/utils"
import { responsive, responsiveGrid, mobileAnimations, mobileModal } from "@/lib/utils/responsive"

export default function MobileTestPage() {
  const [showMobileNav, setShowMobileNav] = useState(false)
  const [activeBreakpoint, setActiveBreakpoint] = useState("mobile")

  // Test data for components
  const testCards = [
    { title: "Active Deals", value: "24", trend: "+12%", icon: TrendingUp },
    { title: "Team Members", value: "8", trend: "+2", icon: Users },
    { title: "Documents", value: "156", trend: "+45", icon: FileText },
    { title: "Success Rate", value: "94%", trend: "+3%", icon: Star },
  ]

  return (
    <div className="min-h-screen bg-tx-surface">
      {/* Mobile-First Header */}
      <header className={cn(
        "safe-top sticky top-0 z-30 border-b bg-background/95 backdrop-blur-sm"
      )}>
        <div className={responsive.padding.section}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowMobileNav(!showMobileNav)}
                className="md:hidden"
              >
                {showMobileNav ? <X className="size-5" /> : <Menu className="size-5" />}
              </Button>
              <h1 className={responsive.text.heading}>Mobile Test</h1>
            </div>
            
            {/* Breakpoint Indicator */}
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="hidden xs:flex md:hidden">
                <Smartphone className="mr-1 size-3" />
                Mobile
              </Badge>
              <Badge variant="outline" className="hidden md:flex lg:hidden">
                <Tablet className="mr-1 size-3" />
                Tablet
              </Badge>
              <Badge variant="outline" className="hidden lg:flex">
                <Monitor className="mr-1 size-3" />
                Desktop
              </Badge>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Navigation Overlay */}
      {showMobileNav && (
        <div className="fixed inset-0 z-40 md:hidden">
          <div 
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={() => setShowMobileNav(false)}
          />
          <div className={cn(
            "absolute inset-y-0 left-0 w-full max-w-sm border-r bg-background shadow-xl",
            "safe-top safe-bottom"
          )}>
            <div className="p-6">
              <h2 className="mb-6 text-xl font-bold">Navigation</h2>
              <nav className="space-y-2">
                {["Dashboard", "Forms", "Deals", "Analytics", "Settings"].map((item) => (
                  <button
                    key={item}
                    className={cn(
                      "flex w-full items-center rounded-xl p-4 text-base font-medium",
                      "touch-target transition-all duration-200",
                      "hover:bg-accent hover:text-accent-foreground active:scale-[0.98]"
                    )}
                    onClick={() => setShowMobileNav(false)}
                  >
                    {item}
                  </button>
                ))}
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className={cn(responsive.padding.section, "space-y-6 md:space-y-8")}>
        {/* Responsive Grid Test */}
        <section>
          <h2 className={cn(responsive.text.subheading, "mb-4")}>Responsive Grid Test</h2>
          <div className={responsiveGrid({ mobile: 1, tablet: 2, desktop: 4 }, { mobile: 4, tablet: 6, desktop: 8 })}>
            {testCards.map((card, index) => (
              <motion.div
                key={card.title}
                variants={mobileAnimations.stagger.item}
                initial="initial"
                animate="animate"
                transition={{ delay: index * 0.1 }}
              >
                <Card className={cn(
                  "transition-all duration-300 hover:shadow-lg active:scale-[0.98]",
                  "touch-manipulation"
                )}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                    <CardTitle className="text-sm font-medium md:text-base">
                      {card.title}
                    </CardTitle>
                    <card.icon className="size-4 text-muted-foreground md:size-5" />
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="text-2xl font-bold md:text-3xl">{card.value}</div>
                    <Badge variant="secondary" className="mt-2 text-xs">
                      {card.trend}
                    </Badge>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </section>

        {/* Form Components Test */}
        <section>
          <h2 className={cn(responsive.text.subheading, "mb-4")}>Form Components Test</h2>
          <Card className={responsive.padding.card}>
            <CardHeader>
              <CardTitle>Mobile-First Form</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input 
                  id="email" 
                  type="email" 
                  placeholder="Enter your email"
                  className="touch-target"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="message">Message</Label>
                <textarea 
                  id="message"
                  placeholder="Enter your message"
                  className={cn(
                    "flex min-h-[120px] w-full rounded-xl border border-input bg-transparent",
                    "px-4 py-3 text-base placeholder:text-muted-foreground",
                    "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                    "touch-target disabled:cursor-not-allowed disabled:opacity-50"
                  )}
                />
              </div>
              <div className="flex flex-col gap-3 xs:flex-row">
                <Button fullWidth className="xs:flex-1">
                  Submit
                </Button>
                <Button variant="outline" fullWidth className="xs:flex-1">
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Modal Test */}
        <section>
          <h2 className={cn(responsive.text.subheading, "mb-4")}>Modal Test</h2>
          <Dialog>
            <DialogTrigger asChild>
              <Button>Open Mobile-First Modal</Button>
            </DialogTrigger>
            <DialogContent className={mobileModal.content}>
              <DialogHeader>
                <DialogTitle>Mobile-First Modal</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <p className={responsive.text.body}>
                  This modal adapts to different screen sizes:
                </p>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center gap-2">
                    <Check className="size-4 text-green-500" />
                    Mobile: Bottom sheet style
                  </li>
                  <li className="flex items-center gap-2">
                    <Check className="size-4 text-green-500" />
                    Tablet+: Centered modal
                  </li>
                  <li className="flex items-center gap-2">
                    <Check className="size-4 text-green-500" />
                    Touch-friendly close button
                  </li>
                  <li className="flex items-center gap-2">
                    <Check className="size-4 text-green-500" />
                    Safe area support
                  </li>
                </ul>
                <Button fullWidth>Close Modal</Button>
              </div>
            </DialogContent>
          </Dialog>
        </section>

        {/* Typography Test */}
        <section>
          <h2 className={cn(responsive.text.subheading, "mb-4")}>Typography Test</h2>
          <Card className={responsive.padding.card}>
            <div className="space-y-4">
              <h1 className={responsive.text.heading}>Heading 1 - Responsive</h1>
              <h2 className={responsive.text.subheading}>Heading 2 - Responsive</h2>
              <p className={responsive.text.body}>
                Body text that scales appropriately across devices. Minimum 16px on mobile for accessibility.
              </p>
              <p className={responsive.text.small}>
                Small text that remains readable on all devices.
              </p>
              <p className={responsive.text.caption}>
                Caption text for additional information.
              </p>
            </div>
          </Card>
        </section>

        {/* Status Indicators */}
        <section>
          <h2 className={cn(responsive.text.subheading, "mb-4")}>Mobile-First Status</h2>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <Card className="p-4">
              <div className="flex items-center gap-3">
                <Check className="size-5 text-green-500" />
                <div>
                  <div className="font-medium">Touch Targets</div>
                  <div className="text-sm text-muted-foreground">44px minimum</div>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-3">
                <Check className="size-5 text-green-500" />
                <div>
                  <div className="font-medium">Typography</div>
                  <div className="text-sm text-muted-foreground">16px minimum</div>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-3">
                <Check className="size-5 text-green-500" />
                <div>
                  <div className="font-medium">Safe Areas</div>
                  <div className="text-sm text-muted-foreground">Device support</div>
                </div>
              </div>
            </Card>
          </div>
        </section>
      </main>
    </div>
  )
}
