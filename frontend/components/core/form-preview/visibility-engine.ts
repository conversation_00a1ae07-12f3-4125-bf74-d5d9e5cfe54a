/**
 * Frontend Visibility Engine
 * 
 * Evaluates visibility conditions for questions and sections in real-time
 * Based on backend logic from /backend/app/utils/form/form_logic.py
 * Updated to match the sophisticated type handling from form-logic.ts
 */

import { VisibilityCondition, ConditionClause, Question } from '@/lib/types/form';

// Operator mapping for condition evaluation
const OPERATOR_MAP = {
  "==": (a: any, b: any) => a == b,
  "!=": (a: any, b: any) => a != b,
  ">": (a: any, b: any) => Number(a) > Number(b),
  "<": (a: any, b: any) => Number(a) < Number(b),
  ">=": (a: any, b: any) => Number(a) >= Number(b),
  "<=": (a: any, b: any) => Number(a) <= Number(b),
};

/**
 * Sophisticated equality evaluation that handles type mismatches
 * Especially important for boolean questions vs string values in conditions
 */
function evaluateEquality(actualValue: any, expectedValue: any): boolean {
  // If types match, use direct comparison
  if (typeof actualValue === typeof expectedValue) {
    return actualValue === expectedValue;
  }

  // Handle boolean comparisons with string values
  if (typeof actualValue === 'boolean' || typeof expectedValue === 'boolean') {
    // Convert both values to boolean for comparison
    const actualBool = Boolean(actualValue === true || actualValue === 'true' || actualValue === 'Yes' || actualValue === 'yes');
    const expectedBool = Boolean(expectedValue === true || expectedValue === 'true' || expectedValue === 'Yes' || expectedValue === 'yes');
    
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Boolean equality evaluation (form-preview):', {
        actualValue,
        expectedValue,
        actualBool,
        expectedBool,
        result: actualBool === expectedBool
      });
    }
    
    return actualBool === expectedBool;
  }

  // Handle number comparisons with string values
  if (typeof actualValue === 'number' || typeof expectedValue === 'number') {
    const actualNum = Number(actualValue);
    const expectedNum = Number(expectedValue);
    
    // Only compare if both are valid numbers
    if (!isNaN(actualNum) && !isNaN(expectedNum)) {
      return actualNum === expectedNum;
    }
  }

  // For arrays/objects, try JSON comparison as fallback
  if (typeof actualValue === 'object' || typeof expectedValue === 'object') {
    try {
      return JSON.stringify(actualValue) === JSON.stringify(expectedValue);
    } catch {
      return false;
    }
  }

  // Default: loose equality
  return actualValue == expectedValue;
}

export interface FormAnswers {
  [questionId: string]: any;
}

export interface RepeatableAnswers {
  [sectionId: string]: {
    [instanceIndex: number]: {
      [questionId: string]: any;
    };
  };
}

export interface VisibilityContext {
  answers: FormAnswers;
  repeatableAnswers: RepeatableAnswers;
  currentSectionInstance?: {
    sectionId: string;
    instanceIndex: number;
  };
}

/**
 * Evaluates a single condition clause against current answers
 */
function evaluateConditionClause(
  clause: ConditionClause,
  context: VisibilityContext
): boolean {
  const { question_id, value: expectedValue, section_instance_index } = clause;
  const { answers, repeatableAnswers, currentSectionInstance } = context;

  let actualValue: any;

  // Determine where to look for the answer
  if (section_instance_index !== undefined) {
    // Specific instance index provided
    const sectionId = currentSectionInstance?.sectionId;
    if (sectionId && repeatableAnswers[sectionId]?.[section_instance_index]) {
      actualValue = repeatableAnswers[sectionId][section_instance_index][question_id];
    }
  } else if (currentSectionInstance) {
    // Look in current repeatable section instance first
    const { sectionId, instanceIndex } = currentSectionInstance;
    if (repeatableAnswers[sectionId]?.[instanceIndex]?.[question_id] !== undefined) {
      actualValue = repeatableAnswers[sectionId][instanceIndex][question_id];
    } else {
      // Fall back to regular answers
      actualValue = answers[question_id];
    }
  } else {
    // Look in regular answers
    actualValue = answers[question_id];
  }

  // If no answer exists, condition is false
  if (actualValue === undefined || actualValue === null) {
    return false;
  }

  // Use sophisticated equality evaluation instead of simple comparison
  return evaluateEquality(actualValue, expectedValue);
}

/**
 * Evaluates a visibility condition against current form state
 */
export function evaluateVisibility(
  condition: VisibilityCondition | null | undefined,
  context: VisibilityContext
): boolean {
  // No condition means always visible
  if (!condition) {
    return true;
  }

  const { operator, conditions } = condition;

  // Handle different operators
  switch (operator) {
    case "and":
      return conditions.every(clause => evaluateConditionClause(clause, context));
    
    case "or":
      return conditions.some(clause => evaluateConditionClause(clause, context));
    
    case "not":
      // NOT operator - all conditions must be false
      return !conditions.some(clause => evaluateConditionClause(clause, context));
    
    case "==":
    case "!=":
    case ">":
    case "<":
    case ">=":
    case "<=":
      // Single comparison operator - evaluate first condition with this operator
      if (conditions.length === 0) return true;
      
      const clause = conditions[0];
      const { question_id, section_instance_index } = clause;
      const { answers, repeatableAnswers, currentSectionInstance } = context;

      let actualValue: any;
      
      // Get the actual value using same logic as evaluateConditionClause
      if (section_instance_index !== undefined) {
        const sectionId = currentSectionInstance?.sectionId;
        if (sectionId && repeatableAnswers[sectionId]?.[section_instance_index]) {
          actualValue = repeatableAnswers[sectionId][section_instance_index][question_id];
        }
      } else if (currentSectionInstance) {
        const { sectionId, instanceIndex } = currentSectionInstance;
        if (repeatableAnswers[sectionId]?.[instanceIndex]?.[question_id] !== undefined) {
          actualValue = repeatableAnswers[sectionId][instanceIndex][question_id];
        } else {
          actualValue = answers[question_id];
        }
      } else {
        actualValue = answers[question_id];
      }

      if (actualValue === undefined || actualValue === null) {
        return false;
      }

      // Special handling for == and != with sophisticated type handling
      if (operator === "==" || operator === "!=") {
        const isEqual = evaluateEquality(actualValue, clause.value);
        return operator === "==" ? isEqual : !isEqual;
      }

      // For other operators, use the standard comparison
      const operatorFn = OPERATOR_MAP[operator];
      if (!operatorFn) {
        console.warn(`Unknown operator: ${operator}`);
        return true;
      }

      return operatorFn(actualValue, clause.value);
    
    default:
      console.warn(`Unknown visibility operator: ${operator}`);
      return true;
  }
}

/**
 * Calculates form completion progress based on visible required questions
 */
export function calculateProgress(
  questions: Array<{ 
    _id?: string; 
    id?: string; 
    required: boolean; 
    visibility_condition?: VisibilityCondition;
  }>,
  context: VisibilityContext
): { completed: number; total: number; percentage: number } {
  const visibleRequiredQuestions = questions.filter(question => {
    return question.required && evaluateVisibility(question.visibility_condition, context);
  });

  const completedQuestions = visibleRequiredQuestions.filter(question => {
    const questionId = question._id || question.id!;
    const { answers, repeatableAnswers, currentSectionInstance } = context;

    let hasAnswer = false;

    if (currentSectionInstance) {
      const { sectionId, instanceIndex } = currentSectionInstance;
      hasAnswer = repeatableAnswers[sectionId]?.[instanceIndex]?.[questionId] !== undefined;
    }
    
    if (!hasAnswer) {
      hasAnswer = answers[questionId] !== undefined && answers[questionId] !== null && answers[questionId] !== '';
    }

    return hasAnswer;
  });

  const total = visibleRequiredQuestions.length;
  const completed = completedQuestions.length;
  const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

  return { completed, total, percentage };
}

/**
 * Gets all questions that should be visible in the current context
 */
export function getVisibleQuestions(
  questions: Question[],
  context: VisibilityContext
): Question[] {
  return questions
    .filter(question => evaluateVisibility(question.visibility_condition, context))
    .sort((a, b) => a.order - b.order);
}
