"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Send,
  Minus,
  GripVertical,
  Loader2,
  ExternalLink,
  AlertCircle,
  RefreshCw
} from "lucide-react"
import { cn } from "@/lib/utils"
import { visualRetreat } from "@/lib/utils/responsive"
import { useToast } from "@/components/ui/use-toast"
import { ChatAPI, ChatMessage, ChatThread, ChatSource, ChatMode } from "@/lib/api/chat-api"
import { DealDetailData } from "@/lib/types/deal-detail"
import { OrbitIcon, OrbitPulseGrid } from "./orbit-icon"
import { Loading, LoadingDots } from "@/components/ui/loading"

interface OrbitAIProps {
  dealId: string
  dealContext?: DealDetailData | null
  className?: string
}

// Constants
const COLLAPSED_SIZE = { width: 160, height: 56 }
const SAFE_MARGIN = 8

// Drag state enum for cleaner state management
enum DragState {
  Idle = 'idle',
  Pending = 'pending',
  Dragging = 'dragging',
  Clicked = 'clicked'
}

const getContextualPrompts = (dealContext: any, mode: ChatMode) => {
  if (mode === 'chat') {
    // Chat mode - conversational, strategic prompts
    const basePrompts = [
      "Summarize this deal",
      "What are the key risks?",
      "How does this score compare?",
      "Should I invest?"
    ]

    if (dealContext?.score_breakdown) {
      return [
        `Explain the ${dealContext.score_breakdown.overall_score} score`,
        "What drives the team strength rating?",
        "How strong is the team?",
        "What's the market opportunity?"
      ]
    }

    return basePrompts
  } else if (mode === 'research') {
    // Research mode - data-driven, sourced prompts
    const basePrompts = [
      "Show me market analysis with sources",
      "Find recent funding rounds in this sector",
      "Research the competitive landscape",
      "Get latest news about this company"
    ]

    if (dealContext?.score_breakdown) {
      return [
        "Research competitors with funding data",
        "Find recent funding rounds in this sector",
        "Show market size data with sources",
        "Get latest news about this company"
      ]
    }

    return basePrompts
  } else {
    // Agent mode - autonomous analysis prompts
    return [
      "Run full deal analysis",
      "Identify all risks automatically",
      "Compare to similar deals",
      "Generate investment memo"
    ]
  }
}

export function OrbitAI({ dealId, dealContext, className }: OrbitAIProps) {
  // Core state
  const [isExpanded, setIsExpanded] = useState(false)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [inputValue, setInputValue] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [activeMode, setActiveMode] = useState<ChatMode>('chat')
  const [thread, setThread] = useState<ChatThread | null>(null)
  const [error, setError] = useState<string | null>(null)
  
  // Position and drag state
  const [position, setPosition] = useState(() => {
    if (typeof window === "undefined") return { x: 0, y: 0 };
    const saved = localStorage.getItem("orbitPosition");
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        if (typeof parsed.x === "number" && typeof parsed.y === "number") {
          return parsed;
        }
      } catch {}
    }
    // Default: bottom right
    const vw = window.innerWidth;
    const vh = window.innerHeight;
    return {
      x: Math.max(SAFE_MARGIN, vw - COLLAPSED_SIZE.width - SAFE_MARGIN),
      y: Math.max(SAFE_MARGIN, vh - COLLAPSED_SIZE.height - SAFE_MARGIN),
    };
  });
  
  // Panel state
  const [panelSize, setPanelSize] = useState({ width: 400, height: 650 })
  const [isResizing, setIsResizing] = useState(false)
  const [showTooltip, setShowTooltip] = useState(false)
  
  // Refs
  const containerRef = useRef<HTMLDivElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const resizeStartRef = useRef<{ size: { width: number; height: number }; mouse: { x: number; y: number } } | null>(null)
  const { toast } = useToast()
  const dragHandleRef = useRef<HTMLDivElement>(null)

  // Constants
  const DRAG_THRESHOLD = 5
  const CLICK_TIME_THRESHOLD = 250

  // Add state for expanded panel position
  const [panelPosition, setPanelPosition] = useState<{ x: number; y: number } | null>(null);

  // Utility functions
  const getViewportBounds = useCallback(() => {
    const docWidth = Math.max(
      document.body.scrollWidth,
      document.body.offsetWidth,
      document.documentElement.clientWidth,
      document.documentElement.scrollWidth,
      document.documentElement.offsetWidth
    )
    const docHeight = Math.max(
      document.body.scrollHeight,
      document.body.offsetHeight,
      document.documentElement.clientHeight,
      document.documentElement.scrollHeight,
      document.documentElement.offsetHeight
    )
    
    const componentWidth = isExpanded ? panelSize.width : COLLAPSED_SIZE.width
    const componentHeight = isExpanded ? panelSize.height : COLLAPSED_SIZE.height
    
    return {
      minX: SAFE_MARGIN,
      minY: SAFE_MARGIN,
      maxX: Math.max(SAFE_MARGIN, docWidth - componentWidth - SAFE_MARGIN),
      maxY: Math.max(SAFE_MARGIN, docHeight - componentHeight - SAFE_MARGIN)
    }
  }, [isExpanded, panelSize])

  const constrainPosition = useCallback((x: number, y: number) => {
    const bounds = getViewportBounds()
    console.log('Constraining position:', { x, y, bounds })
    
    const constrainedX = Math.max(bounds.minX, Math.min(bounds.maxX, x))
    const constrainedY = Math.max(bounds.minY, Math.min(bounds.maxY, y))
    
    return { x: constrainedX, y: constrainedY }
  }, [getViewportBounds])

  const savePosition = useCallback((pos: { x: number; y: number }) => {
    try {
      sessionStorage.setItem('orbit-position', JSON.stringify(pos))
      console.log('Saved position:', pos)
    } catch (e) {
      console.warn('Failed to save position:', e)
    }
  }, [])

  const loadPosition = useCallback(() => {
    try {
      const saved = sessionStorage.getItem('orbit-position')
      if (saved) {
        const parsed = JSON.parse(saved)
        if (typeof parsed.x === 'number' && typeof parsed.y === 'number') {
          console.log('Loaded saved position:', parsed)
          return constrainPosition(parsed.x, parsed.y)
        }
      }
    } catch (e) {
      console.warn('Failed to load position:', e)
    }
    
    const vw = window.innerWidth
    const vh = window.innerHeight
    const defaultX = Math.max(SAFE_MARGIN, vw - COLLAPSED_SIZE.width - SAFE_MARGIN)
    const defaultY = Math.max(SAFE_MARGIN, vh - COLLAPSED_SIZE.height - SAFE_MARGIN)
    const defaultPos = constrainPosition(defaultX, defaultY)
    console.log('Using default position:', defaultPos)
    return defaultPos
  }, [constrainPosition])

  // Initialize position
  useEffect(() => {
    const initialPosition = loadPosition()
    setPosition(initialPosition)
  }, [loadPosition])

  // Handle viewport resize with better debouncing
  useEffect(() => {
    const handleResize = () => {
      console.log('Viewport resized, updating position')
      setPosition(prev => constrainPosition(prev.x, prev.y))
    }

    let timeoutId: NodeJS.Timeout
    const debouncedResize = () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(handleResize, 50)
    }

    window.addEventListener('resize', debouncedResize)
    window.addEventListener('scroll', debouncedResize)
    return () => {
      window.removeEventListener('resize', debouncedResize)
      window.removeEventListener('scroll', debouncedResize)
      clearTimeout(timeoutId)
    }
  }, [constrainPosition])

  // Load chat history
  const loadChatHistory = useCallback(async () => {
    if (!dealId) return

    try {
      setIsLoading(true)
      setError(null)
      const response = await ChatAPI.getChatHistory(dealId, activeMode)
      setThread(response.thread)
      setMessages(response.thread.messages)
    } catch (error) {
      console.error('Error loading chat history:', error)
      setError('Failed to load chat history')
    } finally {
      setIsLoading(false)
    }
  }, [dealId, activeMode])

  // Load chat history when expanded
  useEffect(() => {
    if (dealId && isExpanded) {
      loadChatHistory()
    }
  }, [dealId, isExpanded, activeMode, loadChatHistory])

  // Auto-scroll to bottom
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [messages])

  // Show tooltip on first expand
  useEffect(() => {
    if (isExpanded && messages.length === 0) {
      setShowTooltip(true)
      const timer = setTimeout(() => setShowTooltip(false), 3000)
      return () => clearTimeout(timer)
    }
  }, [isExpanded, messages.length])

  // Remove all dragOffset, dragStart, and dragState logic and replace with a simpler controlled drag
  const draggingRef = useRef(false);
  const dragStartPos = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
  const mouseStart = useRef<{ x: number; y: number }>({ x: 0, y: 0 });

  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

  const getSafePosition = (x: number, y: number) => {
    const vw = window.innerWidth;
    const vh = window.innerHeight;
    const minX = SAFE_MARGIN;
    const minY = SAFE_MARGIN;
    const maxX = vw - COLLAPSED_SIZE.width - SAFE_MARGIN;
    const maxY = vh - COLLAPSED_SIZE.height - SAFE_MARGIN;
    return {
      x: Math.max(minX, Math.min(maxX, x)),
      y: Math.max(minY, Math.min(maxY, y)),
    };
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (isExpanded || isResizing) return;
    e.preventDefault();
    draggingRef.current = true;
    dragStartPos.current = { ...position };
    mouseStart.current = { x: e.clientX, y: e.clientY };
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);
  };

    const handleMouseMove = (e: MouseEvent) => {
    if (!draggingRef.current) return;
    let dx = e.clientX - mouseStart.current.x;
    let dy = e.clientY - mouseStart.current.y;
    let newX = dragStartPos.current.x + (isMobile ? 0 : dx);
    let newY = dragStartPos.current.y + dy;
    // On mobile, always center horizontally
    if (isMobile) newX = (window.innerWidth - COLLAPSED_SIZE.width) / 2;
    setPosition(getSafePosition(newX, newY));
  };

  const handleMouseUp = () => {
    if (!draggingRef.current) return;
    draggingRef.current = false;
    setPosition((pos) => {
      const safe = getSafePosition(pos.x, pos.y);
      localStorage.setItem("orbitPosition", JSON.stringify(safe));
      return safe;
    });
    window.removeEventListener('mousemove', handleMouseMove);
    window.removeEventListener('mouseup', handleMouseUp);
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    if (isExpanded || isResizing) return;
    e.preventDefault();
    const touch = e.touches[0];
    if (!touch) return;
    draggingRef.current = true;
    dragStartPos.current = { ...position };
    mouseStart.current = { x: touch.clientX, y: touch.clientY };
    window.addEventListener('touchmove', handleTouchMove);
    window.addEventListener('touchend', handleTouchEnd);
  };

    const handleTouchMove = (e: TouchEvent) => {
    if (!draggingRef.current) return;
    const touch = e.touches[0];
    if (!touch) return;
    let dx = touch.clientX - mouseStart.current.x;
    let dy = touch.clientY - mouseStart.current.y;
    let newX = dragStartPos.current.x + (isMobile ? 0 : dx);
    let newY = dragStartPos.current.y + dy;
    if (isMobile) newX = (window.innerWidth - COLLAPSED_SIZE.width) / 2;
    setPosition(getSafePosition(newX, newY));
  };

  const handleTouchEnd = () => {
    if (!draggingRef.current) return;
    draggingRef.current = false;
    setPosition((pos) => {
      const safe = getSafePosition(pos.x, pos.y);
      localStorage.setItem("orbitPosition", JSON.stringify(safe));
      return safe;
    });
    window.removeEventListener('touchmove', handleTouchMove);
    window.removeEventListener('touchend', handleTouchEnd);
  };

  // Fallback: reset position on click if drag breaks
  const handleIconClick = () => {
    if (!draggingRef.current && !isExpanded) {
      // Reset to default
      const vw = window.innerWidth;
      const vh = window.innerHeight;
      const defaultX = Math.max(SAFE_MARGIN, vw - COLLAPSED_SIZE.width - SAFE_MARGIN);
      const defaultY = Math.max(SAFE_MARGIN, vh - COLLAPSED_SIZE.height - SAFE_MARGIN);
      const defaultPos = getSafePosition(defaultX, defaultY);
      setPosition(defaultPos);
      localStorage.setItem("orbitPosition", JSON.stringify(defaultPos));
      setIsExpanded(true);
    }
  };

  // Resize functionality
  const handleResizeStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    resizeStartRef.current = {
      size: { ...panelSize },
      mouse: { x: e.clientX, y: e.clientY }
    }
    setIsResizing(true)
  }, [panelSize])

  useEffect(() => {
    if (!isResizing) return

    const handleMouseMove = (e: MouseEvent) => {
      if (!resizeStartRef.current) return

      const deltaX = e.clientX - resizeStartRef.current.mouse.x
      const deltaY = e.clientY - resizeStartRef.current.mouse.y

      const newWidth = Math.max(320, Math.min(800, resizeStartRef.current.size.width + deltaX))
      const newHeight = Math.max(400, Math.min(900, resizeStartRef.current.size.height + deltaY))

      setPanelSize({ width: newWidth, height: newHeight })
    }

    const handleMouseUp = () => {
      setIsResizing(false)
      resizeStartRef.current = null
      try {
        sessionStorage.setItem('orbit-panel-size', JSON.stringify(panelSize))
      } catch (e) {
        console.warn('Failed to save panel size:', e)
      }
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isResizing, panelSize])

  // Load saved panel size
  useEffect(() => {
    try {
      const savedSize = sessionStorage.getItem('orbit-panel-size')
      if (savedSize) {
        const parsed = JSON.parse(savedSize)
        if (parsed.width && parsed.height) {
          setPanelSize(parsed)
        }
      }
    } catch (e) {
      console.warn('Failed to load saved panel size:', e)
    }
  }, [])

  // Message handlers
  const handleSendMessage = async (content: string) => {
    if (!content.trim() || isLoading) return

    const userMessage: ChatMessage = {
      id: `temp-${Date.now()}`,
      thread_id: thread?.id || '',
      role: 'user',
      content: content.trim(),
      mode: activeMode,
      status: 'completed',
      sources: [],
      created_at: Date.now(),
      updated_at: Date.now()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsLoading(true)

    try {
      const response = await ChatAPI.sendMessage(dealId, {
        message: content.trim(),
        mode: activeMode,
        include_deal_context: true
      })

      setMessages(prev => [...prev, response])
    } catch (error) {
      console.error('Error sending message:', error)
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const retryMessage = useCallback(async () => {
    const lastUserMessage = messages.findLast(m => m.role === 'user')
    if (lastUserMessage) {
      await handleSendMessage(lastUserMessage.content)
    }
  }, [messages, handleSendMessage])

  const handleQuickPrompt = (prompt: string) => {
    handleSendMessage(prompt)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage(inputValue)
    }
  }

  // Utility to keep expanded panel fully visible
  const getSafePanelPosition = (x: number, y: number, size: { width: number; height: number }) => {
    const vw = window.innerWidth;
    const vh = window.innerHeight;
    let safeX = x;
    let safeY = y;
    if (x + size.width > vw - SAFE_MARGIN) safeX = vw - size.width - SAFE_MARGIN;
    if (safeX < SAFE_MARGIN) safeX = SAFE_MARGIN;
    if (y + size.height > vh - SAFE_MARGIN) safeY = vh - size.height - SAFE_MARGIN;
    if (safeY < SAFE_MARGIN) safeY = SAFE_MARGIN;
    return { x: safeX, y: safeY };
  };

  // When expanding, set the panel position to a safe value based on icon position
  useEffect(() => {
    if (isExpanded) {
      setPanelPosition(getSafePanelPosition(position.x, position.y, panelSize));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isExpanded, position.x, position.y, panelSize.width, panelSize.height]);

  return (
    <motion.div
      ref={containerRef}
      className={cn(
        // Use visual retreat floating patterns for device-aware positioning
        visualRetreat.floating.container,
        visualRetreat.floating.constrained,
        // Add draggable state for desktop
        draggingRef.current && visualRetreat.floating.draggable,
        className
      )}
      style={{
        // Override default positioning when dragged
        ...(draggingRef.current && {
          left: position.x,
          top: position.y,
        })
      }}
      animate={{
        scale: draggingRef.current ? 1.02 : isResizing ? 1.01 : 1,
      }}
      transition={{
        type: "spring",
        stiffness: 400,
        damping: 30,
        mass: 0.8
      }}
    >
      <AnimatePresence mode="wait">
        {!isExpanded ? (
          // Collapsed State - Draggable Chat Bubble
          <motion.div
            ref={dragHandleRef}
            key="collapsed"
            initial={false}
            animate={false}
            className={cn(
              "group select-none",
              "bg-white/80 backdrop-blur-xl dark:bg-black/80",
              "border border-border/50",
              "rounded-2xl shadow-lg",
              "hover:border-primary/50 hover:shadow-xl",
              "transition-all duration-300 ease-out",
              "cursor-grab",
            )}
            onMouseDown={handleMouseDown}
            onTouchStart={handleTouchStart}
            onClick={handleIconClick}
            style={{
              left: position.x,
              top: position.y,
              position: 'fixed',
              zIndex: 9999,
              willChange: 'transform',
              pointerEvents: 'auto',
              width: COLLAPSED_SIZE.width,
              height: COLLAPSED_SIZE.height,
              boxSizing: 'border-box',
              userSelect: 'none',
            }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="relative flex items-center gap-3 px-4 py-3">
              <OrbitIcon 
                className="size-6" 
                animated={true} 
                glowing={true} 
              />
              <span className="font-semibold text-foreground">
                Ask Orbit
              </span>
            </div>
          </motion.div>
        ) : (
          // Expanded State - Non-draggable Chat Panel
          <motion.div
            key="expanded"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 25,
              duration: 0.4
            }}
            className={cn(
              "bg-white/80 backdrop-blur-xl dark:bg-black/80",
              "border border-border/50",
              "rounded-2xl shadow-2xl",
              "relative flex flex-col overflow-hidden",
              isResizing && "select-none"
            )}
            style={{
              width: panelSize.width,
              height: panelSize.height,
              maxHeight: "90vh",
              minWidth: "320px",
              minHeight: "400px",
              left: panelPosition ? panelPosition.x : position.x,
              top: panelPosition ? panelPosition.y : position.y,
              position: 'fixed',
              zIndex: 9999,
              willChange: 'transform',
              pointerEvents: 'auto',
              boxSizing: 'border-box',
              userSelect: 'auto',
            }}
          >
            {/* Header with Mode Toggle */}
            <div className="flex items-center justify-between border-b border-border/50 bg-muted/30 p-4">
              <div className="flex items-center gap-3">
                <OrbitIcon className="size-5" animated={true} />
                <span className="text-lg font-semibold text-foreground">Orbit AI</span>
              </div>

              {/* Mode Toggle */}
              <div className="flex items-center gap-3">
                <div className="flex items-center rounded-full border border-border/50 bg-muted/50 p-1">
                  <button
                    className={cn(
                      "rounded-full px-2 py-1 text-xs font-medium transition-all duration-200",
                      activeMode === 'chat'
                        ? "bg-primary text-primary-foreground shadow-sm"
                        : "text-muted-foreground hover:text-foreground"
                    )}
                    onClick={() => {
                      setActiveMode('chat')
                      setMessages([])
                      setError(null)
                    }}
                    title="Chat Mode - Fast, human-like responses"
                  >
                    Chat
                  </button>
                  <button
                    className={cn(
                      "rounded-full px-2 py-1 text-xs font-medium transition-all duration-200",
                      activeMode === 'research'
                        ? "bg-primary text-primary-foreground shadow-sm"
                        : "text-muted-foreground hover:text-foreground"
                    )}
                    onClick={() => {
                      setActiveMode('research')
                      setMessages([])
                      setError(null)
                    }}
                    title="Deep Research - Analyst-grade with sources"
                  >
                    Research
                  </button>
                  <button
                    className={cn(
                      "cursor-not-allowed rounded-full px-2 py-1 text-xs font-medium opacity-50 transition-all duration-200",
                      "text-muted-foreground"
                    )}
                    disabled
                    title="Agent Mode – Coming Soon"
                  >
                    Agent
                  </button>
                </div>

                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsExpanded(false)}
                    className="size-8 rounded-full p-0 transition-all duration-200 hover:bg-muted/50"
                  >
                    <Minus className="size-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Chat Area */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {/* Loading state */}
                {isLoading && messages.length === 0 && (
                  <div className="py-8 text-center">
                    <div className="mx-auto mb-4">
                      <Loading size="lg" className="text-primary" />
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Loading chat history...
                    </p>
                  </div>
                )}

                {/* Error state */}
                {error && !isLoading && (
                  <div className="py-8 text-center">
                    <AlertCircle className="mx-auto mb-4 size-8 text-destructive" />
                    <p className="mb-2 text-sm text-destructive">{error}</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={loadChatHistory}
                      className="text-xs"
                    >
                      <RefreshCw className="mr-1 size-3" />
                      Retry
                    </Button>
                  </div>
                )}

                {/* Empty state */}
                {!isLoading && !error && messages.length === 0 && (
                  <div className="py-8 text-center">
                    <div className="mx-auto mb-4">
                      <OrbitPulseGrid 
                        className="mx-auto" 
                        size={80} 
                        dotCount={9}
                        duration={1000}
                      />
                    </div>
                    <p className="mb-2 text-base font-medium text-foreground">
                      Hi! I'm Orbit, your AI investment assistant.
                    </p>
                    <p className="mb-2 text-sm text-muted-foreground">
                      {activeMode === 'chat' && "Ask me anything about this deal. I'll provide fast, strategic insights."}
                      {activeMode === 'research' && "Ask me for research with sources. I'll provide analyst-grade analysis."}
                      {activeMode === 'agent' && "I'll run autonomous analysis and provide proactive insights."}
                    </p>
                    <div className="flex items-center justify-center gap-2">
                      <Badge
                        variant="secondary"
                        className={cn(
                          "rounded-full px-3 py-1 text-xs",
                          activeMode === 'chat' && "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300",
                          activeMode === 'research' && "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300",
                          activeMode === 'agent' && "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300"
                        )}
                      >
                        {activeMode === 'chat' && 'Chat Mode'}
                        {activeMode === 'research' && 'Research Mode'}
                        {activeMode === 'agent' && 'Agent Mode'}
                      </Badge>
                    </div>
                  </div>
                )}

                {/* Messages */}
                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{ duration: 0.3, ease: "easeOut" }}
                    className={cn(
                      "flex",
                      message.role === 'user' ? "justify-end" : "justify-start"
                    )}
                  >
                    <div
                      className={cn(
                        "max-w-[80%] rounded-2xl px-4 py-3",
                        "border backdrop-blur-sm",
                        message.role === 'user'
                          ? "ml-4 border-primary/30 bg-primary/10 text-foreground"
                          : "mr-4 border-border/30 bg-muted/50 text-foreground"
                      )}
                    >
                      <div className="space-y-2">
                        {/* Mode indicator for AI messages */}
                        {message.role === 'assistant' && (
                          <div className="mb-2 flex items-center gap-2">
                            <Badge
                              variant="secondary"
                              className={cn(
                                "rounded-full px-2 py-0.5 text-xs",
                                message.mode === 'chat' && "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300",
                                message.mode === 'research' && "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300",
                                message.mode === 'agent' && "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300"
                              )}
                            >
                              {message.mode === 'chat' && 'Chat'}
                              {message.mode === 'research' && 'Research'}
                              {message.mode === 'agent' && 'Agent'}
                            </Badge>
                            {message.ai_model && (
                              <span className="text-xs text-muted-foreground">
                                {message.ai_model}
                              </span>
                            )}
                            {message.response_time_ms && (
                              <span className="text-xs text-muted-foreground">
                                {message.response_time_ms}ms
                              </span>
                            )}
                          </div>
                        )}

                        <p className="text-sm leading-relaxed">{message.content}</p>

                        {/* Status indicator for failed messages */}
                        {message.status === 'failed' && (
                          <div className="flex items-center gap-2 text-xs text-destructive">
                            <AlertCircle className="size-3" />
                            <span>Failed to generate response</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={retryMessage}
                              className="h-5 px-2 text-xs"
                            >
                              <RefreshCw className="mr-1 size-3" />
                              Retry
                            </Button>
                          </div>
                        )}

                        {/* Sources for AI messages */}
                        {message.role === 'assistant' && message.sources && message.sources.length > 0 && (
                          <div className="space-y-1">
                            <p className="text-xs font-medium text-muted-foreground">Sources:</p>
                            <div className="space-y-1">
                              {message.sources.map((source, index) => (
                                <a
                                  key={index}
                                  href={source.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="flex items-center gap-2 text-xs text-primary transition-colors hover:text-primary/80"
                                >
                                  <ExternalLink className="size-3" />
                                  <span className="truncate">{source.title}</span>
                                </a>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}

                {/* Loading indicator */}
                {isLoading && messages.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="flex justify-start"
                  >
                    <div className="mr-4 rounded-2xl border border-border/30 bg-muted/50 px-4 py-3 backdrop-blur-sm">
                      <div className="flex items-center gap-3">
                        <LoadingDots className="text-primary" />
                        <span className="text-sm text-muted-foreground">Orbit is thinking...</span>
                      </div>
                    </div>
                  </motion.div>
                )}

                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            {/* Quick Prompts */}
            {messages.length === 0 && (
              <div className="px-4 pb-3">
                <div className="flex flex-wrap gap-2">
                  {getContextualPrompts(dealContext, activeMode).map((prompt, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleQuickPrompt(prompt)}
                        className={cn(
                          "text-xs font-medium",
                          "bg-muted/50 backdrop-blur-sm",
                          "hover:bg-primary/10",
                          "border border-border/30",
                          "rounded-full transition-all duration-300",
                          "hover:scale-105"
                        )}
                      >
                        {prompt}
                      </Button>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}

            {/* Input Bar */}
            <div className="border-t border-border/50 bg-muted/30 p-4">
              <div className="flex gap-3">
                <Input
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask Orbit..."
                  className={cn(
                    "flex-1 bg-background/50 backdrop-blur-sm",
                    "border-border/50",
                    "rounded-2xl px-4 py-3",
                    "placeholder:text-muted-foreground/70",
                    "focus:border-primary/50 focus:ring-2 focus:ring-primary/20",
                    "transition-all duration-300"
                  )}
                  disabled={isLoading}
                />
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={() => handleSendMessage(inputValue)}
                    disabled={!inputValue.trim() || isLoading}
                    className={cn(
                      "rounded-2xl px-4 py-3",
                      "bg-primary hover:bg-primary/90",
                      "transition-all duration-300"
                    )}
                  >
                    {isLoading ? (
                      <LoadingDots className="size-4" />
                    ) : (
                      <Send className="size-4" />
                    )}
                  </Button>
                </motion.div>
              </div>

              {/* Tooltip */}
              <AnimatePresence>
                {showTooltip && (
                  <motion.div
                    initial={{ opacity: 0, y: 10, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: 10, scale: 0.9 }}
                    className="mt-3 text-center"
                  >
                    <div className="inline-flex items-center gap-2 rounded-full border border-primary/20 bg-primary/10 px-3 py-1 backdrop-blur-sm">
                      <OrbitIcon className="size-3" animated={true} />
                      <p className="text-xs font-medium text-primary">
                        Minimize to drag around
                      </p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Resize Handle */}
            <div
              className={cn(
                "absolute bottom-0 right-0 size-6",
                "cursor-se-resize",
                "rounded-tl-lg hover:bg-primary/10",
                "transition-all duration-200",
                "flex items-end justify-end p-1"
              )}
              onMouseDown={handleResizeStart}
              title="Drag to resize"
            >
              <div className="size-3 border-b-2 border-r-2 border-primary/40" />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
