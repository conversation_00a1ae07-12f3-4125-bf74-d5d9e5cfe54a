"use client"

import { useEffect, useState } from "react"
import { motion } from "framer-motion"
import { <PERSON>Circle, Spa<PERSON><PERSON>, Star, ArrowRight, Heart } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface CompletionStepProps {
  user: any
  stepData: any
  onNext: (data?: any) => void
  onBack: () => void
  onSkip: () => void
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
  currentStep: number
  totalSteps: number
}

// Confetti particle component
const ConfettiParticle = ({ delay = 0 }: { delay?: number }) => {
  const colors = ['#8B5CF6', '#06B6D4', '#10B981', '#F59E0B', '#EF4444']
  const color = colors[Math.floor(Math.random() * colors.length)]
  
  return (
    <motion.div
      initial={{ 
        opacity: 0, 
        y: -20, 
        x: Math.random() * 400 - 200,
        rotate: 0,
        scale: 0
      }}
      animate={{ 
        opacity: [0, 1, 1, 0], 
        y: [0, 100, 200, 300],
        rotate: [0, 180, 360],
        scale: [0, 1, 1, 0]
      }}
      transition={{ 
        duration: 3,
        delay,
        ease: "easeOut"
      }}
      className="absolute pointer-events-none"
      style={{
        backgroundColor: color,
        width: Math.random() * 8 + 4,
        height: Math.random() * 8 + 4,
        borderRadius: Math.random() > 0.5 ? '50%' : '0%'
      }}
    />
  )
}

export function CompletionStep({ 
  user, 
  stepData, 
  onNext 
}: CompletionStepProps) {
  const [showConfetti, setShowConfetti] = useState(false)
  const [showFeedback, setShowFeedback] = useState(false)

  useEffect(() => {
    // Trigger confetti after a short delay
    const timer = setTimeout(() => {
      setShowConfetti(true)
    }, 500)

    return () => clearTimeout(timer)
  }, [])

  const handleGoToDashboard = () => {
    onNext()
  }

  const handleShowFeedback = () => {
    setShowFeedback(true)
  }

  // Generate confetti particles
  const confettiParticles = Array.from({ length: 50 }, (_, i) => (
    <ConfettiParticle key={i} delay={Math.random() * 2} />
  ))

  return (
    <div className="flex-1 flex flex-col items-center justify-center p-8 text-center relative overflow-hidden">
      {/* Confetti Animation */}
      {showConfetti && (
        <div className="absolute inset-0 pointer-events-none">
          {confettiParticles}
        </div>
      )}

      {/* Success Content */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="space-y-8 max-w-2xl relative z-10"
      >
        {/* Success Icon */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, duration: 0.5, ease: "easeOut" }}
          className="relative"
        >
          <div className="mx-auto flex size-24 items-center justify-center rounded-full bg-gradient-to-br from-emerald-500 to-emerald-600 shadow-lg">
            <CheckCircle className="size-12 text-white" />
          </div>
          
          {/* Floating sparkles */}
          <motion.div
            animate={{ 
              rotate: 360,
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              rotate: { duration: 8, repeat: Infinity, ease: "linear" },
              scale: { duration: 2, repeat: Infinity, ease: "easeInOut" }
            }}
            className="absolute -top-2 -right-2"
          >
            <Sparkles className="size-8 text-yellow-400" />
          </motion.div>
          
          <motion.div
            animate={{ 
              rotate: -360,
              scale: [1, 1.2, 1]
            }}
            transition={{ 
              rotate: { duration: 6, repeat: Infinity, ease: "linear" },
              scale: { duration: 3, repeat: Infinity, ease: "easeInOut" }
            }}
            className="absolute -bottom-2 -left-2"
          >
            <Star className="size-6 text-purple-400" />
          </motion.div>
        </motion.div>

        {/* Success Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.5 }}
          className="space-y-4"
        >
          <h2 className="text-4xl font-bold text-gray-900">
            You're All Set!
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed">
            Your decision layer is ready. Welcome to the future of investment decision-making.
          </p>
        </motion.div>

        {/* Summary Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.5 }}
          className="grid grid-cols-1 sm:grid-cols-3 gap-6"
        >
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-gray-100">
            <div className="text-2xl font-bold text-purple-600">
              {stepData.step2?.thesis ? Object.values(stepData.step2.thesis).flat().length : 0}
            </div>
            <div className="text-sm text-gray-600">Thesis Preferences</div>
          </div>
          
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-gray-100">
            <div className="text-2xl font-bold text-blue-600">
              {stepData.step3?.teamMembers?.length || 0}
            </div>
            <div className="text-sm text-gray-600">Team Members</div>
          </div>
          
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-gray-100">
            <div className="text-2xl font-bold text-emerald-600">3</div>
            <div className="text-sm text-gray-600">Core Workflows</div>
          </div>
        </motion.div>

        {/* Personal Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.5 }}
          className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl p-6 border border-purple-100"
        >
          <p className="text-gray-700">
            <span className="font-semibold text-gray-900">{user?.name}</span>, 
            your TractionX workspace is now configured and ready to help you make better investment decisions with AI-powered insights.
          </p>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0, duration: 0.5 }}
          className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-4"
        >
          <Button
            onClick={handleGoToDashboard}
            size="lg"
            className={cn(
              "bg-gradient-to-r from-purple-500 to-purple-600 text-white",
              "hover:from-purple-600 hover:to-purple-700",
              "px-8 py-3 text-lg font-semibold",
              "shadow-lg hover:shadow-xl transition-all duration-300",
              "flex items-center gap-2"
            )}
          >
            Go to Dashboard
            <ArrowRight className="size-5" />
          </Button>

          {!showFeedback && (
            <Button
              variant="outline"
              onClick={handleShowFeedback}
              size="lg"
              className="px-6 py-3 text-base flex items-center gap-2"
            >
              <Heart className="size-4" />
              How was onboarding?
            </Button>
          )}
        </motion.div>

        {/* Feedback Section */}
        {showFeedback && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            transition={{ duration: 0.3 }}
            className="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-gray-100"
          >
            <h3 className="font-semibold text-gray-900 mb-4">Quick Feedback</h3>
            <div className="flex items-center justify-center gap-2">
              {[1, 2, 3, 4, 5].map((rating) => (
                <button
                  key={rating}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  onClick={() => {
                    // Send feedback to analytics
                    try {
                      console.log("Onboarding: Feedback submitted", {
                        rating: rating,
                        maxRating: 5,
                        userId: user?.id,
                        timestamp: new Date().toISOString()
                      })
                      // TODO: Replace with actual analytics service call
                      // analytics.track('onboarding_feedback', { rating, user_id: user?.id })
                    } catch (error) {
                      console.error("Failed to track feedback:", error)
                    }
                  }}
                >
                  <Star className="size-6 text-gray-300 hover:text-yellow-400 transition-colors" />
                </button>
              ))}
            </div>
            <p className="text-sm text-gray-500 mt-2">
              Rate your onboarding experience (1-5 stars)
            </p>
          </motion.div>
        )}

        {/* Footer Message */}
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.2, duration: 0.5 }}
          className="text-sm text-gray-500 pt-4"
        >
          Need help getting started? Check out our{" "}
          <a href="#" className="text-purple-600 hover:text-purple-700 font-medium">
            quick start guide
          </a>{" "}
          or{" "}
          <a href="#" className="text-purple-600 hover:text-purple-700 font-medium">
            contact support
          </a>
        </motion.p>
      </motion.div>
    </div>
  )
}
