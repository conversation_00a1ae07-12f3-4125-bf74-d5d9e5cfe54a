"use client"

import { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON> } from "next/navigation"
import Link from "next/link"
import { motion } from "framer-motion"
import { Shell } from "@/components/shell"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { 
  ChevronLeft, 
  Wand2, 
  Users, 
  TrendingUp, 
  Target,
  ExternalLink,
  Clock,
  Shield,
  Edit3,
  XCircle,
  TrendingDown,
  BarChart3,
  ChevronRight,
  Calendar,
  LineChart,
  Cpu
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailAPI } from "@/lib/api/deal-detail-api"
import { DealAPI } from "@/lib/api/deal-api"
import { DealDetailData, ThesisMatchBreakdown } from "@/lib/types/deal-detail"
import { useAuth } from "@/lib/auth-context"
import { ScoreOverrideModal } from "@/components/core/deals/deal-detail/score-override-modal"
import { OrbitAI } from "@/components/core/orbit-ai/orbit-ai"
import { EmptyPlaceholder } from "@/components/empty-placeholder"
import { ThesisMatchTab } from "@/components/core/deals/full-analysis/thesis-match-tab"
import { Progress } from "@/components/ui/progress"
import { BonusPenaltySection } from "@/components/core/deals/deal-detail/bonus-penalty-section"
import { AnalysisCard } from "@/components/core/deals/analysis-card"
import { useThesisScoring } from "@/lib/hooks/use-thesis-scoring"
// import { useThesisScoring } from "@/lib/hooks/use-thesis-scoring"

export default function FullAnalysisPage() {
  const params = useParams()
  const dealId = params?.id as string
  const { isAuthenticated } = useAuth()
  
  const [dealData, setDealData] = useState<DealDetailData | null>(null)
  const [fullAnalysisData, setFullAnalysisData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [showOverrideModal, setShowOverrideModal] = useState(false)
  const [selectedSignal, setSelectedSignal] = useState<string | null>(null)
  const { data: thesisScoring } = useThesisScoring(dealId)

  useEffect(() => {
    if (!isAuthenticated || !dealId || dealId === 'undefined') {
      if (dealId === 'undefined') {
        setError('Invalid deal ID. Please check the URL and try again.');
        setLoading(false);
      }
      return;
    }

    const fetchDealDetail = async () => {
      try {
        setLoading(true)
        setError(null)

        console.log('Fetching full analysis for deal ID:', dealId);

        // Fetch deal data - it should already contain scoring information
        const dealResponse = await DealAPI.getDeal(dealId);

        // Convert deal to DealDetailData format
        const dealDetailData: DealDetailData = {
          ...dealResponse,
          timeline: (dealResponse.timeline || []).map((event, index) => ({
            id: `event-${index}`,
            date: event.date,
            event: event.event,
            description: event.notes,
            type: 'system'
          })),
          score_breakdown: dealResponse.scoring ? {
            overall_score: (dealResponse.scoring as any).thesis?.score?.normalized_percent || 0,
            signal_breakdown: {
              thesis_match: {
                score: (dealResponse.scoring as any).thesis?.score?.normalized_percent ? Math.round((dealResponse.scoring as any).thesis.score.normalized_percent) : 0,
                explanation: 'Thesis match analysis',
                ai_insights: 'AI-powered thesis matching'
              },
              team_strength: {
                score: (dealResponse.scoring as any).founders?.normalized_score ? Math.round((dealResponse.scoring as any).founders.normalized_score) : 0,
                explanation: (dealResponse.scoring as any).founders?.ai_analysis || 'Team analysis',
                ai_insights: (dealResponse.scoring as any).founders?.ai_analysis || 'AI team assessment'
              },
              market_signals: {
                score: (dealResponse.scoring as any).market?.normalized_score ? Math.round((dealResponse.scoring as any).market.normalized_score) : 0,
                explanation: (dealResponse.scoring as any).market?.ai_analysis || 'Market analysis',
                ai_insights: (dealResponse.scoring as any).market?.ai_analysis || 'AI market assessment'
              }
            },
            scoring_version: "v2.0",
            last_updated: (dealResponse.scoring as any).thesis?.last_scored_at ? new Date((dealResponse.scoring as any).thesis.last_scored_at * 1000).toISOString() : new Date().toISOString(),
            ai_summary: "Comprehensive AI-powered analysis",
            key_insights: [],
            is_overridden: false,
            override_history: []
          } : undefined,
          founders: (dealResponse as any).founders || [],
          external_signals: [],
          documents: [],
          chat_history: []
        };

        setDealData(dealDetailData);
        setFullAnalysisData(dealResponse);
      } catch (err: any) {
        console.error('Error fetching deal detail:', err)
        setError('Failed to load deal analysis. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    fetchDealDetail()
  }, [isAuthenticated, dealId])

  const handleScoreOverride = (signalType: string) => {
    setSelectedSignal(signalType)
    setShowOverrideModal(true)
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-50 border-green-200'
    if (score >= 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  if (loading) {
    return (
      <Shell className="max-w-none">
        <div className="space-y-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 w-1/3 rounded bg-muted" />
            <div className="h-12 w-2/3 rounded bg-muted" />
            <div className="grid grid-cols-3 gap-4">
              <div className="h-32 rounded bg-muted" />
              <div className="h-32 rounded bg-muted" />
              <div className="h-32 rounded bg-muted" />
            </div>
          </div>
        </div>
      </Shell>
    )
  }

  if (error || !dealData) {
    return (
      <Shell className="max-w-none">
        <EmptyPlaceholder>
          <EmptyPlaceholder.Icon name="alertCircle" />
          <EmptyPlaceholder.Title>Error Loading Analysis</EmptyPlaceholder.Title>
          <EmptyPlaceholder.Description>
            {error || 'Deal analysis could not be loaded.'}
          </EmptyPlaceholder.Description>
          <Link href={`/deals/${dealId}`}>
            <Button variant="outline">
              <ChevronLeft className="mr-2 size-4" />
              Back to Deal
            </Button>
          </Link>
        </EmptyPlaceholder>
      </Shell>
    )
  }

  const scoreBreakdown = dealData.score_breakdown

  if (!scoreBreakdown) {
    return (
      <Shell className="max-w-none">
        <EmptyPlaceholder>
          <EmptyPlaceholder.Icon name="target" />
          <EmptyPlaceholder.Title>No Analysis Available</EmptyPlaceholder.Title>
          <EmptyPlaceholder.Description>
            This deal hasn't been analyzed yet. Analysis will appear here once scoring is complete.
          </EmptyPlaceholder.Description>
          <Link href={`/deals/${dealId}`}>
            <Button variant="outline">
              <ChevronLeft className="mr-2 size-4" />
              Back to Deal
            </Button>
          </Link>
        </EmptyPlaceholder>
      </Shell>
    )
  }

  // Extract all fields directly from the backend deal object
  const { company_name, scoring, exclusion_filter_result } = dealData as any;
  const thesis = scoring?.thesis as any;
  const thesisScore = thesis?.score || {};
  const questionScores = Object.values(thesis?.question_scores || {});
  // Convert bonusScores array to record if needed for BonusPenaltySection
  const bonusScoresRaw = thesis?.bonus_scores || {};
  const bonusScores = Array.isArray(bonusScoresRaw)
    ? Object.fromEntries(bonusScoresRaw.map((b: any, i: number) => [b.rule_id || `bonus_${i}`, b]))
    : bonusScoresRaw;
  const lastScored = thesis?.last_scored_at ? new Date(thesis.last_scored_at * 1000).toLocaleDateString() : '';
  const thesisMatchPercent = thesisScore.normalized_percent ?? 0;
  const coreScore = thesisScore.core ?? 0;
  const bonusTotal = thesisScore.bonus ?? 0;
  const penaltyTotal = thesisScore.penalty ?? 0;
  const totalScore = thesisScore.raw_total ?? 0;
  const maxPossible = thesisScore.max_possible ?? 0;
  const thesisName = thesis?.thesis_name || 'Thesis';

  const renderOverviewTab = () => {
    return (
      <div className="space-y-6">
        {/* High-level Score Cards */}
        <div className="grid gap-6 md:grid-cols-3">
          {/* Thesis Match Card */}
          <AnalysisCard
            title="Thesis Match"
            description="Analysis of how well the company matches our investment thesis"
            icon={<Cpu className="size-8" />}
            status={thesisScoring?.status === 'error' ? 'pending' : (thesisScoring?.status as any) || "pending"}
            score={thesisScoring?.thesis_match_percentage || 0}
            bonusPoints={thesisScoring?.bonus_points || 0}
            penaltyPoints={thesisScoring?.penalty_points || 0}
            onViewDetails={() => setActiveTab("thesis")}
          />

          {/* Team Strength Card - Coming Soon */}
          <AnalysisCard
            title="Team Strength"
            description="Founder & team analysis"
            icon={<Users className="size-8" />}
            status="coming-soon"
          />

          {/* Market Signals Card - Coming Soon */}
          <AnalysisCard
            title="Market Signals"
            description="Market size, growth & competition analysis"
            icon={<LineChart className="size-8" />}
            status="coming-soon"
          />
        </div>

        {/* Summary Stats */}
        <Card className="border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg">Analysis Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Core Score</p>
                <p className="text-2xl font-bold">{coreScore.toFixed(1)} / {maxPossible}</p>
              </div>
              {bonusTotal > 0 && (
                <div className="space-y-2">
                  <p className="text-sm font-medium text-green-600">Bonus Points</p>
                  <p className="text-2xl font-bold text-green-700">+{bonusTotal}</p>
                </div>
              )}
              {penaltyTotal > 0 && (
                <div className="space-y-2">
                  <p className="text-sm font-medium text-red-600">Penalty Points</p>
                  <p className="text-2xl font-bold text-red-700">-{penaltyTotal}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Last Updated Info */}
        <div className="flex items-center justify-end gap-2 text-sm text-muted-foreground">
          <Calendar className="size-4" />
          <span>Last updated: {lastScored}</span>
        </div>
      </div>
    )
  }

  return (
    <Shell className="max-w-none px-4 lg:px-8">
      <div className="space-y-8">
        {/* Breadcrumb */}
        <div className="flex items-center text-sm text-muted-foreground">
          <Link href="/deals" className="flex items-center transition-colors hover:text-foreground">
            <ChevronLeft className="mr-1 size-4" />
            Back to TractionX
          </Link>
        </div>

        {/* Header */}
        <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }} className="space-y-4">
          <div className="flex flex-col gap-4 md:flex-row md:items-end md:justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Full Analysis – {company_name}</h1>
              <div className="mt-2 flex flex-wrap items-center gap-3">
                <Badge variant="secondary" className="gap-2"><Clock className="size-3" />Last updated {lastScored}</Badge>
                {exclusion_filter_result?.excluded && <Badge variant="destructive" className="gap-2"><XCircle className="size-3" />Excluded</Badge>}
              </div>
            </div>
            <div className="flex flex-col items-end gap-2 text-right">
              <div className="mb-1 text-5xl font-bold text-primary">{Number(thesisMatchPercent.toFixed(2))}%</div>
              <Progress value={Number(thesisMatchPercent.toFixed(2))} className="h-2 w-32 bg-muted/50" />
              <p className="text-sm text-muted-foreground">Thesis Match %</p>
            </div>
          </div>
        </motion.div>

        {/* Score Summary */}
        <div className="grid gap-6 md:grid-cols-4">
          <Card className="border-0 bg-white/80 shadow-sm backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="rounded-lg bg-blue-100 p-2 text-blue-600"><BarChart3 className="size-5" /></div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Core Score</p>
                  <p className="text-2xl font-bold text-gray-900">{coreScore.toFixed(1)} / {maxPossible}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="border-0 bg-green-50/80 shadow-sm backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="rounded-lg bg-green-100 p-2 text-green-600"><TrendingUp className="size-5" /></div>
                <div>
                  <p className="text-sm font-medium text-green-600">Bonus Points</p>
                  <p className="text-2xl font-bold text-green-900">+{bonusTotal}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="border-0 bg-red-50/80 shadow-sm backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="rounded-lg bg-red-100 p-2 text-red-600"><TrendingDown className="size-5" /></div>
                <div>
                  <p className="text-sm font-medium text-red-600">Penalty Points</p>
                  <p className="text-2xl font-bold text-red-900">-{penaltyTotal}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          {/* <Card className="border-0 shadow-sm bg-indigo-50/80 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-indigo-100 text-indigo-600"><Shield className="h-5 w-5" /></div>
                <div>
                  <p className="text-sm font-medium text-indigo-600">Total Score</p>
                  <p className="text-2xl font-bold text-indigo-900">{(coreScore + bonusTotal - penaltyTotal).toFixed(1)}</p>
                </div>
              </div>
            </CardContent>
          </Card> */}
        </div>

        {/* Exclusion Block */}
        {exclusion_filter_result?.excluded && (
          <Card className="my-4 border-2 border-red-200 bg-red-50/50">
            <CardHeader className="pb-4">
              <div className="flex items-center gap-3">
                <XCircle className="size-6 text-red-600" />
                <div>
                  <CardTitle className="text-lg text-red-900">Excluded by Filter</CardTitle>
                  {exclusion_filter_result.reason && <p className="mt-1 text-sm text-red-700">{exclusion_filter_result.reason}</p>}
                </div>
              </div>
            </CardHeader>
          </Card>
        )}

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid h-12 w-full grid-cols-4 bg-muted/50">
            <TabsTrigger value="overview" className="gap-2"><Target className="size-4" />Overview</TabsTrigger>
            <TabsTrigger value="team" className="gap-2 opacity-50" disabled><Users className="size-4" />Team Strength<Badge variant="outline" className="ml-2 text-xs">Soon</Badge></TabsTrigger>
            <TabsTrigger value="market" className="gap-2 opacity-50" disabled><TrendingUp className="size-4" />Market Signals<Badge variant="outline" className="ml-2 text-xs">Soon</Badge></TabsTrigger>
            <TabsTrigger value="thesis" className="gap-2"><Shield className="size-4" />Thesis Match</TabsTrigger>
          </TabsList>
          <div className="mt-8">
            <TabsContent value="overview" className="space-y-6">
              {renderOverviewTab()}
            </TabsContent>
            <TabsContent value="thesis">
              <ThesisMatchTab deal={dealData} fullAnalysisData={fullAnalysisData} />
            </TabsContent>
            {/* Other tab contents will be added in next iteration */}
            <TabsContent value="team">
              <Card><CardContent className="p-6"><p className="text-muted-foreground">Team analysis coming soon...</p></CardContent></Card>
            </TabsContent>
            <TabsContent value="market">
              <Card><CardContent className="p-6"><p className="text-muted-foreground">Market analysis coming soon...</p></CardContent></Card>
            </TabsContent>
          </div>
        </Tabs>
      </div>
      {/* Score Override Modal */}
      <ScoreOverrideModal open={showOverrideModal} onOpenChange={setShowOverrideModal} dealId={dealId} signalType={selectedSignal} currentScore={selectedSignal ? scoreBreakdown.signal_breakdown[selectedSignal]?.score : 0} onSuccess={() => { window.location.reload() }} />
      {/* Orbit AI - Floating Glassmorphic Assistant */}
      <OrbitAI dealId={dealId} dealContext={dealData} />
    </Shell>
  )
}
