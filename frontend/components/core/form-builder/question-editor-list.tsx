"use client"

import React from 'react';
import { DraggableQuestionList } from './draggable-question-list';
import { Question } from '@/lib/types/form';

interface QuestionEditorListProps {
  questions: Partial<Question>[];
  sectionId: string;
  allQuestions?: Question[]; // All questions from the form for visibility conditions
  onQuestionsChange: (questions: Partial<Question>[]) => void;
}

export function QuestionEditorList({ questions, sectionId, allQuestions = [], onQuestionsChange }: QuestionEditorListProps) {
  // Validate section ID
  React.useEffect(() => {
    if (!sectionId) {
      console.error('QuestionEditorList: sectionId is missing or invalid', { sectionId });
    } else {
      console.log('QuestionEditorList initialized with sectionId:', sectionId);
    }
  }, [sectionId]);

  // Ensure all questions have the correct section_id
  React.useEffect(() => {
    if (sectionId && questions.length > 0) {
      const needsUpdate = questions.some(q => !q.section_id || q.section_id !== sectionId);

      if (needsUpdate) {
        console.log('Fixing section_id for questions in QuestionEditorList');
        const updatedQuestions = questions.map(q => ({
          ...q,
          section_id: sectionId
        }));
        onQuestionsChange(updatedQuestions);
      }
    }
  }, [questions, sectionId, onQuestionsChange]);

  return (
    <DraggableQuestionList
      questions={questions}
      sectionId={sectionId}
      allQuestions={allQuestions}
      onQuestionsChange={onQuestionsChange}
    />
  );
}
