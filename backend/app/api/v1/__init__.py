from app.api.base import BaseAPIRouter
from app.api.v1 import (
    auth,
    cache,
    chat,
    dashboard,
    deals,
    deal_notes,
    exclusion_filters,
    forms,
    jobs,
    onboarding,
    organizations,
    pipelines,
    public,
    public_file,
    qualifier_forms,
    queue,
    rbac,
    roles,
    settings,
    sharing,
    thesis,
    triggers,
    uploads,
    users,
)

protected_api_router = BaseAPIRouter()
public_api_router = BaseAPIRouter(disable_auth=True, require_org=False)
# Include system-level routers first
protected_api_router.include_router(organizations.system_router)

# Include public auth routes
public_api_router.include_router(auth.public_router)

# Include public form routes
public_api_router.include_router(forms.public_router)

# Include public submission routes
public_api_router.include_router(public.router)

# Include public file routes


public_api_router.include_router(public_file.router)

# Include onboarding routes

public_api_router.include_router(onboarding.public_router)
protected_api_router.include_router(onboarding.protected_router)
protected_api_router.include_router(onboarding.demo_router)

# Include settings routes


protected_api_router.include_router(settings.router)

# Include protected auth routes
protected_api_router.include_router(auth.protected_router)

# Include queue routes
protected_api_router.include_router(queue.router)

# Include sharing routes
protected_api_router.include_router(sharing.router)

# Include trigger routes
protected_api_router.include_router(triggers.router)

# Include qualifier form routes
protected_api_router.include_router(qualifier_forms.router)

# Include exclusion filter routes
protected_api_router.include_router(exclusion_filters.router)

# Include pipeline routes


protected_api_router.include_router(pipelines.router)

# Include other routers
protected_api_router.include_router(users.router)
protected_api_router.include_router(organizations.router)
protected_api_router.include_router(roles.router)
protected_api_router.include_router(forms.router)
protected_api_router.include_router(thesis.router)
protected_api_router.include_router(cache.router)
protected_api_router.include_router(rbac.router)
protected_api_router.include_router(jobs.router)
protected_api_router.include_router(deals.router)
protected_api_router.include_router(deal_notes.router)
protected_api_router.include_router(chat.router)
protected_api_router.include_router(uploads.router)
protected_api_router.include_router(dashboard.router)
