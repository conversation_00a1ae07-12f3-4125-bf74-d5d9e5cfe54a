"""
Dashboard API Schemas

Pydantic models for dashboard API requests and responses.
"""

from typing import Dict, List, Optional

from pydantic import BaseModel, ConfigDict


class SectorDistribution(BaseModel):
    """Schema for sector distribution data."""

    sector: str
    count: int


class DealStage(BaseModel):
    """Schema for deal stage data."""

    stage: str
    count: int


class AIActivity(BaseModel):
    """Schema for AI activity status."""

    active: bool
    last_sync: Optional[str] = None


class OnboardingStatus(BaseModel):
    """Schema for onboarding status."""

    has_form: bool
    has_thesis: bool


class DashboardSummaryResponse(BaseModel):
    """Schema for unified dashboard summary response."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "active_deals": 3,
                "active_deals_change_pct": 12,
                "forms": 2,
                "theses": 1,
                "ai_activity": {"active": True, "last_sync": "2025-06-10T14:00:00Z"},
                "sector_distribution": [
                    {"sector": "FinTech", "count": 1},
                    {"sector": "AI/ML", "count": 1},
                    {"sector": "SaaS", "count": 1},
                ],
                "deal_stages": [
                    {"stage": "Pre-Seed", "count": 2},
                    {"stage": "Seed", "count": 1},
                    {"stage": "Series A", "count": 0},
                    {"stage": "Series B", "count": 0},
                ],
                "onboarding": {"has_form": True, "has_thesis": False},
            }
        }
    )

    active_deals: int
    active_deals_change_pct: int
    forms: int
    theses: int
    ai_activity: AIActivity
    sector_distribution: List[SectorDistribution]
    deal_stages: List[DealStage]
    onboarding: OnboardingStatus


# Insights schemas
class DealFunnelBreakdown(BaseModel):
    """Schema for deal funnel breakdown by status."""

    status: str
    count: int


class SectorStageMatrix(BaseModel):
    """Schema for sector × stage deal matrix."""

    sector: str
    stages: Dict[str, int]


class DealActivityTimeline(BaseModel):
    """Schema for deal activity timeline."""

    date: str
    count: int


class ExclusionFilterReason(BaseModel):
    """Schema for exclusion filter reasons."""

    reason: str
    count: int


class UserStatusTransition(BaseModel):
    """Schema for user status transitions."""

    name: str
    status_transitions: Dict[str, int]


class DealInsightsResponse(BaseModel):
    """Schema for comprehensive deal insights response."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "deal_funnel_breakdown": [
                    {"status": "new", "count": 15},
                    {"status": "triage", "count": 8},
                    {"status": "reviewed", "count": 12},
                    {"status": "excluded", "count": 5},
                    {"status": "approved", "count": 3},
                    {"status": "negotiating", "count": 2},
                    {"status": "closed", "count": 1},
                ],
                "sector_stage_matrix": {
                    "SaaS": {"Pre-Seed": 3, "Seed": 5, "Series A": 2},
                    "FinTech": {"Seed": 2, "Series A": 1},
                },
                "deal_activity_timeline": [
                    {"date": "2024-01-15", "count": 3},
                    {"date": "2024-01-16", "count": 1},
                    {"date": "2024-01-17", "count": 5},
                ],
                "exclusion_filters_triggered": {
                    "Too early": 12,
                    "Not AI": 8,
                    "Wrong geography": 5,
                    "Unspecified": 3,
                },
                "deal_velocity_by_user": {
                    "user_id_1": {
                        "name": "Alice Johnson",
                        "status_transitions": {
                            "new→triage": 5,
                            "triage→reviewed": 3,
                            "reviewed→approved": 1,
                        },
                    }
                },
                "forms_count": 3,
                "deals_assigned_to_me": 8,
            }
        }
    )

    deal_funnel_breakdown: List[DealFunnelBreakdown]
    sector_stage_matrix: Dict[str, Dict[str, int]]
    deal_activity_timeline: List[DealActivityTimeline]
    exclusion_filters_triggered: Dict[str, int]
    deal_velocity_by_user: Dict[str, UserStatusTransition]
    forms_count: int
    deals_assigned_to_me: int
