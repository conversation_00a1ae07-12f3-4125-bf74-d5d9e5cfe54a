from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from app.models.user import PublicUser, User


class UserServiceInterface(ABC):
    """Interface for user services."""

    @abstractmethod
    async def get_user(self, user_id: str) -> Optional[User]:
        """Get user by ID."""
        pass

    @abstractmethod
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        pass

    @abstractmethod
    async def create_user(
        self,
        email: str,
        password: str,
        name: str,
        is_superuser: bool = False,
        org_id: Optional[str] = None,
        role_id: Optional[str] = None,
    ) -> User:
        """Create a new user."""
        pass

    @abstractmethod
    async def update_user(
        self, query: Dict[str, Any], update: Dict[str, Any]
    ) -> Optional[User]:
        """Update an existing user using MongoDB-style update.

        Args:
            query: MongoDB query to find the user (e.g., {"_id": ObjectId(user_id)})
            update: MongoDB update operation (e.g., {"$set": {"role_id": ObjectId(role_id)}})

        Returns:
            Updated user or None if not found
        """
        pass

    @abstractmethod
    async def delete_user(self, user_id: str) -> bool:
        """Delete a user."""
        pass

    @abstractmethod
    async def list_users(
        self, skip: int = 0, limit: int = 100, status: Optional[str] = None
    ) -> List[User]:
        """List users with optional filtering."""
        pass

    @abstractmethod
    async def activate_user(self, user_id: str) -> Optional[User]:
        """Activate a user."""
        pass

    @abstractmethod
    async def suspend_user(self, user_id: str) -> Optional[User]:
        """Suspend a user."""
        pass


class PublicUserServiceInterface(ABC):
    """Interface for public user services."""

    @abstractmethod
    async def get_public_user(self, user_id: str) -> Optional[PublicUser]:
        """Get public user by ID."""
        pass

    @abstractmethod
    async def get_public_user_by_email(
        self, email: str, org_id: Optional[str] = None
    ) -> Optional[PublicUser]:
        """Get public user by email."""
        pass

    @abstractmethod
    async def create_public_user(
        self,
        email: str,
        name: Optional[str] = None,
        org_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> PublicUser:
        """Create a new public user."""
        pass

    @abstractmethod
    async def update_public_user(
        self, query: Dict[str, Any], update: Dict[str, Any]
    ) -> Optional[PublicUser]:
        """Update an existing public user using MongoDB-style update."""
        pass

    @abstractmethod
    async def delete_public_user(self, user_id: str) -> bool:
        """Delete a public user."""
        pass

    @abstractmethod
    async def track_form_access(self, user_id: str, form_id: str) -> bool:
        """Track that a public user accessed a specific form."""
        pass

    @abstractmethod
    async def update_last_access(self, user_id: str, org_id: str) -> bool:
        """Update the last access timestamp for a public user."""
        pass
