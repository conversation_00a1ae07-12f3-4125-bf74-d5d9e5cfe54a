"""
Apollo Company Data Processor Task for TractionX Data Pipeline Service.

This task processes Apollo organization data and stores it in the company schema:
1. Extracts useful company information (name, domain, industry, funding, etc.)
2. Stores in existing company tables
3. Creates new departments table with headcount data
4. Skips keywords and technologies as requested

Part of the comprehensive company enrichment pipeline.
"""

import asyncio
import time
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from app.configs import get_logger
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


class ApolloCompanyProcessor:
    """
    Processor for Apollo organization data.

    Extracts and stores company information in S3 for later processing
    by comprehensive_data_processor. Skips keywords and technologies as requested.
    """

    def __init__(self, s3_storage: S3Storage):
        self.s3 = s3_storage
        self.logger = get_logger(f"{__name__}.ApolloCompanyProcessor")
        self._processed_s3_key = None

    async def process_apollo_organization_data(
        self, apollo_data: Dict[str, Any], metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process Apollo organization data and store in company schema.

        Args:
            apollo_data: Raw Apollo organization data
            metadata: Job metadata (company_domain, org_id, etc.)

        Returns:
            Processing result with success status and details
        """
        start_time = time.time()

        try:
            # Extract organization data
            organization = apollo_data.get("organization", {})
            if not organization:
                return {
                    "success": False,
                    "error": "No organization data found in Apollo response",
                    "processing_time": time.time() - start_time,
                }

            # Extract company data for existing schema
            company_data = self._extract_company_data(organization, metadata)

            # Extract departments data for new table
            departments_data = self._extract_departments_data(organization, metadata)

            # Store data in database
            await self._store_apollo_data(company_data, departments_data, metadata)

            # Store raw data to S3 for audit
            s3_key = await self._store_raw_data(apollo_data, metadata)

            self.logger.info(
                f"Successfully processed Apollo data for {metadata.get('company_domain')}",
                company_name=company_data.get("name"),
                departments_count=len(departments_data),
            )

            return {
                "success": True,
                "company_domain": metadata.get("company_domain"),
                "org_id": metadata.get("org_id"),
                "company_data": company_data,
                "departments_count": len(departments_data),
                "s3_key": s3_key,
                "processed_s3_key": getattr(self, "_processed_s3_key", None),
                "processing_time": time.time() - start_time,
            }

        except Exception as e:
            error_msg = f"Apollo company processing failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            return {
                "success": False,
                "error": error_msg,
                "processing_time": time.time() - start_time,
            }

    def _extract_company_data(
        self, organization: Dict[str, Any], metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract company data for existing schema tables."""

        # Basic company information
        company_data = {
            "name": organization.get("name", ""),
            "domain": organization.get("primary_domain", ""),
            "website_url": organization.get("website_url", ""),
            "linkedin_url": organization.get("linkedin_url", ""),
            "twitter_url": organization.get("twitter_url", ""),
            "facebook_url": organization.get("facebook_url", ""),
            "industry": organization.get("industry", ""),
            "founded_year": organization.get("founded_year"),
            "employee_count": organization.get("estimated_num_employees"),
            "annual_revenue": organization.get("annual_revenue"),
            "total_funding": organization.get("total_funding"),
            "funding_stage": organization.get("latest_funding_stage", ""),
            "description": organization.get("short_description", ""),
            "seo_description": organization.get("seo_description", ""),
            "logo_url": organization.get("logo_url", ""),
            "crunchbase_url": organization.get("crunchbase_url", ""),
            "alexa_ranking": organization.get("alexa_ranking"),
            "publicly_traded_symbol": organization.get("publicly_traded_symbol", ""),
            "publicly_traded_exchange": organization.get(
                "publicly_traded_exchange", ""
            ),
            # Location data
            "address": organization.get("raw_address", ""),
            "street_address": organization.get("street_address", ""),
            "city": organization.get("city", ""),
            "state": organization.get("state", ""),
            "postal_code": organization.get("postal_code", ""),
            "country": organization.get("country", ""),
            # Metadata
            "company_domain": metadata.get("company_domain", ""),
            "org_id": metadata.get("org_id", ""),
            "data_source": "apollo",
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
        }

        # Clean up None values
        company_data = {k: v for k, v in company_data.items() if v is not None}

        return company_data

    def _extract_departments_data(
        self, organization: Dict[str, Any], metadata: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Extract departments data for new departments table."""

        departments_data = []
        departmental_head_count = organization.get("departmental_head_count", {})

        for dept_name, head_count in departmental_head_count.items():
            if head_count and head_count > 0:
                department_data = {
                    "company_domain": metadata.get("company_domain", ""),
                    "org_id": metadata.get("org_id", ""),
                    "department_name": dept_name,
                    "head_count": head_count,
                    "data_source": "apollo",
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc),
                }
                departments_data.append(department_data)

        return departments_data

    async def _store_apollo_data(
        self,
        company_data: Dict[str, Any],
        departments_data: List[Dict[str, Any]],
        metadata: Dict[str, Any],
    ) -> None:
        """Store Apollo data in S3 for later processing by comprehensive_data_processor."""

        try:
            # Prepare data for S3 storage (following the same pattern as other tasks)
            processed_data = {
                "company_data": company_data,
                "departments_data": departments_data,
                "metadata": metadata,
                "processed_at": datetime.now(timezone.utc).isoformat(),
            }

            # Store processed data to S3
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            company_domain = metadata.get("company_domain", "unknown")
            s3_key = (
                f"apollo_company_data/{company_domain}/processed_data_{timestamp}.json"
            )

            success = await self.s3.put_object(s3_key, processed_data)
            if not success:
                self.logger.warning(
                    f"Failed to store processed Apollo data to S3: {s3_key}"
                )

            # Store the S3 key in the result for later retrieval
            self._processed_s3_key = s3_key

            self.logger.info(
                f"Stored Apollo processed data for {metadata.get('company_domain')}",
                s3_key=s3_key,
                departments_count=len(departments_data),
            )

        except Exception as e:
            self.logger.error(f"Failed to store Apollo data: {e}")
            raise

    async def _store_raw_data(
        self, apollo_data: Dict[str, Any], metadata: Dict[str, Any]
    ) -> str:
        """Store raw Apollo data to S3 for audit."""

        # Create S3 key
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        company_domain = metadata.get("company_domain", "unknown")
        s3_key = f"apollo_company_data/{company_domain}/raw_data_{timestamp}.json"

        # Prepare data for storage
        storage_data = {
            "apollo_data": apollo_data,
            "metadata": metadata,
            "processed_at": datetime.now(timezone.utc).isoformat(),
        }

        # Store to S3
        success = await self.s3.put_object(s3_key, storage_data)
        if not success:
            self.logger.warning(f"Failed to store raw Apollo data to S3: {s3_key}")

        return s3_key


class ApolloCompanyProcessorService:
    """
    High-level service for Apollo company data processing.
    """

    def __init__(self):
        self.logger = get_logger(f"{__name__}.ApolloCompanyProcessorService")
        self.s3_storage: Optional[S3Storage] = None
        self.processor: Optional[ApolloCompanyProcessor] = None

    async def initialize(self) -> None:
        """Initialize the service."""
        self.s3_storage = S3Storage()

        await self.s3_storage.initialize()

        self.processor = ApolloCompanyProcessor(self.s3_storage)

        self.logger.info("Apollo company processor service initialized")

    async def cleanup(self) -> None:
        """Clean up service resources."""
        if self.s3_storage:
            await self.s3_storage.cleanup()

        self.logger.info("Apollo company processor service cleaned up")

    async def process_apollo_job(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process Apollo company data job.

        Args:
            job_data: Job data containing company information for Apollo fetching

        Returns:
            Processing result
        """
        try:
            # Extract job metadata
            metadata = {
                "company_domain": job_data.get("company_domain", ""),
                "org_id": job_data.get("org_id", ""),
                "job_id": job_data.get("job_id", ""),
                "barrier_group_id": job_data.get("barrier_group_id", ""),
                "company_name": job_data.get("company_name", ""),
            }

            # Validate required fields
            if not metadata["company_domain"]:
                return {
                    "success": False,
                    "error": "Missing company_domain",
                }

            if not metadata["org_id"]:
                return {
                    "success": False,
                    "error": "Missing org_id",
                }

            self.logger.info(
                "Fetching and processing Apollo company data",
                company_domain=metadata["company_domain"],
                org_id=metadata["org_id"],
            )

            # First, fetch Apollo data using the company pipeline
            apollo_data = await self._fetch_apollo_data(metadata)
            if not apollo_data:
                return {
                    "success": False,
                    "error": "Failed to fetch Apollo data",
                    "company_domain": metadata["company_domain"],
                }

            # Then process the Apollo data
            if self.processor:
                result = await self.processor.process_apollo_organization_data(
                    apollo_data, metadata
                )
                return result
            else:
                return {
                    "success": False,
                    "error": "Apollo processor not initialized",
                }

        except Exception as e:
            error_msg = f"Apollo company processing failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)

            return {
                "success": False,
                "error": error_msg,
            }

    async def _fetch_apollo_data(
        self, metadata: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Fetch Apollo data using the company enrichment pipeline.

        Args:
            metadata: Job metadata containing company information

        Returns:
            Apollo data dictionary or None if failed
        """
        try:
            # Import here to avoid circular imports
            from app.pipelines.apollo import ApolloPipeline

            # Create pipeline instance
            pipeline = ApolloPipeline()
            await pipeline.initialize()

            try:
                # Prepare input data for Apollo enrichment
                input_data = {
                    "domain": metadata["company_domain"],
                    "org_id": metadata["org_id"],
                    "company_name": metadata.get("company_name", ""),
                }

                # Fetch Apollo data
                self.logger.info(
                    "Fetching Apollo data from API",
                    company_name=metadata.get("company_name"),
                    domain=metadata["company_domain"],
                )
                apollo_result = await pipeline._process_data(input_data)

                if not apollo_result.success:
                    self.logger.error(
                        f"Apollo fetch failed: {apollo_result.error_message}"
                    )
                    return None

                # Extract Apollo data from result
                if not apollo_result.data:
                    self.logger.warning("No data returned from Apollo API")
                    return None

                enrichment_data = apollo_result.data.get("apollo_data")
                if not enrichment_data:
                    self.logger.warning("No Apollo data found in API response")
                    return None

                # Get the raw Apollo API response from the metadata
                raw_apollo_response = enrichment_data.apollo_metadata.get(
                    "api_response", {}
                )

                self.logger.info(
                    "Successfully fetched Apollo data",
                    company_domain=metadata["company_domain"],
                )

                return raw_apollo_response

            finally:
                await pipeline.cleanup()

        except Exception as e:
            error_msg = f"Apollo data fetch failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return None


async def process_apollo_company_data_task(job_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Apollo company data processing task.

    This task fetches Apollo organization data from the API and processes it into the company schema.
    It's designed to be part of the comprehensive company enrichment pipeline.

    Args:
        job_data: Job data containing:
            - company_domain: Company domain (required)
            - org_id: Organization ID (required)
            - company_name: Company name (optional)
            - job_id: Job ID (optional)
            - barrier_group_id: Barrier group ID for orchestration (optional)

    Returns:
        Processing result with success status and metadata
    """
    service = ApolloCompanyProcessorService()

    try:
        await service.initialize()
        return await service.process_apollo_job(job_data)
    finally:
        await service.cleanup()


def process_apollo_company_data_task_sync(job_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Synchronous wrapper for the async Apollo company processing task.
    This is needed for RQ compatibility.
    """
    return asyncio.run(process_apollo_company_data_task(job_data))
