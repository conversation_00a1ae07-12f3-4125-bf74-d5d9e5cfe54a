// Types for deal submission preview functionality

export interface SubmissionQuestionAnswer {
  question_id: string
  label: string
  type: string
  answer: any
}

export interface SubmissionRepeatableInstance {
  index: number
  answers: SubmissionQuestionAnswer[]
}

export interface SubmissionSection {
  section_id: string
  title: string
  repeatable: boolean
  questions?: SubmissionQuestionAnswer[]
  instances?: SubmissionRepeatableInstance[]
}

export interface SubmissionPreview {
  submission_id: string
  submitted_at: string
  form_name: string
  form_version: number
  sections: SubmissionSection[]
}

export interface SubmissionDropdownOption {
  form_name: string
  submission_id: string
  submitted_at: string
}

export interface DealSubmissionPreviewResponse {
  deal_id: string
  no_submissions: boolean
  submissions: SubmissionPreview[]
  dropdown: SubmissionDropdownOption[]
}

// Helper types for rendering
export interface FormattedAnswer {
  label: string
  value: string | string[]
  type: string
  isEmpty: boolean
}

export interface FormattedSection {
  id: string
  title: string
  isRepeatable: boolean
  questions?: FormattedAnswer[]
  instances?: {
    index: number
    title: string
    answers: FormattedAnswer[]
  }[]
}

export interface FormattedSubmission {
  id: string
  submittedAt: Date
  formName: string
  formVersion: number
  sections: FormattedSection[]
}
