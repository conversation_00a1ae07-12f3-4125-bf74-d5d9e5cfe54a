"use client"

import React, { createContext, useContext, useState, useEffect } from 'react';
import { PublicSubmissionAPI, MagicLinkVerifyResponse } from '@/lib/api/public-submission';

// Helper function to check if we're on the client side
function isClient() {
  return typeof window !== 'undefined';
}

// Helper function to safely access localStorage
function safeLocalStorage() {
  if (!isClient()) return null;
  try {
    return window.localStorage;
  } catch (error) {
    console.warn('localStorage not available:', error);
    return null;
  }
}

// Define the public user interface
interface PublicUser {
  id: string;
  email: string;
  name?: string;
  type: 'public';
}

// Define the public submission interface
interface PublicSubmission {
  id: string;
  status: 'draft' | 'submitted';
  progress: number;
  can_edit: boolean;
  answers?: Record<string, any>;
  last_question?: string;
}

// Define the auth state interface
interface PublicAuthState {
  isAuthenticated: boolean;
  user: PublicUser | null;
  accessToken: string | null;
  refreshToken: string | null;
  submission: PublicSubmission | null;
  loading: boolean;
}

// Define the context interface
interface PublicAuthContextType extends PublicAuthState {
  login: (email: string, name: string | undefined, token: string) => Promise<void>;
  loginWithTokens: (
    accessToken: string,
    refreshToken: string,
    user: PublicUser,
    submission?: PublicSubmission
  ) => void;
  logout: () => void;
  updateSubmission: (submission: PublicSubmission) => void;
  getStoredToken: () => string | null;
  refreshTokens: () => Promise<void>;
}

// Create the context
const PublicAuthContext = createContext<PublicAuthContextType | undefined>(undefined);

// Storage keys for public auth
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'public_access_token',
  REFRESH_TOKEN: 'public_refresh_token',
  USER: 'public_user',
  SUBMISSION: 'public_submission',
} as const;

// Create a provider component
export function PublicAuthProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState<PublicAuthState>({
    isAuthenticated: false,
    user: null,
    accessToken: null,
    refreshToken: null,
    submission: null,
    loading: true,
  });
  const [isHydrated, setIsHydrated] = useState(false);

  // Mark as hydrated after first render
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Initialize auth state from localStorage on component mount
  useEffect(() => {
    if (!isHydrated) return;

    const storage = safeLocalStorage();
    if (!storage) {
      setState(prev => ({ ...prev, loading: false }));
      return;
    }

    try {
      const accessToken = storage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
      const refreshToken = storage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
      const userJson = storage.getItem(STORAGE_KEYS.USER);
      const submissionJson = storage.getItem(STORAGE_KEYS.SUBMISSION);

      if (accessToken && userJson) {
        try {
          const user = JSON.parse(userJson);
          const submission = submissionJson ? JSON.parse(submissionJson) : null;
          
          setState({
            isAuthenticated: true,
            user,
            accessToken,
            refreshToken,
            submission,
            loading: false,
          });

          console.log('Public auth initialized:', { user: user.email, submission: submission?.id });
        } catch (error) {
          console.error('Failed to parse stored public auth data:', error);
          clearStoredAuth();
          setState(prev => ({ ...prev, loading: false }));
        }
      } else {
        setState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('Error initializing public auth:', error);
      setState(prev => ({ ...prev, loading: false }));
    }
  }, [isHydrated]);

  // Helper function to clear stored auth data
  const clearStoredAuth = () => {
    const storage = safeLocalStorage();
    if (storage) {
      Object.values(STORAGE_KEYS).forEach(key => {
        storage.removeItem(key);
      });
    }
  };

  // Helper function to store auth data
  const storeAuthData = (
    accessToken: string,
    refreshToken: string,
    user: PublicUser,
    submission?: PublicSubmission
  ) => {
    const storage = safeLocalStorage();
    if (storage) {
      try {
        storage.setItem(STORAGE_KEYS.ACCESS_TOKEN, accessToken);
        storage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
        storage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
        if (submission) {
          storage.setItem(STORAGE_KEYS.SUBMISSION, JSON.stringify(submission));
        }
      } catch (error) {
        console.error('Failed to store public auth data:', error);
      }
    }
  };

  // Login function - sends magic link
  const login = async (email: string, name: string | undefined, token: string) => {
    try {
      await PublicSubmissionAPI.login({ email, name, token });
      // Don't update state here - wait for magic link verification
    } catch (error) {
      console.error('Public login error:', error);
      throw error;
    }
  };

  // Login with tokens (after magic link verification)
  const loginWithTokens = (
    accessToken: string,
    refreshToken: string,
    user: PublicUser,
    submission?: PublicSubmission
  ) => {
    setState({
      isAuthenticated: true,
      user,
      accessToken,
      refreshToken,
      submission: submission || null,
      loading: false,
    });

    storeAuthData(accessToken, refreshToken, user, submission);
    console.log('Public user logged in:', { email: user.email, submission: submission?.id });
  };

  // Logout function
  const logout = () => {
    setState({
      isAuthenticated: false,
      user: null,
      accessToken: null,
      refreshToken: null,
      submission: null,
      loading: false,
    });

    clearStoredAuth();
    console.log('Public user logged out');
  };

  // Update submission function
  const updateSubmission = (submission: PublicSubmission) => {
    setState(prev => ({ ...prev, submission }));
    
    const storage = safeLocalStorage();
    if (storage) {
      try {
        storage.setItem(STORAGE_KEYS.SUBMISSION, JSON.stringify(submission));
      } catch (error) {
        console.error('Failed to store updated submission:', error);
      }
    }
  };

  // Get stored token function
  const getStoredToken = (): string | null => {
    const storage = safeLocalStorage();
    return storage ? storage.getItem(STORAGE_KEYS.ACCESS_TOKEN) : null;
  };

  // Add refresh tokens function inside PublicAuthProvider
  const refreshTokens = async () => {
    if (!state.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const refreshResponse = await PublicSubmissionAPI.refreshToken(state.refreshToken);
      
      // Update state with new tokens
      setState(prev => ({
        ...prev,
        accessToken: refreshResponse.access_token,
        refreshToken: refreshResponse.refresh_token,
      }));

      // Update localStorage
      storeAuthData(
        refreshResponse.access_token,
        refreshResponse.refresh_token,
        state.user!,
        state.submission || undefined
      );

      console.log('Tokens refreshed successfully');
    } catch (error) {
      console.error('Failed to refresh tokens:', error);
      // If refresh fails, logout the user
      logout();
      throw error;
    }
  };

  const contextValue: PublicAuthContextType = {
    ...state,
    login,
    loginWithTokens,
    logout,
    updateSubmission,
    getStoredToken,
    refreshTokens,
  };

  return (
    <PublicAuthContext.Provider value={contextValue}>
      {children}
    </PublicAuthContext.Provider>
  );
}

// Custom hook to use the public auth context
export function usePublicAuth() {
  const context = useContext(PublicAuthContext);
  if (context === undefined) {
    throw new Error('usePublicAuth must be used within a PublicAuthProvider');
  }
  return context;
}
