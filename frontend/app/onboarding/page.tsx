"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "sonner"

import { OnboardingWizard } from "@/components/core/onboarding/new-user-wizard"
import { useAuth } from "@/lib/auth-context"
import { SettingsAPI } from "@/lib/api/settings-api"
import { cn } from "@/lib/utils"

export default function OnboardingPage() {
  const router = useRouter()
  const { user, isAuthenticated, loading } = useAuth()
  const [isReady, setIsReady] = useState(false)

  useEffect(() => {
    // Wait for auth to load
    if (loading) return

    // Redirect to login if not authenticated
    if (!isAuthenticated || !user) {
      router.push('/login')
      return
    }

    // Check if user is admin (only admins should see onboarding)
    const isAdmin = user?.is_superuser || (user?.role_id !== null && user?.role_id !== undefined)
    if (!isAdmin) {
      toast.error("Only organization admins can access onboarding")
      router.push('/dashboard')
      return
    }

    setIsReady(true)
  }, [loading, isAuthenticated, user, router])

  const handleOnboardingComplete = async () => {
    try {
      // Mark onboarding as complete
      await SettingsAPI.completeOnboarding()

      toast.success("Welcome to TractionX! Your decision layer is ready.")

      // Redirect to dashboard
      router.push('/dashboard')
    } catch (error) {
      console.error("Failed to complete onboarding:", error)
      toast.error("Failed to complete onboarding. Please try again.")
    }
  }

  // Show loading while auth is being determined
  if (loading || !isReady) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center"
        >
          <div className="relative mb-6">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="border-3 mx-auto size-12 rounded-full border-purple-200 border-t-purple-600"
            />
          </div>
          <h3 className="text-lg font-semibold text-gray-900">Preparing Your Experience</h3>
          <p className="text-sm text-gray-600 mt-2">Setting up your premium workspace...</p>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 safe-top safe-bottom">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
        <OnboardingWizard
          user={user}
          onComplete={handleOnboardingComplete}
        />
      </div>
    </div>
  )
}
