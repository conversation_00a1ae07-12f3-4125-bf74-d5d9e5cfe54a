@tailwind base;
@tailwind components;
@tailwind utilities;

/* Force light mode only - prevent any dark mode activation */
html, body {
  background: #ffffff !important;
  color: #111111 !important;
}

html {
  color-scheme: light !important;
}

/* Ensure all theme variables are light mode */
:root {
  color-scheme: light !important;
}

/* Remove any dark mode classes that might be applied */
.dark, [data-theme="dark"], [class*="dark"] {
  background: #ffffff !important;
  color: #111111 !important;
}

@layer utilities {
  /* Premium Form Preview Animations */
  @keyframes shake {
    0%, 100% {
      transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
      transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
      transform: translateX(2px);
    }
  }

  .animate-shake {
    animation: shake 0.5s ease-in-out;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-4px);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(99, 102, 241, 0.3);
    }
    50% {
      box-shadow: 0 0 20px rgba(99, 102, 241, 0.6);
    }
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  /* Mobile-first responsive utilities */
  .mobile-padding {
    @apply px-4 py-6;
  }

  @media (min-width: 640px) {
    .mobile-padding {
      @apply px-6 py-8;
    }
  }

  @media (min-width: 1024px) {
    .mobile-padding {
      @apply px-8 py-10;
    }
  }

  /* Touch-friendly interactive elements */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .touch-target-lg {
    min-height: 48px;
    min-width: 48px;
  }

  /* Mobile-first grid utilities */
  .mobile-grid {
    @apply grid grid-cols-1 gap-4;
  }

  @media (min-width: 640px) {
    .mobile-grid {
      @apply grid-cols-2 gap-6;
    }
  }

  @media (min-width: 1024px) {
    .mobile-grid {
      @apply gap-8;
    }
  }

  /* Safe area utilities for mobile devices */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Sidebar and layout utilities */
  .sidebar-stable {
    overflow-x: hidden;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .sidebar-stable::-webkit-scrollbar {
    display: none;
  }

  .layout-container {
    box-sizing: border-box;
    max-width: 100%;
    overflow-x: hidden;
  }

  .layout-container * {
    box-sizing: border-box;
  }

  /* Scrollbar utilities for mobile-first design */
  .scrollbar-hide {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Enhanced mobile interactions */
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Visual retreat specific utilities */
  .visual-retreat-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(229, 231, 235, 0.5);
  }

  .visual-retreat-modal {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(12px);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;

    --ring: 215 20.2% 65.1%;

    --radius: 0.5rem;
  }
}

@layer base {
  /* Zoom-invariant full height layout foundation */
  html, body {
    height: 100%;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    scroll-behavior: smooth;
    box-sizing: border-box;
  }

  html {
    overflow-y: scroll; /* Prevent layout shift from scrollbar */
    overflow-x: hidden; /* Prevent horizontal scroll */
  }

  body {
    @apply bg-tx-surface text-tx-fg;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Space Grotesk', system-ui, -apple-system, sans-serif;
    display: flex;
    flex-direction: column;
  }

  /* Ensure Next.js root container fills height */
  #__next {
    height: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  * {
    @apply border-tx-border;
  }

  /* Mobile-first typography system - Notion-grade readability */
  h1 {
    @apply text-3xl font-bold leading-tight;
    /* Mobile: 28px, Desktop: 32px+ */
    font-size: 28px;
    line-height: 1.2;
  }

  @media (min-width: 1024px) {
    h1 {
      @apply text-4xl;
      font-size: 32px;
    }
  }

  h2 {
    @apply text-2xl font-bold leading-tight;
    /* Mobile: 24px, Desktop: 28px+ */
    font-size: 24px;
    line-height: 1.3;
  }

  @media (min-width: 1024px) {
    h2 {
      @apply text-3xl;
      font-size: 28px;
    }
  }

  h3 {
    @apply text-xl font-semibold leading-tight;
    /* Mobile: 20px, Desktop: 24px+ */
    font-size: 20px;
    line-height: 1.4;
  }

  @media (min-width: 1024px) {
    h3 {
      @apply text-2xl;
      font-size: 24px;
    }
  }

  /* Body text - minimum 16px for mobile accessibility */
  p, div {
    @apply text-base;
    font-size: 16px;
    line-height: 1.6;
  }

  /* Small text - minimum 15px for mobile readability */
  .text-sm {
    font-size: 15px;
    line-height: 1.5;
  }

  /* Extra small text - minimum 13px */
  .text-xs {
    font-size: 13px;
    line-height: 1.4;
  }
}
/* @media (min-width: 1024px) { */
@media (min-width: 1px) {
  html {
    zoom: 0.75 ;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}