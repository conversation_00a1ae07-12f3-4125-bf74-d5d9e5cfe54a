"use client"

import { useState } from 'react';
import { motion, easeOut } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import {
  Share2,
  Copy,
  QrCode,
  ExternalLink,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { DashboardSummary } from '@/lib/api/dashboard-api';

interface ShareThesisBlockProps {
  dashboardData: DashboardSummary;
  className?: string;
}

export function ShareThesisBlock({
  dashboardData,
  className
}: ShareThesisBlockProps) {
  // Generate mock share URL and form title based on dashboard data
  // const shareUrl = dashboardData.forms > 0 ? 'https://app.tractionx.com/share/demo-form' : '';
  // const formTitle = dashboardData.forms > 0 ? 'Investment Application Form' : 'No forms available';
  const shareUrl = 'https://app.tractionx.com/share/demo-form';
  const formTitle = 'Investment Application Form';
  // const hasForm = dashboardData.forms > 0;
  const hasForm = false;
  const [copied, setCopied] = useState(false);

  const handleCopyToClipboard = async () => {
    if (!hasForm) return;

    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);

      // Reset copied state after 2 seconds
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const handleOpenLink = () => {
    if (!hasForm) return;
    window.open(shareUrl, '_blank');
  };

  // Generate QR code URL (using a free QR code service)
  const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=120x120&data=${encodeURIComponent(shareUrl)}`;

  const containerVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: easeOut,
        delay: 0.6
      }
    }
  };

  const qrVariants = {
    hidden: { opacity: 0, rotate: -10 },
    visible: {
      opacity: 1,
      rotate: 0,
      transition: {
        duration: 0.6,
        ease: easeOut,
        delay: 0.8
      }
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={className}
    >
      <Card className="relative overflow-hidden border border-gray-200 bg-white shadow-sm">
        <CardHeader className={cn(
          "relative",
          // Mobile-first header padding
          "p-4 md:p-6"
        )}>
          <div className={cn(
            "flex justify-between",
            // Mobile: stack vertically, tablet+: horizontal
            "flex-col gap-3 xs:flex-row xs:items-center"
          )}>
            <div className="min-w-0 flex-1">
              <CardTitle className={cn(
                "flex items-center font-bold text-gray-900",
                // Mobile-first title sizing and spacing
                "gap-2 text-lg md:gap-3 md:text-xl"
              )}>
                <Share2 className="size-5 shrink-0 text-gray-700 md:size-6" />
                <span className="truncate">Share Investment Form</span>
              </CardTitle>
              <p className={cn(
                "mt-2 text-gray-600",
                // Mobile-first description sizing
                "text-sm md:text-base"
              )}>
                Let startups apply directly through your form
              </p>
            </div>
            <Badge variant="secondary" className={cn(
              "border border-gray-200 bg-gray-100 text-gray-700 hover:bg-gray-100",
              // Mobile-first badge sizing
              "px-2 py-1 text-xs md:px-3 md:text-sm",
              "w-fit shrink-0"
            )}>
              Active
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="relative space-y-6">
          {hasForm ? (
            <>
              {/* QR Code Section */}
              <div className="flex items-center gap-4">
                <motion.div
                  variants={qrVariants}
                  className="shrink-0"
                >
                  <div className="rounded-lg border bg-white p-3 shadow-sm">
                    <img
                      src={qrCodeUrl}
                      alt="QR Code for form sharing"
                      className="size-20"
                    />
                  </div>
                </motion.div>

            <div className="min-w-0 flex-1">
              <h4 className="mb-2 text-lg font-semibold text-gray-900">
                {formTitle}
              </h4>
              <p className="mb-4 text-base text-gray-600">
                Scan QR code or use the link below
              </p>

              {/* URL Display */}
              <div className="flex items-center gap-3 rounded-lg border border-gray-200 bg-gray-50 p-3">
                <code className="flex-1 truncate font-mono text-sm text-gray-600">
                  {shareUrl}
                </code>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={handleCopyToClipboard}
              variant="default"
              size="sm"
              className="flex-1 bg-gray-900 text-white hover:bg-gray-800"
              disabled={copied}
            >
              {copied ? (
                <>
                  <CheckCircle className="mr-2 size-4" />
                  Copied!
                </>
              ) : (
                <>
                  <Copy className="mr-2 size-4" />
                  Copy Link
                </>
              )}
            </Button>

            <Button
              onClick={handleOpenLink}
              variant="outline"
              size="sm"
              className="flex-1 border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              <ExternalLink className="mr-2 size-4" />
              Preview
            </Button>
          </div>
          </>
          ) : (
            <div className="py-8 text-center">
              <Share2 className="mx-auto mb-4 size-12 text-muted-foreground" />
              <h4 className="mb-2 font-semibold text-foreground">No Forms Available</h4>
              <p className="mb-4 text-sm text-muted-foreground">
                Create your first form to start sharing with startups
              </p>
              <Button asChild size="sm">
                <a href="/forms/new">Create Form</a>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
