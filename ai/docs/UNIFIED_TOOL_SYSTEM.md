# Unified Tool Registration System

## Overview

The TractionX AI Agent system now features a unified, decorator-based tool registration system that eliminates manual tool registration and provides automatic discovery, categorization, and management of AI agent tools.

## Key Features

### 🎯 Automatic Discovery
- Tools are automatically discovered and registered using the `@tool` decorator
- No more manual registration in multiple files
- Dynamic tool loading at runtime

### 🏷️ Metadata-Driven
- Rich metadata support (categories, tags, descriptions)
- Automatic name generation from class names
- Flexible filtering and querying

### 🔧 Centralized Management
- Single `tool_registry` instance manages all tools
- Consistent API for tool access and execution
- Built-in caching and instance management

### 📊 Developer Experience
- Clean, declarative tool definitions
- Comprehensive CLI tool for management
- API endpoints for runtime inspection

## Usage

### Defining a Tool

```python
from ai.tools.base import BaseTool, tool, ToolInput, ToolOutput

@tool(
    name="my_analysis_tool",  # Optional: auto-generated from class name
    description="Performs advanced data analysis",
    category="analysis",
    tags=["data", "analysis", "ml"],
    enabled=True
)
class MyAnalysisTool(BaseTool):
    """Advanced data analysis tool."""
    
    def __init__(self):
        super().__init__()
        # Tool initialization
    
    async def execute(self, input_data: ToolInput, context: AgentContext) -> ToolOutput:
        # Tool implementation
        return ToolOutput(success=True, data={"result": "analysis complete"})
```

### Accessing Tools

```python
from ai.tools.base import tool_registry

# Get all tools
all_tools = tool_registry.get_all_tools()

# Get tools by category
analysis_tools = tool_registry.get_tools_by_category("analysis")

# Get tools by tags
ml_tools = tool_registry.get_tools_by_tags(["ml", "data"])

# Get specific tool
tool = tool_registry.get_tool_instance("my_analysis_tool")
```

### Tool Discovery

```python
# Trigger discovery (happens automatically)
tool_registry.discover_tools()

# List available tools with metadata
tools = tool_registry.list_available_tools()
```

## Architecture

### Core Components

1. **`@tool` Decorator**: Registers tools with metadata
2. **`ToolRegistry`**: Manages tool discovery and instances
3. **`BaseTool`**: Enhanced base class with metadata support
4. **Auto-discovery**: Automatic module importing and registration

### Registration Flow

```
1. Tool class defined with @tool decorator
2. Decorator registers tool in global registry
3. ToolRegistry.discover_tools() imports all tool modules
4. Tools are available via registry methods
5. AgentRunner uses registry instead of manual registration
```

### File Structure

```
ai/
├── tools/
│   ├── __init__.py          # Auto-imports all tool modules
│   ├── base.py              # Registry and decorator system
│   ├── thesis_tools.py      # @tool decorated tools
│   ├── document_tools.py    # @tool decorated tools
│   ├── memo_tools.py        # @tool decorated tools
│   └── ...
├── api/
│   └── tools.py             # Tool management API
├── scripts/
│   └── tool_manager.py      # CLI tool manager
└── docs/
    └── UNIFIED_TOOL_SYSTEM.md
```

## Migration Guide

### Before (Manual Registration)

```python
# In runner.py
def _register_tools(self):
    self.tool_registry["thesis_matcher"] = ThesisMatcherTool()
    self.tool_registry["scoring_engine"] = ScoringEngineTool()
    # ... manual registration for each tool

# In tool definition
class ThesisMatcherTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="thesis_matcher",
            description="Analyzes investment opportunities"
        )
```

### After (Automatic Registration)

```python
# In runner.py
def __init__(self):
    tool_registry.discover_tools()  # Automatic discovery

@property
def tool_registry(self):
    return tool_registry.get_all_tools()

# In tool definition
@tool(
    name="thesis_matcher",
    description="Analyzes investment opportunities",
    category="analysis",
    tags=["thesis", "matching", "scoring"]
)
class ThesisMatcherTool(BaseTool):
    def __init__(self):
        super().__init__()  # No manual name/description
```

## CLI Tool Manager

Use the CLI tool to manage and inspect tools:

```bash
# List all tools
python ai/scripts/tool_manager.py list

# List tools by category
python ai/scripts/tool_manager.py list --category analysis

# List tools by tags
python ai/scripts/tool_manager.py list --tags "llm,analysis"

# Show tool information
python ai/scripts/tool_manager.py info thesis_matcher

# List categories
python ai/scripts/tool_manager.py categories

# Test discovery system
python ai/scripts/tool_manager.py test-discovery

# Test tool execution
python ai/scripts/tool_manager.py test-exec web_search
```

## API Endpoints

### List Tools
```
GET /tools/
GET /tools/?category=analysis
GET /tools/?tags=llm,analysis
```

### Tool Information
```
GET /tools/{tool_name}
```

### Execute Tool
```
POST /tools/{tool_name}/execute
{
  "tool_name": "web_search",
  "input_data": {
    "query": "AI startup news",
    "limit": 5
  }
}
```

### Tools by Category
```
GET /tools/categories/{category}
```

### Discover Tools
```
POST /tools/discover
```

## Benefits

### For Developers
- ✅ No more manual registration in multiple places
- ✅ Automatic name generation and metadata
- ✅ Rich categorization and tagging
- ✅ Consistent tool interface
- ✅ Easy testing and debugging

### For System
- ✅ Dynamic tool loading
- ✅ Better error handling
- ✅ Centralized management
- ✅ Runtime introspection
- ✅ Scalable architecture

### For Operations
- ✅ Tool discovery and monitoring
- ✅ Category-based organization
- ✅ Tag-based filtering
- ✅ API-driven management
- ✅ CLI tooling

## Best Practices

### Tool Definition
```python
@tool(
    description="Clear, concise description of what the tool does",
    category="logical_category",  # analysis, document, search, etc.
    tags=["relevant", "keywords"],  # for discovery and filtering
    enabled=True  # can disable tools without removing code
)
class WellNamedTool(BaseTool):
    """Detailed docstring explaining the tool's purpose and usage."""
```

### Categories
- `analysis`: Data analysis and scoring tools
- `document`: Document processing and extraction
- `search`: Search and retrieval tools
- `intelligence`: AI-powered analysis tools
- `memo`: Report and memo generation tools

### Tags
- Use descriptive, searchable keywords
- Include technology tags: `llm`, `ai`, `ml`
- Include domain tags: `investment`, `startup`, `finance`
- Include capability tags: `extraction`, `analysis`, `generation`

## Future Enhancements

- **Tool Versioning**: Support for multiple tool versions
- **Dependency Management**: Tool dependency resolution
- **Performance Monitoring**: Tool execution metrics
- **A/B Testing**: Tool variant testing
- **Hot Reloading**: Dynamic tool updates without restart
