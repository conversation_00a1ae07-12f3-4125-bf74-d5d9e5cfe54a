"use client"

import React, { useState, useEffect, useRef } from 'react';
import { X, Save, AlertCircle, ChevronDown, FileText, Check } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogBody,
} from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  ScoringRule,
  RuleType,
  ConditionOperator,
  AggregationType,
  FilterCondition,
  getOperatorDisplay,
  getAggregationDisplay
} from '@/lib/types/thesis';
import { QuestionType } from '@/lib/types/form';
import { RuleSummary } from './rule-summary';

interface RuleEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (rule: Partial<ScoringRule>) => void;
  rule?: Partial<ScoringRule>;
  questionId?: string;
  questionType?: QuestionType;
  questionLabel?: string;
  isRepeatable?: boolean;
  sectionId?: string;
  allowBonusRules?: boolean; // New prop to control bonus rule creation
  questionOptions?: Array<{ label: string; value: string }>; // Question options for MCQ/Boolean
}

// Get valid operators for a question type
function getValidOperators(questionType: QuestionType, isRepeatable: boolean = false): ConditionOperator[] {
  // For repeatable sections, only show numeric operators
  if (isRepeatable) {
    return [
      ConditionOperator.EQUALS,
      ConditionOperator.NOT_EQUALS,
      ConditionOperator.GREATER_THAN,
      ConditionOperator.LESS_THAN,
      ConditionOperator.GREATER_THAN_EQUALS,
      ConditionOperator.LESS_THAN_EQUALS,
      ConditionOperator.BETWEEN,
      ConditionOperator.NOT_BETWEEN
    ];
  }

  switch (questionType) {
    case QuestionType.SHORT_TEXT:
    case QuestionType.LONG_TEXT:
      // Text questions don't need operators - they use good/bad answer references
      return [];
    case QuestionType.NUMBER:
    case QuestionType.RANGE:
      return [
        ConditionOperator.EQUALS,
        ConditionOperator.NOT_EQUALS,
        ConditionOperator.GREATER_THAN,
        ConditionOperator.LESS_THAN,
        ConditionOperator.GREATER_THAN_EQUALS,
        ConditionOperator.LESS_THAN_EQUALS,
        ConditionOperator.BETWEEN,
        ConditionOperator.NOT_BETWEEN
      ];
    case QuestionType.BOOLEAN:
      return [ConditionOperator.EQUALS, ConditionOperator.NOT_EQUALS];
    case QuestionType.SINGLE_SELECT:
    case QuestionType.MULTI_SELECT:
      return [
        ConditionOperator.EQUALS,
        ConditionOperator.NOT_EQUALS,
        ConditionOperator.IN,
        ConditionOperator.NOT_IN,
        ConditionOperator.CONTAINS,
        ConditionOperator.NOT_CONTAINS
      ];
    case QuestionType.DATE:
      return [
        ConditionOperator.EQUALS,
        ConditionOperator.NOT_EQUALS,
        ConditionOperator.GREATER_THAN,
        ConditionOperator.LESS_THAN,
        ConditionOperator.GREATER_THAN_EQUALS,
        ConditionOperator.LESS_THAN_EQUALS,
        ConditionOperator.BETWEEN,
        ConditionOperator.NOT_BETWEEN
      ];
    case QuestionType.FILE:
      // File questions should never be scorable
      return [];
    default:
      return [ConditionOperator.EQUALS, ConditionOperator.NOT_EQUALS];
  }
}

// Check if question type is text-based
function isTextQuestion(questionType: QuestionType): boolean {
  return questionType === QuestionType.SHORT_TEXT || questionType === QuestionType.LONG_TEXT;
}

// Check if question type is file-based
function isFileQuestion(questionType: QuestionType): boolean {
  return questionType === QuestionType.FILE;
}

// Extract good/bad references from condition value for text questions
function getTextReferencesFromCondition(condition: FilterCondition | undefined): { good_reference: string; bad_reference: string } {
  if (!condition || !condition.value) {
    return { good_reference: '', bad_reference: '' };
  }

  // If it's already a dict with good/bad references
  if (typeof condition.value === 'object' && condition.value.good_reference !== undefined) {
    return {
      good_reference: condition.value.good_reference || '',
      bad_reference: condition.value.bad_reference || ''
    };
  }

  // Legacy support for good_answers/bad_answers
  if (typeof condition.value === 'object' && condition.value.good_answers !== undefined) {
    return {
      good_reference: condition.value.good_answers || '',
      bad_reference: condition.value.bad_answers || ''
    };
  }

  // Handle legacy string values (like "Good answer" from your example)
  if (typeof condition.value === 'string') {
    console.warn('⚠️ Legacy string value found in text question condition:', condition.value);
    return {
      good_reference: condition.value, // Put the string in good_reference
      bad_reference: '' // Leave bad_reference empty
    };
  }

  return { good_reference: '', bad_reference: '' };
}

// Create condition value dict for text questions
function createTextConditionValue(good_reference: string, bad_reference: string): object {
  return {
    good_reference: good_reference?.trim() || '',
    bad_reference: bad_reference?.trim() || ''
  };
}

// Tags Picker Component for MCQ/Boolean question options
interface TagsPickerProps {
  selectedValues: string[];
  onValuesChange: (values: string[]) => void;
  options: Array<{ label: string; value: string }>;
  placeholder?: string;
  disabled?: boolean;
  allowMultiple?: boolean;
}

function TagsPicker({ 
  selectedValues, 
  onValuesChange, 
  options, 
  placeholder = "Select options...", 
  disabled = false,
  allowMultiple = true 
}: TagsPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Handle boolean questions specially - convert true/false to Yes/No for display
  const displayOptions = options.map(option => ({
    ...option,
    label: option.value === 'true' ? 'Yes' : option.value === 'false' ? 'No' : option.label
  }));

  // Convert display values back to boolean for storage
  const handleValueChange = (values: string[]) => {
    const convertedValues = values.map(value => {
      if (value === 'Yes') return 'true';
      if (value === 'No') return 'false';
      return value;
    });
    onValuesChange(convertedValues);
  };

  // Convert stored values to display values
  const displayValues = selectedValues.map(value => {
    if (value === 'true') return 'Yes';
    if (value === 'false') return 'No';
    return value;
  });

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const toggleOption = (value: string) => {
    if (disabled) return;
    
    const displayValue = value === 'true' ? 'Yes' : value === 'false' ? 'No' : value;
    
    if (allowMultiple) {
      const newValues = displayValues.includes(displayValue)
        ? displayValues.filter(v => v !== displayValue)
        : [...displayValues, displayValue];
      handleValueChange(newValues);
    } else {
      handleValueChange([value]);
      setIsOpen(false);
    }
  };

  const removeValue = (value: string) => {
    if (disabled) return;
    const newValues = displayValues.filter(v => v !== value);
    handleValueChange(newValues);
  };

  const getOptionLabel = (value: string) => {
    const option = displayOptions.find(opt => opt.value === value);
    return option ? option.label : value;
  };

  return (
    <div className="relative" ref={containerRef}>
      {/* Selected Tags Display */}
      <div
        className={cn(
          "min-h-touch-lg cursor-pointer rounded-xl border border-gray-200 px-4 py-2 transition-all duration-150",
          "focus-within:border-blue-400 focus-within:ring-2 focus-within:ring-blue-200 hover:border-gray-300",
          isOpen && "border-blue-400 ring-2 ring-blue-200",
          disabled && "cursor-not-allowed opacity-50"
        )}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        <div className="flex flex-wrap gap-2">
          {displayValues.length > 0 ? (
            displayValues.map((value) => (
              <Badge
                key={value}
                variant="secondary"
                className="px-2 py-1 text-sm"
              >
                {getOptionLabel(value)}
                {!disabled && (
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeValue(value);
                    }}
                    className="ml-1 hover:text-destructive"
                  >
                    <X className="size-3" />
                  </button>
                )}
              </Badge>
            ))
          ) : (
            <span className="text-sm text-muted-foreground">
              {placeholder}
            </span>
          )}
        </div>
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute inset-x-0 top-full z-50 mt-1 max-h-64 overflow-hidden rounded-lg border border-gray-200 bg-white shadow-lg">
          {/* Options List */}
          <div className="max-h-48 overflow-y-auto">
            {displayOptions.length > 0 ? (
              displayOptions.map((option) => {
                const isSelected = displayValues.includes(option.value);
                return (
                  <div
                    key={option.value}
                    className={cn(
                      "cursor-pointer px-4 py-2 transition-colors hover:bg-gray-50",
                      isSelected && "bg-blue-50 text-blue-700"
                    )}
                    onClick={() => toggleOption(option.value)}
                  >
                    <div className="flex items-center gap-2">
                      <div className={cn(
                        "size-4 rounded border",
                        isSelected 
                          ? "border-blue-600 bg-blue-600" 
                          : "border-gray-300"
                      )}>
                        {isSelected && (
                          <Check className="size-3 text-white" />
                        )}
                      </div>
                      <span className="text-sm">{option.label}</span>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="px-4 py-2 text-sm text-muted-foreground">
                No options available
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export function RuleEditor({
  isOpen,
  onClose,
  onSave,
  rule,
  questionId,
  questionType = QuestionType.SHORT_TEXT,
  questionLabel,
  isRepeatable = false,
  sectionId,
  allowBonusRules = true, // Default to true for backward compatibility
  questionOptions = [] // Question options for MCQ/Boolean questions
}: RuleEditorProps) {
  const [formData, setFormData] = useState<Partial<ScoringRule>>({
    rule_type: RuleType.SCORING,
    weight: 1.0,
    is_deleted: false,
    question_id: questionId || '', // Ensure question_id is set
    // REQUIRED: Always include condition field (backend validation requires this)
    condition: {
      question_id: questionId || '',
      operator: ConditionOperator.EQUALS,
      value: ''
    } as FilterCondition,
    ...rule
  });

  const [errors, setErrors] = useState<string[]>([]);
  const [warnings, setWarnings] = useState<string[]>([]);

  // Check if this is a file question and should not be scorable
  const isFileQuestionType = isFileQuestion(questionType);
  const isTextQuestionType = isTextQuestion(questionType);

  // Extract text references for form state
  const [textReferences, setTextReferences] = useState({ good_reference: '', bad_reference: '' });

  // Reset form when rule changes
  useEffect(() => {
    if (rule) {
      setFormData({
        ...rule,
        // Ensure question_id is set for the rule
        question_id: questionId || rule.question_id,
        // Ensure condition has the correct question_id - handle both FilterCondition and CompoundFilter
        condition: rule.condition ? (() => {
          // Check if it's a FilterCondition (has operator and value)
          if ('operator' in rule.condition && 'value' in rule.condition) {
            // For text questions, remove operator from existing rules
            if (isTextQuestionType) {
              return {
                question_id: questionId || rule.question_id || (rule.condition as FilterCondition).question_id,
                value: (rule.condition as FilterCondition).value
                // NO operator for text questions
              } as FilterCondition;
            } else {
              return {
                ...rule.condition,
                question_id: questionId || rule.question_id || (rule.condition as FilterCondition).question_id
              } as FilterCondition;
            }
          } else {
            // It's a CompoundFilter, return as-is
            return rule.condition;
          }
        })() : isTextQuestionType ? {
          question_id: questionId || rule.question_id || '',
          value: createTextConditionValue('', '')
          // NO operator for text questions
        } as FilterCondition : {
          question_id: questionId || rule.question_id || '',
          operator: ConditionOperator.EQUALS,
          value: ''
        } as FilterCondition
      });

      // Extract text references if it's a text question
      if (isTextQuestionType && rule.condition && 'value' in rule.condition) {
        const extracted = getTextReferencesFromCondition(rule.condition as FilterCondition);
        setTextReferences(extracted);
      }
    } else {
      const defaultCondition = isTextQuestionType ? {
        question_id: questionId || '',
        value: createTextConditionValue('', '')
        // NO operator for text questions
      } as FilterCondition : {
        question_id: questionId || '',
        operator: ConditionOperator.EQUALS,
        value: ''
      } as FilterCondition;

      setFormData({
        rule_type: RuleType.SCORING,
        weight: 1.0,
        question_id: questionId || '',
        is_deleted: false,
        condition: defaultCondition
      });

      setTextReferences({ good_reference: '', bad_reference: '' });
    }
  }, [rule, questionId, isTextQuestionType]);

  // Update condition value when text references change
  useEffect(() => {
    if (isTextQuestionType) {
      setFormData(prev => ({
        ...prev,
        condition: {
          question_id: questionId || prev.question_id || '',
          // NO operator for text questions as per requirements
          value: createTextConditionValue(textReferences.good_reference, textReferences.bad_reference)
        } as FilterCondition
      }));
    }
  }, [textReferences, isTextQuestionType, questionId]);

  // Validate form
  useEffect(() => {
    const newErrors: string[] = [];
    const newWarnings: string[] = [];

    // Debug logging for text questions
    if (isTextQuestionType) {
      console.log('🔍 Text question validation:', {
        textReferences,
        conditionValue: formData.condition && 'value' in formData.condition ? formData.condition.value : undefined,
        formData: formData
      });
    }

    // File question validation
    if (isFileQuestionType) {
      newErrors.push('Files cannot be scored directly. Please choose another question.');
      setErrors(newErrors);
      setWarnings(newWarnings);
      return;
    }

    // Bonus rule validation for repeatable sections
    if (formData.rule_type === RuleType.BONUS && isRepeatable) {
      newErrors.push('Bonus rules cannot target questions inside repeatable sections.');
    }

    // CRITICAL: Validate condition field (required by backend for ALL rules)
    if (!formData.condition) {
      newErrors.push('Condition is required for all rules');
    } else if ('question_id' in formData.condition) {
      // Simple condition validation
      if (!formData.condition.question_id) {
        newErrors.push('Question ID is required in condition');
      }
      
      // For text questions, validate that at least one reference is provided
      if (isTextQuestionType) {
        // Check the actual condition.value object, not just textReferences state
        if (formData.condition && 'value' in formData.condition) {
          const conditionValue = formData.condition.value;
          if (typeof conditionValue === 'object' && conditionValue !== null) {
            const hasGoodRef = conditionValue.good_reference && conditionValue.good_reference.trim().length > 0;
            const hasBadRef = conditionValue.bad_reference && conditionValue.bad_reference.trim().length > 0;
            if (!hasGoodRef && !hasBadRef) {
              newErrors.push('At least one reference (good or bad) is required for text question scoring');
            }
          } else {
            newErrors.push('Text question scoring requires good and bad reference examples');
          }
        } else {
          newErrors.push('Invalid condition structure for text question');
        }
        // Text questions should NOT have an operator
        if (formData.condition && 'operator' in formData.condition && formData.condition.operator) {
          console.warn('⚠️ Text question has operator, removing it');
        }
      } else {
        // Non-text questions need operator and value
        if (formData.condition && 'operator' in formData.condition) {
          if (!formData.condition.operator) {
            newErrors.push('Operator is required in condition');
          }
          if (formData.condition.value === undefined || formData.condition.value === '') {
            newErrors.push('Condition value is required');
          }
        } else {
          newErrors.push('Invalid condition structure for non-text question');
        }
      }
    }

    if (formData.rule_type === RuleType.SCORING) {
      if (!formData.question_id) {
        newErrors.push('Question ID is required for scoring rules');
      }
      if (!formData.weight || formData.weight <= 0) {
        newErrors.push('Weight must be positive');
      }
    }

    if (formData.rule_type === RuleType.BONUS) {
      if (!formData.bonus_points || formData.bonus_points <= 0) {
        newErrors.push('Bonus points must be positive');
      }
    }

    if (isRepeatable && formData.aggregation) {
      // Validate aggregation threshold for types that require it
      if ([AggregationType.COUNT, AggregationType.PERCENTAGE, AggregationType.SUM].includes(formData.aggregation)) {
        if (!formData.aggregate_threshold) {
          newErrors.push('Threshold value is required for this aggregation type');
        }
      }
      
      if (formData.aggregation !== AggregationType.COUNT && formData.aggregation !== AggregationType.NONE) {
        if (!formData.value_field) {
          newErrors.push('Value field is required for this aggregation type');
        }
      }
      if (!formData.value_field) {
        newWarnings.push('Value field is recommended for aggregation');
      }
    }

    setErrors(newErrors);
    setWarnings(newWarnings);
  }, [formData, isRepeatable, isFileQuestionType, isTextQuestionType, textReferences]);

  const handleSave = () => {
    console.log('🚀 handleSave called:', {
      errorsLength: errors.length,
      errors: errors,
      isTextQuestionType,
      textReferences,
      formData,
      conditionValue: formData.condition && 'value' in formData.condition ? formData.condition.value : undefined
    });

    if (errors.length === 0) {
      // Ensure we have a clean payload that matches backend expectations
      let cleanedCondition = formData.condition;

      // For text questions, ensure no operator is sent and value is properly structured
      if (isTextQuestionType && cleanedCondition && 'value' in cleanedCondition) {
        cleanedCondition = {
          question_id: cleanedCondition.question_id,
          value: cleanedCondition.value
          // NO operator for text questions
        } as FilterCondition;

        console.log('🧹 Cleaned condition for text question:', cleanedCondition);
      }

      const cleanedData: Partial<ScoringRule> = {
        rule_type: formData.rule_type || RuleType.SCORING,
        question_id: formData.question_id || questionId,
        weight: formData.weight || 1.0,
        condition: cleanedCondition,
        bonus_points: formData.bonus_points,
        aggregation: formData.aggregation,
        aggregate_threshold: formData.aggregate_threshold,
        aggregate_operator: formData.aggregate_operator,
        value_field: formData.value_field,
        section_id: isRepeatable ? sectionId : undefined, // Include section_id for repeatable sections
        notes: formData.notes,
        is_deleted: false
      };

      // Remove undefined values to avoid backend validation issues
      Object.keys(cleanedData).forEach(key => {
        if (cleanedData[key as keyof typeof cleanedData] === undefined) {
          delete cleanedData[key as keyof typeof cleanedData];
        }
      });

      console.log('💾 Saving rule for', isTextQuestionType ? 'text question' : 'standard question', ':', {
        ...cleanedData,
        isTextQuestion: isTextQuestionType,
        textReferences: isTextQuestionType ? textReferences : undefined,
        conditionValue: cleanedData.condition && 'value' in cleanedData.condition ? cleanedData.condition.value : undefined
      });

      onSave(cleanedData);
    }
  };

  const validOperators = getValidOperators(questionType, isRepeatable);

  // Show file question error message
  if (isFileQuestionType) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent size="small">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="size-5" />
              File Question Not Scorable
            </DialogTitle>
            <DialogDescription>
              File upload questions cannot be used for scoring rules.
            </DialogDescription>
          </DialogHeader>

          <DialogBody>
            <Alert variant="destructive">
              <AlertCircle className="size-4" />
              <AlertDescription>
                Files cannot be scored directly. Please choose another question type for your scoring rule.
              </AlertDescription>
            </Alert>
          </DialogBody>

          <DialogFooter>
            <Button onClick={onClose} className="h-12 rounded-xl px-6 font-semibold">
              Choose Another Question
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent size="large">
        <DialogHeader>
          <DialogTitle>
            {rule ? 'Edit Scoring Rule' : 'Create Scoring Rule'}
          </DialogTitle>
          <DialogDescription>
            Configure how this question will be scored in your thesis.
          </DialogDescription>
          {questionLabel && (
            <div className="mt-2 rounded-lg bg-gray-50 p-3 text-sm">
              <strong>Question:</strong> {questionLabel}
              {isRepeatable && (
                <Badge variant="outline" className="ml-2 text-xs">
                  Repeatable Section
                </Badge>
              )}
            </div>
          )}
        </DialogHeader>

        <DialogBody>
          {/* Rule Type */}
          <div className="space-y-2 px-2">
            <Label className="mb-0 text-sm font-medium md:text-base">Rule Type</Label>
            <Select
              value={formData.rule_type}
              onValueChange={(value) => setFormData(prev => ({
                ...prev,
                rule_type: value as RuleType
              }))}
              disabled={!allowBonusRules && formData.rule_type === RuleType.SCORING}
            >
              <SelectTrigger className="h-12 rounded-xl border-gray-200 px-4">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={RuleType.SCORING}>Scoring Rule</SelectItem>
                {allowBonusRules && !isRepeatable && (
                  <SelectItem value={RuleType.BONUS}>Bonus Rule</SelectItem>
                )}
              </SelectContent>
            </Select>
            {!allowBonusRules && (
              <p className="text-xs text-muted-foreground">
                Bonus rules can only be created in the Bonus tab
              </p>
            )}
            {isRepeatable && formData.rule_type === RuleType.BONUS && (
              <p className="text-xs text-destructive">
                Bonus rules cannot target questions inside repeatable sections
              </p>
            )}
          </div>

          {/* Weight (for scoring rules) */}
          {formData.rule_type === RuleType.SCORING && (
            <div className="space-y-1 px-2">
              <Label className="mb-0 text-sm font-medium md:text-base">Weight</Label>
              <Input
                type="number"
                min="0"
                step="0.1"
                value={formData.weight || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  weight: e.target.value ? Number(e.target.value) : 1
                }))}
                placeholder="1.0"
                className="box-border h-8 rounded-xl border border-gray-200 px-4 transition-all duration-150 focus:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-200"
              />
            </div>
          )}

          {/* Bonus Points (for bonus rules) */}
          {formData.rule_type === RuleType.BONUS && (
            <div className="space-y-2 px-2">
              <Label className="mb-1 text-sm font-medium md:text-base">Bonus Points</Label>
              <Input
                type="number"
                min="0"
                step="0.1"
                value={formData.bonus_points || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  bonus_points: e.target.value ? Number(e.target.value) : 0
                }))}
                placeholder="2.0"
                className="h-12 rounded-xl border-gray-200 px-4"
              />
            </div>
          )}

          {/* Condition Operator - Only for non-text questions */}
          {!isTextQuestionType && validOperators.length > 0 && (
            <div className="space-y-2 px-2">
              <Label className="mb-1 text-sm font-medium md:text-base">Operator</Label>
              <Select
                value={formData.condition && 'operator' in formData.condition ? formData.condition.operator : ''}
                onValueChange={(value) => setFormData(prev => ({
                  ...prev,
                  condition: {
                    question_id: questionId || prev.question_id || '',
                    operator: value as ConditionOperator,
                    value: (prev.condition as FilterCondition)?.value || ''
                  }
                }))}
              >
                <SelectTrigger className="h-12 rounded-xl border-gray-200 px-4">
                  <SelectValue placeholder="Select operator" />
                </SelectTrigger>
                <SelectContent>
                  {validOperators.map(op => (
                    <SelectItem key={op} value={op}>
                      {getOperatorDisplay(op)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Condition Value - Only show for non-text questions */}
          {!isTextQuestionType && (
            <div className="space-y-2 px-2">
              <Label className="mb-1 text-sm font-medium md:text-base">Expected Value</Label>
              {(questionType === QuestionType.SINGLE_SELECT || 
                questionType === QuestionType.MULTI_SELECT || 
                questionType === QuestionType.BOOLEAN) && questionOptions.length > 0 ? (
                <TagsPicker
                  selectedValues={(() => {
                    const currentValue = formData.condition && 'value' in formData.condition ? formData.condition.value : '';
                    if (Array.isArray(currentValue)) {
                      return currentValue;
                    }
                    if (typeof currentValue === 'string' && currentValue) {
                      // Try to parse as array first, then split by comma, or use as single value
                      try {
                        const parsed = JSON.parse(currentValue);
                        return Array.isArray(parsed) ? parsed : [currentValue];
                      } catch {
                        return currentValue.includes(',') ? currentValue.split(',').map(v => v.trim()) : [currentValue];
                      }
                    }
                    return [];
                  })()}
                  onValuesChange={(values) => setFormData(prev => ({
                    ...prev,
                    condition: {
                      question_id: questionId || prev.question_id || '',
                      operator: (prev.condition as FilterCondition)?.operator || ConditionOperator.EQUALS,
                      value: values.length === 1 ? values[0] : values // Store array directly for multiple values
                    }
                  }))}
                  options={questionOptions}
                  placeholder={`Select ${questionType === QuestionType.BOOLEAN ? 'true or false' : 'options'}...`}
                  allowMultiple={true}
                  disabled={!isOpen}
                />
              ) : questionType === QuestionType.DATE ? (
                (() => {
                  const currentOperator = (formData.condition as FilterCondition)?.operator;
                  const isBetweenOperator = currentOperator === ConditionOperator.BETWEEN || currentOperator === ConditionOperator.NOT_BETWEEN;
                  
                  if (isBetweenOperator) {
                    // Handle date range for Between/Not Between operators
                    const currentValue = formData.condition && 'value' in formData.condition ? formData.condition.value : '';
                    let startDate = '';
                    let endDate = '';
                    
                    if (Array.isArray(currentValue) && currentValue.length === 2) {
                      try {
                        const start = new Date(currentValue[0]);
                        const end = new Date(currentValue[1]);
                        if (!isNaN(start.getTime())) startDate = start.toISOString().split('T')[0];
                        if (!isNaN(end.getTime())) endDate = end.toISOString().split('T')[0];
                      } catch (e) {
                        console.warn('Invalid date range value:', currentValue);
                      }
                    }
                    
                    return (
                      <div className="space-y-2 px-2">
                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <Label className="text-xs">Start Date</Label>
                            <Input
                              type="date"
                              value={startDate}
                              onChange={(e) => {
                                const endValue = endDate ? new Date(endDate).toISOString() : '';
                                const startValue = e.target.value ? new Date(e.target.value).toISOString() : '';
                                setFormData(prev => ({
                                  ...prev,
                                  condition: {
                                    question_id: questionId || prev.question_id || '',
                                    operator: currentOperator,
                                    value: [startValue, endValue].filter(v => v)
                                  }
                                }));
                              }}
                              placeholder="Start date"
                              className="h-12 rounded-xl border-gray-200 px-4"
                            />
                          </div>
                          <div>
                            <Label className="text-xs">End Date</Label>
                            <Input
                              type="date"
                              value={endDate}
                              onChange={(e) => {
                                const startValue = startDate ? new Date(startDate).toISOString() : '';
                                const endValue = e.target.value ? new Date(e.target.value).toISOString() : '';
                                setFormData(prev => ({
                                  ...prev,
                                  condition: {
                                    question_id: questionId || prev.question_id || '',
                                    operator: currentOperator,
                                    value: [startValue, endValue].filter(v => v)
                                  }
                                }));
                              }}
                              placeholder="End date"
                              className="h-12 rounded-xl border-gray-200 px-4"
                            />
                          </div>
                        </div>
                      </div>
                    );
                  } else {
                    // Handle single date for other operators
                    return (
                      <Input
                        type="date"
                        value={(() => {
                          const currentValue = formData.condition && 'value' in formData.condition ? formData.condition.value : '';
                          // Convert various date formats to YYYY-MM-DD for the date input
                          if (currentValue) {
                            try {
                              const date = new Date(currentValue);
                              if (!isNaN(date.getTime())) {
                                return date.toISOString().split('T')[0]; // Get YYYY-MM-DD format
                              }
                            } catch (e) {
                              console.warn('Invalid date value:', currentValue);
                            }
                          }
                          return '';
                        })()}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          condition: {
                            question_id: questionId || prev.question_id || '',
                            operator: (prev.condition as FilterCondition)?.operator || ConditionOperator.EQUALS,
                            value: e.target.value ? new Date(e.target.value).toISOString() : ''
                          }
                        }))}
                        placeholder="Select date"
                        className="h-12 rounded-xl border-gray-200 px-4"
                      />
                    );
                  }
                })()
              ) : questionType === QuestionType.NUMBER || questionType === QuestionType.RANGE ? (
                <Input
                  type="number"
                  value={formData.condition && 'value' in formData.condition ? formData.condition.value : ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    condition: {
                      question_id: questionId || prev.question_id || '',
                      operator: (prev.condition as FilterCondition)?.operator || ConditionOperator.EQUALS,
                      value: e.target.value ? Number(e.target.value) : ''
                    }
                  }))}
                  placeholder="Enter numeric value"
                  className="h-12 rounded-xl border-gray-200 px-4"
                />
              ) : isRepeatable ? (
                <Input
                  type="number"
                  value={formData.condition && 'value' in formData.condition ? formData.condition.value : ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    condition: {
                      question_id: questionId || prev.question_id || '',
                      operator: (prev.condition as FilterCondition)?.operator || ConditionOperator.EQUALS,
                      value: e.target.value ? Number(e.target.value) : ''
                    }
                  }))}
                  placeholder="Enter numeric value"
                  className="h-12 rounded-xl border-gray-200 px-4"
                />
              ) : (
                <Input
                  value={formData.condition && 'value' in formData.condition ? formData.condition.value : ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    condition: {
                      question_id: questionId || prev.question_id || '',
                      operator: (prev.condition as FilterCondition)?.operator || ConditionOperator.EQUALS,
                      value: e.target.value
                    }
                  }))}
                  placeholder="Enter expected value"
                  className="h-12 rounded-xl border-gray-200 px-4"
                />
              )}
              {questionType === QuestionType.DATE && (
                <p className="text-xs text-muted-foreground">
                  {(() => {
                    const currentOperator = (formData.condition as FilterCondition)?.operator;
                    const isBetweenOperator = currentOperator === ConditionOperator.BETWEEN || currentOperator === ConditionOperator.NOT_BETWEEN;
                    
                    if (isBetweenOperator) {
                      return `Select a date range. Responses with dates ${currentOperator === ConditionOperator.BETWEEN ? 'between' : 'outside'} these dates will match.`;
                    } else {
                      return 'Select a date to compare against. Use operators like "Greater Than" for dates after this date.';
                    }
                  })()}
                </p>
              )}
            </div>
          )}

          {/* Text Answer Scoring (for text questions only) */}
          {isTextQuestionType && (
            <>
              <div className="space-y-2 px-2">
                <Label className="mb-1 text-sm font-medium md:text-base">Good Reference Examples</Label>
                <Textarea
                  value={textReferences.good_reference}
                  onChange={(e) => setTextReferences(prev => ({
                    ...prev,
                    good_reference: e.target.value
                  }))}
                  placeholder="Examples of good answers that should score highly (one per line)&#10;e.g.:&#10;Strong technical team with relevant experience&#10;Clear path to market penetration&#10;Solid revenue model"
                  className="min-h-[80px] rounded-xl border-gray-200 px-4 py-3"
                />
                <p className="text-xs text-muted-foreground">
                  Define examples of high-quality answers. The AI will use these to score similar responses highly.
                </p>
              </div>

              <div className="space-y-2 px-2">
                <Label className="mb-1 text-sm font-medium md:text-base">Bad Reference Examples</Label>
                <Textarea
                  value={textReferences.bad_reference}
                  onChange={(e) => setTextReferences(prev => ({
                    ...prev,
                    bad_reference: e.target.value
                  }))}
                  placeholder="Examples of poor answers that should score low (one per line)&#10;e.g.:&#10;Vague or generic responses&#10;No clear business model&#10;Unrealistic projections"
                  className="min-h-[80px] rounded-xl border-gray-200 px-4 py-3"
                />
                <p className="text-xs text-muted-foreground">
                  Define examples of poor answers. The AI will use these to score similar responses poorly.
                </p>
              </div>

              {/* Legacy warning if we detected a string value */}
              {rule && rule.condition && 'value' in rule.condition && typeof rule.condition.value === 'string' && (
                <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-3 dark:border-yellow-800 dark:bg-yellow-950/20">
                  <p className="text-sm text-yellow-700 dark:text-yellow-300">
                    <strong>⚠️ Legacy Format Detected:</strong> This rule was created with an older format. Please update both good and bad references and save to migrate to the new format.
                  </p>
                </div>
              )}

              <div className="rounded-lg border border-blue-200 bg-blue-50 p-3 dark:border-blue-800 dark:bg-blue-950/20">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  <strong>Note:</strong> At least one reference (good or bad) is required. Both are recommended for optimal AI scoring accuracy.
                </p>
              </div>
            </>
          )}

          {/* Aggregation (only for BONUS rules in repeatable sections) */}
          {isRepeatable && formData.rule_type === RuleType.BONUS && (
            <>
              <div className="space-y-2 px-2">
                <Label className="mb-1 text-sm font-medium md:text-base">Aggregation Type</Label>
                <Select
                  value={formData.aggregation || ''}
                  onValueChange={(value) => setFormData(prev => ({
                    ...prev,
                    aggregation: value as AggregationType
                  }))}
                >
                  <SelectTrigger className="h-12 rounded-xl border-gray-200 px-4">
                    <SelectValue placeholder="Select aggregation" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(AggregationType).map(type => (
                      <SelectItem key={type} value={type}>
                        {getAggregationDisplay(type)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Threshold and Operator for COUNT, PERCENTAGE, SUM */}
              {[AggregationType.COUNT, AggregationType.PERCENTAGE, AggregationType.SUM].includes(formData.aggregation as AggregationType) && (
                <div className="grid grid-cols-1 gap-4 px-2 lg:grid-cols-2">
                  {formData.aggregation === AggregationType.SUM && (
                    <div className="space-y-2">
                      <Label className="mb-1 text-sm font-medium md:text-base">Comparison Operator</Label>
                      <Select
                        value={formData.aggregate_operator || ConditionOperator.GREATER_THAN_EQUALS}
                        onValueChange={(value) => setFormData(prev => ({
                          ...prev,
                          aggregate_operator: value as ConditionOperator
                        }))}
                      >
                        <SelectTrigger className="h-12 rounded-xl border-gray-200 px-4">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={ConditionOperator.GREATER_THAN_EQUALS}>≥ (Greater than or equal)</SelectItem>
                          <SelectItem value={ConditionOperator.GREATER_THAN}>&gt; (Greater than)</SelectItem>
                          <SelectItem value={ConditionOperator.LESS_THAN_EQUALS}>≤ (Less than or equal)</SelectItem>
                          <SelectItem value={ConditionOperator.LESS_THAN}>&lt; (Less than)</SelectItem>
                          <SelectItem value={ConditionOperator.EQUALS}>= (Equals)</SelectItem>
                          <SelectItem value={ConditionOperator.NOT_EQUALS}>≠ (Not equals)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                  
                  <div className="space-y-2">
                    <Label className="mb-1 text-sm font-medium md:text-base">
                      {formData.aggregation === AggregationType.COUNT ? 'Minimum Count' :
                       formData.aggregation === AggregationType.PERCENTAGE ? 'Minimum Percentage' :
                       'Threshold Value'}
                    </Label>
                    <Input
                      type="number"
                      value={formData.aggregate_threshold || ''}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        aggregate_threshold: e.target.value ? Number(e.target.value) : undefined
                      }))}
                      placeholder={formData.aggregation === AggregationType.COUNT ? 'e.g. 2' :
                                  formData.aggregation === AggregationType.PERCENTAGE ? 'e.g. 50' :
                                  'e.g. 80'}
                      step={formData.aggregation === AggregationType.SUM ? "0.1" : "1"}
                      min={formData.aggregation === AggregationType.PERCENTAGE ? 0 : undefined}
                      max={formData.aggregation === AggregationType.PERCENTAGE ? 100 : undefined}
                      className="h-12 rounded-xl border-gray-200 px-4"
                    />
                    <p className="text-xs text-muted-foreground">
                      {formData.aggregation === AggregationType.COUNT 
                        ? 'Enter the minimum number of items that must match'
                        : formData.aggregation === AggregationType.PERCENTAGE
                        ? 'Enter the minimum percentage (0-100) of items that must match'
                        : 'Enter the numeric threshold to compare the sum against'
                      }
                    </p>
                  </div>
                </div>
              )}

              <div className="space-y-2 px-2">
                <Label className="mb-1 text-sm font-medium md:text-base">
                  Value Field 
                  {formData.aggregation && formData.aggregation !== AggregationType.COUNT && formData.aggregation !== AggregationType.NONE 
                    ? ' (required)' 
                    : ' (optional)'
                  }
                </Label>
                <Input
                  type="number"
                  value={formData.value_field || ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    value_field: e.target.value ? Number(e.target.value) : undefined
                  }))}
                  placeholder="Numeric field for aggregation"
                  className="h-12 rounded-xl border-gray-200 px-4"
                />
                <p className="text-xs text-muted-foreground">
                  For repeatable sections, this should be a numeric value used in aggregation calculations.
                </p>
              </div>
            </>
          )}

          {/* Aggregation Warning for SCORING rules */}
          {isRepeatable && formData.rule_type === RuleType.SCORING && (
            <div className="space-y-2 px-2">
              <Alert>
                <AlertCircle className="size-4" />
                <AlertDescription>
                  <strong>Aggregations are only supported in Bonus Rules.</strong> Scoring rules for questions in repeatable sections will be evaluated per instance individually.
                </AlertDescription>
              </Alert>
            </div>
          )}

          {/* Notes */}
          <div className="space-y-2 px-2">
            <Label className="mb-1 text-sm font-medium md:text-base">Notes (optional)</Label>
            <Textarea
              value={formData.notes || ''}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                notes: e.target.value || undefined
              }))}
              placeholder="Additional notes about this rule"
              className="min-h-[60px] rounded-xl border-gray-200 px-4 py-3"
            />
          </div>

          {/* Rule Summary */}
          <div className="space-y-2 px-2">
            <Label className="mb-1 text-sm font-medium md:text-base">Rule Summary</Label>
            <div className="rounded-lg bg-gray-50 p-3">
              <RuleSummary rule={formData} questionLabel={questionLabel} />
            </div>
          </div>

          {/* Validation Messages */}
          {errors.length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="size-4" />
              <AlertDescription>
                <ul className="list-inside list-disc space-y-1">
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {warnings.length > 0 && (
            <Alert>
              <AlertCircle className="size-4" />
              <AlertDescription>
                <ul className="list-inside list-disc space-y-1">
                  {warnings.map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}
        </DialogBody>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} className="h-12 rounded-xl px-6 font-semibold">
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={errors.length > 0} className="h-12 rounded-xl px-6 font-semibold">
            <Save className="mr-2 size-4" />
            {rule ? 'Update Rule' : 'Create Rule'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
