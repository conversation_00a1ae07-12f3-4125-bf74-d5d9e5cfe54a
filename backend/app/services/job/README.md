# Centralized Job Tracking System

This package provides a centralized job tracking system for asynchronous workflows in the application. It enables tracking of job status, progress, and results across various types of jobs, including form processing, AI analysis, data enrichment, and more.

## Features

- **Unified Job Tracking**: Track any asynchronous job across the application
- **Status Updates**: Monitor job status (queued, in progress, completed, failed)
- **Progress Tracking**: Track job progress from 0% to 100%
- **Result Capture**: Store job outputs, including AI-generated content
- **Error Handling**: Capture and store error information for failed jobs
- **Entity Association**: Link jobs to entities like form submissions, theses, etc.
- **Job Chaining**: Create chains of dependent jobs
- **Job Grouping**: Group related jobs together
- **Metadata Storage**: Store additional information about jobs
- **Configuration**: Configure job parameters and settings
- **Priority Support**: Set job priorities
- **Scheduling**: Schedule jobs to run at specific times
- **Generic Job Handler**: Process any type of job with a generic handler

## Usage Examples

### Creating a Simple Job

```python
# Create a job
job = await job_service.create_job(
    entity_type="form_submission",
    entity_id=submission_id,
    job_type="form_processing",
    payload={
        "submission_id": submission_id,
        "form_id": form_id,
        "org_id": org_id
    },
    metadata={
        "form_name": form_name
    }
)
```

### Creating a Job Chain

```python
# Create a chain of dependent jobs
jobs = await create_job_chain(
    entity_type="form_submission",
    entity_id=submission_id,
    job_configs=[
        # First job
        {
            "job_type": "form_processing",
            "queue_job_type": "process_form_submission",
            "payload": {
                "submission_id": submission_id,
                "form_id": form_id,
                "org_id": org_id
            }
        },
        # Second job (depends on first job)
        {
            "job_type": "ai_summary",
            "queue_job_type": "generic_job",
            "payload": {
                "processor": {
                    "module": "app.services.ai.processors",
                    "function": "generate_submission_summary",
                    "kwargs": {
                        "form_id": form_id,
                        "submission_id": submission_id
                    }
                }
            }
        }
    ]
)
```

### Creating a Job Group

```python
# Create a group of related jobs
jobs = await create_job_group(
    entity_type="thesis",
    entity_id=thesis_id,
    job_configs=[
        {
            "job_type": "scoring",
            "payload": {...}
        },
        {
            "job_type": "ai_analysis",
            "payload": {...}
        }
    ]
)
```

### Executing a Function with Job Tracking

```python
# Execute a function with job tracking
result = await with_job_tracking(
    entity_type="form_submission",
    entity_id=submission_id,
    job_type="data_enrichment",
    func=enrich_submission_data,
    submission_id=submission_id,
    metadata={"source": "external_api"}
)
```

### Creating a Custom Job Processor

```python
# Create a custom job processor
@create_job_processor
async def process_data_export(
    export_id: str,
    format: str,
    tracked_job_id: Optional[str] = None,
    job_service: Optional[JobServiceInterface] = None
) -> Dict[str, Any]:
    # Update job status
    if job_service and tracked_job_id:
        await job_service.update_job_status(
            job_id=tracked_job_id,
            status=JobStatus.IN_PROGRESS,
            progress=0.1
        )
    
    # Process the export
    result = await generate_export(export_id, format)
    
    # Update job status
    if job_service and tracked_job_id:
        await job_service.update_job_status(
            job_id=tracked_job_id,
            status=JobStatus.COMPLETED,
            progress=1.0,
            output={"url": result["url"]}
        )
    
    return result
```

## API Endpoints

- `GET /api/v1/jobs/{job_id}` - Get a job by ID
- `GET /api/v1/jobs/entity/{entity_type}/{entity_id}` - Get all jobs for an entity
- `GET /api/v1/forms/submissions/{submission_id}/jobs` - Get all jobs for a form submission

## Database Schema

```json
{
  "_id": "job_abc123",
  "job_id": "queue_job_xyz",
  "entity_type": "form_submission",
  "entity_id": "submission_123",
  "job_type": "ai_summary",
  "status": "completed",
  "progress": 1.0,
  "error": null,
  "output": {
    "summary": "This is an AI-generated summary...",
    "key_points": ["Point 1", "Point 2"],
    "sentiment": "positive"
  },
  "metadata": {
    "form_name": "Startup Questionnaire",
    "model": "gpt-4o",
    "tokens_used": 1234
  },
  "config": {
    "model": "gpt-4o",
    "max_tokens": 1000,
    "temperature": 0.7
  },
  "priority": 0,
  "parent_job_id": null,
  "depends_on": ["job_def456"],
  "created_at": 1623456789,
  "updated_at": 1623457890,
  "completed_at": 1623457890,
  "started_at": 1623456890,
  "scheduled_at": null
}
```

## Integration with Queue System

The job tracking system integrates with the existing queue system, using the queue for job execution while tracking job status, progress, and results in the database.

When a job is created, it is automatically enqueued in the queue system, and the job ID is stored in the job record. The job handler then updates the job status, progress, and results as the job is executed.

## Future Enhancements

- WebSocket/SSE support for real-time job status updates
- Admin dashboard for job monitoring and management
- Job retry and recovery mechanisms
- Job expiration and cleanup
- Job dependencies and workflows
- Job streaming for large outputs
