"""
Research response schemas for external signals.
"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class CompetitorInfo(BaseModel):
    """Individual competitor information."""
    
    name: str = Field(..., description="Competitor company name")
    website: Optional[str] = Field(None, description="Competitor website URL")
    description: str = Field(..., description="One-line description of the competitor")
    comparison: str = Field(..., description="Comparison sentence with the target company")
    sources: List[str] = Field(default_factory=list, description="Source URLs for this competitor")


class CompetitorsResponse(BaseModel):
    """Response schema for competitors research."""
    
    competitors: List[CompetitorInfo] = Field(..., description="List of top competitors")


class MarketTrend(BaseModel):
    """Individual market trend information."""
    
    summary: str = Field(..., description="Market trend summary")
    sources: List[str] = Field(default_factory=list, description="Source URLs for this trend")


class MarketResponse(BaseModel):
    """Response schema for market research."""
    
    market_trends: List[MarketTrend] = Field(..., description="List of market trends and insights")


class NewsSignal(BaseModel):
    """Individual news signal information."""
    
    headline: str = Field(..., description="News headline")
    summary: str = Field(..., description="One-sentence summary of the news")
    url: str = Field(..., description="Source URL for the news")
    date: Optional[str] = Field(None, description="Publication date if available")


class NewsResponse(BaseModel):
    """Response schema for news research."""
    
    news_signals: List[NewsSignal] = Field(..., description="List of recent news signals")


class ExecutiveSummaryResponse(BaseModel):
    """Response schema for executive summary."""
    
    executive_summary: str = Field(..., description="Sequoia-grade executive summary")
    sources: List[str] = Field(default_factory=list, description="Source URLs referenced in summary")


class ExternalSignalsResponse(BaseModel):
    """Complete external signals response."""
    
    deal_id: str = Field(..., description="Deal ID")
    competitors: Optional[CompetitorsResponse] = Field(None, description="Competitors analysis")
    market: Optional[MarketResponse] = Field(None, description="Market analysis")
    news: Optional[NewsResponse] = Field(None, description="News signals")
    summary: Optional[ExecutiveSummaryResponse] = Field(None, description="Executive summary")
    generated_at: int = Field(..., description="Unix timestamp when research was generated")
    version: str = Field(default="v1.0", description="Research data version")


class ResearchJobPayload(BaseModel):
    """Payload for research queue jobs."""
    
    deal_id: str = Field(..., description="Deal ID to research")
    org_id: str = Field(..., description="Organization ID")
    context_block_url: str = Field(..., description="S3 URL of the context block")
    force_refresh: bool = Field(default=False, description="Force refresh existing research")
