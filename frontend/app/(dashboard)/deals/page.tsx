"use client"

import { useState, useEffect, useMemo } from 'react'
import { useSearchParams } from 'next/navigation'
import { Shell } from '@/components/shell'
import { DealsHeader } from '@/components/core/deals/deals-header'
import { DealsGrid } from '@/components/core/deals/deals-grid'
import { NewDealModal } from '@/components/core/deals/new-deal-modal'
import { ExcelUploadModal } from '@/components/core/deals/excel-upload-modal'
import { DealAPI } from '@/lib/api/deal-api'
import { Deal, DealStatus, DealDashboardFilters } from '@/lib/types/deal'
import { useAuth } from '@/lib/auth-context'
import { useToast } from '@/components/ui/use-toast'
import { PitchDeckParserStatus } from '@/components/core/deals/pitch-deck-parser-status'
import { useParseJobStore } from '@/lib/stores/parse-job-store'
import { TourBadge } from '@/components/core/tour/tour-provider'

export default function DealsPage() {
  const { isAuthenticated, loading, user } = useAuth()
  const searchParams = useSearchParams()
  const { toast } = useToast()

  // State management
  const [allDeals, setAllDeals] = useState<Deal[]>([])
  const [filteredDeals, setFilteredDeals] = useState<Deal[]>([])
  const [error, setError] = useState<string | null>(null)
  const [showNewDealModal, setShowNewDealModal] = useState(false)
  const [showExcelUploadModal, setShowExcelUploadModal] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)
  const [userPreferences, setUserPreferences] = useState<DealDashboardFilters | null>(null)

  const parseJobState = useParseJobStore(state => state.jobState)

  // Parse filters from URL with preferences fallback
  const currentFilters = useMemo(() => {
    // Start with user preferences as defaults, or fallback to hardcoded defaults
    const defaultFilters: DealDashboardFilters = userPreferences || {
      status: ['new', 'triage', 'reviewed'],
      assigned_to_me: false,
      sort_by: 'updated_at',
      sort_dir: 'desc'
    }

    const filters: DealDashboardFilters = { ...defaultFilters }

    // Parse URL parameters (these take priority over preferences)
    const status = searchParams?.get('status')
    if (status) {
      filters.status = status.split(',')
    }
    const assignedToMe = searchParams?.get('assigned_to_me')
    if (assignedToMe !== null) {
      filters.assigned_to_me = assignedToMe === 'true'
    }
    const createdAtStart = searchParams?.get('created_at_start')
    if (createdAtStart) {
      filters.created_at_start = createdAtStart
    }
    const createdAtEnd = searchParams?.get('created_at_end')
    if (createdAtEnd) {
      filters.created_at_end = createdAtEnd
    }
    const sortBy = searchParams?.get('sort_by')
    if (sortBy) {
      filters.sort_by = sortBy
    }
    const sortDir = searchParams?.get('sort_dir')
    if (sortDir) {
      filters.sort_dir = sortDir
    }
    const favouritesOnly = searchParams?.get('favourites_only')
    if (favouritesOnly !== null) {
      filters.favourites_only = favouritesOnly === 'true'
    }

    return filters
  }, [searchParams, userPreferences])

  // Fetch deals with current filters
  const fetchDeals = async (filters?: DealDashboardFilters) => {
    try {
      setError(null)

      const response = await DealAPI.listDeals(0, 1000, filters || currentFilters)
      setAllDeals(response.deals || [])
      setFilteredDeals(response.deals || [])
    } catch (err: any) {
      console.error('Error fetching deals:', err)
      setError('Failed to load deals. Please try again.')
      toast({
        title: "Error",
        description: "Failed to load deals. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Load user preferences first
  useEffect(() => {
    const loadPreferences = async () => {
      if (!isAuthenticated) return
      
      try {
        const preferences = await DealAPI.getDealPreferences()
        if (preferences.deal_dashboard_filters) {
          setUserPreferences(preferences.deal_dashboard_filters)
        } else {
          // No preferences found, use defaults
          setUserPreferences({
            status: ['new', 'triage', 'reviewed'],
            assigned_to_me: false,
            sort_by: 'updated_at',
            sort_dir: 'desc'
          })
        }
      } catch (error) {
        console.warn('Failed to load user preferences:', error)
        // Use defaults on error
        setUserPreferences({
          status: ['new', 'triage', 'reviewed'],
          assigned_to_me: false,
          sort_by: 'updated_at',
          sort_dir: 'desc'
        })
      }
    }
    
    loadPreferences()
  }, [isAuthenticated])

  // Initialize and fetch deals once preferences are loaded
  useEffect(() => {
    if (isAuthenticated && !isInitialized && userPreferences !== null) {
      fetchDeals(currentFilters)
      setIsInitialized(true)
    }
  }, [isAuthenticated, isInitialized, userPreferences, currentFilters])

  // Refetch when URL params change (but only after initial load)
  useEffect(() => {
    if (isAuthenticated && isInitialized && userPreferences !== null) {
      fetchDeals(currentFilters)
    }
  }, [currentFilters])

  // Calculate deal counts for filters
  const dealCounts = useMemo(() => ({
    all: allDeals.length,
    new: allDeals.filter(d => d.status === DealStatus.NEW).length,
    triage: allDeals.filter(d => d.status === DealStatus.TRIAGE).length,
    reviewed: allDeals.filter(d => d.status === DealStatus.REVIEWED).length,
    approved: allDeals.filter(d => d.status === DealStatus.APPROVED).length,
    excluded: allDeals.filter(d => d.status === DealStatus.EXCLUDED).length,
    closed: allDeals.filter(d => d.status === DealStatus.CLOSED).length,
  }), [allDeals])

  const handleNewDeal = () => {
    setShowNewDealModal(true)
  }

  const handleBulkImport = () => {
    setShowExcelUploadModal(true)
  }

  const handleDeleteDeal = async (dealId: string): Promise<boolean> => {
    try {
      await DealAPI.deleteDeal(dealId)
      
      // Remove from local state
      setAllDeals(prev => prev.filter(d => d.id !== dealId && d._id !== dealId))
      setFilteredDeals(prev => prev.filter(d => d.id !== dealId && d._id !== dealId))
      
      toast({
        title: "Deal deleted",
        description: "Deal has been deleted successfully.",
      })
      
      return true
    } catch (error: any) {
      console.error('Error deleting deal:', error)
      toast({
        title: "Error",
        description: "Failed to delete deal. Please try again.",
        variant: "destructive",
      })
      return false
    }
  }

  const handleSearchChange = (search: string) => {
    if (!search.trim()) {
      setFilteredDeals(allDeals)
      return
    }

    const searchLower = search.toLowerCase()
    const filtered = allDeals.filter(deal => 
      deal.company_name?.toLowerCase().includes(searchLower) ||
      deal.company_website?.toLowerCase().includes(searchLower) ||
      deal.sector?.toString().toLowerCase().includes(searchLower) ||
      deal.stage?.toLowerCase().includes(searchLower) ||
      deal.notes?.toLowerCase().includes(searchLower)
    )
    setFilteredDeals(filtered)
  }

  const handleFilterChange = (filters: DealDashboardFilters) => {
    // The filters are already applied via URL params, so we just need to refetch
    fetchDeals(filters)
  }

  return (
    <Shell>
      <div className="space-y-8">
        {/* Show global pitch deck parsing status if a job is in progress */}
        {parseJobState.status === 'processing' && (
          <div className="mb-4">
            <PitchDeckParserStatus onRefresh={() => fetchDeals(currentFilters)} />
          </div>
        )}
        {/* Show loading while checking authentication */}
        {loading ? (
          <div className="flex items-center justify-center p-8">
            <div className="size-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
            <span className="ml-2">Checking authentication...</span>
          </div>
        ) : !isAuthenticated ? (
          <div className="flex items-center justify-center p-8">
            <div className="text-center">
              <p className="text-muted-foreground">Redirecting to login...</p>
            </div>
          </div>
        ) : (
          <>
            <DealsHeader
              onSearchChange={handleSearchChange}
              onFilterChange={handleFilterChange}
              totalDeals={filteredDeals.length}
              dealCounts={dealCounts}
              loading={loading}
              onBulkImport={handleBulkImport}
            />

            <DealsGrid
              deals={filteredDeals}
              loading={loading}
              error={error}
              onNewDeal={handleNewDeal}
              onDeleteDeal={handleDeleteDeal}
            />

            {/* New Deal Modal */}
            <NewDealModal
              open={showNewDealModal}
              onOpenChange={setShowNewDealModal}
              onRefresh={() => fetchDeals(currentFilters)}
            />

            {/* Excel Upload Modal */}
            <ExcelUploadModal
              open={showExcelUploadModal}
              onOpenChange={setShowExcelUploadModal}
              onUploadComplete={() => fetchDeals(currentFilters)}
            />
          </>
        )}
      </div>
    </Shell>
  )
} 