# News Intelligence API - Curl Testing Commands

## Quick Test Commands (Copy & Paste Ready)

### Health Check
```bash
curl -s http://localhost:8002/health | jq .
```

### Check Available Sources
```bash
curl -s http://localhost:8002/api/ai/news/sources | jq .
```

### Basic News Search with AI Analysis
```bash
curl -s -X POST http://localhost:8002/api/ai/news/search \
  -H "Content-Type: application/json" \
  -d '{"query": "Tesla AI", "include_analysis": true}' | jq .
```

### Fast Search (No AI Analysis)
```bash
curl -s -X POST http://localhost:8002/api/ai/news/search \
  -H "Content-Type: application/json" \
  -d '{"query": "startup", "include_analysis": false}' | jq .
```

### Advanced Search with All Parameters
```bash
curl -s -X POST http://localhost:8002/api/ai/news/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "artificial intelligence funding",
    "max_sources": 3,
    "time_range": "7d",
    "include_analysis": true,
    "priority_sources": ["newsapi", "google_news_rss"]
  }' | jq .
```

### News Agent Conversation
```bash
curl -s -X POST http://localhost:8002/api/ai/news/agent \
  -H "Content-Type: application/json" \
  -d '{"message": "Find recent AI developments"}' | jq .
```

### Agent with Session (for follow-up conversations)
```bash
curl -s -X POST http://localhost:8002/api/ai/news/agent \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What about robotics news?",
    "session_id": "my_session_123"
  }' | jq .
```

## Response Analysis Commands

### View Only Executive Summary
```bash
curl -s -X POST http://localhost:8002/api/ai/news/search \
  -H "Content-Type: application/json" \
  -d '{"query": "blockchain", "include_analysis": true}' | jq -r '.executive_summary'
```

### View Performance Metrics
```bash
curl -s -X POST http://localhost:8002/api/ai/news/search \
  -H "Content-Type: application/json" \
  -d '{"query": "fintech"}' | jq '{search_time, total_articles, sources_used}'
```

### View Section Summaries
```bash
curl -s -X POST http://localhost:8002/api/ai/news/search \
  -H "Content-Type: application/json" \
  -d '{"query": "venture capital", "include_analysis": true}' | jq '.sections[] | {title, summary, article_count: (.articles | length)}'
```

### View Raw Articles (if any found)
```bash
curl -s -X POST http://localhost:8002/api/ai/news/search \
  -H "Content-Type: application/json" \
  -d '{"query": "tech IPO"}' | jq '.sections[].articles[] | {title, source, url}'
```

## Error Testing

### Test Invalid Query (Empty String)
```bash
curl -s -X POST http://localhost:8002/api/ai/news/search \
  -H "Content-Type: application/json" \
  -d '{"query": ""}' | jq .
```

### Test Invalid Max Sources
```bash
curl -s -X POST http://localhost:8002/api/ai/news/search \
  -H "Content-Type: application/json" \
  -d '{"query": "test", "max_sources": 10}' | jq .
```

## Performance Testing

### Measure Search Time
```bash
time curl -s -X POST http://localhost:8002/api/ai/news/search \
  -H "Content-Type: application/json" \
  -d '{"query": "artificial intelligence"}' > /dev/null
```

### Compare Analysis vs No Analysis Speed
```bash
echo "With Analysis:"
curl -s -X POST http://localhost:8002/api/ai/news/search \
  -H "Content-Type: application/json" \
  -d '{"query": "AI", "include_analysis": true}' | jq '.search_time'

echo "Without Analysis:"
curl -s -X POST http://localhost:8002/api/ai/news/search \
  -H "Content-Type: application/json" \
  -d '{"query": "AI", "include_analysis": false}' | jq '.search_time'
```

## Testing with Real API Keys

If you have configured news API keys in your `.env` file, these queries are more likely to return actual articles:

### Popular Tech Topics
```bash
curl -s -X POST http://localhost:8002/api/ai/news/search \
  -H "Content-Type: application/json" \
  -d '{"query": "OpenAI ChatGPT", "include_analysis": true}' | jq .
```

### Investment News
```bash
curl -s -X POST http://localhost:8002/api/ai/news/search \
  -H "Content-Type: application/json" \
  -d '{"query": "Series A funding", "time_range": "1d", "include_analysis": true}' | jq .
```

### Company-Specific News
```bash
curl -s -X POST http://localhost:8002/api/ai/news/search \
  -H "Content-Type: application/json" \
  -d '{"query": "Tesla earnings", "time_range": "7d", "include_analysis": true}' | jq .
```

## Useful Tips

1. **Add `| jq .` for pretty JSON formatting**
2. **Add `| jq -r '.field'` to extract specific fields**
3. **Use `time` command to measure request duration**
4. **Save responses with `> response.json` for analysis**
5. **Use `watch` for continuous testing: `watch -n 5 'curl -s ... | jq .'`**

## API Response Structure

Successful news search response:
```json
{
  "success": true,
  "query": "search term",
  "total_articles": 0,
  "sources_used": ["newsapi", "google_news_rss"],
  "sections": [
    {
      "title": "Category Name",
      "summary": "AI-generated summary",
      "articles": [...]
    }
  ],
  "executive_summary": "AI-generated executive summary",
  "search_time": 0.123,
  "error": null
}
```

Agent response:
```json
{
  "success": true,
  "response": "AI agent response",
  "session_id": "session_id",
  "execution_time": 1.23,
  "tools_used": ["news_intelligence"],
  "error": null
}
``` 