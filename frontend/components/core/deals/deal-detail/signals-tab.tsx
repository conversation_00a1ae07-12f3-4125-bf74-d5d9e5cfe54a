"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { 
  ExternalLink, 
  TrendingUp, 
  DollarSign, 
  Package, 
  Users, 
  UserCheck,
  Radio,
  Calendar
} from "lucide-react"


import { cn } from "@/lib/utils"
import { DealDetailData, ExternalSignal } from "@/lib/types/deal-detail"
import { EmptyPlaceholder } from "@/components/empty-placeholder"

interface SignalsTabProps {
  deal: DealDetailData
}

const getSignalIcon = (type: ExternalSignal['type']) => {
  switch (type) {
    case 'funding':
      return DollarSign
    case 'product':
      return Package
    case 'hiring':
      return Users
    case 'partnership':
      return UserCheck
    case 'news':
    default:
      return Radio
  }
}

const getSignalColor = (type: ExternalSignal['type']) => {
  switch (type) {
    case 'funding':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'product':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'hiring':
      return 'bg-purple-100 text-purple-800 border-purple-200'
    case 'partnership':
      return 'bg-orange-100 text-orange-800 border-orange-200'
    case 'news':
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const getSentimentColor = (sentiment?: ExternalSignal['sentiment']) => {
  switch (sentiment) {
    case 'positive':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'negative':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'neutral':
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))
  
  if (diffInDays === 0) {
    return 'Today'
  } else if (diffInDays === 1) {
    return 'Yesterday'
  } else if (diffInDays < 7) {
    return `${diffInDays}d ago`
  } else {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    })
  }
}

export function SignalsTab({ deal }: SignalsTabProps) {
  const signals = deal.external_signals || []

  if (signals.length === 0) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="radio" />
        <EmptyPlaceholder.Title>No external signals found yet</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          We're tracking news, funding, and more in real time. External signals will appear here as they're discovered.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    )
  }

  const handleViewSource = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  // Group signals by type for summary
  const signalsByType = signals.reduce((acc, signal) => {
    acc[signal.type] = (acc[signal.type] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Object.entries(signalsByType).map(([type, count]) => {
          const Icon = getSignalIcon(type as ExternalSignal['type'])
          
          return (
            <motion.div
              key={type}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className={cn(
                      "flex size-10 items-center justify-center rounded-lg border",
                      getSignalColor(type as ExternalSignal['type'])
                    )}>
                      <Icon className="size-5" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{count}</div>
                      <div className="text-sm capitalize text-muted-foreground">
                        {type.replace('_', ' ')}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )
        })}
      </div>

      {/* Signals List */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Recent Signals</h3>
        
        <div className="space-y-3">
          {signals
            .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
            .map((signal, index) => {
              const Icon = getSignalIcon(signal.type)
              
              return (
                <motion.div
                  key={signal.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <Card className="transition-shadow hover:shadow-md">
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        {/* Header */}
                        <div className="flex items-start gap-4">
                          <div className={cn(
                            "flex size-10 shrink-0 items-center justify-center rounded-lg border",
                            getSignalColor(signal.type)
                          )}>
                            <Icon className="size-5" />
                          </div>
                          
                          <div className="min-w-0 flex-1">
                            <h4 className="mb-2 text-base font-medium leading-tight">
                              {signal.headline}
                            </h4>
                            
                            <p className="text-sm leading-relaxed text-muted-foreground">
                              {signal.summary}
                            </p>
                          </div>
                        </div>

                        {/* Metadata */}
                        <div className="flex items-center justify-between gap-4">
                          <div className="flex items-center gap-3">
                            <Badge 
                              variant="outline" 
                              className={cn("capitalize", getSignalColor(signal.type))}
                            >
                              {signal.type.replace('_', ' ')}
                            </Badge>
                            
                            {signal.sentiment && (
                              <Badge 
                                variant="outline" 
                                className={cn("capitalize", getSentimentColor(signal.sentiment))}
                              >
                                {signal.sentiment}
                              </Badge>
                            )}
                            
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                              <Calendar className="size-4" />
                              {formatDate(signal.date)}
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-muted-foreground">
                              {signal.source}
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewSource(signal.source_url)}
                              className="gap-1"
                            >
                              <ExternalLink className="size-4" />
                              View Source
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
        </div>
      </div>

      {/* Signal Insights */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.4 }}
      >
        <Card>
          <CardContent className="p-6">
            <h3 className="mb-4 font-semibold">Signal Insights</h3>
            
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <TrendingUp className="mt-0.5 size-5 text-green-600" />
                <div>
                  <p className="text-sm font-medium">Positive momentum detected</p>
                  <p className="text-xs text-muted-foreground">
                    Recent signals show positive market sentiment and growth indicators
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <Radio className="mt-0.5 size-5 text-blue-600" />
                <div>
                  <p className="text-sm font-medium">Active in media</p>
                  <p className="text-xs text-muted-foreground">
                    Company is generating consistent media coverage and industry attention
                  </p>
                </div>
              </div>
              
              {signalsByType.funding > 0 && (
                <div className="flex items-start gap-3">
                  <DollarSign className="mt-0.5 size-5 text-green-600" />
                  <div>
                    <p className="text-sm font-medium">Funding activity</p>
                    <p className="text-xs text-muted-foreground">
                      Recent funding signals indicate investor interest and capital availability
                    </p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
