"""
Deal Note Service Interface

Defines the interface for deal note operations including CRUD operations,
team mentions, and timeline integration.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union

from bson import ObjectId

from app.models.deal_note import DealNote


class DealNoteServiceInterface(ABC):
    """Interface for deal note service operations."""

    @abstractmethod
    async def initialize(self):
        """Initialize the service."""
        pass

    @abstractmethod
    async def cleanup(self):
        """Cleanup the service."""

    @abstractmethod
    async def create_note(
        self,
        deal_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        created_by: Union[str, ObjectId],
        structured_content: List[Dict[str, Any]],
        tagged_user_ids: Optional[List[Union[str, ObjectId]]] = None,
        pinned: bool = False,
    ) -> Optional[DealNote]:
        """
        Create a new deal note.

        Args:
            deal_id: ID of the deal
            org_id: ID of the organization
            created_by: ID of the user creating the note
            structured_content: Structured rich text content (Slate.js JSON)
            tagged_user_ids: List of user IDs to tag
            pinned: Whether to pin the note

        Returns:
            Created deal note or None if failed
        """
        pass

    @abstractmethod
    async def get_note(
        self, note_id: Union[str, ObjectId], org_id: Union[str, ObjectId]
    ) -> Optional[DealNote]:
        """
        Get a deal note by ID.

        Args:
            note_id: ID of the note
            org_id: ID of the organization for access control

        Returns:
            Deal note or None if not found
        """
        pass

    @abstractmethod
    async def list_notes(
        self,
        deal_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        skip: int = 0,
        limit: int = 100,
        include_deleted: bool = False,
    ) -> tuple[List[DealNote], int]:
        """
        List notes for a deal.

        Args:
            deal_id: ID of the deal
            org_id: ID of the organization
            skip: Number of notes to skip
            limit: Maximum number of notes to return
            include_deleted: Whether to include soft-deleted notes

        Returns:
            Tuple of (notes list, total count)
        """
        pass

    @abstractmethod
    async def update_note(
        self,
        note_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        updated_by: Union[str, ObjectId],
        structured_content: Optional[List[Dict[str, Any]]] = None,
        tagged_user_ids: Optional[List[Union[str, ObjectId]]] = None,
        pinned: Optional[bool] = None,
    ) -> Optional[DealNote]:
        """
        Update a deal note.

        Args:
            note_id: ID of the note
            org_id: ID of the organization
            updated_by: ID of the user updating the note
            structured_content: New structured content (optional)
            tagged_user_ids: New tagged user IDs (optional)
            pinned: New pinned status (optional)

        Returns:
            Updated deal note or None if failed
        """
        pass

    @abstractmethod
    async def delete_note(
        self,
        note_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        deleted_by: Union[str, ObjectId],
    ) -> bool:
        """
        Soft delete a deal note.

        Args:
            note_id: ID of the note
            org_id: ID of the organization
            deleted_by: ID of the user deleting the note

        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    async def restore_note(
        self,
        note_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        restored_by: Union[str, ObjectId],
    ) -> Optional[DealNote]:
        """
        Restore a soft-deleted deal note.

        Args:
            note_id: ID of the note
            org_id: ID of the organization
            restored_by: ID of the user restoring the note

        Returns:
            Restored deal note or None if failed
        """
        pass

    @abstractmethod
    async def toggle_pinned(
        self,
        note_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        user_id: Union[str, ObjectId],
    ) -> Optional[DealNote]:
        """
        Toggle the pinned status of a note.

        Args:
            note_id: ID of the note
            org_id: ID of the organization
            user_id: ID of the user toggling the pin

        Returns:
            Updated deal note or None if failed
        """
        pass

    @abstractmethod
    async def validate_tagged_users(
        self,
        tagged_user_ids: List[Union[str, ObjectId]],
        org_id: Union[str, ObjectId],
    ) -> List[Union[str, ObjectId]]:
        """
        Validate that tagged users exist and belong to the organization.

        Args:
            tagged_user_ids: List of user IDs to validate
            org_id: ID of the organization

        Returns:
            List of valid user IDs
        """
        pass

    @abstractmethod
    async def get_notes_by_user(
        self,
        user_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        skip: int = 0,
        limit: int = 100,
    ) -> tuple[List[DealNote], int]:
        """
        Get notes where a user is tagged.

        Args:
            user_id: ID of the user
            org_id: ID of the organization
            skip: Number of notes to skip
            limit: Maximum number of notes to return

        Returns:
            Tuple of (notes list, total count)
        """
        pass

    @abstractmethod
    async def get_note_count(
        self, deal_id: Union[str, ObjectId], org_id: Union[str, ObjectId]
    ) -> int:
        """
        Get the total number of notes for a deal.

        Args:
            deal_id: ID of the deal
            org_id: ID of the organization

        Returns:
            Number of notes
        """
        pass
