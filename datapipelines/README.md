# TractionX Data Pipeline Queue System

A comprehensive, priority-aware queue system designed for the TractionX Data Pipeline Service with support for multiple backends, round-robin job processing, and extensive monitoring capabilities.

## 🏗️ Architecture

The queue system follows a modular, pluggable architecture:

```
┌─────────────────────────────────────────────────────────────┐
│                    Queue System Architecture                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐    ┌──────────────┐    ┌─────────────────┐ │
│  │ Dispatcher  │ ───▶│ Queue Service│ ───▶│ Queue Backend   │ │
│  │             │    │              │    │ (Redis/RQ/etc.) │ │
│  └─────────────┘    └──────────────┘    └─────────────────┘ │
│         │                   │                              │
│         ▼                   ▼                              │
│  ┌─────────────┐    ┌──────────────┐                       │
│  │ API         │    │ Round-Robin  │                       │
│  │ Endpoints   │    │ Worker       │                       │
│  └─────────────┘    └──────────────┘                       │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Features

### Core Features
- **Priority-based job processing** (high, normal, low)
- **Round-robin queue scheduling** for fair resource allocation
- **Pluggable backend architecture** (Redis, RQ, Celery, Kafka)
- **Comprehensive error handling** with automatic retry logic
- **Real-time job monitoring** and status tracking
- **Health checks** and queue statistics
- **Graceful shutdown** handling

### Development Features
- **Watchdog support** for automatic reload during development
- **Comprehensive logging** with structured output
- **Test utilities** for queue system validation
- **Hot-reload** capability for job handlers

### Production Features
- **High availability** with Redis clustering support
- **Horizontal scaling** with multiple worker instances
- **Monitoring integration** (Prometheus metrics)
- **Rate limiting** and backpressure handling
- **Dead letter queues** for failed jobs

## 📁 File Structure

```
app/queue/
├── __init__.py              # Package exports
├── README.md               # This documentation
├── interfaces.py           # Core interfaces and protocols
├── service.py             # Main queue service implementation
├── dispatcher.py          # Job dispatching utilities
├── factory.py             # Service factory functions
├── worker.py              # Round-robin worker implementation
├── backends/
│   ├── __init__.py        # Backend package
│   └── redis_backend.py   # Redis backend implementation
```

## 🔧 Usage

### Basic Job Dispatching

```python
from app.queue.dispatcher import dispatch_job

# Dispatch a high-priority job
job_id = await dispatch_job(
    job_func="enrich_founder_data",
    job_args={
        "company_id": "company-123",
        "founder_name": "John Doe",
        "founder_linkedin": "https://linkedin.com/in/johndoe"
    },
    priority="high"
)
```

### Queue Service Usage

```python
from app.queue.factory import create_queue_service
from app.queue.interfaces import JobPriority

# Create queue service
queue_service = await create_queue_service()
await queue_service.initialize()

# Enqueue a job
job_id = await queue_service.enqueue_job(
    job_func="enrich_company_data",
    job_args={"company_id": "company-123"},
    priority=JobPriority.HIGH
)

# Check job status
status = await queue_service.get_job_status(job_id)
print(f"Job {job_id}: {status.status}")

# Get queue statistics
stats = await queue_service.get_queue_stats()
```

### Running the Worker

#### Development Mode (with auto-reload)
```bash
# Using the watch script
./watch_worker.sh

# Or with custom settings
WORKER_ID=dev-worker-1 CONCURRENCY=4 ./watch_worker.sh
```

#### Production Mode
```bash
# Direct execution
poetry run python app/worker_runner.py --worker-id prod-worker-1 --concurrency 8

# With Docker
docker run -d --name tractionx-worker \
  -e REDIS_HOST=redis.example.com \
  -e REDIS_PASSWORD=secret \
  tractionx/datapipelines:latest \
  python app/worker_runner.py --concurrency 8
```

## 🎯 Job Priorities and Processing

The system supports three priority levels:

1. **High Priority** (`high`): Critical jobs processed first
2. **Normal Priority** (`normal`): Standard business logic jobs
3. **Low Priority** (`low`): Background/maintenance jobs

### Round-Robin Processing

The worker uses round-robin scheduling to ensure fair processing:

1. Check high-priority queue
2. Check normal-priority queue  
3. Check low-priority queue
4. Repeat cycle

This prevents starvation of lower-priority jobs while maintaining responsiveness for high-priority tasks.

## 🔍 Monitoring and Health Checks

### Health Check Endpoint
```python
from app.queue.dispatcher import health_check

health = await health_check()
print(f"Status: {health['status']}")
print(f"Queues: {health['queues']}")
print(f"Total pending: {health['total_pending']}")
```

### Queue Statistics
```python
from app.queue.dispatcher import get_queue_stats

# Get all queue stats
stats = await get_queue_stats()

# Get specific queue stats
high_stats = await get_queue_stats("high")
print(f"High priority queue: {high_stats.pending_count} pending")
```

### Worker Statistics
```python
# Worker provides runtime statistics
worker_stats = worker.get_stats()
print(f"Jobs processed: {worker_stats['jobs_processed']}")
print(f"Uptime: {worker_stats['uptime']}s")
```

## ⚙️ Configuration

### Environment Variables

```bash
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=secret
REDIS_DB=0

# Worker Configuration  
WORKER_CONCURRENCY=4
WORKER_POLL_INTERVAL=1.0
WORKER_TIMEOUT=3600

# Development
WATCHDOG_ENABLED=true
RELOAD_DIRS=app
```

### Queue Service Configuration

```python
from app.queue.factory import create_queue_service

# Custom Redis configuration
queue_service = await create_queue_service(
    backend_type="redis",
    redis_url="redis://user:pass@host:port/db",
    key_prefix="prod:queue"
)
```

## 🔄 Job Lifecycle

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   PENDING   │───▶│   RUNNING   │───▶│  COMPLETED  │
└─────────────┘    └─────────────┘    └─────────────┘
       │                  │                   
       │                  ▼                   
       │            ┌─────────────┐           
       │            │   FAILED    │           
       │            └─────────────┘           
       │                  │                   
       │                  ▼                   
       │            ┌─────────────┐           
       └───────────▶│  RETRYING   │           
                    └─────────────┘           
                          │                   
                          ▼                   
                    ┌─────────────┐           
                    │ CANCELLED   │           
                    └─────────────┘           
```

## 🛠️ Extending the System

### Adding New Backends

1. Implement the `QueueBackend` protocol:

```python
from app.queue.interfaces import QueueBackend

class MyCustomBackend:
    async def enqueue(self, queue_name: str, job_func: str, job_args: dict, **kwargs) -> str:
        # Implementation
        pass
    
    async def dequeue(self, queue_names: list, timeout: int = 0) -> dict:
        # Implementation
        pass
    
    # ... implement other required methods
```

2. Register in the factory:

```python
# In factory.py
async def create_custom_backend(**kwargs) -> MyCustomBackend:
    return MyCustomBackend(**kwargs)
```

### Adding New Job Handlers

```python
# In app/tasks/my_tasks.py
async def my_custom_task(job_args: dict) -> dict:
    # Process the job
    result = {"status": "success", "data": "processed"}
    return result

# Register in app/tasks/__init__.py
def get_job_handlers():
    return {
        "my_custom_task": my_custom_task,
        # ... other handlers
    }
```

## 🧪 Testing

### Run Queue System Tests
```bash
# Test basic functionality
python test_new_queue.py

# Test with specific configuration
REDIS_HOST=test-redis python test_new_queue.py
```

### Integration Tests
```bash
# Run full test suite
poetry run pytest tests/queue/

# Test specific components
poetry run pytest tests/queue/test_redis_backend.py
```

## 🚨 Troubleshooting

### Common Issues

1. **Redis Connection Errors**
   ```bash
   # Check Redis connectivity
   redis-cli -h $REDIS_HOST -p $REDIS_PORT ping
   ```

2. **Jobs Not Processing**
   ```bash
   # Check worker logs
   tail -f logs/worker.log
   
   # Check queue stats
   curl http://localhost:8001/api/v1/pipelines/stats
   ```

3. **High Memory Usage**
   ```bash
   # Monitor Redis memory
   redis-cli info memory
   
   # Purge completed jobs
   curl -X DELETE http://localhost:8001/api/v1/pipelines/queues/high/purge
   ```

### Performance Tuning

1. **Optimize Concurrency**
   - Start with `WORKER_CONCURRENCY=4`
   - Monitor CPU and memory usage
   - Scale based on job processing time

2. **Redis Optimization**
   - Use Redis clustering for high availability
   - Configure appropriate memory policies
   - Monitor connection pool usage

3. **Job Batching**
   - Group related jobs when possible
   - Use delayed jobs for rate limiting
   - Implement job deduplication

## 📊 Metrics and Monitoring

### Key Metrics

- **Queue Depth**: Number of pending jobs per priority
- **Processing Time**: Average job execution time
- **Throughput**: Jobs processed per second
- **Error Rate**: Failed jobs percentage
- **Worker Health**: Active workers and their status

### Prometheus Integration

```python
# Add to your monitoring setup
from prometheus_client import Counter, Histogram, Gauge

jobs_processed = Counter('queue_jobs_processed_total', 'Total processed jobs', ['queue', 'status'])
job_duration = Histogram('queue_job_duration_seconds', 'Job processing time', ['queue'])
queue_depth = Gauge('queue_depth', 'Number of pending jobs', ['queue'])
```

## 🔐 Security Considerations

1. **Redis Security**
   - Use authentication (AUTH command)
   - Enable TLS for production
   - Restrict network access

2. **Job Data**
   - Encrypt sensitive job arguments
   - Implement data retention policies
   - Audit job access logs

3. **Worker Security**
   - Run workers with minimal privileges
   - Validate job function names
   - Implement resource limits

## 📝 Migration Guide

### From Old Queue System

1. **Update Job Dispatching**
   ```python
   # Old way
   queue_client = QueueClient()
   job_id = await queue_client.enqueue_job(queue_name="high_priority", ...)
   
   # New way
   job_id = await dispatch_job(job_func="...", job_args={...}, priority="high")
   ```

2. **Update Worker Scripts**
   ```bash
   # Old worker
   python -m app.worker
   
   # New worker
   python app/worker_runner.py --concurrency 4
   ```

3. **Update Monitoring**
   - Use new health check endpoints
   - Update dashboard queries
   - Migrate alerting rules

## 🤝 Contributing

1. Follow the existing code style and patterns
2. Add comprehensive tests for new features
3. Update documentation for API changes
4. Ensure backward compatibility when possible

## 📚 Additional Resources

- [Redis Documentation](https://redis.io/documentation)
- [FastAPI Background Tasks](https://fastapi.tiangolo.com/tutorial/background-tasks/)
- [Python asyncio Guide](https://docs.python.org/3/library/asyncio.html)
- [Prometheus Monitoring](https://prometheus.io/docs/) 