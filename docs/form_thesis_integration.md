# Form and Thesis Integration Guide

This comprehensive guide explains how to integrate the Form and Thesis systems with your frontend application. It covers all the necessary API endpoints, data structures, and workflows with practical examples.

## Overview

The TractionX platform provides two core systems that work together:

1. **Form System**: A flexible form builder and engine that supports:
   - Multi-section forms with various question types
   - Conditional visibility logic
   - Repeatable sections
   - Sequential or full-form rendering modes

2. **Thesis Management System**: An investment thesis framework that:
   - Defines scoring rules for form responses
   - Creates match criteria for filtering startups
   - Calculates compatibility scores between startups and investment theses

## Authentication

All API requests require authentication using JWT tokens and organization context:

```bash
# Headers required for all API calls
-H "Authorization: Bearer YOUR_JWT_TOKEN" 
-H "X-ORG-ID: YOUR_ORGANIZATION_ID"
```

## Form System

### Creating a Form

```bash
# Create a new form
curl -X POST "http://localhost:8000/api/v1/forms" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Startup Qualification Form",
    "description": "Form to collect startup information and qualify for investment",
    "is_active": true
  }'
```

Response:
```json
{
  "_id": "6123456789abcdef01234567",
  "name": "Startup Qualification Form",
  "description": "Form to collect startup information and qualify for investment",
  "version": 1,
  "is_active": true,
  "sections": [],
  "default_section_ids": [],
  "created_at": 1678901234,
  "updated_at": 1678901234
}
```

### Adding Sections to a Form

```bash
# Create a section in a form
curl -X POST "http://localhost:8000/api/v1/forms/6123456789abcdef01234567/sections" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Company Information",
    "description": "Basic information about your company",
    "order": 0,
    "repeatable": false
  }'
```

Response:
```json
{
  "_id": "6223456789abcdef01234567",
  "form_id": "6123456789abcdef01234567",
  "title": "Company Information",
  "description": "Basic information about your company",
  "order": 0,
  "questions": [],
  "repeatable": false,
  "created_at": 1678901235,
  "updated_at": 1678901235
}
```

### Adding Questions to a Section

```bash
# Add a question to a section
curl -X POST "http://localhost:8000/api/v1/forms/sections/6223456789abcdef01234567/questions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "short_text",
    "label": "Company Name",
    "help_text": "Legal name of your company",
    "required": true,
    "order": 0
  }'
```

Response:
```json
{
  "_id": "6323456789abcdef01234567",
  "section_id": "6223456789abcdef01234567",
  "type": "short_text",
  "label": "Company Name",
  "help_text": "Legal name of your company",
  "required": true,
  "order": 0,
  "created_at": 1678901236,
  "updated_at": 1678901236
}
```

### Creating a Repeatable Section

```bash
# First, create a section that will be repeated
curl -X POST "http://localhost:8000/api/v1/forms/6123456789abcdef01234567/sections" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Founder Information",
    "description": "Details about each founder",
    "order": 2,
    "repeatable": true
  }'
```

```bash
# Then, create a control question that determines how many times the section repeats
curl -X POST "http://localhost:8000/api/v1/forms/sections/6223456789abcdef01234567/questions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "number",
    "label": "Number of Founders",
    "help_text": "How many founders does your company have?",
    "required": true,
    "order": 5,
    "repeat_section_id": "6323456789abcdef01234568",
    "max_repeats": 10
  }'
```

### Getting a Form with Details

```bash
# Get a form with all its sections and questions
curl -X GET "http://localhost:8000/api/v1/forms/6123456789abcdef01234567/details" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID"
```

### Using the Form Engine API

The Form Engine API provides a flexible way to render forms with conditional logic:

```bash
# Get form in sequential mode (question by question)
curl -X POST "http://localhost:8000/api/v1/forms/6123456789abcdef01234567/engine" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "answers": {
      "6323456789abcdef01234567": "Acme Inc.",
      "6323456789abcdef01234569": 3
    },
    "mode": "sequential",
    "include_dependencies": true,
    "include_validation": true,
    "include_visibility_rules": true
  }'
```

```bash
# Get form in full mode (entire structure with visibility logic)
curl -X POST "http://localhost:8000/api/v1/forms/6123456789abcdef01234567/engine" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "answers": {
      "6323456789abcdef01234567": "Acme Inc.",
      "6323456789abcdef01234569": 3
    },
    "mode": "full",
    "include_dependencies": true,
    "include_validation": true,
    "include_visibility_rules": true
  }'
```

### Submitting a Form

```bash
# Submit a completed form
curl -X POST "http://localhost:8000/api/v1/forms/6123456789abcdef01234567/submit" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "answers": {
      "6323456789abcdef01234567": "Acme Inc.",
      "6323456789abcdef01234568": "Technology",
      "6323456789abcdef01234569": 3,
      "6323456789abcdef01234570_0": "John Doe",
      "6323456789abcdef01234571_0": "CEO",
      "6323456789abcdef01234570_1": "Jane Smith",
      "6323456789abcdef01234571_1": "CTO",
      "6323456789abcdef01234570_2": "Bob Johnson",
      "6323456789abcdef01234571_2": "CFO"
    }
  }'
```

## Thesis Management System

### Creating an Investment Thesis

```bash
# Create a new investment thesis
curl -X POST "http://localhost:8000/api/v1/scoring/thesis" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "SaaS Investment Thesis",
    "description": "Thesis for evaluating SaaS startups",
    "form_id": "6123456789abcdef01234567",
    "status": "draft",
    "is_active": true,
    "scoring_rules": [
      {
        "question_id": "6323456789abcdef01234568",
        "weight": 5,
        "config": {
          "target_value": "Technology",
          "min_score": 0.2,
          "strategy": "exact_match"
        }
      }
    ],
    "match_rules": [
      {
        "question_id": "6323456789abcdef01234569",
        "operator": ">=",
        "value": 2,
        "required": true
      }
    ]
  }'
```

### Previewing Form Questions for Thesis Configuration

```bash
# Preview form questions with scoring configuration options
curl -X GET "http://localhost:8000/api/v1/scoring/thesis/preview/6123456789abcdef01234567?include_suggestions=true" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID"
```

### Calculating Score for a Form Submission

```bash
# Calculate score for a form submission against a thesis
curl -X POST "http://localhost:8000/api/v1/scoring/thesis/6423456789abcdef01234567/calculate-score" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "6323456789abcdef01234567": "Acme Inc.",
    "6323456789abcdef01234568": "Technology",
    "6323456789abcdef01234569": 3,
    "6323456789abcdef01234570_0": "John Doe",
    "6323456789abcdef01234571_0": "CEO",
    "6323456789abcdef01234570_1": "Jane Smith",
    "6323456789abcdef01234571_1": "CTO",
    "6323456789abcdef01234570_2": "Bob Johnson",
    "6323456789abcdef01234571_2": "CFO"
  }'
```

### Finding Matching Theses

```bash
# Find all theses that match a form submission
curl -X POST "http://localhost:8000/api/v1/scoring/thesis/find-matching" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "form_id": "6123456789abcdef01234567",
    "form_responses": {
      "6323456789abcdef01234567": "Acme Inc.",
      "6323456789abcdef01234568": "Technology",
      "6323456789abcdef01234569": 3
    }
  }'
```

## Sharing Forms

### Creating a Sharing Configuration

```bash
# Create a sharing configuration for a form
curl -X POST "http://localhost:8000/api/v1/sharing" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "resource_type": "form",
    "resource_id": "6123456789abcdef01234567",
    "enabled": true,
    "sharing_types": ["link", "embed", "qr_code"],
    "allowed_domains": ["example.com", "trusted-partner.com"],
    "embed_type": "inline",
    "custom_styles": {"theme": "light", "primaryColor": "#007bff"},
    "tracking_enabled": true
  }'
```

### Generating a Sharing Link

```bash
# Generate a sharing link
curl -X POST "http://localhost:8000/api/v1/sharing/6523456789abcdef01234567/link" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-ORG-ID: YOUR_ORGANIZATION_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "expires_at": "2023-12-31T23:59:59Z",
    "metadata": {"source": "dashboard", "campaign": "spring2023"}
  }'
```

## Frontend Integration Tips

1. **Form Rendering**:
   - Use the Form Engine API in "sequential" mode for step-by-step forms
   - Use "full" mode for displaying the entire form structure
   - Handle repeatable sections by dynamically creating UI components based on the control question's answer

2. **Thesis Configuration**:
   - Use the preview endpoint to get suggested configurations for each question type
   - Implement a user-friendly interface for setting up scoring rules
   - Provide templates for common match rule patterns

3. **Scoring Visualization**:
   - Display overall scores and breakdowns by category
   - Highlight areas of strong match and potential concerns
   - Show which match rules passed or failed

4. **Form Sharing**:
   - Implement copy-to-clipboard functionality for sharing links
   - Provide a preview of how embedded forms will appear
   - Generate QR codes for mobile access

## Best Practices

1. **Error Handling**: Implement robust error handling for all API calls
2. **Caching**: Cache form structures to improve performance
3. **Progressive Loading**: Load form sections as needed rather than all at once
4. **Validation**: Implement client-side validation matching server rules
5. **Accessibility**: Ensure forms are accessible to all users
6. **Mobile Optimization**: Design responsive interfaces for all screen sizes
