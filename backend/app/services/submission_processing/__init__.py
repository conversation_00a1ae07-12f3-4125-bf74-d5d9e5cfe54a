"""
Submission Processing Service

This module provides services for processing form submissions through the complete workflow:
1. Exclusion filter checking
2. Thesis matching
3. Thesis scoring
4. Deal creation
5. Enrichment job queuing
"""

from .interfaces import SubmissionProcessingServiceInterface
from .service import SubmissionProcessingService

__all__ = [
    "SubmissionProcessingServiceInterface",
    "SubmissionProcessingService",
]
