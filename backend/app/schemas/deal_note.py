"""
Deal Note API Schemas

Pydantic models for Deal Note API requests and responses.
"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field

from app.models.base import TractionXModel
from app.utils.common import ObjectIdField, PyObjectId


class DealNoteCreate(BaseModel):
    """Schema for creating a new deal note."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "structured_content": [
                    {
                        "type": "paragraph",
                        "children": [
                            {"text": "Discussed traction risk in IC. "},
                            {
                                "type": "mention",
                                "user_id": "507f1f77bcf86cd799439011",
                                "name": "Sam",
                                "children": [{"text": ""}],
                            },
                            {"text": " will follow up."},
                        ],
                    }
                ],
                "tagged_user_ids": ["507f1f77bcf86cd799439011"],
                "pinned": False,
            }
        }
    )

    structured_content: List[Dict[str, Any]] = Field(
        ...,
        description="Structured rich text content supporting mentions and formatting",
    )
    tagged_user_ids: Optional[List[str]] = Field(
        default_factory=list, description="List of user IDs to tag/mention in this note"
    )
    pinned: Optional[bool] = Field(
        default=False, description="Whether to pin this note to the top"
    )


class DealNoteUpdate(BaseModel):
    """Schema for updating an existing deal note."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "structured_content": [
                    {
                        "type": "paragraph",
                        "children": [
                            {"text": "Updated after call with CEO. "},
                            {
                                "type": "mention",
                                "user_id": "507f1f77bcf86cd799439012",
                                "name": "Sam",
                                "children": [{"text": ""}],
                            },
                            {"text": " Metrics checked."},
                        ],
                    }
                ],
                "tagged_user_ids": ["507f1f77bcf86cd799439012"],
                "pinned": True,
            }
        }
    )

    structured_content: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="Structured rich text content supporting mentions and formatting",
    )
    tagged_user_ids: Optional[List[str]] = Field(
        None, description="List of user IDs to tag/mention in this note"
    )
    pinned: Optional[bool] = Field(
        None, description="Whether to pin this note to the top"
    )


class DealNoteResponse(TractionXModel):
    """Schema for deal note responses."""

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "_id": "note123",
                "deal_id": "deal456",
                "org_id": "org789",
                "structured_content": [
                    {
                        "type": "paragraph",
                        "children": [
                            {"text": "Discussed traction risk in IC. "},
                            {
                                "type": "mention",
                                "user_id": "user456",
                                "name": "Sam",
                                "children": [{"text": ""}],
                            },
                            {"text": " will follow up."},
                        ],
                    }
                ],
                "created_by": {
                    "_id": "user123",
                    "name": "Prasanna",
                    "email": "<EMAIL>",
                },
                "tagged_user_ids": ["user456"],
                "pinned": False,
                "created_at": 1720622955,
                "updated_at": 1720622955,
            }
        },
    )

    id: ObjectIdField = Field(default_factory=PyObjectId, description="Note ID")
    deal_id: str = Field(..., description="Deal ID")
    org_id: str = Field(..., description="Organization ID")

    # Content
    structured_content: List[Dict[str, Any]] = Field(
        ...,
        description="Structured rich text content supporting mentions and formatting",
    )
    tagged_user_ids: List[str] = Field(
        default_factory=list, description="List of tagged user IDs"
    )
    pinned: bool = Field(..., description="Whether note is pinned")

    # User information
    created_by: dict = Field(..., description="User who created the note")

    # Timestamps
    created_at: int = Field(..., description="Creation timestamp")
    updated_at: int = Field(..., description="Last update timestamp")


class DealNoteListResponse(BaseModel):
    """Schema for paginated deal note list responses."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "notes": [
                    {
                        "_id": "note123",
                        "content": "Discussed traction risk in IC. @Sam will follow up.",
                        "created_by": {
                            "_id": "user123",
                            "name": "Prasanna",
                            "email": "<EMAIL>",
                        },
                        "tagged_user_ids": ["user456"],
                        "pinned": False,
                        "created_at": 1720622955,
                    }
                ],
                "total": 1,
                "skip": 0,
                "limit": 100,
                "has_more": False,
            }
        }
    )

    notes: List[DealNoteResponse] = Field(..., description="List of notes")
    total: int = Field(..., description="Total number of notes")
    skip: int = Field(..., description="Number of notes skipped")
    limit: int = Field(..., description="Maximum number of notes returned")
    has_more: bool = Field(..., description="Whether there are more notes available")


class DealNoteCreateResponse(BaseModel):
    """Schema for deal note creation response."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "success": True,
                "note_id": "note123",
                "message": "Note created successfully",
            }
        }
    )

    success: bool = Field(..., description="Whether the operation was successful")
    note_id: str = Field(..., description="ID of the created note")
    message: str = Field(..., description="Success message")


class DealNoteDeleteResponse(BaseModel):
    """Schema for deal note deletion response."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {"success": True, "message": "Note deleted successfully"}
        }
    )

    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Success message")


class DealNotePinRequest(BaseModel):
    """Schema for pinning/unpinning a deal note."""

    model_config = ConfigDict(json_schema_extra={"example": {"pinned": True}})

    pinned: bool = Field(..., description="Whether to pin the note")
