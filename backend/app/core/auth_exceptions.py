"""
Custom exceptions for authentication and authorization.
These exceptions provide a structured way to handle auth-related errors.
"""

from fastapi import HTTPEx<PERSON>, status
from typing import Optional, Dict

from app.core.logging import get_logger

logger = get_logger(__name__)


class AuthError(HTTPException):
    """Base class for authentication and authorization errors."""
    
    def __init__(
        self,
        status_code: int,
        detail: str,
        headers: Optional[Dict[str, str]] = None,
        log_message: Optional[str] = None,
        log_level: str = "warning"
    ):
        super().__init__(status_code=status_code, detail=detail, headers=headers)
        
        # Log the error
        log_msg = log_message or detail
        if log_level == "error":
            logger.error(log_msg)
        elif log_level == "warning":
            logger.warning(log_msg)
        elif log_level == "info":
            logger.info(log_msg)
        else:
            logger.debug(log_msg)


class TokenError(AuthError):
    """Base class for token-related errors."""
    
    def __init__(
        self,
        detail: str,
        headers: Optional[Dict[str, str]] = None,
        log_message: Optional[str] = None,
        log_level: str = "warning"
    ):
        headers = headers or {"WWW-Authenticate": "Bearer"}
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers=headers,
            log_message=log_message,
            log_level=log_level
        )


class TokenExpiredError(TokenError):
    """Exception raised when a token has expired."""
    
    def __init__(
        self,
        token_preview: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None
    ):
        token_info = f" ({token_preview}...)" if token_preview else ""
        log_message = f"Token expired{token_info}"
        super().__init__(
            detail="Token has expired",
            headers=headers,
            log_message=log_message
        )

class InvalidTokenError(TokenError):
    """Exception raised when a token is invalid."""
    
    def __init__(
        self,
        reason: Optional[str] = None,
        token_preview: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None
    ):
        token_info = f" ({token_preview}...)" if token_preview else ""
        reason_info = f": {reason}" if reason else ""
        log_message = f"Invalid token{token_info}{reason_info}"
        super().__init__(
            detail="Invalid token",
            headers=headers,
            log_message=log_message
        )


class TokenTypeError(TokenError):
    """Exception raised when a token is of the wrong type."""
    
    def __init__(
        self,
        expected_type: str,
        actual_type: str,
        headers: Optional[Dict[str, str]] = None
    ):
        log_message = f"Token type mismatch: expected {expected_type}, got {actual_type}"
        super().__init__(
            detail="Invalid token type",
            headers=headers,
            log_message=log_message
        )


class MissingTokenError(TokenError):
    """Exception raised when no token is provided."""
    
    def __init__(
        self,
        headers: Optional[Dict[str, str]] = None
    ):
        super().__init__(
            detail="No authentication token provided",
            headers=headers,
            log_message="Missing authentication token"
        )


class UserNotFoundError(AuthError):
    """Exception raised when a user is not found."""
    
    def __init__(
        self,
        user_id: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None
    ):
        user_info = f" (ID: {user_id})" if user_id else ""
        log_message = f"User not found{user_info}"
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
            headers=headers,
            log_message=log_message
        )


class InactiveUserError(AuthError):
    """Exception raised when a user is inactive."""
    
    def __init__(
        self,
        user_id: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None
    ):
        user_info = f" (ID: {user_id})" if user_id else ""
        log_message = f"Inactive user{user_info}"
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Account is not active",
            headers=headers,
            log_message=log_message
        )
