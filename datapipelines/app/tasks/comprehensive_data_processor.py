"""
Comprehensive Data Processor for TractionX Data Pipeline Service.

This module processes comprehensive enrichment data and populates the database tables:
1. company.companies - Canonical, merged, investor-facing entity
2. company.enrichment_sources - Raw data and extracted fields per source
3. company.insights - Structured LLM output (signals from site, deck, news)
4. company.links - Discovered URLs (from sitemap, scraping, AI)

The processor handles data normalization, conflict resolution, and structured storage.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from app.configs import get_logger
from app.storage.rds_storage import RDSStorage

logger = get_logger(__name__)


class ComprehensiveDataProcessor:
    """
    Processes comprehensive enrichment data and populates database tables.

    Handles data extraction, normalization, conflict resolution, and structured storage
    across multiple data sources (LinkedIn, Crunchbase, Website Insights).
    """

    def __init__(self, rds_storage: RDSStorage):
        self.rds = rds_storage
        self.logger = get_logger(f"{__name__}.ComprehensiveDataProcessor")

    async def process_comprehensive_enrichment_data(
        self, enrichment_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process comprehensive enrichment data and merge into company record."""
        try:
            company_domain = enrichment_data.get("company_domain")
            org_id = enrichment_data.get("org_id")
            barrier_group_id = enrichment_data.get("barrier_group_id")

            self.logger.info(f"Processing enrichment data for {company_domain}")

            if not all([company_domain, org_id, barrier_group_id]):
                return {
                    "success": False,
                    "error": "Missing required fields: company_domain, org_id, or barrier_group_id",
                }

            # Get or create company UUID
            company_uuid = await self._get_or_create_company_uuid(
                str(company_domain), str(org_id)
            )

            # Process each data source
            processing_results = {}

            # Process LinkedIn data
            linkedin_data = enrichment_data.get("data", {}).get("linkedin_scraping")
            if linkedin_data and linkedin_data.get("status") == "success":
                self.logger.info(f"Processing LinkedIn data for {company_domain}")
                linkedin_result = await self._process_linkedin_data(
                    company_uuid, linkedin_data
                )
                processing_results["linkedin"] = linkedin_result

                # Store LinkedIn enrichment source
                if linkedin_result and linkedin_result.get("status") == "success":
                    await self.rds.store_enrichment_source(
                        company_uuid=company_uuid,
                        source_name="linkedin_scraping",
                        raw_data=linkedin_data.get("raw_s3_data", {}),
                        extracted_fields=linkedin_result.get("linkedin_data", {}),
                        s3_raw_data_url=linkedin_data.get("s3_raw_data_url"),
                    )

            # Process Crunchbase data
            crunchbase_data = enrichment_data.get("data", {}).get("crunchbase_scraping")
            if crunchbase_data and crunchbase_data.get("status") == "success":
                self.logger.info(f"Processing Crunchbase data for {company_domain}")
                crunchbase_result = await self._process_crunchbase_data(
                    company_uuid, crunchbase_data
                )
                processing_results["crunchbase"] = crunchbase_result

                # Store Crunchbase enrichment source
                if crunchbase_result and crunchbase_result.get("status") == "success":
                    await self.rds.store_enrichment_source(
                        company_uuid=company_uuid,
                        source_name="crunchbase_scraping",
                        raw_data=crunchbase_data.get("raw_s3_data", {}),
                        extracted_fields=crunchbase_result.get("cb_data", {}),
                        s3_raw_data_url=crunchbase_data.get("s3_raw_data_url"),
                    )

            # Process website insights
            website_insights = enrichment_data.get("data", {}).get(
                "website_insights", {}
            )
            if website_insights:
                self.logger.info(f"Processing website insights for {company_domain}")
                website_result = await self._process_website_insights(
                    company_uuid, website_insights
                )
                processing_results["website"] = website_result

                # Store website insights enrichment source
                if website_result and website_result.get("status") == "success":
                    await self.rds.store_enrichment_source(
                        company_uuid=company_uuid,
                        source_name="website_insights",
                        raw_data=website_insights,
                        extracted_fields=website_result.get("insights", {}),
                        s3_raw_data_url=None,
                    )

            # Process Apollo company data
            apollo_data = enrichment_data.get("data", {}).get("apollo_data")
            if apollo_data and apollo_data.get("status") == "success":
                self.logger.info(f"Processing Apollo company data for {company_domain}")
                apollo_result = await self._process_apollo_data(
                    company_uuid, apollo_data
                )
                processing_results["apollo"] = apollo_result

                # Store Apollo enrichment source
                if apollo_result and apollo_result.get("status") == "success":
                    await self.rds.store_enrichment_source(
                        company_uuid=company_uuid,
                        source_name="apollo_data",
                        raw_data=apollo_data.get("raw_s3_data", {}),
                        extracted_fields=apollo_result.get("apollo_data", {}),
                        s3_raw_data_url=apollo_data.get("source_s3_key"),
                    )

            # Process PitchBook company data
            pitchbook_data = enrichment_data.get("data", {}).get("pitchbook_data")
            if pitchbook_data and pitchbook_data.get("status") == "success":
                self.logger.info(
                    f"Processing PitchBook company data for {company_domain}"
                )
                pitchbook_result = await self._process_pitchbook_data(
                    company_uuid, pitchbook_data
                )
                processing_results["pitchbook"] = pitchbook_result

                # Store PitchBook enrichment source
                if pitchbook_result and pitchbook_result.get("status") == "success":
                    await self.rds.store_enrichment_source(
                        company_uuid=company_uuid,
                        source_name="pitchbook_data",
                        raw_data=pitchbook_data.get("raw_s3_data", {}),
                        extracted_fields=pitchbook_result.get("pitchbook_data", {}),
                        s3_raw_data_url=pitchbook_data.get("source_s3_key"),
                    )

            # Update company record with merged data
            self.logger.info(f"Updating company record for {company_domain}")
            merged_data = await self._update_company_record(
                company_uuid, enrichment_data, processing_results
            )

            self.logger.info(
                f"Successfully processed enrichment data for {company_domain}"
            )

            return {
                "success": True,
                "company_uuid": company_uuid,
                "company_domain": company_domain,
                "org_id": org_id,
                "barrier_group_id": barrier_group_id,
                "processing_results": processing_results,
                "merged_data": merged_data,
            }

        except Exception as e:
            self.logger.error(f"Error processing comprehensive enrichment data: {e}")
            return {"success": False, "error": str(e)}

    async def _get_or_create_company_uuid(
        self, company_domain: str, org_id: str
    ) -> str:
        """Get existing company UUID or create new one."""
        try:
            # Use the new RDS method for company schema
            company_uuid = await self.rds.get_or_create_company_by_domain(
                domain=company_domain, org_id=org_id, source="comprehensive_enrichment"
            )

            self.logger.info(f"Retrieved/created company UUID: {company_uuid}")
            return company_uuid

        except Exception as e:
            self.logger.error(f"Error in _get_or_create_company_uuid: {e}")
            raise

    async def _process_linkedin_data(
        self, company_uuid: str, linkedin_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process LinkedIn data and extract relevant fields."""
        try:
            processed_data = linkedin_data.get("processed_data", {})

            if not processed_data:
                return {"status": "error", "error": "No processed data available"}

            # Extract comprehensive LinkedIn data
            linkedin_extracted = {
                "url": processed_data.get("url"),
                "logo": processed_data.get("logo"),
                "image": processed_data.get("image"),
                "founded": processed_data.get("founded"),
                "similar": processed_data.get("similar", []),
                "updates": processed_data.get("updates", []),
                "employees": processed_data.get("employees", []),
                "followers": processed_data.get("followers"),
                "company_id": processed_data.get("company_id"),
                "industries": processed_data.get("industries"),
                "company_size": processed_data.get("company_size"),
                "country_code": processed_data.get("country_code"),
                "processed_at": processed_data.get("processed_at"),
                "source_s3_key": processed_data.get("source_s3_key"),
                "organization_type": processed_data.get("organization_type"),
                "get_directions_url": processed_data.get("get_directions_url"),
                "formatted_locations": processed_data.get("formatted_locations", []),
                "employees_in_linkedin": processed_data.get("employees_in_linkedin"),
            }

            return {
                "status": "success",
                "linkedin_data": linkedin_extracted,
            }

        except Exception as e:
            self.logger.error(f"Error processing LinkedIn data: {e}")
            return {"status": "error", "error": str(e)}

    async def _process_crunchbase_data(
        self, company_uuid: str, crunchbase_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process Crunchbase data and extract relevant fields."""
        try:
            raw_data = crunchbase_data.get("raw_s3_data", {}).get("raw_data", [])

            if not raw_data:
                return {"status": "error", "error": "No raw data available"}

            # Get the first (and usually only) raw data entry
            raw_entry = raw_data[0] if raw_data else {}

            # Extract basic fields from processed data
            cb_data = {
                "type": raw_entry.get("type"),
                "uuid": raw_entry.get("uuid"),
                "about": raw_entry.get("about"),
                "image": raw_entry.get("image"),
                "cb_url": raw_entry.get("url"),
                "region": raw_entry.get("region"),
                "address": raw_entry.get("address"),
                "cb_rank": raw_entry.get("cb_rank"),
                "location": raw_entry.get("location", []),
                "input_url": raw_entry.get("url"),
                "timestamp": raw_entry.get("timestamp"),
                "company_id": raw_entry.get("company_id"),
                "heat_score": raw_entry.get("heat_score"),
                "industries": raw_entry.get("industries", []),
                "ipo_status": raw_entry.get("ipo_status"),
                "legal_name": raw_entry.get("legal_name"),
                "company_type": raw_entry.get("company_type"),
                "growth_score": raw_entry.get("growth_score"),
                "growth_trend": raw_entry.get("growth_trend"),
                "hq_continent": raw_entry.get("hq_continent"),
                "original_url": raw_entry.get("url"),
                "phone_number": raw_entry.get("contact_phone")
                or raw_entry.get("phone_number"),
                "email_address": raw_entry.get("contact_email")
                or raw_entry.get("email_address"),
                "featured_list": raw_entry.get("featured_list", []),
                "builtwith_tech": raw_entry.get("builtwith_tech", []),
                "monthly_visits": raw_entry.get("monthly_visits"),
                "company_industry": raw_entry.get("company_industry"),
                "company_overview": raw_entry.get("company_overview"),
                "full_description": raw_entry.get("full_description"),
                "operating_status": raw_entry.get("operating_status"),
                "active_tech_count": raw_entry.get("active_tech_count"),
                "social_media_links": raw_entry.get("social_media_links", []),
                "headquarters_regions": raw_entry.get("headquarters_regions", []),
                "semrush_last_updated": raw_entry.get("semrush_last_updated"),
                "monthly_visits_growth": raw_entry.get("monthly_visits_growth"),
                "num_employee_profiles": raw_entry.get("num_employee_profiles"),
                "products_and_services": raw_entry.get("products_and_services"),
                "semrush_location_list": raw_entry.get("semrush_location_list", []),
                "technology_highlights": raw_entry.get("technology_highlights", {}),
                "company_activity_level": raw_entry.get("company_activity_level"),
                "semrush_visits_mom_pct": raw_entry.get("semrush_visits_mom_pct"),
                "web_traffic_by_semrush": raw_entry.get("web_traffic_by_semrush", {}),
                "semrush_visits_latest_month": raw_entry.get(
                    "semrush_visits_latest_month"
                ),
                "builtwith_num_technologies_used": raw_entry.get(
                    "builtwith_num_technologies_used"
                ),
            }

            return {
                "status": "success",
                "cb_data": cb_data,
            }

        except Exception as e:
            self.logger.error(f"Error processing Crunchbase data: {e}")
            return {"status": "error", "error": str(e)}

    async def _process_apollo_data(
        self, company_uuid: str, apollo_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process Apollo company data."""
        try:
            # Extract processed data from Apollo S3 storage
            raw_s3_data = apollo_data.get("raw_s3_data", {})
            source_s3_key = apollo_data.get("source_s3_key")

            # Try to get processed data from the raw S3 data
            processed_data = {}
            if raw_s3_data and isinstance(raw_s3_data, dict):
                processed_data = raw_s3_data.get("processed_data", {})

            if not processed_data:
                return {
                    "status": "error",
                    "error": "No processed Apollo data available",
                }

            # Extract company data and departments data
            company_data = processed_data.get("company_data", {})

            # Extract departments from Apollo's departmental_head_count structure
            departments_data = []
            if company_data and isinstance(company_data, dict):
                org = company_data.get("organization", {})
                departmental_head_count = org.get("departmental_head_count", {})

                if departmental_head_count and isinstance(
                    departmental_head_count, dict
                ):
                    for dept_name, head_count in departmental_head_count.items():
                        if head_count and head_count > 0:
                            departments_data.append({
                                "department_name": dept_name.replace("_", " ").title(),
                                "head_count": head_count,
                                "confidence_score": 0.9,  # High confidence for Apollo data
                            })

            # Store departments data in the new company.departments table
            if departments_data:
                self.logger.info(
                    f"Storing {len(departments_data)} departments from Apollo data"
                )

                # Store departments using the new method
                await self.rds.store_company_departments(
                    company_uuid=company_uuid,
                    departments=departments_data,
                    source="apollo_data",
                )

                self.logger.info(
                    f"Successfully stored departments: {[dept['department_name'] for dept in departments_data]}"
                )
            else:
                self.logger.info("No department data found in Apollo data")

            return {
                "status": "success",
                "apollo_data": company_data,
                "departments_count": len(departments_data),
                "source_s3_key": source_s3_key,
            }

        except Exception as e:
            self.logger.error(f"Error processing Apollo data: {e}")
            return {"status": "error", "error": str(e)}

    async def _process_pitchbook_data(
        self, company_uuid: str, pitchbook_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process PitchBook company data."""
        try:
            # Extract processed data from PitchBook S3 storage
            raw_s3_data = pitchbook_data.get("raw_s3_data", {})
            source_s3_key = pitchbook_data.get("source_s3_key")

            # Try to get processed data from the raw S3 data
            processed_data = {}
            if raw_s3_data and isinstance(raw_s3_data, dict):
                processed_data = raw_s3_data.get("processed_data", {})

            if not processed_data:
                return {
                    "status": "error",
                    "error": "No processed PitchBook data available",
                }

            # Extract company data from processed data
            company_data = processed_data

            return {
                "status": "success",
                "pitchbook_data": company_data,
                "source_s3_key": source_s3_key,
            }

        except Exception as e:
            self.logger.error(f"Error processing PitchBook data: {e}")
            return {"status": "error", "error": str(e)}

    async def _process_website_insights(
        self, company_uuid: str, website_insights: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process website insights data."""
        try:
            insights_count = 0
            successful_urls = 0
            failed_urls = 0

            for url, insight_data in website_insights.items():
                if insight_data.get("status") == "success" and insight_data.get("data"):
                    data = insight_data["data"]
                    insights = data.get("insights", [])

                    # Store each insight in company.insights using new RDS method
                    for insight in insights:
                        await self.rds.store_company_insight(
                            company_uuid,
                            {
                                "source_url": url,
                                "label": insight.get("label", ""),
                                "value": insight.get("value", ""),
                                "type": insight.get("type", "general"),
                                "confidence": 0.8,  # Default confidence for website insights
                                "source": "website_scraper",
                            },
                        )
                        insights_count += 1

                    successful_urls += 1
                else:
                    failed_urls += 1

            return {
                "status": "success",
                "insights_count": insights_count,
                "successful_urls": successful_urls,
                "failed_urls": failed_urls,
            }

        except Exception as e:
            self.logger.error(f"Error processing website insights: {e}")
            return {"status": "error", "error": str(e)}

    async def _update_company_record(
        self,
        company_uuid: str,
        enrichment_data: Dict[str, Any],
        processing_results: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Update main company record with merged data from all sources."""
        try:
            # Collect data from all sources
            merged_data = self._merge_company_data(enrichment_data, processing_results)

            self.logger.info(f"Merged data keys: {list(merged_data.keys())}")

            # Check if merged_data is actually populated
            non_null_values = {
                k: v for k, v in merged_data.items() if v is not None and v != ""
            }
            self.logger.info(f"Non-null values in merged data: {non_null_values}")

            if not non_null_values:
                self.logger.error("CRITICAL: Merged data contains no non-null values!")
                return {
                    "status": "error",
                    "error": "No data to update - merged data is empty",
                }

            # Update company record in company.companies using new RDS method
            success = await self.rds.update_company_record_company_schema(
                company_uuid, merged_data
            )

            if success:
                self.logger.info(
                    f"Successfully updated company record with {len(non_null_values)} fields"
                )
                return {"status": "success", "updated_fields": list(merged_data.keys())}
            else:
                self.logger.error("Database update returned False - no rows updated")
                return {"status": "error", "error": "Failed to update company record"}

        except Exception as e:
            self.logger.error(f"Error updating company record: {e}")
            return {"status": "error", "error": str(e)}

    def _merge_company_data(
        self, enrichment_data: Dict[str, Any], processing_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Merge data from multiple sources with conflict resolution."""
        merged = {}

        # Get data sources directly from enrichment_data
        data_sources = enrichment_data.get("data", {})

        self.logger.info(
            f"Starting data merge. Available sources: {list(data_sources.keys())}"
        )

        # Priority order: LinkedIn > Crunchbase > Apollo > PitchBook > Website Insights
        sources = [
            "linkedin_scraping",
            "crunchbase_scraping",
            "apollo_data",
            "pitchbook_data",
            "website_insights",
        ]

        # Initialize JSONB data containers
        linkedin_data = {}
        cb_data = {}
        apollo_data = {}
        pb_data = {}
        funding_data = {}

        for source in sources:
            source_data = data_sources.get(source, {})

            # Check if source has successful data
            if source_data.get("status") == "success":
                self.logger.info(f"Processing {source} data")

                # Try processed_data first
                processed_data = source_data.get("processed_data", {})

                # If processed_data is empty/null, try raw_s3_data
                if not processed_data and source_data.get("raw_s3_data"):
                    raw_data = source_data.get("raw_s3_data", {})
                    if isinstance(raw_data, dict) and raw_data.get("raw_data"):
                        # Extract from first raw data entry
                        raw_entry = (
                            raw_data["raw_data"][0] if raw_data["raw_data"] else {}
                        )
                        processed_data = raw_entry
                        self.logger.info(f"Using raw data for {source}")

                # CASCADE 1: Map to existing company.companies fields (only if not already set)

                # Name field
                if not merged.get("name"):
                    name = self._extract_name_from_source(source, processed_data)
                    if name:
                        merged["name"] = name
                        self.logger.info(f"Set name from {source}: {name}")

                # Description field
                if not merged.get("description"):
                    description = self._extract_description_from_source(
                        source, processed_data
                    )
                    if description:
                        merged["description"] = description
                        self.logger.info(f"Set description from {source}")

                # Industry field
                if not merged.get("industry"):
                    industry = self._extract_industry_from_source(
                        source, processed_data
                    )
                    if industry:
                        merged["industry"] = industry
                        self.logger.info(f"Set industry from {source}: {industry}")

                # Sub-industry field
                if not merged.get("sub_industry"):
                    sub_industry = self._extract_sub_industry_from_source(
                        source, processed_data
                    )
                    if sub_industry:
                        merged["sub_industry"] = sub_industry
                        self.logger.info(
                            f"Set sub_industry from {source}: {sub_industry}"
                        )

                # Employee count field
                if not merged.get("employee_count"):
                    employee_count = self._extract_employee_count_from_source(
                        source, processed_data
                    )
                    if employee_count:
                        merged["employee_count"] = employee_count
                        self.logger.info(
                            f"Set employee_count from {source}: {employee_count}"
                        )

                # Employee count range field
                if not merged.get("employee_count_range"):
                    employee_range = self._extract_employee_range_from_source(
                        source, processed_data
                    )
                    if employee_range:
                        merged["employee_count_range"] = employee_range
                        self.logger.info(
                            f"Set employee_count_range from {source}: {employee_range}"
                        )

                # Founded year field
                if not merged.get("founded_year"):
                    founded_year = self._extract_founded_year_from_source(
                        source, processed_data
                    )
                    if founded_year:
                        merged["founded_year"] = founded_year
                        self.logger.info(
                            f"Set founded_year from {source}: {founded_year}"
                        )

                # Website field
                if not merged.get("website"):
                    website = self._extract_website_from_source(source, processed_data)
                    if website:
                        merged["website"] = website
                        self.logger.info(f"Set website from {source}: {website}")

                # LinkedIn URL field
                if not merged.get("linkedin_url"):
                    linkedin_url = self._extract_linkedin_url_from_source(
                        source, processed_data
                    )
                    if linkedin_url:
                        merged["linkedin_url"] = linkedin_url
                        self.logger.info(
                            f"Set linkedin_url from {source}: {linkedin_url}"
                        )

                # Twitter URL field
                if not merged.get("twitter_url"):
                    twitter_url = self._extract_twitter_url_from_source(
                        source, processed_data
                    )
                    if twitter_url:
                        merged["twitter_url"] = twitter_url
                        self.logger.info(
                            f"Set twitter_url from {source}: {twitter_url}"
                        )

                # Facebook URL field
                if not merged.get("facebook_url"):
                    facebook_url = self._extract_facebook_url_from_source(
                        source, processed_data
                    )
                    if facebook_url:
                        merged["facebook_url"] = facebook_url
                        self.logger.info(
                            f"Set facebook_url from {source}: {facebook_url}"
                        )

                # Email field
                if not merged.get("email"):
                    email = self._extract_email_from_source(source, processed_data)
                    if email:
                        merged["email"] = email
                        self.logger.info(f"Set email from {source}: {email}")

                # Phone field
                if not merged.get("phone"):
                    phone = self._extract_phone_from_source(source, processed_data)
                    if phone:
                        merged["phone"] = phone
                        self.logger.info(f"Set phone from {source}: {phone}")

                # HQ Location field
                if not merged.get("hq_location"):
                    hq_location = self._extract_hq_location_from_source(
                        source, processed_data
                    )
                    if hq_location:
                        merged["hq_location"] = hq_location
                        self.logger.info(
                            f"Set hq_location from {source}: {hq_location}"
                        )

                # Competitors field (extracted from multiple sources)
                if not merged.get("competitors"):
                    competitors = self._extract_competitors_from_source(
                        source, processed_data
                    )
                    if competitors:
                        merged["competitors"] = competitors
                        competitor_names = [
                            comp.get("name", "")
                            for comp in competitors
                            if comp.get("name")
                        ]
                        self.logger.info(
                            f"Set competitors from {source}: {len(competitors)} competitors - {competitor_names}"
                        )

                # Note: PitchBook-specific fields like latest_deal_amount, latest_deal_amount_value,
                # latest_deal_type, financing_rounds, last_funding_amount, funding_status, and research_analysis
                # are stored in pb_data JSONB since they don't exist as separate columns in the company.companies table

                # CASCADE 2: Store complete source data in appropriate JSONB fields
                if source == "linkedin_scraping":
                    linkedin_data = processed_data
                    self.logger.info("Stored LinkedIn data in linkedin_data JSONB")
                elif source == "crunchbase_scraping":
                    cb_data = processed_data
                    self.logger.info("Stored Crunchbase data in cb_data JSONB")
                elif source == "apollo_data":
                    # Extract Apollo-specific fields for JSONB storage
                    apollo_specific_fields = (
                        self._extract_apollo_specific_fields_from_source(
                            source, processed_data
                        )
                    )
                    apollo_data = {
                        **processed_data,  # Include all raw data
                        **apollo_specific_fields,  # Add structured Apollo fields
                    }
                    self.logger.info(
                        f"Stored Apollo data in apollo_data JSONB with {len(apollo_specific_fields)} specific fields"
                    )
                elif source == "pitchbook_data":
                    pb_data = processed_data

                    # Log competitor extraction specifically
                    competitor_relationships = processed_data.get(
                        "competitor_relationships", []
                    )
                    if competitor_relationships:
                        competitor_names = [
                            comp.get("name", "")
                            for comp in competitor_relationships
                            if comp.get("name")
                        ]
                        self.logger.info(
                            f"Found {len(competitor_relationships)} competitors in PitchBook data: {competitor_names}"
                        )
                    else:
                        self.logger.info(
                            "No competitor_relationships found in PitchBook data"
                        )

                    self.logger.info("Stored PitchBook data in pb_data JSONB")

                # CASCADE 3: Extract funding data from all sources
                funding_info = self._extract_funding_from_source(source, processed_data)
                if funding_info:
                    funding_data.update(funding_info)
                    funding_keys = list(funding_info.keys())
                    self.logger.info(
                        f"Extracted funding data from {source}: {funding_keys}"
                    )

                    # Log specific funding fields for PitchBook
                    if source == "pitchbook_data":
                        if funding_info.get("last_funding_amount"):
                            self.logger.info(
                                f"Found last_funding_amount: {funding_info['last_funding_amount']}"
                            )
                        if funding_info.get("funding_status"):
                            self.logger.info(
                                f"Determined funding_status: {funding_info['funding_status']}"
                            )

        # Set JSONB fields
        if linkedin_data:
            merged["linkedin_data"] = linkedin_data
        if cb_data:
            merged["cb_data"] = cb_data
        if apollo_data:
            merged["apollo_data"] = apollo_data
        if pb_data:
            merged["pb_data"] = pb_data
        if funding_data:
            merged["funding_data"] = funding_data

        # Set confidence score based on data quality
        confidence_score = self._calculate_confidence_score(merged, data_sources)
        merged["confidence_score"] = confidence_score

        # Set enrichment metadata
        merged["enrichment_date"] = datetime.now(timezone.utc).isoformat()
        if enrichment_data.get("barrier_group_id"):
            merged["s3_raw_data_key"] = (
                f"comprehensive_enrichment/{enrichment_data['barrier_group_id']}"
            )

        self.logger.info(f"Final merged data keys: {list(merged.keys())}")
        return merged

    def _extract_name_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[str]:
        """Extract company name from any data source."""
        if source == "linkedin_scraping":
            return data.get("name")
        elif source == "crunchbase_scraping":
            return data.get("name") or data.get("legal_name")
        elif source == "apollo_data":
            # Extract from organization data
            org = data.get("organization", {})
            return org.get("name")
        elif source == "pitchbook_data":
            return data.get("company_name")
        elif source == "website_insights":
            # Extract from insights
            for insight in data.get("insights", []):
                if insight.get("label") == "Company Name":
                    return insight.get("value")
        return None

    def _extract_description_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[str]:
        """Extract description from any data source."""
        if source == "linkedin_scraping":
            return data.get("about") or data.get("description")
        elif source == "crunchbase_scraping":
            return (
                data.get("about")
                or data.get("description")
                or data.get("full_description")
            )
        elif source == "apollo_data":
            # Extract from organization data
            org = data.get("organization", {})
            return org.get("short_description") or org.get("seo_description")
        elif source == "pitchbook_data":
            return data.get("description")
        elif source == "website_insights":
            for insight in data.get("insights", []):
                if insight.get("label") == "Description":
                    return insight.get("value")
        return None

    def _extract_industry_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[str]:
        """Extract industry from any data source."""
        if source == "linkedin_scraping":
            industries = data.get("industries", [])
            if isinstance(industries, list):
                return " | ".join(industries[:3])
            return str(industries) if industries else None
        elif source == "crunchbase_scraping":
            industries = data.get("industries", [])
            if isinstance(industries, list):
                return " | ".join([
                    ind.get("value", "") for ind in industries[:3] if ind.get("value")
                ])
            return data.get("company_industry")
        elif source == "apollo_data":
            # Extract from organization data
            org = data.get("organization", {})
            return org.get("industry")
        elif source == "pitchbook_data":
            # Extract from contact_information
            for contact in data.get("contact_information", []):
                if contact.get("Type") == "Primary Industry":
                    return contact.get("value")
        elif source == "website_insights":
            for insight in data.get("insights", []):
                if insight.get("label") == "Sector":
                    return insight.get("value")
        return None

    def _extract_sub_industry_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[str]:
        """Extract sub-industry from any data source."""
        if source == "crunchbase_scraping":
            industries = data.get("industries", [])
            if isinstance(industries, list) and len(industries) > 1:
                sub_industries = [
                    ind.get("value", "") for ind in industries[1:4] if ind.get("value")
                ]
                return " | ".join(sub_industries)
        elif source == "apollo_data":
            return data.get("sub_industry")
        elif source == "pitchbook_data":
            # Extract from contact_information
            for contact in data.get("contact_information", []):
                if contact.get("Type") == "Other Industries":
                    return contact.get("value")
        return None

    def _extract_employee_count_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[int]:
        """Extract employee count from any data source."""
        if source == "linkedin_scraping":
            return data.get("employees_in_linkedin")
        elif source == "crunchbase_scraping":
            return data.get("num_employees")
        elif source == "apollo_data":
            # Extract from organization data
            org = data.get("organization", {})
            return org.get("estimated_num_employees")
        elif source == "pitchbook_data":
            return data.get("employees")
        elif source == "website_insights":
            for insight in data.get("insights", []):
                if insight.get("label") == "Employee Count":
                    try:
                        return int(insight.get("value"))
                    except (ValueError, TypeError):
                        pass
        return None

    def _extract_employee_range_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[str]:
        """Extract employee range from any data source."""
        if source == "linkedin_scraping":
            return data.get("company_size")
        elif source == "crunchbase_scraping":
            return data.get("num_employees")
        return None

    def _extract_founded_year_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[int]:
        """Extract founded year from any data source."""
        if source == "linkedin_scraping":
            return data.get("founded")
        elif source == "crunchbase_scraping":
            founded_date = data.get("founded_date")
            if founded_date:
                return self._extract_year_from_date(founded_date)
        elif source == "apollo_data":
            # Extract from organization data
            org = data.get("organization", {})
            return org.get("founded_year")
        elif source == "pitchbook_data":
            return data.get("year_founded")
        elif source == "website_insights":
            for insight in data.get("insights", []):
                if insight.get("label") == "Year Founded":
                    try:
                        return int(insight.get("value"))
                    except (ValueError, TypeError):
                        pass
        return None

    def _extract_website_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[str]:
        """Extract website from any data source."""
        if source == "linkedin_scraping":
            return data.get("website")
        elif source == "crunchbase_scraping":
            return data.get("website") or data.get("url")
        elif source == "apollo_data":
            # Extract from organization data
            org = data.get("organization", {})
            return org.get("website_url")
        elif source == "pitchbook_data":
            # Extract from contact_information
            for contact in data.get("contact_information", []):
                if contact.get("Type") == "Website":
                    return contact.get("value")
        return None

    def _extract_linkedin_url_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[str]:
        """Extract LinkedIn URL from any data source."""
        if source == "linkedin_scraping":
            return data.get("url")
        elif source == "crunchbase_scraping":
            return data.get("linkedin_url")
        elif source == "apollo_data":
            # Extract from organization data
            org = data.get("organization", {})
            return org.get("linkedin_url")
        elif source == "pitchbook_data":
            # Extract from company_socials
            for social in data.get("company_socials", []):
                if "linkedin.com" in social.get("link", ""):
                    return social["link"]
        return None

    def _extract_twitter_url_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[str]:
        """Extract Twitter URL from any data source."""
        if source == "crunchbase_scraping":
            return data.get("twitter_url")
        elif source == "apollo_data":
            # Extract from organization data
            org = data.get("organization", {})
            return org.get("twitter_url")
        return None

    def _extract_facebook_url_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[str]:
        """Extract Facebook URL from any data source."""
        if source == "crunchbase_scraping":
            return data.get("facebook_url")
        elif source == "apollo_data":
            # Extract from organization data
            org = data.get("organization", {})
            return org.get("facebook_url")
        return None

    def _extract_email_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[str]:
        """Extract email from any data source."""
        if source == "crunchbase_scraping":
            return data.get("email_address") or data.get("contact_email")
        elif source == "apollo_data":
            return data.get("email")
        elif source == "website_insights":
            for insight in data.get("insights", []):
                if insight.get("label") == "Contact":
                    value = insight.get("value", "")
                    if "@" in value:
                        return value
        return None

    def _extract_phone_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[str]:
        """Extract phone from any data source."""
        if source == "crunchbase_scraping":
            return data.get("phone_number") or data.get("contact_phone")
        elif source == "apollo_data":
            # Extract from organization data
            org = data.get("organization", {})
            return org.get("phone")
        elif source == "website_insights":
            for insight in data.get("insights", []):
                if insight.get("label") == "Phone":
                    value = insight.get("value", "")
                    if any(char.isdigit() for char in value):
                        return value
        return None

    def _extract_hq_location_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[str]:
        """Extract HQ location from any data source."""
        if source == "linkedin_scraping":
            locations = data.get("locations", [])
            if isinstance(locations, list):
                return " | ".join(locations[:3])
            return data.get("headquarters")
        elif source == "crunchbase_scraping":
            return data.get("address") or data.get("headquarters")
        elif source == "apollo_data":
            # Extract from organization data
            org = data.get("organization", {})
            return org.get("raw_address")
        elif source == "pitchbook_data":
            # Extract from contact_information
            for contact in data.get("contact_information", []):
                if contact.get("Type") == "Corporate Office":
                    return contact.get("value")
        elif source == "website_insights":
            for insight in data.get("insights", []):
                if insight.get("label") == "Headquarters":
                    return insight.get("value")
        return None

    def _extract_latest_deal_amount_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[str]:
        """Extract latest deal amount from any data source."""
        if source == "pitchbook_data":
            return data.get("latest_deal_amount")
        elif source == "crunchbase_scraping":
            return data.get("last_funding_amount")
        return None

    def _extract_latest_deal_amount_value_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[str]:
        """Extract latest deal amount value from any data source."""
        if source == "pitchbook_data":
            return data.get("latest_deal_amount_value")
        return None

    def _extract_latest_deal_type_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[str]:
        """Extract latest deal type from any data source."""
        if source == "pitchbook_data":
            return data.get("latest_deal_type")
        elif source == "crunchbase_scraping":
            return data.get("last_funding_type")
        return None

    def _extract_financing_rounds_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[List[Dict[str, Any]]]:
        """Extract financing rounds from any data source."""
        if source == "pitchbook_data":
            return data.get("financing_rounds")
        elif source == "crunchbase_scraping":
            return data.get("funding_rounds")
        return None

    def _extract_competitors_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Optional[List[Dict[str, Any]]]:
        """Extract competitors from any data source."""
        if source == "pitchbook_data":
            # Extract from competitor_relationships field
            competitor_relationships = data.get("competitor_relationships", [])
            if competitor_relationships and isinstance(competitor_relationships, list):
                return [
                    {
                        "name": comp.get("name", ""),
                        "industry": comp.get("industry"),
                        "url": comp.get("url"),
                        "confidence_score": comp.get("confidence_score", 0.8),
                    }
                    for comp in competitor_relationships
                    if comp.get("name")
                ]
            # Fallback to competitors field if competitor_relationships is not available
            return data.get("competitors")
        elif source == "linkedin_scraping":
            similar = data.get("similar", [])
            if isinstance(similar, list):
                return [
                    {"name": item.get("name"), "url": item.get("url")}
                    for item in similar
                ]
        return None

    def _extract_apollo_specific_fields_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract Apollo-specific fields that should be stored in apollo_data JSONB."""
        apollo_fields = {}

        if source == "apollo_data":
            # Extract organization data
            org = data.get("organization", {})

            # Basic company info
            apollo_fields.update({
                "apollo_id": org.get("id"),
                "website_url": org.get("website_url"),
                "blog_url": org.get("blog_url"),
                "angellist_url": org.get("angellist_url"),
                "crunchbase_url": org.get("crunchbase_url"),
                "primary_domain": org.get("primary_domain"),
                "logo_url": org.get("logo_url"),
                "linkedin_uid": org.get("linkedin_uid"),
                "publicly_traded_symbol": org.get("publicly_traded_symbol"),
                "publicly_traded_exchange": org.get("publicly_traded_exchange"),
                "alexa_ranking": org.get("alexa_ranking"),
                "languages": org.get("languages", []),
                "primary_phone": org.get("primary_phone", {}),
                "phone": org.get("phone"),
                # Industry and keywords
                "keywords": org.get("keywords", []),
                "industries": org.get("industries", []),
                "secondary_industries": org.get("secondary_industries", []),
                "industry_tag_id": org.get("industry_tag_id"),
                "industry_tag_hash": org.get("industry_tag_hash", {}),
                # Location details
                "raw_address": org.get("raw_address"),
                "street_address": org.get("street_address"),
                "city": org.get("city"),
                "state": org.get("state"),
                "postal_code": org.get("postal_code"),
                "country": org.get("country"),
                "retail_location_count": org.get("retail_location_count"),
                # Descriptions
                "seo_description": org.get("seo_description"),
                "short_description": org.get("short_description"),
                # Organization structure
                "suborganizations": org.get("suborganizations", []),
                "num_suborganizations": org.get("num_suborganizations"),
                "owned_by_organization_id": org.get("owned_by_organization_id"),
                "org_chart_root_people_ids": org.get("org_chart_root_people_ids", []),
                "org_chart_sector": org.get("org_chart_sector"),
                "org_chart_removed": org.get("org_chart_removed"),
                "org_chart_show_department_filter": org.get(
                    "org_chart_show_department_filter"
                ),
                # Revenue and funding
                "annual_revenue_printed": org.get("annual_revenue_printed"),
                "annual_revenue": org.get("annual_revenue"),
                "total_funding_printed": org.get("total_funding_printed"),
                "latest_funding_round_date": org.get("latest_funding_round_date"),
                "latest_funding_stage": org.get("latest_funding_stage"),
                "funding_events": org.get("funding_events", []),
                # Technology stack
                "technology_names": org.get("technology_names", []),
                "current_technologies": org.get("current_technologies", []),
                # Department headcount
                "departmental_head_count": org.get("departmental_head_count", {}),
                # Account information
                "account": org.get("account", {}),
                "account_id": org.get("account_id"),
                # Metadata
                "snippets_loaded": org.get("snippets_loaded"),
            })

        return apollo_fields

    def _extract_funding_from_source(
        self, source: str, data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract funding information from any data source."""
        funding_info = {}

        if source == "crunchbase_scraping":
            if data.get("total_funding"):
                funding_info["total"] = data["total_funding"]
            if data.get("funding_rounds"):
                funding_info["rounds"] = data["funding_rounds"]
            if data.get("last_funding_date"):
                funding_info["last_date"] = data["last_funding_date"]
            if data.get("last_funding_amount"):
                funding_info["last_amount"] = data["last_funding_amount"]
            if data.get("valuation"):
                funding_info["valuation"] = data["valuation"]
            if data.get("revenue"):
                funding_info["revenue"] = data["revenue"]
        elif source == "pitchbook_data":
            if data.get("latest_deal_amount"):
                funding_info["latest_amount"] = data["latest_deal_amount"]
            if data.get("latest_deal_amount_value"):
                # Handle both string and object formats
                latest_amount_value = data["latest_deal_amount_value"]
                if (
                    isinstance(latest_amount_value, dict)
                    and "value" in latest_amount_value
                ):
                    funding_info["latest_amount_value"] = latest_amount_value["value"]
                else:
                    funding_info["latest_amount_value"] = latest_amount_value
            if data.get("last_funding_amount"):
                funding_info["last_funding_amount"] = data["last_funding_amount"]
            if data.get("latest_deal_type"):
                funding_info["latest_type"] = data["latest_deal_type"]
                # Use latest_deal_type to determine funding_status
                if data.get("latest_deal_type"):
                    deal_type = data["latest_deal_type"].lower()
                    if "ipo" in deal_type or "public" in deal_type:
                        funding_info["funding_status"] = "public"
                    elif "acquisition" in deal_type or "exit" in deal_type:
                        funding_info["funding_status"] = "acquired"
                    elif "series" in deal_type or "round" in deal_type:
                        funding_info["funding_status"] = "funded"
                    elif "accelerator" in deal_type or "incubator" in deal_type:
                        funding_info["funding_status"] = "accelerator_incubator"
                    else:
                        funding_info["funding_status"] = "active"
            if data.get("financing_rounds"):
                funding_info["financing_rounds"] = data["financing_rounds"]
        elif source == "apollo_data":
            # Extract from organization data
            org = data.get("organization", {})
            if org.get("total_funding"):
                funding_info["total"] = org["total_funding"]
            if org.get("total_funding_printed"):
                funding_info["total_printed"] = org["total_funding_printed"]
            if org.get("latest_funding_stage"):
                funding_info["stage"] = org["latest_funding_stage"]
            if org.get("latest_funding_round_date"):
                funding_info["latest_date"] = org["latest_funding_round_date"]
            if org.get("annual_revenue"):
                funding_info["revenue"] = org["annual_revenue"]
            if org.get("annual_revenue_printed"):
                funding_info["revenue_printed"] = org["annual_revenue_printed"]
            if org.get("funding_events"):
                funding_info["funding_events"] = org["funding_events"]

        return funding_info

    def _calculate_confidence_score(
        self, merged_data: Dict[str, Any], data_sources: Dict[str, Any]
    ) -> float:
        """Calculate confidence score based on data quality."""
        confidence_score = 0.5  # Base confidence

        # Check for essential fields
        if merged_data.get("name"):
            confidence_score += 0.1
        if merged_data.get("description"):
            confidence_score += 0.1
        if merged_data.get("industry"):
            confidence_score += 0.1
        if merged_data.get("website"):
            confidence_score += 0.1
        if merged_data.get("email"):
            confidence_score += 0.1
        if merged_data.get("phone"):
            confidence_score += 0.1
        if merged_data.get("founded_year"):
            confidence_score += 0.1
        if merged_data.get("employee_count") or merged_data.get("employee_count_range"):
            confidence_score += 0.1
        if merged_data.get("hq_location"):
            confidence_score += 0.05
        if merged_data.get("linkedin_url"):
            confidence_score += 0.05
        if merged_data.get("funding_data"):
            confidence_score += 0.05

        return min(confidence_score, 1.0)

    def _extract_year_from_date(self, date_string: Optional[str]) -> Optional[int]:
        """Extract year from date string."""
        if not date_string:
            return None
        try:
            # Handle various date formats
            if "-" in date_string:
                return int(date_string.split("-")[0])
            elif "/" in date_string:
                return int(date_string.split("/")[-1])
            else:
                return int(date_string)
        except (ValueError, IndexError):
            return None


async def process_comprehensive_enrichment_data_task(
    payload: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Task function to process comprehensive enrichment data and populate database.

    Args:
        payload: Job payload containing enrichment data

    Returns:
        Processing result with status and details
    """
    start_time = datetime.now(timezone.utc)

    try:
        logger.info("Starting comprehensive enrichment data processing task")

        # Handle payload structure
        if "job_args" in payload:
            data = payload["job_args"]
        else:
            data = payload

        # Validate input
        if "enrichment_data" not in data:
            return {
                "success": False,
                "error": "Missing enrichment_data in payload",
                "processing_time": (
                    datetime.now(timezone.utc) - start_time
                ).total_seconds(),
            }

        enrichment_data = data["enrichment_data"]

        # Initialize RDS storage
        rds_storage = RDSStorage()
        await rds_storage.initialize()

        # Create processor and process data
        processor = ComprehensiveDataProcessor(rds_storage)
        result = await processor.process_comprehensive_enrichment_data(enrichment_data)

        await rds_storage.cleanup()

        processing_time = (datetime.now(timezone.utc) - start_time).total_seconds()

        if result["success"]:
            logger.info(
                "Comprehensive data processing completed successfully",
                company_domain=result.get("company_domain"),
                processing_time=processing_time,
            )
        else:
            logger.error(
                f"Comprehensive data processing failed: {result.get('error')}",
                processing_time=processing_time,
            )

        return {**result, "processing_time": processing_time}

    except Exception as e:
        error_msg = f"Comprehensive data processing task failed: {str(e)}"
        logger.error(error_msg, exc_info=True)

        return {
            "success": False,
            "error": error_msg,
            "processing_time": (
                datetime.now(timezone.utc) - start_time
            ).total_seconds(),
        }
