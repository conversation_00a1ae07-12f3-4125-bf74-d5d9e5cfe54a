"""
Founder enrichment pipeline for TractionX Data Pipeline Service.
"""

from datetime import datetime
from typing import Any, Dict, Optional

import httpx

from app.configs import settings
from app.models.base import EnrichmentSource, ProcessingResult
from app.models.founder import PDLFounderData
from app.pipelines.base import BasePipeline


class FounderEnrichmentPipeline(BasePipeline):
    """Pipeline for enriching founder data using PDL API."""

    def __init__(self):
        super().__init__("founder_enrichment")
        self.pdl_client: Optional[httpx.AsyncClient] = None

    async def _initialize_pipeline(self) -> None:
        """Initialize the founder enrichment pipeline."""
        # Initialize PDL API client if configured
        if settings.PDL_API_KEY:
            self.pdl_client = httpx.AsyncClient(
                base_url=settings.PDL_BASE_URL,
                headers={
                    "X-Api-Key": settings.PDL_API_KEY,
                    "Content-Type": "application/json",
                },
                timeout=30.0,
            )
            self.logger.info("PDL API client initialized")
        else:
            self.logger.warning(
                "PDL API key not configured, PDL enrichment will be skipped"
            )

    async def _cleanup_pipeline(self) -> None:
        """Clean up pipeline resources."""
        if self.pdl_client:
            await self.pdl_client.aclose()
            self.pdl_client = None

    def _get_required_fields(self) -> list[str]:
        """Get required input fields."""
        return [
            "founder_id",
            "company_id",
            "org_id",
            "founder_name",
            "founder_linkedin",
        ]

    async def _validate_input(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """Validate input data for founder enrichment."""
        # Check required fields
        validation_result = self._validate_required_fields(input_data)
        if not validation_result.success:
            return validation_result

        # Validate founder name
        # founder_name = input_data.get("founder_name", "").strip()
        # if not founder_name:
        #     return ProcessingResult.error_result(
        #         error_message="Founder name cannot be empty",
        #         error_type="ValidationError",
        #     )

        # Validate LinkedIn URL
        # linkedin_url = input_data.get("founder_linkedin", "").strip()
        # if not linkedin_url:
        #     return ProcessingResult.error_result(
        #         error_message="LinkedIn URL cannot be empty",
        #         error_type="ValidationError",
        #     )

        # if not linkedin_url.startswith("https://linkedin.com/"):
        #     return ProcessingResult.error_result(
        #         error_message="Invalid LinkedIn URL format",
        #         error_type="ValidationError",
        #     )

        return ProcessingResult.success_result({})

    async def _process_data(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """Process founder data through PDL enrichment."""
        self.logger.info("Processing founder data", input_data=input_data)

        founder_id = input_data["founder_id"]
        company_id = input_data["company_id"]
        org_id = input_data["org_id"]
        founder_name = input_data["founder_name"]
        linkedin_url = input_data["founder_linkedin"]

        self.logger.info(
            "Starting founder enrichment",
            founder_id=founder_id,
            company_id=company_id,
            founder_name=founder_name,
            linkedin_url=linkedin_url,
        )

        enrichment_data = {}

        # PDL enrichment
        if settings.ENABLE_FOUNDER_ENRICHMENT and self.pdl_client:
            try:
                pdl_data = await self._enrich_with_pdl(founder_name, linkedin_url)
                if pdl_data:
                    enrichment_data["pdl_data"] = pdl_data
                    self.logger.info("PDL enrichment completed successfully")
                else:
                    self.logger.warning("No data returned from PDL enrichment")
            except Exception as e:
                self.logger.error(f"PDL enrichment failed: {e}")
                # Continue with other enrichments even if PDL fails

        # Store raw enrichment data
        if enrichment_data:
            self._store_raw_data(
                enrichment_data,
                f"pdl_enrichment/founders/{founder_id}.json",
            )

        # Calculate confidence score
        confidence_score = self._calculate_confidence_score(enrichment_data)

        return ProcessingResult.success_result(
            data=enrichment_data,
            metadata={
                "founder_id": founder_id,
                "company_id": company_id,
                "org_id": org_id,
                "founder_name": founder_name,
                "confidence_score": confidence_score,
                "enrichment_sources": list(enrichment_data.keys()),
            },
            source=EnrichmentSource.PDL,
        )

    async def _enrich_with_pdl(
        self, founder_name: str, linkedin_url: str
    ) -> Optional[PDLFounderData]:
        """Enrich founder data using PDL API."""
        if not self.pdl_client:
            self.logger.warning("PDL client not initialized")
            return None

        try:
            # Prepare API parameters based on actual PDL API
            params = {
                "profile": linkedin_url,
                "pretty": "false",
                "min_likelihood": "2",
                "include_if_matched": "false",
                "titlecase": "false",
            }

            # Make API call to PDL
            response = await self.pdl_client.get("/v5/person/enrich", params=params)

            response.raise_for_status()
            pdl_response = response.json()
            self.logger.info("PDL response", pdl_response=pdl_response or "No response")

            # Extract person data from PDL response
            if pdl_response.get("status") == 200 and pdl_response.get("data"):
                person_data = pdl_response["data"]

                # Convert to PDLFounderData model with actual PDL fields
                # Handle PDL's boolean values that should be strings
                def safe_str_field(value):
                    """Convert PDL field to string, handling boolean values."""
                    if isinstance(value, bool):
                        return (
                            None  # Convert boolean to None for optional string fields
                        )
                    return value if isinstance(value, str) else None

                pdl_data = PDLFounderData(
                    pdl_id=person_data.get("id"),
                    first_name=safe_str_field(person_data.get("first_name")),
                    last_name=safe_str_field(person_data.get("last_name")),
                    full_name=safe_str_field(person_data.get("full_name")),
                    middle_name=safe_str_field(person_data.get("middle_name")),
                    # Extract emails from the response structure
                    emails=self._extract_emails_from_pdl(person_data),
                    # Extract phone numbers from the response structure
                    phone_numbers=self._extract_phones_from_pdl(person_data),
                    location_name=safe_str_field(person_data.get("location_name")),
                    location_country=safe_str_field(
                        person_data.get("location_country")
                    ),
                    location_region=safe_str_field(person_data.get("location_region")),
                    location_locality=safe_str_field(
                        person_data.get("location_locality")
                    ),
                    job_title=safe_str_field(person_data.get("job_title")),
                    job_company_name=safe_str_field(
                        person_data.get("job_company_name")
                    ),
                    job_start_date=self._parse_pdl_date(
                        person_data.get("job_start_date")
                    ),
                    experience=self._extract_experience_from_pdl(person_data),
                    education=self._extract_education_from_pdl(person_data),
                    linkedin_url=safe_str_field(person_data.get("linkedin_url")),
                    twitter_url=safe_str_field(person_data.get("twitter_url")),
                    github_url=safe_str_field(person_data.get("github_url")),
                    facebook_url=safe_str_field(person_data.get("facebook_url")),
                    skills=person_data.get("skills", [])
                    if isinstance(person_data.get("skills"), list)
                    else [],
                    interests=person_data.get("interests", [])
                    if isinstance(person_data.get("interests"), list)
                    else [],
                    pdl_metadata={
                        "api_params": params,
                        "api_response": pdl_response,
                        "likelihood": pdl_response.get("likelihood"),
                        "dataset_version": person_data.get("dataset_version"),
                    },
                )

                return pdl_data

            return None

        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                self.logger.error(f"Founder not found in PDL: {founder_name}, {e}")
                return None
            elif e.response.status_code == 429:
                self.logger.warning("PDL API rate limit exceeded")
                raise Exception("PDL API rate limit exceeded")
            else:
                self.logger.error(
                    f"PDL API error: {e.response.status_code} - {e.response.text}"
                )
                raise
        except Exception as e:
            self.logger.error(f"PDL enrichment error: {e}")
            raise

    def _extract_emails_from_pdl(self, person_data: dict) -> list[str]:
        """Extract email addresses from PDL response."""
        emails = []

        # PDL doesn't return actual emails in the enrich endpoint for privacy
        # They only return boolean flags indicating if emails exist
        if person_data.get("work_email"):
            emails.append("work_email_available")
        if person_data.get("personal_emails"):
            emails.append("personal_email_available")
        if person_data.get("recommended_personal_email"):
            emails.append("recommended_personal_email_available")

        return emails

    def _extract_phones_from_pdl(self, person_data: dict) -> list[str]:
        """Extract phone numbers from PDL response."""
        phones = []

        # PDL doesn't return actual phone numbers in the enrich endpoint for privacy
        # They only return boolean flags indicating if phone numbers exist
        if person_data.get("phone_numbers"):
            phones.append("phone_numbers_available")
        if person_data.get("mobile_phone"):
            phones.append("mobile_phone_available")

        return phones

    def _extract_experience_from_pdl(self, person_data: dict) -> list[dict]:
        """Extract work experience from PDL response."""
        experience = person_data.get("experience", [])

        # Transform PDL experience format to our expected format
        transformed_experience = []
        for exp in experience:
            # Handle cases where company or title might be None
            company_data = exp.get("company") or {}
            title_data = exp.get("title") or {}

            transformed_exp = {
                "company": {
                    "name": company_data.get("name"),
                    "size": company_data.get("size"),
                    "industry": company_data.get("industry"),
                    "founded": company_data.get("founded"),
                    "website": company_data.get("website"),
                    "linkedin_url": company_data.get("linkedin_url"),
                },
                "title": title_data.get("name"),
                "start_date": exp.get("start_date"),
                "end_date": exp.get("end_date"),
                "is_primary": exp.get("is_primary", False),
                "location": company_data.get("location", {}),
            }
            transformed_experience.append(transformed_exp)

        return transformed_experience

    def _extract_education_from_pdl(self, person_data: dict) -> list[dict]:
        """Extract education from PDL response."""
        education = person_data.get("education", [])

        # Transform PDL education format to our expected format
        transformed_education = []
        for edu in education:
            # Handle case where school is None
            school_data = edu.get("school") or {}

            transformed_edu = {
                "school": {
                    "name": school_data.get("name"),
                    "type": school_data.get("type"),
                    "website": school_data.get("website"),
                    "linkedin_url": school_data.get("linkedin_url"),
                },
                "degrees": edu.get("degrees", []),
                "majors": edu.get("majors", []),
                "minors": edu.get("minors", []),
                "start_date": edu.get("start_date"),
                "end_date": edu.get("end_date"),
                "gpa": edu.get("gpa"),
            }
            transformed_education.append(transformed_edu)

        return transformed_education

    def _parse_pdl_date(self, date_str: str) -> Optional[datetime]:
        """Parse PDL date format (YYYY-MM or YYYY-MM-DD)."""
        if not date_str:
            return None

        try:
            # Handle YYYY-MM format
            if len(date_str) == 7 and date_str[4] == "-":
                return datetime.strptime(date_str + "-01", "%Y-%m-%d")
            # Handle YYYY-MM-DD format
            elif len(date_str) == 10:
                return datetime.strptime(date_str, "%Y-%m-%d")
            # Handle YYYY format
            elif len(date_str) == 4:
                return datetime.strptime(date_str + "-01-01", "%Y-%m-%d")
            else:
                return None
        except ValueError:
            return None
