# Queue System

A robust, Redis-based queue system for asynchronous job processing.

## Features

- Multiple queue types (high priority, normal, background)
- Job scheduling with delay
- Automatic retries with exponential backoff
- Dead letter queue for failed jobs
- Comprehensive monitoring and statistics
- Extensible worker system

## Usage

### Enqueuing Jobs

```python
from app.services.factory import get_queue_service
from app.services.queue.interfaces import QueueType, JobPriority

async def enqueue_example():
    queue_service = await get_queue_service()
    
    # Simple job
    job = await queue_service.enqueue_job(
        job_type="send_email_notification",
        payload={
            "recipient": "<EMAIL>",
            "subject": "Hello",
            "body": "This is a test email"
        }
    )
    
    # Job with options
    job = await queue_service.enqueue_job(
        job_type="process_form_submission",
        payload={
            "form_id": "123",
            "submission_id": "456",
            "answers": {"question1": "answer1"}
        },
        queue_type=QueueType.HIGH_PRIORITY,
        priority=JobPriority.HIGH,
        retry_config={
            "max_attempts": 5,
            "backoff_factor": 2
        }
    )
    
    # Scheduled job
    job = await queue_service.schedule_job(
        job_type="generate_report",
        payload={
            "report_id": "789",
            "user_id": "user123"
        },
        scheduled_time=datetime.now() + timedelta(hours=1)
    )
    
    return job
```

### Processing Jobs

Jobs are automatically processed by the worker process. To create a new job handler:

1. Create a new file in `app/services/queue/handlers/` directory
2. Define your handler functions
3. Export them in a `HANDLERS` dictionary
4. Import the handlers in `app/services/queue/handlers/__init__.py`

Example handler:

```python
async def process_form_submission(payload):
    form_id = payload.get("form_id")
    submission_id = payload.get("submission_id")
    
    # Process the form submission
    # ...
    
    return {
        "success": True,
        "form_id": form_id,
        "submission_id": submission_id
    }

# Export handlers
HANDLERS = {
    "process_form_submission": process_form_submission
}
```

## Running the Worker

In development:
```bash
make start-worker
```

In Docker:
```bash
make docker-up
```

## Monitoring

You can monitor the queue using the API endpoints:

- GET `/api/v1/queue/stats` - Get queue statistics
- GET `/api/v1/queue/jobs` - List jobs in a queue

## Architecture

The queue system consists of:

1. **Queue Service** - Interface for enqueueing and managing jobs
2. **Worker** - Process that executes jobs from the queue
3. **Handlers** - Functions that process specific job types
4. **Redis Backend** - Storage for queue data

Jobs flow through the system as follows:

1. Application code enqueues a job
2. Job is stored in Redis
3. Worker picks up the job
4. Worker executes the appropriate handler
5. Result is stored and job is marked as completed

Failed jobs are retried according to their retry configuration, and eventually moved to the dead letter queue if they continue to fail.

## Queue Workflow

A high-level step-by-step overview of how jobs are processed through the queue system:

1. **Enqueueing Jobs**
   - Application code obtains a `QueueService` instance via the factory:
     ```python
     from app.services.factory import get_queue_service
     queue_service = await get_queue_service()
     ```
   - Jobs are added using `enqueue_job` (immediate) or `schedule_job` (with a specific run time).

2. **Redis Storage**
   - The `RedisQueueBackend` serializes each `Job` object and stores it in Redis data structures (lists or sorted sets), based on the queue type.

3. **Worker Initialization**
   - A `QueueWorker` (or a pool via `WorkerPool`) is created using `QueueFactory`.
   - Handlers are registered through the `handlers` registry or via `worker.register_module()`.
   - Calling `start()` on the worker spins up asynchronous loops for:
     - **Main Loop**: fetching and processing jobs
     - **Scheduled Jobs**: promoting due scheduled jobs
     - **Stalled Jobs**: requeuing jobs that exceeded the processing timeout

4. **Job Dequeuing & Processing**
   - In the main loop (`_worker_loop`), the worker calls `backend.dequeue()` to fetch the next available job.
   - Once a job is retrieved, the worker looks up the corresponding handler from the registered `HANDLERS`.
   - The job is processed by invoking the handler:
     - If the handler implements `JobHandlerInterface`, its `handle(job)` method is used.
     - If the handler is an async function, it's awaited directly with `payload`.
     - Otherwise, a sync function is executed in an executor.

5. **Completion & Failure Handling**
   - On successful execution, `backend.complete(job.id, result)` is called to mark the job as completed and store the result.
   - On failure, `backend.fail(job.id, error)` is called:
     - If retries remain (based on retry configuration), the job is rescheduled with exponential backoff.
     - Otherwise, the job is moved to the dead letter queue for manual inspection.

6. **Scheduled & Stalled Jobs**
   - The worker periodically runs `backend.process_scheduled_jobs()` to promote jobs whose scheduled time has arrived.
   - It also runs `backend.process_stalled_jobs()` to requeue or fail jobs that have been processing too long.

7. **Dead Letter Queue**
   - Jobs that exhaust their retry attempts are stored in a separate dead letter queue, allowing for manual review or reprocessing.

---

## CRUD Operations on Jobs

The `QueueService` provides high-level methods for common CRUD operations on jobs:

### Creating Jobs

- `enqueue_job(job_type, payload, ...)`: Enqueue a new job immediately.
- `schedule_job(job_type, payload, scheduled_time, ...)`: Schedule a job to run at a specific time.

```python
# Enqueue example
enqueued_job = await queue_service.enqueue_job('send_email', {'to': '<EMAIL>'})
# Schedule example
scheduled_job = await queue_service.schedule_job(
    'generate_report',
    {'report_id': '123'},
    datetime.now() + timedelta(hours=1)
)
```

### Reading Jobs

- `get_job(job_id)`: Retrieve job details by ID.
- `list_jobs(queue_type, status=None, job_type=None, limit=100, offset=0)`: List jobs with optional filters.
- `get_queue_stats(queue_type=None)`: Get statistics (counts of pending, processing, and failed jobs).

```python
job = await queue_service.get_job('job-uuid')
jobs = await queue_service.list_jobs(status=JobStatus.FAILED)
stats = await queue_service.get_queue_stats()
```

### Updating Jobs

- `cancel_job(job_id)`: Cancel a pending or scheduled job.
- `retry_job(job_id, delay_seconds)`: Retry a failed job with an optional delay.
- `process_scheduled_jobs()`: Manually trigger promotion of scheduled jobs.
- `process_stalled_jobs()`: Manually requeue stalled jobs.

```python
await queue_service.cancel_job('job-uuid')
await queue_service.retry_job('job-uuid', delay_seconds=30)
```

### Deleting Jobs

- `purge_queue(queue_type, older_than=None)`: Remove all jobs in a queue, optionally older than a timestamp.

```python
removed_count = await queue_service.purge_queue(
    QueueType.DEFAULT,
    older_than=datetime.now() - timedelta(days=1)
)
```

---

## Adding New Handlers

To extend the queue with custom job handlers:

1. **Create a new module** in `app/services/queue/handlers`, e.g. `my_custom.py`.
2. **Define** an async function or implement `JobHandlerInterface`:

```python
# app/services/queue/handlers/my_custom.py
from typing import Dict, Any

async def handle_my_job(payload: Dict[str, Any]) -> Dict[str, Any]:
    """Process custom job type."""
    # Your business logic here
    return {'success': True}

# Register handlers in this module
HANDLERS = {
    'my_job_type': handle_my_job
}
```

3. **Import and merge** your handlers in `app/services/queue/handlers/__init__.py`:

```python
from app.services.queue.handlers.my_custom import HANDLERS as CUSTOM_HANDLERS
HANDLERS.update(CUSTOM_HANDLERS)
```

4. **Restart** your worker(s) to pick up the new handlers.

*(Optional)* Instead of updating `__init__.py`, you can dynamically load handlers at runtime:

```python
worker = QueueFactory.create_worker(queue_service)
worker.register_module('app.services.queue.handlers.my_custom')
worker.start()
```

Now, any enqueued job with `type='my_job_type'` will be routed to your `handle_my_job` function.
