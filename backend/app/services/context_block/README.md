# Context Block Service

## Overview
The ContextBlockService generates and stores rich AI-ready context blocks for deals in S3. It implements PRD 2: Context Block Generation and S3 Storage.

## S3Storage Integration

### Methods Used
The service uses the following S3Storage methods from `app.utils.aws.s3`:

- **`put_object(key, data, metadata)`** - Stores context blocks in S3
- **`get_object(key)`** - Retrieves context blocks from S3

### Key Features
- All S3 operations are centralized through `app.utils.aws.s3.S3Storage`
- No direct boto3 usage in the service
- Proper error handling and logging
- JSON serialization/deserialization handled by S3Storage

## Service Methods

### Core Methods
- `generate_context_block(deal, submission, form)` - Generate comprehensive context block
- `store_context_block(deal_id, context_block)` - Store context block in S3
- `get_context_block(deal_id)` - Retrieve context block from S3
- `update_context_block(deal, submission)` - Update existing context block

### Helper Methods
- `_build_form_context(form, submission)` - Build form context section
- `_extract_founders_data(submission, form)` - Extract founders data
- `_build_ai_metadata(deal)` - Build AI metadata section
- `_clean_none_values(data)` - Clean None values from data structure

## Usage

```python
from app.services.factory import get_context_block_service

# Get service instance
context_block_service = await get_context_block_service(db)

# Generate and store context block
context_block = await context_block_service.generate_context_block(deal)
s3_url = await context_block_service.store_context_block(deal_id, context_block)

# Retrieve context block
context = await context_block_service.get_context_block(deal_id)
```

## S3 Storage Path
Context blocks are stored at: `s3://{bucket}/deals/{deal_id}/context.json`

## Dependencies
- `app.utils.aws.s3.S3Storage` - S3 storage operations
- `app.models.deal.Deal` - Deal model
- `app.models.form.Form, Submission` - Form models
- `app.services.factory.get_form_service` - Form service (circular import handled) 