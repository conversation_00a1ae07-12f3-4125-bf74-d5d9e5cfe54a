# Barrier-Orchestrated Enrichment & Processing Framework

## Overview

The Barrier-Orchestrated Enrichment & Processing Framework enables parallel, dynamically expanding, and dependency-aware orchestration of enrichment tasks. It provides Redis + MongoDB coordination for managing complex enrichment workflows where tasks can register new child tasks on the fly, and final compilation/merge only runs when all tasks (including dynamic children) are completed.

## Key Features

- **Parallel Processing**: Multiple enrichment tasks run concurrently
- **Dynamic Task Registration**: Tasks can spawn new child tasks during execution
- **Dependency Awareness**: Final merge only triggers when all tasks complete
- **Progress Tracking**: Real-time visibility into task progress via Redis + MongoDB
- **Backward Compatibility**: No breaking changes to existing APIs or job submission
- **Fault Tolerance**: Handles task failures gracefully without blocking the entire workflow

## Architecture

### Data Flow

```mermaid
sequenceDiagram
    participant API
    participant QueueService
    participant Worker
    participant BarrierSync
    participant Redis
    participant MongoDB

    API->>QueueService: enqueue(task, payload, meta)
    QueueService->>BarrierSync: register_barrier()
    BarrierSync->>Redis: SADD expected_tasks
    BarrierSync->>MongoDB: create barrier document
    QueueService->>Worker: enqueue job

    Worker->>Worker: execute(task)
    Worker->>BarrierSync: register_dynamic_tasks()
    BarrierSync->>Redis: SADD new_tasks
    Worker->>BarrierSync: mark_task_complete()
    BarrierSync->>Redis: SADD completed_tasks
    BarrierSync->>MongoDB: update task logs
    BarrierSync->>Redis: check if all complete
    Redis-->>BarrierSync: all done ✅
    BarrierSync->>QueueService: trigger merge job
    BarrierSync->>MongoDB: set status=done
```

### Redis Data Structures

| Key | Type | Purpose |
|-----|------|---------|
| `barrier:{group_id}:expected` | Set | All registered tasks |
| `barrier:{group_id}:completed` | Set | All completed tasks |
| `barrier:{group_id}:lock` | String | Prevents double-merge (NX lock) |

### MongoDB Schema

```json
{
  "_id": "company:uuid",
  "entity_type": "company",
  "entity_id": "uuid",
  "expected_tasks": ["linkedin", "cb", "pitchdeck", "sitemap", "summarizer"],
  "completed_tasks": ["linkedin", "cb"],
  "status": "in_progress",
  "task_logs": [
    {
      "task": "linkedin",
      "status": "success",
      "started_at": "2025-01-17T06:22:00Z",
      "finished_at": "2025-01-17T06:23:21Z",
      "metadata": {...}
    }
  ],
  "created_at": "2025-01-17T06:21:00Z",
  "updated_at": "2025-01-17T06:23:21Z",
  "merge_triggered": false,
  "merge_job_id": null
}
```

## Usage

### Basic Barrier Job

```python
from app.queueing.factory import create_queue_service

# Initialize queue service
queue_service = await create_queue_service()

# Enqueue barrier-aware job
job_id = await queue_service.enqueue_job(
    job_func="enrich_linkedin",
    job_args={"company_id": "abc123"},
    meta={
        "barrier_group_id": "company:abc123",
        "task_name": "linkedin",
        "entity_type": "company",
        "entity_id": "abc123"
    }
)
```

### Dynamic Task Registration

During task execution, you can register new tasks:

```python
from app.tasks.barrier_tasks import register_dynamic_tasks

async def enrich_website(job_args):
    # ... website analysis logic ...
    
    # Discover new tasks to run
    new_tasks = ["summarizer", "contact_extractor"]
    task_payloads = {
        "summarizer": {"content": website_content},
        "contact_extractor": {"html": website_html}
    }
    
    # Register and enqueue new tasks
    await register_dynamic_tasks(
        barrier_group_id="company:abc123",
        new_tasks=new_tasks,
        task_payloads=task_payloads,
        queue_service=queue_service
    )
    
    return {"website_data": {...}}
```

### Barrier-Aware Task Decorator

```python
from app.tasks.barrier_tasks import barrier_aware_task

@barrier_aware_task(
    barrier_group_id="company:abc123",
    task_name="linkedin",
    entity_type="company",
    entity_id="abc123"
)
async def enrich_linkedin(job_args):
    # Your existing LinkedIn enrichment logic
    company_id = job_args.get("company_id")
    
    # ... enrichment logic ...
    
    return {"linkedin_data": {...}}
```

## API Endpoints

### List Barriers

```bash
GET /api/v1/barriers/?entity_type=company&status=in_progress
```

Response:
```json
{
  "barriers": [
    {
      "barrier_id": "company:abc123",
      "entity_type": "company",
      "entity_id": "abc123",
      "status": "in_progress",
      "expected_tasks": ["linkedin", "cb", "website"],
      "completed_tasks": ["linkedin"],
      "progress_percentage": 33.33,
      "created_at": "2025-01-17T06:21:00Z",
      "updated_at": "2025-01-17T06:23:21Z",
      "task_logs": [...]
    }
  ],
  "total_count": 1,
  "page": 1,
  "page_size": 20
}
```

### Get Barrier Status

```bash
GET /api/v1/barriers/company:abc123
```

### Create Barrier

```bash
POST /api/v1/barriers/
{
  "barrier_group_id": "company:abc123",
  "entity_type": "company",
  "entity_id": "abc123",
  "initial_tasks": ["linkedin", "crunchbase"]
}
```

### Register Additional Tasks

```bash
POST /api/v1/barriers/company:abc123/tasks
{
  "new_tasks": ["website", "sitemap"]
}
```

### Health Check

```bash
GET /api/v1/barriers/health/status
```

## Configuration

### Environment Variables

Add to your `.env` file:

```bash
# MongoDB Configuration
MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_DB=tractionx_datapipelines
MONGO_USER=your_user
MONGO_PASSWORD=your_password

# Redis Configuration (existing)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_password
```

### Settings

The framework automatically uses the existing Redis configuration and adds MongoDB settings:

```python
# app/configs/settings.py
class Settings(BaseSettings):
    # ... existing settings ...
    
    # MongoDB (for barrier tracking)
    MONGO_HOST: str = "localhost"
    MONGO_PORT: int = 27017
    MONGO_DB: str = "tractionx_datapipelines"
    MONGO_USER: Optional[str] = None
    MONGO_PASSWORD: Optional[str] = None
    
    @property
    def mongo_connection_string(self) -> str:
        if self.MONGO_USER and self.MONGO_PASSWORD:
            return f"mongodb://{self.MONGO_USER}:{self.MONGO_PASSWORD}@{self.MONGO_HOST}:{self.MONGO_PORT}/{self.MONGO_DB}"
        else:
            return f"mongodb://{self.MONGO_HOST}:{self.MONGO_PORT}/{self.MONGO_DB}"
```

## Integration Examples

### Company Enrichment Pipeline

```python
async def enrich_company_pipeline(company_id: str):
    """Complete company enrichment with barrier orchestration."""
    
    # Initial tasks
    initial_tasks = ["linkedin", "crunchbase", "website"]
    
    # Enqueue initial tasks
    for task in initial_tasks:
        await queue_service.enqueue_job(
            job_func=f"enrich_{task}",
            job_args={"company_id": company_id},
            meta={
                "barrier_group_id": f"company:{company_id}",
                "task_name": task,
                "entity_type": "company",
                "entity_id": company_id
            }
        )
    
    # The merge job will automatically trigger when all tasks complete
    return {"pipeline_id": f"company:{company_id}"}
```

### Website Analysis with Dynamic Tasks

```python
async def enrich_website_dynamic(job_args):
    """Website enrichment that spawns additional tasks."""
    company_id = job_args.get("company_id")
    website_url = job_args.get("website_url")
    
    # Analyze website
    website_data = await analyze_website(website_url)
    
    # Discover additional tasks based on website content
    additional_tasks = []
    task_payloads = {}
    
    if website_data.get("has_blog"):
        additional_tasks.append("blog_analyzer")
        task_payloads["blog_analyzer"] = {"blog_url": website_data["blog_url"]}
    
    if website_data.get("has_contact_form"):
        additional_tasks.append("contact_extractor")
        task_payloads["contact_extractor"] = {"html": website_data["html"]}
    
    # Register dynamic tasks
    if additional_tasks:
        await register_dynamic_tasks(
            barrier_group_id=f"company:{company_id}",
            new_tasks=additional_tasks,
            task_payloads=task_payloads,
            queue_service=queue_service
        )
    
    return website_data
```

## Monitoring and Observability

### Real-time Progress Tracking

Monitor barrier progress via the API:

```python
import asyncio
import time

async def monitor_barrier_progress(barrier_id: str):
    """Monitor barrier progress in real-time."""
    while True:
        response = await api_client.get(f"/api/v1/barriers/{barrier_id}")
        barrier = response.json()
        
        print(f"Progress: {barrier['progress_percentage']:.1f}%")
        print(f"Completed: {len(barrier['completed_tasks'])}/{len(barrier['expected_tasks'])}")
        
        if barrier['status'] in ['done', 'error']:
            break
            
        await asyncio.sleep(5)
```

### Health Monitoring

```python
async def check_barrier_health():
    """Check barrier system health."""
    response = await api_client.get("/api/v1/barriers/health/status")
    health = response.json()
    
    print(f"Status: {health['status']}")
    print(f"Redis: {health['redis']}")
    print(f"MongoDB: {health['mongodb']}")
    print(f"Total barriers: {health['stats']['total_barriers']}")
```

## Error Handling

### Task Failures

When a task fails, it's logged but doesn't block the barrier:

```python
# Task failure is logged in MongoDB
{
  "task": "linkedin",
  "status": "error",
  "error_message": "API rate limit exceeded",
  "started_at": "2025-01-17T06:22:00Z",
  "finished_at": "2025-01-17T06:22:05Z"
}
```

### Barrier Timeout

Barriers can be configured with timeouts:

```python
# In your merge function
async def merge_enrichment_results(job_args):
    barrier_id = job_args.get("barrier_group_id")
    
    # Check for timeout (e.g., 30 minutes)
    barrier = await barrier_sync.get_barrier_status(barrier_id)
    if barrier and (time.time() - barrier.created_at.timestamp()) > 1800:
        # Mark as timed out
        await barrier_sync.update_status(barrier_id, "timed_out")
        return {"status": "timed_out"}
```

## Best Practices

### 1. Task Naming

Use consistent, descriptive task names:

```python
# Good
"linkedin_company_profile"
"crunchbase_funding_data"
"website_technology_stack"

# Avoid
"task1"
"enrichment"
"data"
```

### 2. Entity IDs

Use consistent entity ID formats:

```python
# Good
"company:abc123"
"founder:def456"
"deal:ghi789"

# Avoid
"abc123"
"company_abc123"
```

### 3. Error Handling

Always handle errors gracefully:

```python
async def robust_task(job_args):
    try:
        result = await perform_enrichment(job_args)
        return result
    except RateLimitError:
        # Log and retry later
        logger.warning("Rate limit hit, will retry")
        raise
    except Exception as e:
        # Log error for debugging
        logger.error(f"Task failed: {e}")
        raise
```

### 4. Resource Management

Clean up resources properly:

```python
async def task_with_resources(job_args):
    barrier_sync = None
    try:
        barrier_sync = BarrierSync(...)
        await barrier_sync.initialize()
        
        # ... task logic ...
        
    finally:
        if barrier_sync:
            await barrier_sync.cleanup()
```

## Migration Guide

### From Sequential Processing

**Before:**
```python
# Sequential processing
linkedin_data = await enrich_linkedin(company_id)
crunchbase_data = await enrich_crunchbase(company_id)
website_data = await enrich_website(company_id)

# Merge results
merged_data = merge_results([linkedin_data, crunchbase_data, website_data])
```

**After:**
```python
# Parallel processing with barriers
barrier_id = f"company:{company_id}"

# Enqueue all tasks
for task in ["linkedin", "crunchbase", "website"]:
    await queue_service.enqueue_job(
        job_func=f"enrich_{task}",
        job_args={"company_id": company_id},
        meta={
            "barrier_group_id": barrier_id,
            "task_name": task,
            "entity_type": "company",
            "entity_id": company_id
        }
    )

# Merge job will trigger automatically when all complete
```

### From Manual Coordination

**Before:**
```python
# Manual coordination
pending_tasks = set(["linkedin", "crunchbase", "website"])
completed_tasks = set()
results = {}

async def task_complete(task_name, result):
    completed_tasks.add(task_name)
    results[task_name] = result
    
    if completed_tasks == pending_tasks:
        await merge_results(results)
```

**After:**
```python
# Automatic coordination with barriers
@barrier_aware_task(
    barrier_group_id=f"company:{company_id}",
    task_name="linkedin"
)
async def enrich_linkedin(job_args):
    result = await perform_linkedin_enrichment(job_args)
    return result  # Barrier handles completion tracking
```

## Troubleshooting

### Common Issues

1. **Barrier not triggering merge**
   - Check if all tasks are marked as completed
   - Verify Redis connection and LUA scripts
   - Check MongoDB for task logs

2. **Tasks not being registered**
   - Verify barrier exists before registering tasks
   - Check Redis connection
   - Ensure task names are unique

3. **Performance issues**
   - Monitor Redis memory usage
   - Check MongoDB indexes
   - Review task execution times

### Debug Commands

```bash
# Check Redis barrier keys
redis-cli KEYS "tx_barrier:*"

# Check MongoDB barriers
mongo tractionx_datapipelines --eval "db.barriers.find().pretty()"

# Check barrier health
curl http://localhost:8001/api/v1/barriers/health/status
```

## Future Enhancements

1. **Multi-entity Barriers**: Coordinate barriers across companies and founders
2. **Conditional Tasks**: Tasks that only run based on conditions
3. **Retry Logic**: Automatic retry of failed tasks
4. **Priority Queues**: Different priority levels for tasks
5. **Resource Limits**: Limit concurrent tasks per barrier
6. **Webhooks**: Notify external systems of barrier completion
7. **Metrics**: Prometheus metrics for monitoring
8. **UI Dashboard**: Visual barrier progress tracking 