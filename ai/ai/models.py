"""
Pydantic models for TractionX AI Agent system.
"""

import logging
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, validator
from bson import ObjectId
from datetime import datetime
from enum import Enum

# Configure module logger
logger = logging.getLogger(__name__)


class PyObjectId(ObjectId):
    """Custom ObjectId type for Pydantic."""

    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v, info=None):
        if not ObjectId.is_valid(v):
            logger.error(f"Invalid ObjectId: {v}")
            raise ValueError("Invalid ObjectId")
        logger.debug(f"Validated ObjectId: {v}")
        return ObjectId(v)

    @classmethod
    def __get_pydantic_json_schema__(cls, field_schema):
        field_schema.update(type="string")


class AgentType(str, Enum):
    """Agent type enumeration."""
    PROPRIETARY = "proprietary"
    CUSTOM = "custom"
    SYSTEM = "system"


class AgentVisibility(str, Enum):
    """Agent visibility enumeration."""
    PRIVATE = "private"  # Only creator can see/use
    ORG = "org"         # All org members can see/use
    PUBLIC = "public"   # All users can see/use (for proprietary agents)


class AgentStatus(str, Enum):
    """Agent status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DRAFT = "draft"
    ARCHIVED = "archived"


class Agent(BaseModel):
    """AI Agent model."""

    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    org_id: PyObjectId = Field(..., description="Organization ID")
    user_id: Optional[PyObjectId] = Field(None, description="Creator user ID (null for system agents)")

    # Agent metadata
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    type: AgentType = Field(default=AgentType.CUSTOM)
    visibility: AgentVisibility = Field(default=AgentVisibility.PRIVATE)
    status: AgentStatus = Field(default=AgentStatus.ACTIVE)

    # Agent configuration
    config: Dict[str, Any] = Field(default_factory=dict, description="Agent configuration")
    tools: List[str] = Field(default_factory=list, description="Available tools")
    model: str = Field(default="gpt-4", description="LLM model to use")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    max_tokens: int = Field(default=4000, gt=0)
    max_iterations: int = Field(default=10, gt=0)

    # System prompts
    system_prompt: Optional[str] = Field(None, description="System prompt")
    instructions: Optional[str] = Field(None, description="Agent instructions")

    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_used: Optional[datetime] = None
    usage_count: int = Field(default=0, ge=0)

    # Tags and categorization
    tags: List[str] = Field(default_factory=list)
    category: Optional[str] = None

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}
        schema_extra = {
            "example": {
                "name": "Deal Analyzer",
                "description": "Analyzes investment deals for key metrics",
                "type": "custom",
                "visibility": "org",
                "config": {
                    "focus_areas": ["market_size", "team", "traction"]
                },
                "tools": ["thesis_matcher", "market_analyzer"],
                "system_prompt": "You are an expert investment analyst..."
            }
        }


class AgentLogLevel(str, Enum):
    """Log level enumeration."""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"


class AgentLog(BaseModel):
    """Agent execution log model."""

    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    agent_id: PyObjectId = Field(..., description="Agent ID")
    org_id: PyObjectId = Field(..., description="Organization ID")
    user_id: PyObjectId = Field(..., description="User ID who triggered the agent")

    # Execution context
    session_id: Optional[str] = Field(None, description="Session ID for grouping related logs")
    deal_id: Optional[PyObjectId] = Field(None, description="Deal ID if applicable")

    # Log data
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    level: AgentLogLevel = Field(default=AgentLogLevel.INFO)
    message: str = Field(..., description="Log message")

    # Input/Output
    input_data: Optional[Dict[str, Any]] = Field(None, description="Agent input")
    output_data: Optional[Dict[str, Any]] = Field(None, description="Agent output")

    # Execution details
    tools_used: List[str] = Field(default_factory=list, description="Tools used during execution")
    execution_time: Optional[float] = Field(None, description="Execution time in seconds")
    token_usage: Optional[Dict[str, int]] = Field(None, description="Token usage statistics")

    # Error handling
    error: Optional[str] = Field(None, description="Error message if execution failed")
    stack_trace: Optional[str] = Field(None, description="Stack trace for debugging")

    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class AgentContext(BaseModel):
    """Agent execution context."""

    # User/Org context
    user_id: PyObjectId
    org_id: PyObjectId
    session_id: Optional[str] = None

    # Deal context (if applicable)
    deal_id: Optional[PyObjectId] = None
    deal_data: Optional[Dict[str, Any]] = None

    # Vector context
    vector_collection: Optional[str] = None

    # Additional context
    metadata: Dict[str, Any] = Field(default_factory=dict)

    @validator("vector_collection", always=True)
    def set_vector_collection(cls, v, values, info=None):
        """Auto-set vector collection based on org_id."""
        if not v and "org_id" in values:
            collection_name = f"org_{values['org_id']}_vectors"
            logger.debug(f"Auto-setting vector collection to: {collection_name}")
            return collection_name
        logger.debug(f"Using provided vector collection: {v}")
        return v

    class Config:
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}
