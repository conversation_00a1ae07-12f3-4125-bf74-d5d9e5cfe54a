"""
Exclusion Filter Service Interfaces

This module defines the interfaces for exclusion filter services.
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from bson import ObjectId

from app.models.exclusion_filter import ExclusionFilter


class ExclusionFilterServiceInterface(ABC):
    """Interface for exclusion filter services."""

    @abstractmethod
    async def create_exclusion_filter(
        self,
        **kwargs
    ) -> ExclusionFilter:
        """
        Create a new exclusion filter.

        Args:
            **kwargs: Filter data including:
                org_id: Organization ID
                form_id: Form ID
                name: Name of the filter
                description: Description of the filter
                operator: Logical operator for conditions
                conditions: List of conditions
                created_by: User ID who created this filter

        Returns:
            The created exclusion filter
        """
        pass

    @abstractmethod
    async def get_exclusion_filter(
        self,
        filter_id: Union[str, ObjectId]
    ) -> Optional[ExclusionFilter]:
        """
        Get an exclusion filter by ID.

        Args:
            filter_id: Exclusion filter ID

        Returns:
            The exclusion filter or None if not found
        """
        pass

    @abstractmethod
    async def update_exclusion_filter(
        self,
        filter_id: Union[str, ObjectId],
        update_data: Dict[str, Any]
    ) -> Optional[ExclusionFilter]:
        """
        Update an exclusion filter.

        Args:
            filter_id: Exclusion filter ID
            update_data: Data to update

        Returns:
            The updated exclusion filter or None if not found
        """
        pass

    @abstractmethod
    async def delete_exclusion_filter(
        self,
        filter_id: Union[str, ObjectId]
    ) -> bool:
        """
        Delete an exclusion filter.

        Args:
            filter_id: Exclusion filter ID

        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    async def list_exclusion_filters(
        self,
        org_id: Union[str, ObjectId],
        form_id: Optional[Union[str, ObjectId]] = None,
        include_deleted: bool = False
    ) -> List[ExclusionFilter]:
        """
        List exclusion filters for an organization.

        Args:
            org_id: Organization ID
            form_id: Optional form ID to filter by
            include_deleted: Whether to include deleted filters

        Returns:
            List of exclusion filters
        """
        pass

    @abstractmethod
    async def check_exclusion(
        self,
        org_id: Union[str, ObjectId],
        form_id: Union[str, ObjectId],
        answers: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Check if a submission should be excluded based on exclusion filters.

        Args:
            org_id: Organization ID
            form_id: Form ID
            answers: Form answers

        Returns:
            Dictionary with exclusion results:
            - excluded: Boolean indicating if submission should be excluded
            - filter_id: ID of the matching filter (if excluded)
            - filter_name: Name of the matching filter (if excluded)
            - reason: Description of why it was excluded (if excluded)
        """
        pass
