"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Copy, 
  Download, 
  CheckCircle, 
  FileText,
  RefreshCw
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"

interface ExecutiveSummarySectionProps {
  executive_summary: { title: string; content: string }[]
  metadata?: { 
    model_used: string; 
    generated_at: string 
  }
  onRefresh?: () => void
  refreshing?: boolean
}

export function ExecutiveSummarySection({
  executive_summary,
  metadata,
  onRefresh,
  refreshing = false
}: ExecutiveSummarySectionProps) {
  const [copied, setCopied] = useState(false)
  const { toast } = useToast()

  const handleCopy = async () => {
    if (!executive_summary) return

    try {
      const fullText = executive_summary
        .map(section => `${section.title}\n${section.content}`)
        .join('\n\n')
      
      await navigator.clipboard.writeText(fullText)
      setCopied(true)
      
      toast({
        title: "Copied to clipboard",
        description: "Executive summary has been copied to your clipboard.",
      })

      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Error copying summary:', err)
      toast({
        title: "Copy failed",
        description: "Failed to copy executive summary to clipboard.",
        variant: "destructive"
      })
    }
  }

  const handleDownloadPDF = () => {
    // TODO: Implement PDF export
    toast({
      title: "PDF Export",
      description: "PDF export feature coming soon.",
    })
  }

  if (!executive_summary || executive_summary.length === 0) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="space-y-4 text-center">
          <FileText className="mx-auto size-12 text-muted-foreground" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">No Executive Summary</h3>
            <p className="mt-1 text-sm text-gray-600">
              Executive summary not available for this deal.
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full pt-6 space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center w-full">
        <div>
          <h2 className="text-xl font-bold text-gray-900">Executive Summary</h2>
          {metadata && (
            <div className="flex items-center gap-2 mt-1">
              <small className="text-xs text-muted-foreground">
                Generated by {"Orbit AI"} on{" "}
                {new Date(metadata.generated_at).toLocaleDateString()}
              </small>
            </div>
          )}
        </div>
        <div className="flex items-center gap-2">
          {onRefresh && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onRefresh}
              disabled={refreshing}
              className="gap-2"
            >
              <RefreshCw className={cn("size-4", refreshing && "animate-spin")} />
              Refresh
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDownloadPDF}
            className="gap-2"
          >
            <Download className="size-4" />
            PDF
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopy}
            className="gap-2"
          >
            {copied ? (
              <>
                <CheckCircle className="size-4 text-green-600" />
                Copied
              </>
            ) : (
              <>
                <Copy className="size-4" />
                Copy
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Executive Summary Content */}
      <Card className="w-full border-0 bg-white/80 shadow-xl backdrop-blur-md">
        <CardContent className="p-8 space-y-6">
          {executive_summary.map((section, idx) => (
            <motion.div
              key={idx}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: idx * 0.1 }}
              className="space-y-3"
            >
              <h3 className="text-base font-semibold text-gray-900 flex items-center gap-2">
                <span className="text-primary">▍</span>
                {section.title}
              </h3>
              <p className="text-sm leading-6 text-muted-foreground whitespace-pre-wrap">
                {section.content}
              </p>
            </motion.div>
          ))}
        </CardContent>
      </Card>

      {/* Footer */}
      <div className="pt-6 border-t text-xs text-center text-muted-foreground">
        Generated by <strong>TractionX Intelligence</strong>
      </div>
    </div>
  )
} 