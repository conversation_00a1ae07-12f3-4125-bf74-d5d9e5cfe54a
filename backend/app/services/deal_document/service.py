"""
Deal Document Service for TractionX document management.

This service handles all document operations including upload, download,
deletion, and migration of startup submission files to deal documents.
"""

import hashlib
import logging
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union

import boto3
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.config import settings
from app.models.deal_document import DealDocument, DocumentSource, DocumentType
from app.services.base import BaseService
from app.utils.common import PyObjectId
from app.models.form import Submission

logger = logging.getLogger(__name__)


class DealDocumentService(BaseService):
    """Service for managing deal documents."""

    def __init__(self, db: AsyncIOMotorDatabase):
        super().__init__()
        self.db = db

        # Initialize S3 client with explicit configuration
        logger.info(
            f"Initializing S3 client with region: {settings.AWS_REGION}, bucket: {settings.S3_BUCKET_SUBMISSIONS}"
        )

        # Handle case where credentials might not be set
        if not settings.AWS_ACCESS_KEY_ID or not settings.AWS_SECRET_ACCESS_KEY:
            logger.warning(
                "AWS credentials not found in settings, falling back to default credential chain"
            )
            self.s3_client = boto3.client("s3", region_name=settings.AWS_REGION)
        else:
            self.s3_client = boto3.client(
                "s3",
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_REGION,
            )

        # Test S3 connection at startup
        # try:
        #     logger.info("Testing S3 connection...")
        #     self.s3_client.head_bucket(Bucket=settings.S3_BUCKET_SUBMISSIONS)
        #     logger.info("S3 connection test successful")
        # except Exception as e:
        #     logger.error(f"S3 connection test failed: {e}")

    async def initialize(self) -> None:
        """Initialize service resources."""
        pass

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        pass

    async def list_deal_documents(
        self,
        deal_id: Union[str, ObjectId],
        include_deleted: bool = False,
    ) -> List[DealDocument]:
        """
        List all documents for a deal.

        Args:
            deal_id: Deal ID
            org_id: Organization ID for access control
            include_deleted: Whether to include soft-deleted documents

        Returns:
            List of deal documents
        """
        try:
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)

            query: Dict[str, Any] = {
                "deal_id": deal_id,
            }
            documents = await DealDocument.find_many(
                query,
                skip=0,
                limit=100,
                sort=[("created_at", -1)],
            )

            for document in documents:
                # Generate fresh presigned URLs
                document.download_url = await self._generate_download_url(document)
                document.preview_url = await self._generate_preview_url(document)

            logger.info(f"Found {len(documents)} documents for deal {deal_id}")
            return documents

        except Exception as e:
            await self.handle_error(e, {"deal_id": str(deal_id)})
            return []

    async def upload_document(
        self,
        deal_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        file_content: bytes,
        filename: str,
        mime_type: str,
        user_id: Union[str, ObjectId],
        user_email: str,
        user_name: Optional[str] = None,
        user_role: Optional[str] = None,
        tags: Optional[List[str]] = None,
    ) -> Optional[DealDocument]:
        """
        Upload a new document to a deal.

        Args:
            deal_id: Deal ID
            org_id: Organization ID
            file_content: File content bytes
            filename: Original filename
            mime_type: MIME type of the file
            user_id: ID of the uploading user
            user_email: Email of the uploading user
            user_name: Name of the uploading user
            user_role: Role of the uploading user
            tags: Optional tags for the document

        Returns:
            Created DealDocument or None if failed
        """
        try:
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(user_id, str):
                user_id = ObjectId(user_id)

            # Validate file size
            file_size = len(file_content)
            max_size = settings.MAX_FILE_SIZE_MB * 1024 * 1024
            if file_size > max_size:
                raise ValueError(
                    f"File size ({file_size} bytes) exceeds maximum ({max_size} bytes)"
                )

            # Determine document type
            document_type = DealDocument.determine_document_type(filename, mime_type)

            # Generate S3 key
            file_id = str(uuid.uuid4())
            s3_key = f"deals/{deal_id}/documents/{file_id}_{filename}"

            # Calculate checksum
            checksum = hashlib.md5(file_content).hexdigest()

            # Create document record
            document = DealDocument(
                deal_id=PyObjectId(deal_id),
                org_id=PyObjectId(org_id),
                source=DocumentSource.INVESTOR_UPLOAD,
                submission_id=None,  # Not from a submission
                submission_file_id=None,  # Not from a submission file
                uploaded_by_user_id=PyObjectId(user_id),
                uploaded_by_email=user_email,
                uploaded_by_name=user_name,
                uploaded_by_role=user_role,
                filename=filename,
                s3_key=s3_key,
                s3_bucket=settings.S3_BUCKET_SUBMISSIONS,
                mime_type=mime_type,
                file_size=file_size,
                document_type=document_type,
                checksum=checksum,
                tags=tags or [],
            )

            # Upload to S3 first - if this fails, don't save to database
            logger.info(f"Uploading {filename} to S3 for deal {deal_id}")
            await self._upload_to_s3(s3_key, file_content, mime_type)
            logger.info(f"S3 upload successful for {filename}")

            # Mark as uploaded and ready
            document.mark_uploaded()
            document.mark_ready()

            # Save to database only after successful S3 upload
            logger.info(f"Saving document {filename} to database")
            document = await document.save()

            # Generate URLs
            document.download_url = await self._generate_download_url(document)
            document.preview_url = await self._generate_preview_url(document)

            logger.info(f"Successfully uploaded document {filename} for deal {deal_id}")
            return document

        except Exception as e:
            logger.error(f"Failed to upload document {filename}: {e}", exc_info=True)
            await self.handle_error(
                e,
                {
                    "deal_id": str(deal_id),
                    "org_id": str(org_id),
                    "filename": filename,
                    "user_id": str(user_id),
                },
            )
            return None

    async def delete_document(
        self,
        document_id: Union[str, ObjectId],
        user_email: str,
        user_role: str,
    ) -> tuple[bool, str]:
        """
        Delete a document (soft delete).

        Args:
            document_id: Document ID
            user_email: Email of the user requesting deletion
            user_role: Role of the user requesting deletion

        Returns:
            Tuple of (success: bool, error_message: str)
        """
        try:
            if isinstance(document_id, str):
                document_id = ObjectId(document_id)

            # Get document
            document = await DealDocument.find_one({
                "_id": document_id,
            })
            if not document:
                return False, "Document not found"

            # Check permissions
            if not document.can_be_deleted_by(user_email, user_role):
                return False, "User does not have permission to delete this document"

            # Soft delete
            await document.hard_delete()

            logger.info(f"Deleted document {document_id} by {user_email}")
            return True, "Document deleted successfully"

        except Exception as e:
            await self.handle_error(
                e,
                {
                    "document_id": str(document_id),
                    "user_email": user_email,
                    "user_role": user_role,
                },
            )
            return False, f"Failed to delete document: {str(e)}"

    async def get_document_download_url(
        self,
        document_id: Union[str, ObjectId],
        user_email: str,
    ) -> Optional[str]:
        """
        Get a presigned download URL for a document.

        Args:
            document_id: Document ID
            user_email: Email of the requesting user

        Returns:
            Presigned download URL or None if not accessible
        """
        try:
            if isinstance(document_id, str):
                document_id = ObjectId(document_id)

            # Get document
            document = await DealDocument.find_one({
                "_id": document_id,
            })
            if not document:
                return None

            # document = DealDocument(**doc_data)

            # Track access
            document.track_access(user_email)
            await document.save(is_update=True)

            # Generate presigned URL
            return await self._generate_download_url(document)

        except Exception as e:
            await self.handle_error(
                e, {"document_id": str(document_id), "user_email": user_email}
            )
            return None

    async def migrate_submission_files_to_deal(
        self,
        deal_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        submission_ids: List[Union[str, ObjectId]],
    ) -> List[DealDocument]:
        """
        Migrate submission files to deal documents.

        This is called when creating a deal from submission to move
        all submission files to the deal documents collection.

        Args:
            deal_id: Deal ID
            org_id: Organization ID
            submission_ids: List of submission IDs to migrate files from

        Returns:
            List of created deal documents
        """
        try:
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            submission_ids = [
                ObjectId(sid) if isinstance(sid, str) else sid for sid in submission_ids
            ]

            migrated_documents = []

            # Get all submissions and extract files from their answers
            for submission_id in submission_ids:
                submission = await Submission.find_one({"_id": submission_id})
                submission = submission.model_dump(by_alias=True) if submission else None

                if not submission or not submission.get("answers"):
                    continue

                # Extract files from submission answers
                for question_id, answer in submission["answers"].items():
                    # Check if answer is a file object
                    if (
                        isinstance(answer, dict)
                        and "file" in answer
                        and "filename" in answer
                        and "size" in answer
                    ):
                        # Extract file info from submission answer
                        s3_key = answer["file"]
                        filename = answer["filename"]
                        file_size = answer["size"]
                        mime_type = answer.get("type", "application/octet-stream")
                        uploaded_at = answer.get("uploaded_at")

                        # Parse upload timestamp
                        created_timestamp = int(datetime.now(timezone.utc).timestamp())
                        if uploaded_at:
                            try:
                                # Handle ISO format timestamps
                                if uploaded_at.endswith("Z"):
                                    upload_dt = datetime.fromisoformat(
                                        uploaded_at.replace("Z", "+00:00")
                                    )
                                else:
                                    upload_dt = datetime.fromisoformat(uploaded_at)
                                created_timestamp = int(upload_dt.timestamp())
                            except Exception as e:
                                logger.error(
                                    f"Failed to parse uploaded_at timestamp {uploaded_at}: {e}"
                                )
                                # Fallback to current time if parsing fails
                                pass

                        # Determine document type
                        document_type = DealDocument.determine_document_type(
                            filename, mime_type
                        )

                        # Create deal document from submission file
                        deal_document = DealDocument(
                            deal_id=PyObjectId(deal_id),
                            org_id=PyObjectId(org_id),
                            source=DocumentSource.STARTUP_SUBMISSION,
                            submission_id=PyObjectId(submission_id),
                            submission_file_id=None,  # No submission file ID for embedded files
                            uploaded_by_user_id=PyObjectId(),  # Create placeholder ID for startup submissions
                            uploaded_by_email=submission.get("metadata", {}).get(
                                "public_user_email", "startup"
                            ),
                            uploaded_by_name="Startup Submission",
                            uploaded_by_role="startup",
                            filename=filename,
                            s3_key=s3_key,
                            s3_bucket=settings.S3_BUCKET_SUBMISSIONS,  # Use submissions bucket
                            mime_type=mime_type,
                            file_size=file_size,
                            document_type=document_type,
                            tags=["startup-submission"],
                            created_at=created_timestamp,
                        )

                        deal_document.mark_ready()

                        # Generate URLs
                        deal_document.download_url = await self._generate_download_url(
                            deal_document
                        )
                        deal_document.preview_url = await self._generate_preview_url(
                            deal_document
                        )

                        # Save to database
                        deal_document = await deal_document.save()

                        migrated_documents.append(deal_document)

                        logger.info(
                            f"Migrated file {filename} from submission {submission_id} to deal {deal_id}"
                        )

            logger.info(
                f"Migrated {len(migrated_documents)} files from submissions to deal {deal_id}"
            )
            return migrated_documents

        except Exception as e:
            await self.handle_error(
                e,
                {
                    "deal_id": str(deal_id),
                    "org_id": str(org_id),
                    "submission_ids": [str(sid) for sid in submission_ids],
                },
            )
            return []

    async def _upload_to_s3(
        self, s3_key: str, file_content: bytes, mime_type: str
    ) -> None:
        """Upload file to S3."""
        try:
            logger.info(
                f"Starting S3 upload for key: {s3_key} to bucket: {settings.S3_BUCKET_SUBMISSIONS}"
            )

            # Use asyncio.get_event_loop().run_in_executor to run the sync S3 call in a thread
            import asyncio

            loop = asyncio.get_event_loop()

            await loop.run_in_executor(
                None,
                lambda: self.s3_client.put_object(
                    Bucket=settings.S3_BUCKET_SUBMISSIONS,
                    Key=s3_key,
                    Body=file_content,
                    ContentType=mime_type,
                ),
            )

            logger.info(f"Successfully uploaded file to S3: {s3_key}")

        except Exception as e:
            logger.error(f"Failed to upload to S3: {e}", exc_info=True)
            raise

    async def _generate_download_url(self, document: DealDocument) -> str:
        """Generate presigned download URL."""
        try:
            import asyncio

            loop = asyncio.get_event_loop()

            url = await loop.run_in_executor(
                None,
                lambda: self.s3_client.generate_presigned_url(
                    "get_object",
                    Params={"Bucket": document.s3_bucket, "Key": document.s3_key},
                    ExpiresIn=settings.S3_PRESIGNED_URL_EXPIRY,
                ),
            )
            return url
        except Exception as e:
            logger.error(f"Failed to generate download URL: {e}", exc_info=True)
            return ""

    async def _generate_preview_url(self, document: DealDocument) -> Optional[str]:
        """Generate preview URL for supported file types."""
        try:
            # Only generate preview URLs for PDFs and images
            if document.document_type in [DocumentType.PDF, DocumentType.IMAGE]:
                return await self._generate_download_url(document)
            return None
        except Exception as e:
            logger.error(f"Failed to generate preview URL: {e}")
            return None
