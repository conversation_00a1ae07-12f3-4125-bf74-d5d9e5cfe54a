# Dependency Injection Implementation

## Overview

This document describes the corrected implementation of dependency injection patterns for the submission processing service and queue handlers, ensuring they follow the same patterns used throughout the codebase (like `forms.py`).

## Key Changes Made

### ✅ **Proper Service Factory Pattern**

**Before (Incorrect):**
```python
# Factory tried to create service directly without dependencies
async def get_submission_processing_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    from app.services.submission_processing.service import SubmissionProcessingService
    return await ServiceFactory.get_service(SubmissionProcessingService, db)
```

**After (Correct):**
```python
# Factory properly injects all dependencies
async def get_submission_processing_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    from app.services.submission_processing.service import SubmissionProcessingService
    
    # Get all required dependencies
    exclusion_filter_service = await get_exclusion_filter_service(db)
    thesis_service = await get_thesis_service(db)
    deal_service = await get_deal_service(db)
    queue_service = await get_queue_service()
    form_service = await get_form_service(db)
    
    # Create service with dependencies
    service = SubmissionProcessingService(
        database=db,
        exclusion_filter_service=exclusion_filter_service,
        thesis_service=thesis_service,
        deal_service=deal_service,
        queue_service=queue_service,
        form_service=form_service
    )
    
    await service.initialize()
    return service
```

### ✅ **Service Constructor Dependency Injection**

**Before (Incorrect):**
```python
class SubmissionProcessingService(BaseService, SubmissionProcessingServiceInterface):
    def __init__(self, db: AsyncIOMotorDatabase):
        super().__init__(db)
        self.db = db

    async def process_submission(
        self,
        # ... parameters ...
        deal_service: DealServiceInterface = Depends(get_deal_service),  # ❌ Wrong!
    ) -> SubmissionProcessingResult:
```

**After (Correct):**
```python
class SubmissionProcessingService(BaseService, SubmissionProcessingServiceInterface):
    def __init__(
        self,
        database: AsyncIOMotorDatabase,
        exclusion_filter_service: ExclusionFilterServiceInterface,
        thesis_service: ThesisServiceInterface,
        deal_service: DealServiceInterface,
        queue_service: QueueServiceInterface,
        form_service: FormServiceInterface
    ):
        super().__init__(database)
        self.exclusion_filter_service = exclusion_filter_service
        self.thesis_service = thesis_service
        self.deal_service = deal_service
        self.queue_service = queue_service
        self.form_service = form_service

    async def process_submission(
        self,
        # ... parameters ...
        # No Depends decorators in methods! ✅
    ) -> SubmissionProcessingResult:
```

### ✅ **Queue Handler BaseJobHandler Pattern**

**Before (Incorrect):**
```python
class SubmissionProcessingHandler(JobHandlerInterface):
    def __init__(self):
        self.submission_processing_service: Optional[SubmissionProcessingServiceInterface] = None
        self.job_service: Optional[JobServiceInterface] = None

    async def initialize(self) -> None:
        self.submission_processing_service = await get_submission_processing_service()
        self.job_service = await get_job_service()
```

**After (Correct):**
```python
class SubmissionProcessingHandler(BaseJobHandler):
    def __init__(self):
        super().__init__()  # ✅ Inherit from BaseJobHandler
        self.submission_processing_service: Optional[SubmissionProcessingServiceInterface] = None
        self.job_service: Optional[JobServiceInterface] = None

    async def _initialize_services(self) -> None:  # ✅ Override _initialize_services
        if self.submission_processing_service is None:
            self.submission_processing_service = await get_submission_processing_service()
            await self.submission_processing_service.initialize()
        
        if self.job_service is None:
            self.job_service = await get_job_service()
            await self.job_service.initialize()
```

## Dependency Injection Patterns

### 1. **API Endpoints Pattern (forms.py style)**

```python
@router.post("/submit")
async def submit_form(
    form_id: str,
    request: SubmitFormRequest,
    org_context: tuple[str, bool] = Depends(get_org_context),
    form_service: FormServiceInterface = Depends(get_form_service),  # ✅ Depends in API
    queue_service = Depends(get_queue_service),
    job_service = Depends(get_job_service)
) -> Submission:
    # Use services directly
    result = await form_service.create_submission(...)
```

### 2. **Service Constructor Pattern**

```python
class MyService(BaseService, MyServiceInterface):
    def __init__(
        self,
        database: AsyncIOMotorDatabase,
        dependency1: Dependency1Interface,
        dependency2: Dependency2Interface
    ):
        super().__init__(database)
        self.dependency1 = dependency1
        self.dependency2 = dependency2

    async def my_method(self, param: str) -> str:  # ✅ No Depends in methods
        return await self.dependency1.do_something(param)
```

### 3. **Factory Pattern**

```python
async def get_my_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    # Get all dependencies
    dep1 = await get_dependency1_service(db)
    dep2 = await get_dependency2_service(db)
    
    # Create service with dependencies
    service = MyService(
        database=db,
        dependency1=dep1,
        dependency2=dep2
    )
    
    await service.initialize()
    return service
```

### 4. **Queue Handler Pattern**

```python
class MyJobHandler(BaseJobHandler):
    def __init__(self):
        super().__init__()
        self.my_service: Optional[MyServiceInterface] = None

    async def _initialize_services(self) -> None:
        if self.my_service is None:
            self.my_service = await get_my_service()
            await self.my_service.initialize()

    async def process(self, job: Job) -> Optional[Dict[str, Any]]:
        # Use self.my_service
        return await self.my_service.process_job(job.payload)
```

## Why This Pattern?

### ✅ **Consistency with Codebase**
- Follows the exact same pattern as `forms.py` and other API endpoints
- Services get dependencies via constructor, not method parameters
- Queue handlers inherit from `BaseJobHandler`

### ✅ **Proper Separation of Concerns**
- **API Layer**: Uses `Depends()` for request-scoped dependencies
- **Service Layer**: Gets dependencies via constructor injection
- **Queue Layer**: Uses factory pattern to get services

### ✅ **Testability**
- Services can be easily mocked by injecting test dependencies
- No hidden dependencies via `Depends()` decorators in methods
- Clear dependency graph

### ✅ **Performance**
- Dependencies created once per service instance
- No repeated dependency resolution per method call
- Proper service lifecycle management

## Testing Results

All tests pass confirming:

- ✅ Proper dependency injection via factory pattern
- ✅ Services injected in constructor, not via Depends in methods  
- ✅ Handlers follow BaseJobHandler pattern
- ✅ All service methods callable without Depends
- ✅ Async processing structure properly implemented
- ✅ Condition evaluation engine working
- ✅ Follows codebase patterns (like forms.py)

## Integration Points

### Updated Factory (`app/services/factory.py`)
```python
async def get_submission_processing_service(db: AsyncIOMotorDatabase = Depends(get_database)):
    """Get Submission Processing service instance with all dependencies."""
    # Properly injects all required services
```

### Updated Handlers (`app/services/queue/handlers/`)
- `SubmissionProcessingHandler` - Follows BaseJobHandler pattern
- `DealEnrichmentHandler` - Follows BaseJobHandler pattern

### Service Implementation (`app/services/submission_processing/service.py`)
- Constructor receives all dependencies
- Methods use `self.dependency_service` instead of `Depends()`
- Proper initialization and cleanup lifecycle

## Best Practices Established

1. **Never use `Depends()` in service method signatures**
2. **Always inject dependencies via constructor**
3. **Use factory pattern for complex service creation**
4. **Queue handlers inherit from `BaseJobHandler`**
5. **Override `_initialize_services()` in handlers**
6. **Follow the same patterns as existing code (forms.py)**

This implementation now perfectly aligns with the codebase patterns and provides a solid foundation for the async submission processing system.
