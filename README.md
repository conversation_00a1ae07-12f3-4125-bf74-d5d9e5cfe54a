# TractionX Monorepo

This monorepo contains the entire TractionX project, with the backend and frontend in separate folders. This structure is designed so that each part (backend and frontend) is self-contained (including Docker, env, and configs) and can later be split into separate repositories.

## Structure

- **backend/** – Contains the entire backend (source, tests, Docker, configs, env, and backend README).
- **frontend/** – (Future) Contains the entire frontend (source, Docker, configs, env, and frontend README).

 (If you run commands from the root, update your paths accordingly.) 