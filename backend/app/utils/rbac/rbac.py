from typing import Dict, List, Any
from fastapi import FastAPI
from fastapi.routing import APIRoute
from functools import wraps
from typing import Optional
from fastapi import Request
from app.core.errors import AuthorizationError
from app.core.config import settings
from app.core.logging import get_logger
from functools import wraps

logger = get_logger(__name__)

# Map HTTP methods to RBAC actions
METHOD_TO_ACTION = {
    "GET": "view",
    "POST": "create",
    "PUT": "edit",
    "PATCH": "edit",
    "DELETE": "delete"
}


# Central registry for RBAC metadata (deduped, keyed by (resource, action, group))
RBAC_REGISTRY: Dict[tuple, Dict[str, Any]] = {}


def normalize_resource(resource: str) -> str:
    return resource.lower().replace('-', '_').replace(' ', '_')


def normalize_action(action: str) -> str:
    return action.lower().replace('-', '_').replace(' ', '_')


def rbac_register(
    resource: Optional[str] = None,
    action: Optional[str] = None,
    *,
    group: Optional[str] = None,
    description: Optional[str] = None,
    hidden: bool = False,
):
    """
    Decorator to register RBAC metadata on route handlers.
    Usage:
        @rbac_register
        @rbac_register(resource="deal", action="view", group="Deals", description="View deals")
        @rbac_register(hidden=True)  # Hide from UI/admin
    """
    def decorator(func):
        # Attach RBAC metadata to the function for FastAPI route scanning
        res_norm = normalize_resource(resource) if resource else None
        act_norm = normalize_action(action) if action else None
        setattr(func, "_rbac_registered", True)
        setattr(func, "_rbac_resource", res_norm)
        setattr(func, "_rbac_action", act_norm)
        setattr(func, "_rbac_group", group)
        setattr(func, "_rbac_description", description)
        setattr(func, "_rbac_hidden", hidden)
        # Deduplication: use (resource, action, group) as key
        key = (res_norm, act_norm, group)
        RBAC_REGISTRY[key] = {
            "resource": res_norm,
            "action": act_norm,
            "group": group,
            "description": description,
            "hidden": hidden,
        }
        return func
    return decorator


def get_resources_and_actions(app: FastAPI) -> List[Dict[str, Any]]:
    """
    Returns a deduped, structured list of all RBAC resources/actions for the UI/admin.
    Each entry contains: resource, action, group, description, basic.
    'basic' permissions are filtered out for UI assignment (but still enforced).
    """
    seen = set()
    resources = []
    for route in app.routes:
        if isinstance(route, APIRoute):
            endpoint = route.endpoint
            if not getattr(endpoint, "_rbac_registered", False):
                continue
            # Skip hidden endpoints for UI/admin assignment
            if getattr(endpoint, "_rbac_hidden", False):
                continue
            # Remove API prefix for resource inference
            path = route.path
            if settings.API_V1_STR and path.startswith(settings.API_V1_STR):
                path = path[len(settings.API_V1_STR):]
            path_parts = path.strip("/").split("/")
            resource = getattr(endpoint, "_rbac_resource", None) or (
                normalize_resource(path_parts[-1]) if path_parts else None)
            action = getattr(endpoint, "_rbac_action", None)
            if not action and route.methods:
                # Use HTTP method as action
                action = normalize_action(next(iter(route.methods)).lower())
            group = getattr(endpoint, "_rbac_group", None)
            description = getattr(endpoint, "_rbac_description", None)
            key = (resource, action, group)
            if key in seen:
                continue
            seen.add(key)
            resources.append({
                "resource": resource,
                "action": action,
                "group": group,
                "description": description,
                "path": route.path,
                "method": list(route.methods),
            })
    return resources


def export_all_permissions(structured: bool = True) -> List[Dict[str, Any]]:
    """
    Export all deduped permissions from the RBAC_REGISTRY for admin/UI/devops.
    If structured=True, returns a list of dicts grouped by group/resource/action.
    """
    perms = list(RBAC_REGISTRY.values())
    if structured:
        # Group by group, then resource, then actions
        from collections import defaultdict
        grouped = defaultdict(lambda: defaultdict(list))
        for perm in perms:
            grouped[perm["group"]][perm["resource"]].append(perm["action"])
        # Convert to list of dicts for UI
        result = []
        for group, resources in grouped.items():
            for resource, actions in resources.items():
                result.append({
                    "group": group,
                    "resource": resource,
                    "actions": sorted(set(actions)),
                })
        return result
    return perms


def rbac_check(
    resource: Optional[str] = None,
    action: Optional[str] = None
):
    """
    Decorator to enforce RBAC per-route.

    If resource or action is omitted, they'll be auto-detected:
      • resource = last segment of request.url.path
      • action   = HTTP method (normalized, e.g., 'POST' → 'POST')

    Usage:
      @router.get("/deals")
      @rbac_check()                          # auto-detect 'deals' + 'view'
      async def list_deals(request: Request): ...

      @router.post("/deals")
      @rbac_check(action="create")          # auto-detect 'deals', override 'create'
      async def create_deal(request: Request): ...

      @router.get("/health")
      @rbac_check(resource="system", action="health")  # full override
      async def health_check(request: Request): ...
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Find the Request object among args/kwargs
            request: Request = kwargs.get("request") or next(
                (arg for arg in args if isinstance(arg, Request)), None
            )
            if not request:
                raise RuntimeError(
                    "rbac_check: route handler must include a 'request: Request' parameter"
                )

            user = getattr(request.state, "user", None)
            if user is None:
                raise RuntimeError("rbac_check: request.state.user not set")

            # Superusers bypass all checks
            if getattr(user, "is_superuser", False):
                return await func(*args, **kwargs)

            # Determine resource & action
            res = resource or getattr(func, "_rbac_resource", None)
            act = action or getattr(func, "_rbac_action", None)

            # Always infer from route if not explicitly set
            if not res or not act:
                path_segments = request.url.path.strip("/").split("/")
                if not res and path_segments:
                    # Use last segment for resource
                    res = normalize_resource(path_segments[-1])
                if not act:
                    # Use HTTP method as action (e.g., 'POST', 'GET')
                    act = normalize_action(request.method.upper())

            # If we now have both, perform check
            if res and act:
                rbac = getattr(request.app.state, "rbac_service", None)
                if rbac is None:
                    raise RuntimeError(
                        "rbac_check: request.app.state.rbac_service not set")
                allowed = await rbac.check_permission(str(user.id), res, act)
                if not allowed:
                    raise AuthorizationError(
                        message="Insufficient permissions",
                        details={"resource": res, "action": act}
                    ).to_http_exception()

            return await func(*args, **kwargs)
        return wrapper
    return decorator
