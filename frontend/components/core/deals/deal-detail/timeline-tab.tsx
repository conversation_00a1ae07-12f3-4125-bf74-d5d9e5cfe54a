"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { 
  Clock, 
  FileText, 
  Target, 
  User, 
  Settings,
  CheckCircle
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData, TimelineEvent } from "@/lib/types/deal-detail"
import { EmptyPlaceholder } from "@/components/empty-placeholder"

interface TimelineTabProps {
  deal: DealDetailData
}

const getEventIcon = (type: TimelineEvent['type']) => {
  switch (type) {
    case 'system':
      return Settings
    case 'user':
      return User
    case 'score':
      return Target
    case 'document':
      return FileText
    case 'status':
      return CheckCircle
    default:
      return Clock
  }
}

const getEventColor = (type: TimelineEvent['type']) => {
  switch (type) {
    case 'system':
      return 'bg-gray-100 text-gray-600 border-gray-200'
    case 'user':
      return 'bg-blue-100 text-blue-600 border-blue-200'
    case 'score':
      return 'bg-green-100 text-green-600 border-green-200'
    case 'document':
      return 'bg-purple-100 text-purple-600 border-purple-200'
    case 'status':
      return 'bg-orange-100 text-orange-600 border-orange-200'
    default:
      return 'bg-gray-100 text-gray-600 border-gray-200'
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) {
    return 'Just now'
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`
  } else if (diffInHours < 48) {
    return 'Yesterday'
  } else {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    })
  }
}

const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: '2-digit',
    year: 'numeric'
  }) + ' ' + date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  })
}

const groupEventsByDate = (events: TimelineEvent[]) => {
  const groups: { [key: string]: TimelineEvent[] } = {}
  
  events.forEach(event => {
    const date = new Date(event.date).toDateString()
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(event)
  })
  
  return Object.entries(groups).sort(([a], [b]) => 
    new Date(b).getTime() - new Date(a).getTime()
  )
}

export function TimelineTab({ deal }: TimelineTabProps) {
  const events = deal.timeline || []

  if (events.length === 0) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="clock" />
        <EmptyPlaceholder.Title>No activity recorded for this deal yet</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          Timeline events will appear here as actions are taken on this deal. Key milestones and activities will be tracked automatically.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    )
  }

  // Convert timeline events to proper format if needed
  const normalizedEvents = events.map((event, index) => {
    // Handle both backend format and frontend format
    if (typeof event === 'object' && event.date && event.event) {
      return {
        id: event.id || `event-${index}`,
        date: event.date,
        event: event.event,
        description: (event as any).notes || event.description,
        user_id: event.user_id,
        user_name: event.user_name,
        type: event.type || 'system',
        metadata: event.metadata || {}
      } as TimelineEvent
    }
    return event as TimelineEvent
  })

  const groupedEvents = groupEventsByDate(normalizedEvents)

  return (
    <div className="space-y-6">
      {groupedEvents.map(([dateString, dayEvents], groupIndex) => (
        <motion.div
          key={dateString}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: groupIndex * 0.1 }}
          className="space-y-4"
        >
          {/* Date Header */}
          <div className="border-b pb-3">
            <h3 className="text-xl font-semibold text-gray-900">
              {formatDate(dayEvents[0].date)}
            </h3>
            <p className="text-sm text-muted-foreground">
              {dayEvents.length} {dayEvents.length === 1 ? 'event' : 'events'}
            </p>
          </div>

          {/* Timeline Events */}
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute inset-y-0 left-5 w-0.5 bg-gray-200" />
            
            <div className="space-y-6">
              {dayEvents
                .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                .map((event, eventIndex) => {
                  const Icon = getEventIcon(event.type)
                  
                  return (
                    <motion.div
                      key={event.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: eventIndex * 0.05 }}
                      className="relative flex items-start gap-4"
                    >
                      {/* Event Icon */}
                      <div className={cn(
                        "z-10 flex size-10 items-center justify-center rounded-lg border-2 bg-white",
                        getEventColor(event.type)
                      )}>
                        <Icon className="size-5" />
                      </div>

                      {/* Event Content */}
                      <div className="min-w-0 flex-1">
                        <Card className="transition-shadow hover:shadow-sm">
                          <CardContent className="p-4">
                            <div className="space-y-3">
                              <div className="flex items-start justify-between gap-4">
                                <div className="flex-1">
                                  <h4 className="text-base font-medium text-gray-900">
                                    {event.event}
                                  </h4>
                                  {event.description && (
                                    <p className="mt-1 text-sm leading-relaxed text-muted-foreground">
                                      {event.description}
                                    </p>
                                  )}
                                </div>
                                
                                <div className="flex items-center gap-3 text-xs text-muted-foreground">
                                  <Badge 
                                    variant="outline" 
                                    className={cn("text-xs capitalize", getEventColor(event.type))}
                                  >
                                    {event.type}
                                  </Badge>
                                  <span>{formatTime(event.date)}</span>
                                </div>
                              </div>
                              
                              {/* Event Metadata */}
                              {event.user_name && (
                                <div className="flex items-center gap-2 border-t pt-2">
                                  <Avatar className="size-6">
                                    <AvatarFallback className="text-xs font-medium">
                                      {event.user_name.split(' ').map(n => n[0]).join('').toUpperCase()}
                                    </AvatarFallback>
                                  </Avatar>
                                  <span className="text-sm text-muted-foreground">
                                    {event.user_name}
                                  </span>
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </motion.div>
                  )
                })}
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  )
}
