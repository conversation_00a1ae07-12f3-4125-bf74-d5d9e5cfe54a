"""
Submission Processing Service Interface

This module defines the interface for submission processing services.
"""

from typing import Dict, Any, List, Optional, Union, Protocol
from bson import ObjectId

from app.models.deal import Deal
from app.models.thesis import InvestmentThesis


class SubmissionProcessingResult:
    """Result of submission processing."""
    
    def __init__(
        self,
        success: bool,
        deal_id: Optional[str] = None,
        excluded: bool = False,
        exclusion_reason: Optional[str] = None,
        matching_theses: Optional[List[Dict[str, Any]]] = None,
        enrichment_job_id: Optional[str] = None,
        error: Optional[str] = None
    ):
        self.success = success
        self.deal_id = deal_id
        self.excluded = excluded
        self.exclusion_reason = exclusion_reason
        self.matching_theses = matching_theses or []
        self.enrichment_job_id = enrichment_job_id
        self.error = error
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "success": self.success,
            "deal_id": self.deal_id,
            "excluded": self.excluded,
            "exclusion_reason": self.exclusion_reason,
            "matching_theses": self.matching_theses,
            "enrichment_job_id": self.enrichment_job_id,
            "error": self.error
        }


class SubmissionProcessingServiceInterface(Protocol):
    """Interface for submission processing services."""

    async def initialize(self) -> None:
        """Initialize service dependencies."""
        ...

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        ...

    async def process_submission(
        self,
        submission_id: Union[str, ObjectId],
        form_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        answers: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
        created_by: Optional[Union[str, ObjectId]] = None
    ) -> SubmissionProcessingResult:
        """
        Process a form submission through the complete workflow.
        
        This method orchestrates the entire submission processing pipeline:
        1. Check exclusion filters
        2. Find matching theses
        3. Score against matching theses
        4. Create deal record
        5. Enqueue enrichment jobs
        
        Args:
            submission_id: ID of the submission
            form_id: ID of the form
            org_id: Organization ID
            answers: Form answers
            metadata: Optional submission metadata
            created_by: Optional user ID who created the submission
            
        Returns:
            SubmissionProcessingResult with processing details
        """
        ...

    async def check_exclusion_filters(
        self,
        form_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        answers: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Check if submission should be excluded.
        
        Args:
            form_id: Form ID
            org_id: Organization ID
            answers: Form answers
            
        Returns:
            Exclusion check result
        """
        ...

    async def find_matching_theses(
        self,
        form_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        answers: Dict[str, Any]
    ) -> List[InvestmentThesis]:
        """
        Find all theses that match the submission.
        
        Args:
            form_id: Form ID
            org_id: Organization ID
            answers: Form answers
            
        Returns:
            List of matching theses
        """
        ...

    async def score_against_theses(
        self,
        theses: List[InvestmentThesis],
        answers: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Score submission against multiple theses.
        
        Args:
            theses: List of theses to score against
            answers: Form answers
            
        Returns:
            List of scoring results with thesis_id and scores
        """
        ...

    async def create_deal_from_processing(
        self,
        submission_id: Union[str, ObjectId],
        form_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        answers: Dict[str, Any],
        exclusion_result: Dict[str, Any],
        thesis_scores: List[Dict[str, Any]],
        metadata: Optional[Dict[str, Any]] = None,
        created_by: Optional[Union[str, ObjectId]] = None
    ) -> Optional[Deal]:
        """
        Create a deal record from processing results.
        
        Args:
            submission_id: Submission ID
            form_id: Form ID
            org_id: Organization ID
            answers: Form answers
            exclusion_result: Exclusion filter result
            thesis_scores: Thesis scoring results
            metadata: Optional metadata
            created_by: Optional creator user ID
            
        Returns:
            Created deal or None if creation fails
        """
        ...

    async def enqueue_enrichment_jobs(
        self,
        deal_id: Union[str, ObjectId],
        submission_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        form_id: Union[str, ObjectId]
    ) -> Optional[str]:
        """
        Enqueue AI enrichment jobs for the deal.
        
        Args:
            deal_id: Deal ID
            submission_id: Submission ID
            org_id: Organization ID
            form_id: Form ID
            
        Returns:
            Job ID if successful, None otherwise
        """
        ...
