"""Company service implementation."""

from typing import Any, Dict, Optional

from app.core.config import settings
from app.core.logging import get_logger
from app.services.base import BaseService
from app.storage.rds_storage import RDSStorage

logger = get_logger(__name__)


class CompanyService(BaseService):
    """Service for managing company data and operations."""

    def __init__(self):
        super().__init__()
        self.rds = RDSStorage()

    async def initialize(self) -> None:
        """Initialize the company service."""
        try:
            await self.rds.initialize()
            logger.info("Company service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize company service: {e}")
            raise

    async def cleanup(self) -> None:
        """Cleanup company service resources."""
        try:
            await self.rds.cleanup()
            logger.info("Company service cleaned up successfully")
        except Exception as e:
            logger.error(f"Error during company service cleanup: {e}")

    async def get_company_with_all_relations(self, domain: str) -> Optional[Dict[str, Any]]:
        """
        Get a company with all related data by domain.

        Args:
            domain: The company domain

        Returns:
            Dictionary with company data and all relations, or None if not found
        """
        try:
            logger.info(f"Getting company with relations: {domain}")
            result = await self.rds.get_company_with_all_relations(domain)

            if result:
                # Convert to camelCase for frontend compatibility
                return self._convert_to_camel_case(result)

            return None

        except Exception as e:
            logger.error(f"Error getting company with relations {domain}: {e}")
            await self.handle_error(e, {"domain": domain})
            return None

    def _convert_to_camel_case(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert snake_case keys to camelCase for frontend compatibility.
        
        Args:
            data: Dictionary with snake_case keys
            
        Returns:
            Dictionary with camelCase keys
        """
        def to_camel_case(snake_str: str) -> str:
            components = snake_str.split('_')
            return components[0] + ''.join(word.capitalize() for word in components[1:])
        
        def convert_dict(obj: Any) -> Any:
            if isinstance(obj, dict):
                return {to_camel_case(k): convert_dict(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_dict(item) for item in obj]
            else:
                return obj
        
        return convert_dict(data)
