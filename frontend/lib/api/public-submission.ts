// API client for public submission flow

const getApiBase = () => {
  if (typeof window !== 'undefined') {
    return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
  }
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
};

const API_BASE = getApiBase();

export interface PublicLoginRequest {
  email: string;
  name?: string;
  token: string;
}

export interface PublicLoginResponse {
  message: string;
  email: string;
  requires_verification: boolean;
}

export interface MagicLinkVerifyResponse {
  access_token: string;
  refresh_token: string;
  user: {
    id: string;
    email: string;
    name?: string;
    type: 'public';
  };
  submission?: {
    id: string;
    status: string;
    progress: number;
    can_edit: boolean;
    answers?: Record<string, any>;
    last_question?: string;
  };
  can_edit: boolean;
  redirect_url?: string;
}

export interface PublicSubmission {
  id: string;
  status: 'draft' | 'submitted';
  progress: number;
  can_edit: boolean;
  answers: Record<string, any>;
  last_question?: string;
}

export interface SubmissionProgressRequest {
  answers: Record<string, any>;
  progress: number;
  last_question?: string;
}

export interface SubmissionProgressResponse {
  id: string;
  status: string;
  progress: number;
  message: string;
}

export interface SubmitResponse {
  id: string;
  status: string;
  message: string;
  public_submission_id: string;
}

export interface TokenRefreshResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

export class PublicSubmissionAPI {
  /**
   * Login with email and sharing token
   */
  static async login(request: PublicLoginRequest): Promise<PublicLoginResponse> {
    try {
      const response = await fetch(`${API_BASE}/public/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to send magic link');
      }

      return response.json();
    } catch (error) {
      console.error('Public login error:', error);
      throw error;
    }
  }

  /**
   * Verify magic link token
   */
  static async verifyMagicLink(token: string): Promise<MagicLinkVerifyResponse> {
    try {
      const response = await fetch(`${API_BASE}/public/magic-link/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({token}),
      });


      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Invalid or expired magic link');
      }

      return response.json();
    } catch (error) {
      console.error('Magic link verification error:', error);
      throw error;
    }
  }

  /**
   * Get submission by token and email
   */
  static async getSubmission(token: string, email: string): Promise<PublicSubmission> {
    try {
      const response = await fetch(
        `${API_BASE}/public/submission/${token}?email=${encodeURIComponent(email)}`
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to get submission');
      }

      return response.json();
    } catch (error) {
      console.error('Get submission error:', error);
      throw error;
    }
  }

  /**
   * Save submission progress with automatic token refresh
   */
  static async saveProgress(
    submissionId: string,
    request: SubmissionProgressRequest,
    accessToken?: string
  ): Promise<SubmissionProgressResponse> {
    const attemptSave = async (token?: string): Promise<SubmissionProgressResponse> => {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${API_BASE}/public/submission/${submissionId}/progress`, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        
        // Check if it's a token expiration error
        if (response.status === 401 && errorData.detail?.includes('expired')) {
          const error = new Error(errorData.detail || 'Token has expired');
          (error as any).isTokenExpired = true;
          throw error;
        }
        
        throw new Error(errorData.detail || 'Failed to save progress');
      }

      return response.json();
    };

    try {
      // First attempt with current token
      return await attemptSave(accessToken);
    } catch (error: any) {
      // If token expired, try to refresh and retry
      if (error.isTokenExpired && accessToken) {
        try {
          // Get refresh token from localStorage
          const refreshToken = typeof window !== 'undefined' 
            ? localStorage.getItem('public_refresh_token') 
            : null;
            
          if (refreshToken) {
            console.log('Attempting token refresh...');
            const refreshResponse = await this.refreshToken(refreshToken);
            
            // Update tokens in localStorage
            if (typeof window !== 'undefined') {
              localStorage.setItem('public_access_token', refreshResponse.access_token);
              localStorage.setItem('public_refresh_token', refreshResponse.refresh_token);
            }
            
            // Retry the original request with new token
            console.log('Retrying request with new token...');
            return await attemptSave(refreshResponse.access_token);
          }
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
          // If refresh fails, throw the original error
          throw error;
        }
      }
      
      // If not a token error or refresh failed, throw original error
      throw error;
    }
  }

  /**
   * Submit final submission with automatic token refresh
   */
  static async submitSubmission(
    submissionId: string,
    accessToken?: string
  ): Promise<SubmitResponse> {
    const attemptSubmit = async (token?: string): Promise<SubmitResponse> => {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${API_BASE}/public/submission/${submissionId}/submit`, {
        method: 'POST',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        
        // Check if it's a token expiration error
        if (response.status === 401 && errorData.detail?.includes('expired')) {
          const error = new Error(errorData.detail || 'Token has expired');
          (error as any).isTokenExpired = true;
          throw error;
        }
        
        throw new Error(errorData.detail || 'Failed to submit');
      }

      return response.json();
    };

    try {
      // First attempt with current token
      return await attemptSubmit(accessToken);
    } catch (error: any) {
      // If token expired, try to refresh and retry
      if (error.isTokenExpired && accessToken) {
        try {
          // Get refresh token from localStorage
          const refreshToken = typeof window !== 'undefined' 
            ? localStorage.getItem('public_refresh_token') 
            : null;
            
          if (refreshToken) {
            console.log('Attempting token refresh for submission...');
            const refreshResponse = await this.refreshToken(refreshToken);
            
            // Update tokens in localStorage
            if (typeof window !== 'undefined') {
              localStorage.setItem('public_access_token', refreshResponse.access_token);
              localStorage.setItem('public_refresh_token', refreshResponse.refresh_token);
            }
            
            // Retry the original request with new token
            console.log('Retrying submission with new token...');
            return await attemptSubmit(refreshResponse.access_token);
          }
        } catch (refreshError) {
          console.error('Token refresh failed during submission:', refreshError);
          // If refresh fails, throw the original error
          throw error;
        }
      }
      
      // If not a token error or refresh failed, throw original error
      throw error;
    }
  }

  /**
   * Get sharing token details (form info, organization, etc.)
   */
  static async getTokenDetails(token: string): Promise<{
    form: any;
    organization: any;
    sharing_config: any;
    branding?: any;
  }> {
    try {
      const response = await fetch(`${API_BASE}/forms/share/${token}/details`);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('This form link has expired or is invalid.');
        }
        if (response.status >= 500) {
          throw new Error('Server error. Please try again later.');
        }
        throw new Error('Failed to load form details.');
      }

      return response.json();
    } catch (error) {
      console.error('Get token details error:', error);
      throw error;
    }
  }

  /**
   * Refresh access token using refresh token
   */
  static async refreshToken(refreshToken: string): Promise<TokenRefreshResponse> {
    try {
      const response = await fetch(`${API_BASE}/api/v1/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({refresh_token: refreshToken}),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        
        // If refresh token is invalid/expired (401) or any other client error
        if (response.status === 401 || (response.status >= 400 && response.status < 500)) {
          console.log('🚪 Public refresh token invalid/expired, clearing auth state');
          
          // Clear public auth state
          if (typeof window !== 'undefined') {
            localStorage.removeItem('public_access_token');
            localStorage.removeItem('public_refresh_token');
            localStorage.removeItem('public_user');
            localStorage.removeItem('public_submission');
          }
          
          // For public forms, we don't redirect to login - just clear the auth state
          // The form will handle this gracefully
          throw new Error('Authentication session expired');
        }
        
        throw new Error(errorData.detail || 'Failed to refresh token');
      }

      return response.json();
    } catch (error) {
      console.error('Token refresh error:', error);
      throw error;
    }
  }

  /**
   * Login with deal creation metadata
   */
  static async loginWithDealCreation(
    email: string,
    name: string,
    token: string,
    dealMetadata: {
      company_name: string;
      company_website?: string;
      stage?: string;
      sector?: string;
      notes?: string;
    },
    createdBy?: string
  ): Promise<PublicLoginResponse> {
    try {
      const response = await fetch(`${API_BASE}/public/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          name,
          token,
          deal_metadata: dealMetadata,
          ...(createdBy ? { created_by: createdBy } : {}),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Login failed');
      }

      return response.json();
    } catch (error) {
      console.error('Login with deal creation error:', error);
      throw error;
    }
  }

  /**
   * Get or create a sharing token for a form
   */
  static async getOrCreateSharingToken(formId: string): Promise<{ token: string; url: string }> {
    try {
      // First, try to get existing sharing configs for this form
      const configResponse = await fetch(`${API_BASE}/sharing/configs?resource_type=form&resource_id=${formId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'X-ORG-ID': localStorage.getItem('orgId') || '',
        },
      });

      let configId: string;

      if (configResponse.ok) {
        const configs = await configResponse.json();
        if (configs.length > 0) {
          configId = configs[0]._id;
        } else {
          // Create new sharing config
          const createConfigResponse = await fetch(`${API_BASE}/sharing/configs`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'X-ORG-ID': localStorage.getItem('orgId') || '',
            },
            body: JSON.stringify({
              resource_type: 'form',
              resource_id: formId,
              enabled: true,
              sharing_types: ['link'],
              tracking_enabled: true,
            }),
          });

          if (!createConfigResponse.ok) {
            throw new Error('Failed to create sharing config');
          }

          const config = await createConfigResponse.json();
          configId = config._id;
        }
      } else {
        throw new Error('Failed to get sharing configs');
      }

      // Generate a sharing link
      const linkResponse = await fetch(`${API_BASE}/sharing/configs/${configId}/links`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'X-ORG-ID': localStorage.getItem('orgId') || '',
        },
        body: JSON.stringify({
          expires_at: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000), // 30 days
          metadata: { source: 'deal_creation' },
        }),
      });

      if (!linkResponse.ok) {
        throw new Error('Failed to create sharing link');
      }

      const link = await linkResponse.json();
      return {
        token: link.token,
        url: link.url,
      };
    } catch (error) {
      console.error('Error getting or creating sharing token:', error);
      throw error;
    }
  }
}
