# Apollo Company Enrichment Setup Guide

## 🎯 Overview

This guide walks you through setting up the Apollo-based company enrichment pipeline for TractionX. The pipeline uses Apollo's `/api/v1/organizations/enrich` endpoint to fetch comprehensive company data.

## 📋 Prerequisites

1. **Apollo API Account**: You need an active Apollo account with API access
2. **Apollo API Key**: Obtain your API key from Apollo dashboard
3. **Database**: PostgreSQL database for storing enriched data
4. **S3 Storage**: AWS S3 bucket for raw data backup

## 🔧 Setup Steps

### 1. Configure Environment Variables

Copy the example environment file and add your Apollo API key:

```bash
cd datapipelines
cp .env.example .env
```

Edit `.env` and set your Apollo API key:

```bash
# Apollo API Configuration
APOLLO_API_KEY=your_apollo_api_key_here
APOLLO_BASE_URL=https://api.apollo.io
APOLLO_RATE_LIMIT=1000
```

### 2. Test Apollo API Connection

Before running the full pipeline, test your Apollo API connection:

```bash
export APOLLO_API_KEY=your_apollo_api_key_here
python test_apollo_api.py
```

Expected output:
```
🧪 Apollo API Integration Test
==================================================

🔍 Testing domain: kubegrade.com
📡 Making request to: https://api.apollo.io/api/v1/organizations/enrich
📊 Response Status: 200
✅ Apollo API request successful!
🏢 Company: Kubegrade
🌐 Domain: kubegrade.com
👥 Employees: 25
🏭 Industry: Technology
📍 Location: San Francisco, CA
🔧 Technologies: 15
🏢 Departments: 6
🏷️  Keywords: 8
```

### 3. Database Setup

The pipeline automatically creates the required tables when first run. The schema includes:

- `companies_enrichment` - Main company data
- `company_keywords` - Company keywords (1:N)
- `company_technologies` - Technology stack (1:N)
- `company_department_counts` - Department headcounts (1:N)

### 4. Test Company Enrichment Pipeline

Run the pipeline test to verify everything works:

```bash
python test_company_enrichment.py
```

Expected output:
```
🧪 TractionX Company Enrichment Pipeline Tests
==================================================
✅ PASS - Code Structure
✅ PASS - Apollo Transformer
✅ PASS - Company Enrichment Task
✅ PASS - Database Schema
🎯 Overall: 4/4 tests passed
🎉 All tests passed! Company enrichment pipeline is ready.
```

## 🚀 Usage

### Trigger Company Enrichment

The pipeline is triggered with job data containing the company domain:

```python
job_data = {
    "company_id": "example.com",
    "org_id": "org_123",
    "domain": "example.com",        # Required for Apollo
    "company_name": "Example Corp", # Optional
    "job_id": "unique_job_id"       # Optional
}

# Async usage
result = await enrich_company_data(job_data)

# Sync usage (for RQ compatibility)
result = enrich_company_data_sync(job_data)
```

### Expected Response

```python
{
    "success": True,
    "job_id": "unique_job_id",
    "company_id": "example.com",
    "records_created": {
        "keywords": 5,
        "technologies": 12,
        "departments": 8
    },
    "s3_raw_data_key": "companies/org_123/example.com/raw_data_20240710_143022.json",
    "apollo_fetched": True
}
```

## 🔍 Apollo API Details

### Endpoint Used
```
GET https://api.apollo.io/api/v1/organizations/enrich?domain={domain}
```

### Headers Required
```
x-api-key: your_apollo_api_key
Content-Type: application/json
accept: application/json
Cache-Control: no-cache
```

### Sample cURL Command
```bash
curl --request GET \
     --url 'https://api.apollo.io/api/v1/organizations/enrich?domain=kubegrade.com' \
     --header 'Cache-Control: no-cache' \
     --header 'Content-Type: application/json' \
     --header 'accept: application/json' \
     --header 'x-api-key: your_apollo_api_key'
```

### Response Structure
Apollo returns a single organization object (not an array):
```json
{
  "organization": {
    "id": "apollo_id",
    "name": "Company Name",
    "primary_domain": "example.com",
    "website_url": "https://example.com",
    "industry": "Technology",
    "estimated_num_employees": 100,
    "technologies": [...],
    "departmental_head_count": {...},
    "keywords": [...]
  }
}
```

## ⚠️ Important Notes

### Domain Requirement
- **Apollo requires a company domain** - company name alone is not sufficient
- The domain should be the primary domain (e.g., "example.com" not "www.example.com")
- Subdomains may not return results

### Rate Limits
- Default rate limit: 1000 requests per minute
- Configure `APOLLO_RATE_LIMIT` in your environment
- The pipeline includes automatic rate limiting

### Data Quality
- Apollo data quality varies by company
- Not all companies will have complete technology or department data
- The pipeline handles missing data gracefully

## 🛠️ Troubleshooting

### Common Issues

**1. Authentication Error (401)**
```
❌ Authentication failed - check your Apollo API key
```
- Verify your Apollo API key is correct
- Check that your Apollo account has API access
- Ensure the key is properly set in environment variables

**2. Company Not Found (404)**
```
❌ Company not found for domain: example.com
```
- Try alternative domain formats (with/without www)
- Verify the company exists in Apollo's database
- Some smaller companies may not be in Apollo's dataset

**3. Rate Limit Exceeded (429)**
```
❌ Rate limit exceeded
```
- Reduce the `APOLLO_RATE_LIMIT` setting
- Implement delays between requests
- Consider upgrading your Apollo plan

**4. Missing Domain Error**
```
Missing domain (required for Apollo enrichment)
```
- Ensure job data includes a valid domain
- Domain cannot be empty or null
- Use the primary company domain

### Debug Mode

Enable debug logging to see detailed API interactions:

```bash
export LOG_LEVEL=DEBUG
python your_enrichment_script.py
```

## 📊 Monitoring

### Key Metrics to Monitor

1. **API Success Rate**: Percentage of successful Apollo API calls
2. **Response Times**: Apollo API response latency
3. **Data Quality**: Completeness of returned data
4. **Rate Limit Usage**: Requests per minute vs. limit
5. **Database Performance**: Insert/update times

### Logging

The pipeline logs all important events:
- Apollo API requests and responses
- Data transformation steps
- Database operations
- Error conditions

## 🔮 Next Steps

After successful setup:

1. **Integration Testing**: Test with real company domains
2. **Performance Tuning**: Optimize for your expected load
3. **Monitoring Setup**: Implement alerting for failures
4. **Data Validation**: Verify enriched data quality
5. **Scaling**: Configure multiple workers for high throughput

---

**Need Help?** Check the implementation documentation in `docs/company_enrichment_implementation.md` for detailed technical information.
