"""
Comprehensive Data Processor for TractionX Data Pipeline Service.

This module processes comprehensive enrichment data and populates the database tables:
1. company.companies - Canonical, merged, investor-facing entity
2. company.enrichment_sources - Raw data and extracted fields per source
3. company.insights - Structured LLM output (signals from site, deck, news)
4. company.links - Discovered URLs (from sitemap, scraping, AI)

The processor handles data normalization, conflict resolution, and structured storage.
"""

import json
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from app.configs import get_logger
from app.storage.rds_storage import RDSStorage

logger = get_logger(__name__)


class ComprehensiveDataProcessor:
    """
    Processes comprehensive enrichment data and populates database tables.

    Handles data extraction, normalization, conflict resolution, and structured storage
    across multiple data sources (LinkedIn, Crunchbase, Website Insights).
    """

    def __init__(self, rds_storage: RDSStorage):
        self.rds = rds_storage
        self.logger = get_logger(f"{__name__}.ComprehensiveDataProcessor")

    async def process_comprehensive_enrichment_data(
        self, enrichment_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process comprehensive enrichment data and merge into company record."""
        try:
            company_domain = enrichment_data.get("company_domain")
            org_id = enrichment_data.get("org_id")
            barrier_group_id = enrichment_data.get("barrier_group_id")

            self.logger.info(f"Processing enrichment data for {company_domain}")

            if not all([company_domain, org_id, barrier_group_id]):
                return {
                    "success": False,
                    "error": "Missing required fields: company_domain, org_id, or barrier_group_id",
                }

            # Get or create company UUID
            company_uuid = await self._get_or_create_company_uuid(
                str(company_domain), str(org_id)
            )

            # Process each data source
            processing_results = {}

            # Process LinkedIn data
            linkedin_data = enrichment_data.get("data", {}).get("linkedin_scraping")
            self.logger.info(f"LinkedIn data found: {linkedin_data is not None}")
            if linkedin_data:
                self.logger.info(f"LinkedIn status: {linkedin_data.get('status')}")
                self.logger.info(
                    f"LinkedIn data keys: {list(linkedin_data.keys()) if linkedin_data else 'None'}"
                )

            if linkedin_data and linkedin_data.get("status") == "success":
                self.logger.info(f"Processing LinkedIn data for {company_domain}")
                linkedin_result = await self._process_linkedin_data(
                    company_uuid, linkedin_data
                )
                processing_results["linkedin"] = linkedin_result
                self.logger.info(
                    f"LinkedIn processing result keys: {list(linkedin_result.keys()) if linkedin_result else 'None'}"
                )
                if linkedin_result and linkedin_result.get("linkedin_data"):
                    self.logger.info(
                        f"LinkedIn data keys: {list(linkedin_result['linkedin_data'].keys())}"
                    )
                else:
                    self.logger.warning(
                        "No linkedin_data in LinkedIn processing result"
                    )

                # Store LinkedIn enrichment source
                if linkedin_result and linkedin_result.get("status") == "success":
                    await self.rds.store_enrichment_source(
                        company_uuid=company_uuid,
                        source_name="linkedin_scraping",
                        raw_data=linkedin_data.get("raw_s3_data", {}),
                        extracted_fields=linkedin_result.get("linkedin_data", {}),
                        s3_raw_data_url=linkedin_data.get("s3_raw_data_url"),
                    )
            else:
                self.logger.warning(
                    f"No LinkedIn data or status not success for {company_domain}"
                )

            # Process Crunchbase data
            crunchbase_data = enrichment_data.get("data", {}).get("crunchbase_scraping")
            if crunchbase_data and crunchbase_data.get("status") == "success":
                self.logger.info(f"Processing Crunchbase data for {company_domain}")
                crunchbase_result = await self._process_crunchbase_data(
                    company_uuid, crunchbase_data
                )
                processing_results["crunchbase"] = crunchbase_result

                # Store Crunchbase enrichment source
                if crunchbase_result and crunchbase_result.get("status") == "success":
                    await self.rds.store_enrichment_source(
                        company_uuid=company_uuid,
                        source_name="crunchbase_scraping",
                        raw_data=crunchbase_data.get("raw_s3_data", {}),
                        extracted_fields=crunchbase_result.get("cb_data", {}),
                        s3_raw_data_url=crunchbase_data.get("s3_raw_data_url"),
                    )

            # Process website insights
            website_insights = enrichment_data.get("data", {}).get(
                "website_insights", {}
            )
            if website_insights:
                self.logger.info(f"Processing website insights for {company_domain}")
                website_result = await self._process_website_insights(
                    company_uuid, website_insights
                )
                processing_results["website"] = website_result

                # Store website insights enrichment source
                if website_result and website_result.get("status") == "success":
                    await self.rds.store_enrichment_source(
                        company_uuid=company_uuid,
                        source_name="website_insights",
                        raw_data=website_insights,
                        extracted_fields=website_result.get("insights", {}),
                        s3_raw_data_url=None,
                    )

            # Process Apollo company data
            apollo_data = enrichment_data.get("data", {}).get("apollo_data")
            if apollo_data and apollo_data.get("status") == "success":
                self.logger.info(f"Processing Apollo company data for {company_domain}")
                apollo_result = await self._process_apollo_data(
                    company_uuid, apollo_data
                )
                processing_results["apollo"] = apollo_result

                # Store Apollo enrichment source
                if apollo_result and apollo_result.get("status") == "success":
                    await self.rds.store_enrichment_source(
                        company_uuid=company_uuid,
                        source_name="apollo_data",
                        raw_data=apollo_data.get("raw_s3_data", {}),
                        extracted_fields=apollo_result.get("apollo_data", {}),
                        s3_raw_data_url=apollo_data.get("source_s3_key"),
                    )

            # Process PitchBook company data
            pitchbook_data = enrichment_data.get("data", {}).get("pitchbook_data")
            if pitchbook_data and pitchbook_data.get("status") == "success":
                self.logger.info(
                    f"Processing PitchBook company data for {company_domain}"
                )
                pitchbook_result = await self._process_pitchbook_data(
                    company_uuid, pitchbook_data
                )
                processing_results["pitchbook"] = pitchbook_result

                # Store PitchBook enrichment source
                if pitchbook_result and pitchbook_result.get("status") == "success":
                    await self.rds.store_enrichment_source(
                        company_uuid=company_uuid,
                        source_name="pitchbook_data",
                        raw_data=pitchbook_data.get("raw_s3_data", {}),
                        extracted_fields=pitchbook_result.get("pitchbook_data", {}),
                        s3_raw_data_url=pitchbook_data.get("source_s3_key"),
                    )

            # Update company record with merged data
            self.logger.info(f"Updating company record for {company_domain}")
            merged_data = await self._update_company_record(
                company_uuid, enrichment_data, processing_results
            )

            self.logger.info(
                f"Successfully processed enrichment data for {company_domain}"
            )

            return {
                "success": True,
                "company_uuid": company_uuid,
                "company_domain": company_domain,
                "org_id": org_id,
                "barrier_group_id": barrier_group_id,
                "processing_results": processing_results,
                "merged_data": merged_data,
            }

        except Exception as e:
            self.logger.error(f"Error processing comprehensive enrichment data: {e}")
            return {"success": False, "error": str(e)}

    async def _get_or_create_company_uuid(
        self, company_domain: str, org_id: str
    ) -> str:
        """Get existing company UUID or create new one."""
        try:
            # Use the new RDS method for company schema
            company_uuid = await self.rds.get_or_create_company_by_domain(
                domain=company_domain, org_id=org_id, source="comprehensive_enrichment"
            )

            self.logger.info(f"Retrieved/created company UUID: {company_uuid}")
            return company_uuid

        except Exception as e:
            self.logger.error(f"Error in _get_or_create_company_uuid: {e}")
            raise

    async def _process_linkedin_data(
        self, company_uuid: str, linkedin_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process LinkedIn data and extract relevant fields."""
        try:
            processed_data = linkedin_data.get("processed_data", {})
            # The raw_s3_data might not exist, so we'll use processed_data for all extractions

            if not processed_data:
                return {"status": "error", "error": "No processed data available"}

            # Extract comprehensive LinkedIn data directly from processed_data
            linkedin_extracted = {
                "url": processed_data.get("url"),
                "logo": processed_data.get("logo"),
                "image": processed_data.get("image"),
                "founded": processed_data.get("founded"),
                "similar": processed_data.get("similar", []),
                "updates": processed_data.get("updates", []),
                "employees": processed_data.get("employees", []),
                "followers": processed_data.get("followers"),
                "company_id": processed_data.get("company_id"),
                "industries": processed_data.get("industries"),
                "company_size": processed_data.get("company_size"),
                "country_code": processed_data.get("country_code"),
                "processed_at": processed_data.get("processed_at"),
                "source_s3_key": processed_data.get("source_s3_key"),
                "organization_type": processed_data.get("organization_type"),
                "get_directions_url": processed_data.get("get_directions_url"),
                "formatted_locations": processed_data.get("formatted_locations", []),
                "employees_in_linkedin": processed_data.get("employees_in_linkedin"),
            }

            # Enhanced field extraction for missing data
            enhanced_fields = {}

            # Extract formatted locations
            if not enhanced_fields.get("formatted_locations"):
                enhanced_fields["formatted_locations"] = (
                    self._extract_formatted_locations_from_processed(processed_data)
                )

            # Extract industries
            if not enhanced_fields.get("industries"):
                enhanced_fields["industries"] = self._extract_industries_from_processed(
                    processed_data
                )

            # Extract directions URL
            if not enhanced_fields.get("get_directions_url"):
                enhanced_fields["get_directions_url"] = self._extract_directions_url(
                    processed_data
                )

            # Extract followers
            if not enhanced_fields.get("followers"):
                enhanced_fields["followers"] = self._extract_followers(processed_data)

            # Extract employees in LinkedIn
            if not enhanced_fields.get("employees_in_linkedin"):
                enhanced_fields["employees_in_linkedin"] = (
                    self._extract_employees_in_linkedin(processed_data)
                )

            # Extract organization type
            if not enhanced_fields.get("organization_type"):
                enhanced_fields["organization_type"] = self._extract_organization_type(
                    processed_data
                )

            # Extract industries
            if not enhanced_fields.get("industries"):
                enhanced_fields["industries"] = self._extract_industries(processed_data)

            # Extract employees data
            if not enhanced_fields.get("employees_data"):
                enhanced_fields["employees_data"] = self._extract_employees_data(
                    processed_data
                )

            # Extract image
            if not enhanced_fields.get("image"):
                enhanced_fields["image"] = self._extract_image(processed_data)

            # Extract logo
            if not enhanced_fields.get("logo"):
                enhanced_fields["logo"] = self._extract_logo(processed_data)

            # Extract LinkedIn URL
            if not enhanced_fields.get("linkedin_url"):
                enhanced_fields["linkedin_url"] = self._extract_linkedin_url(
                    processed_data
                )

            # Extract updates
            if not enhanced_fields.get("updates"):
                enhanced_fields["updates"] = self._extract_updates(processed_data)

            # Merge enhanced fields into LinkedIn data
            linkedin_extracted.update(enhanced_fields)

            return {
                "status": "success",
                "linkedin_data": linkedin_extracted,
                "enhanced_fields": enhanced_fields,
                "processed_data": processed_data,
            }

        except Exception as e:
            self.logger.error(f"Error processing LinkedIn data: {e}")
            return {"status": "error", "error": str(e)}

    def _extract_formatted_locations_from_processed(self, processed_data: dict) -> list:
        """Extract formatted location strings from LinkedIn processed data."""
        locations = []

        # Extract from locations array
        if processed_data.get("locations"):
            if isinstance(processed_data["locations"], list):
                locations.extend(processed_data["locations"])
            else:
                locations.append(processed_data["locations"])

        # Extract from headquarters
        if processed_data.get("headquarters"):
            locations.append(processed_data["headquarters"])

        # Extract from country_code
        if processed_data.get("country_code"):
            locations.append(processed_data["country_code"])

        return list(set(locations))  # Remove duplicates

    def _extract_industries_from_processed(self, processed_data: dict) -> list:
        """Extract industries from LinkedIn processed data."""
        industries = []

        if processed_data.get("industries"):
            if isinstance(processed_data["industries"], list):
                industries.extend(processed_data["industries"])
            else:
                industries.append(processed_data["industries"])

        return list(set(industries))  # Remove duplicates

    def _extract_directions_url(self, processed_data: dict) -> Optional[str]:
        """Extract directions URL from LinkedIn data."""
        return processed_data.get("get_directions_url")

    def _extract_followers(self, processed_data: dict) -> Optional[int]:
        """Extract follower count from LinkedIn data."""
        return processed_data.get("followers")

    def _extract_employees_in_linkedin(self, processed_data: dict) -> Optional[int]:
        """Extract number of employees listed on LinkedIn."""
        return processed_data.get("employees_in_linkedin")

    def _extract_organization_type(self, processed_data: dict) -> Optional[str]:
        """Extract organization type from LinkedIn data."""
        return processed_data.get("organization_type")

    def _extract_industries(self, processed_data: dict) -> list:
        """Extract industries array from LinkedIn data."""
        industries = []

        if processed_data.get("industries"):
            if isinstance(processed_data["industries"], list):
                industries.extend(processed_data["industries"])
            else:
                industries.append(processed_data["industries"])

        return list(set(industries))  # Remove duplicates

    def _extract_employees_data(self, processed_data: dict) -> dict:
        """Extract detailed employee breakdown from LinkedIn data."""
        return processed_data.get("employees", {})

    def _extract_image(self, processed_data: dict) -> Optional[str]:
        """Extract background image URL from LinkedIn data."""
        return processed_data.get("image")

    def _extract_logo(self, processed_data: dict) -> Optional[str]:
        """Extract logo URL from LinkedIn data."""
        return processed_data.get("logo")

    def _extract_linkedin_url(self, processed_data: dict) -> Optional[str]:
        """Extract LinkedIn URL from data."""
        return processed_data.get("url")

    def _extract_updates(self, processed_data: dict) -> list:
        """Extract company updates/posts from LinkedIn data."""
        return processed_data.get("updates", [])

    async def _process_crunchbase_data(
        self, company_uuid: str, crunchbase_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process Crunchbase data and extract relevant fields."""
        try:
            processed_data = crunchbase_data.get("processed_data", {})
            raw_data = crunchbase_data.get("raw_s3_data", {}).get("raw_data", [])

            if not raw_data:
                return {"status": "error", "error": "No raw data available"}

            # Get the first (and usually only) raw data entry
            raw_entry = raw_data[0] if raw_data else {}

            # Extract basic fields from processed data
            cb_data = {
                "type": raw_entry.get("type"),
                "uuid": raw_entry.get("uuid"),
                "about": raw_entry.get("about"),
                "image": raw_entry.get("image"),
                "cb_url": raw_entry.get("url"),
                "region": raw_entry.get("region"),
                "address": raw_entry.get("address"),
                "cb_rank": raw_entry.get("cb_rank"),
                "location": raw_entry.get("location", []),
                "input_url": raw_entry.get("url"),
                "timestamp": raw_entry.get("timestamp"),
                "company_id": raw_entry.get("company_id"),
                "heat_score": raw_entry.get("heat_score"),
                "industries": raw_entry.get("industries", []),
                "ipo_status": raw_entry.get("ipo_status"),
                "legal_name": raw_entry.get("legal_name"),
                "company_type": raw_entry.get("company_type"),
                "growth_score": raw_entry.get("growth_score"),
                "growth_trend": raw_entry.get("growth_trend"),
                "hq_continent": raw_entry.get("hq_continent"),
                "original_url": raw_entry.get("url"),
                "phone_number": raw_entry.get("contact_phone")
                or raw_entry.get("phone_number"),
                "email_address": raw_entry.get("contact_email")
                or raw_entry.get("email_address"),
                "featured_list": raw_entry.get("featured_list", []),
                "builtwith_tech": raw_entry.get("builtwith_tech", []),
                "monthly_visits": raw_entry.get("monthly_visits"),
                "company_industry": raw_entry.get("company_industry"),
                "company_overview": raw_entry.get("company_overview"),
                "full_description": raw_entry.get("full_description"),
                "operating_status": raw_entry.get("operating_status"),
                "active_tech_count": raw_entry.get("active_tech_count"),
                "social_media_links": raw_entry.get("social_media_links", []),
                "headquarters_regions": raw_entry.get("headquarters_regions", []),
                "semrush_last_updated": raw_entry.get("semrush_last_updated"),
                "monthly_visits_growth": raw_entry.get("monthly_visits_growth"),
                "num_employee_profiles": raw_entry.get("num_employee_profiles"),
                "products_and_services": raw_entry.get("products_and_services"),
                "semrush_location_list": raw_entry.get("semrush_location_list", []),
                "technology_highlights": raw_entry.get("technology_highlights", {}),
                "company_activity_level": raw_entry.get("company_activity_level"),
                "semrush_visits_mom_pct": raw_entry.get("semrush_visits_mom_pct"),
                "web_traffic_by_semrush": raw_entry.get("web_traffic_by_semrush", {}),
                "semrush_visits_latest_month": raw_entry.get(
                    "semrush_visits_latest_month"
                ),
                "builtwith_num_technologies_used": raw_entry.get(
                    "builtwith_num_technologies_used"
                ),
            }

            # Enhanced field extraction for missing data
            enhanced_fields = {}

            # Extract stage information
            stage = self._extract_stage_from_crunchbase(raw_entry)
            if stage:
                enhanced_fields["stage"] = stage

            # Extract sector information
            sector = self._extract_sector_from_crunchbase(raw_entry)
            if sector:
                enhanced_fields["sector"] = sector

            # Extract enhanced industry information
            industry_info = self._extract_enhanced_industry_from_crunchbase(raw_entry)
            if industry_info.get("industry"):
                enhanced_fields["industry"] = industry_info["industry"]
            if industry_info.get("sub_industry"):
                enhanced_fields["sub_industry"] = industry_info["sub_industry"]

            # Merge enhanced fields into processed data
            processed_data.update(enhanced_fields)

            # Store the enhanced processed data
            # Note: We'll let the main processor handle the company update
            # await self.rds.update_company(
            #     company_uuid=company_uuid,
            #     update_data=processed_data
            # )

            return {
                "status": "success",
                "cb_data": cb_data,
                "enhanced_fields": enhanced_fields,
                "processed_data": processed_data,
            }

        except Exception as e:
            self.logger.error(f"Error processing Crunchbase data: {e}")
            return {"status": "error", "error": str(e)}

    def _extract_stage_from_crunchbase(
        self, raw_entry: Dict[str, Any]
    ) -> Optional[str]:
        """Extract stage information from Crunchbase raw data."""
        # Check IPO status first
        ipo_status = raw_entry.get("ipo_status")
        if ipo_status:
            if ipo_status == "private":
                # Determine stage based on company maturity indicators
                founded_date = raw_entry.get("founded_date")
                employee_count = raw_entry.get("num_employees")
                growth_score = raw_entry.get("growth_score")

                if founded_date:
                    founded_year = self._extract_year_from_date(founded_date)
                    if founded_year:
                        current_year = 2025
                        years_old = current_year - founded_year

                        if years_old <= 1:
                            return "Seed"
                        elif years_old <= 3:
                            return "Early Stage"
                        elif years_old <= 5:
                            return "Growth Stage"
                        else:
                            return "Established"

                # Fallback based on employee count
                if employee_count:
                    if "1-10" in employee_count:
                        return "Seed"
                    elif "11-50" in employee_count:
                        return "Early Stage"
                    elif "51-200" in employee_count:
                        return "Growth Stage"
                    else:
                        return "Established"

                # Fallback based on growth score
                if growth_score and growth_score > 80:
                    return "Growth Stage"
                elif growth_score and growth_score > 60:
                    return "Early Stage"
                else:
                    return "Seed"

            elif ipo_status == "public":
                return "Public"

        return None

    def _extract_sector_from_crunchbase(
        self, raw_entry: Dict[str, Any]
    ) -> Optional[str]:
        """Extract sector information from Crunchbase raw data."""
        # Check headquarters regions first
        headquarters_regions = raw_entry.get("headquarters_regions", [])
        if headquarters_regions:
            for region in headquarters_regions:
                region_value = region.get("value", "")
                if "Asia-Pacific" in region_value or "APAC" in region_value:
                    return "Asia-Pacific"
                elif "ASEAN" in region_value:
                    return "Southeast Asia"
                elif "Europe" in region_value:
                    return "Europe"
                elif "North America" in region_value:
                    return "North America"

        # Fallback to company industry
        company_industry = raw_entry.get("company_industry")
        if company_industry:
            if company_industry in ["Aerospace", "Space Research and Technology"]:
                return "Aerospace & Defense"
            elif (
                "AI" in company_industry
                or "Artificial Intelligence" in company_industry
            ):
                return "Technology"
            elif "Data" in company_industry:
                return "Technology"

        # Fallback to hq_continent
        hq_continent = raw_entry.get("hq_continent")
        if hq_continent:
            if hq_continent == "Asia":
                return "Asia-Pacific"
            elif hq_continent == "Europe":
                return "Europe"
            elif hq_continent == "North America":
                return "North America"

        return None

    def _extract_enhanced_industry_from_crunchbase(
        self, raw_entry: Dict[str, Any]
    ) -> Dict[str, Optional[str]]:
        """Extract enhanced industry information from Crunchbase raw data."""
        result: Dict[str, Optional[str]] = {"industry": None, "sub_industry": None}

        # Extract primary industry from industries array
        industries = raw_entry.get("industries", [])
        if industries:
            primary_industry = industries[0].get("value") if industries else None
            if primary_industry:
                result["industry"] = primary_industry

                # Create sub-industry from additional industries
                if len(industries) > 1:
                    sub_industries = [
                        ind.get("value") for ind in industries[1:] if ind.get("value")
                    ]
                    if sub_industries:
                        result["sub_industry"] = " | ".join(sub_industries)

        # Fallback to company_industry if no industries array
        if not result["industry"]:
            company_industry = raw_entry.get("company_industry")
            if company_industry:
                result["industry"] = company_industry

        return result

    async def _process_apollo_data(
        self, company_uuid: str, apollo_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process Apollo company data."""
        try:
            # Extract processed data from Apollo S3 storage
            raw_s3_data = apollo_data.get("raw_s3_data", {})
            source_s3_key = apollo_data.get("source_s3_key")

            # Try to get processed data from the raw S3 data
            processed_data = {}
            if raw_s3_data and isinstance(raw_s3_data, dict):
                processed_data = raw_s3_data.get("processed_data", {})

            if not processed_data:
                return {
                    "status": "error",
                    "error": "No processed Apollo data available",
                }

            # Extract company data and departments data
            company_data = processed_data.get("company_data", {})
            departments_data = processed_data.get("departments_data", [])

            # Store departments data in the new company.departments table
            if departments_data:
                # Convert to the format expected by store_company_departments
                departments_for_storage = []
                for dept in departments_data:
                    departments_for_storage.append({
                        "department_name": dept.get("department_name", ""),
                        "head_count": dept.get("head_count", 0),
                        "confidence_score": dept.get("confidence_score", 0.8),
                    })

                # Store departments using the new method
                await self.rds.store_company_departments(
                    company_uuid=company_uuid,
                    departments=departments_for_storage,
                    source="apollo_data",
                )

            return {
                "status": "success",
                "apollo_data": company_data,
                "departments_count": len(departments_data),
                "source_s3_key": source_s3_key,
            }

        except Exception as e:
            self.logger.error(f"Error processing Apollo data: {e}")
            return {"status": "error", "error": str(e)}

    async def _process_pitchbook_data(
        self, company_uuid: str, pitchbook_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process PitchBook company data."""
        try:
            # Extract processed data from PitchBook S3 storage
            raw_s3_data = pitchbook_data.get("raw_s3_data", {})
            source_s3_key = pitchbook_data.get("source_s3_key")

            # Try to get processed data from the raw S3 data
            processed_data = {}
            if raw_s3_data and isinstance(raw_s3_data, dict):
                processed_data = raw_s3_data.get("processed_data", {})

            if not processed_data:
                return {
                    "status": "error",
                    "error": "No processed PitchBook data available",
                }

            # Extract company data from processed data
            company_data = processed_data

            return {
                "status": "success",
                "pitchbook_data": company_data,
                "source_s3_key": source_s3_key,
            }

        except Exception as e:
            self.logger.error(f"Error processing PitchBook data: {e}")
            return {"status": "error", "error": str(e)}

    async def _process_website_insights(
        self, company_uuid: str, website_insights: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process website insights data."""
        try:
            insights_count = 0
            successful_urls = 0
            failed_urls = 0

            for url, insight_data in website_insights.items():
                if insight_data.get("status") == "success" and insight_data.get("data"):
                    data = insight_data["data"]
                    insights = data.get("insights", [])

                    # Store each insight in company.insights using new RDS method
                    for insight in insights:
                        await self.rds.store_company_insight(
                            company_uuid,
                            {
                                "source_url": url,
                                "label": insight.get("label", ""),
                                "value": insight.get("value", ""),
                                "type": insight.get("type", "general"),
                                "confidence": 0.8,  # Default confidence for website insights
                                "source": "website_scraper",
                            },
                        )
                        insights_count += 1

                    successful_urls += 1
                else:
                    failed_urls += 1

            return {
                "status": "success",
                "insights_count": insights_count,
                "successful_urls": successful_urls,
                "failed_urls": failed_urls,
            }

        except Exception as e:
            self.logger.error(f"Error processing website insights: {e}")
            return {"status": "error", "error": str(e)}

    async def _store_company_insight(
        self, company_uuid: str, insight: Dict[str, Any]
    ) -> None:
        """Store a single company insight in company.insights."""
        try:
            await self.rds.store_company_insight(company_uuid, insight)
        except Exception as e:
            # Log the error but don't fail the entire process
            # The upsert functionality should handle duplicates gracefully
            self.logger.warning(
                f"Error storing company insight (will be handled by upsert): {e}"
            )
            # Don't re-raise the exception to avoid failing the entire enrichment process

    async def _update_company_record(
        self,
        company_uuid: str,
        enrichment_data: Dict[str, Any],
        processing_results: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Update main company record with merged data from all sources."""
        try:
            # Collect data from all sources
            merged_data = self._merge_company_data(enrichment_data, processing_results)

            self.logger.info(f"Merged data keys: {list(merged_data.keys())}")
            self.logger.info(
                f"Merged data sample: {dict(list(merged_data.items())[:5])}"
            )
            self.logger.info(f"Full merged data: {merged_data}")

            # Check if merged_data is actually populated
            non_null_values = {
                k: v for k, v in merged_data.items() if v is not None and v != ""
            }
            self.logger.info(f"Non-null values in merged data: {non_null_values}")

            if not non_null_values:
                self.logger.error("CRITICAL: Merged data contains no non-null values!")
                return {
                    "status": "error",
                    "error": "No data to update - merged data is empty",
                }

            # Update company record in company.companies using new RDS method
            success = await self.rds.update_company_record_company_schema(
                company_uuid, merged_data
            )

            if success:
                self.logger.info(
                    f"Successfully updated company record with {len(non_null_values)} fields"
                )
                return {"status": "success", "updated_fields": list(merged_data.keys())}
            else:
                self.logger.error("Database update returned False - no rows updated")
                return {"status": "error", "error": "Failed to update company record"}

        except Exception as e:
            self.logger.error(f"Error updating company record: {e}")
            return {"status": "error", "error": str(e)}

    def _merge_company_data(
        self, enrichment_data: Dict[str, Any], processing_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Merge data from multiple sources with conflict resolution."""
        merged = {}

        # Get data sources directly from enrichment_data
        data_sources = enrichment_data.get("data", {})

        self.logger.info(
            f"Starting data merge. Available sources: {list(data_sources.keys())}"
        )
        self.logger.info(f"Processing results keys: {list(processing_results.keys())}")

        # Priority order: LinkedIn > Crunchbase > Apollo > PitchBook > Website Insights
        sources = [
            "linkedin_scraping",
            "crunchbase_scraping",
            "apollo_data",
            "pitchbook_data",
            "website_insights",
        ]

        for source in sources:
            source_data = data_sources.get(source, {})

            # Check if source has successful data
            if source_data.get("status") == "success":
                self.logger.info(f"Processing {source} data")

                # Try processed_data first
                processed_data = source_data.get("processed_data", {})

                # If processed_data is empty/null, try raw_s3_data
                if not processed_data and source_data.get("raw_s3_data"):
                    raw_data = source_data.get("raw_s3_data", {})
                    if isinstance(raw_data, dict) and raw_data.get("raw_data"):
                        # Extract from first raw data entry
                        raw_entry = (
                            raw_data["raw_data"][0] if raw_data["raw_data"] else {}
                        )
                        processed_data = raw_entry
                        self.logger.info(f"Using raw data for {source}")

                self.logger.info(
                    f"{source} processed_data keys: {list(processed_data.keys()) if processed_data else 'None'}"
                )
                self.logger.info(
                    f"{source} processed_data sample: {dict(list(processed_data.items())[:3]) if processed_data else 'None'}"
                )

                # Merge fields with priority - only set if not already set
                if not merged.get("name") and processed_data.get("name"):
                    merged["name"] = processed_data["name"]
                    self.logger.info(
                        f"Set name from {source}: {processed_data['name']}"
                    )

                if not merged.get("description") and (
                    processed_data.get("about")
                    or processed_data.get("description")
                    or processed_data.get("full_description")
                ):
                    merged["description"] = (
                        processed_data.get("about")
                        or processed_data.get("description")
                        or processed_data.get("full_description")
                    )
                    self.logger.info(f"Set description from {source}")

                if not merged.get("industry") and processed_data.get("industry"):
                    industry = processed_data["industry"]
                    if isinstance(industry, list):
                        merged["industry"] = " | ".join(
                            industry[:3]
                        )  # Limit to 3 industries
                    else:
                        merged["industry"] = str(industry)
                    self.logger.info(
                        f"Set industry from {source}: {merged['industry']}"
                    )

                if not merged.get("sub_industry") and processed_data.get(
                    "sub_industry"
                ):
                    sub_industry = processed_data["sub_industry"]
                    if isinstance(sub_industry, list):
                        merged["sub_industry"] = " | ".join(
                            sub_industry[:3]
                        )  # Limit to 3 sub-industries
                    else:
                        merged["sub_industry"] = str(sub_industry)
                    self.logger.info(
                        f"Set sub_industry from {source}: {merged['sub_industry']}"
                    )

                if not merged.get("employee_count") and processed_data.get(
                    "employee_count"
                ):
                    merged["employee_count"] = processed_data["employee_count"]
                    self.logger.info(
                        f"Set employee_count from {source}: {processed_data['employee_count']}"
                    )

                if not merged.get("employee_count_range") and processed_data.get(
                    "employee_count_range"
                ):
                    merged["employee_count_range"] = processed_data[
                        "employee_count_range"
                    ]
                    self.logger.info(
                        f"Set employee_count_range from {source}: {processed_data['employee_count_range']}"
                    )

                if not merged.get("founded_year") and processed_data.get(
                    "founded_year"
                ):
                    merged["founded_year"] = processed_data["founded_year"]
                    self.logger.info(
                        f"Set founded_year from {source}: {processed_data['founded_year']}"
                    )

                if not merged.get("website") and processed_data.get("website"):
                    merged["website"] = processed_data["website"]
                    self.logger.info(
                        f"Set website from {source}: {processed_data['website']}"
                    )

                if not merged.get("linkedin_url") and processed_data.get(
                    "linkedin_url"
                ):
                    merged["linkedin_url"] = processed_data["linkedin_url"]
                    self.logger.info(
                        f"Set linkedin_url from {source}: {processed_data['linkedin_url']}"
                    )

                if not merged.get("twitter_url") and processed_data.get("twitter_url"):
                    merged["twitter_url"] = processed_data["twitter_url"]
                    self.logger.info(
                        f"Set twitter_url from {source}: {processed_data['twitter_url']}"
                    )

                if not merged.get("facebook_url") and processed_data.get(
                    "facebook_url"
                ):
                    merged["facebook_url"] = processed_data["facebook_url"]
                    self.logger.info(
                        f"Set facebook_url from {source}: {processed_data['facebook_url']}"
                    )

                if not merged.get("email") and processed_data.get("email"):
                    merged["email"] = processed_data["email"]
                    self.logger.info(
                        f"Set email from {source}: {processed_data['email']}"
                    )

                if not merged.get("phone") and processed_data.get("phone"):
                    merged["phone"] = processed_data["phone"]
                    self.logger.info(
                        f"Set phone from {source}: {processed_data['phone']}"
                    )

                # LinkedIn-specific fields
                if source == "linkedin_scraping":
                    if not merged.get("company_type") and processed_data.get(
                        "organization_type"
                    ):
                        merged["company_type"] = processed_data["organization_type"]
                        self.logger.info(
                            f"Set company_type from LinkedIn: {processed_data['organization_type']}"
                        )

                    if not merged.get("hq_location") and processed_data.get(
                        "headquarters"
                    ):
                        merged["hq_location"] = processed_data["headquarters"]
                        self.logger.info(
                            f"Set hq_location from LinkedIn: {processed_data['headquarters']}"
                        )

                    # Map LinkedIn industries to industry field
                    if not merged.get("industry") and processed_data.get("industries"):
                        industries = processed_data["industries"]
                        if isinstance(industries, list):
                            merged["industry"] = " | ".join(
                                industries[:3]
                            )  # Limit to 3 industries
                        else:
                            merged["industry"] = str(industries)
                        self.logger.info(
                            f"Set industry from LinkedIn industries: {merged['industry']}"
                        )

                    # Map LinkedIn company size to employee count range
                    if not merged.get("employee_count_range") and processed_data.get(
                        "company_size"
                    ):
                        merged["employee_count_range"] = processed_data["company_size"]
                        self.logger.info(
                            f"Set employee_count_range from LinkedIn: {processed_data['company_size']}"
                        )

                    # Map LinkedIn employee count
                    if not merged.get("employee_count") and processed_data.get(
                        "employees_in_linkedin"
                    ):
                        merged["employee_count"] = processed_data[
                            "employees_in_linkedin"
                        ]
                        self.logger.info(
                            f"Set employee_count from LinkedIn: {processed_data['employees_in_linkedin']}"
                        )

                    # Map LinkedIn founded year
                    if not merged.get("founded_year") and processed_data.get("founded"):
                        merged["founded_year"] = processed_data["founded"]
                        self.logger.info(
                            f"Set founded_year from LinkedIn: {processed_data['founded']}"
                        )

                    # Map LinkedIn locations to hq_location (pack multiple locations)
                    if not merged.get("hq_location") and processed_data.get(
                        "locations"
                    ):
                        locations = processed_data.get("locations", [])
                        if isinstance(locations, list):
                            merged["hq_location"] = " | ".join(
                                locations[:3]
                            )  # Limit to 3 locations
                            self.logger.info(
                                f"Set hq_location from LinkedIn locations: {merged['hq_location']}"
                            )

                    # Map LinkedIn country codes to sub_industry (sector info)
                    if not merged.get("sub_industry") and processed_data.get(
                        "country_code"
                    ):
                        merged["sub_industry"] = (
                            f"International ({processed_data.get('country_code')})"
                        )
                        self.logger.info(
                            f"Set sub_industry from LinkedIn country_code: {merged['sub_industry']}"
                        )

                # Apollo-specific fields
                if source == "apollo_data":
                    # Map Apollo fields to company schema
                    if not merged.get("name") and processed_data.get("name"):
                        merged["name"] = processed_data["name"]
                        self.logger.info(
                            f"Set name from Apollo: {processed_data['name']}"
                        )

                    if not merged.get("domain") and processed_data.get("domain"):
                        merged["domain"] = processed_data["domain"]
                        self.logger.info(
                            f"Set domain from Apollo: {processed_data['domain']}"
                        )

                    if not merged.get("website") and processed_data.get("website_url"):
                        merged["website"] = processed_data["website_url"]
                        self.logger.info(
                            f"Set website from Apollo: {processed_data['website_url']}"
                        )

                    if not merged.get("linkedin_url") and processed_data.get(
                        "linkedin_url"
                    ):
                        merged["linkedin_url"] = processed_data["linkedin_url"]
                        self.logger.info(
                            f"Set linkedin_url from Apollo: {processed_data['linkedin_url']}"
                        )

                    if not merged.get("twitter_url") and processed_data.get(
                        "twitter_url"
                    ):
                        merged["twitter_url"] = processed_data["twitter_url"]
                        self.logger.info(
                            f"Set twitter_url from Apollo: {processed_data['twitter_url']}"
                        )

                    if not merged.get("facebook_url") and processed_data.get(
                        "facebook_url"
                    ):
                        merged["facebook_url"] = processed_data["facebook_url"]
                        self.logger.info(
                            f"Set facebook_url from Apollo: {processed_data['facebook_url']}"
                        )

                    if not merged.get("industry") and processed_data.get("industry"):
                        merged["industry"] = processed_data["industry"]
                        self.logger.info(
                            f"Set industry from Apollo: {processed_data['industry']}"
                        )

                    if not merged.get("founded_year") and processed_data.get(
                        "founded_year"
                    ):
                        merged["founded_year"] = processed_data["founded_year"]
                        self.logger.info(
                            f"Set founded_year from Apollo: {processed_data['founded_year']}"
                        )

                    if not merged.get("employee_count") and processed_data.get(
                        "employee_count"
                    ):
                        merged["employee_count"] = processed_data["employee_count"]
                        self.logger.info(
                            f"Set employee_count from Apollo: {processed_data['employee_count']}"
                        )

                    if not merged.get("description") and processed_data.get(
                        "description"
                    ):
                        merged["description"] = processed_data["description"]
                        self.logger.info(
                            f"Set description from Apollo: {processed_data['description']}"
                        )

                    if not merged.get("hq_location") and processed_data.get("address"):
                        merged["hq_location"] = processed_data["address"]
                        self.logger.info(
                            f"Set hq_location from Apollo: {processed_data['address']}"
                        )

                    # Map Apollo funding data
                    if not merged.get("funding_total") and processed_data.get(
                        "total_funding"
                    ):
                        merged["funding_total"] = processed_data["total_funding"]
                        self.logger.info(
                            f"Set funding_total from Apollo: {processed_data['total_funding']}"
                        )

                    if not merged.get("funding_stage") and processed_data.get(
                        "funding_stage"
                    ):
                        merged["funding_stage"] = processed_data["funding_stage"]
                        self.logger.info(
                            f"Set funding_stage from Apollo: {processed_data['funding_stage']}"
                        )

                    if not merged.get("revenue") and processed_data.get(
                        "annual_revenue"
                    ):
                        merged["revenue"] = processed_data["annual_revenue"]
                        self.logger.info(
                            f"Set revenue from Apollo: {processed_data['annual_revenue']}"
                        )

                # PitchBook-specific fields
                if source == "pitchbook_company_data":
                    # Map PitchBook fields to company schema
                    if not merged.get("name") and processed_data.get("company_name"):
                        merged["name"] = processed_data["company_name"]
                        self.logger.info(
                            f"Set name from PitchBook: {processed_data['company_name']}"
                        )

                    if not merged.get("description") and processed_data.get(
                        "description"
                    ):
                        merged["description"] = processed_data["description"]
                        self.logger.info(
                            f"Set description from PitchBook: {processed_data['description']}"
                        )

                    if not merged.get("founded_year") and processed_data.get(
                        "year_founded"
                    ):
                        merged["founded_year"] = processed_data["year_founded"]
                        self.logger.info(
                            f"Set founded_year from PitchBook: {processed_data['year_founded']}"
                        )

                    if not merged.get("employee_count") and processed_data.get(
                        "employees"
                    ):
                        merged["employee_count"] = processed_data["employees"]
                        self.logger.info(
                            f"Set employee_count from PitchBook: {processed_data['employees']}"
                        )

                    if not merged.get("status") and processed_data.get("status"):
                        merged["status"] = processed_data["status"]
                        self.logger.info(
                            f"Set status from PitchBook: {processed_data['status']}"
                        )

                    if not merged.get("latest_deal_type") and processed_data.get(
                        "latest_deal_type"
                    ):
                        merged["latest_deal_type"] = processed_data["latest_deal_type"]
                        self.logger.info(
                            f"Set latest_deal_type from PitchBook: {processed_data['latest_deal_type']}"
                        )

                    if not merged.get("financing_rounds") and processed_data.get(
                        "financing_rounds"
                    ):
                        merged["financing_rounds"] = processed_data["financing_rounds"]
                        self.logger.info(
                            f"Set financing_rounds from PitchBook: {processed_data['financing_rounds']}"
                        )

                    if not merged.get("latest_deal_amount") and processed_data.get(
                        "latest_deal_amount"
                    ):
                        merged["latest_deal_amount"] = processed_data[
                            "latest_deal_amount"
                        ]
                        self.logger.info(
                            f"Set latest_deal_amount from PitchBook: {processed_data['latest_deal_amount']}"
                        )

                    # Map PitchBook specific fields
                    if not merged.get("pitchbook_id") and processed_data.get("id"):
                        merged["pitchbook_id"] = processed_data["id"]
                        self.logger.info(
                            f"Set pitchbook_id from PitchBook: {processed_data['id']}"
                        )

                    if not merged.get("pitchbook_url") and processed_data.get("url"):
                        merged["pitchbook_url"] = processed_data["url"]
                        self.logger.info(
                            f"Set pitchbook_url from PitchBook: {processed_data['url']}"
                        )

                    # Map PitchBook social URLs from company_socials
                    if not merged.get("linkedin_url") and processed_data.get(
                        "company_socials"
                    ):
                        for social in processed_data["company_socials"]:
                            if "linkedin.com" in social.get("link", ""):
                                merged["linkedin_url"] = social["link"]
                                self.logger.info(
                                    f"Set linkedin_url from PitchBook socials: {social['link']}"
                                )
                                break

                    # Map PitchBook contact information
                    if processed_data.get("contact_information"):
                        for contact in processed_data["contact_information"]:
                            contact_type = contact.get("Type", "")
                            contact_value = contact.get("value", "")

                            if contact_type == "Website" and not merged.get("website"):
                                merged["website"] = contact_value
                                self.logger.info(
                                    f"Set website from PitchBook contact: {contact_value}"
                                )
                            elif contact_type == "Corporate Office" and not merged.get(
                                "hq_location"
                            ):
                                merged["hq_location"] = contact_value
                                self.logger.info(
                                    f"Set hq_location from PitchBook contact: {contact_value}"
                                )
                            elif contact_type == "Primary Industry" and not merged.get(
                                "industry"
                            ):
                                merged["industry"] = contact_value
                                self.logger.info(
                                    f"Set industry from PitchBook contact: {contact_value}"
                                )

                    # Map PitchBook competitors
                    if not merged.get("competitors") and processed_data.get(
                        "competitors"
                    ):
                        merged["competitors"] = processed_data["competitors"]
                        self.logger.info(
                            f"Set competitors from PitchBook: {len(processed_data['competitors'])} competitors"
                        )

                    # Map PitchBook FAQ
                    if not merged.get("faq") and processed_data.get("faq"):
                        merged["faq"] = processed_data["faq"]
                        self.logger.info(
                            f"Set faq from PitchBook: {len(processed_data['faq'])} FAQ items"
                        )

                    # Map PitchBook investments
                    if not merged.get("investments") and processed_data.get(
                        "investments"
                    ):
                        merged["investments"] = processed_data["investments"]
                        self.logger.info(
                            f"Set investments from PitchBook: {len(processed_data['investments'])} investments"
                        )

                    # Map PitchBook research analysis
                    if not merged.get("research_analysis") and processed_data.get(
                        "research_analysis"
                    ):
                        merged["research_analysis"] = processed_data[
                            "research_analysis"
                        ]
                        self.logger.info(
                            f"Set research_analysis from PitchBook: {len(processed_data['research_analysis'])} items"
                        )

                    # Map PitchBook patent activity
                    if not merged.get("patent_activity") and processed_data.get(
                        "patent_activity"
                    ):
                        merged["patent_activity"] = processed_data["patent_activity"]
                        self.logger.info(
                            f"Set patent_activity from PitchBook: {len(processed_data['patent_activity'])} items"
                        )

                    # Map PitchBook all investments
                    if not merged.get("all_investments") and processed_data.get(
                        "all_investments"
                    ):
                        merged["all_investments"] = processed_data["all_investments"]
                        self.logger.info(
                            f"Set all_investments from PitchBook: {len(processed_data['all_investments'])} items"
                        )

                    # Map PitchBook patents
                    if not merged.get("patents") and processed_data.get("patents"):
                        merged["patents"] = processed_data["patents"]
                        self.logger.info(
                            f"Set patents from PitchBook: {len(processed_data['patents'])} patents"
                        )

                # Crunchbase-specific fields
                if source == "crunchbase_scraping":
                    if not merged.get("incorporation_country") and processed_data.get(
                        "country"
                    ):
                        merged["incorporation_country"] = processed_data["country"]
                        self.logger.info(
                            f"Set incorporation_country from Crunchbase: {processed_data['country']}"
                        )

                    # Map Crunchbase funding data
                    if not merged.get("funding_total") and processed_data.get(
                        "total_funding"
                    ):
                        merged["funding_total"] = processed_data["total_funding"]
                        self.logger.info(
                            f"Set funding_total from Crunchbase: {processed_data['total_funding']}"
                        )

                    if not merged.get("funding_rounds") and processed_data.get(
                        "funding_rounds"
                    ):
                        merged["funding_rounds"] = processed_data["funding_rounds"]
                        self.logger.info(
                            f"Set funding_rounds from Crunchbase: {processed_data['funding_rounds']}"
                        )

                    if not merged.get("last_funding_date") and processed_data.get(
                        "last_funding_date"
                    ):
                        merged["last_funding_date"] = processed_data[
                            "last_funding_date"
                        ]
                        self.logger.info(
                            f"Set last_funding_date from Crunchbase: {processed_data['last_funding_date']}"
                        )

                    if not merged.get("last_funding_amount") and processed_data.get(
                        "last_funding_amount"
                    ):
                        merged["last_funding_amount"] = processed_data[
                            "last_funding_amount"
                        ]
                        self.logger.info(
                            f"Set last_funding_amount from Crunchbase: {processed_data['last_funding_amount']}"
                        )

                    if not merged.get("valuation") and processed_data.get("valuation"):
                        merged["valuation"] = processed_data["valuation"]
                        self.logger.info(
                            f"Set valuation from Crunchbase: {processed_data['valuation']}"
                        )

                    if not merged.get("revenue") and processed_data.get("revenue"):
                        merged["revenue"] = processed_data["revenue"]
                        self.logger.info(
                            f"Set revenue from Crunchbase: {processed_data['revenue']}"
                        )

                    # Extract social URLs from Crunchbase data
                    if not merged.get("linkedin_url") and processed_data.get(
                        "linkedin_url"
                    ):
                        merged["linkedin_url"] = processed_data["linkedin_url"]
                        self.logger.info(
                            f"Set linkedin_url from Crunchbase: {processed_data['linkedin_url']}"
                        )

                    if not merged.get("twitter_url") and processed_data.get(
                        "twitter_url"
                    ):
                        merged["twitter_url"] = processed_data["twitter_url"]
                        self.logger.info(
                            f"Set twitter_url from Crunchbase: {processed_data['twitter_url']}"
                        )

                    if not merged.get("facebook_url") and processed_data.get(
                        "facebook_url"
                    ):
                        merged["facebook_url"] = processed_data["facebook_url"]
                        self.logger.info(
                            f"Set facebook_url from Crunchbase: {processed_data['facebook_url']}"
                        )

                    # Map Crunchbase stage to industry field (combine with existing)
                    if processed_data.get("stage"):
                        current_industry = merged.get("industry", "")
                        if current_industry:
                            merged["industry"] = (
                                f"{current_industry} | {processed_data['stage']}"
                            )
                        else:
                            merged["industry"] = processed_data["stage"]
                        self.logger.info(
                            f"Set industry from Crunchbase stage: {processed_data['stage']}"
                        )

                    # Map Crunchbase city/state to hq_location (append if exists)
                    if processed_data.get("city") or processed_data.get("state"):
                        geo_parts = []
                        if processed_data.get("city"):
                            geo_parts.append(processed_data["city"])
                        if processed_data.get("state"):
                            geo_parts.append(processed_data["state"])

                        if geo_parts:
                            current_location = merged.get("hq_location", "")
                            if current_location:
                                merged["hq_location"] = (
                                    f"{current_location} | {', '.join(geo_parts)}"
                                )
                            else:
                                merged["hq_location"] = ", ".join(geo_parts)
                            self.logger.info(
                                f"Set hq_location from Crunchbase geo: {merged['hq_location']}"
                            )

        # Extract insights from website insights data
        website_insights = data_sources.get("website_insights", {})
        for url, insight_data in website_insights.items():
            if insight_data.get("status") == "success" and insight_data.get("data"):
                insights = insight_data["data"].get("insights", [])
                for insight in insights:
                    # Legal entity name - high priority
                    if insight.get(
                        "label"
                    ) == "Legal or Entity Name" and not merged.get("legal_entity_name"):
                        merged["legal_entity_name"] = insight.get("value")

                    # Business model
                    elif insight.get("label") == "Type of Business" and not merged.get(
                        "business_model"
                    ):
                        merged["business_model"] = insight.get("value")

                    # Company name (if not already set)
                    elif insight.get("label") == "Company Name" and not merged.get(
                        "name"
                    ):
                        merged["name"] = insight.get("value")

                    # Founded year
                    elif insight.get("label") == "Year Founded" and not merged.get(
                        "founded_year"
                    ):
                        try:
                            merged["founded_year"] = int(insight.get("value"))
                        except (ValueError, TypeError):
                            pass

                    # Sector/Industry
                    elif insight.get("label") == "Sector" and not merged.get(
                        "industry"
                    ):
                        merged["industry"] = insight.get("value")

                    # Description
                    elif insight.get("label") == "Description" and not merged.get(
                        "description"
                    ):
                        merged["description"] = insight.get("value")

                    # Contact email
                    elif insight.get("label") == "Contact" and not merged.get("email"):
                        value = insight.get("value", "")
                        if "@" in value:  # Basic email validation
                            merged["email"] = value

                    # Phone
                    elif insight.get("label") == "Phone" and not merged.get("phone"):
                        value = insight.get("value", "")
                        if any(
                            char.isdigit() for char in value
                        ):  # Basic phone validation
                            merged["phone"] = value

                    # Headquarters location
                    elif insight.get("label") == "Headquarters" and not merged.get(
                        "hq_location"
                    ):
                        merged["hq_location"] = insight.get("value")

                    # Employee count
                    elif insight.get("label") == "Employee Count" and not merged.get(
                        "employee_count"
                    ):
                        try:
                            merged["employee_count"] = int(insight.get("value"))
                        except (ValueError, TypeError):
                            pass

                    # Revenue
                    elif insight.get("label") == "Revenue" and not merged.get(
                        "revenue"
                    ):
                        merged["revenue"] = insight.get("value")

                    # Stage/Company Stage - pack into industry field
                    elif insight.get("label") == "Stage":
                        current_industry = merged.get("industry", "")
                        if current_industry:
                            merged["industry"] = (
                                f"{current_industry} | {insight.get('value')}"
                            )
                        else:
                            merged["industry"] = insight.get("value")

                    # Funding Status - pack into business_model field
                    elif insight.get("label") == "Funding Status":
                        current_model = merged.get("business_model", "")
                        if current_model:
                            merged["business_model"] = (
                                f"{current_model} | {insight.get('value')}"
                            )
                        else:
                            merged["business_model"] = insight.get("value")

                    # Traction Level - pack into business_model field
                    elif insight.get("label") == "Traction Level":
                        current_model = merged.get("business_model", "")
                        if current_model:
                            merged["business_model"] = (
                                f"{current_model} | {insight.get('value')}"
                            )
                        else:
                            merged["business_model"] = insight.get("value")

        # Add linkedin_data from processing results if available
        self.logger.info(f"Processing results keys: {list(processing_results.keys())}")
        if processing_results.get("linkedin"):
            self.logger.info(
                f"LinkedIn processing result keys: {list(processing_results['linkedin'].keys())}"
            )
            if processing_results["linkedin"].get("linkedin_data"):
                self.logger.info("LinkedIn data found in processing results")
            else:
                self.logger.warning("No linkedin_data in LinkedIn processing results")

        if processing_results.get("linkedin", {}).get("linkedin_data"):
            linkedin_data = processing_results["linkedin"]["linkedin_data"]
            merged["linkedin_data"] = linkedin_data
            self.logger.info(
                f"Added LinkedIn data to merged: {list(linkedin_data.keys())}"
            )

            # Extract email and phone from LinkedIn JSONB data if not already set
            if not merged.get("email") and linkedin_data.get("email"):
                merged["email"] = linkedin_data["email"]

            if not merged.get("phone") and linkedin_data.get("phone"):
                merged["phone"] = linkedin_data["phone"]
        else:
            self.logger.warning("No LinkedIn data found in processing results")

        # Extract and merge geo tags from multiple sources
        geo_tags = self._extract_geo_tags(enrichment_data, processing_results)
        if geo_tags:
            merged["geo_tags"] = (
                geo_tags  # This should already be a list from _extract_geo_tags
            )

        # Final fallback: Extract from raw processed data if still missing
        data_sources = enrichment_data.get("data", {})

        # Check LinkedIn processed data
        if not merged.get("email") and data_sources.get("linkedin_scraping", {}).get(
            "processed_data", {}
        ).get("email"):
            merged["email"] = data_sources["linkedin_scraping"]["processed_data"][
                "email"
            ]

        if not merged.get("phone") and data_sources.get("linkedin_scraping", {}).get(
            "processed_data", {}
        ).get("phone"):
            merged["phone"] = data_sources["linkedin_scraping"]["processed_data"][
                "phone"
            ]

        # Check Crunchbase processed data
        if not merged.get("email") and data_sources.get("crunchbase_scraping", {}).get(
            "processed_data", {}
        ).get("email"):
            merged["email"] = data_sources["crunchbase_scraping"]["processed_data"][
                "email"
            ]

        if not merged.get("phone") and data_sources.get("crunchbase_scraping", {}).get(
            "processed_data", {}
        ).get("phone"):
            merged["phone"] = data_sources["crunchbase_scraping"]["processed_data"][
                "phone"
            ]

        # Set S3 raw data key for tracking enrichment source
        if enrichment_data.get("barrier_group_id"):
            merged["s3_raw_data_key"] = (
                f"comprehensive_enrichment/{enrichment_data['barrier_group_id']}"
            )

        # Set confidence score based on data quality
        confidence_score = 0.5  # Base confidence
        if merged.get("name"):
            confidence_score += 0.1
        if merged.get("legal_entity_name"):
            confidence_score += 0.1
        if merged.get("description"):
            confidence_score += 0.1
        if merged.get("industry"):
            confidence_score += 0.1
        if merged.get("website"):
            confidence_score += 0.1
        if merged.get("email"):
            confidence_score += 0.1
        if merged.get("phone"):
            confidence_score += 0.1
        if merged.get("founded_year"):
            confidence_score += 0.1
        if merged.get("employee_count") or merged.get("employee_count_range"):
            confidence_score += 0.1
        if merged.get("hq_location"):
            confidence_score += 0.05
        if merged.get("linkedin_url"):
            confidence_score += 0.05
        if merged.get("funding_total") or merged.get("funding_rounds"):
            confidence_score += 0.05
        if merged.get("stage") or merged.get("funding_status"):
            confidence_score += 0.05

        merged["confidence_score"] = min(confidence_score, 1.0)

        # Pack funding data into JSONB field
        funding_data = {}
        if merged.get("funding_total"):
            funding_data["total"] = merged.pop("funding_total")
        if merged.get("funding_rounds"):
            funding_data["rounds"] = merged.pop("funding_rounds")
        if merged.get("last_funding_date"):
            funding_data["last_date"] = merged.pop("last_funding_date")
        if merged.get("last_funding_amount"):
            funding_data["last_amount"] = merged.pop("last_funding_amount")
        if merged.get("valuation"):
            funding_data["valuation"] = merged.pop("valuation")
        if merged.get("revenue"):
            funding_data["revenue"] = merged.pop("revenue")

        if funding_data:
            merged["funding_data"] = funding_data

        # Add cb_data from processing results if available
        if processing_results.get("crunchbase", {}).get("cb_data"):
            cb_data = processing_results["crunchbase"]["cb_data"]
            merged["cb_data"] = cb_data

            # Extract social URLs from Crunchbase JSONB data if not already set
            if not merged.get("linkedin_url") and cb_data.get("social_media_links"):
                linkedin_url = self._extract_social_url(
                    cb_data["social_media_links"], "linkedin"
                )
                if linkedin_url:
                    merged["linkedin_url"] = linkedin_url

            if not merged.get("twitter_url") and cb_data.get("social_media_links"):
                twitter_url = self._extract_social_url(
                    cb_data["social_media_links"], "twitter"
                )
                if twitter_url:
                    merged["twitter_url"] = twitter_url

            if not merged.get("facebook_url") and cb_data.get("social_media_links"):
                facebook_url = self._extract_social_url(
                    cb_data["social_media_links"], "facebook"
                )
                if facebook_url:
                    merged["facebook_url"] = facebook_url

            # Extract email and phone from Crunchbase JSONB data if not already set
            if not merged.get("email") and cb_data.get("email_address"):
                merged["email"] = cb_data["email_address"]

            if not merged.get("phone") and cb_data.get("phone_number"):
                merged["phone"] = cb_data["phone_number"]

        # Add apollo_data from processing results if available
        if processing_results.get("apollo", {}).get("apollo_data"):
            apollo_data = processing_results["apollo"]["apollo_data"]
            merged["apollo_data"] = apollo_data
            self.logger.info(f"Added Apollo data to merged: {list(apollo_data.keys())}")

        # Add pb_data from processing results if available
        if processing_results.get("pitchbook", {}).get("pitchbook_data"):
            pb_data = processing_results["pitchbook"]["pitchbook_data"]
            merged["pb_data"] = pb_data
            self.logger.info(f"Added PitchBook data to merged: {list(pb_data.keys())}")

            # Extract social URLs from PitchBook JSONB data if not already set
            if not merged.get("linkedin_url") and pb_data.get("linkedin_url"):
                merged["linkedin_url"] = pb_data["linkedin_url"]

            if not merged.get("website") and pb_data.get("website"):
                merged["website"] = pb_data["website"]

            # Extract email and phone from PitchBook JSONB data if not already set
            if not merged.get("email") and pb_data.get("email"):
                merged["email"] = pb_data["email"]

            if not merged.get("phone") and pb_data.get("phone"):
                merged["phone"] = pb_data["phone"]

            # Extract additional PitchBook fields that might not be in the main schema
            if not merged.get("latest_deal_amount_value") and pb_data.get(
                "latest_deal_amount_value"
            ):
                merged["latest_deal_amount_value"] = pb_data["latest_deal_amount_value"]

            if not merged.get("company_socials") and pb_data.get("company_socials"):
                merged["company_socials"] = pb_data["company_socials"]

            if not merged.get("contact_information") and pb_data.get(
                "contact_information"
            ):
                merged["contact_information"] = pb_data["contact_information"]

            if not merged.get("research_analysis") and pb_data.get("research_analysis"):
                merged["research_analysis"] = pb_data["research_analysis"]

            if not merged.get("patent_activity") and pb_data.get("patent_activity"):
                merged["patent_activity"] = pb_data["patent_activity"]

            if not merged.get("all_investments") and pb_data.get("all_investments"):
                merged["all_investments"] = pb_data["all_investments"]

            if not merged.get("patents") and pb_data.get("patents"):
                merged["patents"] = pb_data["patents"]

            if not merged.get("timestamp") and pb_data.get("timestamp"):
                merged["timestamp"] = pb_data["timestamp"]

            if not merged.get("input") and pb_data.get("input"):
                merged["input"] = pb_data["input"]

        # Add enhanced fields from Crunchbase processing if available
        if processing_results.get("crunchbase", {}).get("enhanced_fields"):
            enhanced_fields = processing_results["crunchbase"]["enhanced_fields"]
            self.logger.info(f"Enhanced fields from Crunchbase: {enhanced_fields}")

            # Extract stage information
            if not merged.get("stage") and enhanced_fields.get("stage"):
                merged["stage"] = enhanced_fields["stage"]
                self.logger.info(
                    f"Set stage from enhanced fields: {enhanced_fields['stage']}"
                )

            # Extract sector information - store in industry field instead of sector
            if not merged.get("industry") and enhanced_fields.get("sector"):
                merged["industry"] = enhanced_fields["sector"]
                self.logger.info(
                    f"Set industry from enhanced fields sector: {enhanced_fields['sector']}"
                )

            # Extract enhanced industry information
            if not merged.get("industry") and enhanced_fields.get("industry"):
                merged["industry"] = enhanced_fields["industry"]
                self.logger.info(
                    f"Set industry from enhanced fields: {enhanced_fields['industry']}"
                )

            if not merged.get("sub_industry") and enhanced_fields.get("sub_industry"):
                merged["sub_industry"] = enhanced_fields["sub_industry"]
                self.logger.info(
                    f"Set sub_industry from enhanced fields: {enhanced_fields['sub_industry']}"
                )

        # Extract stage from industry field if it contains stage information
        if merged.get("industry") and "|" in merged["industry"]:
            industry_parts = merged["industry"].split(" | ")
            # Look for stage indicators in the industry field
            stage_indicators = [
                "Seed",
                "Series A",
                "Series B",
                "Series C",
                "Series D",
                "IPO",
                "Public",
                "Startup",
            ]
            for part in industry_parts:
                if part in stage_indicators and not merged.get("stage"):
                    merged["stage"] = part
                    self.logger.info(f"Extracted stage from industry field: {part}")
                    break
        else:
            self.logger.warning("No enhanced fields found in processing results")

            # Clean data to ensure all values are database-compatible
        cleaned_merged = {}

        # Define valid fields for the companies table schema
        valid_fields = {
            "name",
            "legal_entity_name",
            "description",
            "industry",
            "sub_industry",
            "business_model",
            "company_type",
            "incorporation_country",
            "hq_location",
            "employee_count",
            "employee_count_range",
            "founded_year",
            "website",
            "linkedin_url",
            "twitter_url",
            "facebook_url",
            "email",
            "stage",
            "phone",
            "funding_data",
            "cb_data",
            "linkedin_data",
            "apollo_data",
            "pb_data",
            "competitors",
            "geo_tags",
            "confidence_score",
            "enrichment_date",
            "s3_raw_data_key",
            "domain",
            "source",
            "created_at",
            "updated_at",
            "revenue",
            "traction_level",
            "funding_status",
            "funding_total",
            "funding_rounds",
            "last_funding_date",
            "last_funding_amount",
            "valuation",
            "deal_type",
            "round_size",
            "sector",
            "apollo_id",
            "apollo_metadata",
            # PitchBook specific fields
            "status",
            "latest_deal_type",
            "financing_rounds",
            "latest_deal_amount",
            "latest_deal_amount_value",
            "pitchbook_id",
            "pitchbook_url",
            "investments",
            "faq",
            "research_analysis",
            "patent_activity",
            "all_investments",
            "patents",
            "company_socials",
            "contact_information",
            "timestamp",
            "input",
        }

        # Define fields that should be integers
        integer_fields = {"employee_count", "founded_year"}

        # Define fields that should be floats
        float_fields = {"confidence_score"}

        # Define fields that should be JSONB (lists/dicts)
        jsonb_fields = {
            "geo_tags",
            "linkedin_data",
            "cb_data",
            "apollo_data",
            "pb_data",
            "competitors",
            "funding_data",
            # PitchBook JSONB fields
            "investments",
            "faq",
            "research_analysis",
            "patent_activity",
            "all_investments",
            "patents",
            "company_socials",
            "contact_information",
            "latest_deal_amount_value",
            "timestamp",
            "input",
        }

        for key, value in merged.items():
            # Skip fields that don't exist in the database schema
            if key not in valid_fields:
                self.logger.warning(f"Skipping field '{key}' - not in database schema")
                continue

            if value is None:
                continue
            elif key in jsonb_fields:
                # Keep JSONB fields as-is (they will be serialized by the database layer)
                cleaned_merged[key] = value
            elif isinstance(value, list):
                # Convert lists to pipe-separated strings
                cleaned_merged[key] = " | ".join(
                    str(item) for item in value[:5]
                )  # Limit to 5 items
            elif isinstance(value, (dict, set)):
                # Convert complex types to JSON strings
                cleaned_merged[key] = json.dumps(value, default=str)
            elif key in integer_fields:
                # Convert to integer for specific fields
                try:
                    cleaned_merged[key] = int(value)
                except (ValueError, TypeError):
                    self.logger.warning(
                        f"Could not convert {key} value '{value}' to integer, skipping"
                    )
                    continue
            elif key in float_fields:
                # Convert to float for specific fields
                try:
                    cleaned_merged[key] = float(value)
                except (ValueError, TypeError):
                    self.logger.warning(
                        f"Could not convert {key} value '{value}' to float, skipping"
                    )
                    continue
            else:
                # Convert everything else to string
                cleaned_merged[key] = str(value)

        self.logger.info(f"Final cleaned merged data before return: {cleaned_merged}")
        return cleaned_merged

    async def _process_discovered_urls(
        self, company_uuid: str, data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process discovered URLs from website insights."""
        try:
            urls_processed = 0

            # Extract URLs from website insights
            website_insights = data.get("website_insights", {})
            for url, insight_data in website_insights.items():
                if url.startswith("http"):
                    # Determine page type from URL or insights
                    page_type = self._determine_page_type(url, insight_data)

                    # Store in company.links using new RDS method
                    await self.rds.store_company_link(
                        company_uuid=company_uuid,
                        discovered_from="website_scraper",
                        page_url=url,
                        page_type=page_type,
                    )

                    urls_processed += 1

            return {"status": "success", "urls_processed": urls_processed}

        except Exception as e:
            self.logger.error(f"Error processing discovered URLs: {e}")
            return {"status": "error", "error": str(e)}

    def _determine_page_type(self, url: str, insight_data: Dict[str, Any]) -> str:
        """Determine page type from URL and insights."""
        url_lower = url.lower()

        # URL-based detection
        if "contact" in url_lower:
            return "contact"
        elif "about" in url_lower:
            return "about"
        elif "faq" in url_lower:
            return "support"
        elif "blog" in url_lower or "news" in url_lower:
            return "blog"
        elif "pricing" in url_lower:
            return "pricing"
        elif "product" in url_lower or "service" in url_lower:
            return "product"

        # Insight-based detection
        if insight_data.get("status") == "success" and insight_data.get("data"):
            insights = insight_data["data"].get("insights", [])
            for insight in insights:
                if insight.get("label") == "Type of Business":
                    return "company"
                elif insight.get("type") == "product":
                    return "product"

        return "general"

    def _extract_company_domain_from_data(
        self, enrichment_data: Dict[str, Any]
    ) -> Optional[str]:
        """Extract company domain from various data sources."""
        data = enrichment_data.get("data", {})

        # Try LinkedIn data first
        if "linkedin_scraping" in data:
            linkedin_data = data["linkedin_scraping"]
            if linkedin_data.get("status") == "success":
                processed_data = linkedin_data.get("processed_data", {})
                website = processed_data.get("website")
                if website:
                    return self._extract_domain_from_url(website)

        # Try Crunchbase data
        if "crunchbase_scraping" in data:
            crunchbase_data = data["crunchbase_scraping"]
            if crunchbase_data.get("status") == "success":
                processed_data = crunchbase_data.get("processed_data", {})
                website = processed_data.get("website")
                if website:
                    return self._extract_domain_from_url(website)

        # Try website insights data
        if "website_insights" in data:
            website_insights = data["website_insights"]
            for url in website_insights.keys():
                if url.startswith("http"):
                    return self._extract_domain_from_url(url)

        return None

    def _extract_domain_from_url(self, url: str) -> Optional[str]:
        """Extract domain from URL."""
        if not url:
            return None

        # Remove protocol
        if url.startswith(("http://", "https://")):
            url = url.split("://", 1)[1]

        # Remove path and query parameters
        domain = url.split("/")[0]

        # Remove port if present
        domain = domain.split(":")[0]

        # Return raw domain (cleaning will be done later)
        return domain

    def _clean_domain(self, domain: str) -> str:
        """Clean domain by removing www. prefix."""
        if domain.startswith("www."):
            return domain[4:]
        return domain

    def _extract_year_from_date(self, date_string: Optional[str]) -> Optional[int]:
        """Extract year from date string."""
        if not date_string:
            return None
        try:
            # Handle various date formats
            if "-" in date_string:
                return int(date_string.split("-")[0])
            elif "/" in date_string:
                return int(date_string.split("/")[-1])
            else:
                return int(date_string)
        except (ValueError, IndexError):
            return None

    def _extract_social_url(
        self, social_links: Optional[List[str]], platform: str
    ) -> Optional[str]:
        """Extract social media URL for specific platform."""
        if not social_links:
            return None

        platform_keywords = {
            "linkedin": ["linkedin.com"],
            "twitter": ["twitter.com", "x.com"],
            "facebook": ["facebook.com"],
        }

        keywords = platform_keywords.get(platform, [])
        for link in social_links:
            if any(keyword in link.lower() for keyword in keywords):
                return link

        return None

    def _extract_geo_tags(
        self, enrichment_data: Dict[str, Any], processing_results: Dict[str, Any]
    ) -> Optional[List[str]]:
        """Extract geo tags from multiple sources with fallbacks."""
        geo_tags = set()

        # Extract from Crunchbase data
        if processing_results.get("crunchbase", {}).get("cb_data"):
            cb_data = processing_results["crunchbase"]["cb_data"]

            # Extract from headquarters_regions
            if cb_data.get("headquarters_regions"):
                for region in cb_data["headquarters_regions"]:
                    if region.get("value"):
                        geo_tags.add(region["value"])

            # Extract from location array
            if cb_data.get("location"):
                for loc in cb_data["location"]:
                    if loc.get("name"):
                        geo_tags.add(loc["name"])

            # Extract from hq_continent
            if cb_data.get("hq_continent"):
                geo_tags.add(cb_data["hq_continent"])

            # Extract from address
            if cb_data.get("address"):
                address_parts = cb_data["address"].split(", ")
                for part in address_parts:
                    if part and part not in ["Central Region"]:  # Skip generic parts
                        geo_tags.add(part)

        # Extract from LinkedIn data
        if processing_results.get("linkedin", {}).get("linkedin_data"):
            linkedin_data = processing_results["linkedin"]["linkedin_data"]

            # Extract from formatted_locations
            if linkedin_data.get("formatted_locations"):
                for location in linkedin_data["formatted_locations"]:
                    if isinstance(location, str):
                        geo_tags.add(location)
                    elif isinstance(location, dict) and location.get("name"):
                        geo_tags.add(location["name"])

            # Extract from country_code
            if linkedin_data.get("country_code"):
                geo_tags.add(linkedin_data["country_code"])

        # Extract from raw data sources
        data_sources = enrichment_data.get("data", {})

        # LinkedIn raw data
        if (
            data_sources.get("linkedin_scraping", {})
            .get("processed_data", {})
            .get("locations")
        ):
            locations = data_sources["linkedin_scraping"]["processed_data"]["locations"]
            if isinstance(locations, list):
                for location in locations[:3]:  # Limit to 3 locations
                    if isinstance(location, str):
                        geo_tags.add(location)
                    elif isinstance(location, dict) and location.get("name"):
                        geo_tags.add(location["name"])

        # Crunchbase raw data
        if (
            data_sources.get("crunchbase_scraping", {})
            .get("raw_s3_data", {})
            .get("raw_data")
        ):
            raw_data = data_sources["crunchbase_scraping"]["raw_s3_data"]["raw_data"]
            if raw_data and isinstance(raw_data, list) and len(raw_data) > 0:
                raw_entry = raw_data[0]

                # Extract from headquarters_regions
                if raw_entry.get("headquarters_regions"):
                    for region in raw_entry["headquarters_regions"]:
                        if region.get("value"):
                            geo_tags.add(region["value"])

                # Extract from location
                if raw_entry.get("location"):
                    for loc in raw_entry["location"]:
                        if loc.get("name"):
                            geo_tags.add(loc["name"])

        # Convert set to list and clean up
        geo_tags_list = list(geo_tags)
        geo_tags_list = [
            tag for tag in geo_tags_list if tag and len(tag) > 1
        ]  # Remove empty/short tags

        self.logger.info(f"Extracted geo tags: {geo_tags_list}")
        return geo_tags_list if geo_tags_list else None


async def process_comprehensive_enrichment_data_task(
    payload: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Task function to process comprehensive enrichment data and populate database.

    Args:
        payload: Job payload containing enrichment data

    Returns:
        Processing result with status and details
    """
    start_time = datetime.now(timezone.utc)

    try:
        logger.info("Starting comprehensive enrichment data processing task")

        # Handle payload structure
        if "job_args" in payload:
            data = payload["job_args"]
        else:
            data = payload

        # Validate input
        if "enrichment_data" not in data:
            return {
                "success": False,
                "error": "Missing enrichment_data in payload",
                "processing_time": (
                    datetime.now(timezone.utc) - start_time
                ).total_seconds(),
            }

        enrichment_data = data["enrichment_data"]

        # Initialize RDS storage
        rds_storage = RDSStorage()
        await rds_storage.initialize()

        # Create processor and process data
        processor = ComprehensiveDataProcessor(rds_storage)
        result = await processor.process_comprehensive_enrichment_data(enrichment_data)

        await rds_storage.cleanup()

        processing_time = (datetime.now(timezone.utc) - start_time).total_seconds()

        if result["success"]:
            logger.info(
                "Comprehensive data processing completed successfully",
                company_domain=result.get("company_domain"),
                processing_time=processing_time,
            )
        else:
            logger.error(
                f"Comprehensive data processing failed: {result.get('error')}",
                processing_time=processing_time,
            )

        return {**result, "processing_time": processing_time}

    except Exception as e:
        error_msg = f"Comprehensive data processing task failed: {str(e)}"
        logger.error(error_msg, exc_info=True)

        return {
            "success": False,
            "error": error_msg,
            "processing_time": (
                datetime.now(timezone.utc) - start_time
            ).total_seconds(),
        }
