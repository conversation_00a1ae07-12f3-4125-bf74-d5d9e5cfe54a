"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Code2 } from "lucide-react"

interface Technology {
  name: string
  category: string
}

interface CompanyTechStackCardProps {
  technologies: Technology[]
}

export function CompanyTechStackCard({ technologies }: CompanyTechStackCardProps) {
  // Group technologies by category
  const groupedTech = technologies.reduce((acc, tech) => {
    if (!acc[tech.category]) {
      acc[tech.category] = []
    }
    acc[tech.category].push(tech)
    return acc
  }, {} as Record<string, Technology[]>)

  // Sort categories by number of technologies
  const sortedCategories = Object.entries(groupedTech).sort((a, b) => b[1].length - a[1].length)

  if (!technologies || technologies.length === 0) {
    return null
  }

  return (
    <div className="animate-in fade-in duration-500">
      <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-md">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-2">
            <Code2 className="h-5 w-5 text-gray-600" />
            <CardTitle className="text-lg font-semibold text-gray-900">
              Technology Stack
            </CardTitle>
            <Badge variant="secondary" className="text-xs">
              {technologies.length} technologies
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {sortedCategories.map(([category, techs], categoryIndex) => (
            <div
              key={category}
              className="animate-in fade-in slide-in-from-left duration-300"
              style={{ animationDelay: `${categoryIndex * 100}ms` }}
            >
              <h4 className="text-sm font-medium text-gray-700 capitalize">
                {category}
              </h4>
              
              <div className="flex flex-wrap gap-2 mt-2">
                {techs.map((tech, techIndex) => (
                  <div
                    key={tech.name}
                    className="animate-in fade-in scale-in-95 duration-200"
                    style={{ animationDelay: `${(categoryIndex * 100) + (techIndex * 50)}ms` }}
                  >
                    <Badge 
                      variant="outline" 
                      className="text-xs px-3 py-1 bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 transition-colors"
                    >
                      {tech.name}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  )
} 