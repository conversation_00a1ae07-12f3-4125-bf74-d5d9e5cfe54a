"""
Barrier Management API endpoints for TractionX Data Pipeline Service.

This module provides REST API endpoints for monitoring and managing
barrier-orchestrated enrichment tasks.
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from app.configs import get_logger
from app.configs.settings import settings
from app.queueing.barrier_sync import BarrierStatus, BarrierSync

logger = get_logger(__name__)

router = APIRouter(prefix="/barriers", tags=["barriers"])


class BarrierStatusResponse(BaseModel):
    """Response model for barrier status."""

    barrier_id: str
    entity_type: str
    entity_id: str
    status: str
    expected_tasks: List[str]
    completed_tasks: List[str]
    progress_percentage: float
    created_at: str
    updated_at: str
    task_logs: List[Dict[str, Any]]


class BarrierListResponse(BaseModel):
    """Response model for barrier list."""

    barriers: List[BarrierStatusResponse]
    total_count: int
    page: int
    page_size: int


class CreateBarrierRequest(BaseModel):
    """Request model for creating a barrier."""

    barrier_group_id: str
    entity_type: str = "company"
    entity_id: str
    initial_tasks: List[str]


class RegisterTasksRequest(BaseModel):
    """Request model for registering new tasks."""

    new_tasks: List[str]


@router.get("/", response_model=BarrierListResponse)
async def list_barriers(
    entity_type: Optional[str] = Query(None, description="Filter by entity type"),
    status: Optional[str] = Query(None, description="Filter by barrier status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
) -> BarrierListResponse:
    """List barrier groups with optional filtering."""
    try:
        barrier_sync = BarrierSync(
            redis_url=settings.redis_connection_string,
            mongo_url=settings.mongo_connection_string,
        )
        await barrier_sync.initialize()

        # Convert status string to enum if provided
        status_enum = None
        if status:
            try:
                status_enum = BarrierStatus(status)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid status: {status}")

        # Get barriers
        barriers = await barrier_sync.list_barriers(
            entity_type=entity_type, status=status_enum, limit=page_size
        )

        # Convert to response format
        barrier_responses = []
        for barrier in barriers:
            progress = (
                len(barrier.completed_tasks) / len(barrier.expected_tasks)
                if barrier.expected_tasks
                else 0
            )

            barrier_responses.append(
                BarrierStatusResponse(
                    barrier_id=barrier.id,
                    entity_type=barrier.entity_type,
                    entity_id=barrier.entity_id,
                    status=barrier.status,
                    expected_tasks=barrier.expected_tasks,
                    completed_tasks=barrier.completed_tasks,
                    progress_percentage=progress * 100,
                    created_at=barrier.created_at.isoformat(),
                    updated_at=barrier.updated_at.isoformat(),
                    task_logs=[log.model_dump() for log in barrier.task_logs],
                )
            )

        await barrier_sync.cleanup()

        return BarrierListResponse(
            barriers=barrier_responses,
            total_count=len(barrier_responses),
            page=page,
            page_size=page_size,
        )

    except Exception as e:
        logger.error(f"Failed to list barriers: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to list barriers: {str(e)}"
        )


@router.get("/{barrier_id}", response_model=BarrierStatusResponse)
async def get_barrier_status(barrier_id: str) -> BarrierStatusResponse:
    """Get the status of a specific barrier group."""
    try:
        barrier_sync = BarrierSync(
            redis_url=settings.redis_connection_string,
            mongo_url=settings.mongo_connection_string,
        )
        await barrier_sync.initialize()

        barrier = await barrier_sync.get_barrier_status(barrier_id)
        if not barrier:
            raise HTTPException(
                status_code=404, detail=f"Barrier {barrier_id} not found"
            )

        progress = (
            len(barrier.completed_tasks) / len(barrier.expected_tasks)
            if barrier.expected_tasks
            else 0
        )

        response = BarrierStatusResponse(
            barrier_id=barrier.id,
            entity_type=barrier.entity_type,
            entity_id=barrier.entity_id,
            status=barrier.status,
            expected_tasks=barrier.expected_tasks,
            completed_tasks=barrier.completed_tasks,
            progress_percentage=progress * 100,
            created_at=barrier.created_at.isoformat(),
            updated_at=barrier.updated_at.isoformat(),
            task_logs=[log.model_dump() for log in barrier.task_logs],
        )

        await barrier_sync.cleanup()
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get barrier status for {barrier_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get barrier status: {str(e)}"
        )


@router.post("/", response_model=Dict[str, Any])
async def create_barrier(request: CreateBarrierRequest) -> Dict[str, Any]:
    """Create a new barrier group."""
    try:
        barrier_sync = BarrierSync(
            redis_url=settings.redis_connection_string,
            mongo_url=settings.mongo_connection_string,
        )
        await barrier_sync.initialize()

        success = await barrier_sync.register_barrier(
            group_id=request.barrier_group_id,
            entity_type=request.entity_type,
            entity_id=request.entity_id,
            initial_tasks=request.initial_tasks,
        )

        if not success:
            raise HTTPException(status_code=500, detail="Failed to create barrier")

        await barrier_sync.cleanup()

        return {
            "message": "Barrier created successfully",
            "barrier_id": request.barrier_group_id,
            "entity_type": request.entity_type,
            "entity_id": request.entity_id,
            "initial_tasks": request.initial_tasks,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create barrier: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to create barrier: {str(e)}"
        )


@router.post("/{barrier_id}/tasks", response_model=Dict[str, Any])
async def register_tasks(
    barrier_id: str,
    request: RegisterTasksRequest,
) -> Dict[str, Any]:
    """Register new tasks to an existing barrier group."""
    try:
        barrier_sync = BarrierSync(
            redis_url=settings.redis_connection_string,
            mongo_url=settings.mongo_connection_string,
        )
        await barrier_sync.initialize()

        # Check if barrier exists
        existing_barrier = await barrier_sync.get_barrier_status(barrier_id)
        if not existing_barrier:
            raise HTTPException(
                status_code=404, detail=f"Barrier {barrier_id} not found"
            )

        # Register new tasks
        added_count = await barrier_sync.register_tasks(
            group_id=barrier_id,
            new_tasks=request.new_tasks,
        )

        await barrier_sync.cleanup()

        return {
            "message": f"Registered {added_count} new tasks",
            "barrier_id": barrier_id,
            "new_tasks": request.new_tasks,
            "added_count": added_count,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to register tasks for barrier {barrier_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to register tasks: {str(e)}"
        )


@router.delete("/{barrier_id}", response_model=Dict[str, Any])
async def cleanup_barrier(barrier_id: str) -> Dict[str, Any]:
    """Clean up a barrier group and all its data."""
    try:
        barrier_sync = BarrierSync(
            redis_url=settings.redis_connection_string,
            mongo_url=settings.mongo_connection_string,
        )
        await barrier_sync.initialize()

        success = await barrier_sync.cleanup_barrier(barrier_id)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to cleanup barrier")

        await barrier_sync.cleanup()

        return {
            "message": "Barrier cleaned up successfully",
            "barrier_id": barrier_id,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cleanup barrier {barrier_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to cleanup barrier: {str(e)}"
        )


@router.get("/health/status", response_model=Dict[str, Any])
async def barrier_health_check() -> Dict[str, Any]:
    """Check the health status of the barrier sync system."""
    try:
        barrier_sync = BarrierSync(
            redis_url=settings.redis_connection_string,
            mongo_url=settings.mongo_connection_string,
        )
        await barrier_sync.initialize()

        health_status = await barrier_sync.health_check()

        await barrier_sync.cleanup()

        return health_status

    except Exception as e:
        logger.error(f"Barrier health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "redis": "unknown",
            "mongodb": "unknown",
            "stats": {},
        }
