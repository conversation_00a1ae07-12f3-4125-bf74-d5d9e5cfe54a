# TractionX Data Pipelines Architecture Documentation

## Overview

The TractionX Data Pipelines service is a standalone, modular data processing system designed to orchestrate the ingestion, enrichment, merging, and storage of structured and unstructured data relevant to companies, founders, news, and market signals. It serves as the backbone for data processing in the TractionX investment platform.

## Core Architecture

### High-Level Data Flow
```
Deal Submission → Pipeline Trigger → Enrichment Jobs → ETL Merge → Storage
     ↓                    ↓              ↓              ↓         ↓
Form/API/Webhook → RQ Job Queue → Clay/PDL/News → Canonical → RDS/S3/Qdrant
```

### Key Components

1. **FastAPI Application** (`app/main.py`) - HTTP API server for webhooks and management
2. **RQ Worker** (`app/worker.py`) - Background job processor using Redis Queue
3. **Pipelines** (`app/pipelines/`) - Business logic for data processing
4. **Tasks** (`app/tasks/`) - RQ job definitions that wrap pipeline execution
5. **Storage Layer** (`app/storage/`) - Abstracted storage interfaces for RDS, S3, and Qdrant
6. **Models** (`app/models/`) - Pydantic data models and schemas
7. **Webhooks** (`app/webhooks/`) - External API integration endpoints

## Pipelines vs Tasks: Understanding the Distinction

### Pipelines (`app/pipelines/`)

**Purpose**: Pipelines contain the core business logic for data processing. They are reusable, stateful classes that handle the actual data transformation and enrichment.

**Characteristics**:
- **Object-oriented**: Inherit from `BasePipeline` abstract class
- **Stateful**: Maintain connections, configuration, and resources
- **Lifecycle management**: Initialize, process, cleanup phases
- **Error handling**: Built-in logging, validation, and error recovery
- **Reusable**: Can be used in different contexts (sync, async, testing)

**Example Structure**:
```python
class CompanyEnrichmentPipeline(BasePipeline):
    async def _initialize_pipeline(self):
        # Set up API clients, storage connections
    
    async def _process_data(self, input_data):
        # Core enrichment logic
        # Call external APIs (Clay)
        # Transform and validate data
        
    async def _cleanup_pipeline(self):
        # Close connections, cleanup resources
```

### Tasks (`app/tasks/`)

**Purpose**: Tasks are RQ job functions that provide the interface between the job queue system and the pipeline classes. They handle job-specific concerns like serialization, error reporting, and result formatting.

**Characteristics**:
- **Functional**: Simple functions that can be serialized by RQ
- **Stateless**: No persistent state between executions
- **Job-focused**: Handle RQ-specific concerns (job metadata, retries, results)
- **Wrapper layer**: Orchestrate pipeline execution within job context
- **Synchronous interface**: Provide sync wrappers for async pipelines

**Example Structure**:
```python
async def enrich_company_data(job_data: Dict[str, Any]) -> Dict[str, Any]:
    # Extract job parameters
    # Initialize pipeline
    # Execute pipeline.process()
    # Format results for RQ
    # Handle cleanup
    
def enrich_company_data_sync(job_data: Dict[str, Any]) -> Dict[str, Any]:
    # Synchronous wrapper for RQ compatibility
    return asyncio.run(enrich_company_data(job_data))
```

### Why Both Are Needed

1. **Separation of Concerns**:
   - Pipelines focus on business logic
   - Tasks focus on job queue integration

2. **Reusability**:
   - Pipelines can be used outside of RQ context (testing, direct calls)
   - Tasks provide standardized job interface

3. **Scalability**:
   - Pipelines can be optimized for performance
   - Tasks handle job distribution and retry logic

4. **Maintainability**:
   - Clear boundaries between queue management and data processing
   - Easier to test and debug individual components

## Data Processing Flow

### 1. Trigger Phase
- **Entry Points**: Form submissions, API calls, webhooks
- **Input**: Company data, deal information, form responses
- **Output**: Queued jobs in Redis

### 2. Enrichment Phase
Multiple parallel pipelines process different data types:

#### Company Enrichment (Clay API)
- **Input**: Company name, domain
- **Process**: Call Clay API for company data
- **Output**: Enriched company information
- **Storage**: Raw data in S3, structured data in RDS

#### Founder Enrichment (PDL API)
- **Input**: Founder names, LinkedIn profiles
- **Process**: Call People Data Labs API
- **Output**: Founder background, experience
- **Storage**: Raw data in S3, structured data in RDS

#### News Aggregation (Bing News API)
- **Input**: Company name, keywords
- **Process**: Fetch recent news articles
- **Output**: News articles, sentiment analysis
- **Storage**: Articles in RDS, raw data in S3

#### Embedding Generation (OpenAI + Qdrant)
- **Input**: Text content from all sources
- **Process**: Generate embeddings using OpenAI
- **Output**: Vector embeddings for semantic search
- **Storage**: Vectors in Qdrant

### 3. ETL Merge Phase
- **Input**: All enriched data sources
- **Process**: Deterministic merge with priority (form data > enrichment data)
- **Output**: Canonical company/deal record
- **Storage**: Final record in RDS with source tracking

## Storage Architecture

### Multi-Storage Strategy
The system uses different storage types optimized for specific use cases:

#### RDS (PostgreSQL)
- **Purpose**: Structured, queryable data
- **Content**: Final merged records, metadata, job status
- **Features**: ACID compliance, complex queries, relationships

#### S3 (Object Storage)
- **Purpose**: Raw data archival and backup
- **Content**: Original API responses, large documents
- **Features**: Scalable, cost-effective, versioning

#### Qdrant (Vector Database)
- **Purpose**: Semantic search and similarity matching
- **Content**: Text embeddings, vector indices
- **Features**: Fast similarity search, ML integration

#### Redis (Cache/Queue)
- **Purpose**: Job queue and temporary caching
- **Content**: RQ jobs, session data, temporary results
- **Features**: High performance, pub/sub, expiration

### Storage Interface Pattern
All storage implementations follow common interfaces:
- `RelationalStorageInterface` for RDS operations
- `ObjectStorageInterface` for S3 operations  
- `VectorStorageInterface` for Qdrant operations
- `CacheStorageInterface` for Redis operations

This abstraction allows for:
- Easy testing with mock implementations
- Swapping storage providers without code changes
- Consistent error handling and retry logic

## Configuration Management

### Environment-Based Configuration
The system uses Pydantic Settings for type-safe configuration:

```python
class Settings(BaseSettings):
    # Service Configuration
    SERVICE_NAME: str = "tractionx-datapipelines"
    VERSION: str = "0.1.0"
    ENVIRONMENT: str = "development"
    
    # API Configuration
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8001
    
    # Redis/RQ Configuration
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # Database Configuration
    DATABASE_URL: str = "postgresql://..."
    
    # External API Keys
    CLAY_API_KEY: Optional[str] = None
    PDL_API_KEY: Optional[str] = None
    OPENAI_API_KEY: Optional[str] = None
```

### Feature Flags
Enable/disable specific pipelines:
- `ENABLE_COMPANY_ENRICHMENT`
- `ENABLE_FOUNDER_ENRICHMENT`
- `ENABLE_NEWS_AGGREGATION`
- `ENABLE_EMBEDDING_GENERATION`

## Webhook Integration

### Clay Webhook (`/webhooks/clay`)
- **Purpose**: Receive enrichment data from Clay
- **Security**: Signature verification with `CLAY_WEBHOOK_SECRET`
- **Process**: Extract data, queue enrichment job
- **Response**: Job ID and status

### Generic Webhook (`/webhooks/generic`)
- **Purpose**: Trigger pipelines from external systems
- **Flexibility**: Configurable pipeline selection
- **Use Cases**: Form submissions, manual triggers, scheduled jobs

## Development and Deployment

### Docker Architecture
- **API Service**: FastAPI application on port 8001
- **Worker Service**: RQ worker with hot-reload capability
- **Development**: Volume mounts for code changes
- **Production**: Optimized builds without volume mounts

### Hot Reload System
The `watch_worker.sh` script provides automatic worker restart:
- Monitors `app/` directory for changes
- Uses `inotifywait` or polling fallback
- Graceful worker shutdown and restart
- Development productivity optimization

### Testing Strategy
- **Unit Tests**: Individual component testing
- **Integration Tests**: End-to-end pipeline testing
- **Mock Services**: External API simulation
- **Coverage**: Comprehensive test coverage reporting

## Monitoring and Observability

### Logging
- **Structured Logging**: JSON format with contextual information
- **Pipeline Logging**: Dedicated logger per pipeline
- **Error Tracking**: Detailed error context and stack traces
- **Performance Metrics**: Processing time, success rates

### Health Checks
- **Service Health**: `/health` endpoint
- **Component Health**: Storage, Redis, external API connectivity
- **Pipeline Status**: Job queue metrics, processing statistics

### Metrics and Monitoring
- **Job Metrics**: Success/failure rates, processing times
- **Storage Metrics**: Connection health, operation latencies
- **API Metrics**: Request rates, response times
- **Resource Metrics**: Memory usage, CPU utilization

## Security Considerations

### API Security
- **Webhook Signatures**: HMAC verification for external webhooks
- **API Keys**: Secure storage and rotation of external API credentials
- **CORS**: Configurable cross-origin resource sharing

### Data Security
- **Encryption**: Data encryption in transit and at rest
- **Access Control**: Role-based access to storage systems
- **Audit Logging**: Comprehensive audit trail for data operations

## Scalability and Performance

### Horizontal Scaling
- **Stateless Workers**: Multiple worker instances for parallel processing
- **Queue Distribution**: Load balancing across worker pools
- **Storage Scaling**: Independent scaling of storage components

### Performance Optimization
- **Async Processing**: Non-blocking I/O for external API calls
- **Connection Pooling**: Efficient database connection management
- **Caching**: Strategic caching of frequently accessed data
- **Batch Processing**: Efficient bulk operations where possible

This architecture provides a robust, scalable foundation for data processing while maintaining flexibility for future enhancements and integrations.
