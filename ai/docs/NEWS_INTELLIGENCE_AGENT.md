# News Intelligence Agent - Implementation Guide

## Overview

The News Intelligence Agent is a comprehensive solution for real-time news gathering, analysis, and intelligence generation for investment decisions. It implements the full Product Requirements Document (PRD) specifications with multi-source search, intelligent deduplication, AI-powered summarization, and structured output.

## Architecture

### Core Components

1. **NewsIntelligenceTool** - Main orchestration tool
2. **Enhanced WebSearchTool** - Multi-source search capability
3. **News Intelligence Agent** - Pre-configured agent with optimized prompts
4. **News API Endpoints** - Direct access and agent-based interfaces

### Data Flow

```
User Query → Intent Detection → Source Selection → Parallel Search → 
Deduplication → Scoring → Categorization → AI Summarization → Structured Output
```

## Features Implemented

### ✅ Multi-Source Search
- **NewsAPI**: Breaking news headlines and historical articles
- **Bing News API**: Real-time news with global coverage
- **Google News RSS**: Free access to Google News (no API key required)
- **SerpAPI**: Google search results (placeholder for future implementation)
- **GNews API**: Google News API (placeholder for future implementation)

### ✅ Intelligent Query Processing
- Automatic news intent detection using keyword triggers
- Query optimization for different source types
- Parallel execution with configurable timeouts

### ✅ Advanced Deduplication
- Title similarity analysis using SequenceMatcher
- Configurable similarity threshold (default: 0.8)
- Normalization of titles (removes prefixes, suffixes, case)

### ✅ Source Credibility Scoring
- High-credibility source identification
- Multi-factor scoring algorithm:
  - Credibility score (0-40 points)
  - Recency score (0-30 points)
  - Relevance score (0-30 points)

### ✅ Intelligent Categorization
- **Funding & Investment**: funding, raised, investment, round, series, etc.
- **Product & Technology**: launched, release, product, feature, technology, etc.
- **Business & Strategy**: partnership, merger, acquisition, expansion, etc.
- **Leadership & People**: CEO, founder, executive, hired, appointed, etc.
- **Market & Industry**: market, industry, sector, trend, analysis, etc.

### ✅ Structured Output
- JSON format with clear sections
- Source attribution with clickable URLs
- Executive summaries (placeholder for LLM integration)
- Performance metrics (search time, article count, sources used)

## API Endpoints

### 1. Direct News Search
```
POST /api/ai/news/search
```

**Request:**
```json
{
  "query": "Company X latest funding news",
  "max_sources": 3,
  "time_range": "7d",
  "include_analysis": true,
  "priority_sources": ["newsapi", "bing_news"]
}
```

**Response:**
```json
{
  "success": true,
  "query": "Company X latest funding news",
  "total_articles": 15,
  "sources_used": ["newsapi", "bing_news", "google_news_rss"],
  "sections": [
    {
      "title": "Funding & Investment",
      "summary": "Found 8 articles about recent funding activities...",
      "articles": [
        {
          "title": "Company X Raises $50M Series B",
          "url": "https://techcrunch.com/...",
          "description": "Company X announced...",
          "source": "TechCrunch",
          "published_at": "2024-01-15T10:30:00Z",
          "score": 85.5
        }
      ]
    }
  ],
  "executive_summary": "Analysis shows significant funding activity...",
  "search_time": 3.2
}
```

### 2. News Intelligence Agent
```
POST /api/ai/news/agent
```

**Request:**
```json
{
  "message": "What's the latest news about AI startups in the fintech sector?",
  "session_id": "optional_session_id",
  "context_data": {}
}
```

**Response:**
```json
{
  "success": true,
  "response": "Based on my analysis of recent news sources...",
  "session_id": "news_agent_user123",
  "execution_time": 5.8,
  "tools_used": ["news_intelligence", "sentiment_analyzer"]
}
```

### 3. Available Sources
```
GET /api/ai/news/sources
```

**Response:**
```json
{
  "available_sources": ["newsapi", "google_news_rss"],
  "source_details": {
    "newsapi": {
      "name": "NewsAPI",
      "description": "Breaking news headlines and historical articles",
      "requires_key": true,
      "available": true
    }
  },
  "total_available": 2
}
```

## Configuration

### Environment Variables

```bash
# Required for NewsAPI
NEWSAPI_KEY=your_newsapi_key

# Required for Bing News
BING_NEWS_API_KEY=your_bing_news_api_key

# Optional for future implementations
SERPAPI_KEY=your_serpapi_key
GNEWS_KEY=your_gnews_key
TWITTER_API_KEY=your_twitter_api_key

# News Intelligence Configuration
NEWS_SEARCH_TIMEOUT=8
NEWS_MAX_SOURCES=3
NEWS_DEDUP_THRESHOLD=0.8
```

### Agent Configuration

The News Intelligence Agent is pre-configured with:
- **Model**: GPT-4
- **Temperature**: 0.3 (for consistent, factual responses)
- **Max Tokens**: 3000
- **Tools**: news_intelligence, web_search, news_analyzer, sentiment_analyzer

## Usage Examples

### 1. Investment Due Diligence
```python
# Search for recent news about a target company
result = await news_tool.execute(
    NewsIntelligenceInput(
        query="Stripe latest funding acquisition news",
        time_range="30d",
        max_sources=3
    ),
    context
)
```

### 2. Market Research
```python
# Analyze sector trends
result = await news_tool.execute(
    NewsIntelligenceInput(
        query="fintech AI automation trends 2024",
        time_range="7d",
        include_analysis=True
    ),
    context
)
```

### 3. Competitive Intelligence
```python
# Monitor competitor activities
result = await news_tool.execute(
    NewsIntelligenceInput(
        query="OpenAI Anthropic Claude funding partnerships",
        priority_sources=["newsapi", "bing_news"],
        time_range="14d"
    ),
    context
)
```

## Performance Characteristics

- **Search Timeout**: 8 seconds maximum
- **Parallel Processing**: Up to 3 sources simultaneously
- **Deduplication**: ~80% similarity threshold
- **Typical Response Time**: 3-6 seconds
- **Article Limit**: 10 per source (30 total before deduplication)

## Testing

Run the comprehensive test suite:

```bash
cd ai/
poetry run pytest tests/test_news_intelligence.py -v
```

Test coverage includes:
- Tool initialization and configuration
- News intent detection
- Source prioritization
- Article deduplication
- Scoring algorithms
- Categorization logic
- Error handling
- API endpoint functionality

## Future Enhancements

### Phase 2 (Planned)
1. **LLM-Powered Summarization**: Replace placeholder summaries with GPT-4 analysis
2. **SerpAPI Integration**: Add Google search results
3. **GNews API Integration**: Add Google News API support
4. **Twitter/X Integration**: Social media sentiment analysis
5. **Web Scraping Fallback**: Direct company press page scraping

### Phase 3 (Future)
1. **Real-time Alerts**: Webhook-based news monitoring
2. **Sentiment Analysis**: Advanced sentiment scoring
3. **Entity Recognition**: Company/person/product extraction
4. **Timeline Visualization**: Chronological news mapping
5. **Custom Source Addition**: User-defined RSS feeds

## Troubleshooting

### Common Issues

1. **No API Keys Configured**
   - Ensure at least one news API key is set
   - Google News RSS works without API keys

2. **Search Timeouts**
   - Increase `NEWS_SEARCH_TIMEOUT` setting
   - Reduce `max_sources` in requests

3. **Low Article Count**
   - Check API key validity
   - Verify source availability
   - Adjust time range

4. **Duplicate Articles**
   - Adjust `NEWS_DEDUP_THRESHOLD` setting
   - Check title normalization logic

### Monitoring

Monitor the following metrics:
- Search success rate
- Average response time
- API rate limit usage
- Deduplication effectiveness
- Source availability

## Security Considerations

- API keys stored as environment variables
- Rate limiting on endpoints
- Input validation and sanitization
- Audit logging for all operations
- Multi-tenant data isolation

## Support

For issues or questions:
- Check logs in `/ai/logs/`
- Review test cases for examples
- Consult API documentation at `/docs`
- Contact TractionX development team
