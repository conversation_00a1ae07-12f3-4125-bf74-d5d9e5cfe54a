"""
PitchBook URL Resolver Task for TractionX Data Pipeline Service.

This task handles the complete PitchBook URL resolution pipeline:
1. <PERSON><PERSON> search for PitchBook URLs
2. LLM-based link selection
3. BrightData scraping trigger
4. Data storage and processing
"""

import time
from datetime import datetime, timezone
from typing import Any, Dict

from app.configs import get_logger
from app.models.pitchbook import PitchBookResolverInput
from app.services.pitchbook_resolver import get_pitchbook_resolver_service
from app.storage.s3_storage import S3Storage

logger = get_logger(__name__)


async def resolve_pitchbook_url_task(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main task function for PitchBook URL resolution.

    This function is called by the job queue system and handles:
    - Input validation
    - Service initialization
    - URL resolution
    - Result storage

    Args:
        payload: Job payload containing company domain and optional description

    Returns:
        Processing result with status and data
    """
    start_time = time.time()

    try:
        logger.info("Starting PitchBook URL resolver task", payload=payload)

        # Handle payload structure - data might be in job_args
        if "job_args" in payload:
            data = payload["job_args"]
        else:
            data = payload

        # Validate input
        if "company_domain" not in data:
            return {
                "success": False,
                "error": "Missing required field: company_domain",
                "processing_time": time.time() - start_time,
            }

        # Create input model
        input_data = PitchBookResolverInput(
            company_domain=data["company_domain"],
            company_description=data.get("company_description"),
            org_id=data.get("org_id"),
            job_id=data.get("job_id"),
        )

        # Get resolver service
        try:
            logger.info("Getting PitchBook resolver service")
            resolver_service = await get_pitchbook_resolver_service()
            logger.info("PitchBook resolver service obtained successfully")
        except Exception as e:
            error_msg = f"Failed to get PitchBook resolver service: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "processing_time": time.time() - start_time,
            }

        # Resolve PitchBook URL
        try:
            logger.info("Starting PitchBook URL resolution")
            result = await resolver_service.resolve_pitchbook_url(input_data)
            logger.info("PitchBook URL resolution completed")
        except Exception as e:
            error_msg = f"PitchBook URL resolution failed: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "processing_time": time.time() - start_time,
            }

        # Store result to S3 for audit
        s3_storage = S3Storage()
        await s3_storage.initialize()

        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        s3_key = f"pitchbook_resolver_results/{input_data.org_id or 'unknown'}/{input_data.company_domain}/{timestamp}_result.json"

        # Prepare data for S3 storage
        s3_data = {
            "input": input_data.model_dump(),
            "output": result.model_dump(),
            "processing_time": time.time() - start_time,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        # Store to S3
        success = await s3_storage.put_object(s3_key, s3_data)

        if success:
            logger.info(f"Successfully stored result to S3: {s3_key}")
            logger.info("S3 Data Summary:")
            logger.info(f"  - Input domain: {input_data.company_domain}")
            logger.info(f"  - Output status: {result.status}")
            logger.info(f"  - PitchBook URL: {result.pitchbook_url or 'N/A'}")
            logger.info(
                f"  - BrightData Snapshot ID: {result.brightdata_snapshot_id or 'N/A'}"
            )
            logger.info(f"  - Processing time: {time.time() - start_time:.2f}s")
            logger.info(f"  - Error message: {result.error_message or 'None'}")
            logger.info(f"  - LLM Decision: {result.llm_decision or 'N/A'}")
            logger.info(f"  - Serper Results Count: {result.serper_results_count or 0}")
        else:
            logger.error(f"Failed to store result to S3: {s3_key}")

        await s3_storage.cleanup()

        # Prepare return result - only return S3 key, not the data
        return_result = {
            "success": result.status in ["triggered", "no_match", "completed"],
            "status": result.status,
            "s3_key": s3_key,
            "processing_time": time.time() - start_time,
        }

        # Log the return result
        logger.info("Returning result:")
        logger.info(f"  - Success: {return_result['success']}")
        logger.info(f"  - Status: {return_result['status']}")
        logger.info(f"  - PitchBook URL: {result.pitchbook_url or 'N/A'}")
        logger.info(
            f"  - BrightData Snapshot ID: {result.brightdata_snapshot_id or 'N/A'}"
        )
        logger.info(f"  - Processing time: {return_result['processing_time']:.2f}s")
        logger.info(f"  - S3 Key: {s3_key}")

        return return_result

    except Exception as e:
        error_msg = f"PitchBook resolver task failed: {str(e)}"
        logger.error(error_msg, exc_info=True)

        return {
            "success": False,
            "error": error_msg,
            "processing_time": time.time() - start_time,
        }


async def process_brightdata_pitchbook_data_task(
    payload: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Process BrightData PitchBook data from scraping.

    This task handles:
    - Polling for BrightData snapshot completion
    - Downloading BrightData snapshot
    - Processing and cleaning data
    - Storing to S3 for later processing
    - Updating enrichment status

    Args:
        payload: Job payload containing snapshot ID and metadata

    Returns:
        Processing result with status and data
    """
    start_time = time.time()

    try:
        logger.info(
            "Starting BrightData PitchBook data processing task", payload=payload
        )

        # Handle payload structure - data might be in job_args
        if "job_args" in payload:
            data = payload["job_args"]
        else:
            data = payload

        # Validate input
        required_fields = ["snapshot_id", "company_domain", "org_id"]
        for field in required_fields:
            if field not in data:
                return {
                    "success": False,
                    "error": f"Missing required field: {field}",
                    "processing_time": time.time() - start_time,
                }

        snapshot_id = data["snapshot_id"]
        company_domain = data["company_domain"]
        org_id = data["org_id"]

        # Get resolver service for BrightData operations
        resolver_service = await get_pitchbook_resolver_service()

        # Step 1: Poll for snapshot completion
        logger.info(f"Polling for BrightData snapshot completion: {snapshot_id}")
        poll_result = await _poll_brightdata_snapshot_completion(
            snapshot_id, resolver_service
        )
        if not poll_result["success"]:
            return {
                "success": False,
                "error": f"Snapshot polling failed: {poll_result['error']}",
                "processing_time": time.time() - start_time,
                "brightdata_status": poll_result.get("status", "poll_failed"),
            }

        logger.info(f"BrightData snapshot ready: {snapshot_id}")

        # Step 2: Download snapshot data
        download_result = await resolver_service._download_brightdata_snapshot(
            snapshot_id
        )
        if not download_result["success"]:
            return {
                "success": False,
                "error": f"Failed to download snapshot: {download_result['error']}",
                "processing_time": time.time() - start_time,
            }

        raw_data = download_result["data"]
        logger.info(
            "BrightData snapshot downloaded successfully",
            snapshot_id=snapshot_id,
            data_keys=list(raw_data.keys())
            if isinstance(raw_data, dict)
            else "not_dict",
        )

        # Store raw data to S3
        s3_storage = S3Storage()
        await s3_storage.initialize()

        s3_key = f"brightdata_company_pitchbook/{company_domain}.json"

        s3_data = {
            "snapshot_id": snapshot_id,
            "company_domain": company_domain,
            "org_id": org_id,
            "raw_data": raw_data,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        success = await s3_storage.put_object(s3_key, s3_data)
        if success:
            logger.info(f"Raw BrightData data stored to S3: {s3_key}")
        else:
            logger.error(f"Failed to store raw BrightData data to S3: {s3_key}")

        await s3_storage.cleanup()

        # Process the company data
        processing_result = await _process_pitchbook_data(
            raw_data, company_domain, org_id, snapshot_id, s3_key
        )

        if not processing_result["success"]:
            return {
                "success": False,
                "error": f"Data processing failed: {processing_result['error']}",
                "processing_time": time.time() - start_time,
            }

        # Update S3 data to include processed data
        s3_storage = S3Storage()
        await s3_storage.initialize()

        updated_s3_data = {
            "snapshot_id": snapshot_id,
            "company_domain": company_domain,
            "org_id": org_id,
            "raw_data": raw_data,
            "processed_data": processing_result["data"],
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        success = await s3_storage.put_object(s3_key, updated_s3_data)
        if success:
            logger.info(f"Updated S3 data with processed data: {s3_key}")
        else:
            logger.error(f"Failed to update S3 data with processed data: {s3_key}")

        await s3_storage.cleanup()

        # Prepare return result - only return S3 key, not the data
        return_result = {
            "success": True,
            "status": "completed",
            "s3_key": s3_key,
            "processing_time": time.time() - start_time,
        }

        logger.info(
            "BrightData PitchBook data processing completed successfully",
            snapshot_id=snapshot_id,
            company_domain=company_domain,
            processing_time=return_result["processing_time"],
            s3_key=s3_key,
        )

        return return_result

    except Exception as e:
        error_msg = f"BrightData PitchBook data processing task failed: {str(e)}"
        logger.error(error_msg, exc_info=True)

        return {
            "success": False,
            "error": error_msg,
            "processing_time": time.time() - start_time,
        }


async def _poll_brightdata_snapshot_completion(
    snapshot_id: str, resolver_service
) -> Dict[str, Any]:
    """
    Poll for BrightData snapshot completion.

    Args:
        snapshot_id: BrightData snapshot ID
        resolver_service: PitchBook resolver service instance

    Returns:
        Dictionary with success status and error if any
    """
    import asyncio

    max_poll_time = 600  # 10 minutes
    poll_interval = 10  # 10 seconds
    start_time = time.time()

    while time.time() - start_time < max_poll_time:
        try:
            # Check snapshot progress using BrightData API
            if not resolver_service.brightdata_client:
                return {
                    "success": False,
                    "status": "error",
                    "error": "BrightData client not initialized",
                }

            response = await resolver_service.brightdata_client.get(
                f"/datasets/v3/progress/{snapshot_id}"
            )
            response.raise_for_status()
            result = response.json()

            status = result.get("status")
            logger.info(f"Polling status: {status} for {snapshot_id}")

            if status == "ready":
                return {
                    "success": True,
                    "status": "ready",
                }
            elif status in ["error", "failed"]:
                return {
                    "success": False,
                    "status": "error",
                    "error": f"Job failed with status: {status}",
                }

            # Wait before next poll
            await asyncio.sleep(poll_interval)

        except Exception as e:
            logger.warning(f"Poll error: {e}")
            await asyncio.sleep(poll_interval)

    # Timeout
    return {
        "success": False,
        "status": "timeout",
        "error": f"Polling timed out after {max_poll_time} seconds",
    }


async def _process_pitchbook_data(
    raw_data: Any, company_domain: str, org_id: str, snapshot_id: str, s3_key: str
) -> Dict[str, Any]:
    """
    Process and clean PitchBook company data from BrightData.

    Args:
        raw_data: Raw data from BrightData
        company_domain: Company domain
        org_id: Organization ID
        snapshot_id: BrightData snapshot ID
        s3_key: S3 key for raw data

    Returns:
        Dictionary with success status and processed data
    """
    try:
        logger.info(
            "Processing PitchBook company data",
            company_domain=company_domain,
            snapshot_id=snapshot_id,
        )
        logger.info(f"Raw data from BrightData: {raw_data}")

        # Extract company data from raw response
        if type(raw_data) is list:
            company_data = raw_data[0]
        else:
            company_data = raw_data

        if not company_data:
            return {
                "success": False,
                "error": "No company data found in BrightData response",
            }

        logger.info(
            "Company data extracted from BrightData response",
            company_data_keys=list(company_data.keys())
            if isinstance(company_data, dict)
            else "not_dict",
        )

        # Log specific fields we're looking for
        target_fields = [
            "last_funding_amount",
            "research_analysis",
            "latest_deal_amount",
            "latest_deal_type",
        ]
        for field in target_fields:
            if field in company_data:
                logger.info(
                    f"Found {field} in raw PitchBook data: {company_data[field]}"
                )
            else:
                logger.info(f"Field {field} NOT found in raw PitchBook data")

        # Process PitchBook data to match our company schema
        processed_company_data = {
            "name": company_data.get("company_name"),
            "description": company_data.get("description"),
            "founded_year": company_data.get("year_founded"),
            "employee_count": company_data.get("employees"),
            "status": company_data.get("status"),
            "investments_count": company_data.get("investments"),
            "latest_deal_type": company_data.get("latest_deal_type"),
            "financing_rounds": company_data.get("financing_rounds"),
            "latest_deal_amount": company_data.get("latest_deal_amount"),
            "pitchbook_id": company_data.get("id"),
            "pitchbook_url": company_data.get("url"),
            "patents": company_data.get("patents"),
            "competitors": company_data.get("competitors"),
            "research_analysis": company_data.get("research_analysis"),
            "patent_activity": company_data.get("patent_activity"),
            "latest_deal_amount_value": company_data.get("latest_deal_amount_value"),
        }

        # Derive last_funding_amount from latest_deal_amount if not available
        if company_data.get("last_funding_amount"):
            processed_company_data["last_funding_amount"] = company_data.get(
                "last_funding_amount"
            )
        elif company_data.get("latest_deal_amount"):
            # Use latest_deal_amount as last_funding_amount if no specific last_funding_amount
            processed_company_data["last_funding_amount"] = company_data.get(
                "latest_deal_amount"
            )
            logger.info(
                f"Derived last_funding_amount from latest_deal_amount: {company_data.get('latest_deal_amount')}"
            )
        else:
            logger.info("No funding amount data available in PitchBook data")

        # Extract contact information
        contact_info = company_data.get("contact_information", [])
        for contact in contact_info:
            if isinstance(contact, dict):
                contact_type = contact.get("Type", "")
                value = contact.get("value", "")

                if contact_type == "Website":
                    processed_company_data["website"] = value
                elif contact_type == "Year Founded":
                    if value and not processed_company_data.get("founded_year"):
                        try:
                            processed_company_data["founded_year"] = int(value)
                        except (ValueError, TypeError):
                            pass
                elif contact_type == "Corporate Office":
                    processed_company_data["headquarters"] = value
                elif contact_type == "Investor Status":
                    processed_company_data["investor_status"] = value
                elif contact_type == "Primary Investor Type":
                    processed_company_data["primary_investor_type"] = value
                elif contact_type == "Other Investor Types":
                    processed_company_data["other_investor_types"] = value

        # Extract additional company information
        processed_company_data.update({
            "investor_status": next(
                (
                    c.get("value")
                    for c in contact_info
                    if isinstance(c, dict) and c.get("Type") == "Investor Status"
                ),
                None,
            ),
            "primary_investor_type": next(
                (
                    c.get("value")
                    for c in contact_info
                    if isinstance(c, dict) and c.get("Type") == "Primary Investor Type"
                ),
                None,
            ),
            "other_investor_types": next(
                (
                    c.get("value")
                    for c in contact_info
                    if isinstance(c, dict) and c.get("Type") == "Other Investor Types"
                ),
                None,
            ),
        })

        # Extract social links
        socials = company_data.get("company_socials", [])
        for social in socials:
            if isinstance(social, dict):
                domain = social.get("domain", "")
                link = social.get("link", "")

                if "linkedin.com" in domain:
                    processed_company_data["linkedin_url"] = link

                # Extract investments data
        investments = company_data.get("all_investments", [])
        if investments:
            processed_company_data["investments"] = investments

            # Add investment relationships to the main data
            processed_company_data["investment_relationships"] = []
            for investment in investments:
                if isinstance(investment, dict):
                    # Extract deal size if available
                    deal_size = None
                    if investment.get("deal_size"):
                        try:
                            deal_size = float(investment["deal_size"])
                        except (ValueError, TypeError):
                            pass

                    # Determine investment stage from deal type
                    investment_stage = None
                    deal_type = investment.get("deal_type")
                    if deal_type:
                        if "seed" in deal_type.lower():
                            investment_stage = "seed"
                        elif "series a" in deal_type.lower():
                            investment_stage = "series_a"
                        elif "series b" in deal_type.lower():
                            investment_stage = "series_b"
                        elif "series c" in deal_type.lower():
                            investment_stage = "series_c"
                        elif "ipo" in deal_type.lower():
                            investment_stage = "ipo"
                        elif "acquisition" in deal_type.lower():
                            investment_stage = "acquisition"
                        elif (
                            "accelerator" in deal_type.lower()
                            or "incubator" in deal_type.lower()
                        ):
                            investment_stage = "accelerator_incubator"

                    investment_relationship = {
                        "company_name": investment.get("company_name"),
                        "industry": investment.get("industry"),
                        "deal_date": investment.get("deal_date"),
                        "deal_size": deal_size,
                        "deal_type": deal_type,
                        "investment_stage": investment_stage,
                        "confidence_score": 0.9,
                    }
                    processed_company_data["investment_relationships"].append(
                        investment_relationship
                    )

                # Extract competitors if available
        competitors = company_data.get("competitors", [])
        if competitors:
            processed_company_data["competitors"] = competitors

            # Add competitor relationships to the main data
            processed_company_data["competitor_relationships"] = []
            for competitor in competitors:
                if isinstance(competitor, dict):
                    competitor_relationship = {
                        "name": competitor.get("name")
                        or competitor.get("company_name"),
                        "industry": competitor.get("industry"),
                        "url": competitor.get("url"),
                        "confidence_score": 0.8,
                    }
                    processed_company_data["competitor_relationships"].append(
                        competitor_relationship
                    )

        # Extract FAQ data
        faq = company_data.get("faq", [])
        if faq:
            processed_company_data["faq"] = faq

        # Log what's being returned
        logger.info(
            f"Final processed PitchBook data keys: {list(processed_company_data.keys())}"
        )
        logger.info(
            f"last_funding_amount in processed data: {processed_company_data.get('last_funding_amount')}"
        )
        logger.info(
            f"research_analysis in processed data: {processed_company_data.get('research_analysis')}"
        )

        return {
            "success": True,
            "data": processed_company_data,
        }

    except Exception as e:
        error_msg = f"Failed to process PitchBook data: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return {"success": False, "error": error_msg}


def _clean_pitchbook_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Clean and validate PitchBook company data.

    Args:
        data: Raw company data

    Returns:
        Cleaned company data
    """
    cleaned_data = data.copy()

    # Clean employee count
    if cleaned_data.get("employee_count"):
        try:
            if isinstance(cleaned_data["employee_count"], str):
                # Extract numbers from strings like "50-100 employees"
                import re

                numbers = re.findall(r"\d+", cleaned_data["employee_count"])
                if numbers:
                    cleaned_data["employee_count"] = int(numbers[0])
                else:
                    cleaned_data["employee_count"] = None
            else:
                cleaned_data["employee_count"] = int(cleaned_data["employee_count"])
        except (ValueError, TypeError):
            cleaned_data["employee_count"] = None

    # Clean founded year
    if cleaned_data.get("founded_year"):
        try:
            cleaned_data["founded_year"] = int(cleaned_data["founded_year"])
        except (ValueError, TypeError):
            cleaned_data["founded_year"] = None

    # Clean funding amounts
    funding_fields = ["total_funding", "last_funding_amount", "valuation", "revenue"]
    for field in funding_fields:
        if cleaned_data.get(field):
            try:
                if isinstance(cleaned_data[field], str):
                    # Remove currency symbols and convert to float
                    import re

                    amount_str = re.sub(r"[^\d.]", "", cleaned_data[field])
                    if amount_str:
                        cleaned_data[field] = float(amount_str)
                else:
                    cleaned_data[field] = float(cleaned_data[field])
            except (ValueError, TypeError):
                cleaned_data[field] = None

    # Ensure lists are actually lists
    list_fields = [
        "investors",
        "founders",
        "executives",
        "technologies",
        "keywords",
        "competitors",
        "funding_rounds",
    ]
    for field in list_fields:
        if field in cleaned_data and not isinstance(cleaned_data[field], list):
            cleaned_data[field] = []

    return cleaned_data
