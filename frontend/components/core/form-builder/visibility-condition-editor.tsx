"use client"

import React, { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFieldArray } from 'react-hook-form';
import * as z from 'zod';
import { Plus, Trash2, Eye } from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Toolt<PERSON>, Toolt<PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

import { VisibilityCondition, Question, ConditionClause } from '@/lib/types/form';

// Available operators for conditions
const CONDITION_OPERATORS = [
  { value: '==', label: 'equals', symbol: '=', description: 'Value must be exactly equal' },
  { value: '!=', label: 'does not equal', symbol: '≠', description: 'Value must not be equal' },
  { value: '>', label: 'is greater than', symbol: '>', description: 'Numeric value must be greater' },
  { value: '<', label: 'is less than', symbol: '<', description: 'Numeric value must be less' },
  { value: '>=', label: 'is greater than or equal to', symbol: '≥', description: 'Numeric value must be greater or equal' },
  { value: '<=', label: 'is less than or equal to', symbol: '≤', description: 'Numeric value must be less or equal' },
];

// Validation schema for visibility conditions
const conditionSchema = z.object({
  operator: z.enum(['and', 'or']),
  conditions: z.array(
    z.object({
      question_id: z.string().min(1, 'Question is required'),
      operator: z.enum(['==', '!=', '>', '<', '>=', '<=']).default('=='),
      value: z.any(),
      section_instance_index: z.number().optional(),
    })
  ).min(1, 'At least one condition is required'),
});

type ConditionFormValues = z.infer<typeof conditionSchema>;

interface VisibilityConditionEditorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  condition?: VisibilityCondition;
  questions: Question[];
  currentQuestionId?: string;
  onSave: (condition: VisibilityCondition) => void;
}

export function VisibilityConditionEditor({
  open,
  onOpenChange,
  condition,
  questions,
  currentQuestionId,
  onSave,
}: VisibilityConditionEditorProps) {
  // Filter out the current question from available questions
  const availableQuestions = questions.filter(q =>
    (q._id || q.id) !== currentQuestionId
  );

  const form = useForm<ConditionFormValues>({
    resolver: zodResolver(conditionSchema),
    defaultValues: {
      operator: (condition?.operator === 'and' || condition?.operator === 'or') ? condition.operator : 'and',
      conditions: condition?.conditions?.map(c => ({
        question_id: c.question_id,
        operator: (c as any).operator || '==',
        value: c.value,
        section_instance_index: c.section_instance_index
      })) || [
        { question_id: '', operator: '==', value: '' }
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'conditions',
  });

  const handleSave = (values: ConditionFormValues) => {
    const visibilityCondition: VisibilityCondition = {
      operator: values.operator as any,
      conditions: values.conditions.filter(c => c.question_id && c.value !== undefined) as any,
    };

    onSave(visibilityCondition);
    onOpenChange(false);
  };

  const addCondition = () => {
    append({ question_id: '', operator: '==', value: '' });
  };

  const getQuestionById = (questionId: string) => {
    return availableQuestions.find(q => (q._id || q.id) === questionId);
  };

  const renderValueInput = (questionId: string, value: any, onChange: (value: any) => void) => {
    const question = getQuestionById(questionId);

    if (!question) {
      return (
        <Input
          placeholder="Enter value"
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
        />
      );
    }

    switch (question.type) {
      case 'boolean':
        return (
          <Select value={value || ''} onValueChange={onChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select value" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Yes</SelectItem>
              <SelectItem value="false">No</SelectItem>
            </SelectContent>
          </Select>
        );

      case 'single_select':
      case 'multi_select':
        return (
          <Select value={value || ''} onValueChange={onChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select option" />
            </SelectTrigger>
            <SelectContent>
              {question.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'number':
      case 'range':
        return (
          <Input
            type="number"
            placeholder="Enter number"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
          />
        );

      default:
        return (
          <Input
            placeholder="Enter value"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
          />
        );
    }
  };

  return (
    <TooltipProvider>
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[80vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="size-5" />
            Edit Visibility Condition
          </DialogTitle>
          <DialogDescription>
            Define when this question should be visible based on other questions' answers.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSave)} className="space-y-6">
            {/* Operator Selection */}
            <div className="space-y-2">
              <Label>Combine conditions with:</Label>
              <FormField
                control={form.control}
                name="operator"
                render={({ field }) => (
                  <FormItem>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="and">AND</SelectItem>
                        <SelectItem value="or">OR</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Separator />

            {/* Conditions */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-base font-medium">Conditions</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addCondition}
                  disabled={availableQuestions.length === 0}
                >
                  <Plus className="mr-2 size-4" />
                  Add Condition
                </Button>
              </div>

              {availableQuestions.length === 0 && (
                <Card>
                  <CardContent className="pt-6">
                    <p className="text-center text-sm text-muted-foreground">
                      No other questions available. Add more questions to create visibility conditions.
                    </p>
                  </CardContent>
                </Card>
              )}

              {fields.map((field, index) => (
                <Card key={field.id}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm">
                        Condition {index + 1}
                      </CardTitle>
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => remove(index)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="size-4" />
                        </Button>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-3 gap-3">
                      {/* Question Selection */}
                      <div className="space-y-2">
                        <Label className="text-xs">Question</Label>
                        <FormField
                          control={form.control}
                          name={`conditions.${index}.question_id`}
                          render={({ field }) => (
                            <FormItem>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select question" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {availableQuestions.map((question) => (
                                    <SelectItem
                                      key={question._id || question.id}
                                      value={question._id || question.id || ''}
                                    >
                                      <div className="flex items-center gap-2">
                                        <Badge variant="outline" className="text-xs">
                                          {question.type}
                                        </Badge>
                                        <span className="truncate">{question.label}</span>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Operator Selection */}
                      <div className="space-y-2">
                        <Label className="text-xs">Operator</Label>
                        <FormField
                          control={form.control}
                          name={`conditions.${index}.operator`}
                          render={({ field }) => (
                            <FormItem>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div>
                                    <Select onValueChange={field.onChange} value={field.value}>
                                      <FormControl>
                                        <SelectTrigger className="w-[60px]">
                                          <SelectValue>
                                            {(() => {
                                              const op = CONDITION_OPERATORS.find(o => o.value === field.value);
                                              return op ? (
                                                <span className="font-mono text-lg font-semibold">{op.symbol}</span>
                                              ) : '=';
                                            })()}
                                          </SelectValue>
                          </SelectTrigger>
                                      </FormControl>
                          <SelectContent>
                                        {CONDITION_OPERATORS.map((op) => (
                                          <SelectItem key={op.value} value={op.value}>
                                            <div className="flex items-center gap-2">
                                              <span className="font-mono text-sm">{op.symbol}</span>
                                              <span className="text-sm">{op.label}</span>
                                            </div>
                                          </SelectItem>
                                        ))}
                          </SelectContent>
                        </Select>
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p className="text-xs">
                                    {CONDITION_OPERATORS.find(o => o.value === field.value)?.description || 'Select an operator'}
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Value Input */}
                      <div className="space-y-2">
                        <Label className="text-xs">Value</Label>
                        <FormField
                          control={form.control}
                          name={`conditions.${index}.value`}
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                {renderValueInput(
                                  form.watch(`conditions.${index}.question_id`),
                                  field.value,
                                  field.onChange
                                )}
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit">
                Save Condition
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
    </TooltipProvider>
  );
}
