#!/bin/bash

# Fix common syntax issues caused by dark: class removal
find . -name "*.tsx" -o -name "*.ts" | grep -v node_modules | while read file; do
  # Fix incomplete className strings
  sed -i '' 's/className="[^"]*$/&"/g' "$file"
  
  # Fix broken JSX with extra quotes
  sed -i '' 's/""}/"/g; s/""}/"}/g; s/"">/"/g' "$file"
  
  # Fix double spaces
  sed -i '' 's/  / /g' "$file"
  
  # Fix incomplete template literals
  sed -i '' 's/`[^`]*$/&`/g' "$file"
  
  # Fix broken className concatenations
  sed -i '' 's/className="\([^"]*\) $/className="\1"/g' "$file"
done

echo "Syntax fixes applied" 