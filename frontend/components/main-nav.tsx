"use client"

import * as React from "react"
import Link from "next/link"
import { useSelectedLayoutSegment } from "next/navigation"

import { MainNavItem } from "@/types"
// import { siteConfig } from "@/config/site"
import { cn } from "@/lib/utils"
import { Icons } from "@/components/icons"

interface MainNavProps {
  items?: MainNavItem[]
  children?: React.ReactNode
}

export function MainNav({ items, children }: MainNavProps) {
  const segment = useSelectedLayoutSegment()
  const [showMobileMenu, setShowMobileMenu] = React.useState<boolean>(false)
  const [MobileNav, setMobileNav] = React.useState<React.ComponentType<any> | null>(null)

  // Dynamic import to avoid webpack issues
  React.useEffect(() => {
    if (showMobileMenu && !MobileNav) {
      import("@/components/mobile-nav").then((module) => {
        setMobileNav(() => module.MobileNav)
      }).catch((error) => {
        console.warn('Failed to load MobileNav:', error)
      })
    }
  }, [showMobileMenu, MobileNav])

  return (
    <div className="flex gap-6 md:gap-10">
      {/* Removed TractionX logo and name from top bar */}
      {items?.length ? (
        <nav className="hidden gap-6 md:flex">
          {items?.map((item, index) => (
            <Link
              key={index}
              href={item.disabled ? "#" : item.href}
              className={cn(
                "flex items-center text-lg font-medium transition-colors hover:text-foreground/80 sm:text-sm",
                "rounded-lg px-3 py-2 hover:bg-accent", // Enhanced touch targets
                item.href.startsWith(`/${segment}`)
                  ? "bg-accent text-foreground"
                  : "text-foreground/60",
                item.disabled && "cursor-not-allowed opacity-80"
              )}
            >
              {item.title}
            </Link>
          ))}
        </nav>
      ) : null}
      <button
        className={cn(
          "flex items-center space-x-2 md:hidden",
          "rounded-xl p-2 transition-all duration-200",
          "touch-target hover:bg-accent active:scale-95"
        )}
        onClick={() => setShowMobileMenu(!showMobileMenu)}
      >
        {showMobileMenu ? <Icons.close /> : <Icons.logo />}
        <span className="font-bold">Menu</span>
      </button>
      {showMobileMenu && items && MobileNav && (
        <MobileNav items={items}>{children}</MobileNav>
      )}
    </div>
  )
}
