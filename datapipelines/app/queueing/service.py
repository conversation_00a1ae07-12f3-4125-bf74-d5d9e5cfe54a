"""
Queue Service Implementation

This module provides the main QueueService implementation that acts as a high-level
interface for job management, built on top of pluggable backends.
"""

from typing import Any, Dict, List, Optional, Union

from app.configs import get_logger
from app.queueing.backends.redis_backend import RedisQueueBackend
from app.queueing.interfaces import (
    JobPriority,
    JobProgress,
    JobResult,
    QueueBackend,
    QueueService,
    QueueStats,
)

logger = get_logger(__name__)


class TractionXQueueService(QueueService):
    """Main queue service implementation for TractionX Data Pipeline Service."""

    def __init__(self, backend: QueueBackend):
        """Initialize the queue service with a backend."""
        super().__init__(backend)
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the queue service."""
        if self._initialized:
            return

        await self.backend.initialize()
        self._initialized = True
        logger.info("TractionX Queue Service initialized successfully")

    async def cleanup(self) -> None:
        """Clean up queue service resources."""
        if not self._initialized:
            return

        await self.backend.cleanup()
        self._initialized = False
        logger.info("TractionX Queue Service cleaned up")

    async def enqueue_job(
        self,
        job_func: str,
        job_args: Dict[str, Any],
        priority: JobPriority = JobPriority.NORMAL,
        job_id: Optional[str] = None,
        delay_seconds: int = 0,
        retry_config: Optional[Dict[str, Any]] = None,
        meta: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Enqueue a job with the specified priority.

        Args:
            job_func: Function name to execute
            job_args: Arguments to pass to the function
            priority: Job priority level
            job_id: Optional custom job ID
            delay_seconds: Delay before job becomes available
            retry_config: Retry configuration

        Returns:
            Job ID
        """
        if not self._initialized:
            await self.initialize()

        queue_name = priority.value

        logger.info(
            f"Enqueuing job {job_func} to {queue_name} queue",
            job_func=job_func,
            queue_name=queue_name,
            delay_seconds=delay_seconds,
        )

        # Add detailed logging for debugging
        logger.info(f"Job args type: {type(job_args)}")
        logger.info(
            f"Job args keys: {list(job_args.keys()) if isinstance(job_args, dict) else 'Not a dict'}"
        )

        # Test JSON serialization of job_args
        try:
            import json
            from datetime import datetime as dt

            class DateTimeEncoder(json.JSONEncoder):
                def default(self, obj):
                    if isinstance(obj, dt):
                        return obj.isoformat()
                    return super().default(obj)

            test_json = json.dumps(job_args, cls=DateTimeEncoder)
            logger.info(
                f"Job args JSON serialization test passed, size: {len(test_json)} characters"
            )
        except Exception as json_error:
            logger.error(f"Job args JSON serialization test failed: {json_error}")
            logger.error(f"Job args structure: {type(job_args)}")
            raise

        # Handle barrier-aware job registration
        if meta and "barrier_group_id" in meta and "task_name" in meta:
            from app.configs import settings
            from app.queueing.barrier_sync import BarrierSync

            # Initialize barrier sync if not already done
            if not hasattr(self, "_barrier_sync"):
                self._barrier_sync = BarrierSync(
                    redis_url=settings.redis_connection_string,
                    mongo_url=settings.mongo_connection_string
                    if hasattr(settings, "mongo_connection_string")
                    else "mongodb://localhost:27017",
                )
                await self._barrier_sync.initialize()

            # Register barrier if this is the first task
            barrier_group_id = meta["barrier_group_id"]
            entity_type = meta.get("entity_type", "company")
            entity_id = meta.get(
                "entity_id",
                barrier_group_id.split(":", 1)[1]
                if ":" in barrier_group_id
                else barrier_group_id,
            )

            # Check if barrier already exists
            existing_barrier = await self._barrier_sync.get_barrier_status(
                barrier_group_id
            )
            if not existing_barrier:
                # Register new barrier with initial task
                await self._barrier_sync.register_barrier(
                    group_id=barrier_group_id,
                    entity_type=entity_type,
                    entity_id=entity_id,
                    initial_tasks=[meta["task_name"]],
                )
            else:
                # Register additional task to existing barrier
                await self._barrier_sync.register_tasks(
                    group_id=barrier_group_id, new_tasks=[meta["task_name"]]
                )

        # Pass meta as part of job_args for backward compatibility
        job_data = {
            "job_func": job_func,
            "job_args": job_args,
            "meta": meta,
        }

        job_id = await self.backend.enqueue(
            queue_name=queue_name,
            job_func=job_func,
            job_args=job_data,
            job_id=job_id,
            delay_seconds=delay_seconds,
            retry_config=retry_config,
        )

        logger.info(f"Job {job_id} enqueued successfully to {queue_name} queue")
        return job_id

    async def get_job_status(self, job_id: str) -> Optional[JobResult]:
        """Get the status of a job by ID."""
        if not self._initialized:
            await self.initialize()

        return await self.backend.get_job_status(job_id)

    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a pending or running job."""
        if not self._initialized:
            await self.initialize()

        logger.info(f"Cancelling job {job_id}")
        success = await self.backend.cancel_job(job_id)

        if success:
            logger.info(f"Job {job_id} cancelled successfully")
        else:
            logger.warning(f"Failed to cancel job {job_id}")

        return success

    async def retry_job(self, job_id: str, delay_seconds: int = 0) -> bool:
        """Retry a failed job."""
        if not self._initialized:
            await self.initialize()

        # Get job details to check if it's a merge job
        job_status = await self.backend.get_job_status(job_id)
        if job_status and isinstance(self.backend, RedisQueueBackend):
            try:
                job_data = await self.backend._get_job_data(job_id)
                job_func = job_data.get("job_func") if job_data else None

                # Special handling for merge_comprehensive_enrichment_results
                if job_func == "merge_comprehensive_enrichment_results":
                    logger.info(
                        f"Special retry for merge job {job_id} - using merge and process utility"
                    )

                    # Import here to avoid circular imports
                    from app.utils.retry_merge_and_process import (
                        retry_merge_and_process,
                    )

                    # Get Redis URL from backend if available
                    redis_url = getattr(
                        self.backend, "redis_url", "redis://localhost:6379/0"
                    )

                    # Use the special utility
                    result = await retry_merge_and_process(job_id, redis_url)

                    if result.get("success"):
                        logger.info(
                            f"Merge and process retry successful for job {job_id}"
                        )
                        return True
                    else:
                        logger.error(
                            f"Merge and process retry failed for job {job_id}: {result.get('error')}"
                        )
                        return False
            except Exception as e:
                logger.warning(f"Error checking job function for {job_id}: {e}")
                # Fall back to normal retry

        # Normal retry logic
        logger.info(f"Retrying job {job_id} with delay {delay_seconds}s")
        success = await self.backend.retry_job(job_id, delay_seconds)

        if success:
            logger.info(f"Job {job_id} queued for retry")
        else:
            logger.warning(f"Failed to retry job {job_id}")

        return success

    async def update_job_progress(self, job_id: str, progress: JobProgress) -> bool:
        """Update progress for a running job."""
        if not self._initialized:
            await self.initialize()

        logger.debug(
            f"Updating progress for job {job_id}: {progress.current_step} ({progress.overall_progress:.1%})"
        )
        success = await self.backend.update_job_progress(job_id, progress)

        if not success:
            logger.warning(f"Failed to update progress for job {job_id}")

        return success

    async def get_queue_stats(
        self, queue_name: Optional[str] = None
    ) -> Union[QueueStats, Dict[str, QueueStats]]:
        """Get statistics for queues."""
        if not self._initialized:
            await self.initialize()

        if queue_name:
            return await self.backend.get_queue_stats(queue_name)
        else:
            # Get stats for all queues
            stats = {}
            for queue in self.queues:
                try:
                    stats[queue] = await self.backend.get_queue_stats(queue)
                except Exception as e:
                    logger.warning(f"Failed to get stats for queue {queue}: {e}")
                    # Create empty stats for failed queues
                    stats[queue] = QueueStats(
                        queue_name=queue,
                        pending_count=0,
                        running_count=0,
                        completed_count=0,
                        failed_count=0,
                        total_processed=0,
                    )
            return stats

    async def list_queues(self) -> List[str]:
        """List all available queues."""
        if not self._initialized:
            await self.initialize()

        return await self.backend.list_queues()

    async def purge_queue(self, queue_name: str) -> int:
        """Remove all jobs from a queue."""
        if not self._initialized:
            await self.initialize()

        logger.warning(f"Purging all jobs from queue {queue_name}")
        count = await self.backend.purge_queue(queue_name)
        logger.info(f"Purged {count} jobs from queue {queue_name}")

        return count

    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check on the queue system."""
        if not self._initialized:
            try:
                await self.initialize()
            except Exception as e:
                return {
                    "status": "unhealthy",
                    "error": str(e),
                    "initialized": False,
                }

        try:
            # Get basic stats for all queues
            queue_stats = await self.get_queue_stats()

            # Ensure we have a dict of stats
            if isinstance(queue_stats, dict):
                stats = queue_stats
            else:
                # Single queue stats, shouldn't happen here but handle it
                stats = {"unknown": queue_stats}

            # Calculate total jobs across all queues
            total_pending = sum(stat.pending_count for stat in stats.values())
            total_running = sum(stat.running_count for stat in stats.values())
            total_completed = sum(stat.completed_count for stat in stats.values())
            total_failed = sum(stat.failed_count for stat in stats.values())

            # Check if any queues have stalled jobs (running for too long)
            stalled_queues = []
            for queue_name, stat in stats.items():
                if stat.running_count > 0 and stat.avg_processing_time:
                    # Simple heuristic: if average processing time > 10 minutes, consider stalled
                    if stat.avg_processing_time > 600:
                        stalled_queues.append(queue_name)

            health_status = "healthy"
            if stalled_queues:
                health_status = "degraded"
            elif total_failed > total_completed * 0.1:  # More than 10% failure rate
                health_status = "degraded"

            return {
                "status": health_status,
                "initialized": True,
                "queues": list(stats.keys()),
                "total_pending": total_pending,
                "total_running": total_running,
                "total_completed": total_completed,
                "total_failed": total_failed,
                "stalled_queues": stalled_queues,
                "queue_stats": {
                    name: {
                        "pending": stat.pending_count,
                        "running": stat.running_count,
                        "completed": stat.completed_count,
                        "failed": stat.failed_count,
                        "avg_processing_time": stat.avg_processing_time,
                    }
                    for name, stat in stats.items()
                },
            }

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "initialized": self._initialized,
            }
