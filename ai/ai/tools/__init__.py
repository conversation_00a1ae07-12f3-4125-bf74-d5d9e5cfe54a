"""
TractionX AI Agent Tools Package.

This package contains all the tools available to AI agents in the TractionX system.
Tools are automatically discovered and registered using the @tool decorator.

Usage:
    from ai.tools.base import tool_registry

    # Get all available tools
    all_tools = tool_registry.get_all_tools()

    # Get tools by category
    analysis_tools = tool_registry.get_tools_by_category("analysis")

    # Get specific tool
    tool = tool_registry.get_tool_instance("thesis_matcher")
"""

# Import all tool modules to trigger registration
from . import base
from . import thesis_tools
from . import founder_tools
from . import market_tools
from . import vector_tools
from . import websearch
from . import news_intelligence

# Import document and memo tools if they exist
try:
    from . import document_tools
    from . import memo_tools
except ImportError:
    pass

# Export the registry for easy access
from .base import tool_registry, tool

__all__ = [
    "tool_registry",
    "tool",
]
