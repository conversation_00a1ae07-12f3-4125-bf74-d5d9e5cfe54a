"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { motion } from "framer-motion"
import * as z from "zod"
import { Loader2, Building2, Globe, Mail, MapPin, FileText, Save, Crown } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { LogoUploader } from "./logo-uploader"
import { UploadsAPI } from "@/lib/api/uploads-api"
import { useAuth } from "@/lib/auth-context"

const organizationSchema = z.object({
  name: z.string().min(2, "Organization name must be at least 2 characters"),
  subdomain: z.string()
    .min(3, "Subdomain must be at least 3 characters")
    .regex(/^[a-z0-9-]+$/, "Subdomain can only contain lowercase letters, numbers, and hyphens"),
  website: z.string().url("Please enter a valid website URL").optional().or(z.literal("")),
  contactEmail: z.string().email("Please enter a valid email address").optional().or(z.literal("")),
  address: z.string().optional(),
  description: z.string().optional(),
})

type OrganizationFormValues = z.infer<typeof organizationSchema>

interface OrganizationFormProps {
  organization: {
    id: string
    name: string
    subdomain?: string
    website?: string
    contact_email?: string
    address?: string
    description?: string
    logo_url?: string
    plan?: string
  }
  onUpdate: (data: OrganizationFormValues & { logoUrl?: string }) => Promise<void>
}

export function OrganizationForm({
  organization,
  onUpdate,
}: OrganizationFormProps) {
  const { token } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [currentLogo, setCurrentLogo] = useState(organization.logo_url)

  const form = useForm<OrganizationFormValues>({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      name: organization.name,
      subdomain: organization.subdomain || "",
      website: organization.website || "",
      contactEmail: organization.contact_email || "",
      address: organization.address || "",
      description: organization.description || "",
    },
  })

  const handleSubmit = async (data: OrganizationFormValues) => {
    setIsLoading(true)
    try {
      await onUpdate({
        ...data,
        logoUrl: currentLogo,
      })
      
      toast({
        title: "Organization updated",
        description: "Your organization settings have been updated successfully.",
      })
    } catch (error) {
      console.error('Organization update error:', error)
      toast({
        title: "Update failed",
        description: error instanceof Error ? error.message : "Failed to update organization",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogoUpload = async (file: File) => {
    if (!token) throw new Error("No authentication token")
    const logoUrl = await UploadsAPI.uploadLogo(file, token)
    setCurrentLogo(logoUrl)
    return logoUrl
  }

  const handleLogoRemove = async () => {
    setCurrentLogo(undefined)
    // TODO: Implement logo removal API call if needed
  }

  const isDirty = form.formState.isDirty || currentLogo !== organization.logo_url

  // Note: Admin check is handled at the page/route level

  return (
    <div className="space-y-8">
      {/* Logo Section */}
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Organization Branding
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            Upload your organization logo to personalize shared forms and public pages
          </p>
        </div>
        
        <LogoUploader
          currentLogo={currentLogo}
          organizationName={organization.name}
          onUpload={handleLogoUpload}
          onRemove={handleLogoRemove}
        />
      </div>

      <Separator className="bg-gray-200 dark:bg-gray-700" />

      {/* Organization Information */}
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Organization Details
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              Update your organization information and contact details
            </p>
          </div>
          
          <Badge variant="secondary" className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
            {organization.plan || "Basic"} Plan
          </Badge>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          {/* Organization Name */}
          <div className="space-y-2">
            <Label htmlFor="name" className="flex items-center text-sm font-medium">
              <Building2 className="mr-2 size-4" />
              Organization Name
            </Label>
            <Input
              id="name"
              type="text"
              placeholder="Enter organization name"
              className="h-11 border-white/20 bg-white/50 focus:border-blue-500/50 focus:ring-blue-500/20 dark:border-white/10 dark:bg-black/50"
              {...form.register("name")}
            />
            {form.formState.errors.name && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.name.message}
              </motion.p>
            )}
          </div>

          {/* Subdomain */}
          <div className="space-y-2">
            <Label htmlFor="subdomain" className="flex items-center text-sm font-medium">
              <Globe className="mr-2 size-4" />
              Subdomain
            </Label>
            <div className="flex">
              <Input
                id="subdomain"
                type="text"
                placeholder="your-org"
                className="h-11 rounded-r-none border-white/20 bg-white/50 focus:border-blue-500/50 focus:ring-blue-500/20 dark:border-white/10 dark:bg-black/50"
                {...form.register("subdomain")}
              />
              <div className="flex items-center rounded-r-lg border border-l-0 border-gray-300 bg-gray-100 px-3 text-sm text-gray-500 dark:border-gray-600 dark:bg-gray-800">
                .tractionx.ai
              </div>
            </div>
            {form.formState.errors.subdomain && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.subdomain.message}
              </motion.p>
            )}
            <p className="text-xs text-gray-500">
              This will be your organization's unique URL for shared forms
            </p>
          </div>

          {/* Website */}
          <div className="space-y-2">
            <Label htmlFor="website" className="flex items-center text-sm font-medium">
              <Globe className="mr-2 size-4" />
              Website
            </Label>
            <Input
              id="website"
              type="url"
              placeholder="https://yourcompany.com"
              className="h-11 border-white/20 bg-white/50 focus:border-blue-500/50 focus:ring-blue-500/20 dark:border-white/10 dark:bg-black/50"
              {...form.register("website")}
            />
            {form.formState.errors.website && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.website.message}
              </motion.p>
            )}
          </div>

          {/* Contact Email */}
          <div className="space-y-2">
            <Label htmlFor="contactEmail" className="flex items-center text-sm font-medium">
              <Mail className="mr-2 size-4" />
              Contact Email
            </Label>
            <Input
              id="contactEmail"
              type="email"
              placeholder="<EMAIL>"
              className="h-11 border-white/20 bg-white/50 focus:border-blue-500/50 focus:ring-blue-500/20 dark:border-white/10 dark:bg-black/50"
              {...form.register("contactEmail")}
            />
            {form.formState.errors.contactEmail && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.contactEmail.message}
              </motion.p>
            )}
          </div>
        </div>

        {/* Address */}
        <div className="space-y-2">
          <Label htmlFor="address" className="flex items-center text-sm font-medium">
            <MapPin className="mr-2 size-4" />
            Address
          </Label>
          <Input
            id="address"
            type="text"
            placeholder="123 Main St, City, State, Country"
            className="h-11 border-white/20 bg-white/50 focus:border-blue-500/50 focus:ring-blue-500/20 dark:border-white/10 dark:bg-black/50"
            {...form.register("address")}
          />
        </div>

        {/* Description */}
        <div className="space-y-2">
          <Label htmlFor="description" className="flex items-center text-sm font-medium">
            <FileText className="mr-2 size-4" />
            Description
          </Label>
          <Textarea
            id="description"
            placeholder="Brief description of your organization..."
            rows={4}
            className="border-white/20 bg-white/50 focus:border-blue-500/50 focus:ring-blue-500/20 dark:border-white/10 dark:bg-black/50"
            {...form.register("description")}
          />
          <p className="text-xs text-gray-500">
            This description may appear on shared forms and public pages
          </p>
        </div>

        {/* Save Button */}
        <div className="flex items-center justify-between pt-4">
          <div className="flex items-center space-x-4">
            <Button
              type="submit"
              disabled={!isDirty || isLoading}
              className="bg-gradient-to-r from-blue-600 to-purple-600 font-medium text-white shadow-lg shadow-blue-500/25 transition-all duration-200 hover:from-blue-700 hover:to-purple-700"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 size-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 size-4" />
                  Save Changes
                </>
              )}
            </Button>

            {isDirty && (
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-sm text-amber-600 dark:text-amber-400"
              >
                You have unsaved changes
              </motion.p>
            )}
          </div>
        </div>
      </form>
    </div>
  )
}
