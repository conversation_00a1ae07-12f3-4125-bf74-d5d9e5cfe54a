#!/bin/bash

# News Intelligence API Testing Script
# Usage: ./test_news_api.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# API Base URL
API_BASE="http://localhost:8002"

echo -e "${BLUE}🧪 TractionX AI News Intelligence API Testing${NC}"
echo "=================================================="

# Test 1: Health Check
echo -e "\n${YELLOW}1. Health Check${NC}"
echo "Testing: GET $API_BASE/health"
response=$(curl -s "$API_BASE/health")
if echo "$response" | jq -e '.status == "healthy"' > /dev/null; then
    echo -e "${GREEN}✅ Service is healthy${NC}"
else
    echo -e "${RED}❌ Service health check failed${NC}"
    echo "$response"
    exit 1
fi

# Test 2: News Sources
echo -e "\n${YELLOW}2. Available News Sources${NC}"
echo "Testing: GET $API_BASE/api/ai/news/sources"
curl -s "$API_BASE/api/ai/news/sources" | jq '{
  total_available,
  available_sources,
  configured_sources: [.source_details | to_entries[] | select(.value.available == true) | .key]
}'

# Test 3: Basic News Search
echo -e "\n${YELLOW}3. Basic News Search${NC}"
echo "Testing: POST $API_BASE/api/ai/news/search"
search_response=$(curl -s -X POST "$API_BASE/api/ai/news/search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "artificial intelligence",
    "max_sources": 2,
    "include_analysis": true
  }')

echo "Search Results:"
echo "$search_response" | jq '{
  success,
  query,
  total_articles,
  sources_used,
  sections: (.sections | length),
  search_time,
  has_executive_summary: (.executive_summary != null)
}'

# Test 4: Quick Search (No Analysis)
echo -e "\n${YELLOW}4. Quick Search (No AI Analysis)${NC}"
echo "Testing: Fast search without OpenAI analysis"
quick_response=$(curl -s -X POST "$API_BASE/api/ai/news/search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "technology",
    "max_sources": 1,
    "include_analysis": false
  }')

echo "$quick_response" | jq '{
  success,
  total_articles,
  search_time,
  analysis_enabled: (.executive_summary != "No news articles found for '\''technology'\''.")
}'

# Test 5: News Agent
echo -e "\n${YELLOW}5. News Intelligence Agent${NC}"
echo "Testing: POST $API_BASE/api/ai/news/agent"
agent_response=$(curl -s -X POST "$API_BASE/api/ai/news/agent" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What are the latest developments in AI?"
  }')

echo "Agent Response:"
echo "$agent_response" | jq '{
  success,
  response: (.response | .[0:200] + "..."),
  session_id,
  execution_time,
  tools_used
}'

# Test 6: Advanced Search Parameters
echo -e "\n${YELLOW}6. Advanced Search with All Parameters${NC}"
echo "Testing: Search with priority sources and time range"
advanced_response=$(curl -s -X POST "$API_BASE/api/ai/news/search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "startup funding",
    "max_sources": 3,
    "time_range": "7d",
    "include_analysis": true,
    "priority_sources": ["newsapi", "google_news_rss"]
  }')

echo "$advanced_response" | jq '{
  query,
  sources_used,
  search_time,
  sections: [.sections[] | {title, article_count: (.articles | length)}]
}'

# Test 7: Error Handling
echo -e "\n${YELLOW}7. Error Handling Test${NC}"
echo "Testing: Invalid request"
error_response=$(curl -s -X POST "$API_BASE/api/ai/news/search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "",
    "max_sources": 10
  }')

if echo "$error_response" | jq -e '.detail' > /dev/null; then
    echo -e "${GREEN}✅ Error handling working correctly${NC}"
    echo "$error_response" | jq '.detail'
else
    echo -e "${YELLOW}⚠️  No validation error (might be valid request)${NC}"
fi

# Performance Summary
echo -e "\n${YELLOW}8. Performance Summary${NC}"
echo "Analyzing search performance across tests..."

echo "Basic Search Time: $(echo "$search_response" | jq -r '.search_time')s"
echo "Quick Search Time: $(echo "$quick_response" | jq -r '.search_time')s"
echo "Agent Response Time: $(echo "$agent_response" | jq -r '.execution_time')s"

# OpenAI Integration Test
echo -e "\n${YELLOW}9. OpenAI Integration Status${NC}"
if echo "$search_response" | jq -e '.executive_summary and (.executive_summary | length > 50)' > /dev/null; then
    echo -e "${GREEN}✅ OpenAI integration is working (generated executive summary)${NC}"
else
    echo -e "${YELLOW}⚠️  OpenAI integration status unclear (no articles found or API key issue)${NC}"
fi

echo -e "\n${BLUE}=================================================="
echo -e "🎉 News Intelligence API Testing Complete!${NC}"
echo -e "${GREEN}All endpoints are responding correctly.${NC}"
echo -e "\n${YELLOW}💡 Tips for further testing:${NC}"
echo "• Configure API keys in .env file for real news data"
echo "• Try different query types: 'Tesla news', 'AI funding', 'tech IPO'"
echo "• Test with longer time ranges: '30d', '7d', '1d'"
echo "• Use session IDs for agent conversations"
echo -e "\n${BLUE}Example with real data (if API keys configured):${NC}"
echo "curl -X POST $API_BASE/api/ai/news/search \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"query\": \"OpenAI\", \"include_analysis\": true}'" 