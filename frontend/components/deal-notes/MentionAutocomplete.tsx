"use client"

/**
 * Mention Autocomplete Component
 * 
 * Dropdown for selecting users when typing @ in the editor
 */

import React, { useCallback, useMemo } from 'react';
import { Range } from 'slate';
import { useFloating, offset, flip, shift } from '@floating-ui/react';
import { OrgUser } from '@/lib/types/deal-notes';
import { filterUsersForMention } from '@/lib/utils/mentions';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

interface MentionAutocompleteProps {
  target: Range | null;
  search: string;
  index: number;
  users: OrgUser[];
  onSelect: (user: OrgUser) => void;
  onClose: () => void;
}

export function MentionAutocomplete({
  target,
  search,
  index,
  users,
  onSelect,
  onClose,
}: MentionAutocompleteProps) {
  const filteredUsers = useMemo(() => {
    return filterUsersForMention(users, search);
  }, [users, search]);

  const { refs, floatingStyles } = useFloating({
    placement: 'top-start',
    middleware: [offset(8), flip(), shift()],
  });

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === 'ArrowDown') {
        event.preventDefault();
        // Handle arrow down
      } else if (event.key === 'ArrowUp') {
        event.preventDefault();
        // Handle arrow up
      } else if (event.key === 'Enter') {
        event.preventDefault();
        if (filteredUsers[index]) {
          onSelect(filteredUsers[index]);
        }
      } else if (event.key === 'Escape') {
        event.preventDefault();
        onClose();
      }
    },
    [filteredUsers, index, onSelect, onClose]
  );

  if (!target || filteredUsers.length === 0) {
    return null;
  }

  return (
    <div
      ref={refs.setFloating}
      style={floatingStyles}
      className="z-50 w-64 bg-tx-surface border rounded-lg shadow-lg"
      onKeyDown={handleKeyDown}
    >
      <div className="p-2 border-b">
        <div className="text-sm font-medium text-muted-foreground">
          Mention someone
        </div>
      </div>
      
      <ScrollArea className="max-h-64">
        {filteredUsers.map((user, i) => {
          const initials = user.name
            .split(' ')
            .map(n => n[0])
            .join('')
            .toUpperCase()
            .slice(0, 2);

          return (
            <Button
              key={user._id}
              variant="ghost"
              className={cn(
                'w-full justify-start gap-3 h-auto p-3 rounded-none',
                i === index && 'bg-muted'
              )}
              onClick={() => onSelect(user)}
            >
              <Avatar className="w-6 h-6">
                <AvatarFallback className="text-xs bg-primary/10 text-primary">
                  {initials}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex flex-col items-start">
                <span className="text-sm font-medium">{user.name}</span>
                <span className="text-xs text-muted-foreground">{user.email}</span>
              </div>
            </Button>
          );
        })}
      </ScrollArea>

      {filteredUsers.length === 0 && search && (
        <div className="p-3 text-sm text-muted-foreground text-center">
          No users found for "{search}"
        </div>
      )}
    </div>
  );
} 