"""
Authentication dependencies for TractionX AI Agent system.
Reuses authentication patterns from the backend.
"""

from typing import Op<PERSON>, <PERSON><PERSON>
from fastapi import Depends, HTTPException, status, Request, Header
from motor.motor_asyncio import AsyncIOMotorDatabase
import logging

from ai.db import get_ai_database
from ai.models import PyObjectId
from ai.config import settings

logger = logging.getLogger(__name__)


# Import backend auth models and dependencies
try:
    import sys
    import os

    # Add backend to path
    backend_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "backend")
    if backend_path not in sys.path:
        sys.path.append(backend_path)

    from app.models.user import User
    from app.dependencies.auth import get_current_user as backend_get_current_user
    from app.dependencies.org import get_org_context as backend_get_org_context
    from app.core.org_context import OrgContext

except ImportError as e:
    logger.error(f"Failed to import backend auth dependencies: {e}")
    # Fallback implementations for development

    class User:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

    async def backend_get_current_user():
        # Dummy user for development
        return User(
            id=PyObjectId("000000000000000000000000"),
            email="<EMAIL>",
            name="Dev User",
            org_id=PyObjectId("000000000000000000000000"),
            org_ids=[PyObjectId("000000000000000000000000")],
            is_superuser=True
        )

    async def backend_get_org_context():
        return ("000000000000000000000000", False)


async def get_current_user(
    authorization: Optional[str] = Header(None),
    request: Request = None
) -> User:
    """Get current user using backend authentication."""

    logger.debug("Authenticating user")

    # In test mode, return dummy user
    if settings.TEST_MODE:
        logger.debug("Using test mode authentication with dummy user")
        test_user = User(
            id=PyObjectId("000000000000000000000000"),
            email="<EMAIL>",
            name="Test User",
            org_id=PyObjectId("000000000000000000000000"),
            org_ids=[PyObjectId("000000000000000000000000")],
            is_superuser=True,
            is_active=True
        )
        logger.debug(f"Authenticated test user: {test_user.email}")
        return test_user

    try:
        # Use backend authentication
        logger.debug("Attempting to authenticate with backend")

        # TODO: Remove this temporary code when backend auth is fully integrated
        logger.debug("Using temporary authentication (development only)")
        dev_user = User(
            id=PyObjectId("000000000000000000000000"),
            email="<EMAIL>",
            name="Test User",
            org_id=PyObjectId("000000000000000000000000"),
            org_ids=[PyObjectId("000000000000000000000000")],
            is_superuser=True,
            is_active=True
        )
        logger.debug(f"Authenticated development user: {dev_user.email}")
        return dev_user

        # Real implementation:
        # user = await backend_get_current_user(authorization, request)
        # logger.debug(f"Successfully authenticated user: {user.email}")
        # return user
    except Exception as e:
        logger.error(f"Authentication failed: {e}")
        logger.debug(f"Authentication error details: {type(e).__name__}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"}
        )


async def get_org_context(
    request: Request,
    current_user: User = Depends(get_current_user)
) -> Tuple[str, bool]:
    """Get organization context using backend patterns."""

    logger.debug(f"Getting organization context for user {current_user.email}")

    # In test mode, return dummy org context
    if settings.TEST_MODE:
        logger.debug("Using test mode org context")
        return ("000000000000000000000000", False)

    try:
        # Use backend org context
        logger.debug("Attempting to get org context from backend")

        # TODO: Remove this temporary code when backend integration is complete
        logger.debug("Using temporary org context (development only)")
        org_id = "000000000000000000000000"
        is_cross_org = False
        logger.debug(f"Using org_id={org_id}, is_cross_org={is_cross_org}")
        return (org_id, is_cross_org)

        # Real implementation:
        # org_id, is_cross_org = await backend_get_org_context(request, current_user)
        # logger.debug(f"Got org context from backend: org_id={org_id}, is_cross_org={is_cross_org}")
        # return (org_id, is_cross_org)
    except Exception as e:
        logger.error(f"Org context extraction failed: {e}")
        logger.debug(f"Org context error details: {type(e).__name__}")
        # Fallback to user's default org
        fallback_org_id = str(current_user.org_id)
        logger.debug(f"Falling back to user's default org: {fallback_org_id}")
        return (fallback_org_id, False)


async def validate_agent_access(
    agent_id: str,
    current_user: User,
    org_id: str,
    db: AsyncIOMotorDatabase = Depends(get_ai_database)
) -> bool:
    """Validate user access to an agent."""

    logger.debug(f"Validating access to agent {agent_id} for user {current_user.email} in org {org_id}")

    try:
        # Get agent
        logger.debug(f"Fetching agent {agent_id} from database")
        agent = await db.agents.find_one({"_id": PyObjectId(agent_id)})

        if not agent:
            logger.warning(f"Agent {agent_id} not found during access validation")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )

        # Check access permissions
        agent_org_id = str(agent.get("org_id"))
        agent_user_id = agent.get("user_id")
        agent_visibility = agent.get("visibility", "private")
        agent_type = agent.get("type")

        logger.debug(f"Agent details: type={agent_type}, visibility={agent_visibility}, org_id={agent_org_id}, user_id={agent_user_id}")

        # System/proprietary agents are accessible to all
        if agent_type in ["proprietary", "system"]:
            logger.debug(f"Access granted: {agent_type} agent is accessible to all users")
            return True

        # Org-level agents
        if agent_visibility == "org" and agent_org_id == org_id:
            logger.debug(f"Access granted: org-level agent in user's organization")
            return True

        # Private agents - only creator can access
        if agent_visibility == "private":
            if agent_user_id and str(agent_user_id) == str(current_user.id):
                logger.debug(f"Access granted: private agent owned by user")
                return True
            else:
                logger.debug(f"Access denied: private agent not owned by user")

        # Superusers can access everything
        if getattr(current_user, "is_superuser", False):
            logger.debug(f"Access granted: user is superuser")
            return True

        logger.warning(f"Access denied for user {current_user.email} to agent {agent_id}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have access to this agent"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Agent access validation failed: {e}")
        logger.debug(f"Access validation error details: {type(e).__name__}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate agent access"
        )


async def validate_deal_access(
    deal_id: str,
    current_user: User,
    org_id: str
) -> bool:
    """Validate user access to a deal."""

    logger.debug(f"Validating access to deal {deal_id} for user {current_user.email} in org {org_id}")

    # TODO: Implement deal access validation
    # This would check if the user has access to the specific deal
    # For now, we'll allow access if the user is in the same org

    try:
        # In test mode, allow all access
        if settings.TEST_MODE:
            logger.debug(f"Access granted in test mode for deal {deal_id}")
            return True

        # TODO: Query deal from main backend database
        # For now, assume access is granted if user is in the org
        logger.debug(f"Access granted for deal {deal_id} (temporary implementation)")
        return True

    except Exception as e:
        logger.error(f"Deal access validation failed: {e}")
        logger.debug(f"Deal access validation error details: {type(e).__name__}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have access to this deal"
        )


class AuthContext:
    """Authentication context for AI operations."""

    def __init__(self, user: User, org_id: str, is_cross_org: bool = False):
        self.user = user
        self.org_id = org_id
        self.is_cross_org = is_cross_org

    @property
    def user_id(self) -> str:
        return str(self.user.id)

    @property
    def is_superuser(self) -> bool:
        return getattr(self.user, "is_superuser", False)


async def get_auth_context(
    request: Request,
    current_user: User = Depends(get_current_user)
) -> AuthContext:
    """Get complete authentication context."""

    logger.debug(f"Building auth context for user {current_user.email}")

    org_id, is_cross_org = await get_org_context(request, current_user)
    logger.debug(f"Using org_id={org_id}, is_cross_org={is_cross_org} for auth context")

    auth_context = AuthContext(
        user=current_user,
        org_id=org_id,
        is_cross_org=is_cross_org
    )

    logger.debug(f"Auth context created: user_id={auth_context.user_id}, org_id={auth_context.org_id}, is_superuser={auth_context.is_superuser}")
    return auth_context
