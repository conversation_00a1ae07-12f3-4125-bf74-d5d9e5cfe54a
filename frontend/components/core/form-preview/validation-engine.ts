/**
 * Validation Engine
 * 
 * Provides real-time validation for form fields based on question configuration
 * Supports all validation rules from the backend ValidationRule model
 */

import { ValidationRule, Question } from '@/lib/types/form';

export interface ValidationError {
  field: string;
  message: string;
  type: 'required' | 'min' | 'max' | 'regex' | 'custom' | 'type';
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

/**
 * Validates a single field value against its question configuration
 */
export function validateField(
  question: Question,
  value: any,
  allAnswers?: { [questionId: string]: any }
): ValidationResult {
  const errors: ValidationError[] = [];
  const fieldId = question._id || question.id!;

  // Check if field is required
  if (question.required && (value === undefined || value === null || value === '')) {
    errors.push({
      field: fieldId,
      message: `${question.label} is required`,
      type: 'required'
    });
    // If required field is empty, don't run other validations
    return { isValid: false, errors };
  }

  // Skip validation if field is empty and not required
  if (value === undefined || value === null || value === '') {
    return { isValid: true, errors: [] };
  }

  // Type-specific validation
  const typeValidation = validateByType(question, value);
  if (!typeValidation.isValid) {
    errors.push(...typeValidation.errors);
  }

  // Custom validation rules
  if (question.validation) {
    const ruleValidation = validateByRules(question, value, allAnswers);
    if (!ruleValidation.isValid) {
      errors.push(...ruleValidation.errors);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validates value based on question type
 */
function validateByType(question: Question, value: any): ValidationResult {
  const errors: ValidationError[] = [];
  const fieldId = question._id || question.id!;

  switch (question.type) {
    case 'number':
    case 'range':
      if (isNaN(Number(value))) {
        errors.push({
          field: fieldId,
          message: `${question.label} must be a valid number`,
          type: 'type'
        });
      }
      break;

    case 'date':
      const dateValue = new Date(value);
      if (isNaN(dateValue.getTime())) {
        errors.push({
          field: fieldId,
          message: `${question.label} must be a valid date`,
          type: 'type'
        });
      }
      break;

    case 'single_select':
      if (question.options && question.options.length > 0) {
        const validValues = question.options.map(opt => opt.value);
        if (!validValues.includes(value)) {
          errors.push({
            field: fieldId,
            message: `${question.label} must be one of the available options`,
            type: 'type'
          });
        }
      }
      break;

    case 'multi_select':
      if (Array.isArray(value) && question.options && question.options.length > 0) {
        const validValues = question.options.map(opt => opt.value);
        const invalidValues = value.filter(v => !validValues.includes(v));
        if (invalidValues.length > 0) {
          errors.push({
            field: fieldId,
            message: `${question.label} contains invalid options: ${invalidValues.join(', ')}`,
            type: 'type'
          });
        }
      } else if (!Array.isArray(value)) {
        errors.push({
          field: fieldId,
          message: `${question.label} must be an array of values`,
          type: 'type'
        });
      }
      break;

    case 'boolean':
      if (typeof value !== 'boolean' && value !== 'true' && value !== 'false') {
        errors.push({
          field: fieldId,
          message: `${question.label} must be true or false`,
          type: 'type'
        });
      }
      break;

    case 'file':
      // File validation would typically be handled by the file input component
      // Here we just check if it's a valid file object or file path
      if (typeof value !== 'string' && !(value instanceof File)) {
        errors.push({
          field: fieldId,
          message: `${question.label} must be a valid file`,
          type: 'type'
        });
      }
      break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validates value based on validation rules
 */
function validateByRules(
  question: Question,
  value: any,
  allAnswers?: { [questionId: string]: any }
): ValidationResult {
  const errors: ValidationError[] = [];
  const fieldId = question._id || question.id!;
  const validation = question.validation!;

  // Min validation
  if (validation.min !== undefined) {
    if (question.type === 'number' || question.type === 'range') {
      if (Number(value) < validation.min) {
        errors.push({
          field: fieldId,
          message: `${question.label} must be at least ${validation.min}`,
          type: 'min'
        });
      }
    } else if (typeof value === 'string') {
      if (value.length < validation.min) {
        errors.push({
          field: fieldId,
          message: `${question.label} must be at least ${validation.min} characters`,
          type: 'min'
        });
      }
    } else if (Array.isArray(value)) {
      if (value.length < validation.min) {
        errors.push({
          field: fieldId,
          message: `${question.label} must have at least ${validation.min} selections`,
          type: 'min'
        });
      }
    }
  }

  // Max validation
  if (validation.max !== undefined) {
    if (question.type === 'number' || question.type === 'range') {
      if (Number(value) > validation.max) {
        errors.push({
          field: fieldId,
          message: `${question.label} must be at most ${validation.max}`,
          type: 'max'
        });
      }
    } else if (typeof value === 'string') {
      if (value.length > validation.max) {
        errors.push({
          field: fieldId,
          message: `${question.label} must be at most ${validation.max} characters`,
          type: 'max'
        });
      }
    } else if (Array.isArray(value)) {
      if (value.length > validation.max) {
        errors.push({
          field: fieldId,
          message: `${question.label} must have at most ${validation.max} selections`,
          type: 'max'
        });
      }
    }
  }

  // Regex validation
  if (validation.regex && typeof value === 'string') {
    try {
      const regex = new RegExp(validation.regex);
      if (!regex.test(value)) {
        errors.push({
          field: fieldId,
          message: `${question.label} format is invalid`,
          type: 'regex'
        });
      }
    } catch (error) {
      console.error('Invalid regex pattern:', validation.regex);
    }
  }

  // Required if validation
  if (validation.required_if && allAnswers) {
    const { question_id, value: requiredValue } = validation.required_if;
    const dependentValue = allAnswers[question_id];
    
    if (dependentValue === requiredValue) {
      if (value === undefined || value === null || value === '') {
        errors.push({
          field: fieldId,
          message: `${question.label} is required when the specified condition is met`,
          type: 'required'
        });
      }
    }
  }

  // Custom validation (placeholder for future implementation)
  if (validation.custom) {
    // This could be extended to support custom validation functions
    console.log('Custom validation not yet implemented:', validation.custom);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validates all visible fields in a form
 */
export function validateForm(
  questions: Question[],
  answers: { [questionId: string]: any },
  visibleQuestionIds: string[]
): ValidationResult {
  const allErrors: ValidationError[] = [];

  for (const question of questions) {
    const questionId = question._id || question.id!;
    
    // Only validate visible questions
    if (!visibleQuestionIds.includes(questionId)) {
      continue;
    }

    const value = answers[questionId];
    const result = validateField(question, value, answers);
    
    if (!result.isValid) {
      allErrors.push(...result.errors);
    }
  }

  return {
    isValid: allErrors.length === 0,
    errors: allErrors
  };
}

/**
 * Gets validation message for a specific field
 */
export function getFieldError(
  fieldId: string,
  validationResult: ValidationResult
): string | null {
  const error = validationResult.errors.find(err => err.field === fieldId);
  return error ? error.message : null;
}

/**
 * Checks if a specific field has errors
 */
export function hasFieldError(
  fieldId: string,
  validationResult: ValidationResult
): boolean {
  return validationResult.errors.some(err => err.field === fieldId);
}
