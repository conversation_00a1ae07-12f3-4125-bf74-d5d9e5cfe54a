"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  ExternalLink, 
  MapPin, 
  Calendar, 
  Users, 
  Building2,
  ChevronDown,
  ChevronUp,
  Globe,
  Linkedin
} from "lucide-react"
import { cn } from "@/lib/utils"

interface CompanyOverviewCardProps {
  company: {
    name: string
    domain: string
    description: string
    employeeCount: number
    industry: string
    city: string
    country: string
    foundedYear: number
    website: string
    linkedinUrl: string | null
    enrichmentDate: string
  }
}

export function CompanyOverviewCard({ company }: CompanyOverviewCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const formatDescription = (description: string) => {
    if (!isExpanded && description.length > 200) {
      return description.substring(0, 200) + "..."
    }
    return description
  }

  const formatEnrichmentDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <div className="animate-in fade-in duration-500">
      <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-md">
        <CardHeader className="pb-4">
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <CardTitle className="text-2xl font-bold text-gray-900">
                  {company.name}
                </CardTitle>
                <Badge variant="secondary" className="text-xs">
                  {company.domain}
                </Badge>
              </div>
              
              <div className="flex flex-wrap items-center gap-2 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Building2 className="h-4 w-4" />
                  <span>{company.industry}</span>
                </div>
                
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  <span>{company.city}, {company.country}</span>
                </div>
                
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>Founded {company.foundedYear}</span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {company.website && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => window.open(company.website, '_blank')}
                  className="h-8 w-8 p-0"
                >
                  <Globe className="h-4 w-4" />
                </Button>
              )}
              
              {company.linkedinUrl && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => window.open(company.linkedinUrl!, '_blank')}
                  className="h-8 w-8 p-0"
                >
                  <Linkedin className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Employee Count Stat */}
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2 rounded-lg bg-blue-50 px-3 py-2">
              <Users className="h-5 w-5 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">
                {company.employeeCount} employees
              </span>
            </div>
            
            <div className="text-xs text-gray-500">
              Enriched {formatEnrichmentDate(company.enrichmentDate)}
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <p className="text-sm leading-6 text-gray-700 whitespace-pre-wrap">
              {formatDescription(company.description)}
            </p>
            
            {company.description.length > 200 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="h-8 px-2 text-xs text-blue-600 hover:text-blue-700"
              >
                {isExpanded ? (
                  <>
                    Show less <ChevronUp className="ml-1 h-3 w-3" />
                  </>
                ) : (
                  <>
                    Show more <ChevronDown className="ml-1 h-3 w-3" />
                  </>
                )}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 