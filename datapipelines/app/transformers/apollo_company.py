"""
Apollo Company Data Transformer for TractionX Data Pipeline Service.

This module transforms Apollo API company enrichment payloads into normalized
structured data for storage in relational database tables.
"""

import time
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from app.configs import get_logger
from app.models.company import (
    ApolloCompanyData,
)

logger = get_logger(__name__)


def transform_apollo_company_payload(
    payload: Dict[str, Any], org_id: str
) -> Dict[str, Any]:
    """
    Transform Apollo company enrichment payload into normalized data structure.

    Args:
        payload: Raw Apollo API response payload (may contain "organization" wrapper)
        org_id: Organization identifier

    Returns:
        Dictionary containing:
        - company: Flat company data dict
        - keywords: List of keyword strings
        - technologies: List of technology dicts with category
        - departments: List of department count dicts
    """
    try:
        logger.info("Transforming Apollo company payload", org_id=org_id)

        # Apollo API returns data wrapped in "organization" key
        if "organization" in payload:
            apollo_org_data = payload["organization"]
            logger.info("Found organization data in Apollo response")
        else:
            # Fallback: assume payload is the organization data directly
            apollo_org_data = payload
            logger.info("Using payload directly as organization data")

        # Extract company data from Apollo response
        company_data = _extract_company_data(apollo_org_data, org_id)

        # Extract keywords
        keywords = _extract_keywords(apollo_org_data)
        logger.info(
            f"Extracted {len(keywords)} keywords",
            keywords_sample=keywords[:3] if keywords else [],
        )

        # Extract technologies with categories
        technologies = _extract_technologies(apollo_org_data)
        logger.info(
            f"Extracted {len(technologies)} technologies",
            technologies_sample=[t.get("technology") for t in technologies[:3]],
        )

        # Extract department counts
        departments = _extract_department_counts(apollo_org_data)
        logger.info(
            f"Extracted {len(departments)} departments",
            departments_sample=[
                f"{d.get('department')}: {d.get('head_count')}" for d in departments[:3]
            ],
        )

        result = {
            "company": company_data,
            "keywords": keywords,
            "technologies": technologies,
            "departments": departments,
        }

        logger.info(
            "Apollo transformation completed",
            org_id=org_id,
            keywords_count=len(keywords),
            technologies_count=len(technologies),
            departments_count=len(departments),
        )

        return result

    except Exception as e:
        logger.error(f"Apollo transformation failed: {e}", exc_info=True)
        raise


def _extract_company_data(payload: Dict[str, Any], org_id: str) -> Dict[str, Any]:
    """Extract and clean core company data from Apollo payload."""

    # Create Apollo data model first - using actual Apollo API field names
    apollo_data = ApolloCompanyData(
        apollo_id=_clean_string(payload.get("id")),
        name=_clean_string(payload.get("name")),
        domain=_clean_string(
            payload.get("primary_domain")
        ),  # Apollo uses primary_domain
        website=_clean_url(payload.get("website_url")),
        description=_clean_string(
            payload.get("short_description")
        ),  # Apollo uses short_description
        industry=_clean_string(payload.get("industry")),
        sub_industry=None,  # Apollo doesn't have sub_industry, using industries array instead
        employee_count=_clean_integer(payload.get("estimated_num_employees")),
        employee_count_range=None,  # Apollo doesn't provide this
        founded_year=_clean_integer(payload.get("founded_year")),
        headquarters=_clean_string(
            payload.get("raw_address")
        ),  # Apollo uses raw_address
        country=_clean_string(payload.get("country")),
        city=_clean_string(payload.get("city")),
        state=_clean_string(payload.get("state")),
        funding_total=_clean_float(payload.get("total_funding")),
        funding_rounds=None,  # Apollo doesn't provide this directly
        last_funding_date=_parse_date(payload.get("latest_funding_round_date")),
        last_funding_amount=None,  # Apollo doesn't provide this directly
        valuation=None,  # Apollo doesn't provide this
        revenue=None,  # Apollo doesn't provide annual_revenue
        # Note: technologies and tech_stack removed - stored in separate tables
        linkedin_url=_clean_url(payload.get("linkedin_url")),
        twitter_url=_clean_url(payload.get("twitter_url")),
        facebook_url=_clean_url(payload.get("facebook_url")),
        email=_clean_string(payload.get("email")),
        phone=_clean_string(payload.get("phone")),
        apollo_metadata={
            "api_response_timestamp": time.time(),
            "raw_payload_keys": list(payload.keys()),
            "linkedin_uid": payload.get("linkedin_uid"),
            "logo_url": payload.get("logo_url"),
            "alexa_ranking": payload.get("alexa_ranking"),
            "publicly_traded_symbol": payload.get("publicly_traded_symbol"),
            "crunchbase_url": payload.get("crunchbase_url"),
            "latest_funding_stage": payload.get("latest_funding_stage"),
        },
    )

    # Convert to flat dict for database storage, excluding fields that go to separate tables
    company_dict = apollo_data.model_dump()

    # Remove ALL fields that don't exist in companies_enrichment table schema
    # These are stored in separate normalized tables
    fields_to_remove = [
        "keywords",  # → company_keywords table
        "departmental_head_count",  # → company_department_counts table
        "technologies",  # → company_technologies table
        "tech_stack",  # → company_technologies table (as category)
    ]

    for field in fields_to_remove:
        company_dict.pop(field, None)

    return company_dict


def _extract_keywords(payload: Dict[str, Any]) -> List[str]:
    """Extract and clean keywords from Apollo payload."""
    keywords = []

    # Debug logging
    logger.info(f"Extracting keywords from payload with keys: {list(payload.keys())}")

    # Apollo returns keywords as a direct array
    apollo_keywords = payload.get("keywords", [])
    logger.info(f"Found {len(apollo_keywords)} raw keywords in Apollo response")

    if isinstance(apollo_keywords, list):
        for keyword in apollo_keywords:
            cleaned = _clean_string(keyword)
            if cleaned and cleaned not in keywords:
                keywords.append(cleaned)

    # Also extract from industries if available
    industries = payload.get("industries", [])
    logger.info(f"Found {len(industries)} industries in Apollo response")

    if isinstance(industries, list):
        for industry in industries:
            cleaned = _clean_string(industry)
            if cleaned and cleaned not in keywords:
                keywords.append(cleaned)

    logger.info(f"Final keyword extraction result: {len(keywords)} keywords")
    return sorted(keywords)


def _extract_technologies(payload: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract technologies with categories from Apollo payload."""
    technologies = []

    # Debug logging
    logger.info(
        f"Extracting technologies from payload with keys: {list(payload.keys())}"
    )

    # Apollo returns current_technologies as array of objects with uid, name, category
    current_technologies = payload.get("current_technologies", [])
    logger.info(
        f"Found {len(current_technologies)} current_technologies in Apollo response"
    )

    if isinstance(current_technologies, list):
        for tech in current_technologies:
            if isinstance(tech, dict):
                tech_name = _clean_string(tech.get("name"))
                category = _clean_string(tech.get("category"))

                if tech_name:
                    technologies.append({
                        "technology": tech_name,
                        "category": category,
                    })

    # Also extract from technology_names array (simple names without categories)
    tech_names = payload.get("technology_names", [])
    logger.info(f"Found {len(tech_names)} technology_names in Apollo response")

    if isinstance(tech_names, list):
        for tech_name in tech_names:
            cleaned_name = _clean_string(tech_name)
            if cleaned_name:
                # Check if we already have this technology from current_technologies
                existing = next(
                    (t for t in technologies if t["technology"] == cleaned_name), None
                )
                if not existing:
                    technologies.append({
                        "technology": cleaned_name,
                        "category": None,  # No category info in technology_names
                    })

    logger.info(f"Final technology extraction result: {len(technologies)} technologies")
    return technologies


def _extract_department_counts(payload: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract department head counts from Apollo payload."""
    departments = []

    # Debug logging
    logger.info(
        f"Extracting departments from payload with keys: {list(payload.keys())}"
    )

    dept_counts = payload.get("departmental_head_count", {})
    logger.info(f"Found departmental_head_count with {len(dept_counts)} departments")

    if isinstance(dept_counts, dict):
        for dept_name, count in dept_counts.items():
            cleaned_dept = _clean_string(dept_name)
            cleaned_count = _clean_integer(count)

            # Only include departments with head_count > 0 (DB constraint)
            if cleaned_dept and cleaned_count is not None and cleaned_count > 0:
                # Convert snake_case department names to readable format
                readable_dept = cleaned_dept.replace("_", " ").title()
                departments.append({
                    "department": readable_dept,
                    "head_count": cleaned_count,
                })

    logger.info(
        f"Final department extraction result: {len(departments)} departments (head_count > 0)"
    )
    return departments


def _clean_string(value: Any) -> Optional[str]:
    """Clean and normalize string values."""
    if not value or not isinstance(value, str):
        return None

    cleaned = value.strip()
    if not cleaned:
        return None

    # Remove excessive whitespace
    cleaned = " ".join(cleaned.split())

    return cleaned


def _clean_url(value: Any) -> Optional[str]:
    """Clean and normalize URL values."""
    if not value or not isinstance(value, str):
        return None

    url = value.strip()
    if not url:
        return None

    # Add protocol if missing
    if not url.startswith(("http://", "https://")):
        url = f"https://{url}"

    return url


def _clean_integer(value: Any) -> Optional[int]:
    """Clean and convert to integer."""
    if value is None:
        return None

    if isinstance(value, int):
        return value

    if isinstance(value, str):
        try:
            return int(value.replace(",", ""))
        except ValueError:
            return None

    if isinstance(value, float):
        return int(value)

    return None


def _clean_float(value: Any) -> Optional[float]:
    """Clean and convert to float."""
    if value is None:
        return None

    if isinstance(value, (int, float)):
        return float(value)

    if isinstance(value, str):
        try:
            return float(value.replace(",", ""))
        except ValueError:
            return None

    return None


def _parse_date(date_str: Any) -> Optional[datetime]:
    """Parse date string to datetime object."""
    if not date_str:
        return None

    if isinstance(date_str, str):
        # Try different date formats
        for fmt in ["%Y-%m-%d", "%Y-%m-%dT%H:%M:%S", "%Y-%m-%dT%H:%M:%SZ"]:
            try:
                return datetime.strptime(date_str, fmt).replace(tzinfo=timezone.utc)
            except ValueError:
                continue

    return None
