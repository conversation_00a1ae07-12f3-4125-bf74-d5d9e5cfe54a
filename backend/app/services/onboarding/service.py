import re
from typing import Any, Dict, List, Optional

from app.core.logging import get_logger
from app.core.security import get_password_hash
from app.models.organization import Organization, OrganizationSettings
from app.models.role import Role
from app.models.user import InviteCode, InviteCodeStatus, User, UserStatus
from app.services.auth.interface import IAuthService
from app.services.email.interface import IEmailService
from app.services.factory import get_form_service, get_role_service, get_thesis_service
from app.services.form.mongo import FormService
from app.services.onboarding.interface import IOnboardingService
from app.utils.common import PyObjectId
from fastapi import HTTPException, status

logger = get_logger(__name__)


class OnboardingService(IOnboardingService):
    """Service for handling organization onboarding workflows."""

    def __init__(self, auth_service: IAuthService, email_service: IEmailService):
        self.auth_service = auth_service
        self.email_service = email_service

    async def create_invite_code(
        self, email: str, org_name: Optional[str] = None
    ) -> InviteCode:
        """Create a new invite code for organization onboarding."""
        try:
            # Check if email already has a pending invite code
            existing_code = await InviteCode.find_one({
                "email": email,
                "status": InviteCodeStatus.PENDING,
            })

            if existing_code and not existing_code.is_expired():
                logger.info(f"Returning existing invite code for {email}")
                return existing_code

            # Generate unique code
            max_attempts = 10
            for _ in range(max_attempts):
                code = InviteCode.generate_code()
                existing = await InviteCode.find_one({"code": code})
                if not existing:
                    break
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to generate unique invite code",
                )

            # Create new invite code
            invite_code = InviteCode(code=code, email=email, org_name=org_name)
            await invite_code.save()

            logger.info(f"Created invite code {code} for {email}")
            return invite_code

        except Exception as e:
            logger.error(f"Error creating invite code: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create invite code",
            )

    async def validate_invite_code(self, code: str) -> InviteCode:
        """Validate and retrieve an invite code."""
        invite_code = await InviteCode.find_one({"code": code})

        if not invite_code:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Invite code not found"
            )

        if not invite_code.is_redeemable():
            if invite_code.is_expired():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invite code has expired",
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invite code has already been used",
                )

        return invite_code

    async def check_subdomain_availability(self, subdomain: str) -> bool:
        """Check if a subdomain is available for use."""
        # Validate subdomain format
        if not re.match(r"^[a-z0-9-]+$", subdomain):
            return False

        if len(subdomain) < 3 or len(subdomain) > 50:
            return False

        # Check if subdomain already exists
        existing_org = await Organization.find_one({"subdomain": subdomain})
        return existing_org is None

    async def create_organization(
        self,
        invite_code: InviteCode,
        org_name: str,
        subdomain: str,
        website: Optional[str] = None,
        logo_url: Optional[str] = None,
    ) -> Organization:
        """Create a new organization during onboarding."""
        try:
            # Validate subdomain availability
            if not await self.check_subdomain_availability(subdomain):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Subdomain is not available",
                )

            # Create organization
            organization = Organization(
                name=org_name,
                subdomain=subdomain,
                website=website,
                logo_url=logo_url,
                contact_email=invite_code.email,
                created_by=PyObjectId(),  # Will be updated after user creation
                settings=OrganizationSettings(
                    plan="advanced",
                    subdomain_enabled=True,
                    custom_domain=subdomain,
                    features={"onboarding_completed": True},
                ),
            )
            await organization.save()

            # create a default form for the organization
            # and add basic questions to give them an example of how to use the form builder
            form_service: FormService = await get_form_service()  # type: ignore
            default_form = await form_service.create_default_form(
                org_id=organization.id  # type: ignore
            )
            if not default_form:
                logger.warning(
                    f"Failed to create default form for organization {org_name}"
                )

            # thesis also
            thesis_service = await get_thesis_service()  # type: ignore
            default_thesis = await thesis_service.create_demo_thesis(  # type: ignore
                org_id=str(organization.id),
                form_id=str(default_form["_id"]),  # type: ignore
            )
            if not default_thesis:
                logger.warning(
                    f"Failed to create default thesis for organization {org_name}"
                )

            # Create demo deals to demonstrate platform value
            # Note: We'll create these after the user is created so we have a proper created_by
            # For now, we'll create them with a system user ID
            if default_form:  # Only create demo deals if form was created successfully
                try:
                    from app.services.deal.demo import DemoDealService
                    from app.services.factory import get_demo_deal_service

                    demo_deal_service: DemoDealService = await get_demo_deal_service()  # type: ignore
                    demo_deals = await demo_deal_service.create_multiple_demo_deals(
                        org_id=str(organization.id),
                        form_id=str(default_form["_id"]),
                        created_by=PyObjectId(),  # System user for now
                        count=3,  # Create 3 demo deals to show variety
                    )
                    logger.info(
                        f"Created {len(demo_deals)} demo deals for organization {org_name}"
                    )
                except Exception as e:
                    logger.warning(
                        f"Failed to create demo deals for organization {org_name}: {str(e)}"
                    )

            logger.info(f"Created organization {org_name} with subdomain {subdomain}")
            return organization

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating organization: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create organization",
            )

    async def create_user_profile(
        self,
        invite_code: InviteCode,
        organization: Organization,
        name: str,
        password: str,
        profile_picture_url: Optional[str] = None,
    ) -> User:
        """Create user profile and complete onboarding."""
        try:
            # Check if user already exists
            existing_user = await User.find_one({"email": invite_code.email})
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User with this email already exists",
                )

            # Get default role (admin for first user)
            admin_role = await Role.find_one({"name": "General Partner"})
            if not admin_role:
                role_service = await get_role_service()
                admin_role, _ = await role_service.create_default_roles(  # type: ignore
                    str(organization.id)
                )

            # Create user
            user = User(
                name=name,
                email=invite_code.email,
                password_hash=get_password_hash(password),
                org_id=organization.id,
                org_ids=[organization.id],
                role_id=admin_role.id,
                status=UserStatus.ACTIVE,
                is_active=True,
            )
            await user.save()

            # Update organization with creator info
            organization.created_by = user.id
            organization.admins = [user.id]
            organization.members = [user.id]
            organization.user_ids = [user.id]
            await organization.save(is_update=True)

            # Mark invite code as redeemed
            invite_code.redeem(user.id)
            await invite_code.save(is_update=True)

            logger.info(f"Created user profile for {invite_code.email}")
            return user

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating user profile: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create user profile: {str(e)}",
            )

    async def complete_onboarding(
        self, invite_code: InviteCode, user: User, organization: Organization
    ) -> Dict[str, Any]:
        """Complete the onboarding process and return auth tokens."""
        try:
            # Generate auth tokens
            token_response = await self.auth_service.create_tokens(user)

            return {
                "user_id": str(user.id),
                "org_id": str(organization.id),
                "access_token": token_response.access_token,
                "refresh_token": token_response.refresh_token,
                "redirect_url": "/login",
            }

        except Exception as e:
            logger.error(f"Error completing onboarding: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to complete onboarding",
            )

    async def send_peer_invites(
        self,
        org_id: str,
        invited_by: str,
        emails: List[str],
        role_id: Optional[str] = None,
        message: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Send invitations to peers to join the organization."""
        try:
            results = []

            for email in emails:
                try:
                    # Use existing invite functionality
                    result = await self.auth_service.invite_user(
                        name="",  # Will be filled during acceptance # type: ignore
                        email=email,
                        role_id=role_id or "",
                        org_id=org_id,
                        invited_by=invited_by,
                    )
                    results.append({"email": email, "status": "sent", "result": result})
                except Exception as e:
                    logger.error(f"Failed to invite {email}: {str(e)}")
                    results.append({
                        "email": email,
                        "status": "failed",
                        "error": str(e),
                    })

            return {
                "total_sent": len([r for r in results if r["status"] == "sent"]),
                "total_failed": len([r for r in results if r["status"] == "failed"]),
                "results": results,
            }

        except Exception as e:
            logger.error(f"Error sending peer invites: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send peer invites",
            )
