"""
Agent CRUD API endpoints for TractionX AI Agent system.
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from motor.motor_asyncio import AsyncIOMotorDatabase
from pydantic import BaseModel, Field
import logging
from datetime import datetime

from ai.db import get_ai_database
from ai.models import Agent, AgentType, AgentVisibility, AgentStatus, AgentContext, PyObjectId
from ai.api.auth import get_auth_context, AuthContext, validate_agent_access
from ai.registry import agent_registry
from ai.runner import agent_runner
from ai.context import context_builder
from ai.audit import audit_logger

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/agents", tags=["agents"])


# Request/Response models
class CreateAgentRequest(BaseModel):
    """Request model for creating an agent."""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    visibility: AgentVisibility = Field(default=AgentVisibility.PRIVATE)
    config: Dict[str, Any] = Field(default_factory=dict)
    tools: List[str] = Field(default_factory=list)
    model: str = Field(default="gpt-4")
    temperature: float = Field(default=0.3, ge=0.0, le=2.0)
    max_tokens: int = Field(default=4000, gt=0)
    max_iterations: int = Field(default=10, gt=0)
    system_prompt: Optional[str] = None
    instructions: Optional[str] = None
    tags: List[str] = Field(default_factory=list)
    category: Optional[str] = None


class UpdateAgentRequest(BaseModel):
    """Request model for updating an agent."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    visibility: Optional[AgentVisibility] = None
    status: Optional[AgentStatus] = None
    config: Optional[Dict[str, Any]] = None
    tools: Optional[List[str]] = None
    model: Optional[str] = None
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0)
    max_tokens: Optional[int] = Field(None, gt=0)
    max_iterations: Optional[int] = Field(None, gt=0)
    system_prompt: Optional[str] = None
    instructions: Optional[str] = None
    tags: Optional[List[str]] = None
    category: Optional[str] = None


class RunAgentRequest(BaseModel):
    """Request model for running an agent."""
    message: str = Field(..., min_length=1)
    session_id: Optional[str] = None
    deal_id: Optional[str] = None
    context_data: Optional[Dict[str, Any]] = Field(default_factory=dict)


class AgentResponse(BaseModel):
    """Response model for agent data."""
    id: str
    name: str
    description: Optional[str]
    type: AgentType
    visibility: AgentVisibility
    status: AgentStatus
    config: Dict[str, Any]
    tools: List[str]
    model: str
    temperature: float
    max_tokens: int
    max_iterations: int
    system_prompt: Optional[str]
    instructions: Optional[str]
    created_at: datetime
    updated_at: datetime
    last_used: Optional[datetime]
    usage_count: int
    tags: List[str]
    category: Optional[str]
    org_id: str
    user_id: Optional[str]


@router.post("/", status_code=status.HTTP_201_CREATED, response_model=AgentResponse)
async def create_agent(
    request: CreateAgentRequest,
    auth_context: AuthContext = Depends(get_auth_context),
    db: AsyncIOMotorDatabase = Depends(get_ai_database)) -> AgentResponse:
    """Create a new custom agent."""

    try:
        # Create agent
        agent = Agent(
            org_id=PyObjectId(auth_context.org_id),
            user_id=PyObjectId(auth_context.user_id),
            name=request.name,
            description=request.description,
            type=AgentType.CUSTOM,
            visibility=request.visibility,
            status=AgentStatus.ACTIVE,
            config=request.config,
            tools=request.tools,
            model=request.model,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            max_iterations=request.max_iterations,
            system_prompt=request.system_prompt,
            instructions=request.instructions,
            tags=request.tags,
            category=request.category
        )

        # Insert into database
        result = await db.agents.insert_one(
            agent.dict(by_alias=True, exclude_none=True)
        )

        agent.id = result.inserted_id

        logger.info(f"Created agent: {agent.name} ({agent.id}) for user {auth_context.user_id}")

        return AgentResponse(
            id=str(agent.id),
            name=agent.name,
            description=agent.description,
            type=agent.type,
            visibility=agent.visibility,
            status=agent.status,
            config=agent.config,
            tools=agent.tools,
            model=agent.model,
            temperature=agent.temperature,
            max_tokens=agent.max_tokens,
            max_iterations=agent.max_iterations,
            system_prompt=agent.system_prompt,
            instructions=agent.instructions,
            created_at=agent.created_at,
            updated_at=agent.updated_at,
            last_used=agent.last_used,
            usage_count=agent.usage_count,
            tags=agent.tags,
            category=agent.category,
            org_id=str(agent.org_id),
            user_id=str(agent.user_id) if agent.user_id else None
        )

    except Exception as e:
        logger.error(f"Failed to create agent: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create agent"
        )


@router.get("/", response_model=List[AgentResponse])
async def list_agents(
    agent_type: Optional[AgentType] = Query(None, description="Filter by agent type"),
    visibility: Optional[AgentVisibility] = Query(None, description="Filter by visibility"),
    category: Optional[str] = Query(None, description="Filter by category"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of agents to return"),
    skip: int = Query(0, ge=0, description="Number of agents to skip"),
    auth_context: AuthContext = Depends(get_auth_context),
    db: AsyncIOMotorDatabase = Depends(get_ai_database)
) -> List[AgentResponse]:
    """List agents accessible to the user."""

    logger.debug(f"Listing agents for user {auth_context.user_id}, org {auth_context.org_id}")
    logger.debug(f"Filters: type={agent_type}, visibility={visibility}, category={category}, limit={limit}, skip={skip}")

    try:
        # Build filter query
        filter_query = {
            "$or": [
                # User's own agents
                {"org_id": PyObjectId(auth_context.org_id), "user_id": PyObjectId(auth_context.user_id)},
                # Org-level agents
                {"org_id": PyObjectId(auth_context.org_id), "visibility": AgentVisibility.ORG},
                # Public/proprietary agents
                {"visibility": AgentVisibility.PUBLIC, "type": {"$in": [AgentType.PROPRIETARY, AgentType.SYSTEM]}}
            ]
        }

        # Add filters
        if agent_type:
            filter_query["type"] = agent_type
        if visibility:
            filter_query["visibility"] = visibility
        if category:
            filter_query["category"] = category

        logger.debug(f"Agent filter query: {filter_query}")

        # Query agents
        cursor = db.agents.find(filter_query).sort("created_at", -1).skip(skip).limit(limit)

        agents = []
        async for agent_doc in cursor:
            agent_response = AgentResponse(
                id=str(agent_doc["_id"]),
                name=agent_doc["name"],
                description=agent_doc.get("description"),
                type=agent_doc["type"],
                visibility=agent_doc["visibility"],
                status=agent_doc["status"],
                config=agent_doc.get("config", {}),
                tools=agent_doc.get("tools", []),
                model=agent_doc.get("model", "gpt-4"),
                temperature=agent_doc.get("temperature", 0.7),
                max_tokens=agent_doc.get("max_tokens", 4000),
                max_iterations=agent_doc.get("max_iterations", 10),
                system_prompt=agent_doc.get("system_prompt"),
                instructions=agent_doc.get("instructions"),
                created_at=agent_doc["created_at"],
                updated_at=agent_doc["updated_at"],
                last_used=agent_doc.get("last_used"),
                usage_count=agent_doc.get("usage_count", 0),
                tags=agent_doc.get("tags", []),
                category=agent_doc.get("category"),
                org_id=str(agent_doc["org_id"]),
                user_id=str(agent_doc["user_id"]) if agent_doc.get("user_id") else None
            )
            agents.append(agent_response)

        logger.debug(f"Found {len(agents)} agents matching criteria")
        logger.info(f"Listed {len(agents)} agents for user {auth_context.user_id}")
        return agents

    except Exception as e:
        logger.error(f"Failed to list agents: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list agents"
        )


@router.get("/{agent_id}", response_model=AgentResponse)
async def get_agent(
    agent_id: str,
    auth_context: AuthContext = Depends(get_auth_context),
    db: AsyncIOMotorDatabase = Depends(get_ai_database)
) -> AgentResponse:
    """Get agent details."""

    logger.debug(f"Getting agent details for agent_id={agent_id}, user={auth_context.user_id}")

    try:
        # Validate access
        logger.debug(f"Validating access for agent {agent_id}")
        await validate_agent_access(agent_id, auth_context.user, auth_context.org_id, db)

        # Get agent
        logger.debug(f"Fetching agent {agent_id} from database")
        agent_doc = await db.agents.find_one({"_id": PyObjectId(agent_id)})

        if not agent_doc:
            logger.warning(f"Agent {agent_id} not found")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )

        logger.debug(f"Found agent: {agent_doc['name']} (type={agent_doc['type']}, visibility={agent_doc['visibility']})")
        logger.info(f"Retrieved agent {agent_id} for user {auth_context.user_id}")

        return AgentResponse(
            id=str(agent_doc["_id"]),
            name=agent_doc["name"],
            description=agent_doc.get("description"),
            type=agent_doc["type"],
            visibility=agent_doc["visibility"],
            status=agent_doc["status"],
            config=agent_doc.get("config", {}),
            tools=agent_doc.get("tools", []),
            model=agent_doc.get("model", "gpt-4"),
            temperature=agent_doc.get("temperature", 0.7),
            max_tokens=agent_doc.get("max_tokens", 4000),
            max_iterations=agent_doc.get("max_iterations", 10),
            system_prompt=agent_doc.get("system_prompt"),
            instructions=agent_doc.get("instructions"),
            created_at=agent_doc["created_at"],
            updated_at=agent_doc["updated_at"],
            last_used=agent_doc.get("last_used"),
            usage_count=agent_doc.get("usage_count", 0),
            tags=agent_doc.get("tags", []),
            category=agent_doc.get("category"),
            org_id=str(agent_doc["org_id"]),
            user_id=str(agent_doc["user_id"]) if agent_doc.get("user_id") else None
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get agent {agent_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent"
        )


@router.put("/{agent_id}", response_model=AgentResponse)
async def update_agent(
    agent_id: str,
    request: UpdateAgentRequest,
    auth_context: AuthContext = Depends(get_auth_context),
    db: AsyncIOMotorDatabase = Depends(get_ai_database)
) -> AgentResponse:
    """Update an agent."""

    try:
        # Validate access
        await validate_agent_access(agent_id, auth_context.user, auth_context.org_id, db)

        # Build update data
        update_data = {}
        if request.name is not None:
            update_data["name"] = request.name
        if request.description is not None:
            update_data["description"] = request.description
        if request.visibility is not None:
            update_data["visibility"] = request.visibility
        if request.status is not None:
            update_data["status"] = request.status
        if request.config is not None:
            update_data["config"] = request.config
        if request.tools is not None:
            update_data["tools"] = request.tools
        if request.model is not None:
            update_data["model"] = request.model
        if request.temperature is not None:
            update_data["temperature"] = request.temperature
        if request.max_tokens is not None:
            update_data["max_tokens"] = request.max_tokens
        if request.max_iterations is not None:
            update_data["max_iterations"] = request.max_iterations
        if request.system_prompt is not None:
            update_data["system_prompt"] = request.system_prompt
        if request.instructions is not None:
            update_data["instructions"] = request.instructions
        if request.tags is not None:
            update_data["tags"] = request.tags
        if request.category is not None:
            update_data["category"] = request.category

        update_data["updated_at"] = datetime.utcnow()

        # Update agent
        result = await db.agents.update_one(
            {"_id": PyObjectId(agent_id)},
            {"$set": update_data}
        )

        if result.matched_count == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )

        # Get updated agent
        return await get_agent(agent_id, auth_context, db)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update agent {agent_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update agent"
        )


@router.delete("/{agent_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_agent(
    agent_id: str,
    auth_context: AuthContext = Depends(get_auth_context),
    db: AsyncIOMotorDatabase = Depends(get_ai_database)
) -> None:
    """Delete an agent."""

    try:
        # Validate access
        await validate_agent_access(agent_id, auth_context.user, auth_context.org_id, db)

        # Delete agent
        result = await db.agents.delete_one({"_id": PyObjectId(agent_id)})

        if result.deleted_count == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )

        logger.info(f"Deleted agent {agent_id} by user {auth_context.user_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete agent {agent_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete agent"
        )


@router.post("/{agent_id}/run")
async def run_agent(
    agent_id: str,
    request: RunAgentRequest,
    auth_context: AuthContext = Depends(get_auth_context),
    db: AsyncIOMotorDatabase = Depends(get_ai_database)
) -> Dict[str, Any]:
    """Run an agent with the given input."""

    logger.debug(f"Running agent {agent_id} for user {auth_context.user_id}")
    logger.debug(f"Input message: {request.message[:100]}...")
    logger.debug(f"Session ID: {request.session_id}, Deal ID: {request.deal_id}")

    try:
        # Validate access
        logger.debug(f"Validating access for agent {agent_id}")
        await validate_agent_access(agent_id, auth_context.user, auth_context.org_id, db)

        # Get agent
        logger.debug(f"Fetching agent {agent_id} from database")
        agent_doc = await db.agents.find_one({"_id": PyObjectId(agent_id)})
        if not agent_doc:
            logger.warning(f"Agent {agent_id} not found")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )

        # Create agent object
        logger.debug(f"Creating agent object for {agent_doc['name']}")
        agent = Agent(**agent_doc)

        # Build context
        logger.debug("Building agent execution context")
        context = await context_builder.build_context(
            user_id=auth_context.user_id,
            org_id=auth_context.org_id,
            session_id=request.session_id,
            deal_id=request.deal_id,
            metadata=request.context_data
        )

        # Run agent
        logger.info(f"Executing agent {agent.name} (ID: {agent_id})")
        result = await agent_runner.run_agent(
            agent=agent,
            context=context,
            input_message=request.message
        )

        # Update agent usage
        logger.debug(f"Updating usage statistics for agent {agent_id}")
        await agent_registry.update_agent_usage(agent_id)

        logger.info(f"Agent {agent.name} execution completed with success={result.get('success', False)}")
        logger.debug(f"Execution time: {result.get('execution_time', 0):.2f}s, Steps: {result.get('intermediate_steps', 0)}")

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to run agent {agent_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to run agent"
        )


@router.get("/{agent_id}/logs")
async def get_agent_logs(
    agent_id: str,
    limit: int = Query(50, ge=1, le=100),
    skip: int = Query(0, ge=0),
    session_id: Optional[str] = Query(None),
    auth_context: AuthContext = Depends(get_auth_context),
    db: AsyncIOMotorDatabase = Depends(get_ai_database)
) -> Dict[str, Any]:
    """Get agent execution logs."""

    logger.debug(f"Getting logs for agent {agent_id}, user {auth_context.user_id}")
    logger.debug(f"Query parameters: limit={limit}, skip={skip}, session_id={session_id}")

    try:
        # Validate access
        logger.debug(f"Validating access for agent {agent_id}")
        await validate_agent_access(agent_id, auth_context.user, auth_context.org_id, db)

        # Get logs
        logger.debug(f"Fetching logs for agent {agent_id} from audit logger")
        logs = await audit_logger.get_agent_logs(
            org_id=auth_context.org_id,
            agent_id=agent_id,
            session_id=session_id,
            limit=limit,
            skip=skip
        )

        logger.debug(f"Found {len(logs)} logs for agent {agent_id}")
        logger.info(f"Retrieved {len(logs)} logs for agent {agent_id}")

        return {
            "logs": logs,
            "total": len(logs),
            "agent_id": agent_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get agent logs {agent_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent logs"
        )


@router.get("/{agent_id}/capabilities")
async def get_agent_capabilities(
    agent_id: str,
    auth_context: AuthContext = Depends(get_auth_context),
    db: AsyncIOMotorDatabase = Depends(get_ai_database)
) -> Dict[str, Any]:
    """Get agent capabilities and available tools."""

    logger.debug(f"Getting capabilities for agent {agent_id}, user {auth_context.user_id}")

    try:
        # Validate access
        logger.debug(f"Validating access for agent {agent_id}")
        await validate_agent_access(agent_id, auth_context.user, auth_context.org_id, db)

        # Get agent
        logger.debug(f"Fetching agent {agent_id} from database")
        agent_doc = await db.agents.find_one({"_id": PyObjectId(agent_id)})
        if not agent_doc:
            logger.warning(f"Agent {agent_id} not found")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )

        # Create agent object
        logger.debug(f"Creating agent object for {agent_doc['name']}")
        agent = Agent(**agent_doc)

        # Get capabilities
        logger.debug(f"Retrieving capabilities for agent {agent.name}")
        capabilities = await agent_runner.get_agent_capabilities(agent)

        logger.debug(f"Agent {agent.name} has {len(capabilities.get('tools', []))} tools and {len(capabilities.get('categories', []))} categories")
        logger.info(f"Retrieved capabilities for agent {agent_id}")

        return capabilities

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get agent capabilities {agent_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent capabilities"
        )
