"""
Deal-specific chat API endpoints for TractionX AI Agent system.
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from motor.motor_asyncio import AsyncIOMotorDatabase
from pydantic import BaseModel, Field
import logging
from datetime import datetime

from ai.db import get_ai_database
from ai.models import AgentContext, PyObjectId
from ai.api.auth import get_auth_context, AuthContext, validate_deal_access
from ai.registry import agent_registry
from ai.runner import agent_runner
from ai.context import context_builder
from ai.audit import audit_logger

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/deals", tags=["deals"])


# Request/Response models
class DealChatRequest(BaseModel):
    """Request model for deal chat."""
    message: str = Field(..., min_length=1)
    agent_type: str = Field(default="thesis_matching", description="Type of agent to use")
    session_id: Optional[str] = None
    context_data: Optional[Dict[str, Any]] = Field(default_factory=dict)


class DealChatResponse(BaseModel):
    """Response model for deal chat."""
    response: str
    agent_name: str
    agent_id: str
    session_id: str
    execution_time: float
    tools_used: List[str]
    deal_id: str
    timestamp: datetime


class DealAnalysisRequest(BaseModel):
    """Request model for deal analysis."""
    analysis_type: str = Field(..., description="Type of analysis to perform")
    agent_id: Optional[str] = Field(None, description="Specific agent to use")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=dict)


class DealAnalysisResponse(BaseModel):
    """Response model for deal analysis."""
    analysis_results: Dict[str, Any]
    agent_name: str
    agent_id: str
    deal_id: str
    analysis_type: str
    execution_time: float
    timestamp: datetime


@router.post("/{deal_id}/chat", response_model=DealChatResponse)
async def chat_with_deal(
    deal_id: str,
    request: DealChatRequest,
    auth_context: AuthContext = Depends(get_auth_context),
    db: AsyncIOMotorDatabase = Depends(get_ai_database)
) -> DealChatResponse:
    """Chat with AI about a specific deal."""

    logger.debug(f"Processing chat request for deal {deal_id}, user {auth_context.user_id}")
    logger.debug(f"Chat request: agent_type={request.agent_type}, message_length={len(request.message)}")

    try:
        # Validate deal access
        logger.debug(f"Validating access to deal {deal_id}")
        await validate_deal_access(deal_id, auth_context.user, auth_context.org_id)

        # Get appropriate agent for deal chat
        logger.debug(f"Getting agent for deal chat, type={request.agent_type}")
        agent = await _get_deal_chat_agent(request.agent_type, auth_context.org_id, db)

        if not agent:
            logger.warning(f"No agent found for type: {request.agent_type}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No agent available for type: {request.agent_type}"
            )

        logger.debug(f"Using agent: {agent.name} (ID: {agent.id})")

        # Build context with deal data
        logger.debug(f"Building context for deal {deal_id}")
        context = await context_builder.build_context(
            user_id=auth_context.user_id,
            org_id=auth_context.org_id,
            session_id=request.session_id,
            deal_id=deal_id,
            metadata=request.context_data
        )

        # Get deal context data
        logger.debug(f"Getting deal context data for {deal_id}")
        deal_context = await context_builder.get_deal_context(context, deal_id)
        context.metadata.update(deal_context)
        logger.debug(f"Deal context added to agent context, keys: {list(deal_context.keys())}")

        # Enhance message with deal context
        logger.debug("Creating enhanced message with deal context")
        enhanced_message = f"""
        Deal ID: {deal_id}
        User Question: {request.message}

        Please analyze this question in the context of the specific deal.
        """

        # Run agent
        logger.info(f"Running agent {agent.name} for deal {deal_id}")
        result = await agent_runner.run_agent(
            agent=agent,
            context=context,
            input_message=enhanced_message
        )

        if not result.get("success", False):
            error_msg = result.get("error", "Unknown error")
            logger.error(f"Agent execution failed for deal {deal_id}: {error_msg}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Agent execution failed: {error_msg}"
            )

        # Update agent usage
        logger.debug(f"Updating usage statistics for agent {agent.id}")
        await agent_registry.update_agent_usage(str(agent.id))

        logger.info(f"Chat with deal {deal_id} completed successfully in {result['execution_time']:.2f}s")
        logger.debug(f"Tools used: {result.get('tools_used', [])}")

        return DealChatResponse(
            response=result["response"],
            agent_name=agent.name,
            agent_id=str(agent.id),
            session_id=result["session_id"],
            execution_time=result["execution_time"],
            tools_used=result.get("tools_used", []),
            deal_id=deal_id,
            timestamp=datetime.utcnow()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to chat with deal {deal_id}: {e}")
        logger.debug(f"Chat error details: {type(e).__name__}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process deal chat"
        )


@router.post("/{deal_id}/analyze", response_model=DealAnalysisResponse)
async def analyze_deal(
    deal_id: str,
    request: DealAnalysisRequest,
    auth_context: AuthContext = Depends(get_auth_context),
    db: AsyncIOMotorDatabase = Depends(get_ai_database)
) -> DealAnalysisResponse:
    """Perform AI analysis on a specific deal."""

    logger.debug(f"Processing analysis request for deal {deal_id}, user {auth_context.user_id}")
    logger.debug(f"Analysis request: type={request.analysis_type}, agent_id={request.agent_id}")

    try:
        # Validate deal access
        logger.debug(f"Validating access to deal {deal_id}")
        await validate_deal_access(deal_id, auth_context.user, auth_context.org_id)

        # Get agent for analysis
        if request.agent_id:
            # Use specific agent
            logger.debug(f"Using specific agent ID: {request.agent_id}")
            agent_doc = await db.agents.find_one({"_id": PyObjectId(request.agent_id)})
            if not agent_doc:
                logger.warning(f"Agent {request.agent_id} not found")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Agent not found"
                )
            from ai.models import Agent
            agent = Agent(**agent_doc)
            logger.debug(f"Using specified agent: {agent.name}")
        else:
            # Get default agent for analysis type
            logger.debug(f"Getting default agent for analysis type: {request.analysis_type}")
            agent = await _get_analysis_agent(request.analysis_type, auth_context.org_id, db)

            if not agent:
                logger.warning(f"No agent found for analysis type: {request.analysis_type}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"No agent available for analysis type: {request.analysis_type}"
                )
            logger.debug(f"Using default agent: {agent.name} for analysis type {request.analysis_type}")

        # Build context
        logger.debug(f"Building context for deal {deal_id}")
        context = await context_builder.build_context(
            user_id=auth_context.user_id,
            org_id=auth_context.org_id,
            deal_id=deal_id,
            metadata=request.parameters
        )

        # Get deal context data
        logger.debug(f"Getting deal context data for {deal_id}")
        deal_context = await context_builder.get_deal_context(context, deal_id)
        context.metadata.update(deal_context)
        logger.debug(f"Deal context added to agent context, keys: {list(deal_context.keys())}")

        # Create analysis prompt
        logger.debug(f"Creating analysis prompt for type: {request.analysis_type}")
        analysis_prompt = _create_analysis_prompt(request.analysis_type, deal_id, request.parameters)

        # Run analysis
        logger.info(f"Running analysis with agent {agent.name} for deal {deal_id}")
        result = await agent_runner.run_agent(
            agent=agent,
            context=context,
            input_message=analysis_prompt
        )

        if not result.get("success", False):
            error_msg = result.get("error", "Unknown error")
            logger.error(f"Analysis failed for deal {deal_id}: {error_msg}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Analysis failed: {error_msg}"
            )

        # Parse analysis results
        logger.debug(f"Parsing analysis results for type: {request.analysis_type}")
        analysis_results = _parse_analysis_results(result["response"], request.analysis_type)

        # Update agent usage
        logger.debug(f"Updating usage statistics for agent {agent.id}")
        await agent_registry.update_agent_usage(str(agent.id))

        logger.info(f"Analysis of deal {deal_id} completed successfully in {result['execution_time']:.2f}s")

        return DealAnalysisResponse(
            analysis_results=analysis_results,
            agent_name=agent.name,
            agent_id=str(agent.id),
            deal_id=deal_id,
            analysis_type=request.analysis_type,
            execution_time=result["execution_time"],
            timestamp=datetime.utcnow()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to analyze deal {deal_id}: {e}")
        logger.debug(f"Analysis error details: {type(e).__name__}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to analyze deal"
        )


@router.get("/{deal_id}/chat/history")
async def get_deal_chat_history(
    deal_id: str,
    limit: int = Query(50, ge=1, le=100),
    skip: int = Query(0, ge=0),
    session_id: Optional[str] = Query(None),
    auth_context: AuthContext = Depends(get_auth_context)
) -> Dict[str, Any]:
    """Get chat history for a deal."""

    logger.debug(f"Getting chat history for deal {deal_id}, user {auth_context.user_id}")
    logger.debug(f"Query parameters: limit={limit}, skip={skip}, session_id={session_id}")

    try:
        # Validate deal access
        logger.debug(f"Validating access to deal {deal_id}")
        await validate_deal_access(deal_id, auth_context.user, auth_context.org_id)

        # Get chat logs
        logger.debug(f"Fetching chat logs for deal {deal_id} from audit logger")
        logs = await audit_logger.get_agent_logs(
            org_id=auth_context.org_id,
            deal_id=deal_id,
            session_id=session_id,
            limit=limit,
            skip=skip
        )

        logger.debug(f"Found {len(logs)} log entries for deal {deal_id}")

        # Format chat history
        logger.debug("Formatting chat history from log entries")
        chat_history = []
        for log in logs:
            if log.get("input_data") and log.get("output_data"):
                chat_entry = {
                    "timestamp": log["timestamp"],
                    "session_id": log.get("session_id"),
                    "agent_name": log.get("agent_name", "Unknown"),
                    "user_message": log["input_data"].get("message", ""),
                    "agent_response": log["output_data"].get("response", ""),
                    "tools_used": log.get("tools_used", []),
                    "execution_time": log.get("execution_time", 0)
                }
                chat_history.append(chat_entry)

        logger.debug(f"Formatted {len(chat_history)} chat entries from {len(logs)} log entries")
        logger.info(f"Retrieved {len(chat_history)} chat history entries for deal {deal_id}")

        return {
            "chat_history": chat_history,
            "total": len(chat_history),
            "deal_id": deal_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get chat history for deal {deal_id}: {e}")
        logger.debug(f"Chat history error details: {type(e).__name__}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get chat history"
        )


# Helper functions
async def _get_deal_chat_agent(agent_type: str, org_id: str, db: AsyncIOMotorDatabase):
    """Get appropriate agent for deal chat."""

    logger.debug(f"Getting deal chat agent for type: {agent_type}, org: {org_id}")

    # Map agent types to agent names
    agent_mapping = {
        "thesis_matching": "Thesis Matching Agent",
        "founder_analysis": "Founder Signals Agent",
        "market_analysis": "Market Momentum Agent"
    }

    agent_name = agent_mapping.get(agent_type)
    if not agent_name:
        logger.warning(f"No agent mapping found for type: {agent_type}")
        return None

    logger.debug(f"Mapped agent type {agent_type} to agent name: {agent_name}")

    # Get proprietary agent
    logger.debug(f"Querying database for agent: {agent_name}")
    agent_doc = await db.agents.find_one({
        "name": agent_name,
        "type": "proprietary",
        "status": "active"
    })

    if agent_doc:
        logger.debug(f"Found agent: {agent_name} (ID: {agent_doc['_id']})")
        from ai.models import Agent
        return Agent(**agent_doc)

    logger.warning(f"No active proprietary agent found with name: {agent_name}")
    return None


async def _get_analysis_agent(analysis_type: str, org_id: str, db: AsyncIOMotorDatabase):
    """Get appropriate agent for analysis type."""

    # Map analysis types to agents
    analysis_mapping = {
        "thesis_match": "Thesis Matching Agent",
        "founder_signals": "Founder Signals Agent",
        "market_momentum": "Market Momentum Agent",
        "comprehensive": "Thesis Matching Agent"  # Default to thesis matching
    }

    agent_name = analysis_mapping.get(analysis_type, "Thesis Matching Agent")

    # Get proprietary agent
    agent_doc = await db.agents.find_one({
        "name": agent_name,
        "type": "proprietary",
        "status": "active"
    })

    if agent_doc:
        from ai.models import Agent
        return Agent(**agent_doc)

    return None


def _create_analysis_prompt(analysis_type: str, deal_id: str, parameters: Dict[str, Any]) -> str:
    """Create analysis prompt based on type."""

    base_prompt = f"Please perform a {analysis_type} analysis for deal {deal_id}."

    if analysis_type == "thesis_match":
        return f"{base_prompt} Analyze how well this deal matches our investment thesis criteria. Provide a detailed assessment with scores and recommendations."

    elif analysis_type == "founder_signals":
        return f"{base_prompt} Analyze the founder team for key signals and investment potential. Focus on experience, track record, and team dynamics."

    elif analysis_type == "market_momentum":
        return f"{base_prompt} Analyze the market conditions and momentum for this investment opportunity. Consider timing, trends, and competitive landscape."

    elif analysis_type == "comprehensive":
        return f"{base_prompt} Provide a comprehensive investment analysis covering thesis match, founder assessment, and market conditions."

    else:
        return f"{base_prompt} Provide a detailed analysis based on available data."


def _parse_analysis_results(response: str, analysis_type: str) -> Dict[str, Any]:
    """Parse analysis results from agent response."""

    # TODO: Implement more sophisticated parsing
    # For now, return the response as structured data

    return {
        "analysis_type": analysis_type,
        "summary": response,
        "raw_response": response,
        "structured_data": {
            "score": 0.75,  # Placeholder
            "recommendation": "Further evaluation recommended",  # Placeholder
            "key_points": ["Analysis completed", "See summary for details"]  # Placeholder
        }
    }
